{"_id": "df957eb4-739b-420e-b572-bc5a635231c8", "colName": "Kanban APIs", "created": "2023-03-23T04:57:37.389Z", "sortNum": 70000, "folders": [{"_id": "d640eb36-ca33-4d35-8c8b-01550394f815", "name": "Leads", "containerId": "", "created": "2023-04-03T08:03:56.153Z", "sortNum": 10000}, {"_id": "682216a4-ea7e-4563-9c83-a18921e351ad", "name": "Statuses", "containerId": "", "created": "2023-04-03T08:05:11.472Z", "sortNum": 20000}, {"_id": "b068a251-02fd-496f-bfa9-31ea9e1bb066", "name": "Status Groups", "containerId": "", "created": "2023-04-03T08:14:34.349Z", "sortNum": 30000}, {"_id": "c6ec8dad-b535-4e06-9be8-534093fc3199", "name": "Partner Users", "containerId": "", "created": "2023-04-03T08:17:14.533Z", "sortNum": 40000}, {"_id": "6c7201e6-49a5-4b56-bf8f-15d37f9935dc", "name": "Old Lead endpoints", "containerId": "", "created": "2023-04-19T07:19:59.302Z", "sortNum": 50000}, {"_id": "844b749c-4164-47a1-ad9d-b3c76710ff57", "name": "New Kanban 2024", "containerId": "", "created": "2024-09-26T05:11:37.307Z", "sortNum": 60000}], "requests": [{"_id": "2230c46c-8e81-4f55-b31b-9616552c3768", "colId": "df957eb4-739b-420e-b572-bc5a635231c8", "containerId": "d640eb36-ca33-4d35-8c8b-01550394f815", "name": "Get All Leads", "url": "{{domain_name}}/kanban/leads/get-leads/", "method": "POST", "sortNum": 10000, "created": "2023-03-24T05:58:26.720Z", "modified": "2023-05-29T01:19:29.731Z", "headers": [], "params": [], "body": {"type": "json", "raw": "{\n  \"start_date\": \"2016-04-23\",\n  \"end_date\": \"2023-05-24\",\n  \"filter_by_date\": \"created\",//created || updated\n  // \"status\": [2],\n    // \"status\": [10,1,2]\n//   // \"customer_type\": \"commercial\"//\"commercial || consumer\"\n//   // \"assignee\": [1,40, 3],\n  // \"product_type\": [2],\n//   // \"lender\": [1,6]\n  \"is_closed\": 0,\n  \"no_leads\":0 //to prevent receiving data\n  // \"settled_date\": 0//settlement+created/updated date must be between date range if set to 1\n}", "form": []}, "tests": []}, {"_id": "fb8808a2-5d71-4cdc-b19a-766e6580294a", "colId": "df957eb4-739b-420e-b572-bc5a635231c8", "containerId": "682216a4-ea7e-4563-9c83-a18921e351ad", "name": "Get All Manual Status", "url": "{{domain_name}}/kanban/manual-statuses/get-all-statuses", "method": "GET", "sortNum": 10000, "created": "2023-03-26T22:55:06.967Z", "modified": "2023-04-03T08:05:39.320Z", "headers": [], "params": [], "tests": []}, {"_id": "b336e86c-23b0-408a-ac1d-0db4b83fe27e", "colId": "df957eb4-739b-420e-b572-bc5a635231c8", "containerId": "682216a4-ea7e-4563-9c83-a18921e351ad", "name": "Add Manual Status", "url": "{{domain_name}}/kanban/manual-statuses/add", "method": "POST", "sortNum": 20000, "created": "2023-03-26T22:59:03.883Z", "modified": "2023-04-03T08:05:59.329Z", "headers": [], "params": [], "body": {"type": "json", "raw": "{\n  \"partner_id\": 1,\n  \"status_name\": \"New Lead22\",\n  \"man_status_group_id\": 1,\n  \"order\": 5,\n  \"active\": true,\n  \"is_new_lead\": true,\n  \"is_settled\": false\n}", "form": []}, "tests": []}, {"_id": "9ec88037-a760-41f4-84d5-7f6a71c719a1", "colId": "df957eb4-739b-420e-b572-bc5a635231c8", "containerId": "b068a251-02fd-496f-bfa9-31ea9e1bb066", "name": "Add Manual Status Group", "url": "{{domain_name}}/kanban/manual-status-groups/add", "method": "POST", "sortNum": 10000, "created": "2023-03-26T23:04:40.103Z", "modified": "2023-04-04T01:55:31.337Z", "headers": [], "params": [], "body": {"type": "json", "raw": "{\n    \"partner_id\": 1,\n    \"group_name\": \"New Lead\",\n    \"color\": \"#ff0000\",\n    \"order\": 1,\n    \"active\": true\n}", "form": []}, "tests": []}, {"_id": "bc42a09f-5eae-4c52-8ff9-5464b10b4ea5", "colId": "df957eb4-739b-420e-b572-bc5a635231c8", "containerId": "682216a4-ea7e-4563-9c83-a18921e351ad", "name": "Update Manual Status", "url": "{{domain_name}}/kanban/manual-statuses/edit", "method": "POST", "sortNum": 30000, "created": "2023-03-27T00:01:14.882Z", "modified": "2023-04-03T08:06:56.940Z", "headers": [], "params": [], "body": {"type": "json", "raw": "{\n  \"id\": 5,\n  // \"partner_id\": 1,\n  \"status_name\": \"New 778\",\n  \"man_status_group_id\": 1,\n  \"order\": 1,\n  \"active\": true,\n  \"is_new_lead\": true,\n  \"is_settled\": false\n}", "form": []}, "tests": []}, {"_id": "4fce5abf-c48c-49af-babe-ffa7ae9fa8c8", "colId": "df957eb4-739b-420e-b572-bc5a635231c8", "containerId": "b068a251-02fd-496f-bfa9-31ea9e1bb066", "name": "Update Manual Status Group", "url": "{{domain_name}}/kanban/manual-status-groups/edit", "method": "POST", "sortNum": 20000, "created": "2023-03-27T00:02:41.035Z", "modified": "2023-04-03T08:15:10.032Z", "headers": [], "params": [], "body": {"type": "json", "raw": "{\n    \"id\": 3,\n    \"group_name\": \"New Lead255455\",\n    \"color\": \"#ff0111\",\n    \"order\": 1,\n    \"active\": true\n}", "form": []}, "tests": []}, {"_id": "60d2cafa-5ee1-45ff-ab76-ac455ab7077c", "colId": "df957eb4-739b-420e-b572-bc5a635231c8", "containerId": "c6ec8dad-b535-4e06-9be8-534093fc3199", "name": "Update Partner to manual", "url": "{{domain_name}}/kanban/partner-users/update-partner-to-manual", "method": "POST", "sortNum": 10000, "created": "2023-03-27T00:03:44.735Z", "modified": "2023-04-03T23:06:13.485Z", "headers": [], "params": [], "tests": []}, {"_id": "ed954cca-1076-47c3-9d8b-c50ba93972f8", "colId": "df957eb4-739b-420e-b572-bc5a635231c8", "containerId": "c6ec8dad-b535-4e06-9be8-534093fc3199", "name": "Update Partner User", "url": "{{domain_name}}/kanban/partner-users/update-partner-user", "method": "POST", "sortNum": 20000, "created": "2023-03-27T00:15:03.710Z", "modified": "2023-04-03T08:17:41.447Z", "headers": [], "params": [], "body": {"type": "json", "raw": "{\n    \"kanban_leads\": {\"name\":\"<PERSON>\", \"age\":30, \"car\":null},\n    // \"kanban_filter\": \"\",\n    // \"closed_leads_filter\": \"\"\n}", "form": []}, "tests": []}, {"_id": "40c8c45d-7126-4813-ad03-330c08648a37", "colId": "df957eb4-739b-420e-b572-bc5a635231c8", "containerId": "d640eb36-ca33-4d35-8c8b-01550394f815", "name": "Update Lead", "url": "{{domain_name}}/kanban/leads/update", "method": "POST", "sortNum": 30000, "created": "2023-03-27T00:15:52.424Z", "modified": "2023-05-08T08:14:24.054Z", "headers": [], "params": [], "body": {"type": "json", "raw": "{\n    // \"lead_ref\": \"{{leqdRef}}\",\n    \"lead_ref\": \"55H7yQJ\",//77741\n    // \"man_status_id\": 10,\n    // \"kanban_colour\":\"34344\",\n    \"kanban_data\":{\"swimlane_id\":\"10\", \"groups\":{\"1\":[\"55H7yQJ\"]}}\n    // \"assignee\":1\n    // \"is_closed\":1\n}", "form": []}, "tests": []}, {"_id": "66a4d4bc-f513-424e-9801-249527b5903b", "colId": "df957eb4-739b-420e-b572-bc5a635231c8", "containerId": "c6ec8dad-b535-4e06-9be8-534093fc3199", "name": "Get Partner User", "url": "{{domain_name}}/kanban/partner-users/get-partner-user?kanban_leads=true&kanban_filter=value2&closed_leads_filter=value2", "method": "GET", "sortNum": 40000, "created": "2023-03-27T00:25:52.288Z", "modified": "2023-04-03T08:18:06.509Z", "headers": [], "params": [{"name": "kanban_leads", "value": "true", "isPath": false}, {"name": "kanban_filter", "value": "value2", "isPath": false}, {"name": "closed_leads_filter", "value": "value2", "isPath": false}], "tests": []}, {"_id": "39f1deee-5b9b-448c-95da-ac1476d10109", "colId": "df957eb4-739b-420e-b572-bc5a635231c8", "containerId": "d640eb36-ca33-4d35-8c8b-01550394f815", "name": "Lead quick view", "url": "{{domain_name}}/kanban/leads/quick-view/24xiUDc", "method": "GET", "sortNum": 20000, "created": "2023-03-27T00:32:59.405Z", "modified": "2023-04-03T08:04:04.547Z", "headers": [], "params": [], "tests": []}, {"_id": "8612e76f-b0af-43f9-bb84-557268f14a8a", "colId": "df957eb4-739b-420e-b572-bc5a635231c8", "containerId": "682216a4-ea7e-4563-9c83-a18921e351ad", "name": "Delete Manual Status", "url": "{{domain_name}}/kanban/manual-statuses/delete", "method": "POST", "sortNum": 50000, "created": "2023-03-27T01:05:19.543Z", "modified": "2023-04-03T08:12:02.757Z", "headers": [], "params": [], "body": {"type": "json", "raw": "{\n  \"id\": 1\n}", "form": []}, "tests": []}, {"_id": "58db32ea-850c-4359-990b-45b83bbc7433", "colId": "df957eb4-739b-420e-b572-bc5a635231c8", "containerId": "c6ec8dad-b535-4e06-9be8-534093fc3199", "name": "Get All assignees", "url": "{{domain_name}}/kanban/partner-users/get-partner-users", "method": "GET", "sortNum": 25000, "created": "2023-03-27T01:22:35.245Z", "modified": "2023-04-03T08:18:48.167Z", "headers": [], "params": [], "tests": []}, {"_id": "9de73118-4118-4cd6-b4ad-21986c5f1eae", "colId": "df957eb4-739b-420e-b572-bc5a635231c8", "containerId": "682216a4-ea7e-4563-9c83-a18921e351ad", "name": "Update Manual Status Force", "url": "{{domain_name}}/kanban/manual-statuses/edit/true", "method": "POST", "sortNum": 40000, "created": "2023-04-03T08:06:35.790Z", "modified": "2023-04-03T08:06:35.790Z", "headers": [], "params": [], "body": {"type": "json", "raw": "{\n  \"id\": 5,\n  // \"partner_id\": 1,\n  \"status_name\": \"New 778\",\n  \"man_status_group_id\": 1,\n  \"order\": 1,\n  \"active\": true,\n  \"is_new_lead\": true,\n  \"is_settled\": false\n}", "form": []}, "tests": []}, {"_id": "794b6bfe-42f4-4d51-b70b-fd38a5f8f9d6", "colId": "df957eb4-739b-420e-b572-bc5a635231c8", "containerId": "c6ec8dad-b535-4e06-9be8-534093fc3199", "name": "Update Partner User Copy", "url": "{{domain_name}}/kanban/partner-users/test", "method": "POST", "sortNum": 22500, "created": "2023-04-04T07:13:10.029Z", "modified": "2023-04-04T07:13:20.316Z", "headers": [], "params": [], "body": {"type": "json", "raw": "{\n    \"kanban_leads\": {\"name\":\"<PERSON>\", \"age\":30, \"car\":null},\n    // \"kanban_filter\": \"\",\n    // \"closed_leads_filter\": \"\"\n}", "form": []}, "tests": []}, {"_id": "33416288-c33d-4f3d-9373-094b0a7cd134", "colId": "df957eb4-739b-420e-b572-bc5a635231c8", "containerId": "d640eb36-ca33-4d35-8c8b-01550394f815", "name": "<PERSON> Ka<PERSON>ban Leads", "url": "{{domain_name}}/kanban/leads/get-kanban-leads/", "method": "POST", "sortNum": 15000, "created": "2023-04-10T23:14:07.003Z", "modified": "2023-04-11T01:44:46.400Z", "headers": [], "params": [], "tests": []}, {"_id": "9434169b-3298-4e52-95e0-84278573e5cc", "colId": "df957eb4-739b-420e-b572-bc5a635231c8", "containerId": "6c7201e6-49a5-4b56-bf8f-15d37f9935dc", "name": "Get leads", "url": "{{domain_name}}/leads/filter-with-pag-read-data?manage=Leads&start_date=2016-04-01&end_date=2023-04-30", "method": "POST", "sortNum": 10000, "created": "2023-04-19T07:20:57.642Z", "modified": "2023-06-06T06:21:50.889Z", "headers": [], "params": [{"name": "manage", "value": "Leads", "isPath": false}, {"name": "start_date", "value": "2016-04-01", "isPath": false}, {"name": "end_date", "value": "2023-04-30", "isPath": false}], "body": {"type": "json", "raw": "{\n  \"start\": 0,\n  \"search\": {\n    \"value\": \"\",\n    \"regex\": false\n  },\n  \"draw\": 1,\n  \"length\": 10000,\n  \"refreshPagination\": true,\n  \"record\": \"leads\",\n  \"manage\": \"Leads\",\n  \"order\": [\n    {\n      \"column\": \"created\",\n      \"dir\": \"desc\"\n    }\n  ],\n  \"filters\": {\n    \"filter_by_date\": \"created\",\n    // \"settled_date\": 1,\n    // \"is_closed\": 0,\n    \"product_type_id\": [null, 1]\n  }\n}", "form": []}, "tests": []}, {"_id": "1d662dc8-2617-44fc-a69e-53f005f63c50", "colId": "df957eb4-739b-420e-b572-bc5a635231c8", "containerId": "d640eb36-ca33-4d35-8c8b-01550394f815", "name": "Remove duplicate leads from user kanban data -remove", "url": "{{domain_name}}/kanban/leads/removeduplicates/true", "method": "POST", "sortNum": 17500, "created": "2023-05-08T04:59:41.084Z", "modified": "2023-05-08T07:08:02.229Z", "headers": [], "params": [], "tests": []}, {"_id": "f394172d-9af8-4ab7-986f-80f748ac06fe", "colId": "df957eb4-739b-420e-b572-bc5a635231c8", "containerId": "d640eb36-ca33-4d35-8c8b-01550394f815", "name": "Remove duplicate leads from user kanban data -check", "url": "{{domain_name}}/kanban/leads/removeduplicates/", "method": "POST", "sortNum": 16250, "created": "2023-05-09T05:06:44.731Z", "modified": "2023-05-09T06:07:47.523Z", "headers": [], "params": [], "tests": []}, {"_id": "24377f55-cd74-4774-a881-af8324f5d5e3", "colId": "df957eb4-739b-420e-b572-bc5a635231c8", "containerId": "b068a251-02fd-496f-bfa9-31ea9e1bb066", "name": "Update Manual Status Group order", "url": "{{domain_name}}/kanban/manual-status-groups/edit-groups-statuses-order", "method": "POST", "sortNum": 30000, "created": "2024-05-15T07:09:07.969Z", "modified": "2024-05-15T07:37:04.665Z", "headers": [], "params": [], "body": {"type": "json", "raw": "{\n  \"1\": [\n    1,\n    2,\n    3,\n    4,\n    5,\n    6,\n    8,\n    7\n  ]\n}", "form": []}, "tests": []}, {"_id": "6257c7bc-501b-445f-804a-4dec7290079a", "colId": "df957eb4-739b-420e-b572-bc5a635231c8", "containerId": "844b749c-4164-47a1-ad9d-b3c76710ff57", "name": "Move lead (to specific position)", "url": "{{domain_name}}/kanban/leads/move-lead", "method": "POST", "sortNum": 10000, "created": "2024-09-26T05:04:28.320Z", "modified": "2024-09-30T04:53:42.140Z", "headers": [], "body": {"type": "json", "raw": "{\n  \"lead_ref\": \"007NPN7\",//Required\n  \"socket_id\": \"t565656vsdgh\",//Required - this prevents return emit to this socket\n  //if relative fields are not provided, lead will move to top\n  \"relative_location\": \"above\", //optional\n  \"relative_lead_ref\": \"18sY1zz\",//optional\n  \"man_status_id\": 1//optional - only to provided when status is changing\n}", "form": []}}, {"_id": "7ea98d34-5b8c-4515-bc53-8bef858ffcd2", "colId": "df957eb4-739b-420e-b572-bc5a635231c8", "containerId": "844b749c-4164-47a1-ad9d-b3c76710ff57", "name": "Get Ka<PERSON>ban Leads Filtered", "url": "{{domain_name}}/kanban/leads/get-kanban-leads-filtered", "method": "POST", "sortNum": 20000, "created": "2024-09-30T04:17:18.593Z", "modified": "2025-06-25T07:59:46.432Z", "headers": [], "body": {"type": "json", "raw": "{\n  \"combineSwimlanes\": false,\n  \"filters\": {\n    \"settled_mode\": false,\n    \"start_date\": \"2023-01-01\",\n    \"end_date\": \"2024-12-31\",\n    \"filter_by_date\": \"created\"\n    // \"status\": [\n    //   3\n    // ],\n    // \"customer_type\": \"consumer\",\n    // \"assignee\": [\n    //   1,\n    //   40\n    // ],\n    // \"product_type\": [\n    //   1\n    // ],\n    // \"lender\": [\n    //   1,\n    //   \"some lender\"\n    // ],\n    // \"tag_id\": [\n    //   1\n    // ],\n    // \"referrer\": [\n    //   1\n    // ]\n  }\n}", "form": []}}]}