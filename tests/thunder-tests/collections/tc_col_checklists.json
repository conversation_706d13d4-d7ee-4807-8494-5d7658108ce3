{"_id": "d8676dd0-78fc-4cec-a549-c4cfaa96b45a", "colName": "Checklists", "created": "2025-02-24T04:20:36.902Z", "sortNum": 120000, "folders": [], "requests": [{"_id": "e5ad1211-e0fd-481a-a89f-906227141d8d", "colId": "d8676dd0-78fc-4cec-a549-c4cfaa96b45a", "containerId": "", "name": "Create partner checklist", "url": "{{domain_name}}/partners/checklists", "method": "POST", "sortNum": 10000, "created": "2025-02-24T04:21:43.859Z", "modified": "2025-03-03T03:26:24.684Z", "headers": [], "body": {"type": "json", "raw": "{\n  \"checklist_name\" : \"Test checklist2\",\n  \"partner_checklist_product_types\": [\n    {\n      \"product_type_id\": 1\n    }\n  ],\n  \"checklist_items\": [\n   {\n      \"item_name\": \"Sign engagement letter\",\n      \"item_description\": \"One per applicant\",\n      \"order\": 1\n  }]\n}", "form": []}}, {"_id": "ad964237-d4ec-4801-8f87-c1f1e0a11e30", "colId": "d8676dd0-78fc-4cec-a549-c4cfaa96b45a", "containerId": "", "name": "Update partner checklist", "url": "{{domain_name}}/partners/checklists", "method": "POST", "sortNum": 20000, "created": "2025-02-27T05:34:13.296Z", "modified": "2025-06-06T02:37:44.881Z", "headers": [], "body": {"type": "json", "raw": "{\n  \"partner_checklist_id\": 2,\n  // \"is_active\": false,\n  // \"checklist_name\" : \"Test checklist4r4543534\"//,\n  \"partner_checklist_product_types\": [\n    {\n      \"partner_checklist_product_type_id\": 32,\n      \"product_type_id\": 3\n    },\n    {\n      \"partner_checklist_product_type_id\": 33,\n      \"product_type_id\": 2\n    },\n    {\n      // \"partner_checklist_product_type_id\": 27,\n      \"product_type_id\": 5\n    },\n    {\n      \"partner_checklist_product_type_id\": 31,\n      \"product_type_id\": 4\n    }\n  ]\n  // \"checklist_items\": [\n  //   {\n  //     // \"is_active\": false,\n  //       // \"partner_checklist_item_id\": 6, \n  //       \"item_name\": \"Sign engagement updated\",\n  //       \"item_description\": \"One per appliwewewewecant\",\n  //       \"order\": 3\n  //   }//,\n  //   // {\n  //   //   \"item_name\": \"Sign engagement new one\",\n  //   //   \"item_description\": \"One per appliwewewewecant\",\n  //   //   \"order\": 2\n  //   // }\n  // ]\n}", "form": []}}, {"_id": "ca6b32f9-0631-4207-af3a-14238247132b", "colId": "d8676dd0-78fc-4cec-a549-c4cfaa96b45a", "containerId": "", "name": "Delete/Archive partner checklist", "url": "{{domain_name}}/partners/checklists/10", "method": "DELETE", "sortNum": 30000, "created": "2025-03-03T00:14:42.886Z", "modified": "2025-03-03T03:33:45.278Z", "headers": []}, {"_id": "bc16a776-0324-43a7-8ab3-c69e0df0c950", "colId": "d8676dd0-78fc-4cec-a549-c4cfaa96b45a", "containerId": "", "name": "List partner checklists", "url": "{{domain_name}}/partners/checklists/?active=1", "method": "GET", "sortNum": 40000, "created": "2025-03-03T03:32:33.390Z", "modified": "2025-03-03T04:18:09.020Z", "headers": [], "params": [{"name": "active", "value": "1", "isPath": false}]}, {"_id": "0ad828ba-37a2-4550-9131-d11f6b6376b0", "colId": "d8676dd0-78fc-4cec-a549-c4cfaa96b45a", "containerId": "", "name": "Update lead checklist item status", "url": "{{domain_name}}/lead-checklists/item", "method": "POST", "sortNum": 30000, "created": "2025-06-09T23:50:15.195Z", "modified": "2025-06-10T00:03:26.151Z", "headers": [], "body": {"type": "json", "raw": "{\n  \"lead_checklist_item_id\": 3,\n  \"is_complete\": true\n}", "form": []}}]}