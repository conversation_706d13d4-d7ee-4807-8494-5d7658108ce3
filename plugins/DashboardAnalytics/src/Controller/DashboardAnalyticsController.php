<?php
namespace DashboardAnalytics\Controller;

use DashboardAnalytics\Controller\AppController;

use Cake\Log\Log;
use Cake\Cache\Cache;
use Cake\ORM\TableRegistry;
use Cake\Database\Expression\QueryExpression;
use Cake\Core\Configure;

use App\Lend\Config;
use App\Lend\LendInternalAuth;

use App\Lend\DashboardAnalyticsService;

class DashboardAnalyticsController extends AppController
{
    public function initialize(){
        parent::initialize();
        $partner_user = $this->Auth->identify();
        $this->filter_items_key = 'filter_items_'.$partner_user['partner_user_id'];
        $this->filter_seleteced_key = 'filter_selected_'.$partner_user['partner_user_id'];
        $this->cache_config = 'dashboard_analytics';
        $this->asset_filter_available = false;
        $this->query_cache = 'queryRedis';//cache to use for query result caching
        $this->query_where_hash = '';
        $this->query_force_refresh = false;
    }

    public function init(){
      try{
        $partner_user = $this->Auth->identify();
        $partner_feature_flags = $this->loadModel('PartnerFeatureFlags')->getFeatureFlag(['partner_id'=>$partner_user['partner_id']]);
        if(!empty($partner_feature_flags['access_to_asset_form']))
          $this->asset_filter_available = true;

        $filter_items = $this->_getFilterItems();
        $filter_selected = $this->_getFilters($filter_items);
        return $this->setJsonResponse(['success'=>true, 'filter_items'=>$filter_items, 'filter_selected'=>$filter_selected]);
      }catch(\Exception $e){
        Log::error($e->getMessage());
        Log::error($e->getTraceAsString());
        return $this->setJsonResponse(['success'=>false, 'message'=>$e->getMessage()]);
      }
    }

    public function getDashboardAnalytics($forceRefresh = false){
      try{
        if (!$this->request->is('post')) {
          throw new \Exception("Only POST request allowed.", 405);
        }
        $user = $this->Auth->user();
        if (empty($user)) {
          $user = $this->Auth->identify();
        }
        $schema = array_flip(['start_date', 'end_date', 'user_refs']);
        $data = array_intersect_key($this->request->getData(), $schema);
        if(!$user['account_admin']){
          $data['user_refs'][] = $user['partner_user_ref'];
        }

        $checkDates = TableRegistry::get('Leads')->isValidDatesRange($data);
        if ($checkDates!== true){
          $checkDates = $checkDates==""?"Invalid date range":$checkDates;
          throw new \Exception($checkDates);
        }

        if(isset($data['user_refs'])){
          if(is_array($data['user_refs'])){
            foreach($data['user_refs'] as $userRef){
              $data['partner_user_ids'][] = LendInternalAuth::unhashPartnerUserId($userRef);
            }
          }
          else{
            throw new \Exception("Invalid user_refs provided");
          }
        }

        // Create base where conditions
        $whereBase = [
          'LeadEntity.partner_id' => $user['partner_id']
        ];
        if(isset($data['partner_user_ids'])){
          sort($data['partner_user_ids']);//so we use caching even if same users are sent in a different order
          $whereBase['PartnerUserLeadsEntity.status'] = 'ACCESS';
          $whereBase['PartnerUserLeadsEntity.partner_user_id IN'] = $data['partner_user_ids'];
        }

        // Where clause for lead created date metrics
        $whereLeadCreated = array_merge($whereBase, [
          'LeadEntity.created >=' => $data['start_date'] . ' 00:00:00',
          'LeadEntity.created <=' => $data['end_date'] . ' 23:59:59'
        ]);

        // Where clause for submission date metrics
        $whereSubmitted = array_merge($whereBase, [
          'SaleEntity.created >=' => $data['start_date'] . ' 00:00:00',
          'SaleEntity.created <=' => $data['end_date'] . ' 23:59:59'
        ]);

        // Where clause for settled date metrics
        $whereSettled = array_merge($whereBase, [
          'PartnerCommissionEntity.funded_date >=' => $data['start_date'] . ' 00:00:00',
          'PartnerCommissionEntity.funded_date <=' => $data['end_date'] . ' 23:59:59'
        ]);

        // Create hash for caching that includes all where clauses
        $allWhereClauses = [
          'lead_created' => $whereLeadCreated,
          'submitted' => $whereSubmitted,
          'settled' => $whereSettled
        ];
        $this->query_where_hash = md5(json_encode($allWhereClauses));
        $this->query_force_refresh = $forceRefresh;

        $leadsTable = TableRegistry::getTableLocator()->get('LeadEntity');
  
        //subquery for partner_commissions
        $partnerCommissionsTable = TableRegistry::getTableLocator()->get('PartnerCommissionEntity');
        $subQueryPC = $partnerCommissionsTable->find()
          ->select([
              'lead_id' => 'PartnerCommissionEntity.lead_id',
              'funded_amount_sum' => $partnerCommissionsTable->find()->func()->sum('PartnerCommissionEntity.funded_amount'),
              'funded_amount_avg' => $partnerCommissionsTable->find()->func()->avg('PartnerCommissionEntity.funded_amount'),
              'commission_sum' => $partnerCommissionsTable->find()->func()->sum('PartnerCommissionEntity.commission'),
              'commission_exists' => new QueryExpression('CASE WHEN COUNT(PartnerCommissionEntity.commission_id) > 0 THEN 1 ELSE 0 END'),
              'days_to_settle' => $leadsTable->query()->newExpr(
                'CASE 
                  WHEN PartnerCommissionEntity.commission_id = LeadAssociatedDataEntity.min_partner_commission_id
                  AND ManStatusEntity.is_settled = 1
                  THEN DATEDIFF(PartnerCommissionEntity.funded_date, LeadEntity.created) 
                  ELSE NULL 
                END'
              ),
  
          ])
          // ->from(['PartnerCommissionEntity' => 'partner_commissions'])
          ->join([
            'table' => 'leads',
            'alias' => 'LeadEntity',
            'conditions' => [
                'LeadEntity.lead_id = PartnerCommissionEntity.lead_id'
            ]
          ])
          ->join([
            'table' => 'lead_associated_data',
            'alias' => 'LeadAssociatedDataEntity',
            'conditions' => [
                'LeadAssociatedDataEntity.lead_id = LeadEntity.lead_id'
            ]
          ])
          ->join([
            'table' => 'man_statuses',
            'alias' => 'ManStatusEntity',
            'conditions' => [
                'ManStatusEntity.id = LeadEntity.man_status_id'
            ]
          ])
          ->join([
            'table' => 'partner_user_leads',
            'alias' => 'PartnerUserLeadsEntity',
            'type' => 'LEFT',
            'conditions' => [
                'PartnerUserLeadsEntity.lead_id = LeadEntity.lead_id',
                'PartnerUserLeadsEntity.status = "ACCESS"'
            ]
          ])
          ->where($whereLeadCreated)
          ->group('PartnerCommissionEntity.lead_id');
  
        //subquery for lead_call_attempts
        $leadCallAttemptsTable = TableRegistry::getTableLocator()->get('LeadCallAttemptEntity');
        $subQueryLCA = $leadCallAttemptsTable->find()
          ->select([
            'lead_id' => 'LeadCallAttemptEntity.lead_id',
            'call_queue_leads_called' => $leadCallAttemptsTable->find()->func()->count('LeadCallAttemptEntity.call_attempts_id'),
            'call_queue_answered' => $leadCallAttemptsTable->find()->func()->count(
              new QueryExpression('CASE WHEN LeadCallAttemptEntity.outcome_type = "answered" THEN 1 ELSE NULL END')
            ),
          ])
          ->join([
            'table' => 'leads',
            'alias' => 'LeadEntity',
            'conditions' => [
                'LeadEntity.lead_id = LeadCallAttemptEntity.lead_id'
            ]
          ])
          ->join([
            'table' => 'partner_user_leads',
            'alias' => 'PartnerUserLeadsEntity',
            'type' => 'LEFT',
            'conditions' => [
                'PartnerUserLeadsEntity.lead_id = LeadEntity.lead_id',
                'PartnerUserLeadsEntity.status = "ACCESS"'
            ]
          ])
          ->where($whereLeadCreated) 
          ->group('LeadCallAttemptEntity.lead_id');
  

          
        //subquery for lend_signature_requests
        $lendSignatureRequestsTable = TableRegistry::getTableLocator()->get('LendSignatureRequestEntity');
        $subQueryLSR = $lendSignatureRequestsTable->find()
          ->from(['LendSignatureRequestEntity' => 'lend_signature_requests'])
          ->select([
              'lead_id' => 'LendSignatureRequestEntity.lead_id',
              'request_count' => $lendSignatureRequestsTable->find()->func()->count('LendSignatureRequestEntity.id'),
              'completion_count' => $lendSignatureRequestsTable->find()->func()->sum(
                new QueryExpression("CASE WHEN LendSignatureRequestEntity.status = 'signed' THEN 1 ELSE 0 END")
              ),
              'conversion_rate' => $leadsTable->query()->newExpr(
                "CASE WHEN COUNT(LendSignatureRequestEntity.id) = 0 THEN 0 
                ELSE (SUM(CASE WHEN LendSignatureRequestEntity.status = 'signed' THEN 1 ELSE 0 END) / 
                COUNT(LendSignatureRequestEntity.id)) END"
              )
          ])
          ->join([
            'table' => 'leads',
            'alias' => 'LeadEntity',
            'conditions' => [
                'LeadEntity.lead_id = LendSignatureRequestEntity.lead_id'
            ]
          ])
          ->join([
            'table' => 'partner_user_leads',
            'alias' => 'PartnerUserLeadsEntity',
            'type' => 'LEFT',
            'conditions' => [
                'PartnerUserLeadsEntity.lead_id = LeadEntity.lead_id',
                'PartnerUserLeadsEntity.status = "ACCESS"'
            ]
          ])
          ->where($whereLeadCreated)
          ->group('LendSignatureRequestEntity.lead_id');
          
        //end subquery 


        $saleDetailsTable = TableRegistry::getTableLocator()->get('SaleDetailEntity');
        // Total submission metrics subquery (uses $whereSubmitted)
        $submissionTotalsQuery = $saleDetailsTable->find()
          ->select([
              'submitted_volume' => $saleDetailsTable->find()->func()->sum('SaleDetailEntity.financed_amount'),
              'leads_submitted' => $saleDetailsTable->find()->func()->count('DISTINCT SaleEntity.lead_id')
          ])
          ->join([
            'table' => 'sales',
            'alias' => 'SaleEntity',
            'conditions' => 'SaleDetailEntity.sale_id = SaleEntity.sale_id'
          ])
          ->join([
            'table' => 'leads',
            'alias' => 'LeadEntity',
            'conditions' => 'LeadEntity.lead_id = SaleEntity.lead_id'
          ])
          ->join([
            'table' => 'partner_user_leads',
            'alias' => 'PartnerUserLeadsEntity',
            'type' => 'LEFT',
            'conditions' => [
                'PartnerUserLeadsEntity.lead_id = LeadEntity.lead_id',
                'PartnerUserLeadsEntity.status = "ACCESS"'
            ]
          ])
          ->where($whereSubmitted)
          ->enableHydration(false);
        
        $submissionTotals = $this->getQueryResults('dashboard_submission_totals', $submissionTotalsQuery);
        $submissionTotals = $submissionTotals[0] ?? [];

        // Total settlement metrics subquery (uses $whereSettled)
        $partnerCommissionsTable = TableRegistry::getTableLocator()->get('PartnerCommissionEntity');
        $settlementReviewsTable = TableRegistry::getTableLocator()->get('SettlementReviewsEntity');
        
        $settlementTotalsQuery = $partnerCommissionsTable->find()
          ->select([
              'settled_volume' => $partnerCommissionsTable->find()->func()->sum('PartnerCommissionEntity.funded_amount'),
              'brokerage_origination' => $partnerCommissionsTable->find()->func()->sum('PartnerCommissionEntity.commission'),
              'average_settled_volume' => $partnerCommissionsTable->find()->func()->avg('PartnerCommissionEntity.funded_amount'),
              'overall_settlements' => $partnerCommissionsTable->find()->func()->count('PartnerCommissionEntity.commission_id'),
              'average_remuneration' => $partnerCommissionsTable->find()->func()->avg('PartnerCommissionEntity.commission')
          ])
          ->join([
            'table' => 'leads',
            'alias' => 'LeadEntity',
            'conditions' => 'LeadEntity.lead_id = PartnerCommissionEntity.lead_id'
          ])
          ->join([
            'table' => 'partner_user_leads',
            'alias' => 'PartnerUserLeadsEntity',
            'type' => 'LEFT',
            'conditions' => [
                'PartnerUserLeadsEntity.lead_id = LeadEntity.lead_id',
                'PartnerUserLeadsEntity.status = "ACCESS"'
            ]
          ])
          ->where($whereSettled)
          ->enableHydration(false);
        
        $settlementCommissionTotals = $this->getQueryResults('dashboard_settlement_commission_totals', $settlementTotalsQuery);
        $settlementCommissionTotals = $settlementCommissionTotals[0] ?? [];

        // Settlement review totals (uses $whereSettled)
        $settlementReviewTotalsQuery = $settlementReviewsTable->find()
          ->select([
              'origination_fees' => $settlementReviewsTable->find()->func()->sum('SettlementReviewsEntity.origination_fee'),
              'vbi' => $settlementReviewsTable->find()->func()->sum('SettlementReviewsEntity.vbi_income'),
              'warranty_comms' => $settlementReviewsTable->find()->func()->sum('SettlementReviewsEntity.warranty_comms'),
              'comp_ins_comms' => $settlementReviewsTable->find()->func()->sum('SettlementReviewsEntity.comp_ins_comms'),
              'gap_ins_comms' => $settlementReviewsTable->find()->func()->sum('SettlementReviewsEntity.gap_ins_comms'),
              'cci_ins_comms' => $settlementReviewsTable->find()->func()->sum('SettlementReviewsEntity.cci_ins_comms'),
              'referrer_fees' => $settlementReviewsTable->find()->func()->sum('SettlementReviewsEntity.referrer_commission'),
              'other_commissions' => $settlementReviewsTable->find()->func()->sum('SettlementReviewsEntity.other_income'),
              'warranty_income' => $settlementReviewsTable->find()->func()->sum('SettlementReviewsEntity.warranty_income')
          ])
          ->join([
            'table' => 'partner_commissions',
            'alias' => 'PartnerCommissionEntity',
            'conditions' => 'PartnerCommissionEntity.lead_id = SettlementReviewsEntity.lead_id'
          ])
          ->join([
            'table' => 'leads',
            'alias' => 'LeadEntity',
            'conditions' => 'LeadEntity.lead_id = SettlementReviewsEntity.lead_id'
          ])
          ->join([
            'table' => 'partner_user_leads',
            'alias' => 'PartnerUserLeadsEntity',
            'type' => 'LEFT',
            'conditions' => [
                'PartnerUserLeadsEntity.lead_id = LeadEntity.lead_id',
                'PartnerUserLeadsEntity.status = "ACCESS"'
            ]
          ])
          ->where($whereSettled)
          ->enableHydration(false);
        
        $settlementReviewTotals = $this->getQueryResults('dashboard_settlement_review_totals', $settlementReviewTotalsQuery);
        $settlementReviewTotals = $settlementReviewTotals[0] ?? [];

        //main query //
        $resultData = $leadsTable->find()
          ->join([
            'table' => 'lead_associated_data',
            'alias' => 'LeadAssociatedDataEntity',
            'conditions' => [
                'LeadAssociatedDataEntity.lead_id = LeadEntity.lead_id'
            ]
          ])
          ->join([
            'table' => 'man_statuses',
            'alias' => 'ManStatusEntity',
            'conditions' => [
                'ManStatusEntity.id = LeadEntity.man_status_id'
            ]
          ])
          ->join([
            'table' => $subQueryPC,
            'alias' => 'PartnerCommissionAggregate',
            'type' => 'LEFT',
            'conditions' => [
                'PartnerCommissionAggregate.lead_id = LeadEntity.lead_id'
            ]
          ])
          ->join([
            'table' => $subQueryLCA,
            'alias' => 'LeadCallAttemptAggregate',
            'type' => 'LEFT',
            'conditions' => [
                'LeadCallAttemptAggregate.lead_id = LeadEntity.lead_id'
            ]
          ])
          ->join([
            'table' => $subQueryLSR,
            'alias' => 'LendSignatureRequestAggregate',
            'type' => 'LEFT',
            'conditions' => [
                'LendSignatureRequestAggregate.lead_id = LeadEntity.lead_id'
            ]
          ])
          ->join([
            'table' => 'partner_user_leads',
            'alias' => 'PartnerUserLeadsEntity',
            'type' => 'LEFT',
            'conditions' => [
                'PartnerUserLeadsEntity.lead_id = LeadEntity.lead_id',
                'PartnerUserLeadsEntity.status = "ACCESS"'
            ]
          ])
          ->select([
            'leads_in' => $leadsTable->find()->func()->count('LeadEntity.lead_id'),
            'average_days_to_settle' => new QueryExpression('AVG(COALESCE(PartnerCommissionAggregate.days_to_settle, NULL))'),
            
            'referrals_in' => $leadsTable->find()->func()->count('LeadEntity.referrer_person_id'),
            
            'leads_lost' => $leadsTable->find()->func()->count(
              new QueryExpression('
                CASE 
                  WHEN (LeadEntity.is_closed = 1
                  OR LeadEntity.is_archived = 1) 
                  AND ManStatusEntity.is_settled = 0 
                  THEN 1 
                  ELSE NULL 
                END')
            ),
            
            'lendsign_requests' => new QueryExpression('SUM(COALESCE(LendSignatureRequestAggregate.request_count, 0))'),
            'lendsign_completions' => new QueryExpression('SUM(COALESCE(LendSignatureRequestAggregate.completion_count, 0))'),
            'lendsign_conversion' => new QueryExpression('CASE WHEN SUM(COALESCE(LendSignatureRequestAggregate.request_count, 0)) = 0 THEN 0 
                ELSE SUM(COALESCE(LendSignatureRequestAggregate.completion_count, 0)) / SUM(COALESCE(LendSignatureRequestAggregate.request_count, 0)) END'),
            
            'submission_rate' => $leadsTable->query()->newExpr(
              'IF(COUNT(LeadEntity.lead_id) = 0 , 0, (COUNT(CASE WHEN LeadAssociatedDataEntity.max_sale_id IS NOT NULL THEN 1 ELSE NULL END)/COUNT(LeadEntity.lead_id)))'
            ),
  
            'settlement_rate' => $leadsTable->query()->newExpr(
              'IF(COUNT(LeadEntity.lead_id) = 0 , 0, (COUNT(CASE WHEN PartnerCommissionAggregate.commission_exists > 0 THEN 1 ELSE NULL END)/COUNT(LeadEntity.lead_id)))'
            ),
  
            'call_queue_leads_called' => $leadsTable->find()->func()->sum(
              new QueryExpression('CASE WHEN LeadCallAttemptAggregate.call_queue_leads_called > 0 THEN 1 ELSE 0 END')
            ),
  
            'call_queue_submissions' => $leadsTable->find()->func()->sum(
              new QueryExpression(
                'CASE 
                  WHEN LeadCallAttemptAggregate.call_queue_answered > 0 
                  AND LeadAssociatedDataEntity.max_sale_id IS NOT NULL THEN 1 
                  ELSE 0 
                END')
            ),
  
            'call_queue_conversion' => $leadsTable->query()->newExpr(
              'IF(SUM(CASE WHEN LeadCallAttemptAggregate.call_queue_leads_called > 0 THEN 1 ELSE 0 END) = 0, 0, 
                (SUM(CASE 
                  WHEN LeadCallAttemptAggregate.call_queue_answered > 0 
                  AND LeadAssociatedDataEntity.max_sale_id IS NOT NULL THEN 1 
                  ELSE 0 
                END) / 
                SUM(CASE WHEN LeadCallAttemptAggregate.call_queue_leads_called > 0 THEN 1 ELSE 0 END)))'
            )

          ])
          ->where($whereLeadCreated)
          ->enableHydration(false);

          $resultDataArray = $this->getQueryResults('dashboard_main', $resultData);
          $resultData = $resultDataArray[0] ?? [];

          // Add pre-calculated submission metrics (using correct $whereSubmitted date filter)
          $resultData['submitted_volume'] = $submissionTotals['submitted_volume'] ?? 0;
          $resultData['leads_submitted'] = $submissionTotals['leads_submitted'] ?? 0;
          
          // Add pre-calculated settlement metrics (using correct $whereSettled date filter)
          $resultData['settled_volume'] = $settlementCommissionTotals['settled_volume'] ?? 0;
          $resultData['brokerage_origination'] = $settlementCommissionTotals['brokerage_origination'] ?? 0;
          $resultData['average_settled_volume'] = $settlementCommissionTotals['average_settled_volume'] ?? 0;
          $resultData['average_remuneration'] = $settlementCommissionTotals['average_remuneration'] ?? 0;
          $resultData['overall_settlements'] = $settlementCommissionTotals['overall_settlements'] ?? 0;
          
          $resultData['origination_fees'] = $settlementReviewTotals['origination_fees'] ?? 0;
          $resultData['vbi'] = $settlementReviewTotals['vbi'] ?? 0;
          $resultData['warranty_comms'] = $settlementReviewTotals['warranty_comms'] ?? 0;
          $resultData['insurance_commission'] = ($settlementReviewTotals['comp_ins_comms'] ?? 0) + 
                                                ($settlementReviewTotals['gap_ins_comms'] ?? 0) + 
                                                ($settlementReviewTotals['cci_ins_comms'] ?? 0);
          $resultData['referrer_fees'] = $settlementReviewTotals['referrer_fees'] ?? 0;
          $resultData['other_commissions'] = $settlementReviewTotals['other_commissions'] ?? 0;
          
          // Calculate combined totals
          $brokerage = $resultData['brokerage_origination'];
          $origination = $resultData['origination_fees'];
          $vbi = $resultData['vbi'];
          $warranty = $settlementReviewTotals['warranty_income'] ?? 0;
          $comp_ins = $settlementReviewTotals['comp_ins_comms'] ?? 0;
          $gap_ins = $settlementReviewTotals['gap_ins_comms'] ?? 0;
          $cci_ins = $settlementReviewTotals['cci_ins_comms'] ?? 0;
          $other = $resultData['other_commissions'];
          $referrer = $resultData['referrer_fees'];
          $settlements = $resultData['overall_settlements'];
          
          $totalRemuneration = $brokerage + $origination + $vbi + $warranty + $comp_ins + $gap_ins + $cci_ins + $other;
          $totalExclReferrer = $totalRemuneration - $referrer;
          
          $resultData['total_remuneration'] = $totalRemuneration;
          $resultData['total_excl_referrer_fees'] = $totalExclReferrer;
          $resultData['average_lead_remuneration'] = $settlements > 0 ? $totalRemuneration / $settlements : 0;
          $resultData['average_excl_referrer_fees'] = $settlements > 0 ? $totalExclReferrer / $settlements : 0;

          $resultData['pie_lenders'] = $this->getPieChartDataLenders($whereLeadCreated, $whereSubmitted, $whereSettled);
          $resultData['pie_product_types'] = $this->getPieChartDataProductTypes($whereLeadCreated, $whereSubmitted, $whereSettled);
          $resultData['pie_sources'] = $this->getPieChartDataLeadSources($whereLeadCreated, $whereSubmitted, $whereSettled);
          $resultData['pie_referrers'] = $this->getPieChartDataLeadReferrers($whereLeadCreated, $whereSubmitted, $whereSettled);//inefficient
          $resultData['pie_call_queue_pipeline'] = $this->getPieChartDataCallQueuePipeline($whereLeadCreated);
          $resultData['pie_lendsign_statuses'] = [
              [
                  'lendsign_status' => 'lendsign_requests',
                  'count' => (int)$resultData['lendsign_requests']
              ],
              [
                  'lendsign_status' => 'lendsign_completions',
                  'count' => (int)$resultData['lendsign_completions']
              ]
          ];
          
          $resultData['lead_pipeline'] = $this->getLeadPipeline($whereLeadCreated, $whereSettled);

          return $this->setJsonResponse($resultData);
      } catch (\Exception $e) {
        Log::error($e->getMessage());
        Log::error($e->getTraceAsString());
        return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
      }
    }

  /**
   * Get query results from cache if present otherwise get results, store in cache and return
   * If force_refresh is true - get results, store in cache and return 
   */
  private function getQueryResults($cachePrefix, $query, $postProcessFunctionName = null){
    $cacheKey = $cachePrefix.'_'.$this->query_where_hash;

    $fetchAndProcess = function () use ($query, $postProcessFunctionName) {
      $results = $query->hydrate(false)->all()->toArray();
      if ($postProcessFunctionName && method_exists($this, $postProcessFunctionName)) {
        return $this->$postProcessFunctionName($results);
      }
      return $results;
    };

    $results = [];
    if($this->query_force_refresh){
      $results = $fetchAndProcess();
      Cache::write($cacheKey, $results, $this->query_cache);
      return $results;
    }
    return Cache::remember($cacheKey, function () use ($fetchAndProcess) {
      return $fetchAndProcess();
    }, $this->query_cache);

  }

  private function getPieChartDataLenders($whereLeadCreated, $whereSubmitted, $whereSettled)
  {
    $results = [];
    $salesTable = TableRegistry::getTableLocator()->get('SaleEntity');
    $offPanelLenderId = Configure::read('Lend.OFF_PANEL_LENDER_ID');
    
    // Using SQL CASE for submitted lenders to handle both regular and off-panel lenders
    $submittedQuery = $salesTable->find();
    $caseExpr = $submittedQuery->newExpr()->add(
        "CASE WHEN LenderEntity.lender_id = $offPanelLenderId AND SaleEntity.off_panel_lender IS NOT NULL " .
        "THEN SaleEntity.off_panel_lender ELSE LenderEntity.lender_name END"
    );
    
    $submittedQuery
      ->select([
        'lender' => $caseExpr,
        'count' => $submittedQuery->func()->count('SaleEntity.sale_id')
      ])
      ->join([
        'table' => 'lender_product',
        'alias' => 'LenderProductEntity',
        'conditions' => 'LenderProductEntity.lender_product_id = SaleEntity.product_id'
      ])
      ->join([
        'table' => 'lenders',
        'alias' => 'LenderEntity',
        'conditions' => 'LenderEntity.lender_id = LenderProductEntity.lender_id'
      ])
      ->join([
        'table' => 'leads',
        'alias' => 'LeadEntity',
        'conditions' => 'LeadEntity.lead_id = SaleEntity.lead_id'
      ])
      ->join([
        'table' => 'partner_user_leads',
        'alias' => 'PartnerUserLeadsEntity',
        'type' => 'LEFT',
        'conditions' => 'PartnerUserLeadsEntity.lead_id = LeadEntity.lead_id AND PartnerUserLeadsEntity.status = "ACCESS"'
      ])
      ->where($whereSubmitted)
      ->group(['lender'])
      ->orderDesc('count')
      ->enableHydration(false);
    
    $results['submitted'] = $this->getQueryResults('dashboard_pie_lenders_submitted', $submittedQuery, 'getPieChartDataLendersFormatData');
    
    //settled
    $partnerCommissionsTable = TableRegistry::getTableLocator()->get('PartnerCommissionEntity');
    
    // Using SQL CASE for settled lenders
    $settledQuery = $partnerCommissionsTable->find();
    $caseExpr = $settledQuery->newExpr()->add(
        "CASE WHEN LenderEntity.lender_id = $offPanelLenderId AND SaleEntity.off_panel_lender IS NOT NULL " .
        "THEN SaleEntity.off_panel_lender ELSE LenderEntity.lender_name END"
    );
    
    $settledQuery
      ->select([
        'lender' => $caseExpr,
        'count' => $settledQuery->func()->count('PartnerCommissionEntity.commission_id')
      ])
      ->join([
        'table' => 'sale_outcomes',
        'alias' => 'SaleOutcomesEntity',
        'conditions' => 'SaleOutcomesEntity.sale_outcome_id = PartnerCommissionEntity.sale_outcome_id'
      ])
      ->join([
        'table' => 'sales',
        'alias' => 'SaleEntity',
        'conditions' => 'SaleEntity.sale_id = SaleOutcomesEntity.sale_id'
      ])
      ->join([
        'table' => 'lender_product',
        'alias' => 'LenderProductEntity',
        'conditions' => 'LenderProductEntity.lender_product_id = SaleEntity.product_id'
      ])
      ->join([
        'table' => 'lenders',
        'alias' => 'LenderEntity',
        'conditions' => 'LenderEntity.lender_id = LenderProductEntity.lender_id'
      ])
      ->join([
        'table' => 'leads',
        'alias' => 'LeadEntity',
        'conditions' => 'LeadEntity.lead_id = PartnerCommissionEntity.lead_id'
      ])
      ->join([
        'table' => 'partner_user_leads',
        'alias' => 'PartnerUserLeadsEntity',
        'type' => 'LEFT',
        'conditions' => 'PartnerUserLeadsEntity.lead_id = LeadEntity.lead_id AND PartnerUserLeadsEntity.status = "ACCESS"'
      ])
      ->where($whereSettled)
      ->group(['lender'])
      ->orderDesc('count')
      ->enableHydration(false);
    
    $results['settled'] = $this->getQueryResults('dashboard_pie_lenders_settled', $settledQuery, 'getPieChartDataLendersFormatData');

    return $results;
  }

  /**
   * Reformat lender data to extract and add off-panel flag and lender name for off panel lenders
   */
  private function getPieChartDataLendersFormatData($results){
    $lenderData = [];
    foreach ($results as $row) {
      $lenderName = '';
      $isOffPanel = false;
      
      $jsonLenderName = '';
      if(is_string($row['lender'])){
        $data = json_decode($row['lender'], true);
        $jsonLenderName = (json_last_error() == JSON_ERROR_NONE && isset($data['lender_name'])) ? $data['lender_name'] : '';
      }
  
      if (!empty($jsonLenderName)) {
          // This is an off-panel lender with JSON data
          $lenderName = $jsonLenderName;
          $isOffPanel = true;
      } else {
          // Regular lender
          $lenderName = $row['lender'];
      }
      
      // Skip if we couldn't get a lender name
      if (empty($lenderName)) {
          continue;
      }
      
      // Add to results
      $lenderData[] = [
        'lender_name' => $lenderName,
        'leads' => $row['count'],
        'offpanel' => $isOffPanel
      ];
    }
    return $lenderData;
  }

    private function getPieChartDataProductTypes($whereLeadCreated, $whereSubmitted, $whereSettled){
      $results = [];
      $leadsTable = TableRegistry::getTableLocator()->get('LeadEntity');
      //all leads
      $query = $leadsTable->find()
        ->select([
          'product_type_id' => 'PartnerProductTypeEntity.product_type_id',
          'product_type_name' => 'PartnerProductTypeEntity.product_type_name',
          'leads' => $leadsTable->find()->func()->count('LeadEntity.lead_id')
        ])
        ->join([
          'table' => 'partner_product_types',
          'alias' => 'PartnerProductTypeEntity',
          'conditions' => 'PartnerProductTypeEntity.product_type_id = LeadEntity.product_type_id'
        ])
        ->join([
          'table' => 'partner_user_leads',
          'alias' => 'PartnerUserLeadsEntity',
          'type' => 'LEFT',
          'conditions' => 'PartnerUserLeadsEntity.lead_id = LeadEntity.lead_id AND PartnerUserLeadsEntity.status = "ACCESS"'
        ])
        ->group('PartnerProductTypeEntity.product_type_id')
        ->where($whereLeadCreated)
        ->enableHydration(false);
      $results['all_leads'] = $this->getQueryResults('dashboard_pie_product_types_all', $query);
      //submitted
      $query = $leadsTable->find()
        ->select([
          'product_type_id' => 'PartnerProductTypeEntity.product_type_id',
          'product_type_name' => 'PartnerProductTypeEntity.product_type_name',
          'leads' => $leadsTable->find()->func()->count('LeadEntity.lead_id')
        ])
        ->join([
          'table' => 'sales',
          'alias' => 'SaleEntity',
          'conditions' => 'SaleEntity.lead_id = LeadEntity.lead_id'
        ])
        ->join([
          'table' => 'partner_product_types',
          'alias' => 'PartnerProductTypeEntity',
          'conditions' => 'PartnerProductTypeEntity.product_type_id = SaleEntity.product_type_id'
        ])
        ->join([
          'table' => 'partner_user_leads',
          'alias' => 'PartnerUserLeadsEntity',
          'type' => 'LEFT',
          'conditions' => 'PartnerUserLeadsEntity.lead_id = LeadEntity.lead_id AND PartnerUserLeadsEntity.status = "ACCESS"'
        ])
        ->group('PartnerProductTypeEntity.product_type_id')
        ->where($whereSubmitted)
        ->enableHydration(false);
        $results['submitted'] = $this->getQueryResults('dashboard_pie_product_types_submitted', $query);
      //settled
      $query = $leadsTable->find()
        ->select([
          'product_type_id' => 'PartnerProductTypeEntity.product_type_id',
          'product_type_name' => 'PartnerProductTypeEntity.product_type_name',
          'leads' => $leadsTable->find()->func()->count('LeadEntity.lead_id')
        ])
        ->join([
          'table' => 'partner_commissions',
          'alias' => 'PartnerCommissionEntity',
          'conditions' => 'PartnerCommissionEntity.lead_id = LeadEntity.lead_id'
        ])
        ->join([
          'table' => 'sale_outcomes',
          'alias' => 'SaleOutcomesEntity',
          'conditions' => 'SaleOutcomesEntity.sale_outcome_id = PartnerCommissionEntity.sale_outcome_id'
        ])
        ->join([
          'table' => 'sales',
          'alias' => 'SaleEntity',
          'conditions' => 'SaleEntity.sale_id = SaleOutcomesEntity.sale_id'
        ])
        ->join([
          'table' => 'partner_product_types',
          'alias' => 'PartnerProductTypeEntity',
          'conditions' => 'PartnerProductTypeEntity.product_type_id = SaleEntity.product_type_id'
        ])
        ->join([
          'table' => 'partner_user_leads',
          'alias' => 'PartnerUserLeadsEntity',
          'type' => 'LEFT',
          'conditions' => 'PartnerUserLeadsEntity.lead_id = LeadEntity.lead_id AND PartnerUserLeadsEntity.status = "ACCESS"'
        ])
        ->group('PartnerProductTypeEntity.product_type_id')
        ->where($whereSettled)
        ->enableHydration(false);
        $results['settled'] = $this->getQueryResults('dashboard_pie_product_types_settled', $query);
      return $results;
    }

    private function getPieChartDataLeadSources($whereLeadCreated, $whereSubmitted, $whereSettled){
      $results = [];
      $leadsTable = TableRegistry::getTableLocator()->get('LeadEntity');
      //all leads
      $query = $leadsTable->find()
        ->select([
          'source' => 'LeadEntity.source',
          'leads' => $leadsTable->find()->func()->count('LeadEntity.lead_id')
        ])
        ->join([
          'table' => 'partner_user_leads',
          'alias' => 'PartnerUserLeadsEntity',
          'type' => 'LEFT',
          'conditions' => 'PartnerUserLeadsEntity.lead_id = LeadEntity.lead_id AND PartnerUserLeadsEntity.status = "ACCESS"'
        ])
        ->group('LeadEntity.source')
        ->where($whereLeadCreated)
        ->where('LeadEntity.source IS NOT NULL')
        ->enableHydration(false);
        $results['all_leads'] = $this->getQueryResults('dashboard_pie_sources_all', $query);
      //submitted
      $query = $leadsTable->find()
        ->select([
          'source' => 'LeadEntity.source',
          'leads' => $leadsTable->find()->func()->count('DISTINCT LeadEntity.lead_id')
        ])
        ->join([
          'table' => 'sales',
          'alias' => 'SaleEntity',
          'conditions' => 'SaleEntity.lead_id = LeadEntity.lead_id'
        ])
        ->join([
          'table' => 'partner_user_leads',
          'alias' => 'PartnerUserLeadsEntity',
          'type' => 'LEFT',
          'conditions' => 'PartnerUserLeadsEntity.lead_id = LeadEntity.lead_id AND PartnerUserLeadsEntity.status = "ACCESS"'
        ])
        ->group('LeadEntity.source')
        ->where($whereSubmitted)
        ->where('LeadEntity.source IS NOT NULL')
        ->enableHydration(false);
        // echo $query->sql();
        $results['submitted'] = $this->getQueryResults('dashboard_pie_sources_submitted', $query);
      //settled
      $query = $leadsTable->find()
        ->select([
          'source' => 'LeadEntity.source',
          'leads' => $leadsTable->find()->func()->count('DISTINCT LeadEntity.lead_id')
        ])
        ->join([
          'table' => 'partner_commissions',
          'alias' => 'PartnerCommissionEntity',
          'conditions' => 'PartnerCommissionEntity.lead_id = LeadEntity.lead_id'
        ])
        ->join([
          'table' => 'partner_user_leads',
          'alias' => 'PartnerUserLeadsEntity',
          'type' => 'LEFT',
          'conditions' => 'PartnerUserLeadsEntity.lead_id = LeadEntity.lead_id AND PartnerUserLeadsEntity.status = "ACCESS"'
        ])
        ->group('LeadEntity.source')
        ->where($whereSettled)
        ->where('LeadEntity.source IS NOT NULL')
        ->enableHydration(false);
        $results['settled'] = $this->getQueryResults('dashboard_pie_sources_settled', $query);
      return $results;
    }

    private function getPieChartDataLeadReferrers($whereLeadCreated, $whereSubmitted, $whereSettled){
      $results = [];
      $leadsTable = TableRegistry::getTableLocator()->get('LeadEntity');
      //all leads
      $query = $leadsTable->find()
        ->select([
          'referrer' => 'ReferrerEntity.nickname',
          'leads' => $leadsTable->find()->func()->count('LeadEntity.lead_id')
        ])
        ->join([
          'table' => 'referrer_people',
          'alias' => 'ReferrerPeopleEntity',
          'conditions' => 'ReferrerPeopleEntity.id = LeadEntity.referrer_person_id'
        ])
        ->join([
          'table' => 'referrers',
          'alias' => 'ReferrerEntity',
          'conditions' => 'ReferrerEntity.id = ReferrerPeopleEntity.referrer_id'
        ])
        ->join([
          'table' => 'partner_user_leads',
          'alias' => 'PartnerUserLeadsEntity',
          'type' => 'LEFT',
          'conditions' => 'PartnerUserLeadsEntity.lead_id = LeadEntity.lead_id AND PartnerUserLeadsEntity.status = "ACCESS"'
        ])
        ->group('ReferrerEntity.nickname')
        ->where($whereLeadCreated)
        ->enableHydration(false);
      $results['all_leads'] = $this->getQueryResults('dashboard_pie_referrers_all', $query);
      //submitted
      $query = $leadsTable->find()
        ->select([
          'referrer' => 'ReferrerEntity.nickname',
          'leads' => $leadsTable->find()->func()->count('DISTINCT LeadEntity.lead_id')
        ])
        ->join([
          'table' => 'sales',
          'alias' => 'SaleEntity',
          'conditions' => 'SaleEntity.lead_id = LeadEntity.lead_id'
        ])
        ->join([
          'table' => 'referrer_people',
          'alias' => 'ReferrerPeopleEntity',
          'conditions' => 'ReferrerPeopleEntity.id = LeadEntity.referrer_person_id'
        ])
        ->join([
          'table' => 'referrers',
          'alias' => 'ReferrerEntity',
          'conditions' => 'ReferrerEntity.id = ReferrerPeopleEntity.referrer_id'
        ])
        ->join([
          'table' => 'partner_user_leads',
          'alias' => 'PartnerUserLeadsEntity',
          'type' => 'LEFT',
          'conditions' => 'PartnerUserLeadsEntity.lead_id = LeadEntity.lead_id AND PartnerUserLeadsEntity.status = "ACCESS"'
        ])
        ->group('ReferrerEntity.nickname')
        ->where($whereSubmitted)
        ->enableHydration(false);
        $results['submitted'] = $this->getQueryResults('dashboard_pie_referrers_submitted', $query);
      //settled
      $query = $leadsTable->find()
        ->select([
          'referrer' => 'ReferrerEntity.nickname',
          'leads' => $leadsTable->find()->func()->count('DISTINCT LeadEntity.lead_id')
        ])
        ->join([
          'table' => 'partner_commissions',
          'alias' => 'PartnerCommissionEntity',
          'conditions' => 'PartnerCommissionEntity.lead_id = LeadEntity.lead_id'
        ])
        ->join([
          'table' => 'referrer_people',
          'alias' => 'ReferrerPeopleEntity',
          'conditions' => 'ReferrerPeopleEntity.id = LeadEntity.referrer_person_id'
        ])
        ->join([
          'table' => 'referrers',
          'alias' => 'ReferrerEntity',
          'conditions' => 'ReferrerEntity.id = ReferrerPeopleEntity.referrer_id'
        ])
        ->join([
          'table' => 'partner_user_leads',
          'alias' => 'PartnerUserLeadsEntity',
          'type' => 'LEFT',
          'conditions' => 'PartnerUserLeadsEntity.lead_id = LeadEntity.lead_id AND PartnerUserLeadsEntity.status = "ACCESS"'
        ])
        ->group('ReferrerEntity.nickname')
        ->where($whereSettled)
        ->enableHydration(false);
        $results['settled'] = $this->getQueryResults('dashboard_pie_referrers_settled', $query);
      return $results;
    }

    private function getPieChartDataCallQueuePipeline($whereLeadCreated){
      $leadsTable = TableRegistry::getTableLocator()->get('LeadEntity');
      $query = $leadsTable->find()
        ->select([
          'call_queue_status' => 'LeadEntity.call_queue_status',
          'leads' => $leadsTable->find()->func()->count('LeadEntity.lead_id')
        ])
        ->join([
          'table' => 'partner_user_leads',
          'alias' => 'PartnerUserLeadsEntity',
          'type' => 'LEFT',
          'conditions' => 'PartnerUserLeadsEntity.lead_id = LeadEntity.lead_id AND PartnerUserLeadsEntity.status = "ACCESS"'
        ])
        ->group('LeadEntity.call_queue_status')
        ->where($whereLeadCreated)
        ->enableHydration(false);

      return $this->getQueryResults('dashboard_pie_call_queue', $query, 'getPieChartDataCallQueuePipelineFormatData');
    }

    private function getPieChartDataCallQueuePipelineFormatData($rows){
      $statusMap = [
        -5 => "Call queue not proceeding",
        -4 => "Max attempts not reached in time",
        -3 => "Not in queue, max attempts reached",
        -2 => "Currently viewed by operator",
        -1 => "Not in queue",
        0 => "Not called yet",
        1 => "1 attempt",
      ];
      foreach ($rows as $k => $row) {
        $statusId = $rows[$k]['call_queue_status'];
        $rows[$k]['call_queue_status'] = $statusMap[$statusId] ?? ($statusId . ' attempts');
      }
      return $rows;
    }

    private function getLeadPipeline($whereLeadCreated, $whereSettled){
      $leadsTable = TableRegistry::getTableLocator()->get('LeadEntity');
      
      // Query for non-settled statuses (use lead created date)
      // whereLeadCreated - dont want modify the original
      $whereLeadCreatedCopy = array_merge([], $whereLeadCreated);
      $whereLeadCreatedCopy['ManStatusGroupEntity.active'] = 1;
      $whereLeadCreatedCopy['ManStatusEntity.active'] = 1;
      $whereLeadCreatedCopy['ManStatusEntity.is_settled'] = 0;

      $nonSettledQuery = $leadsTable->find()
        ->select([
          'group_name' => 'ManStatusGroupEntity.group_name',
          'status_name' => 'ManStatusEntity.status_name',
          'leads' => $leadsTable->find()->func()->count('LeadEntity.lead_id'),
          'is_settled' => 'ManStatusEntity.is_settled',
          'group_order' => 'ManStatusGroupEntity.order',
          'status_order' => 'ManStatusEntity.order'
        ])
        ->join([
          'table' => 'man_statuses',
          'alias' => 'ManStatusEntity',
          'conditions' => 'ManStatusEntity.id = LeadEntity.man_status_id'
        ])
        ->join([
          'table' => 'man_status_groups',
          'alias' => 'ManStatusGroupEntity',
          'conditions' => 'ManStatusGroupEntity.id = ManStatusEntity.man_status_group_id'
        ])
        ->join([
          'table' => 'partner_user_leads',
          'alias' => 'PartnerUserLeadsEntity',
          'type' => 'LEFT',
          'conditions' => 'PartnerUserLeadsEntity.lead_id = LeadEntity.lead_id AND PartnerUserLeadsEntity.status = "ACCESS"'
        ])
        ->group(['ManStatusGroupEntity.group_name', 'ManStatusEntity.status_name'])
        ->where($whereLeadCreatedCopy)
        ->order(['ManStatusGroupEntity.order','ManStatusEntity.order'])
        ->enableHydration(false);

      // Query for settled statuses (use settlement date)
      $partnerCommissionsTable = TableRegistry::getTableLocator()->get('PartnerCommissionEntity');
      $whereSettledCopy = array_merge([], $whereSettled);
      $whereSettledCopy['ManStatusGroupEntity.active'] = 1;
      $whereSettledCopy['ManStatusEntity.active'] = 1;
      $whereSettledCopy['ManStatusEntity.is_settled'] = 1;

      $settledQuery = $partnerCommissionsTable->find()
        ->select([
          'group_name' => 'ManStatusGroupEntity.group_name',
          'status_name' => 'ManStatusEntity.status_name',
          'leads' => $partnerCommissionsTable->find()->func()->count('DISTINCT LeadEntity.lead_id'),
          'is_settled' => 'ManStatusEntity.is_settled',
          'group_order' => 'ManStatusGroupEntity.order',
          'status_order' => 'ManStatusEntity.order'
        ])
        ->join([
          'table' => 'leads',
          'alias' => 'LeadEntity',
          'conditions' => 'LeadEntity.lead_id = PartnerCommissionEntity.lead_id'
        ])
        ->join([
          'table' => 'man_statuses',
          'alias' => 'ManStatusEntity',
          'conditions' => 'ManStatusEntity.id = LeadEntity.man_status_id'
        ])
        ->join([
          'table' => 'man_status_groups',
          'alias' => 'ManStatusGroupEntity',
          'conditions' => 'ManStatusGroupEntity.id = ManStatusEntity.man_status_group_id'
        ])
        ->join([
          'table' => 'partner_user_leads',
          'alias' => 'PartnerUserLeadsEntity',
          'type' => 'LEFT',
          'conditions' => 'PartnerUserLeadsEntity.lead_id = LeadEntity.lead_id AND PartnerUserLeadsEntity.status = "ACCESS"'
        ])
        ->group(['ManStatusGroupEntity.group_name', 'ManStatusEntity.status_name'])
        ->where($whereSettledCopy)
        ->order(['ManStatusGroupEntity.order','ManStatusEntity.order'])
        ->enableHydration(false);

      // Get results from both queries
      $nonSettledResults = $this->getQueryResults('dashboard_lead_pipeline_non_settled', $nonSettledQuery);
      $settledResults = $this->getQueryResults('dashboard_lead_pipeline_settled', $settledQuery);

      // Merge results
      $combinedResults = array_merge($nonSettledResults, $settledResults);

      return $this->getLeadPipelineFormatData($combinedResults);
    }

    /**
     * Reformat results to group by group_name and merge counts for same status
     * Preserves original status ordering
     */
    private function getLeadPipelineFormatData($rows){
      $groupedResults = [];
      foreach ($rows as $row) {
        $groupName = $row['group_name'];
        $statusName = $row['status_name'];
        $groupOrder = isset($row['group_order']) ? (int)$row['group_order'] : 0;
        $statusOrder = isset($row['status_order']) ? (int)$row['status_order'] : 0;
        // Initialize group if not exists
        if (!isset($groupedResults[$groupName])) {
          $groupedResults[$groupName] = [
            'group_name' => $groupName,
            'group_order' => $groupOrder,
            'statuses' => []
          ];
        }
        // Check if status already exists in this group
        $statusExists = false;
        foreach ($groupedResults[$groupName]['statuses'] as $key => $status) {
          if ($status['status_name'] === $statusName) {
            // Merge counts if status already exists
            $groupedResults[$groupName]['statuses'][$key]['leads'] += (int)$row['leads'];
            $statusExists = true;
            break;
          }
        } 
        // Add new status if it doesn't exist
        if (!$statusExists) {
          $groupedResults[$groupName]['statuses'][] = [
            'status_name' => $statusName,
            'leads' => (int)$row['leads'],
            'status_order' => $statusOrder
          ];
        }
      }
      // Sort groups by group_order
      uasort($groupedResults, function($a, $b) {
        return $a['group_order'] <=> $b['group_order'];
      });
      
      // Sort statuses within each group by status_order
      foreach ($groupedResults as $groupName => $group) {
        usort($groupedResults[$groupName]['statuses'], function($a, $b) {
          return $a['status_order'] <=> $b['status_order'];
        });
        
        // Remove order fields from final output
        foreach ($groupedResults[$groupName]['statuses'] as $key => $status) {
          unset($groupedResults[$groupName]['statuses'][$key]['status_order']);
        }
        unset($groupedResults[$groupName]['group_order']);
      }
      
      return array_values($groupedResults);
    }

    public function run(){
      try{
        $partner_user = $this->Auth->identify();
        $filters = $this->request->getData();
        $dash = new DashboardAnalyticsService($partner_user['partner_user_id']);
        $filters['partner_id'] = $partner_user['partner_id'];
        $filters['reports'] = [
          "loan_amount_by_status",
          "lead_count_by_status",
          "loan_amount_by_lender",
          "count_of_product_type",
          "loan_amount_by_user",
          "funded_and_total_commission",
          "lend_score_by_lead_count"
        ];
        $filters['start_date'] = !empty($filters['start_date']) ? date('Y-m-d 00:00:00', strtotime($filters['start_date'])) : null;
        $filters['end_date'] = !empty($filters['end_date']) ? date('Y-m-d 23:59:59', strtotime($filters['end_date'])) : null;
        if(empty($partner_user['account_admin']) AND empty($partner_user['access_all_leads']))
          $filters['partner_user_id'] = [$partner_user['partner_user_id']];

        $filters = $this->_unsetEmptyArray($filters);
        $result = $dash->run($filters);
        return $this->setJsonResponse($result);
      }catch(\Exception $e){
        Log::error($e->getMessage());
        Log::error($e->getTraceAsString());
        return $this->setJsonResponse(['success'=>false, 'message'=>$e->getMessage()]);
      }
    }

    private function getSubmissionMetrics($whereSubmitted) {
        $leadsTable = TableRegistry::getTableLocator()->get('LeadEntity');
        $saleDetailsTable = TableRegistry::getTableLocator()->get('SaleDetailEntity');
        
        // Submission volume - sum of all submitted amounts
        $submissionVolumeQuery = $saleDetailsTable->find()
            ->select([
                'submitted_volume' => $saleDetailsTable->find()->func()->sum('SaleDetailEntity.financed_amount')
            ])
            ->join([
                'table' => 'sales',
                'alias' => 'SaleEntity',
                'conditions' => 'SaleDetailEntity.sale_id = SaleEntity.sale_id'
            ])
            ->join([
                'table' => 'leads',
                'alias' => 'LeadEntity',
                'conditions' => 'LeadEntity.lead_id = SaleEntity.lead_id'
            ])
            ->join([
                'table' => 'partner_user_leads',
                'alias' => 'PartnerUserLeadsEntity',
                'type' => 'LEFT',
                'conditions' => [
                    'PartnerUserLeadsEntity.lead_id = LeadEntity.lead_id',
                    'PartnerUserLeadsEntity.status = "ACCESS"'
                ]
            ])
            ->where($whereSubmitted)
            ->enableHydration(false);
        
        // Leads submitted count - count of distinct leads that have sales
        $leadsSubmittedQuery = $leadsTable->find()
            ->select([
                'leads_submitted' => $leadsTable->find()->func()->count('DISTINCT LeadEntity.lead_id')
            ])
            ->join([
                'table' => 'sales',
                'alias' => 'SaleEntity',
                'conditions' => 'SaleEntity.lead_id = LeadEntity.lead_id'
            ])
            ->join([
                'table' => 'partner_user_leads',
                'alias' => 'PartnerUserLeadsEntity',
                'type' => 'LEFT',
                'conditions' => [
                    'PartnerUserLeadsEntity.lead_id = LeadEntity.lead_id',
                    'PartnerUserLeadsEntity.status = "ACCESS"'
                ]
            ])
            ->where($whereSubmitted)
            ->enableHydration(false);
        
        $submissionVolume = $submissionVolumeQuery->first();
        $leadsSubmitted = $leadsSubmittedQuery->first();
        
        return [
            'submitted_volume' => $submissionVolume['submitted_volume'] ?? 0, // Submitted Volume
            'leads_submitted' => $leadsSubmitted['leads_submitted'] ?? 0 // Leads to Submission
        ];
    }
    
    private function getSettlementMetrics($whereSettled) {
        $partnerCommissionsTable = TableRegistry::getTableLocator()->get('PartnerCommissionEntity');
        $settlementReviewsTable = TableRegistry::getTableLocator()->get('SettlementReviewsEntity');
        
        $query = $partnerCommissionsTable->find()
            ->select([
                'settled_volume' => $partnerCommissionsTable->find()->func()->sum('PartnerCommissionEntity.funded_amount'),
                'brokerage_origination' => $partnerCommissionsTable->find()->func()->sum('PartnerCommissionEntity.commission'),
                'average_settled_volume' => $partnerCommissionsTable->find()->func()->avg('PartnerCommissionEntity.funded_amount'),
                'overall_settlements' => $partnerCommissionsTable->find()->func()->count('PartnerCommissionEntity.commission_id'),
                'average_remuneration' => $partnerCommissionsTable->find()->func()->avg('PartnerCommissionEntity.commission')
            ])
            ->join([
                'table' => 'leads',
                'alias' => 'LeadEntity',
                'conditions' => 'LeadEntity.lead_id = PartnerCommissionEntity.lead_id'
            ])
            ->join([
                'table' => 'partner_user_leads',
                'alias' => 'PartnerUserLeadsEntity',
                'type' => 'LEFT',
                'conditions' => [
                    'PartnerUserLeadsEntity.lead_id = LeadEntity.lead_id',
                    'PartnerUserLeadsEntity.status = "ACCESS"'
                ]
            ])
            ->where($whereSettled)
            ->enableHydration(false);
        
        $result = $query->first();
        
        // Get settlement review metrics
        $settlementQuery = $settlementReviewsTable->find()
            ->select([
                'origination_fees' => $settlementReviewsTable->find()->func()->sum('SettlementReviewsEntity.origination_fee'),
                'vbi' => $settlementReviewsTable->find()->func()->sum('SettlementReviewsEntity.vbi_income'),
                'warranty_comms' => $settlementReviewsTable->find()->func()->sum('SettlementReviewsEntity.warranty_comms'),
                'comp_ins_comms' => $settlementReviewsTable->find()->func()->sum('SettlementReviewsEntity.comp_ins_comms'),
                'gap_ins_comms' => $settlementReviewsTable->find()->func()->sum('SettlementReviewsEntity.gap_ins_comms'),
                'cci_ins_comms' => $settlementReviewsTable->find()->func()->sum('SettlementReviewsEntity.cci_ins_comms'),
                'referrer_fees' => $settlementReviewsTable->find()->func()->sum('SettlementReviewsEntity.referrer_commission'),
                'other_commissions' => $settlementReviewsTable->find()->func()->sum('SettlementReviewsEntity.other_income'),
                'warranty_income' => $settlementReviewsTable->find()->func()->sum('SettlementReviewsEntity.warranty_income')
            ])
            ->join([
                'table' => 'partner_commissions',
                'alias' => 'PartnerCommissionEntity',
                'conditions' => 'PartnerCommissionEntity.lead_id = SettlementReviewsEntity.lead_id'
            ])
            ->join([
                'table' => 'leads',
                'alias' => 'LeadEntity',
                'conditions' => 'LeadEntity.lead_id = SettlementReviewsEntity.lead_id'
            ])
            ->join([
                'table' => 'partner_user_leads',
                'alias' => 'PartnerUserLeadsEntity',
                'type' => 'LEFT',
                'conditions' => [
                    'PartnerUserLeadsEntity.lead_id = LeadEntity.lead_id',
                    'PartnerUserLeadsEntity.status = "ACCESS"'
                ]
            ])
            ->where($whereSettled)
            ->enableHydration(false);
        
        $settlementResult = $settlementQuery->first();
        
        // Calculate combined metrics
        $brokerage = $result['brokerage_origination'] ?? 0;
        $origination = $settlementResult['origination_fees'] ?? 0;
        $vbi = $settlementResult['vbi'] ?? 0;
        $warranty = $settlementResult['warranty_income'] ?? 0;
        $comp_ins = $settlementResult['comp_ins_comms'] ?? 0;
        $gap_ins = $settlementResult['gap_ins_comms'] ?? 0;
        $cci_ins = $settlementResult['cci_ins_comms'] ?? 0;
        $other = $settlementResult['other_commissions'] ?? 0;
        $referrer = $settlementResult['referrer_fees'] ?? 0;
        
        $totalRemuneration = $brokerage + $origination + $vbi + $warranty + $comp_ins + $gap_ins + $cci_ins + $other;
        $totalExclReferrer = $totalRemuneration - $referrer;
        $settlements = $result['overall_settlements'] ?? 0;
        
        return [
            'settled_volume' => $result['settled_volume'] ?? 0,
            'brokerage_origination' => $brokerage,
            'average_settled_volume' => $result['average_settled_volume'] ?? 0,
            'average_remuneration' => $result['average_remuneration'] ?? 0,
            'overall_settlements' => $settlements,
            'origination_fees' => $origination,
            'vbi' => $vbi,
            'warranty_comms' => $settlementResult['warranty_comms'] ?? 0,
            'insurance_commission' => $comp_ins + $gap_ins + $cci_ins,
            'referrer_fees' => $referrer,
            'other_commissions' => $other,
            'total_remuneration' => $totalRemuneration,
            'total_excl_referrer_fees' => $totalExclReferrer,
            'average_lead_remuneration' => $settlements > 0 ? $totalRemuneration / $settlements : 0,
            'average_excl_referrer_fees' => $settlements > 0 ? $totalExclReferrer / $settlements : 0
        ];
    }

    private function _unsetEmptyArray($arr){
      foreach($arr as $key => $val){
        if(empty($val) && !is_numeric($val)) unset($arr[$key]);
        elseif(is_array($val)) $arr[$key] = $this->_unsetEmptyArray($val);

        if(empty($arr[$key]) && !is_numeric($arr[$key])) unset($arr[$key]);
      }
      return $arr;
    }

    private function _getFilterItems(){
      $filter_items = Cache::read($this->filter_items_key, $this->cache_config);
      if(empty($filter_items)){
        $filter_items = $this->_getFilterItemsFromDb();
        Cache::write($this->filter_items_key, json_encode($filter_items), $this->cache_config);
      }else{
        $filter_items = json_decode($filter_items, true);
      }
      $partner_user = $this->Auth->identify();
      $filter_items['campaign'] = $this->loadModel('Leads')->getDistinctCampaignByPartnerId($partner_user['partner_id']);
      return $filter_items;
    }

    private function _getFilterItemsFromDb(){
      try{
        $partner_user = $this->Auth->identify();
        $partner_country = strtoupper(getenv('REGION', true));
        $filter_items = [];
        $this->loadModel('Leads');
        $this->loadModel('PartnerProductTypes');
        $this->loadModel('Lenders');
        $config_model = new Config;

        if(!empty($partner_user['account_admin'])){
          $filter_items['partner_users'] = $this->loadModel('PartnerUsers')->getActiveUsers($partner_user['partner_id'], false);
          if($unassigned_leads = $this->loadModel('LeadEntity')->checkUnassignedLeads($partner_user['partner_id'])){
            array_push($filter_items['partner_users'], ['partner_user_id'=>0, 'name'=>'Unassigned Leads']);
          }
        }
        $filter_items['source'] = $this->Leads->getDistinctSourceByPartnerId($partner_user['partner_id']);
        $filter_items['lenders'] = $this->Lenders->leaveSpecificFields(
          $this->Lenders->getLenders([
            'status'=>1,
            'country' => $partner_country,
          ]),
          ['lender_id', 'lender_name']
        );
        $filter_items['status'] = ["Attempting", "In Progress", "Pending", "Rejected", "Settled"];
        $filter_items['product_types'] = $this->_generateTreeStructure($this->PartnerProductTypes->getPartnerProductTypes(['active' => 1], true), ['key'=>'product_type_id', 'label'=>'product_type_name', 'parent_child'=>'sub_product']);
        $filter_items['purpose'] = $config_model->leaveSpecificFields($config_model->putOtherToEndOfTheList($config_model->getConfig('frm_purpose', ['status' => 1,'uses' => 'commercial']), 'purpose'), ['purpose_id', 'purpose']);
        $filter_items['state'] = ["ACT", "NSW", "VIC", "QLD", "SA", "WA", "TAS", "NT"];
        $filter_items['industries'] = $this->_generateTreeStructure($config_model->getConfig('config_industries'), ['key'=>'industry_id', 'label'=>'child', 'parent_child'=>'parent']);
        $filter_items['home_owner'] = ["yes", "no"];
        if($this->asset_filter_available){
          $filter_items['asset_contract_type'] = ["low doc", "full doc"];
          $filter_items['asset_equipments'] = $this->_generateTreeStructure($config_model->getAllEquipment(), ['key'=>'equipment_id', 'label'=>'equipment', 'parent_child'=>'parent_id']);;
        }

        // Convert object to be in array
        if(!empty($filter_items['industries'])) $filter_items['industries'] = array_values($filter_items['industries']);
        if(!empty($filter_items['product_types'])) $filter_items['product_types'] = array_values($filter_items['product_types']);
        if(!empty($filter_items['asset_equipments'])) $filter_items['asset_equipments'] = array_values($filter_items['asset_equipments']);

        return $filter_items;
      }catch(\Exception $e){
        Log::error($e->getMessage());
        Log::error($e->getTraceAsString());
        return $e->getMessage();
      }
    }

    private function _getFilters($filter_items){
      $partner_user = $this->Auth->identify();
      $filter_selected = Cache::read($this->filter_seleteced_key, $this->cache_config);
      if(empty($filter_selected)){
        $filter_selected = [
          'start_date' => date('Y-m-01', strtotime('-1 months')),
          'end_date' => date('Y-m-d'),
          'loan_amount' => [
              'min' => 0,
              'max' => 1000000
          ],
          'partner_user_id' => [],
          'source' => [],
          'campaign' => [],
          'lender_id' => [],
          'status' => [],
          'product_type_id' => [],
          'purpose_id' => [],
          'state' => [],
          'industry_id' => [],
          'home_owner' => [],
        ];

        if($this->asset_filter_available){
          $filter_selected['asset'] = [
            'contract_type' => [],
            'equipment_id' => []
          ];
          $filter_selected['asset']['age'] = new \StdClass();
        }
      }else{
        $filter_selected = json_decode($filter_selected, true);
        unset($filter_selected['partner_id'], $filter_selected['reports']);
      }
      return $filter_selected;
    }


    private function _generateTreeStructure($industries, $options){
      $grouped = [];
      foreach($industries as $i){
        // If it's a parent item,
        // else it's a child item,
        if(empty($i[$options['parent_child']]) OR $i[$options['parent_child']] == '0'){
          if(empty($grouped[$i[$options['key']]])){
            $grouped[$i[$options['key']]] = [
              'key' => $i[$options['key']],
              'label' => $i[$options['label']],
              'data' => $i[$options['label']],
              'children' => []
            ];
          }else{
            $grouped[$i[$options['key']]]['key'] = $i[$options['key']];
            $grouped[$i[$options['key']]]['label'] = $i[$options['label']];
            $grouped[$i[$options['key']]]['data'] = $i[$options['label']];
          }
        }else{
          if(empty($grouped[$i[$options['parent_child']]])){
            $grouped[$i[$options['parent_child']]] = [
              'key' => '',
              'label' => '',
              'data' => '',
              'children' => [],
            ];
          }
          $grouped[$i[$options['parent_child']]]['children'][] = [
            'key' => $i[$options['key']],
            'label' => $i[$options['label']],
            'data' => $i[$options['label']],
          ];
        }
      }
      return $grouped;
    }
    
    // get the filter data for dashboard analytics
    public function getDashboardAnalyticsOld(){
      $requested_data = $this->request->getData();
      Log::write('info', 'getDashboardData'.json_encode($requested_data));
      return $this->setJsonResponse(['success'=>true, 'message'=>'Data fetched successfully']);
      // get the filter data //
    }
}
