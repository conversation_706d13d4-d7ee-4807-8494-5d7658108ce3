<?php
namespace DashboardAnalytics\Controller;

use DashboardAnalytics\Controller\AppController;

use Cake\Log\Log;
use Cake\Cache\Cache;
use Cake\ORM\TableRegistry;
use Cake\Database\Expression\QueryExpression;
use Cake\Core\Configure;

use App\Lend\Config;
use App\Lend\LendInternalAuth;

use App\Lend\DashboardAnalyticsService;

class DashboardAnalyticsController extends AppController
{
    public function initialize(){
        parent::initialize();
        $partner_user = $this->Auth->identify();
        $this->filter_items_key = 'filter_items_'.$partner_user['partner_user_id'];
        $this->filter_seleteced_key = 'filter_selected_'.$partner_user['partner_user_id'];
        $this->cache_config = 'dashboard_analytics';
        $this->asset_filter_available = false;
        $this->query_cache = 'queryRedis';//cache to use for query result caching
        $this->query_where_hash = '';
        $this->query_force_refresh = false;
    }

    public function init(){
      try{
        $partner_user = $this->Auth->identify();
        $partner_feature_flags = $this->loadModel('PartnerFeatureFlags')->getFeatureFlag(['partner_id'=>$partner_user['partner_id']]);
        if(!empty($partner_feature_flags['access_to_asset_form']))
          $this->asset_filter_available = true;

        $filter_items = $this->_getFilterItems();
        $filter_selected = $this->_getFilters($filter_items);
        return $this->setJsonResponse(['success'=>true, 'filter_items'=>$filter_items, 'filter_selected'=>$filter_selected]);
      }catch(\Exception $e){
        Log::error($e->getMessage());
        Log::error($e->getTraceAsString());
        return $this->setJsonResponse(['success'=>false, 'message'=>$e->getMessage()]);
      }
    }

    public function getDashboardAnalytics($forceRefresh = false){
      try{
        if (!$this->request->is('post')) {
          throw new \Exception("Only POST request allowed.", 405);
        }
        $user = $this->Auth->user();
        if (empty($user)) {
          $user = $this->Auth->identify();
        }
        $schema = array_flip(['start_date', 'end_date', 'user_refs']);
        $data = array_intersect_key($this->request->getData(), $schema);
        if(!$user['account_admin']){
          $data['user_refs'][] = $user['partner_user_ref'];
        }

        $checkDates = TableRegistry::get('Leads')->isValidDatesRange($data);
        if ($checkDates!== true){
          $checkDates = $checkDates==""?"Invalid date range":$checkDates;
          throw new \Exception($checkDates);
        }

        if(isset($data['user_refs'])){
          if(is_array($data['user_refs'])){
            foreach($data['user_refs'] as $userRef){
              $data['partner_user_ids'][] = LendInternalAuth::unhashPartnerUserId($userRef);
            }
          }
          else{
            throw new \Exception("Invalid user_refs provided");
          }
        }

        $where = [
          'LeadEntity.created >=' => $data['start_date'] . ' 00:00:00',
          'LeadEntity.created <=' => $data['end_date'] . ' 23:59:59',
          'LeadEntity.partner_id' => $user['partner_id']
        ];
        if(isset($data['partner_user_ids'])){
          sort($data['partner_user_ids']);//so we use caching even if same users are sent in a different order
          $where['PartnerUserLeadsEntity.status'] = 'ACCESS';
          $where['PartnerUserLeadsEntity.partner_user_id IN'] = $data['partner_user_ids'];
        }
        $this->query_where_hash = md5(json_encode($where));
        $this->query_force_refresh = $forceRefresh;

        $leadsTable = TableRegistry::getTableLocator()->get('LeadEntity');
  
        //subquery for partner_commissions
        $partnerCommissionsTable = TableRegistry::getTableLocator()->get('PartnerCommissionEntity');
        $subQueryPC = $partnerCommissionsTable->find()
          ->select([
              'lead_id' => 'PartnerCommissionEntity.lead_id',
              'funded_amount_sum' => $partnerCommissionsTable->find()->func()->sum('PartnerCommissionEntity.funded_amount'),
              'funded_amount_avg' => $partnerCommissionsTable->find()->func()->avg('PartnerCommissionEntity.funded_amount'),
              'commission_sum' => $partnerCommissionsTable->find()->func()->sum('PartnerCommissionEntity.commission'),
              'commission_exists' => new QueryExpression('CASE WHEN COUNT(PartnerCommissionEntity.commission_id) > 0 THEN 1 ELSE 0 END'),
              'days_to_settle' => $leadsTable->query()->newExpr(
                'CASE 
                  WHEN PartnerCommissionEntity.commission_id = LeadAssociatedDataEntity.min_partner_commission_id
                  AND ManStatusEntity.is_settled = 1
                  THEN DATEDIFF(PartnerCommissionEntity.funded_date, LeadEntity.created) 
                  ELSE NULL 
                END'
              ),
  
          ])
          // ->from(['PartnerCommissionEntity' => 'partner_commissions'])
          ->join([
            'table' => 'leads',
            'alias' => 'LeadEntity',
            'conditions' => [
                'LeadEntity.lead_id = PartnerCommissionEntity.lead_id'
            ]
          ])
          ->join([
            'table' => 'lead_associated_data',
            'alias' => 'LeadAssociatedDataEntity',
            'conditions' => [
                'LeadAssociatedDataEntity.lead_id = LeadEntity.lead_id'
            ]
          ])
          ->join([
            'table' => 'man_statuses',
            'alias' => 'ManStatusEntity',
            'conditions' => [
                'ManStatusEntity.id = LeadEntity.man_status_id'
            ]
          ])
          ->join([
            'table' => 'partner_user_leads',
            'alias' => 'PartnerUserLeadsEntity',
            'type' => 'LEFT',
            'conditions' => [
                'PartnerUserLeadsEntity.lead_id = LeadEntity.lead_id',
                'PartnerUserLeadsEntity.status = "ACCESS"'
            ]
          ])
          ->where($where)
          ->group('PartnerCommissionEntity.lead_id');
  
        //subquery for lead_call_attempts
        $leadCallAttemptsTable = TableRegistry::getTableLocator()->get('LeadCallAttemptEntity');
        $subQueryLCA = $leadCallAttemptsTable->find()
          ->select([
            'lead_id' => 'LeadCallAttemptEntity.lead_id',
            'call_queue_leads_called' => $leadCallAttemptsTable->find()->func()->count('LeadCallAttemptEntity.call_attempts_id'),
            'call_queue_answered' => $leadCallAttemptsTable->find()->func()->count(
              new QueryExpression('CASE WHEN LeadCallAttemptEntity.outcome_type = "answered" THEN 1 ELSE NULL END')
            ),
          ])
          ->join([
            'table' => 'leads',
            'alias' => 'LeadEntity',
            'conditions' => [
                'LeadEntity.lead_id = LeadCallAttemptEntity.lead_id'
            ]
          ])
          ->join([
            'table' => 'partner_user_leads',
            'alias' => 'PartnerUserLeadsEntity',
            'type' => 'LEFT',
            'conditions' => [
                'PartnerUserLeadsEntity.lead_id = LeadEntity.lead_id',
                'PartnerUserLeadsEntity.status = "ACCESS"'
            ]
          ])
          ->where($where) 
          ->group('LeadCallAttemptEntity.lead_id');
  
          //subquery for sale_details
          $saleDetailsTable = TableRegistry::getTableLocator()->get('SaleDetailEntity');
  
        $subQuerySD = $saleDetailsTable->find()
          ->select([
              'lead_id' => 'SaleEntity.lead_id',
              'submitted_sum' => $saleDetailsTable->find()->func()->sum('SaleDetailEntity.financed_amount'),
          ])
          ->join([
            'table' => 'sales',
            'alias' => 'SaleEntity',
            'conditions' => 'SaleDetailEntity.sale_id = SaleEntity.sale_id',
          ])
          ->join([
            'table' => 'leads',
            'alias' => 'LeadEntity',
            'conditions' => [
                'LeadEntity.lead_id = SaleEntity.lead_id'
            ]
          ])
          ->join([
            'table' => 'partner_user_leads',
            'alias' => 'PartnerUserLeadsEntity',
            'type' => 'LEFT',
            'conditions' => [
                'PartnerUserLeadsEntity.lead_id = LeadEntity.lead_id',
                'PartnerUserLeadsEntity.status = "ACCESS"'
            ]
          ])
          ->where($where)
          ->group('SaleEntity.lead_id');
          
        //subquery for lend_signature_requests
        $lendSignatureRequestsTable = TableRegistry::getTableLocator()->get('LendSignatureRequestEntity');
        $subQueryLSR = $lendSignatureRequestsTable->find()
          ->from(['LendSignatureRequestEntity' => 'lend_signature_requests'])
          ->select([
              'lead_id' => 'LendSignatureRequestEntity.lead_id',
              'request_count' => $lendSignatureRequestsTable->find()->func()->count('LendSignatureRequestEntity.id'),
              'completion_count' => $lendSignatureRequestsTable->find()->func()->sum(
                new QueryExpression("CASE WHEN LendSignatureRequestEntity.status = 'signed' THEN 1 ELSE 0 END")
              ),
              'conversion_rate' => $leadsTable->query()->newExpr(
                "CASE WHEN COUNT(LendSignatureRequestEntity.id) = 0 THEN 0 
                ELSE (SUM(CASE WHEN LendSignatureRequestEntity.status = 'signed' THEN 1 ELSE 0 END) / 
                COUNT(LendSignatureRequestEntity.id)) END"
              )
          ])
          ->join([
            'table' => 'leads',
            'alias' => 'LeadEntity',
            'conditions' => [
                'LeadEntity.lead_id = LendSignatureRequestEntity.lead_id'
            ]
          ])
          ->join([
            'table' => 'partner_user_leads',
            'alias' => 'PartnerUserLeadsEntity',
            'type' => 'LEFT',
            'conditions' => [
                'PartnerUserLeadsEntity.lead_id = LeadEntity.lead_id',
                'PartnerUserLeadsEntity.status = "ACCESS"'
            ]
          ])
          ->where($where)
          ->group('LendSignatureRequestEntity.lead_id');
          
        //subquery for settlement_reviews
        $settlementReviewsTable = TableRegistry::getTableLocator()->get('SettlementReviewsEntity');
        $subQuerySR = $settlementReviewsTable->find()
          ->from(['SettlementReviewsEntity' => 'settlement_reviews'])
          ->select([
              'lead_id' => 'SettlementReviewsEntity.lead_id',
              'origination_fee_sum' => $settlementReviewsTable->find()->func()->sum('SettlementReviewsEntity.origination_fee'),
              'vbi_income_sum' => $settlementReviewsTable->find()->func()->sum('SettlementReviewsEntity.vbi_income'),
              'warranty_comms_sum' => $settlementReviewsTable->find()->func()->sum('SettlementReviewsEntity.warranty_comms'),
              'comp_ins_comms_sum' => $settlementReviewsTable->find()->func()->sum('SettlementReviewsEntity.comp_ins_comms'),
              'gap_ins_comms_sum' => $settlementReviewsTable->find()->func()->sum('SettlementReviewsEntity.gap_ins_comms'),
              'cci_ins_comms_sum' => $settlementReviewsTable->find()->func()->sum('SettlementReviewsEntity.cci_ins_comms'),
              'referrer_commission_sum' => $settlementReviewsTable->find()->func()->sum('SettlementReviewsEntity.referrer_commission'),
              'other_income_sum' => $settlementReviewsTable->find()->func()->sum('SettlementReviewsEntity.other_income'),
              'warranty_income_sum' => $settlementReviewsTable->find()->func()->sum('SettlementReviewsEntity.warranty_income'),
              'exists' => new QueryExpression('CASE WHEN COUNT(SettlementReviewsEntity.lead_id) > 0 THEN 1 ELSE 0 END')
          ])
          ->join([
            'table' => 'leads',
            'alias' => 'LeadEntity',
            'conditions' => [
                'LeadEntity.lead_id = SettlementReviewsEntity.lead_id'
            ]
          ])
          ->join([
            'table' => 'partner_user_leads',
            'alias' => 'PartnerUserLeadsEntity',
            'type' => 'LEFT',
            'conditions' => [
                'PartnerUserLeadsEntity.lead_id = LeadEntity.lead_id',
                'PartnerUserLeadsEntity.status = "ACCESS"'
            ]
          ])
          ->where($where)
          ->group('SettlementReviewsEntity.lead_id');

        $resultData = $leadsTable->find()
          ->join([
            'table' => 'lead_associated_data',
            'alias' => 'LeadAssociatedDataEntity',
            'conditions' => [
                'LeadAssociatedDataEntity.lead_id = LeadEntity.lead_id'
            ]
          ])
          ->join([
            'table' => 'man_statuses',
            'alias' => 'ManStatusEntity',
            'conditions' => [
                'ManStatusEntity.id = LeadEntity.man_status_id'
            ]
          ])
          ->join([
            'table' => $subQueryPC,
            'alias' => 'PartnerCommissionAggregate',
            'type' => 'LEFT',
            'conditions' => [
                'PartnerCommissionAggregate.lead_id = LeadEntity.lead_id'
            ]
          ])
          ->join([
            'table' => $subQueryLCA,
            'alias' => 'LeadCallAttemptAggregate',
            'type' => 'LEFT',
            'conditions' => [
                'LeadCallAttemptAggregate.lead_id = LeadEntity.lead_id'
            ]
          ])
          ->join([
            'table' => $subQuerySD,
            'alias' => 'SaleDetailsAggregate',
            'type' => 'LEFT',
            'conditions' => [
                'SaleDetailsAggregate.lead_id = LeadEntity.lead_id'
            ]
          ])
          ->join([
            'table' => $subQueryLSR,
            'alias' => 'LendSignatureRequestAggregate',
            'type' => 'LEFT',
            'conditions' => [
                'LendSignatureRequestAggregate.lead_id = LeadEntity.lead_id'
            ]
          ])
          ->join([
            'table' => $subQuerySR,
            'alias' => 'SettlementReviewAggregate',
            'type' => 'LEFT',
            'conditions' => [
                'SettlementReviewAggregate.lead_id = LeadEntity.lead_id'
            ]
          ])
          ->join([
            'table' => 'partner_user_leads',
            'alias' => 'PartnerUserLeadsEntity',
            'type' => 'LEFT',
            'conditions' => [
                'PartnerUserLeadsEntity.lead_id = LeadEntity.lead_id',
                'PartnerUserLeadsEntity.status = "ACCESS"'
            ]
          ])
          ->select([
            'leads_in' => $leadsTable->find()->func()->count('LeadEntity.lead_id'),
            'submitted_volume' => new QueryExpression('SUM(COALESCE(SaleDetailsAggregate.submitted_sum, 0))'),
            'settled_volume' => new QueryExpression('SUM(COALESCE(PartnerCommissionAggregate.funded_amount_sum, 0))'),
            'brokerage_origination' => new QueryExpression('SUM(COALESCE(PartnerCommissionAggregate.commission_sum, 0))'),
            'average_settled_volume' => new QueryExpression('AVG(PartnerCommissionAggregate.funded_amount_avg)'), // Ankur solution
            'average_remuneration' => new QueryExpression('SUM(COALESCE(PartnerCommissionAggregate.commission_sum, 0)) / NULLIF(SUM(CASE WHEN PartnerCommissionAggregate.commission_exists > 0 THEN 1 ELSE 0 END), 0)'),
            'average_days_to_settle' => new QueryExpression('AVG(COALESCE(PartnerCommissionAggregate.days_to_settle, NULL))'),
            
            'referrals_in' => $leadsTable->find()->func()->count('LeadEntity.referrer_person_id'),

            'leads_submitted' => $leadsTable->find()->func()->count(
              new QueryExpression('CASE WHEN LeadAssociatedDataEntity.max_sale_id IS NOT NULL THEN 1 ELSE NULL END')
            ),
            'overall_settlements' => $leadsTable->find()->func()->sum(
              new QueryExpression('CASE WHEN PartnerCommissionAggregate.commission_exists > 0 THEN 1 ELSE 0 END')
            ),
            
            'origination_fees' => new QueryExpression('SUM(COALESCE(SettlementReviewAggregate.origination_fee_sum, 0))'),
            'vbi' => new QueryExpression('SUM(COALESCE(SettlementReviewAggregate.vbi_income_sum, 0))'),
            'warranty_comms' => new QueryExpression('SUM(COALESCE(SettlementReviewAggregate.warranty_comms_sum, 0))'),
            'insurance_commission' => new QueryExpression('SUM(COALESCE(SettlementReviewAggregate.comp_ins_comms_sum, 0) + COALESCE(SettlementReviewAggregate.gap_ins_comms_sum, 0) + COALESCE(SettlementReviewAggregate.cci_ins_comms_sum, 0))'),
            'referrer_fees' => new QueryExpression('SUM(COALESCE(SettlementReviewAggregate.referrer_commission_sum, 0))'),
            'other_commissions' => new QueryExpression('SUM(COALESCE(SettlementReviewAggregate.other_income_sum, 0))'),
            
            'total_remuneration' => new QueryExpression(
                'SUM(COALESCE(PartnerCommissionAggregate.commission_sum, 0) + 
                 COALESCE(SettlementReviewAggregate.origination_fee_sum, 0) + 
                 COALESCE(SettlementReviewAggregate.vbi_income_sum, 0) + 
                 COALESCE(SettlementReviewAggregate.warranty_income_sum, 0) + 
                 COALESCE(SettlementReviewAggregate.comp_ins_comms_sum, 0) + 
                 COALESCE(SettlementReviewAggregate.gap_ins_comms_sum, 0) + 
                 COALESCE(SettlementReviewAggregate.cci_ins_comms_sum, 0) + 
                 COALESCE(SettlementReviewAggregate.other_income_sum, 0))'
            ),
            
            'total_excl_referrer_fees' => new QueryExpression(
                'SUM(COALESCE(PartnerCommissionAggregate.commission_sum, 0) + 
                 COALESCE(SettlementReviewAggregate.origination_fee_sum, 0) + 
                 COALESCE(SettlementReviewAggregate.vbi_income_sum, 0) + 
                 COALESCE(SettlementReviewAggregate.warranty_income_sum, 0) + 
                 COALESCE(SettlementReviewAggregate.comp_ins_comms_sum, 0) + 
                 COALESCE(SettlementReviewAggregate.gap_ins_comms_sum, 0) + 
                 COALESCE(SettlementReviewAggregate.cci_ins_comms_sum, 0) + 
                 COALESCE(SettlementReviewAggregate.other_income_sum, 0)) - 
                 SUM(COALESCE(SettlementReviewAggregate.referrer_commission_sum, 0))'
            ),
            
            'average_lead_remuneration' => new QueryExpression(
                'SUM(COALESCE(PartnerCommissionAggregate.commission_sum, 0) + 
                 COALESCE(SettlementReviewAggregate.origination_fee_sum, 0) + 
                 COALESCE(SettlementReviewAggregate.vbi_income_sum, 0) + 
                 COALESCE(SettlementReviewAggregate.warranty_income_sum, 0) + 
                 COALESCE(SettlementReviewAggregate.comp_ins_comms_sum, 0) + 
                 COALESCE(SettlementReviewAggregate.gap_ins_comms_sum, 0) + 
                 COALESCE(SettlementReviewAggregate.cci_ins_comms_sum, 0) + 
                 COALESCE(SettlementReviewAggregate.other_income_sum, 0)) / 
                 NULLIF(COUNT(DISTINCT CASE WHEN PartnerCommissionAggregate.commission_exists > 0 OR 
                 (SettlementReviewAggregate.exists > 0 AND 
                  (COALESCE(SettlementReviewAggregate.origination_fee_sum, 0) > 0 OR 
                   COALESCE(SettlementReviewAggregate.vbi_income_sum, 0) > 0 OR 
                   COALESCE(SettlementReviewAggregate.warranty_income_sum, 0) > 0 OR 
                   COALESCE(SettlementReviewAggregate.comp_ins_comms_sum, 0) > 0 OR 
                   COALESCE(SettlementReviewAggregate.gap_ins_comms_sum, 0) > 0 OR 
                   COALESCE(SettlementReviewAggregate.other_income_sum, 0) > 0))
                 THEN LeadEntity.lead_id ELSE NULL END), 0)'
            ),
            
            'average_excl_referrer_fees' => new QueryExpression(
                '(SUM(COALESCE(PartnerCommissionAggregate.commission_sum, 0) + 
                 COALESCE(SettlementReviewAggregate.origination_fee_sum, 0) + 
                 COALESCE(SettlementReviewAggregate.vbi_income_sum, 0) + 
                 COALESCE(SettlementReviewAggregate.warranty_income_sum, 0) + 
                 COALESCE(SettlementReviewAggregate.comp_ins_comms_sum, 0) + 
                 COALESCE(SettlementReviewAggregate.gap_ins_comms_sum, 0) + 
                 COALESCE(SettlementReviewAggregate.cci_ins_comms_sum, 0) + 
                 COALESCE(SettlementReviewAggregate.other_income_sum, 0)) - 
                 SUM(COALESCE(SettlementReviewAggregate.referrer_commission_sum, 0))) / 
                 NULLIF(COUNT(DISTINCT CASE WHEN PartnerCommissionAggregate.commission_exists > 0 OR 
                 (SettlementReviewAggregate.exists > 0 AND 
                  (COALESCE(SettlementReviewAggregate.origination_fee_sum, 0) > 0 OR 
                   COALESCE(SettlementReviewAggregate.vbi_income_sum, 0) > 0 OR 
                   COALESCE(SettlementReviewAggregate.warranty_income_sum, 0) > 0 OR 
                   COALESCE(SettlementReviewAggregate.comp_ins_comms_sum, 0) > 0 OR 
                   COALESCE(SettlementReviewAggregate.gap_ins_comms_sum, 0) > 0 OR 
                   COALESCE(SettlementReviewAggregate.other_income_sum, 0) > 0))
                 THEN LeadEntity.lead_id ELSE NULL END), 0)'
            ),
            
            'leads_lost' => $leadsTable->find()->func()->count(
              new QueryExpression('
                CASE 
                  WHEN (LeadEntity.is_closed = 1
                  OR LeadEntity.is_archived = 1) 
                  AND ManStatusEntity.is_settled = 0 
                  THEN 1 
                  ELSE NULL 
                END')
            ),
            
            'lendsign_requests' => new QueryExpression('SUM(COALESCE(LendSignatureRequestAggregate.request_count, 0))'),
            'lendsign_completions' => new QueryExpression('SUM(COALESCE(LendSignatureRequestAggregate.completion_count, 0))'),
            'lendsign_conversion' => new QueryExpression('CASE WHEN SUM(COALESCE(LendSignatureRequestAggregate.request_count, 0)) = 0 THEN 0 
                ELSE SUM(COALESCE(LendSignatureRequestAggregate.completion_count, 0)) / SUM(COALESCE(LendSignatureRequestAggregate.request_count, 0)) END'),
            
            'submission_rate' => $leadsTable->query()->newExpr(
              'IF(COUNT(LeadEntity.lead_id) = 0 , 0, (COUNT(CASE WHEN LeadAssociatedDataEntity.max_sale_id IS NOT NULL THEN 1 ELSE NULL END)/COUNT(LeadEntity.lead_id)))'
            ),
  
            'settlement_rate' => $leadsTable->query()->newExpr(
              'IF(COUNT(LeadEntity.lead_id) = 0 , 0, (COUNT(CASE WHEN PartnerCommissionAggregate.commission_exists > 0 THEN 1 ELSE NULL END)/COUNT(LeadEntity.lead_id)))'
            ),
  
            'call_queue_leads_called' => $leadsTable->find()->func()->sum(
              new QueryExpression('CASE WHEN LeadCallAttemptAggregate.call_queue_leads_called > 0 THEN 1 ELSE 0 END')
            ),
  
            'call_queue_submissions' => $leadsTable->find()->func()->sum(
              new QueryExpression(
                'CASE 
                  WHEN LeadCallAttemptAggregate.call_queue_answered > 0 
                  AND LeadAssociatedDataEntity.max_sale_id IS NOT NULL THEN 1 
                  ELSE 0 
                END')
            ),
  
            'call_queue_conversion' => $leadsTable->query()->newExpr(
              'IF(SUM(CASE WHEN LeadCallAttemptAggregate.call_queue_leads_called > 0 THEN 1 ELSE 0 END) = 0, 0, 
                (SUM(CASE 
                  WHEN LeadCallAttemptAggregate.call_queue_answered > 0 
                  AND LeadAssociatedDataEntity.max_sale_id IS NOT NULL THEN 1 
                  ELSE 0 
                END) / 
                SUM(CASE WHEN LeadCallAttemptAggregate.call_queue_leads_called > 0 THEN 1 ELSE 0 END)))'
            )

          ])
          ->where($where)
          ->enableHydration(false);

          $cacheKey = 'dashboard_main_'.$this->query_where_hash;
          if($forceRefresh){
            $resultData = $resultData->first();
            Cache::write($cacheKey, $resultData, $this->query_cache);
          }
          else{
            $resultData = Cache::remember($cacheKey, function () use ($resultData) {
              return $resultData->first();
            }, $this->query_cache);
          }

          $resultData['pie_lenders'] = $this->getPieChartDataLenders($where);
          $resultData['pie_product_types'] = $this->getPieChartDataProductTypes($where);
          $resultData['pie_sources'] = $this->getPieChartDataLeadSources($where);
          $resultData['pie_referrers'] = $this->getPieChartDataLeadReferrers($where);//inefficient
          $resultData['pie_call_queue_pipeline'] = $this->getPieChartDataCallQueuePipeline($where);
          $resultData['pie_lendsign_statuses'] = [
              [
                  'lendsign_status' => 'lendsign_requests',
                  'count' => (int)$resultData['lendsign_requests']
              ],
              [
                  'lendsign_status' => 'lendsign_completions',
                  'count' => (int)$resultData['lendsign_completions']
              ]
          ];
          
          $resultData['lead_pipeline'] = $this->getLeadPipeline($where);

          return $this->setJsonResponse($resultData);
      } catch (\Exception $e) {
        Log::error($e->getMessage());
        Log::error($e->getTraceAsString());
        return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
      }
    }

  /**
   * Get query results from cache if present otherwise get results, store in cache and return
   * If force_refresh is true - get results, store in cache and return 
   */
  private function getQueryResults($cachePrefix, $query, $postProcessFunctionName = null){
    $cacheKey = $cachePrefix.'_'.$this->query_where_hash;

    $fetchAndProcess = function () use ($query, $postProcessFunctionName) {
      $results = $query->hydrate(false)->all()->toArray();
      if ($postProcessFunctionName && method_exists($this, $postProcessFunctionName)) {
        return $this->$postProcessFunctionName($results);
      }
      return $results;
    };

    $results = [];
    if($this->query_force_refresh){
      $results = $fetchAndProcess();
      Cache::write($cacheKey, $results, $this->query_cache);
      return $results;
    }
    return Cache::remember($cacheKey, function () use ($fetchAndProcess) {
      return $fetchAndProcess();
    }, $this->query_cache);

  }

  private function getPieChartDataLenders($where)
  {
    $results = [];
    $salesTable = TableRegistry::getTableLocator()->get('SaleEntity');
    $offPanelLenderId = Configure::read('Lend.OFF_PANEL_LENDER_ID');
    
    // Using SQL CASE for submitted lenders to handle both regular and off-panel lenders
    $submittedQuery = $salesTable->find();
    $caseExpr = $submittedQuery->newExpr()->add(
        "CASE WHEN LenderEntity.lender_id = $offPanelLenderId AND SaleEntity.off_panel_lender IS NOT NULL " .
        "THEN SaleEntity.off_panel_lender ELSE LenderEntity.lender_name END"
    );
    
    $submittedQuery
      ->select([
        'lender' => $caseExpr,
        'count' => $submittedQuery->func()->count('SaleEntity.sale_id')
      ])
      ->join([
        'table' => 'lender_product',
        'alias' => 'LenderProductEntity',
        'conditions' => 'LenderProductEntity.lender_product_id = SaleEntity.product_id'
      ])
      ->join([
        'table' => 'lenders',
        'alias' => 'LenderEntity',
        'conditions' => 'LenderEntity.lender_id = LenderProductEntity.lender_id'
      ])
      ->join([
        'table' => 'leads',
        'alias' => 'LeadEntity',
        'conditions' => 'LeadEntity.lead_id = SaleEntity.lead_id'
      ])
      ->join([
        'table' => 'partner_user_leads',
        'alias' => 'PartnerUserLeadsEntity',
        'type' => 'LEFT',
        'conditions' => 'PartnerUserLeadsEntity.lead_id = LeadEntity.lead_id AND PartnerUserLeadsEntity.status = "ACCESS"'
      ])
      ->where($where)
      ->group(['lender'])
      ->orderDesc('count')
      ->enableHydration(false);
    
    $results['submitted'] = $this->getQueryResults('dashboard_pie_lenders_submitted', $submittedQuery, 'getPieChartDataLendersFormatData');
    
    //settled
    $partnerCommissionsTable = TableRegistry::getTableLocator()->get('PartnerCommissionEntity');
    
    // Using SQL CASE for settled lenders
    $settledQuery = $partnerCommissionsTable->find();
    $caseExpr = $settledQuery->newExpr()->add(
        "CASE WHEN LenderEntity.lender_id = $offPanelLenderId AND SaleEntity.off_panel_lender IS NOT NULL " .
        "THEN SaleEntity.off_panel_lender ELSE LenderEntity.lender_name END"
    );
    
    $settledQuery
      ->select([
        'lender' => $caseExpr,
        'count' => $settledQuery->func()->count('PartnerCommissionEntity.commission_id')
      ])
      ->join([
        'table' => 'sale_outcomes',
        'alias' => 'SaleOutcomesEntity',
        'conditions' => 'SaleOutcomesEntity.sale_outcome_id = PartnerCommissionEntity.sale_outcome_id'
      ])
      ->join([
        'table' => 'sales',
        'alias' => 'SaleEntity',
        'conditions' => 'SaleEntity.sale_id = SaleOutcomesEntity.sale_id'
      ])
      ->join([
        'table' => 'lender_product',
        'alias' => 'LenderProductEntity',
        'conditions' => 'LenderProductEntity.lender_product_id = SaleEntity.product_id'
      ])
      ->join([
        'table' => 'lenders',
        'alias' => 'LenderEntity',
        'conditions' => 'LenderEntity.lender_id = LenderProductEntity.lender_id'
      ])
      ->join([
        'table' => 'leads',
        'alias' => 'LeadEntity',
        'conditions' => 'LeadEntity.lead_id = PartnerCommissionEntity.lead_id'
      ])
      ->join([
        'table' => 'partner_user_leads',
        'alias' => 'PartnerUserLeadsEntity',
        'type' => 'LEFT',
        'conditions' => 'PartnerUserLeadsEntity.lead_id = LeadEntity.lead_id AND PartnerUserLeadsEntity.status = "ACCESS"'
      ])
      ->where($where)
      ->group(['lender'])
      ->orderDesc('count')
      ->enableHydration(false);
    
    $results['settled'] = $this->getQueryResults('dashboard_pie_lenders_settled', $settledQuery, 'getPieChartDataLendersFormatData');

    return $results;
  }

  /**
   * Reformat lender data to extract and add off-panel flag and lender name for off panel lenders
   */
  private function getPieChartDataLendersFormatData($results){
    $lenderData = [];
    foreach ($results as $row) {
      $lenderName = '';
      $isOffPanel = false;
      
      $jsonLenderName = '';
      if(is_string($row['lender'])){
        $data = json_decode($row['lender'], true);
        $jsonLenderName = (json_last_error() == JSON_ERROR_NONE && isset($data['lender_name'])) ? $data['lender_name'] : '';
      }
  
      if (!empty($jsonLenderName)) {
          // This is an off-panel lender with JSON data
          $lenderName = $jsonLenderName;
          $isOffPanel = true;
      } else {
          // Regular lender
          $lenderName = $row['lender'];
      }
      
      // Skip if we couldn't get a lender name
      if (empty($lenderName)) {
          continue;
      }
      
      // Add to results
      $lenderData[] = [
        'lender_name' => $lenderName,
        'leads' => $row['count'],
        'offpanel' => $isOffPanel
      ];
    }
    return $lenderData;
  }

    private function getPieChartDataProductTypes($where){
      $results = [];
      $leadsTable = TableRegistry::getTableLocator()->get('LeadEntity');
      //all leads
      $query = $leadsTable->find()
        ->select([
          'product_type_id' => 'PartnerProductTypeEntity.product_type_id',
          'product_type_name' => 'PartnerProductTypeEntity.product_type_name',
          'leads' => $leadsTable->find()->func()->count('LeadEntity.lead_id')
        ])
        ->join([
          'table' => 'partner_product_types',
          'alias' => 'PartnerProductTypeEntity',
          'conditions' => 'PartnerProductTypeEntity.product_type_id = LeadEntity.product_type_id'
        ])
        ->join([
          'table' => 'partner_user_leads',
          'alias' => 'PartnerUserLeadsEntity',
          'type' => 'LEFT',
          'conditions' => 'PartnerUserLeadsEntity.lead_id = LeadEntity.lead_id AND PartnerUserLeadsEntity.status = "ACCESS"'
        ])
        ->group('PartnerProductTypeEntity.product_type_id')
        ->where($where)
        ->enableHydration(false);
      $results['all_leads'] = $this->getQueryResults('dashboard_pie_product_types_all', $query);
      //submitted
      $query = $leadsTable->find()
        ->select([
          'product_type_id' => 'PartnerProductTypeEntity.product_type_id',
          'product_type_name' => 'PartnerProductTypeEntity.product_type_name',
          'leads' => $leadsTable->find()->func()->count('LeadEntity.lead_id')
        ])
        ->join([
          'table' => 'sales',
          'alias' => 'SaleEntity',
          'conditions' => 'SaleEntity.lead_id = LeadEntity.lead_id'
        ])
        ->join([
          'table' => 'partner_product_types',
          'alias' => 'PartnerProductTypeEntity',
          'conditions' => 'PartnerProductTypeEntity.product_type_id = SaleEntity.product_type_id'
        ])
        ->join([
          'table' => 'partner_user_leads',
          'alias' => 'PartnerUserLeadsEntity',
          'type' => 'LEFT',
          'conditions' => 'PartnerUserLeadsEntity.lead_id = LeadEntity.lead_id AND PartnerUserLeadsEntity.status = "ACCESS"'
        ])
        ->group('PartnerProductTypeEntity.product_type_id')
        ->where($where)
        ->enableHydration(false);
        $results['submitted'] = $this->getQueryResults('dashboard_pie_product_types_submitted', $query);
      //settled
      $query = $leadsTable->find()
        ->select([
          'product_type_id' => 'PartnerProductTypeEntity.product_type_id',
          'product_type_name' => 'PartnerProductTypeEntity.product_type_name',
          'leads' => $leadsTable->find()->func()->count('LeadEntity.lead_id')
        ])
        ->join([
          'table' => 'partner_commissions',
          'alias' => 'PartnerCommissionEntity',
          'conditions' => 'PartnerCommissionEntity.lead_id = LeadEntity.lead_id'
        ])
        ->join([
          'table' => 'sale_outcomes',
          'alias' => 'SaleOutcomesEntity',
          'conditions' => 'SaleOutcomesEntity.sale_outcome_id = PartnerCommissionEntity.sale_outcome_id'
        ])
        ->join([
          'table' => 'sales',
          'alias' => 'SaleEntity',
          'conditions' => 'SaleEntity.sale_id = SaleOutcomesEntity.sale_id'
        ])
        ->join([
          'table' => 'partner_product_types',
          'alias' => 'PartnerProductTypeEntity',
          'conditions' => 'PartnerProductTypeEntity.product_type_id = SaleEntity.product_type_id'
        ])
        ->join([
          'table' => 'partner_user_leads',
          'alias' => 'PartnerUserLeadsEntity',
          'type' => 'LEFT',
          'conditions' => 'PartnerUserLeadsEntity.lead_id = LeadEntity.lead_id AND PartnerUserLeadsEntity.status = "ACCESS"'
        ])
        ->group('PartnerProductTypeEntity.product_type_id')
        ->where($where)
        ->enableHydration(false);
        $results['settled'] = $this->getQueryResults('dashboard_pie_product_types_settled', $query);
      return $results;
    }

    private function getPieChartDataLeadSources($where){
      $results = [];
      $leadsTable = TableRegistry::getTableLocator()->get('LeadEntity');
      //all leads
      $query = $leadsTable->find()
        ->select([
          'source' => 'LeadEntity.source',
          'leads' => $leadsTable->find()->func()->count('LeadEntity.lead_id')
        ])
        ->join([
          'table' => 'partner_user_leads',
          'alias' => 'PartnerUserLeadsEntity',
          'type' => 'LEFT',
          'conditions' => 'PartnerUserLeadsEntity.lead_id = LeadEntity.lead_id AND PartnerUserLeadsEntity.status = "ACCESS"'
        ])
        ->group('LeadEntity.source')
        ->where($where)
        ->where('LeadEntity.source IS NOT NULL')
        ->enableHydration(false);
        $results['all_leads'] = $this->getQueryResults('dashboard_pie_sources_all', $query);
      //submitted
      $query = $leadsTable->find()
        ->select([
          'source' => 'LeadEntity.source',
          'leads' => $leadsTable->find()->func()->count('DISTINCT LeadEntity.lead_id')
        ])
        ->join([
          'table' => 'sales',
          'alias' => 'SaleEntity',
          'conditions' => 'SaleEntity.lead_id = LeadEntity.lead_id'
        ])
        ->join([
          'table' => 'partner_user_leads',
          'alias' => 'PartnerUserLeadsEntity',
          'type' => 'LEFT',
          'conditions' => 'PartnerUserLeadsEntity.lead_id = LeadEntity.lead_id AND PartnerUserLeadsEntity.status = "ACCESS"'
        ])
        ->group('LeadEntity.source')
        ->where($where)
        ->where('LeadEntity.source IS NOT NULL')
        ->enableHydration(false);
        // echo $query->sql();
        $results['submitted'] = $this->getQueryResults('dashboard_pie_sources_submitted', $query);
      //settled
      $query = $leadsTable->find()
        ->select([
          'source' => 'LeadEntity.source',
          'leads' => $leadsTable->find()->func()->count('DISTINCT LeadEntity.lead_id')
        ])
        ->join([
          'table' => 'partner_commissions',
          'alias' => 'PartnerCommissionEntity',
          'conditions' => 'PartnerCommissionEntity.lead_id = LeadEntity.lead_id'
        ])
        ->join([
          'table' => 'partner_user_leads',
          'alias' => 'PartnerUserLeadsEntity',
          'type' => 'LEFT',
          'conditions' => 'PartnerUserLeadsEntity.lead_id = LeadEntity.lead_id AND PartnerUserLeadsEntity.status = "ACCESS"'
        ])
        ->group('LeadEntity.source')
        ->where($where)
        ->where('LeadEntity.source IS NOT NULL')
        ->enableHydration(false);
        $results['settled'] = $this->getQueryResults('dashboard_pie_sources_settled', $query);
      return $results;
    }

    private function getPieChartDataLeadReferrers($where){
      $results = [];
      $leadsTable = TableRegistry::getTableLocator()->get('LeadEntity');
      //all leads
      $query = $leadsTable->find()
        ->select([
          'referrer' => 'ReferrerEntity.nickname',
          'leads' => $leadsTable->find()->func()->count('LeadEntity.lead_id')
        ])
        ->join([
          'table' => 'referrer_people',
          'alias' => 'ReferrerPeopleEntity',
          'conditions' => 'ReferrerPeopleEntity.id = LeadEntity.referrer_person_id'
        ])
        ->join([
          'table' => 'referrers',
          'alias' => 'ReferrerEntity',
          'conditions' => 'ReferrerEntity.id = ReferrerPeopleEntity.referrer_id'
        ])
        ->join([
          'table' => 'partner_user_leads',
          'alias' => 'PartnerUserLeadsEntity',
          'type' => 'LEFT',
          'conditions' => 'PartnerUserLeadsEntity.lead_id = LeadEntity.lead_id AND PartnerUserLeadsEntity.status = "ACCESS"'
        ])
        ->group('ReferrerEntity.nickname')
        ->where($where)
        ->enableHydration(false);
      $results['all_leads'] = $this->getQueryResults('dashboard_pie_referrers_all', $query);
      //submitted
      $query = $leadsTable->find()
        ->select([
          'referrer' => 'ReferrerEntity.nickname',
          'leads' => $leadsTable->find()->func()->count('DISTINCT LeadEntity.lead_id')
        ])
        ->join([
          'table' => 'sales',
          'alias' => 'SaleEntity',
          'conditions' => 'SaleEntity.lead_id = LeadEntity.lead_id'
        ])
        ->join([
          'table' => 'referrer_people',
          'alias' => 'ReferrerPeopleEntity',
          'conditions' => 'ReferrerPeopleEntity.id = LeadEntity.referrer_person_id'
        ])
        ->join([
          'table' => 'referrers',
          'alias' => 'ReferrerEntity',
          'conditions' => 'ReferrerEntity.id = ReferrerPeopleEntity.referrer_id'
        ])
        ->join([
          'table' => 'partner_user_leads',
          'alias' => 'PartnerUserLeadsEntity',
          'type' => 'LEFT',
          'conditions' => 'PartnerUserLeadsEntity.lead_id = LeadEntity.lead_id AND PartnerUserLeadsEntity.status = "ACCESS"'
        ])
        ->group('ReferrerEntity.nickname')
        ->where($where)
        ->enableHydration(false);
        $results['submitted'] = $this->getQueryResults('dashboard_pie_referrers_submitted', $query);
      //settled
      $query = $leadsTable->find()
        ->select([
          'referrer' => 'ReferrerEntity.nickname',
          'leads' => $leadsTable->find()->func()->count('DISTINCT LeadEntity.lead_id')
        ])
        ->join([
          'table' => 'partner_commissions',
          'alias' => 'PartnerCommissionEntity',
          'conditions' => 'PartnerCommissionEntity.lead_id = LeadEntity.lead_id'
        ])
        ->join([
          'table' => 'referrer_people',
          'alias' => 'ReferrerPeopleEntity',
          'conditions' => 'ReferrerPeopleEntity.id = LeadEntity.referrer_person_id'
        ])
        ->join([
          'table' => 'referrers',
          'alias' => 'ReferrerEntity',
          'conditions' => 'ReferrerEntity.id = ReferrerPeopleEntity.referrer_id'
        ])
        ->join([
          'table' => 'partner_user_leads',
          'alias' => 'PartnerUserLeadsEntity',
          'type' => 'LEFT',
          'conditions' => 'PartnerUserLeadsEntity.lead_id = LeadEntity.lead_id AND PartnerUserLeadsEntity.status = "ACCESS"'
        ])
        ->group('ReferrerEntity.nickname')
        ->where($where)
        ->enableHydration(false);
        $results['settled'] = $this->getQueryResults('dashboard_pie_referrers_settled', $query);
      return $results;
    }

    private function getPieChartDataCallQueuePipeline($where){
      $leadsTable = TableRegistry::getTableLocator()->get('LeadEntity');
      $query = $leadsTable->find()
        ->select([
          'call_queue_status' => 'LeadEntity.call_queue_status',
          'leads' => $leadsTable->find()->func()->count('LeadEntity.lead_id')
        ])
        ->join([
          'table' => 'partner_user_leads',
          'alias' => 'PartnerUserLeadsEntity',
          'type' => 'LEFT',
          'conditions' => 'PartnerUserLeadsEntity.lead_id = LeadEntity.lead_id AND PartnerUserLeadsEntity.status = "ACCESS"'
        ])
        ->group('LeadEntity.call_queue_status')
        ->where($where)
        ->enableHydration(false);

      return $this->getQueryResults('dashboard_pie_call_queue', $query, 'getPieChartDataCallQueuePipelineFormatData');
    }

    private function getPieChartDataCallQueuePipelineFormatData($rows){
      $statusMap = [
        -5 => "Call queue not proceeding",
        -4 => "Max attempts not reached in time",
        -3 => "Not in queue, max attempts reached",
        -2 => "Currently viewed by operator",
        -1 => "Not in queue",
        0 => "Not called yet",
        1 => "1 attempt",
      ];
      foreach ($rows as $k => $row) {
        $statusId = $rows[$k]['call_queue_status'];
        $rows[$k]['call_queue_status'] = $statusMap[$statusId] ?? ($statusId . ' attempts');
      }
      return $rows;
    }

    private function getLeadPipeline($where){
      $leadsTable = TableRegistry::getTableLocator()->get('LeadEntity');
      //all leads
      $where['ManStatusGroupEntity.active'] = 1;
      $where['ManStatusEntity.active'] = 1;

      $query = $leadsTable->find()
        ->select([
          'group_name' => 'ManStatusGroupEntity.group_name',
          'status_name' => 'ManStatusEntity.status_name',
          'leads' => $leadsTable->find()->func()->count('LeadEntity.lead_id')
        ])
        ->join([
          'table' => 'man_statuses',
          'alias' => 'ManStatusEntity',
          'conditions' => 'ManStatusEntity.id = LeadEntity.man_status_id'
        ])
        ->join([
          'table' => 'man_status_groups',
          'alias' => 'ManStatusGroupEntity',
          'conditions' => 'ManStatusGroupEntity.id = ManStatusEntity.man_status_group_id'
        ])
        ->join([
          'table' => 'partner_user_leads',
          'alias' => 'PartnerUserLeadsEntity',
          'type' => 'LEFT',
          'conditions' => 'PartnerUserLeadsEntity.lead_id = LeadEntity.lead_id AND PartnerUserLeadsEntity.status = "ACCESS"'
        ])
        ->group(['ManStatusGroupEntity.group_name', 'ManStatusEntity.status_name'])
        ->where($where)
        ->order(['ManStatusGroupEntity.order','ManStatusEntity.order'])
        ->enableHydration(false);

      return $this->getQueryResults('dashboard_lead_pipeline', $query, 'getLeadPipelineFormatData');
    }

    /**
     * Reformat results to group by group_name
     */
    private function getLeadPipelineFormatData($rows){
      $groupedResults = [];
      foreach ($rows as $row) {
        $groupName = $row['group_name'];
        if (!isset($groupedResults[$groupName])) {
          $groupedResults[$groupName] = [
            'group_name' => $groupName,
            'statuses' => []
          ];
        }
        $groupedResults[$groupName]['statuses'][] = [
          'status_name' => $row['status_name'],
          'leads' => $row['leads']
        ];
      }
      return array_values($groupedResults);
    }

    public function run(){
      try{
        $partner_user = $this->Auth->identify();
        $filters = $this->request->getData();
        $dash = new DashboardAnalyticsService($partner_user['partner_user_id']);
        $filters['partner_id'] = $partner_user['partner_id'];
        $filters['reports'] = [
          "loan_amount_by_status",
          "lead_count_by_status",
          "loan_amount_by_lender",
          "count_of_product_type",
          "loan_amount_by_user",
          "funded_and_total_commission",
          "lend_score_by_lead_count"
        ];
        $filters['start_date'] = !empty($filters['start_date']) ? date('Y-m-d 00:00:00', strtotime($filters['start_date'])) : null;
        $filters['end_date'] = !empty($filters['end_date']) ? date('Y-m-d 23:59:59', strtotime($filters['end_date'])) : null;
        if(empty($partner_user['account_admin']) AND empty($partner_user['access_all_leads']))
          $filters['partner_user_id'] = [$partner_user['partner_user_id']];

        $filters = $this->_unsetEmptyArray($filters);
        $result = $dash->run($filters);
        return $this->setJsonResponse($result);
      }catch(\Exception $e){
        Log::error($e->getMessage());
        Log::error($e->getTraceAsString());
        return $this->setJsonResponse(['success'=>false, 'message'=>$e->getMessage()]);
      }
    }

    private function _unsetEmptyArray($arr){
      foreach($arr as $key => $val){
        if(empty($val) && !is_numeric($val)) unset($arr[$key]);
        elseif(is_array($val)) $arr[$key] = $this->_unsetEmptyArray($val);

        if(empty($arr[$key]) && !is_numeric($arr[$key])) unset($arr[$key]);
      }
      return $arr;
    }

    private function _getFilterItems(){
      $filter_items = Cache::read($this->filter_items_key, $this->cache_config);
      if(empty($filter_items)){
        $filter_items = $this->_getFilterItemsFromDb();
        Cache::write($this->filter_items_key, json_encode($filter_items), $this->cache_config);
      }else{
        $filter_items = json_decode($filter_items, true);
      }
      $partner_user = $this->Auth->identify();
      $filter_items['campaign'] = $this->loadModel('Leads')->getDistinctCampaignByPartnerId($partner_user['partner_id']);
      return $filter_items;
    }

    private function _getFilterItemsFromDb(){
      try{
        $partner_user = $this->Auth->identify();
        $partner_country = strtoupper(getenv('REGION', true));
        $filter_items = [];
        $this->loadModel('Leads');
        $this->loadModel('PartnerProductTypes');
        $this->loadModel('Lenders');
        $config_model = new Config;

        if(!empty($partner_user['account_admin'])){
          $filter_items['partner_users'] = $this->loadModel('PartnerUsers')->getActiveUsers($partner_user['partner_id'], false);
          if($unassigned_leads = $this->loadModel('LeadEntity')->checkUnassignedLeads($partner_user['partner_id'])){
            array_push($filter_items['partner_users'], ['partner_user_id'=>0, 'name'=>'Unassigned Leads']);
          }
        }
        $filter_items['source'] = $this->Leads->getDistinctSourceByPartnerId($partner_user['partner_id']);
        $filter_items['lenders'] = $this->Lenders->leaveSpecificFields(
          $this->Lenders->getLenders([
            'status'=>1,
            'country' => $partner_country,
          ]),
          ['lender_id', 'lender_name']
        );
        $filter_items['status'] = ["Attempting", "In Progress", "Pending", "Rejected", "Settled"];
        $filter_items['product_types'] = $this->_generateTreeStructure($this->PartnerProductTypes->getPartnerProductTypes(['active' => 1], true), ['key'=>'product_type_id', 'label'=>'product_type_name', 'parent_child'=>'sub_product']);
        $filter_items['purpose'] = $config_model->leaveSpecificFields($config_model->putOtherToEndOfTheList($config_model->getConfig('frm_purpose', ['status' => 1,'uses' => 'commercial']), 'purpose'), ['purpose_id', 'purpose']);
        $filter_items['state'] = ["ACT", "NSW", "VIC", "QLD", "SA", "WA", "TAS", "NT"];
        $filter_items['industries'] = $this->_generateTreeStructure($config_model->getConfig('config_industries'), ['key'=>'industry_id', 'label'=>'child', 'parent_child'=>'parent']);
        $filter_items['home_owner'] = ["yes", "no"];
        if($this->asset_filter_available){
          $filter_items['asset_contract_type'] = ["low doc", "full doc"];
          $filter_items['asset_equipments'] = $this->_generateTreeStructure($config_model->getAllEquipment(), ['key'=>'equipment_id', 'label'=>'equipment', 'parent_child'=>'parent_id']);;
        }

        // Convert object to be in array
        if(!empty($filter_items['industries'])) $filter_items['industries'] = array_values($filter_items['industries']);
        if(!empty($filter_items['product_types'])) $filter_items['product_types'] = array_values($filter_items['product_types']);
        if(!empty($filter_items['asset_equipments'])) $filter_items['asset_equipments'] = array_values($filter_items['asset_equipments']);

        return $filter_items;
      }catch(\Exception $e){
        Log::error($e->getMessage());
        Log::error($e->getTraceAsString());
        return $e->getMessage();
      }
    }

    private function _getFilters($filter_items){
      $partner_user = $this->Auth->identify();
      $filter_selected = Cache::read($this->filter_seleteced_key, $this->cache_config);
      if(empty($filter_selected)){
        $filter_selected = [
          'start_date' => date('Y-m-01', strtotime('-1 months')),
          'end_date' => date('Y-m-d'),
          'loan_amount' => [
              'min' => 0,
              'max' => 1000000
          ],
          'partner_user_id' => [],
          'source' => [],
          'campaign' => [],
          'lender_id' => [],
          'status' => [],
          'product_type_id' => [],
          'purpose_id' => [],
          'state' => [],
          'industry_id' => [],
          'home_owner' => [],
        ];

        if($this->asset_filter_available){
          $filter_selected['asset'] = [
            'contract_type' => [],
            'equipment_id' => []
          ];
          $filter_selected['asset']['age'] = new \StdClass();
        }
      }else{
        $filter_selected = json_decode($filter_selected, true);
        unset($filter_selected['partner_id'], $filter_selected['reports']);
      }
      return $filter_selected;
    }


    private function _generateTreeStructure($industries, $options){
      $grouped = [];
      foreach($industries as $i){
        // If it's a parent item,
        // else it's a child item,
        if(empty($i[$options['parent_child']]) OR $i[$options['parent_child']] == '0'){
          if(empty($grouped[$i[$options['key']]])){
            $grouped[$i[$options['key']]] = [
              'key' => $i[$options['key']],
              'label' => $i[$options['label']],
              'data' => $i[$options['label']],
              'children' => []
            ];
          }else{
            $grouped[$i[$options['key']]]['key'] = $i[$options['key']];
            $grouped[$i[$options['key']]]['label'] = $i[$options['label']];
            $grouped[$i[$options['key']]]['data'] = $i[$options['label']];
          }
        }else{
          if(empty($grouped[$i[$options['parent_child']]])){
            $grouped[$i[$options['parent_child']]] = [
              'key' => '',
              'label' => '',
              'data' => '',
              'children' => [],
            ];
          }
          $grouped[$i[$options['parent_child']]]['children'][] = [
            'key' => $i[$options['key']],
            'label' => $i[$options['label']],
            'data' => $i[$options['label']],
          ];
        }
      }
      return $grouped;
    }
    
    // get the filter data for dashboard analytics
    public function getDashboardAnalyticsOld(){
      $requested_data = $this->request->getData();
      Log::write('info', 'getDashboardData'.json_encode($requested_data));
      return $this->setJsonResponse(['success'=>true, 'message'=>'Data fetched successfully']);
      // get the filter data //
    }
}
