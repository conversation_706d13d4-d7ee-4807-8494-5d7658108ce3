<?php
namespace CallQueue\Controller;

use Cake\Database\Expression\QueryExpression;
use CallQueue\Controller\AppController;

use Cake\Datasource\ConnectionManager;
use Cake\Datasource\EntityInterface;
use Cake\Http\Exception\MethodNotAllowedException;
use Cake\Http\Exception\NotFoundException;
use Cake\ORM\TableRegistry;
use Cake\Core\Configure;
use Cake\Log\Log;
use App\Lend\Config;
use App\Lend\LendInternalAuth;
use App\Lend\SocketHelper;
use App\Lend\SqsHelper;

/**
 * Summary of LeadsController
 */
class LeadsController extends AppController
{
  public function initialize()
  {
    parent::initialize();
    if ($this->request->getHeader('take-lead-api-key')) {
      $take_lead_api_key = $this->request->getHeader('take-lead-api-key')[0];
      if ($take_lead_api_key === getenv('TAKE_LEAD_API_KEY')) {
        $this->Auth->allow(['takeLeadLambda']);
      }
      else{
        $this->setJsonResponse(['error'=> 'Invalid take-lead-api-key'], 401);
      }
    }
  }

  private function _getLeadsCondition($user, $include_cooling_down = 0, $include_not_allowed = 0, $extraWhere = ["where" => [], "explain" => []])
  {
    if(empty($user['states_list'])){
      throw new \Exception("User does not have a call queue states list. User id: ".$user['partner_user_id'], 418);
    }
    if(empty($user['product_type_id_list'])){
      throw new \Exception("User does not have a call queue product list. User id: ".$user['partner_user_id'], 418);
    }

    $where = [
      //is current partner
      'LeadEntity.partner_id' => $user['partner_id'],
      //is in call queue
      'LeadEntity.call_queue_status >=' => 0,
      //is in call queue
      '(LeadEntity.is_closed is null OR LeadEntity.is_closed =0)',
      //is in call queue
      'LeadEntity.is_archived <>' => 1,
    ];
    $explain = [
      "leads.parnter_id = " . $user['partner_id'],
      "leads.call_queue_status >= 0",
      "leads.is_closed is null OR leads.is_closed =0)",
      "leads.is_archived <> 1",
    ];
    if ($include_not_allowed == 0) {
      $where[] = array_merge($where, [
        //is in partner user's state list
        '(FIND_IN_SET(`LeadEntity`.`call_state`, \'' . $user['states_list'] . '\') OR `LeadEntity`.`call_state` IS NULL)',
        //is in partner user's product list
        '(FIND_IN_SET(`LeadEntity`.`product_type_id`, \'' . $user['product_type_id_list'] . '\') OR `LeadEntity`.`product_type_id` IS NULL)',
      ]);
      $explain = array_merge($explain, [
        "leads.call_state in user's allowed states (" . $user['states_list'] . ") OR leads.call_state is null",
        "leads.product_type_id in user's allowed products (" . $user['product_type_id_list'] . ") OR leads.product_type_id is null",
      ]);
    }
    if ($include_cooling_down == 0) {
      if ($user['access_to_call_queue'] == 1) {
        $where[] = [
          'PartnerUserLeadsEntity.partner_user_lead_id IS' => NULL
        ];
        $explain[] = "Lead is not assigned to any user, because partner_users.access_to_call_queue = 1";
      }
      $partner = TableRegistry::getTableLocator()->get('PartnerEntity')->findByPartnerId($user['partner_id'])->first()->toArray();
      if(empty($partner['business_open_time']) || empty($partner['business_close_time'])){
        throw new \Exception("Please set business open and close time for partner. ".$user['partner_id'], 418);
      }
      $statesInWorkingHours = $this->_findStatesInWorkingHours(date("Y-m-d H:i:s", strtotime($partner['business_open_time'])), date("Y-m-d H:i:s", strtotime($partner['business_close_time'])));
      if(empty($statesInWorkingHours)){
        throw new \Exception("Outside of business hours: " . $partner['business_open_time'] . "-" . $partner['business_close_time'].".".$user['partner_id'], 418);
      }
      $where[] = array_merge($where, [
        //is lead cooling down: after a lead is called and not answered it cannot be called again until this wait time has passed
        '(LeadEntity.last_attempt_time IS NULL OR TIMESTAMPDIFF(MINUTE, LeadEntity.last_attempt_time, CONVERT_TZ(NOW(), @@system_time_zone, "Australia/Sydney")) > ' . $partner['wait_time'] . ")",
        //if last_attempt_time date is today, check if lead reached max_daily_attempts
        '(DATE(LeadEntity.last_attempt_time) <> CONVERT_TZ(CURDATE(), @@system_time_zone, "Australia/Sydney") OR LeadEntity.last_daily_attempts < ' . $partner['max_daily_attempts'] . ')',
        //is lead state currently in working hours
        'OR' => [
          'LeadEntity.call_state IN' => $statesInWorkingHours,
          'LeadEntity.call_state IS NULL'
          ]
      ]);
      $explain = array_merge($explain, [
        "(leads.last_attempt_time is NULL) OR (current time  - leads.last_attempt_time > " . $partner['wait_time'] . " minutes)",
        "(leads.last_attempt_time is not today) OR (today's total attempts < " . $partner['max_daily_attempts'] . ")",
        "Today's business open hours: " . $partner['business_open_time'] . "-" . $partner['business_close_time'] . ", current working states: " . implode(',', $statesInWorkingHours) . ", leads.call_state IN (" . implode(',', $statesInWorkingHours) . ")"
      ]);
    }
    if ($extraWhere) {
      $where = array_merge($where, $extraWhere['where']);
      $explain = array_merge($explain, $extraWhere['explain']);
    }
    if(getenv('LEND_ENV') == 2 && $user['partner_id'] != 112){
      $explain = [];
    }
    return ["where" => $where, "explain" => $explain];
  }

  public function leadsCount($include_cooling_down = 0, $total_only = 0)
  {
    try {
      if (getenv('LEND_ENV') == 0) {
        $user = $this->Auth->user();
      } else {
        $user = $this->Auth->identify();
      }
      if ($user['access_to_call_queue'] == 0) {
        throw new \Exception("User does not have access to call queue.".$user['partner_user_id'], 401);
      }
      TableRegistry::getTableLocator()->remove('LeadEntity');
      $lead_table = TableRegistry::getTableLocator()->get('LeadEntity', [
        'connection' => ConnectionManager::get('reader_db')
      ]);
      $query = $lead_table->find('all', ['contain' => ['PartnerUserLeadsEntity']]);
      if ($user['access_to_call_queue'] < 3) {
        $include_cooling_down = 0;
      }
      $where = $this->_getLeadsCondition($user, $include_cooling_down);
      $query->select(['total_leads' => $query->func()->count('DISTINCT LeadEntity.lead_id'), 'call_queue_status'])
        ->where($where['where']);
      if($total_only == 0){
        $query->group('call_queue_status');
      }
      $leadsCount = $query->toArray();
      return $this->setJsonResponse(["count" => $leadsCount, "conditions" => $where['explain']]);
    } catch (\Exception $e) {
      if ($e->getCode() == 418) {
        return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode());
      }
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  private function _getCallQueueData($user, $include_cooling_down = 0, $include_not_allowed = 0, $is_first = 0, $extraWhere = ["where" => [], "explain" => []])
  {
    try {
      $lead_table = TableRegistry::getTableLocator()->get('LeadEntity');
      $associated = [
        'PocOwner' => ['fields' => ['first_name', 'last_name', 'state', 'phone', 'mobile', 'avatar_image_url']],
        'PartnerProductTypeEntity' => ['fields' => ['product_type_id', 'product_type_name']],
        'PartnerUserLeadsEntity' => ['fields' => ['partner_user_lead_id']],
        'LeadAssociatedDataEntity'=> ['fields' => ['LeadAssociatedDataEntity__has_submitted_sale'=>'!ISNULL(LeadAssociatedDataEntity.max_sale_id)', 'lender_name']],
        'IntermediaryOriginalPartner' => ['fields' => ['company_name']],
        'LeadMarketingEntity' => ['fields' => ['lead_ga_data_id', 'utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content']],
      ];
      $options = ['contain' => $associated];
      $options['fields'] = [
        'lead_ref',
        'organisation_name',
        'business_name',
        'created',
        'last_changed_date',
        'amount_requested',
        'lead_type',
        'call_queue_status',
        'call_state',
        'last_attempt_time',
        'last_daily_attempts',
        'source',
        'intermediary_original_partner_id',
        'campaign'
      ];
      $query = $lead_table->find('all', $options);
      $where = $this->_getLeadsCondition($user, $include_cooling_down, $include_not_allowed, $extraWhere);
      $select = ['can_take' => '(FIND_IN_SET(`LeadEntity`.`call_state`, \'' . $user['states_list'] . '\') OR `LeadEntity`.`call_state` IS NULL) AND (FIND_IN_SET(`LeadEntity`.`product_type_id`, \'' . $user['product_type_id_list'] . '\') OR `LeadEntity`.`product_type_id` IS NULL)'];
      $query->select($select);
      $order = [
        //Order by Uncalled leads, ordered by the newest first. 
        //Then called leads, ordered by number of calls, and then oldest
        "call_queue_status" => "ASC",
        "(CASE WHEN call_queue_status = 0 then TIMESTAMPDIFF(MINUTE, LeadEntity.created, NOW()) else TIMESTAMPDIFF(MINUTE, NOW(), LeadEntity.created) END)" => "ASC",
      ];
      $query->where($where['where']);
      $query->order($order);
      $query->group("LeadEntity.lead_id");
      if ($is_first == true) {
        $next_lead = $query->first();
        if (!$next_lead) {
          return false;
        }
        return $next_lead->toArray();
      }
      return ["leads" => $query->toArray(), "conditions" => $where['explain']];
    } catch (\Exception $e) {
      if($e->getCode() == 418){
        return false;
      }
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }

  public function getCallQueue($include_cooling_down = 0)
  {
    try {
      if (getenv('LEND_ENV') == 0) {
        $user = $this->Auth->user();
      } else {
        $user = $this->Auth->identify();
      }
      if (!isset($user['partner_user_id'])) {
        throw new \Exception("Can't find a partner user.");
      }
      if ($user['access_to_call_queue'] == 0) {
        throw new \Exception("User does not have access to call queue.".$user['partner_user_id'], 401);
      }
      if ($user['access_to_call_queue'] < 3) {
        $include_cooling_down = 0;
      }
      $leads = $this->_getCallQueueData($user, $include_cooling_down);
      return $this->setJsonResponse($leads);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  public function searchCallQueue()
  {
    try {
      if (!$this->request->is('post')) {
        throw new \Exception("only POST request is available.", 405);
      }
      if (getenv('LEND_ENV') == 0) {
        $user = $this->Auth->user();
      } else {
        $user = $this->Auth->identify();
      }
      if (!isset($user['partner_user_id'])) {
        throw new \Exception("Can't find a partner user.");
      }
      if ($user['access_to_call_queue'] == 0) {
        throw new \Exception("User does not have access to call queue.".$user['partner_user_id'], 401);
      }
      $searchString = $this->request->getData('search');
      if (is_numeric($searchString)) {
        if (strlen($searchString) < 5) {
          throw new \Exception("Please input more than 5 digits to search phone number.");
        }
        $extraWhere['where'] = ["(PocOwner.phone like '%" . $searchString . "%' OR PocOwner.mobile like '%" . $searchString . "%' )"];
      } else {
        if (strlen(str_replace(" ", "", $searchString)) < 3) {
          throw new \Exception("Please input more than 3 letters to search name.");
        }
        if ($searchString == trim($searchString) && strpos($searchString, ' ') !== false) {
          // if $searchString has space, check first_name and last_name separately
          $name = explode(" ", trim($searchString));
          $extraWhere['where'] = ["((PocOwner.first_name like '%" . $name[0] . "%' AND PocOwner.last_name like '%" . $name[1] . "%') OR LeadEntity.organisation_name like '%" . $searchString . "%' OR LeadEntity.business_name like '%" . $searchString . "%' )"];
        } else {
          $extraWhere['where'] = ["(PocOwner.first_name like '%" . $searchString . "%' OR PocOwner.last_name like '%" . $searchString . "%'  OR LeadEntity.organisation_name like '%" . $searchString . "%' OR LeadEntity.business_name like '%" . $searchString . "%' )"];
        }
      }
      $extraWhere['explain'] = $extraWhere['where'];
      $leads = $this->_getCallQueueData($user, 1, 1, 0, $extraWhere);
      return $this->setJsonResponse($leads);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  /**
   * Update a lead
   * @throws \Exception
   * @return void
   */
  public function takeLead()
  {
    try {
      if (!$this->request->is('post')) {
        throw new \Exception("only POST request is available.", 405);
      }
      if (getenv('LEND_ENV') == 0) {
        $user = $this->Auth->user();
      } else {
        $user = $this->Auth->identify();
      }
      if (!isset($user['partner_user_id'])) {
        throw new \Exception("Can't find a partner user.");
      }
      if ($user['access_to_call_queue'] == 0) {
        throw new \Exception("User does not have access to call queue.".$user['partner_user_id'], 401);
      }
      $lead_ref = $this->request->getData('lead_ref');
      $useSqs = $this->request->getData('use_sqs') ?? true;

      $partner_feature = $this->loadModel('PartnerFeatureFlags')->getFeatureFlag(['partner_id' => $user['partner_id']]);
      $sqsTakeLead = !empty($partner_feature['SQS_TAKE_LEAD']);
      if(!$sqsTakeLead || !$useSqs){
        $this->request = $this->request->withData('non_sqs_request', true);
        $this->request = $this->request->withData('partner_user_id', $user['partner_user_id']);
        $this->takeLeadLambda();
      }
      else{
        //put message in SQS take lead queue
        $msg = [
          'partner_user_id' => $user['partner_user_id'],
        ];
        if($lead_ref){
          $msg['lead_ref'] = $lead_ref;
        }
        SqsHelper::sendMessage('SQS_TAKELEAD_URL', $msg, (string)$user['partner_id']);
        return $this->setJsonResponse(["success" => true, "message" => "Take lead request added"]);
      }
      
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  /**
   * Take a lead
   * @throws \Exception
   * @return void
   */
  public function takeLeadLambda()
  {
    try {
      if (!$this->request->is('post')) {
        throw new \Exception("only POST request is available.", 405);
      }
      $data = $this->request->getData();
      $sqsRequest = !isset($data['non_sqs_request']);
      $acceptedFields = ['partner_user_id', 'lead_ref'];
      $data = array_intersect_key($data, array_flip($acceptedFields));
      if (empty($data['partner_user_id'])) {
          throw new \Exception("partner_user_id must be provided");
      }
      $data['partner_user_id'] = (int)$data['partner_user_id'];
      $partner_user_table = TableRegistry::getTableLocator()->get('PartnerUserEntity');
      $user = $partner_user_table->get($data['partner_user_id']);

      if ($user['access_to_call_queue'] == 0) {
        throw new \Exception("User does not have access to call queue.".$user['partner_user_id'], 401);
      }
      $lead_ref = $data['lead_ref'];
      $lend_internal_auth = new LendInternalAuth;
      if ($lead_ref) {
        $lead_id = $lend_internal_auth->unhashLeadId($lead_ref);
      } else {
        $next_lead = $this->_getCallQueueData($user, 0, 0, 1);
        $lead_id = $lend_internal_auth->unhashLeadId($next_lead['lead_ref']);
        if (empty($next_lead)) {
          if(!$sqsRequest){
            throw new \Exception("There is no available lead in the call queue.");
          }
          else
          {
            SocketHelper::sendMessage([$data['partner_user_id']], 'callQueue', ['type'=> 'take_lead_no_lead', 'data' => '']);
            return $this->setJsonResponse(["success" => true, "message" => "There is no available lead in the call queue."]);
          }
        }
      }

      $lead_table = TableRegistry::getTableLocator()->get('LeadEntity');
      $associated = [
        'PocOwner' => ['fields' => ['first_name', 'last_name', 'phone', 'mobile', 'email', 'avatar_image_url']],
        'ManStatusEntity' => ['fields' => ['ManStatusEntity__status_id' => 'ManStatusEntity.id', 'status_name']],
        'PartnerProductTypeEntity' => ['fields' => ['product_type_id', 'product_type_name']],
        'LendStatusEntity' => ['fields' => ['status_name']],
        'LeadCallAttemptEntity' => ['fields' => ['created', 'partner_user_id', 'outcome_type', 'lead_id']],
        'LeadCallAttemptEntity.PartnerUserEntity' => ['fields' => ['partner_user_id', 'name']],
        'PartnerTagEntity' => ['fields' => ['tag', 'color', 'PartnerTagEntity.id']],
        'PartnerUserLeadsEntity' => ['fields' => ['partner_user_lead_id']],
        'PartnerUserLeadsEntity.PartnerUserEntity' => ['fields' => ['name']],
        'LeadAssetFinanceEntity' => ['fields' => ['lead_asset_finance_id', 'equipment_id']],
        'LeadMarketingEntity' => ['fields' => ['lead_ga_data_id', 'utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content']],
        'LeadNotesEntity' => [
            'fields' => ['LeadNotesEntity.lead_id', 'LeadNotesEntity.notes', 'LeadNotesEntity.created']
        ]
      ];
      $options = ['contain' => $associated];
      $options['fields'] = ['lead_ref', 'lead_id', 'account_id', 'organisation_name', 'created', 'last_changed_date', 'amount_requested', 'lead_type', 'call_queue_status', 'call_state', 'last_attempt_time', 'last_daily_attempts', 'purpose_id', 'equipment_id', 'campaign'];
      $lead = $lead_table->get($lead_id, $options);
      $leadArray = $lead->toArray();
      if ($leadArray['call_queue_status'] < 0) {
        throw new \Exception("Sorry, this lead has since been taken.");
      }
      if ($lead_ref && !empty($leadArray['call_state']) && !in_array($leadArray['call_state'], explode(",", $user['states_list']))) {
        throw new \Exception("You can not take leads from state: " . $leadArray['call_state'] . ".".$user['partner_user_id']);
      }
      if ($lead_ref && !empty($leadArray['partner_product_type']['product_type_id']) && !in_array($leadArray['partner_product_type']['product_type_id'], explode(",", $user['product_type_id_list']))) {
        throw new \Exception("You can not take leads of product type: " . $leadArray['partner_product_type']['product_type_name'] . ".".$user['partner_user_id']);
      }
      if (
        isset($leadArray['partner_user_lead'])
        && isset($leadArray['partner_user_lead']['partner_user_lead_id'])
        && $user['access_to_call_queue'] == 1 //only access to unassigned leads
      ) {
        throw new \Exception("Lead has already been assigned to a user.");
      }
      if(isset($leadArray['partner_user_lead']['partner_user_lead_id'])){
        $lead_table->patchEntity($lead, [
          'partner_user_lead' => [
            'partner_user_lead_id' => $leadArray['partner_user_lead']['partner_user_lead_id'],
            'status' => 'REVOKED',
            'lead_id' => $lead_id
          ]
        ]);
        $lead_table->save($lead);
        unset($lead->partner_user_lead);
      }
      $lead_table->patchEntity($lead, [
        'call_queue_status' => -2,
        'partner_user_lead' => [
          'partner_user_id' => $user['partner_user_id'],
          'status' => 'ACCESS',
          'lead_id' => $lead_id
        ]
      ]);
      $lead_table->save($lead);

      //add background job to put lead back in 5 minutes if no action
      $partner = TableRegistry::getTableLocator()->get('PartnerEntity')->findByPartnerId($user['partner_id'])->first()->toArray();
      $this->loadModel('BackgroundJobs')->addBackgroundJob([
        'lead_id' => $lead_id,
        'job_type' => 'put_lead_back_to_queue',
        'ref_id' => json_encode(["run_time" => strtotime("+ " . $partner['take_time'] . "minutes")]),
        'class_name' => 'CallQueue',
        'function_name' => 'put_lead_back_to_queue',
        'created' => date('Y-m-d H:i:s'),
      ]);

      if(!$sqsRequest){
        return $this->setJsonResponse($lead->toArray());
      }
      else{
        $msg = [
          'type'=> 'take_lead',
          'data' => [
            'lead' => $lead->toArray()
          ] 
        ];
        SocketHelper::sendMessage([$data['partner_user_id']], 'callQueue', $msg);
        return $this->setJsonResponse(["success" => true, "message" => "Lead id: ".$lead_id]);
      }
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  public function putLeadBack()
  {
    try {
      if (!$this->request->is('post')) {
        throw new \Exception("only POST request is available.", 405);
      }
      $lead_ref = $this->request->getData('lead_ref');
      if (empty($lead_ref)) {
        throw new \Exception("lead_ref is required.");
      }
      $lend_internal_auth = new LendInternalAuth;
      $data = [];
      $data['lead_id'] = $lend_internal_auth->unhashLeadId($lead_ref);
      if (empty($data['lead_id'])) {
        throw new \Exception("Can't find a lead.");
      }
      if (getenv('LEND_ENV') == 0) {
        $user = $this->Auth->user();
      } else {
        $user = $this->Auth->identify();
      }
      if (!isset($user['partner_user_id'])) {
        throw new \Exception("Can't find a partner user.");
      }
      $lead_table = TableRegistry::getTableLocator()->get('LeadEntity');
      $lead = $lead_table->get($data['lead_id'], ['contain' => ['LeadCallAttemptEntity']]);
      $leadArray = $lead->toArray();
      if ($leadArray['call_queue_status'] != -2) {
        throw new \Exception("Lead is in call queue. Ref: ".$lead_ref);
      }
      $lead_table->patchEntity($lead, [
        'call_queue_status' => count($leadArray['lead_call_attempts']),
      ]);
      $lead_table->save($lead);
      $partner_user_lead_table = TableRegistry::getTableLocator()->get('PartnerUserLeadsEntity');
      $partner_user_lead = $partner_user_lead_table->find()
                            ->where([
                                'status' => "ACCESS",
                                'lead_id' => $data['lead_id'],
                                'partner_user_id' => $user['partner_user_id'],
                            ])
                            ->first();
      if ($partner_user_lead) {
        $partner_user_lead_table->patchEntity($partner_user_lead, [
          'status' => 'REVOKED',
        ]);
        $partner_user_lead_table->save($partner_user_lead);
      }
      return $this->setJsonResponse(array_intersect_key($lead->toArray(), ['lead_ref' => null, 'call_queue_status' => null]));
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  /**
   * _findStatesInWorkingHours
   * By ChatGPT
   *
   * @param  mixed $start_time
   * @param  mixed $end_time
   * @return [] an array of Australian states that have their current time within that period
   */
  private function _findStatesInWorkingHours($start_time = null, $end_time = null)
  {
    // Define an array of time zones representing the Australian states
    $australian_states = array(
      'WA' => 'Australia/Perth',
      'NT' => 'Australia/Darwin',
      'QLD' => 'Australia/Brisbane',
      'NSW' => 'Australia/Sydney',
      'ACT' => 'Australia/Sydney',
      'VIC' => 'Australia/Melbourne',
      'TAS' => 'Australia/Hobart',
      'SA' => 'Australia/Adelaide',
    );

    // Initialize an empty array to store the states that are within the period
    $states_within_period = array();

    // Loop through each state's time zone
    foreach ($australian_states as $state => $time_zone) {
      // Get the current time in the state's time zone
      $current_time_in_state = new \DateTime('now', new \DateTimeZone($time_zone));

      // Check if the current time is within the period
      if (
        $current_time_in_state >= new \DateTime($start_time, new \DateTimeZone($time_zone)) &&
        $current_time_in_state <= new \DateTime($end_time, new \DateTimeZone($time_zone))
      ) {
        $states_within_period[] = $state;
      }
    }
    // Return the array of states that have their current time within the period
    return $states_within_period;
  }

  public function removeFromQueue()
  {
    try {
      if (!$this->request->is('post')) {
        throw new \Exception("only POST request is available.", 405);
      }
      $lend_internal_auth = new LendInternalAuth;
      $schema = ['lead_ref' => null, 'assignee_id' => null, 'man_status_id' => null, 'is_closed' => null, 'call_queue_status' => null];
      $lead_data = array_intersect_key($this->request->data(), $schema);
      if (empty($lead_data['lead_ref'])) {
        throw new \Exception("lead_ref is required.");
      }
      $lead_data['lead_id'] = $lend_internal_auth->unhashLeadId($lead_data['lead_ref']);
      if (empty($lead_data['lead_id'])) {
        throw new \Exception("Can't find a lead.");
      }
      if (getenv('LEND_ENV') == 0) {
        $user = $this->Auth->user();
      } else {
        $user = $this->Auth->identify();
      }
      $lead_table = TableRegistry::getTableLocator()->get('LeadEntity');
      $lead = $lead_table->get($lead_data['lead_id'], ['contain' => ['PartnerUserLeadsEntity']]);
      $leadArray = $lead->toArray();
      if ($leadArray['call_queue_status'] < 0) {
        throw new \Exception("Lead is not in call queue. Ref: ".$lead_data['lead_ref']);
      }
      if (!isset($user['account_admin']) && empty($user['permission_edit_assignee']) && $leadArray['partner_user_lead']['partner_user_id'] != $user['partner_user_id']) {
        throw new \Exception("Not an account admin and lead is not assigned to you." . $user['partner_user_id']);
      }
      if (isset($lead_data['call_queue_status'])) {
        $lead_table->patchEntity($lead, ['call_queue_status' => $lead_data['call_queue_status']]);
      }else{
        $lead_table->patchEntity($lead, ['call_queue_status' => -1]);
      }
      if (isset($leadArray['partner_user_lead'])) {
        $lead_table->patchEntity($lead, [
          'partner_user_lead' => [
            'partner_user_lead_id' => $leadArray['partner_user_lead']['partner_user_lead_id'],
            'status' => 'REVOKED',
          ]
        ]);
        $lead_table->save($lead);
      }
      if (isset($lead_data['man_status_id'])) {
        $lead_table->patchEntity($lead, ['man_status_id' => $lead_data['man_status_id']]);
      }
      if (isset($lead_data['is_closed'])) {
        $lead_table->patchEntity($lead, ['is_closed' => $lead_data['is_closed']]);
      }
      $lead_table->save($lead);
      if (isset($lead_data['assignee_id'])) {
        $lead_table->patchEntity($lead, [
          'partner_user_lead' => [
            'partner_user_id' => $lead_data['assignee_id'],
            'status' => 'ACCESS',
            'lead_id' => $lead_data['lead_id']
          ]
        ]);
      }

      $lead_table->save($lead);
      return $this->setJsonResponse(array_intersect_key($lead->toArray(), array_merge($schema, ['call_queue_status' => null])));
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }


  public function removeFromMultipleQueue()
  {

      try {
          if (!$this->request->is('post')) {
              throw new \Exception("only POST request is available.", 405);
          }
          $lend_internal_auth = new LendInternalAuth;
          $schema = ['selected_leads' => [], 'assignee_id' => null, 'man_status_id' => null, 'is_closed' => null, 'call_queue_status' => null];
  
          $lead_data = array_intersect_key($this->request->data(), $schema);
  
          if (empty($lead_data['selected_leads'])) {
              throw new \Exception("At least 1 lead_ref is required.");
          }
  
          if (getenv('LEND_ENV') == 0) {
              $user = $this->Auth->user();
          } else {
              $user = $this->Auth->identify();
          }
          if (!isset($user['account_admin']) && empty($user['permission_edit_assignee'])) {
              throw new \Exception("Not an account admin." . $user['partner_user_id']);
          }
  
          $lead_table = TableRegistry::getTableLocator()->get('LeadEntity');
          $processedLeads = []; 

          foreach ($lead_data['selected_leads'] as $lead_ref) {

              $lead_id = $lend_internal_auth->unhashLeadId($lead_ref);
              if (empty($lead_id)) {
                  throw new \Exception("Can't find a lead with ref: " . $lead_ref);
              }
  
              $lead = $lead_table->get($lead_id, ['contain' => ['PartnerUserLeadsEntity']]);
              $leadArray = $lead->toArray();
  
              if ($leadArray['call_queue_status'] < 0) {
                  throw new \Exception("Lead is not in call queue. Ref: " . $lead_ref);
              }
              if (isset($lead_data['call_queue_status'])) {
                $updates['call_queue_status'] = $lead_data['call_queue_status'];
              }else{
                $updates['call_queue_status'] = -1;
              }
              if (isset($lead_data['man_status_id'])) {
                  $updates['man_status_id'] = $lead_data['man_status_id'];
              }
              if (isset($lead_data['is_closed'])) {
                  $updates['is_closed'] = $lead_data['is_closed'];
              }
              if (isset($lead_data['assignee_id'])) {
                  $updates['partner_user_lead'] = [
                      'partner_user_id' => $lead_data['assignee_id'],
                      'status' => 'ACCESS',
                      'lead_id' => $lead_id
                  ];
              }
  
              $lead = $lead_table->patchEntity($lead, $updates);
              $lead_table->save($lead);

              $leadOutput = array_intersect_key($lead->toArray(), array_merge($schema, ['call_queue_status' => null]));
              $leadOutput += ['lead_ref' => $lead_ref]; 
              $processedLeads[] = $leadOutput;
          }
  
          return $this->setJsonResponse($processedLeads);
      } catch (\Exception $e) {
          Log::error($e->getMessage());
          Log::error($e->getTraceAsString());
          return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
      }
  }
  



}