<?php

namespace LeadApisV2\Controller;

use App\Enums\LendSignTemplateUse;
use Cake\Http\Exception\HttpException;
use Cake\Log\Log;
use Cake\ORM\TableRegistry;
use App\Lend\LendInternalAuth;

use App\Lend\SignatureServiceFactory;
use Cake\Core\Configure;
use Cake\ORM\Table;
use LeadApisV2\Helper\CreditGuidePageStatusHelper;

class NonSignatureCreditGuideController extends AppController
{
    public function initialize()
    {
        parent::initialize();
        $this->loadComponent('AvatarAPI');
        $this->flowShortName = LendSignTemplateUse::NonSignatureCreditGuide;
    }

    public function previewUrl($leadRef, $ownerRefs, $platform)
    {
        try {
            $ownerRefsArray = explode(',', $ownerRefs);
            $user = $this->getPartnerUserByLeadRef($leadRef);

            if (empty($user)) {
                Log::debug('** Failed to get user by leadRef: ' . $leadRef . PHP_EOL);
                return $this->setJsonResponse(['success' => false, 'url' => '', 'message' => 'User not found']);
            }

            $preview = [];
            $sign = new SignatureServiceFactory($user->user, LendSignTemplateUse::NonSignatureCreditGuide);
            foreach ($ownerRefsArray as $ownerRef) {
                $ownerId = (new LendInternalAuth)->unhashOwnerId($ownerRef);
                $requestParams = [
                    'leadRef' => $leadRef,
                    'requestOwnerIds' => [$ownerId],
                    'via' => [$ownerId => 'email'],
                ];
                $payload = $sign->getSignatureTemplatePayload($requestParams);
                $preview[] = $sign->generatePreview([
                    "flow" => $payload['flow'],
                    "dataJson" => json_encode([
                        "recipients" => $payload['recipients'],
                        "payload" => $payload['payload'],
                        "callbackUrl" => null,
                    ]),
                ]);
            }
            
            $refs = array_map(function ($item) {
                return $item['ref'];
            }, $preview);

            $signatures = array_map(function ($item) {
                return $item['signatures'];
            }, $preview);

            $redirectUrl = getEnv("DOMAIN_LEND_SIGNATURE") . "/non-signature/" . implode("/", $refs) . "?platform=".$platform."&owners=" . $ownerRefs . "&signature=" . implode("|", $signatures) . "&referrer=" . urlencode($_SERVER['HTTP_REFERER']);
            Log::debug('** Get LendSign Url: ' . $redirectUrl . PHP_EOL);
            return $this->setJsonResponse([
                'success' => true,
                'url' => $redirectUrl,
                'message' => 'Preview generated successfully',
            ]);
        } catch (\Exception $e) {
            Log::error('** Failed to generate preview: ' . $e->getMessage() . PHP_EOL);
            return $this->setJsonResponse(['success' => false, 'url' => '', 'message' => $e->getMessage()]);
        }
    }

    public function send(){
        $user = $this->Auth->user();
        if (empty($user)) {
            $user = $this->Auth->identify();
        }

        try {
            $this->request->allowMethod(['POST', 'OPTIONS']);
            $data = $this->request->getData();
            if (!$data['lead_ref'] || !$data['owner_refs'] || !$data['pdfs']) {
                return $this->setJsonResponse(['success' => false, 'message' => 'Missing required parameters']);
            }

            $leadId = (new LendInternalAuth)->unhashLeadId($data['lead_ref']);
            $partnerUser = TableRegistry::getTableLocator()->get('PartnerUserLeadsEntity')
                ->find()
                ->contain(['PartnerUserEntity'])
                ->where(['lead_id' => $leadId])
                ->first();

            $owners = [];
            $ownerTable = TableRegistry::getTableLocator()->get('LeadOwnersEntity');
            foreach ($data['owner_refs'] as $ownerRef) {
                $ownerId = (new LendInternalAuth)->unhashOwnerId($ownerRef);
                $owner = $ownerTable->get($ownerId);
                $owners[$owner->owner_id] = $owner;

                if (empty($owner)) {
                    return $this->setJsonResponse(['success' => false, 'message' => 'Owner not found']);
                }

                $permission_check = $this->checkPermission($owner->lead_id, null, $user['account_type'] === 'Applicant');
                if (!$permission_check['success']) return $this->setJsonResponse(['success' => false, 'message' => $permission_check['message']]);
            }

            $sign = new SignatureServiceFactory($partnerUser->user, $this->flowShortName);

            foreach ($owners as $owner) {
                $pdfBuffer = $data['pdfs'][$owner->owner_ref] ?? null;
                if (empty($pdfBuffer)) {
                    continue;
                }
                $fileUploadResult = $this->generatePDF($sign, $data['lead_ref'], $owner->owner_ref, $partnerUser['partner_user_ref'], $pdfBuffer);
                $this->processSend($owner->owner_id, $partnerUser->user, $fileUploadResult, 'sent', 'broker-platform');
            }

            CreditGuidePageStatusHelper::updateStatusByLeadId($owner['lead_id']);
            return $this->setJsonResponse(['success' => true, 'message' => 'Consent Updated']);
        } catch (\Throwable $th) {
            return $this->setJsonResponse(['success' => false, 'message' => $th->getMessage()]);
        }
    }

    public function updateNonSignatureConsent()
    {
        $user = $this->Auth->user();
        if (empty($user)) {
            $user = $this->Auth->identify();
        }

        try {
            $this->request->allowMethod(['POST', 'OPTIONS']);
            $data = $this->request->getData();
            if (!$data['lead_ref'] || !isset($data['accepted']) || !$data['owner_refs'] || !$data['pdfs']) {
                return $this->setJsonResponse(['success' => false, 'message' => 'Missing required parameters']);
            }

            $leadId = (new LendInternalAuth)->unhashLeadId($data['lead_ref']);
            $partnerUser = TableRegistry::getTableLocator()->get('PartnerUserLeadsEntity')
                ->find()
                ->contain(['PartnerUserEntity'])
                ->where(['lead_id' => $leadId])
                ->first();

            $owners = [];
            $ownerTable = TableRegistry::getTableLocator()->get('LeadOwnersEntity');
            foreach ($data['owner_refs'] as $ownerRef) { //check access
                $ownerId = (new LendInternalAuth)->unhashOwnerId($ownerRef);
                $owner = $ownerTable->get($ownerId);
                $owners[$owner->owner_id] = $owner;

                if (empty($owner)) {
                    return $this->setJsonResponse(['success' => false, 'message' => 'Owner not found']);
                }

                $permission_check = $this->checkPermission($owner->lead_id, null, $user['account_type'] === 'Applicant');
                if (!$permission_check['success']) return $this->setJsonResponse(['success' => false, 'message' => $permission_check['message']]);
            }

            $sign = new SignatureServiceFactory($partnerUser->user, $this->flowShortName);

            foreach ($owners as $owner) {
                $pdfBuffer = $data['pdfs'][$owner->owner_ref] ?? null;
                if (empty($pdfBuffer)) {
                    continue;
                }

                $fileUploadResult = $this->generatePDF($sign, $data['lead_ref'], $owner->owner_ref, $partnerUser['partner_user_ref'], $pdfBuffer);
                $this->processSend($owner->owner_id, $partnerUser->user, $fileUploadResult, $data['accepted'] ? 'sent' : 'declined', 'client-app');
                $owner->hl_no_sig_cg_consent = $data['accepted'] ? 1 : 0;
                if ($data['accepted']) {
                    $owner->consent = date('Y-m-d H:i:s');
                }
                $ownerTable->save($owner);
            }

            CreditGuidePageStatusHelper::updateStatusByLeadId($owner['lead_id']);
            return $this->setJsonResponse(['success' => true, 'message' => 'Consent Updated']);
        } catch (\Throwable $th) {
            return $this->setJsonResponse(['success' => false, 'message' => $th->getMessage()]);
        }
    }

    private function getPartnerUserByLeadRef($leadRef)
    {
        $leadId = (new LendInternalAuth)->unhashLeadId($leadRef);
        return TableRegistry::getTableLocator()->get('PartnerUserLeadsEntity')
            ->find()
            ->contain(['PartnerUserEntity'])
            ->where(['lead_id' => $leadId])
            ->first();
    }

    private function generatePDF($sign, $leadRef, $ownerRef, $partnerUserRef, $pdfBuffer)
    {
        $ownerId = (new LendInternalAuth)->unhashOwnerId($ownerRef);

        $date = date('YmdHis', time());
        $fileName = "non_signature_credit_guide_{$date}_{$ownerRef}";
        return $this->generatePdfFromBase64AndUploadToS3(
            [
                'owner_ref' => $ownerRef,
                'partner_user_ref' => $partnerUserRef,
                'date' => date('jS F o'),
                'base64' => $pdfBuffer,
            ],
            'export-non-signature-credit-guide',
            $fileName,
            false
        );
    }


    private function processSend($ownerId, $partnerUser, $fileUploadResult, $status = 'sent', $platform = null)
    {
        $owner = TableRegistry::getTableLocator()->get('LeadOwnersEntity')->get($ownerId);
        $conPartnerUserSettings = TableRegistry::getTableLocator()
            ->get('ConPartnerUserSettings')
            ->find('all')
            ->where(['partner_user_id' => $partnerUser['partner_user_id']])
            ->first();

        $brokerCompanyName = $conPartnerUserSettings['entity_name'];
        $brokerEmail = $conPartnerUserSettings['email'] ?? $partnerUser['email'];

        $recipientEmail = $owner['email'];

        $extraData = [
            'applicant_first_name' => $owner['first_name'],
            'broker_full_name' => $partnerUser['name'],
            'broker_company_name' => $brokerCompanyName,
            'client_email' => $recipientEmail,
            'from' => [
                'email' => $brokerEmail,
                'name' =>  $brokerCompanyName,
            ],
            'attachments' => [
                'non_signature_credit_guide.pdf' => [
                    'file' => $fileUploadResult['temp_file'],
                    'mimetype' => 'application/pdf',
                ],
            ],
        ];

        try {
            $notificationSend = $this->LoadModel('PartnerNotifications')->sendPartnerNotifications(
                $partnerUser['partner_id'],
                'NonSignatureCreditGuideEmail',
                $owner['lead_id'],
                [],
                null,
                $extraData
            );

            if (empty($notificationSend)) {
                throw new \Exception('Failed to send email');
            } else {
                foreach ($notificationSend as $notification) {
                    if ($notification['successfully_sent'] === false) {
                        throw new \Exception('Failed to send email');
                    }
                }
                $lendSignatureRequestTable = TableRegistry::getTableLocator()->get('LendSignatureRequestEntity');
                $existingRequest = $lendSignatureRequestTable->newEntity();
                $existingRequest->owner_id = $owner->owner_id;
                $existingRequest->template_use_shortname = LendSignTemplateUse::NonSignatureCreditGuide;
                $existingRequest->template_ref = '';
                $existingRequest->partner_id = $partnerUser->user->partner_id;
                $existingRequest->partner_user_id = $partnerUser->partner_user_id;
                $existingRequest->lead_id = $owner->lead_id;
                $existingRequest->owner_id = $owner->owner_id;
                $existingRequest->service_envelope_id = null;
                $existingRequest->recipient_name = $owner->full_name;
                $existingRequest->recipient_email = $owner->email;
                $existingRequest->recipient_mobile = $owner->mobile;
                $existingRequest->via_sms = 0;
                $existingRequest->via_email = 1;
                $existingRequest->status = $status;
                $existingRequest->upload_url = $fileUploadResult['s3_result']['url'];
                $existingRequest->sent_time = date('Y-m-d H:i:s');
                $existingRequest->platform = $platform;
                $lendSignatureRequestTable->save($existingRequest);
            }
        } catch (\Exception $e) {
            Log::info('** Failed to send NonSignatureCreditGuide: ' . json_encode($extraData) . PHP_EOL, ['scope' => ['notifications']]);
            Log::info('** Failed to send NonSignatureCreditGuide error: ' . $e->getMessage() . PHP_EOL, ['scope' => ['notifications']]);
            throw new HttpException('Failed to send Non Signature Credit Guide', 500);
        }
    }

    private function generatePdfFromBase64AndUploadToS3($fileGenerationPayload, $pdfType, $s3Filename, $unlinkTmp = true)
    {
        $temp = tempnam(sys_get_temp_dir(), $s3Filename . '.pdf');
        $year = date('Y');

        $s3Path = $pdfType . '/' . $year . '/' . $s3Filename . '.pdf';

        $base64 = str_replace("data:application/pdf;base64,", "", $fileGenerationPayload['base64']);
        $pdf_decoded = base64_decode($base64);
        $tempOpen = fopen($temp, 'w+');
        fwrite($tempOpen, $pdf_decoded);
        fclose($tempOpen);

        $size = filesize($temp);

        $uploadResult = $this->uploadToS3([
            'path' => $s3Path,
            'file' => [
                'type' => 'application/pdf',
                'tmp_name' => $temp,
                'size' => $size,
            ],
        ]);
        Log::info('AppController::generatePdfAndUploadToS3 => ' . json_encode([
            'payload' => $fileGenerationPayload,
            's3Path' => $s3Path,
            'pdf_decoded' => $pdf_decoded,
            'uploadResult' => $uploadResult,
        ]) . PHP_EOL, ['scope' => ['debug']]);

        if ($unlinkTmp) {
            unlink($temp);
            return [
                's3_result' => $uploadResult,
                'size' => $size,
            ];
        }

        return [
            'temp_file' => $temp,
            'size' => $size,
            's3_result' => $uploadResult
        ];
    }
}
