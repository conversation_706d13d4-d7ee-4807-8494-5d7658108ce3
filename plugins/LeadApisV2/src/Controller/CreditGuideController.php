<?php

namespace LeadApisV2\Controller;

use App\Enums\LendSignTemplateUse;
use App\Lend\SignatureServiceFactory;
use Cake\Log\Log;
use Cake\ORM\TableRegistry;
use App\Lend\LendInternalAuth;
use Lead<PERSON>pis\Controller\LeadApisController;
use Cake\Http\Exception\HttpException;
use LeadApisV2\Helper\CreditGuidePageStatusHelper;

class CreditGuideController extends AppController
{

    public $flowShortName = LendSignTemplateUse::CreditGuideAndQuote;
    public function initialize(){
        parent::initialize();
        $this->loadComponent('AvatarAPI');
    }
    
    public function preview($leadRef)
    {
        if (empty($leadRef)) {
            $data = $this->request->getData();
            $leadRef = $data['lead_ref'];
        }
        if (empty($leadRef)) {
            return $this->setJsonResponse(['success' => false, 'message' => 'Missing Lead reference']);
        }
        $user = $this->Auth->user();
        if (empty($user)) {
            $user = $this->Auth->identify();
        }

        $leadId = (new LendInternalAuth)->unhashLeadId($leadRef);
        $permission_check = $this->checkPermission($leadId, null, $user['account_type'] === 'Applicant');
        if (!$permission_check['success']) return $this->setJsonResponse(['success' => false, 'message' => $permission_check['message']]);

        $partnerUserRef = null;
        if (!empty($user)) {
            $partnerUserRef = $user['partner_user_ref'];
        }

        $payload = [
            'lead_ref' => $leadRef,
            'partner_user_ref' => $partnerUserRef,
        ];
        return (new LeadApisController())->exportAsPdf($payload, 'export-credit-guide');
    }

    public function previewPrivacy($type = 'consumer')
    {
        $supportedTypes = [
            'consumer',
            'commercial',
        ];
        if (!in_array($type, $supportedTypes)) {
            return $this->setJsonResponse(['success' => false, 'message' => 'Not supported Privacy type']);
        }
        $documentsMapByType = [
            'consumer' => [
                'partner_form' => 'Consumer Privacy Form',
                'lend_form' => 'Consumer Full Privacy Form',
                'lend_form_acr' => 'Consumer Full Privacy Form with ACR',
            ],
            'commercial' => [
                'partner_form' => 'Privacy Form',
                'lend_form' => 'Full Privacy Form',
                'lend_form_acr' => 'Full Privacy Form with ACR',
            ]
        ];

        $user = $this->Auth->user();
        if (empty($user)) {
            $user = $this->Auth->identify();
        }

        $partnerId = $user['partner_id'];
        $privacyFormUrl = null;
        $customPrivacyFormRecord = TableRegistry::getTableLocator()
            ->get('PartnerCustomPrivacyFormsEntity')
            ->find()
            ->where([
                'partner_id' => $partnerId,
                'document_title' => $documentsMapByType[$type]['partner_form'],
                'status' => 'Ready to Use',
            ])
            ->first();
        
        $bucket = null;
        if (!empty($customPrivacyFormRecord)) {
            $privacyFormUrl = $customPrivacyFormRecord['s3full'];
            $bucket = 'signature-service-templates'.(getenv('LEND_ENV')=='2' ? '' : '-staging');
        } else {
            $conPartnerUserSettings = TableRegistry::getTableLocator()
                ->get('ConPartnerUserSettingsEntity')
                ->find()
                ->where(['partner_user_id' => $user['partner_user_id']])
                ->first();
            if (!empty($conPartnerUserSettings) && !empty($conPartnerUserSettings['acr'])) {
                $lendPrivacyFormRecord = TableRegistry::getTableLocator()
                    ->get('PartnerCustomPrivacyFormsEntity')
                    ->find()
                    ->where([
                        'document_title' => $documentsMapByType[$type]['lend_form_acr'],
                        'status' => 'Ready to Use',
                    ])
                    ->first();
            }
            else {
                $lendPrivacyFormRecord = TableRegistry::getTableLocator()
                    ->get('PartnerCustomPrivacyFormsEntity')
                    ->find()
                    ->where([
                        'document_title' => $documentsMapByType[$type]['lend_form'],
                        'status' => 'Ready to Use',
                    ])
                    ->first();
            }
            $privacyFormUrl = $lendPrivacyFormRecord['s3full'];
        }

        if (empty($privacyFormUrl)) {
            throw new HttpException('No privacy form found', 404);
        }
        $fileUrl = $this->getS3FileUrl($privacyFormUrl, 2, $bucket);

        header('Pragma: public');     // required
        header('Expires: 0');     // no cache
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Cache-Control: private', false);
        header('Content-Type: application/pdf');
        header('Content-Disposition: inline; filename="PrivacyForm.pdf"');
        header('Content-Transfer-Encoding: binary');
        header('Connection: close');
        readfile($fileUrl);

        exit;
    }

    public function previewLendSignature($leadRef){
        $leadId = (new LendInternalAuth)->unhashLeadId($leadRef);
        $owners = TableRegistry::getTableLocator()->get('LeadOwnersEntity')->find()->where(['lead_id' => $leadId])->toArray();
        $user = $this->Auth->user();
        if (empty($user)) {
            $user = $this->Auth->identify();
        }
        $requestOwnerIds = [];
        $requestVia = [];
        foreach ($owners as $owner) {
            $requestOwnerIds[] = $owner->owner_id;
            $requestVia[$owner->owner_id] = 'email';
        }
        $sign = new SignatureServiceFactory($user, $this->flowShortName);
        $requestParams = [
            'leadRef' => $leadRef,
            'requestOwnerIds' => $requestOwnerIds,
            'via' => $requestVia,
        ];
        $payload = $sign->getSignatureTemplatePayload($requestParams);
        $preview = $sign->generatePreview([
            "flow" => $payload['flow'],
            "dataJson" => json_encode([
                "recipients" => $payload['recipients'],
                "payload" => $payload['payload'],
            ]),
        ]);
        if($preview['ref']){
            $this->redirect(getEnv("DOMAIN_LEND_SIGNATURE")."/preview/".$preview['ref']."?signature=".$preview['signatures']);
        }else{
            throw new HttpException('No privacy form found', 404);
        }
    }

    public function send()
    {
        try{
            $params = $this->request->getData();
            $ownerRefs = $params['owner_refs'];
            $requestOwnerIds = [];
            $requestVia = [];
            foreach ($ownerRefs as $ownerRef) {
                $ownerId = (new LendInternalAuth)->unhashOwnerId($ownerRef);
                $requestOwnerIds[] = $ownerId;
                if (!empty($params['via']) && !empty($params['via'][$ownerRef])) {
                    $requestVia[$ownerId] = $params['via'][$ownerRef];
                }
            }
            if (empty($requestOwnerIds)) {
                return $this->setJsonResponse(['success' => false, 'message' => 'Owner not found']);
            }
            $owner = TableRegistry::getTableLocator()->get('LeadOwnersEntity')->get($requestOwnerIds[0]);
            $leadId = $owner['lead_id'];
            $leadRef = (new LendInternalAuth)->hashLeadId($leadId);

            $user = $this->Auth->user();
            if (empty($user)) {
                $user = $this->Auth->identify();
            }

            $permission_check = $this->checkPermission($leadId, null, $user['account_type'] === 'Applicant');
            if (!$permission_check['success']) return $this->setJsonResponse(['success' => false, 'message' => $permission_check['message']]);

            $lendSignatureRequestTable = TableRegistry::getTableLocator()->get('LendSignatureRequestEntity');

            $sign = new SignatureServiceFactory($user, $this->flowShortName);
            $requestParams = [
              'leadRef' => $leadRef,
              'requestOwnerIds' => $requestOwnerIds,
              'via' => $requestVia,
            ];
            $newEnvelope = $sign->callService($requestParams);

            if (is_array($newEnvelope) && !empty($newEnvelope['success']) && $newEnvelope['success'] == true) {
                $leadId = (new LendInternalAuth)->unhashLeadId($leadRef);
                $leadOwners = TableRegistry::getTableLocator()
                    ->get('LeadOwnersEntity')
                    ->find()
                    ->where(['owner_id IN' => $requestOwnerIds])
                    ->toArray();

                $recordIds = [];
                $envelopeId = $newEnvelope['data'][0]['envelopeId'];
                $templateRef = $newEnvelope['data'][0]['templateRef'];
                $createdDate = date('Y-m-d H:i:s', time());
                foreach ($leadOwners as $owner) {
                    $requestRecord = $lendSignatureRequestTable->newEntity();
                    $requestRecord->template_use_shortname = $this->flowShortName;
                    $requestRecord->owner_id = $ownerId;
                    $requestRecord->template_ref = $templateRef;
                    $requestRecord->partner_id = $user['partner_id'];
                    $requestRecord->partner_user_id = $user['partner_user_id'];
                    $requestRecord->lead_id = $leadId;
                    $requestRecord->owner_id = $owner->owner_id;
                    $requestRecord->sent_time = $createdDate;
                    $requestRecord->status = 'sent';
                    $requestRecord->service_envelope_id = $envelopeId;
                    $requestRecord->recipient_name = $owner->full_name;
                    $requestRecord->recipient_email = $owner->email;
                    $requestRecord->recipient_mobile = $owner->mobile;
                    if (!empty($requestVia) && !empty($requestVia[$owner->owner_id])) {
                        $requestRecord->via_sms = $requestVia[$owner->owner_id]['via_sms'];
                        $requestRecord->via_email = $requestVia[$owner->owner_id]['via_email'];
                    }
                    $record = $lendSignatureRequestTable->save($requestRecord);
                    $recordIds[] = $record->id;
                }

                CreditGuidePageStatusHelper::updateStatus($leadRef);
                $lead_poc_owner = $this->getTableLocator()->get('LeadOwnersEntity')->find('all')->where(['lead_id' => $leadId , 'point_of_contact' => true])->first();
                $record = $lendSignatureRequestTable
                    ->find()
                    ->where(['owner_id' => $lead_poc_owner['owner_id'],'status'=>'sent'])
                    ->first();
                
                if($record){
                    if(!empty($lead_poc_owner['email']) &&  empty($lead_poc_owner['avatar_image_url'])){
                        $avatar_image_url = $this->AvatarAPI->getImageURL(trim($lead_poc_owner['email']));
                        $this->getTableLocator()->get('LeadOwnersEntity')->updateAll(['avatar_image_url' => $avatar_image_url], ['owner_id' => $lead_poc_owner['owner_id']]);
                    }
                }

                return $this->setJsonResponse([
                    'success'=>true,
                    'result'=> [
                        'new_envelope' => $newEnvelope['data'][0],
                        'forms' => $recordIds,
                    ]
                ]);
            } else {
                $errorMessage = PrivacyFormsController::lendSignatureErrorsToMessage($newEnvelope, 'Error sending Credit Guide');
                return $this->setJsonResponse(['success' => false, 'message' => $errorMessage]);
            }
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage(), 'error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
        }
    }

    public function migrate() {
      $params = $this->request->getQueryParams();
      $pass = $params['pass'];
      if (empty($pass) || $pass !== 'JrkjNKSf4v0a1S') {
        throw new \Exception("Please specify the pass to run this command.");
      }
      $creditGuideQuoteTable = TableRegistry::getTableLocator()->get('ConCreditGuideQuoteEntity');
      $lendSignatureRequestsTable = TableRegistry::getTableLocator()->get('LendSignatureRequestEntity');
      $notMigratedRecords = $creditGuideQuoteTable->find()->where(['migrated' => 0])->contain(['LeadOwnersEntity'])->toArray();

      foreach ($notMigratedRecords as $record) {

        $newLendSignatureRequestRecord = $lendSignatureRequestsTable->newEntity();
        $newLendSignatureRequestRecord->template_ref = '';
        $newLendSignatureRequestRecord->template_use_shortname = $this->flowShortName;
        $newLendSignatureRequestRecord->owner_id = $record->owner_id;
        $newLendSignatureRequestRecord->lead_id = $record->owner->lead_id;

        if (empty($record->recipient_name)) {
          $newLendSignatureRequestRecord->recipient_name = $record->owner->full_name;
          $newLendSignatureRequestRecord->recipient_email = $record->owner->email;
          $newLendSignatureRequestRecord->recipient_mobile = $record->owner->mobile ?? '';
        }
        else {
          $newLendSignatureRequestRecord->recipient_name = $record->recipient_name ?? '';
          $newLendSignatureRequestRecord->recipient_email = $record->recipient_email ?? '';
          $newLendSignatureRequestRecord->recipient_mobile = $record->recipient_mobile ?? '';
        }

        $newLendSignatureRequestRecord->status = $record->cgcq_status;
        $newLendSignatureRequestRecord->partner_user_id = $record->last_sent_by_user_id;
        $newLendSignatureRequestRecord->sent_time = $record->cgcq_sent_time;
        $newLendSignatureRequestRecord->signed_time = $record->cgcq_signed_time;
        $newLendSignatureRequestRecord->upload_url = $record->cgcq_upload_url;
        $newLendSignatureRequestRecord->service_envelope_id = $record->service_envelope_id;
        $newLendSignatureRequestRecord->via_email = $record->via_email;
        $newLendSignatureRequestRecord->via_sms = $record->via_sms;

        $newLendSignatureRequestRecord->created = $record->created;
        $newLendSignatureRequestRecord->updated = $record->updated;

        $lendSignatureRequestsTable->save($newLendSignatureRequestRecord);

        $record->migrated = 1;
        $creditGuideQuoteTable->save($record);
      }
      return $this->setJsonResponse([
        'success'=>true,
      ]);

    }

}
