<?php

namespace LeadApisV2\Controller;

use Cake\Log\Log;
use Cake\ORM\TableRegistry;
use Exception;
use Hashids\Hashids;
use App\Lend\LendInternalAuth;
use Cake\Datasource\ConnectionManager;

class LendersController extends AppController
{
    public function initialize()
    {
        parent::initialize();
    }

    public function beforeFilter($event)
    {
        parent::beforeFilter($event);
        $functions = [
            //   'GET'=> [],
            // 'POST' => []
        ];
        $ret = $this->checkAccessToLead($functions);
        if (!empty($ret))
            return $ret;
    }

    public function updateStatus()
    {
        try {
            if (!$this->request->is('post')) {
                throw new Exception("Invalid Request Method.");
            }

            $data = $this->request->getData();

            if (empty($data['sale_ref']) or empty($data['status'])) {
                throw new Exception("Data not provided");
            }
            $hashids = new Hashids('Sales', 6);
            $sale_id = $hashids->decode(substr($data['sale_ref'], 1, 6));
            $sale = $this->getTableLocator()->get('SaleEntity')->get($sale_id, [
                'contain' => [
                    'LeadEntity' => [
                        'PartnerEntity' => [
                            'ManStatusEntity',
                        ],
                    ],
                    'LenderProductEntity' => [
                        'LenderEntity' => [
                            'LenderStatusEntity' => function($q) {
                                return $q->where([
                                    'LenderStatusEntity.active' => true,
                                    'LenderStatusEntity.type' => 4 // Funded
                                ]);
                            },
                        ],
                    ],
                ],
            ]);

            $update_status = strtolower($data['status']);
            $payload = [
                'leadId' => $sale->lead_id,
                'productId' => $sale->product_id,
                'status' => $update_status,
                'sale_ref' => $data['sale_ref'],
            ];

            $man_status_id = null;

            if ($update_status == 'funded') {
                if (!empty($sale->product->lender->lender_statuses)) {
                    $payload['status'] = $sale->product->lender->lender_statuses[0]->combined_status_string;
                }
                if (!empty($sale->lead->partner->man_statuses)) {
                    foreach ($sale->lead->partner->man_statuses as $man_status) {
                        if ($man_status->is_settled) {
                            $man_status_id = $man_status->id;
                            break;
                        }
                    }
                }

                if ($data['lender_type'] === 'Intermediary') {
                    $payload = array_merge($payload, [
                        'funded_amount' => $data['funded_amount'],
                        'funded_date' => $data['funded_date'],
                        'lender_type' => $data['lender_type'],
                        'commission_value' => $data['referes_commission_value'],
                        'intermediary_lender_commision' => $data['interlender_commission_value'],
                        'commission_expected' => ($data['lend_commission_value'] + $data['referes_commission_value']),
                        'inter_lender' => true,
                    ]);
                } else {
                    if ($data['commission_type'] === 'percentage') {
                        $commission = (float)$data['funded_amount'] * ((float)$data['commission_value'] / 100);
                    } else {
                        $commission = (float)$data['commission_value'];
                    }
                    $payload = array_merge($payload, [
                        'funded_amount' => $data['funded_amount'],
                        'funded_date' => $data['funded_date'],
                        'lender_type' => @$data['lender_type'] ?? 'MANUAL',
                        'commission_value' => $commission,
                    ]);
                    if($data['is_intermediary']){
                        $payload['inter_lender'] = true;
                        $payload['commission_expected'] = $commission;
                    }
                }
            }

            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => getenv('DOMAIN_CRM') . '/api/team-lend/manually-add-status',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 60,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_POSTFIELDS => json_encode($payload),
                CURLOPT_HTTPHEADER => [
                    "Content-Type: application/json",
                ],
            ]);
            //execute post
            $result = curl_exec($ch);


            $curlReturnValue = curl_errno($ch);
            $httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $err = curl_error($ch);
            curl_close($ch);

            if ('0' !== "$curlReturnValue") {
                throw new Exception("Failed to communicate with server: " . $err);
            } else {
                if (!in_array($httpcode, ['200', '201', '202']))
                    throw new Exception(!empty($result) ? $result : 'Http Error Code: ' . $httpcode);

                $result = json_decode($result, true);
                if (!$result['success'])
                    throw new Exception($result['error']);
            }

            if (!empty($man_status_id)) {
                $leads_table = TableRegistry::getTableLocator()->get('LeadEntity');
                $lead_entity = $leads_table->get($sale->lead_id);
                $leads_table->patchEntity($lead_entity, ['man_status_id' => $man_status_id]);
                $leads_table->save($lead_entity);
            }

            return $this->setJsonResponse(['success' => true]);
        } catch (Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return $this->setJsonResponse(['success' => false, 'error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
        }
    }



    public function getProductGuide()
    {
        $this->request->allowMethod(['post']);
        $lenderRef = $this->request->getData('lender_ref');


        $hashids = new Hashids('lender', 7);
        $lenderIdArray = $hashids->decode($lenderRef);

        if (!empty($lenderIdArray)) {
            $lenderId = $lenderIdArray[0];
        }

        if (!$lenderId) {
            return $this->setJsonResponse(['success' => false, 'error' => 'Lender ID is required'], 400);
        }
    
        $lendersTable = $this->getTableLocator()->get('LenderEntity');
    
        $lender = $lendersTable->find()
        ->select(['LenderEntity.lender_id', 'LenderEntity.lender_logo', 'LenderEntity.lender_name'])
        ->contain([
            'LenderProductGuidesEntity' => function ($q) {
                return $q->select(['LenderProductGuidesEntity.id','LenderProductGuidesEntity.lender_id', 'LenderProductGuidesEntity.display_name', 
                    'LenderProductGuidesEntity.s3_url', 'LenderProductGuidesEntity.is_visible', 'LenderProductGuidesEntity.created_date','LenderProductGuidesEntity.modified_date','LenderProductGuidesEntity.file_size']);
            }
        ])
        ->where(['LenderEntity.lender_id' => $lenderId])
        ->first();
    
        if (!$lender) {
            return $this->setJsonResponse(['success' => false, 'error' => 'Lender not found'], 404);
        }
    
        return $this->setJsonResponse(['success' => true, 'data' => $lender]);
    }

    public function getProductGuideDocFromS3($doc_id){
        try {
            if (!$doc_id) {
                return $this->setJsonResponse(array('success'=>false, 'message'=>'No upload id supplied'));
            }
            $data =  $this->getTableLocator()->get('LenderProductGuidesEntity')->find()->where(["id" => $doc_id])->first()->toArray();
            if (empty($data) || $data['s3_url'] == null) {
                throw new \Exception("No file found with that upload id");
            }
            $s3_url = $this->_getS3ViewImageRequestByPath($data['s3_url']);
        
            if ($s3_url !== false) {
                return $this->setJsonResponse(array('success'=>true, 'url'=>$s3_url));  
            } else {
                return $this->setJsonResponse(['success' => false]);
            }
        } catch(\Exception $e) {
          Log::error($e->getMessage());
          Log::error($e->getTraceAsString());
          return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()]);
        }

    }

    public function sendUploadToLender()
    {
        try {
            $data = $this->request->getData();

            if (empty($data['leadRef']) || empty($data['upload']) || empty($data['lenderId']) || empty($data['sale'])) {
                throw new Exception("Data not provided");
            }

            // Prepare payload for background job
            $upload = $data['upload'];
            $files_to_send = array();
            $form = array();
            $form['upload_id'] = $upload['upload_id'];
            $form['full_path'] = $upload['full_path'];
            $form['name'] = $upload['name'];
            $form['type'] = $upload['specified'] ?? 'doc';
            $form['source_table'] = 'partner_lead_uploads';
            array_push($files_to_send, $form);

            $lend_internal_auth = new LendInternalAuth;
            $lead_id = $lend_internal_auth->unhashLeadId($data['leadRef']);

            $payload = [
                'lead_id' => $lead_id,
                'lender_id' => $data['lenderId'],
                'files_to_send' => $files_to_send,
                'sale_id' => $data['sale']['sale_id'],
                'sale_ref' => $data['sale']['sale_ref'],
                'product_id' => $data['sale']['product_id'],
            ];

            $leadLenderUploadTrackingTable = TableRegistry::getTableLocator()->get('LeadLenderUploadTracking');
            $trackingData = [
                'upload_id' => $data['upload']['upload_id'],
                'lender_id' => $data['lenderId'],
                'lead_id' => $lead_id,
                'source_table' => 'partner_lead_uploads',
                'created' => date('Y-m-d H:i:s'),
                'status' => 'Pending',
                'notes' => 'Document is pending to be sent to lender',
            ];
            $trackingEntity = $leadLenderUploadTrackingTable->newEntity($trackingData);
            $leadLenderUploadTrackingTable->save($trackingEntity);

            $this->LoadModel('BackgroundJobs')->addBackgroundJob(array(
                'lead_id' => $lead_id,
                'job_type' => 'send_uploads_to_lender',
                'ref_id' => json_encode($payload),
                'job_status' => 0,
                'class_name' => 'SendToLender',
                'function_name' => 'receiveSendUploadJob', // this job will determine how it's sent in STAR
                'created' => date('Y-m-d H:i:s')
            ));
            return $this->setJsonResponse(['success' => true]);        
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()]);
        }
    }
}