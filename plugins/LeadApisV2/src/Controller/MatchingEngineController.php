<?php

namespace LeadApisV2\Controller;

use App\Lend\CurlHelper;
use App\Lend\LeadValidation;
use App\Lend\LendInternalAuth;
use Cake\Core\Configure;
use Cake\Datasource\ConnectionManager;
use Cake\Http\Client;
use Cake\Log\Log;
use Cake\ORM\TableRegistry;
use Exception;

class MatchingEngineController extends AppController
{
    protected $bsaRequests = [];

    public function initialize()
    {
        parent::initialize();
    }

    public function beforeFilter($event)
    {
        parent::beforeFilter($event);
        $functions = [
            //   'GET'=> [],
            'POST' => ['run']
        ];
        $ret = $this->checkAccessToLead($functions);
        if (!empty($ret))
            return $ret;
    }

    public function checkRequiredFields($lead_ref)
    {
        try {
            $user = $this->Auth->user();
            if (empty($user)) {
                $user = $this->Auth->identify();
            }

            $lend_internal_auth = new LendInternalAuth;
            $lead_id = $lend_internal_auth->unhashLeadId($lead_ref);

            // Get Lead detail:
            TableRegistry::getTableLocator()->remove('LeadEntity');
            $lead_table = TableRegistry::getTableLocator()->get('LeadEntity', [
                'connection' => ConnectionManager::get('reader_db')
            ]);
            $associated = [
                'Owners' => [
                    'OwnerAllEmployments',
                    'OwnerAllAddresses',
                    'CurrentAddress',
                    'LeadCreditScoreEntity',
                    'LeadOwnerIncomeEntity',
                    'LeadOwnerExpenseEntity',
                    'LeadOwnerFinancesEntity',
                ],
                'LeadAssetFinanceEntity',
                'LeadAbnLookupEntity',
                'LeadPricingEntity',
                'LeadCreditScoreEntity',
                'PartnerEntity',
                'PartnerProductTypeEntity',
                'AllAddresses',
            ];
            $lead = $lead_table->get($lead_id, [
                'contain' => $associated
            ]);

            // validation check:
            $lead = $lead_table->formatPayload($lead);
            $errors = $this->_leadValidationToSendLead($lead);
            if (!empty($errors)) {
                throw new Exception(json_encode($errors), 200);
            }
            return $this->setJsonResponse(['success' => true]);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            $array_errors = json_decode($e->getMessage(), true);
            if (is_array($array_errors)) {
                return $this->setJsonResponse(['success' => false, 'error' => 'Validation Failed', 'validation_errors' => $array_errors], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
            } else {
                return $this->setJsonResponse(['success' => false, 'error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
            }
        }
    }


    private function _save($data)
    {
        try {
            $user = $this->Auth->user();
            if (empty($user)) {
                $user = $this->Auth->identify();
            }

            if (empty($data['lead_ref'])) {
                throw new Exception("Missing params.", 400);
            }

            $lend_internal_auth = new LendInternalAuth;
            $lead_id = $lend_internal_auth->unhashLeadId($data['lead_ref']);

            // Get Lead detail:
            $lead_table = $this->getTableLocator()->get('LeadEntity');
            $associated = [
                'Owners' => [
                    'OwnerAllEmployments',
                    'OwnerAllAddresses',
                    'CurrentAddress',
                    'LeadCreditScoreEntity',
                    'LeadOwnerIncomeEntity',
                    'LeadOwnerExpenseEntity',
                    'LeadOwnerFinancesEntity',
                ],
                'LeadAssetFinanceEntity',
                'LeadAbnLookupEntity',
                'LeadPricingEntity',
                'LeadCreditScoreEntity',
                'PartnerEntity' => [
                    'PartnerLenderPriorityEntity',
                    'PartnerLenderExcludeEntity',
                ],
                'PartnerProductTypeEntity',
                'AllAddresses',
                'LeadPreferredLenderEntity',
                'LeadExcludedLenderEntity',
                'LeadAssociatedDataEntity',
                'ConPageStatusEntity',
                'FrmPurposeEntity',
            ];
            $lead = $lead_table->get($lead_id, [
                'contain' => $associated
            ]);

            if (!empty($lead->con_page_status)) {
                $patch_con_page_status = [];
                if ($lead->con_page_status->con_prelim_status === 'complete') {
                    $patch_con_page_status['con_prelim_status'] = 'review required';
                }
                if (in_array($lead->con_page_status->con_credit_proposal_status, ['sent', 'overridden'])) {
                    $patch_con_page_status['con_credit_proposal_status'] = 'review required';
                }
                if (!empty($patch_con_page_status)) {
                    $lead_table->patchEntity($lead, [
                        'con_page_status' => $patch_con_page_status,
                    ]);
                    $lead_table->save($lead);
                }
            }

            // Update lead_pricing:
            $lead_pricing_data = $this->_formatLeadPricingData($data, $lead_id);
            // get BSA
            $bsa = $this->_getBSA($lead->account_id, $lead_id);
            // get avg_credit & avg_debit
            if (!empty($bsa)) {
                $bss_responsse = $this->_getLeadSuplusFromBankStatements($lead->account_id, $lead_id);
                $lead_pricing_data['avg_credit'] = @$bss_responsse['avg_credit'];
                $lead_pricing_data['avg_debit'] = isset($bss_responsse['avg_debit']) ? abs($bss_responsse['avg_debit']) : 0; // convert minus to plus //
            }
            $patchLead = [
                'loan_term_requested_months' => $data['loan_term'],
                'pricing' => $lead_pricing_data,
            ];
            // update balloon:
            if (!empty($data['asset_balloon'])) {
                $patchLead['asset_finance'] = [
                    'balloon_percent_or_value' => @$data['balloon_percent_or_value'],
                    'asset_balloon' => @$data['asset_balloon'],
                ];
            }
            if ($data['lead_preferred_lender']) {
                $patchLead['lead_preferred_lender'] = $data['lead_preferred_lender'];
                $lead_preferred_lender_table = $this->getTableLocator()->get('LeadPreferredLenderEntity');
                $lead_preferred_lender_table->deleteAll(['lead_id' => $lead_id]);
            }
            $pocKey = 0;
            if ($lead->owners_all) {
                foreach ($lead->owners_all as $key => $o) {
                    if ($o->point_of_contact) {
                        $pocKey = $key;
                    }
                }
                $patchLead['owners_all'][$pocKey] = [
                    'owner_id' => $lead->owners_all[$pocKey]->owner_id,
                    'home_owner' => $data['backed_with_property']
                ];
            }
            // Update lead:
            $lead_table->patchEntity($lead, $patchLead, [
                'associated' => $associated,
            ]);
            $lead_table->save($lead);

            return $lead;
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            throw new Exception($e->getMessage(), $e->getCode());
        }
    }


    public function save()
    {
        try {
            $data = $this->request->getData();
            if (empty($data['lead_ref'])) {
                throw new Exception("Missing params.", 400);
            }
            $lead = $this->_save($data);

            return $this->setJsonResponse(['lead_ref' => $lead->lead_ref]);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            $array_errors = json_decode($e->getMessage(), true);
            if (is_array($array_errors)) {
                return $this->setJsonResponse(['error' => 'Validation Failed', 'validation_errors' => $array_errors], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
            } else {
                return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
            }
        }
    }

    public function run()
    {
        try {
            $user = $this->Auth->user();
            if (empty($user)) {
                $user = $this->Auth->identify();
            }

            $data = $this->request->getData();
            if (empty($data['lead_ref'])) {
                throw new Exception("Missing params.", 400);
            }

            $lead = $this->_save($data);

            $lead_table = $this->getTableLocator()->get('LeadEntity');
            $associated = [
                'Owners' => [
                    'OwnerAllEmployments',
                    'OwnerAllAddresses',
                    'CurrentAddress',
                    'LeadCreditScoreEntity',
                    'LeadOwnerIncomeEntity',
                    'LeadOwnerExpenseEntity',
                    'LeadOwnerFinancesEntity',
                ],
                'LeadAssetsEntity' => [
                    'ConAssetShareEntity'
                ],
                'LeadLiabilitiesEntity',
                'LeadAssetFinanceEntity',
                'LeadAbnLookupEntity',
                'LeadPricingEntity',
                'LeadCreditScoreEntity',
                'PartnerEntity' => [
                    'PartnerLenderPriorityEntity',
                    'PartnerLenderExcludeEntity',
                ],
                'PartnerProductTypeEntity',
                'AllAddresses',
                'LeadPreferredLenderEntity',
                'LeadExcludedLenderEntity',
                'LeadAssociatedDataEntity',
                'ConPageStatusEntity',
                'FrmPurposeEntity',
                'LatestCommission' => [
                    'DealsEntity' => [
                        'SaleEntity' => [
                            'LenderProductEntity',
                        ]
                    ],
                ],
            ];

            // Need to get the lead details again, because some of the data are rely on ORM override from one to the other:
            $lead = $lead_table->get($lead->lead_id, [
                'contain' => $associated
            ]);

            // Set user's preferences > preferred_lenders:
            $preferences = [];
            $preferred_lenders = [];
            if (!empty($lead['lead_preferred_lender'])) {
                foreach ($lead['lead_preferred_lender'] as $lead_preferred_lender) {
                    $preferred_lenders[] = [
                        'lender_id' => $lead_preferred_lender->lender_id,
                        'priority' => ($lead_preferred_lender->order * 0.5),
                    ];
                }
            }
            if (!empty($lead['partner']['partner_lender_priority'])) {
                foreach ($lead['partner']['partner_lender_priority'] as $partner_preferred_lender) {
                    $preferred_lenders[] = [
                        'lender_id' => $partner_preferred_lender->lender_id,
                        'priority' => $partner_preferred_lender->priority,
                    ];
                }
            }
            if (!empty($preferred_lenders)) {
                $preferences['preferred_lenders'] = $preferred_lenders;
            }

            // Set user's preferences > excluded_lenders:
            $excluded_lenders = [];
            if (!empty($lead['lead_excluded_lender'])) {
                foreach ($lead['lead_excluded_lender'] as $lead_excluded_lender) {
                    $excluded_lenders[] = $lead_excluded_lender->lender_id;
                }
            }
            if (!empty($lead['partner']['partner_lender_excludes'])) {
                foreach ($lead['partner']['partner_lender_excludes'] as $partner_excluded_lender) {
                    $excluded_lenders[] = $partner_excluded_lender->lender_id;
                }
            }
            if (!empty($excluded_lenders)) {
                $preferences['excluded_lenders'] = $excluded_lenders;
            }

            if (!empty($preferences)) {
                $lead['preferences'] = $preferences;
            }

            $type = $lead->lead_type === 'consumer' ? 'Consumer' : 'Commercial';
            if ($type === 'Consumer' && !empty($lead->purpose->is_car_loan)) {
                $type .= '_Asset';
            } elseif ($type === 'Commercial' && !empty($lead->partner_product_type->is_commercial_asset)) { // no matter, application_type is e2e or basic, all asset product types consider as COMMERCIAL_ASSET
                $type .= '_Asset';
            }

            // generate payload:
            $payload = [
                'requested' => [
                    'from' => 'Partners',
                    'partner_id' => $user['partner_id'],
                    'partner_user_id' => $user['partner_user_id'],
                ],
                'payload' => $lead_table->formatPayload($lead),
                'is_quote' => false,
                'type' => $type,
                'country' => strtoupper(getenv('REGION', true)),
            ];
            $bsa = $this->_getBSA($lead->account_id, $lead->lead_id);
            if (!empty($bsa)) {
                $payload['payload']['bank_statements_analysis'] = $bsa;
            }
            //insert consumer is_bid setting
            $conPartnerUserSettings = TableRegistry::getTableLocator()
                ->get('ConPartnerUserSettingsEntity')
                ->find()
                ->where(['partner_user_id' => $user['partner_user_id']])
                ->first();
            $isBid = $conPartnerUserSettings? $conPartnerUserSettings['is_bid']: 0;
            $payload['payload']['con_partner_user_settings']['is_bid'] = $isBid;

            $payload['payload']['has_bsa'] = false;
            if (!empty($bsa)) {
                $payload['payload']['has_bsa'] = true;
            }
            try {
              $debugData = [
                'has_bsa' => $payload['payload']['has_bsa'],
                'lead_id' => $lead->lead_id,
                'partner_account_id' => $lead->account_id,
              ];
              Log::write('debug', 'Has BSA: ' . json_encode($debugData));
            }
            catch (Exception $exception) {

            }

            // validation check:
            // $errors = $this->_leadValidationToSendLead($payload['payload']);
            // if (!empty($errors)) {
            //     throw new Exception(json_encode($errors), 422);
            // }
            // Save request record before request:
            $lender_match_requests_table = $this->getTableLocator()->get('LenderMatchRequestEntity');
            $lender_match_request = $lender_match_requests_table->newEntity([
                'partner_id' => $user['partner_id'],
                'partner_user_id' => $user['partner_user_id'],
                'product_type_id' => $lead->product_type_id ?? null,
                'lead_id' => $lead->lead_id,
                'payload' => $payload['payload'],
                'filter' => 'all',
            ]);
            $lender_match_requests_table->save($lender_match_request);

            // Request matching engine service
            $http = new Client();
            $header = [
                'type' => 'json',
                'headers' => ['x-api-key' => getenv('MATCHING_ENGINE_KEY')]
            ];
            $response = $http->post(getenv('DOMAIN_MATCHING_ENGINE') . "/run", json_encode($payload), $header);
            $match_result = $response->getJson();

            $lender_match_requests_table->patchEntity($lender_match_request, [
                'match_ref' => @$match_result['data']['ref']
            ]);
            $lender_match_requests_table->save($lender_match_request);

            return $this->setJsonResponse($match_result);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            $array_errors = json_decode($e->getMessage(), true);
            if (is_array($array_errors)) {
                return $this->setJsonResponse(['error' => 'Validation Failed', 'validation_errors' => $array_errors], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
            } else {
                return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
            }
        }
    }

    private function _formatLeadPricingData($data, $lead_id)
    {
        try {
            return [
                'lead_id' => $lead_id,
                'rate_discount' => @$data['rate_discount'],
                'commission' => @$data['commission'],
                'commission_type' => @$data['commission_unit'] === 'dollar' ? '$' : '%',
                'origination_fee' => @$data['origination_fee'],
                'include_fees_requested' => @$data['include_fees_requested'],
                'borrower_bs_period' => @$data['borrower_bs_period'],
                'borrower_ato_portal' => @$data['borrower_ato_portal'],
                'borrower_bas' => @$data['borrower_bas'],
                'borrower_comparable_lender' => @$data['borrower_comparable_lender'],
                'borrower_comparable_lender_id' => @$data['borrower_comparable_lender_id'],
                'borrower_comparable_lender_when' => @$data['borrower_comparable_lender_when'],
                'borrower_company_financials' => @$data['borrower_company_financials'],
                'borrower_tax_returns' => @$data['borrower_tax_returns'],
                'borrower_p_and_l' => @$data['borrower_p_and_l'],
                'contract_type' => @$data['contract_type'],
                'payment_type' => @$data['payment_type'],
                'backed_with_property' => @$data['backed_with_property'],
                'deposit_over_twenty_percent' => @$data['deposit_over_twenty_percent'],
                'application_type' => @$data['application_type'],
            ];
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return false;
        }
    }

    private function _getBSA($partner_account_id, $lead_id = null)
    {
        if(!empty($this->bsaRequests[$partner_account_id]) && !empty($this->bsaRequests[$partner_account_id][$lead_id])){
            return $this->bsaRequests[$partner_account_id][$lead_id];
        }
        $bank_statements_analysis = $this->getTableLocator()->get('BankStatementsAnalysis')->getBSAnalysis(array('partner_account_id' => $partner_account_id, 'lead_id' => $lead_id));
        if ($bank_statements_analysis) {
            $bank_statements_analysis = $bank_statements_analysis[0];
        }
        $this->bsaRequests[$partner_account_id][$lead_id] = $bank_statements_analysis;
        return $bank_statements_analysis;
    }

    private function _getLeadSuplusFromBankStatements($partner_account_id, $lead_id = null)
    {
        $accountLevelBSAccountData = ['partnerAccountId' => $partner_account_id];
        if (!empty($lead_id)) {
            $accountLevelBSAccountData['leadId'] = $lead_id;
        }
        $curl = new CurlHelper(getenv('DOMAIN_BSS') . "/get-analysis-by-partner-account-id");
        $lendconfig = Configure::read('Lend');
        $bs_analysis = $curl->post($accountLevelBSAccountData, true, ["x-api-key" => $lendconfig['bs_service_auth_key']]);

        if ($bs_analysis['success']) {
            foreach ($bs_analysis['data'] as $bs_analysis_record) {
                $account_ids[] = $bs_analysis_record['account_id'];
            }
        }

        if (!empty($account_ids)) {
            $accountIDSpayload = ['account_ids' => $account_ids];
            $curl          = new CurlHelper(getenv('DOMAIN_BSS') . "/get-avg-surplus-by-bank-account-ids");
            $avg_surplus = $curl->post($accountIDSpayload, true, ["x-api-key" => $lendconfig['bs_service_auth_key']]);

            if ($avg_surplus['success']) {
                return ($avg_surplus['data']);
            }
        }

        return null;
    }

    private function _leadValidationToSendLead($data)
    {
        Log::info('=========_leadValidationToSendLead() ==========');
        Log::info(json_encode($data));

        $data = json_decode(json_encode($data), true);
        // Check validation
        $leadValidation = new LeadValidation;
        $leadValidation->check_all_owners = true;

        //fields below are always required
        $requiredFields['lead_owner'] = ['first_name', 'last_name', 'email'];

        $requiredFields['lead'] = ['purpose_id', 'amount_requested', 'company_registration_date', 'sales_monthly', 'industry_id', 'organisation_name', 'product_type_id'];
        $lead_asset_finance = $data['lead_asset_finance'];
        $data['lead'] = $data['leads'];
        $data['lead_owner'] = $data['lead_owners'];
        $data['product_type'] = $data['leads']['partner_product_type'];
        $data['abnlookup'] = $data['abn_lookup'];

        unset($data['leads']);
        unset($data['lead_owners']);
        unset($data['abn_lookup']);

        if ($data['lead']['product_type_id'] == 10) {  //if Equipment finance
            if (empty($lead_asset_finance) or empty($lead_asset_finance['contract_type'])) {
                //we need to validate ABN
                if (getenv('REGION', true) === 'au') {
                    $requiredFields['abnlookup'] = array();
                }
                $requiredFields['lead']['equipment_id'] = 'equipment_id';
                $requiredFields['lead']['equipment_details'] = 'equipment_details';
                //we need the eqipment fields to be filled
                switch ($data['lead']['equipment_id']) {
                    case '':
                        unset($data['lead']['equipment_condition']);
                        break;
                    default:
                        $requiredFields['lead']['equipment_condition'] = 'equipment_condition';
                        break;
                }
                if (!empty($data['lead']['equipment_found'])) {
                    $requiredFields['lead']['equipment_source'] = 'equipment_source';
                }
            }
        }

        if (!empty($data['product_type']['sub_product']) && $data['product_type']['sub_product'] == 10) {
            array_splice($requiredFields['lead'], array_search('sales_monthly', $requiredFields['lead']), 1);
        }

        if (!empty($lead_asset_finance) && !empty($lead_asset_finance['contract_type'])) {
            if (!empty($data['lead_owner'])) {
                foreach ($data['lead_owner'] as &$owner) {
                    $owner['lead_owner_addresses'] = $owner['addresses'];
                    $owner['lead_owner_employments'] = $owner['employments'];
                    unset($owner['addresses']);
                    unset($owner['employments']);
                }
            }
            $requiredFields['lead'][] = 'r_address';
            $requiredFields['lead'][] = 'r_suburb';
            $requiredFields['lead'][] = 'r_state';
            $requiredFields['lead'][] = 'r_postcode';

            // $requiredFields['abnlookup'] = ['entity_type_desc'];
            $requiredFields['lead_owner'][] = 'title';
            $requiredFields['lead_owner'][] = 'dob';
            $requiredFields['lead_owner'][] = 'credit_history';
            $requiredFields['lead_owner'][] = 'lead_owner_addresses';

            $requiredFields['lead_asset_finance'][] = 'equipment_id';
            $requiredFields['lead_asset_finance'][] = 'asset_description';
            $requiredFields['lead_asset_finance'][] = 'year';
            $requiredFields['lead_asset_finance'][] = 'make';
            $requiredFields['lead_asset_finance'][] = 'condition';
            $requiredFields['lead_asset_finance'][] = 'sale_type';

            if (!empty($lead_asset_finance['asset_balloon']) && (int)$lead_asset_finance['asset_balloon'] > 1) {
                $requiredFields['lead_asset_finance'][] = 'balloon_reason';
                $requiredFields['lead_asset_finance'][] = 'intention_after_loan';
                if (!empty($lead_asset_finance['balloon_reason']) && strtolower($lead_asset_finance['balloon_reason']) === 'other') {
                    $requiredFields['lead_asset_finance'][] = 'balloon_reason_explanation';
                }
            }

            $requiredFields['lead_addresses'] = [
                'address',
                'suburb',
                'state',
                'postcode',
            ];

            $requiredFields['lead'][] = 'loan_term_requested_months';
        }

        // check Property Value and Mortgage Balance
        if (!empty($data['lead_owner']['home_owner']) and !empty($data['lead']['product_type_id']) and ($data['lead']['product_type_id'] == '2' or $data['lead']['product_type_id'] == '7' or $data['product_type']['sub_product'] == '7')) {
            $requiredFields['lead_owner'][] = 'estimated_value';
            $requiredFields['lead_owner'][] = 'remaining_debt';
        }

        //mobile or phone must not be empty
        if (!empty($data['lead_owner']['mobile']))
            $requiredFields['lead_owner'][] = 'mobile';
        else if (!empty($data['lead_owner']['phone']))
            $requiredFields['lead_owner'][] = 'phone';
        else
            $requiredFields['lead_owner'][] = 'mobile';
        $leadValidation->setRequiredFields($requiredFields);
        Log::info('====($requiredFields');
        Log::info(json_encode($requiredFields));

        $errors = $leadValidation->validate($data);

        return $errors;
    }

    public function addOffPanelLender()
    {
        try {
            $data = $this->request->getData();
            $lend_internal_auth = new LendInternalAuth;
            $lead_id = $lend_internal_auth->unhashLeadId($data['lead_ref']);

            $user = $this->Auth->user();
            if (empty($user)) {
                $user = $this->Auth->identify();
            }

            // off panel lender name:
            $lender_name = $this->_checkOffPanelLender($data['lender_name'], $lead_id);

            // get match_ref
            $lead_table = $this->getTableLocator()->get('LeadEntity');
            $associated = [
                'PartnerProductTypeEntity',
                'LenderMatchRequestEntity',
            ];
            $lead = $lead_table->get($lead_id, [
                'contain' => $associated
            ]);

            if (!empty($lead->lender_match_requests)) {
                $match_ref = $lead->lender_match_requests[0]->match_ref;
            }
            // generate payload:
            $type = !empty($lead->partner_product_type->is_consumer)
                ? 'Consumer'
                : (
                    !empty($lead->partner_product_type->is_commercial_asset)
                    ? 'Commercial_Asset'
                    : 'Commercial'
                );
            $payload = [
                'requested' => [
                    'from' => 'Partners',
                    'partner_id' => $user['partner_id'],
                    'partner_user_id' => $user['partner_user_id'],
                ],
                'payload' => [
                    'match_ref' => @$match_ref,
                    'tier_id' => @$data['tier_id'] ?? 99,
                    'product_id' => @$data['product_id'] ?? Configure::read('Lend.OFF_PANEL_LENDER_PRODUCT_ID'),
                    'lender_name' => @ucwords(strtolower($lender_name ?? $data['lender_name'])),
                    'lender_prod_name' => @ucwords(strtolower($data['product_name'])),
                    'base_rate' => @$data['base_rate'],
                    'customer_rate' => @$data['customer_rate'],
                    'apr' => @$data['apr'],
                    'adjusted_rate' => @$data['adjusted_rate'],
                    'origination_fee' => @$data['origination_fee'],
                    'commission' => @$data['commission'],
                    'commission_type' => @$data['commission_type'],
                    'financed_amount' => @$data['financed_amount'],
                    'repayment_amt' => @$data['repayment_amt'],
                    'repayment_with_brokerage' => @$data['repayment_with_brokerage'],
                    'repayment_freq' => @$data['repayment_freq'],
                    'loan_amt' => $lead->amount_requested ?? 0,
                    'term_months' => $lead->loan_term_requested_months ?? 0,
                ],
                'is_quote' => false,
                'type' => $type,
            ];
            if (!empty($data['lender_estab_fee'])) {
                $payload['payload']['lender_match_result_fees'][] = [
                    'category_id' => 1,
                    'type_id' => 1,
                    'strategy_id' => 1,
                    'fee' => $data['lender_estab_fee'],
                ];
            }
            if (!empty($data['lender_monthly_fee'])) {
                $payload['payload']['lender_match_result_fees'][] = [
                    'category_id' => 2,
                    'type_id' => 3,
                    'strategy_id' => 2,
                    'fee' => $data['lender_monthly_fee'],
                ];
            }

            // Request matching engine service
            $http = new Client();
            $header = [
                'type' => 'json',
                'headers' => ['x-api-key' => getenv('MATCHING_ENGINE_KEY')]
            ];
            $response = $http->post(getenv('DOMAIN_MATCHING_ENGINE') . "/add-off-panel-lender", json_encode($payload), $header);
            $status = $response->getStatusCode();
            $match_result = $response->getJson();
            if ($status != '200') {
                throw new Exception($match_result['error']['message']);
            }

            // if there was no match requests, save this one
            if (empty($lead->lender_match_requests)) {
                $lender_match_requests_table = $this->getTableLocator()->get('LenderMatchRequestEntity');
                $lender_match_request = $lender_match_requests_table->newEntity([
                    'partner_id' => $user['partner_id'],
                    'partner_user_id' => $user['partner_user_id'],
                    'product_type_id' => @$lead->product_type_id,
                    'lead_id' => $lead_id,
                    'payload' => $payload,
                    'match_ref' => @$match_result['data']['match_ref']
                ]);
                $lender_match_requests_table->save($lender_match_request);
            }

            // Add new off panel lender:
            if (empty($lender_name)) {
                $admin_jobs_table = TableRegistry::getTableLocator()->get('AdminJobEntityTeamDb');
                $admin_job = $admin_jobs_table->newEntity([
                    'lead_id' => $lead_id,
                    'job_type' => 'off_panel_lender',
                    'job_status' => 'pending',
                    'meta' => [
                        'lender_name' => $data['lender_name'],
                        'match_result_ref' => @$match_result['data']['match_result_ref']
                    ],
                ]);
                $admin_jobs_table->save($admin_job);

                $buttonData = [
                    'Team Lend' => [
                        'url' => getenv('DOMAIN_TEAM') . '/lenders/off-panel-lenders',
                    ]
                ];

                // post to Slack
                TableRegistry::getTableLocator()->get('App')->postToSlack("Review New Off Panel Lender Name *" . $data['lender_name'] . "*", "off_panel_lenders", $buttonData);
            }

            return $this->setJsonResponse([
                'match_ref' => @$match_result['data']['match_ref'],
                'match_result_ref' => @$match_result['data']['match_result_ref']
            ]);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
        }
    }

    private function _checkOffPanelLender($lender_name)
    {
        $lender_name = trim($lender_name);
        $off_panel_lender_table = TableRegistry::getTableLocator()->get('OffPanelLenderEntity');
        $off_panel_lenders = $off_panel_lender_table->find('all')->where(['active' => true])->toArray();

        // Find matched lender_name:
        $filtered_lenders = array_filter($off_panel_lenders, function ($lender) use ($lender_name) {
            return strtolower(trim($lender['lender_name'])) === strtolower($lender_name);
        });
        if (!empty($filtered_lenders)) {
            $filtered_lenders = array_values($filtered_lenders);
            return $filtered_lenders[0]['lender_name'];
        }

        // Find matched thesaurus:
        $filtered_lenders = array_filter($off_panel_lenders, function ($lender) use ($lender_name) {
            if (empty($lender['thesaurus'])) {
                return false;
            }
            $thesaurus = explode(',', $lender['thesaurus']);
            $thesaurus = array_map('trim', array_map('strtolower', $thesaurus));
            return in_array(strtolower($lender_name), $thesaurus);
        });
        if (!empty($filtered_lenders)) {
            $filtered_lenders = array_values($filtered_lenders);
            return $filtered_lenders[0]['lender_name'];
        }

        // return null
        return null;
    }
}
