<?php

namespace LeadApisV2\Controller;

use Cake\ORM\TableRegistry;
use Cake\Log\Log;
use Hashids\Hashids;
use Cake\Http\Client;
use App\Lend\LendInternalAuth;
use Cake\Datasource\ConnectionManager;
use Cake\Utility\Security;
use Firebase\JWT\JWT;

class PartnerUserInfoController extends AppController
{
  public function initialize()
  {
    parent::initialize();
    $this->loadComponent('LeadApisV2.PrelimTemplate');
  }

  public function getPartnerUserId()
  {
    try{
      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }

      if (!$user) {
          throw new \Exception("user not logged in.");
      }
      return $this->setJsonResponse(["success"=>true,"data"=> $user['partner_user_id']]);
    }
    catch(\Exception $e){
      Log::write('error', $e->getMessage());
      return $this->setJsonResponse(["success"=>false,"data"=>$e->getMessage()]);
    }
  }

  public function getPartnerUserInfo()
  {
      try{
        $user = $this->Auth->user();
        if (empty($user)) {
          $user = $this->Auth->identify();
        }

        if (!$user) {
            throw new \Exception("user not logged in.");
        }
        TableRegistry::getTableLocator()->remove('PartnerUserEntity');
        $user_settings = TableRegistry::getTableLocator()->get('PartnerUserEntity', [
          'connection' => ConnectionManager::get('reader_db')
          ])->get($user['partner_user_id'], [
            'contain' => [
              'PartnerEntity' => function ($q) {
                return $q->autoFields(true);
              },
              'PartnerEntity.PartnerTagEntity' => function ($q) {
                return $q->autoFields(true);
              },
              'PartnerEntity.PartnerFeatureFlagEntity' => function ($q) {
                return $q->autoFields(true);
              },
              'PartnerEntity.TmpPartnerBillingEntity' => [
                'fields' => [
                  'card_type',
                  'card_number_ending',
                ]
              ],
              'PartnerEntity.PartnerCustomPrivacyFormsEntity' => function ($q) {
                return $q->where(['status !=' => 'Deleted']);
              },
              'PartnerEntity.LenderEntity' => [
                'fields' => [
                  'lender_id',
                  'partner_id',
                  'lender_name',
                  'shorthand',
                  'lender_logo',
                  'intermediary_lender_id',
                ],
              ],
              'PartnerEntity.IntermediaryLenderEntity' => [
                'fields' => [
                  'lender_id',
                  'lender_name',
                  'shorthand',
                  'lender_logo',
                  'contact_email',
                  'contact_number',
                  'contact_name',
                ]
              ],
              'PartnerEntity.PartnerExtraCredentialsEntity' => [
                'fields' => [
                  'partner_id',
                  'type',
                ],
              ],
              'ConPartnerUserSettingsEntity' => function ($q) {
                return $q->autoFields(true);
              },
              'ConPartnerUserFeeDefaultEntity' => function ($q) {
                return $q->autoFields(true);
              },
            ],
            'fields' => [
              "partner_user_id",
              "partner_id",
              "partner_user_ref",
              "created",
              "email",
              "email_verified",
              "account_type",
              "level",
              "title",
              "name",
              "dob",
              "mobile",
              "phone",
              "business_activities",
              "acl_number",
              "acr_number",
              "role",
              "contact_title",
              "web_affiliate",
              "point_of_contact",
              "account_admin",
              "timezone",
              "sms_available_time_start",
              "sms_available_time_end",
              "active",
              "notification_recipient",
              "settings_complete",
              "referrer_code",
              "call_me_prompt",
              "call_me_prompt_agreed",
              "access_all_leads",
              "access_lead_list_export",
              "access_attempt_count",
              "accreditation_list",
              "indus_assoc_number",
              "indus_assoc_name",
              "indus_assoc_member",
              "is_accredited",
              "q_investigation",
              "q_membership",
              "q_bankrupt",
              "q_offence",
              "q_otherName",
              "signed_accreditation_S3",
              "viewed_whats_new",
              "show_full_app_consent",
              "acc_requested",
              "viewed_new_accred",
              "quotes_calculated",
              "quotes_selected",
              "notes",
              "access_to_call_queue",
              "states_list",
              "product_type_id_list",
              "position",
              "permission_edit_assignee",
              "lend_signature_user_ref",
              "kanban_colour",
              "con_bypass_req_field_off_panel_match",
              "con_submit_offpanel_w_o_req_field",
              "con_allow_bypass_credit_prop_if_acl"
            ]
        ]);

        $partnerUserId = $user['partner_user_id'];


        $partnerId = $user['partner_id']; 
        $partnerUserTable = TableRegistry::getTableLocator()->get('PartnerUserEntity', [
            'connection' => ConnectionManager::get('reader_db')
        ]);
        $accountAdminUser = $partnerUserTable->find()
            ->where([
                'partner_id' => $partnerId,
                'account_admin' => true 
            ])
            ->order(['partner_user_id' => 'ASC']) 
            ->first(); 
        
        $user_settings['partner']['has_sms_credentials'] = TableRegistry::getTableLocator()->get('PartnerExtraCredentialsEntity')
          ->credentialsExist($this->Auth->user('partner_id'), 'burstSms');
        
        $user_settings['current_user_is_admin'] = false;
        if($accountAdminUser['partner_user_id'] == $partnerUserId){
            $user_settings['current_user_is_admin'] = true;
        }
        
        $user_settings['current_users_admin_user'] = array("name" => $accountAdminUser['name'], "partner_user_id" => $accountAdminUser['partner_user_id'], "is_admin" => $user_settings['current_user_is_admin']);

        $prelimTemplatesQuestions = $this->PrelimTemplate->getPrelimTemplatesQuestions($user['partner_user_id']);
        $user_settings['prelim_templates_questions'] = $prelimTemplatesQuestions;


        $partnerNoticeDismissedTable = TableRegistry::getTableLocator()->get('PartnerNoticeDismissedEntity', [
          'connection' => ConnectionManager::get('reader_db')
        ]);

        $latestDismissedNotice = $partnerNoticeDismissedTable->find()
          ->contain(['PartnerNoticesEntity']) 
          ->where(['PartnerNoticeDismissedEntity.partner_user_id' => $user['partner_user_id']])
          ->order(['PartnerNoticeDismissedEntity.partner_notice_dismissed_id' => 'DESC'])
          ->first();


        if ($latestDismissedNotice) {
          $user_settings['last_dismissed_notice_ref'] = $latestDismissedNotice->partner_notices_entity->notice_ref;
        } else {
          $user_settings['last_dismissed_notice_ref'] = null;
        }

        return $this->setJsonResponse(["success"=>true,"data"=>$user_settings]);
      }
      catch(\Exception $e){
        Log::write('error', $e->getMessage());
        return $this->setJsonResponse(["success"=>false,"data"=>$e->getMessage()]);
      }
  }

  public function update()
  {
    try {
      if (!$this->request->is('post')) {
        throw new \Exception("only POST request is available.", 405);
      }

      $db = ConnectionManager::get('default');
      $collection = $db->getSchemaCollection();
      $con_partner_user_settings_schema = $collection->describe('con_partner_user_settings');
      $schema = array_flip($con_partner_user_settings_schema->columns());
      $partner_user_info_data = array_intersect_key($this->request->getData(), $schema);
      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }
      $partner_user_info_data['partner_id'] = $user['partner_id'];
      $partner_user_info_data['partner_user_id'] = $user['partner_user_id'];
      $partner_user_info_table = TableRegistry::getTableLocator()->get('ConPartnerUserSettingsEntity');
      $partner_user_info = $partner_user_info_table->findByPartnerUserId($user['partner_user_id'])->first();
      if($partner_user_info){
        $partner_user_info_entity = $partner_user_info_table->patchEntity($partner_user_info, $partner_user_info_data);
      }else{
        $partner_user_info_entity = $partner_user_info_table->newEntity($partner_user_info_data);
      }
      $result = $partner_user_info_table->save($partner_user_info_entity);
      if (!$result) {
        throw new \Exception(json_encode($partner_user_info->getErrors()));
      }
      return $this->setJsonResponse($partner_user_info_entity);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600? $e->getCode() : 400);
    }
  }

  public function copyUserInfo()
  {
    $ConPartnerUserSettingsEntityTable   = TableRegistry::getTableLocator()->get('ConPartnerUserSettingsEntity');
    $ConPartnerUserFeeDefaultEntityTable = TableRegistry::getTableLocator()->get('ConPartnerUserFeeDefaultEntity');

    try {
      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }

      if (empty($user)) {
        throw new \Exception("Unauthorized access.", 401);
      }
      if (!$this->request->is('post')) {
        throw new \Exception("Only POST request is allowed.", 405);
      }

      $sourcePartnerUserId = $this->request->getData('partner_user_id');
      $currentPartnerUserId = $user['partner_user_id'];

      if ($sourcePartnerUserId == $currentPartnerUserId) {
        throw new \Exception("Source and destination users are the same.", 400);
      }
      $sourceUserInfo = TableRegistry::getTableLocator()->get('PartnerUserEntity')
      ->find()
        ->where(['PartnerUserEntity.partner_user_id' => $sourcePartnerUserId])
        ->contain(['ConPartnerUserSettingsEntity', 'ConPartnerUserFeeDefaultEntity'])
        ->firstOrFail();

      $sourceUserSettings = $sourceUserInfo->user_settings;
      $sourceFeeDefaults  = $sourceUserInfo->con_partner_user_fee_default;

      if (empty($sourceUserSettings)) {
        throw new \Exception("Source user settings not found.", 404);
      }

      $currentUserSettings = $ConPartnerUserSettingsEntityTable->find()
        ->where(['partner_user_id' => $currentPartnerUserId])
        ->first();

      if (!$currentUserSettings) {
        $currentUserSettings = $ConPartnerUserSettingsEntityTable->newEntity([
          'partner_id' => $user['partner_id'],
          'partner_user_id' => $user['partner_user_id'],
        ]);
      }
      $fieldsToCopy = [
        'abn', 'entity_name', 'bus_name', 'address',
        'email', 'phone', 'cg_upfront_comms_range_min', 'cg_upfront_comms_range_max',
        'cg_trail_comms_range_min', 'cg_trail_comms_range_max', 'cg_vb_comms_range_min',
        'cg_vb_comms_range_max', 'cg_complain_phone', 'cg_complain_email',
        'cg_complaint_address', 'external_acl_name',
        'external_acl', 'external_abn', 'external_acl_address',
        'external_acl_email', 'external_acl_contact_num', 'is_bid', 'hl_no_sig_cg'
      ];

      foreach ($fieldsToCopy as $field) {
        if (isset($sourceUserSettings->$field)) {
          $currentUserSettings->$field = $sourceUserSettings->$field;
        }
      }

      if (isset($sourceUserSettings->cg_top_lenders) && !empty($sourceUserSettings->cg_top_lenders)) {
        $currentUserSettings->cg_top_lenders = $sourceUserSettings->cg_top_lenders;
      }

      if (!$ConPartnerUserSettingsEntityTable->save($currentUserSettings)) {
        throw new \Exception("Failed to update user settings information.");
      }

      $currentFeeDefaults = [];

      foreach ($sourceFeeDefaults as $sourceFeeDefault) {
          $currentFeeDefault = $ConPartnerUserFeeDefaultEntityTable->find()
              ->where([
                  'config_con_fee_id' => $sourceFeeDefault->config_con_fee_id,
                  'partner_user_id' => $currentPartnerUserId
              ])
              ->first();
      
          if (!$currentFeeDefault) {
              $currentFeeDefault = $ConPartnerUserFeeDefaultEntityTable->newEntity([
                  'partner_id' => $user['partner_id'],
                  'partner_user_id' => $user['partner_user_id'],
              ]);
          }
      
          foreach ($sourceFeeDefault->toArray() as $key => $value) {
              if (!in_array($key, ['id', 'created', 'updated', 'partner_id', 'partner_user_id'])) {
                  $currentFeeDefault->$key = $value;
              }
          }
      
          $currentFeeDefault->partner_id = $user['partner_id'];
          $currentFeeDefault->partner_user_id = $user['partner_user_id'];
      
          $currentFeeDefaults[] = $currentFeeDefault;
      }
      
      if (!$ConPartnerUserFeeDefaultEntityTable->saveMany($currentFeeDefaults)) {
          throw new \Exception("Failed to update user fee default information.");
      }

      return $this->setJsonResponse(['success' => true, 'message' => 'User information copied successfully']);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      return $this->setJsonResponse(['success' => false, 'error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 500);
    }
  }
  
  public function getLeadAssignedUser($lead_ref){
    try{

      $lead_id = (new LendInternalAuth)->unhashLeadId($lead_ref);
      if (!$lead_id) {
        throw new \Exception("Can't find a lead.");
      }

      $leadTable = TableRegistry::getTableLocator()->get('LeadEntity');
      $lead = $leadTable->find()
          ->where(['LeadEntity.lead_id' => $lead_id])
          ->contain([
              'PartnerEntity',
              'PartnerUserLeadsEntity' => [
                  'PartnerUserEntity'
              ]
          ])
          ->first();

      if(empty($lead)){
        throw new \Exception("Lead info not found.");
      }

      return $this->setJsonResponse([
        'partner'=>[
          'name'=>$lead->partner->organisation_name ?? $lead->partner->company_name,  
          'trading_address' => $lead->partner->trading_address ?? $lead->partner->address,
        ],
        'partner_user' => [
          'name' => $lead->partner_user_lead->user->name,
          'email'=> $lead->partner_user_lead->user->email,
          'mobile'=> $lead->partner_user_lead->user->mobile,
          'phone' => $lead->partner_user_lead->user->phone,
        ]
      ]);
    }
    catch(\Exception $e){
      Log::write('error', $e->getMessage());
      return $this->setJsonResponse(["success"=>false,"data"=>$e->getMessage()]);
    }
  }
  
}