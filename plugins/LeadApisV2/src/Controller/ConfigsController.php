<?php
namespace LeadApisV2\Controller;

use Cake\ORM\TableRegistry;
use Cake\Log\Log;
use App\Lend\Config;
use Cake\Core\Configure;
use Cake\Utility\Inflector;
use \Cake\Datasource\ConnectionManager;
use Cake\Cache\Cache;

class ConfigsController extends AppController
{
  public function initialize(){
    parent::initialize();
    if ($this->request->getHeader('referrers-api-key')) {
      $referrer_api_key = $this->request->getHeader('referrers-api-key')[0];
       if ($referrer_api_key === getenv('REFERRERS_API_KEY')) {
        $this->Auth->allow(['index']);
      }
    }
    $this->cache_config = "lend_config";
  }

  public function version() {
    $config_others = TableRegistry::getTableLocator()->get('ConfigOther')->get(1)->toArray();
    return $this->setJsonResponse(['version' => $config_others['app_config_version']]);
  } 

  public function camelToHuman($string){
    return Inflector::humanize(Inflector::underscore($string));
  }

  public function arrayToLableValues($list, $camelToHuman=true){
    if(!empty($list)){
      foreach($list as $key => $value){
        $list[$key] = [
          "label" => $camelToHuman?$this->camelToHuman($value):$value,
          "value" => $value
        ];
      }
    }
    return $list;
  }

  public function index() {
    try {
      ConnectionManager::alias('reader_db', 'default');
      $config_others = TableRegistry::getTableLocator()->get('ConfigOther')->get(1)->toArray();
      $configCacheVersion = "V2_".$config_others['app_config_version'];
      $configs = Cache::read($configCacheVersion, $this->cache_config);
      if(!empty($configs)){
        return $this->response->withType('application/json')->withStringBody($configs);
      }else{
        $config_model = new Config;
        $configs = [
          'config_version' => $config_others['app_config_version'],
          'homeowner_types' => Configure::read('Lend.homeowner_types'),
          'lead_owner_type' => Configure::read('Lend.lead_owner_type'),
          'AU_states' => Configure::read('Lend.AU_states'),
          'bs_slugs_logo_available' => Configure::read('Lend.bs_slugs_logo_available'),
          'pf_delivery_costs' => Configure::read('Lend.pf_delivery_costs'),
          'purpose' => TableRegistry::getTableLocator()->get('FrmPurposeEntity')->find('all')->contain(['SubPurpose'])->where(['status' => 1, 'parent_id IS null'])->orderAsc('purpose_order')->toArray(),
          'purpose_private' => $this->_putOtherToEndOfTheList(TableRegistry::getTableLocator()->get('FrmPrivateLendingPurpose')->find('all')->where(['status' => 1])->toArray(), 'purpose'),
          'equipment' => TableRegistry::getTableLocator()->get('FrmEquipmentEntity')
          ->find('all')
          ->contain(['SubEquipment'])
          ->where(['status != 0', 'parent_id IS null'])
          ->order(['sort_order' => 'ASC'])
          ->toArray(),    
          'equipment_condition' => $config_model->getEnumValues('leads', 'equipment_condition'),
          'equipment_source' => $this->loadModel("Leads")->getAvailableEquipmentSource(),
          'loan_terms' => $this->_putOtherToEndOfTheList(TableRegistry::getTableLocator()->get('FrmLoanTerms')->find('all')->where(['status' => 1])->toArray(), 'loan_term'),
          'operating_type' => $config_model->getEnumValues('lead_asset_finance', 'operating_type'),
          'personal_service' => $config_model->getEnumValues('lead_asset_finance', 'personal_service'),
          'green_electric_vehicle' => $config_model->getEnumValues('lead_asset_finance', 'green_electric_vehicle'),
          'third_party_evaluation' => $config_model->getEnumValues('frm_equipment', 'third_party_evaluation'),
          'asset_reading' => $config_model->getEnumValues('frm_equipment', 'asset_reading'),
          'trading_period' => $config_model->getEnumValues('partner_account_meta', 'trading_period_select'),
          'industries' => TableRegistry::getTableLocator()->get('ConfigIndustryEntity')->find('all')->contain(['SubIndustry'])->where(['parent IS null'])->toArray(),
          'occupations' => TableRegistry::getTableLocator()->get('Occupation')->find('all') // always show the "Other" occupation top of the list
          ->order([
              'CASE WHEN occupations_name = "Other" THEN 1 ELSE 2 END',
              'occupations_name' => 'ASC'
          ])
          ->toArray(),
          'product_types' => TableRegistry::getTableLocator()->get('PartnerProductTypeEntity')->find('all')->contain('SubProductType')->where(['active' => true])->orderAsc('product_order')->toArray(),
          // 'all_product_types' => we may not need it
          'residency_status' => $config_model->getEnumValues('lead_owners', 'residency_status'),
          'trading_period_select' => $config_model->getEnumValues('leads', 'trading_period_select'),
          'asset_types' => [
            'business' => $this->_putOtherToEndOfTheList(TableRegistry::getTableLocator()->get('ConfigAssetTypeEntity')->find('all')->where(['status' => true, 'asset_type' => 'business'])->toArray(), 'asset_type_name'),
            'personal' => $this->_putOtherToEndOfTheList(TableRegistry::getTableLocator()->get('ConfigAssetTypeEntity')->find('all')->where(['status' => true, 'asset_type' => 'personal'])->toArray(), 'asset_type_name'),
          ],
          'liabilities' => [
            'business' => $this->_putOtherToEndOfTheList(TableRegistry::getTableLocator()->get('ConfigLiabilityEntity')->find('all')->where(['status' => 1, 'liability_type' => 'business'])->toArray(), 'liability_name'),
            'personal' => $this->_putOtherToEndOfTheList(TableRegistry::getTableLocator()->get('ConfigLiabilityEntity')->find('all')->where(['status' => 1, 'liability_type' => 'personal'])->toArray(), 'liability_name'),
          ],
          'entity_types' => TableRegistry::getTableLocator()->get('FrmEntityTypeEntity')->find('all')->where(['status' => true])->toArray(),
          'abn_entity_types' => TableRegistry::getTableLocator()->get('AbnEntityTypeEntity')->find('all')->toArray(),
          'asset_sale_types' => TableRegistry::getTableLocator()->get('AssetCalcSaleTypeEntity')->find('all')->toArray(),
          'reference_types' => $config_model->getEnumValues('lead_references', 'reference_type'),
          'lead_asset_finance_configs' => [
            'condition' => $config_model->getEnumValues('lead_asset_finance', 'condition'),
            'reason_for_purchase' => $config_model->getEnumValues('lead_asset_finance', 'reason_for_purchase'),
            'supplier_type' => $config_model->getEnumValues('lead_asset_finance', 'supplier_type'),
            'personal_service_options' => $config_model->getEnumValues('lead_asset_finance', 'personal_service'),
            'sale_type' => TableRegistry::getTableLocator()->get('ConfigAssetSaleTypeEntity')->find('all')->where(['active' => true])->order(['order' => 'ASC'])->toArray(),
            'fuel_type' => $config_model->getEnumValues('lead_asset_finance', 'fuel_type'),
            'transmission' => $config_model->getEnumValues('lead_asset_finance', 'transmission'),
            'balloon_reason' => $this->arrayToLableValues($config_model->getEnumValues('lead_asset_finance', 'balloon_reason')),
            'balloon_pay_method' => $this->arrayToLableValues($config_model->getEnumValues('lead_asset_finance', 'balloon_pay_method')),
          ],
          'contract_types' => $config_model->getEnumValues('lead_asset_finance', 'contract_type'),
          'employment_types' => $config_model->getEnumValues('lead_owner_employment', 'employment_type'),
          'living_status' => $config_model->getEnumValues('lead_owner_addresses', 'living_status'),
          'marital_status' => $config_model->getEnumValues('lead_owners', 'marital_status'),
          'credit_history' => $config_model->getEnumValues('lead_owners', 'credit_history'),
          'customer_type' => array_values(array_diff($config_model->getEnumValues('leads', 'customer_type'), ['Businesses you invoice'])),
          'lease_or_finance' => $config_model->getEnumValues('leads', 'lease_or_finance'),
          'security_type' => $config_model->getEnumValues('lead_owners', 'security_type'),
          'gender' => $config_model->getEnumValues('lead_owners', 'gender'),
          'driving_licence_type' => $config_model->getEnumValues('lead_owners', 'driving_licence_type'),
          'owner_titles' => $config_model->getEnumValues('lead_owners', 'title'),
          'requested_specifies' => $this->_formatDocumentTypes(TableRegistry::getTableLocator()->get('ConfigDocumentTypeEntity')->find('list')->where(['is_account_level' => 1])->order("type_name ASC")->toArray()),
          'partner_lead_uploads_specifies' => $partner_lead_uploads_specifies = $this->_formatDocumentTypes(TableRegistry::getTableLocator()->get('ConfigDocumentTypeEntity')->find('list')->where(['is_lead_level' => 1])->order("type_name ASC")->toArray()),
          'partner_lead_uploads_specifies_sub' => array_values(array_diff($partner_lead_uploads_specifies, ['Privacy Form (Electronic Signature)'])),
          'partner_account_uploads_specifies' => $this->_formatDocumentTypes(TableRegistry::getTableLocator()->get('ConfigDocumentTypeEntity')->find('list')->where(['is_account_level' => 1])->order("type_name ASC")->toArray()),
          'partner_applicant_group_uploads_specifies' => $this->_formatDocumentTypes(TableRegistry::getTableLocator()->get('ConfigDocumentTypeEntity')->find('list')->where(['is_applicant_level' => 1])->order("type_name ASC")->toArray()),
          'books_package' => TableRegistry::getTableLocator()->get('FrmBooksPackageEntity')->find('all')->where(['status' => 1])->toArray(),
          'trustee_type' => $config_model->getEnumValues('entity_trust', 'trustee_type'),
          'trust_settor' => $config_model->getEnumValues('entity_trust', 'trust_settor'),
          'max_amount' => ********,
          'max_invoices' => 127,
          'domains' => [
            'domain_wp'     => getenv('DOMAIN_WP'),
            'domain_crm'    => getenv('DOMAIN_CRM'),
            'domain_app'    => getenv('DOMAIN_APP', true),
            'domain_bro'    => getenv('DOMAIN_BRO', true),
            'domain_bss'    => getenv('DOMAIN_BSS'),
            'domain_files'  => getenv('DOMAIN_FILES'),
            'domain_glass_service' => getenv('DOMAIN_GLASS_SERVICE'),
            'domain_bs_spa' => getenv('DOMAIN_BS_SPA'),
            'domain_lend_sign' => getenv('DOMAIN_LEND_SIGNATURE'),
          ],
          'max_lender_preferences' => 5,
          'lend_statuses' => TableRegistry::getTableLocator()->get('LendStatusEntity')->find('all')->select(['lend_status_id', 'status_name', 'group_name', 'off_panel_lender', 'is_consumer', 'portal_order'])->where(['portal_selectable' => true])->order(['group_name' => 'asc', 'portal_order' => 'asc'])->toArray(),
          'all_lend_statuses' => TableRegistry::getTableLocator()->get('LendStatusEntity')->find('all')->select(['lend_status_id', 'status_name', 'group_name', 'off_panel_lender', 'is_consumer', 'portal_selectable', 'portal_order'])->order(['group_name' => 'asc', 'portal_order' => 'asc'])->toArray(),
          'required_docs' => TableRegistry::getTableLocator()->get('ProductRequiredDocEntity')->find('all')->toArray(),
          'con_fee_groups' => $config_model->getEnumValues('config_con_fees', 'group'),
          'con_fees' => $this->_getConFees(),
          'requirements' => TableRegistry::getTableLocator()->get('ConfigRequirementEntity')->find('all')->where(['active' => true])->toArray(),
          'incomes' => TableRegistry::getTableLocator()->get('ConfigIncomeEntity')->find('all')->orderAsc('sort_sequence')->toArray(),
          'expenses' => TableRegistry::getTableLocator()->get('ConfigExpenseEntity')->find('all')->contain(['ConfigExpensesBreakdownLendersEntity', 'ConfigExpensesBreakdownEntity.LenderEntity'=> [ 'fields' => [ 'LenderEntity.lender_name' ] ]])->orderAsc('sort_sequence')->toArray(),
          'expenses_breakdown' => $this->_groupBreakdownByLender(TableRegistry::getTableLocator()->get('ConfigExpensesBreakdownEntity')->find('all')->where(['active' => true])->contain(['LenderEntity', 'ConfigExpenseEntity'])->orderAsc('ConfigExpensesBreakdownEntity.lender_id')->toArray()),
          'how_soon' => TableRegistry::getTableLocator()->get('FrmHowSoonEntity')->find('all')->toArray(),
          'document_types' => TableRegistry::getTableLocator()->get('ConfigDocumentTypeEntity')->find('all')->order("type_name ASC")->toArray(),
          'lender_logos' => TableRegistry::getTableLocator()->get('LenderEntity')->find('list', [
            'keyField' => function ($lender) {return strtolower($lender->shorthand);},
            'valueField' => 'lender_logo'
          ])->where(["status" => 1])->toArray(),
          'lenders' => TableRegistry::getTableLocator()->get('LenderEntity')->find('all')->select(['id' => 'lender_id', 'name' => 'shorthand', 'logo' => 'lender_logo', 'full_name' => 'lender_name', "country", "lender_type", "intermediary_lender_id"])->where(['status' => 1])->toArray(),
          'callback_types' => $config_model->getEnumValues('partner_callbacks', 'type'),
          'countries' => Configure::read('Lend.countries'),
          'product_purpose'=> $this->_productPurpose(),
          'private_lend_type'=>$config_model->getEnumValues('leads','private_lend_type'),
          'credit_score_types' => $this->getTableLocator()->get('ConfigCreditScoreTypeEntity')->find('all')->where(['active' => true])->toArray(),
          'ask_applicant_pages' => $this->_getAskApplicantPages(),
          'asset_financiers'=>  TableRegistry::getTableLocator()->get('AssetFinanciers')->find('all')->where(['status' => "Active"])->toArray(),
          'off_panel_lenders'=>  TableRegistry::getTableLocator()->get('OffPanelLenderEntity')->find('all')->select(['lender_name', 'thesaurus'])->where(['active' => true])->toArray(),
          'home_loan' => [
            'loan_purposes' => $config_model->getEnumValues('lead_home_loan_details', 'loan_purpose'),
            'repayment_types' => $config_model->getEnumValues('lead_home_loan_details', 'repayment_type'),
            'rate_types' => $config_model->getEnumValues('lead_home_loan_details', 'rate_type'),
            'repayment_frequencies' => $config_model->getEnumValues('lead_home_loan_details', 'repayment_frequency'),
            'refinance_reasons' => TableRegistry::getTableLocator()->get('home_loan_refinance_reasons')->find('all')->select(['id', 'name'])->order("sort_order ASC")->toArray(),
            'loan_features' => TableRegistry::getTableLocator()->get('home_loan_features')->find('all')->select(['id', 'name'])->order("sort_order ASC")->toArray(),
          ],
          'home_loan_property' => [
            'property_found_options' => $config_model->getEnumValues('lead_home_loan_properties', 'property_found'),
            'property_use_options' => $config_model->getEnumValues('lead_home_loan_properties', 'property_use'),
            'property_types' => $config_model->getEnumValues('lead_home_loan_properties', 'property_type'),
            'development_types' => $config_model->getEnumValues('lead_home_loan_properties', 'development_type'),
            'rent_frequencies' => $config_model->getEnumValues('lead_home_loan_properties', 'rent_frequency'),
          ],
          'home_loan_compliance' => [
            'document_verification_methods' => $config_model->getEnumValues('lead_home_loan_compliances', 'document_verification_method'),
            'client_interview_methods' => $config_model->getEnumValues('lead_home_loan_compliances', 'client_interview_method'),
            'approval_types' => $config_model->getEnumValues('lead_home_loan_compliances', 'approval_type'),
            'lenders' => TableRegistry::getTableLocator()->get('home_loan_lenders')->find('all')->select(['id', 'name' => 'short_name'])->where(["status" => 1])->order("sort_order ASC")->toArray(),
            'fees_options' => TableRegistry::getTableLocator()->get('home_loan_fees')->find('all')->select(['id', 'name' => 'fee_name'])->where(["status" => 1])->order("sort_order ASC")->toArray(),
            'scenarios_option_keys' => $config_model->getEnumValues('lead_home_loan_compliance_scenarios', 'option_key'),
            'refinance_risks' => TableRegistry::getTableLocator()->get('home_loan_refinancing_risks')->find('all')->select(['id', 'risk', 'answer_options'])->where(["status" => 1])->order("sort_order ASC")->toArray(),
            'repayment_types' => $config_model->getEnumValues('lead_home_loan_compliance_scenarios', 'repayment_type'),
          ],
          'pay_off_options' => TableRegistry::getTableLocator()->get('home_loan_pay_off_options')->find('all')->select(['id', 'name'])->order("sort_order ASC")->toArray(),
          'security_questions' => TableRegistry::getTableLocator()->get('security_questions')->find('all')->select(['question'])->order("sort_order ASC")->extract('question')->toArray(),
          'countries_iso_2'=> $this->_getCountries()
        ];
        ConnectionManager::dropAlias('default');
        Cache::write($configCacheVersion, json_encode($configs), $this->cache_config);
      }
      return $this->setJsonResponse($configs);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode()>=100 ? $e->getCode() : 400);
    }
  }

  private function _getAskApplicantPages(){
    $askApplicantPageConfig = Configure::read('Lend.ask_applicant_pages');
    $ask_applicant_pages = [];
    foreach ($askApplicantPageConfig as $lead_type => $pages) {
      foreach ($pages as $page => $label) {
        $ask_applicant_pages[$lead_type][] = [
          'value' => $page,
          'label' => $label,
        ];
      }
    }
    return $ask_applicant_pages;
  }

  private function _getConFees(){
    $fees = TableRegistry::getTableLocator()->get('ConfigConFeeEntity')->find('all')->toArray();
    foreach ($fees as $key => $value) {
      if(is_string($value['fields'])){
        $fees[$key]['fields'] = json_decode($value['fields']);
      }
    }
    return $fees;
  }

  private function _getCountries(){
    $countries = TableRegistry::getTableLocator()->get('countries')->find('all')->select(['value'=>'code', 'label' => 'name', 'address_format'])->order("name ASC")->enableHydration(true)->toArray();
    foreach ($countries as $key => &$country) {
      if(is_string($country['address_format'])){
        $country['address_format'] = json_decode($country['address_format'], true);
      }
    }
    return $countries;
  }

  private function _putOtherToEndOfTheList($configs, $field_name){
    $return = []; $others = [];
    foreach($configs as $c){
      if(in_array(strtolower($c[$field_name]), ['other', 'others'])){
        $others[] = $c;
      }else{
        $return[] = $c;
      }
    }
    return array_merge($return, $others);
  }

  private function _formatDocumentTypes($data) {
    unset($data[array_search('Other', $data)]);
    usort($data, 'strnatcasecmp');
    array_push($data, 'Other');
    return $data;
  }

  private function _groupBreakdownByLender($breakdowns) {
    $result = array();
    foreach ($breakdowns as $b) {
      $shorthand = $b['lender']['shorthand'];
      unset($b['lender']);
      if(!empty($result[$shorthand][$b['config_expense_id']]) && !$result[$shorthand][$b['config_expense_id']]){
        $result[$shorthand][$b['config_expense_id']] = $b['config_expense'];
        $result[$shorthand][$b['config_expense_id']]['breakdowns'] = [];
      }
      unset($b['config_expense']);
      $result[$shorthand][$b['config_expense_id']]['breakdowns'][] = $b;
    }
    return $result;
  }

  private function _productPurpose(){
    $frmPurposeTable = TableRegistry::getTableLocator()->get('FrmPurposeEntity');
    $purposes = $frmPurposeTable->find('all', [
        'contain' => ['PurposeProductEntity.PartnerProductTypeEntity']
    ])->toArray();
    $structuredData = [];

    foreach ($purposes as $purpose) {
        $products = [];   
        foreach ($purpose->purpose_products as $product) {
            $products[] = [
                'product_id' => $product->partner_product_type->product_type_id,
                'product_name' => $product->partner_product_type->product_type_name,
            ];
        }
        $structuredData[] = [
            'purpose' => $purpose->purpose,
            'purpose_id' => $purpose->purpose_id,
            'list_of_products' => $products,
        ];
    }

    return $structuredData;
  }

}