<?php
namespace LeadApisV2\Controller;
use Cake\Core\Configure;
use Cake\Event\Event;
use Cake\Http\Response;
use Hashids\Hashids;
use Cake\ORM\TableRegistry;
use Cake\Utility\Security;
use Firebase\JWT\JWT;
use App\Controller\AppController as BaseController;
use App\Lend\LendInternalAuth;
use Cake\Chronos\Chronos;
use Cake\Log\Log;
use \Aws\S3\S3Client;
use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Request;
use Aws\S3\Exception\S3Exception;
use App\Traits\S3Trait;

class AppController extends BaseController
{
  use S3Trait;

	public function initialize() {
		parent::initialize();
	}
	public function beforeFilter($event){
		parent::beforeFilter($event);
	}

	protected function _saveLeadEntity($lead_ref, $post_data, $associated){
    try {
      if (empty($lead_ref)) {
        throw new \Exception("Lead ref is required.");
      }
      $lend_internal_auth = new LendInternalAuth;
      $lead_id = $lend_internal_auth->unhashLeadId($lead_ref);
      if (empty($lead_id)) {
        throw new \Exception("Can't find a lead.");
      }
      if (!empty($post_data['asset_finance']) && @$post_data['asset_finance']['is_vehicle_found'] === false) {
        $post_data['asset_finance']['year'] = null;
      }
      $lead_table = TableRegistry::getTableLocator()->get('LeadEntity');
      $lead = $lead_table->get($lead_id, [
        'contain' => $associated
      ]);
      if (!empty($post_data['owners_all'])) {
        $ownerAddresses = array_column($lead['owners_all'], 'all_addresses', 'owner_id');
        foreach ($post_data['owners_all'] as $key => &$owner) {
          if (!empty($owner['owner_ref']) && empty($owner['owner_id'])) {
            $hashids = new Hashids('lead_owners', 7);
            $owner_id = $hashids->decode($owner['owner_ref'])[0];
            $owner['owner_id'] = $owner_id;
          }

          if (!empty($ownerAddresses[$owner['owner_id']]) && !empty($owner['all_addresses'])) {
            foreach ($owner['all_addresses'] as &$address) {
              if (empty($address['lead_owner_address_id'])) {
                foreach ($ownerAddresses[$owner['owner_id']] as $existAddress) {
                  if (
                    strtolower($existAddress['address_type']) == strtolower($address['address_type'])
                    && strtolower($existAddress['unit_number']) == strtolower($address['unit_number'])
                    && strtolower($existAddress['street_number']) == strtolower($address['street_number'])
                    && strtolower($existAddress['street_name']) == strtolower($address['street_name'])
                    && strtolower($existAddress['suburb']) == strtolower($address['suburb'])
                    && strtolower($existAddress['city']) == strtolower($address['city'])
                    && strtolower($existAddress['state']) == strtolower($address['state'])
                    && strtolower($existAddress['postcode']) == strtolower($address['postcode'])
                    && strtolower($existAddress['country']) == strtolower($address['country'])
                  ) {
                    $address['lead_owner_address_id'] = $existAddress['lead_owner_address_id'];
                    break;
                  }
                }
              }
            }
          }
        }
      }
      if (!empty($post_data['con_preliminary'])) {
        $post_data['con_preliminary'] = [$post_data['con_preliminary']];
      }
      // handle incomes and expenses:
      $lead_incomes = $lead->incomes ? json_decode(json_encode($lead->incomes), true) : [];
      if (!empty($post_data['incomes']) && !empty($lead_incomes)) {
        $post_data['incomes'] = $this->mappingIncomes($lead_incomes, $post_data['incomes']);
      }


      $lead_expenses = $lead->expenses ? json_decode(json_encode($lead->expenses), true) : [];
      if (!empty($post_data['expenses']) && !empty($lead_expenses)) {
        $expense_ids = array_column($lead_expenses, 'id', 'config_expense_id');
        foreach ($post_data['expenses'] as &$expenses) {
          if (empty($expenses['id']) && isset($expense_ids[$expenses['config_expense_id']])) {
            $expenses['id'] = $expense_ids[$expenses['config_expense_id']];
          }
        }
      }

      if (!empty($post_data['lead_home_loan_compliance'])) {
        // map Home Loan Compliance Fees
        $compliance_fees = json_decode(json_encode($lead->lead_home_loan_compliance->fees ?? []), true);
        if (!empty($post_data['lead_home_loan_compliance']['fees']) && !empty($compliance_fees)) {
          $compliance_fee_Ids = array_column($compliance_fees, 'id', 'fee_id');
          foreach ($post_data['lead_home_loan_compliance']['fees'] as &$fee) {
            if (empty($fee['id']) && isset($compliance_fee_Ids[$fee['fee_id']])) {
              $fee['id'] = $compliance_fee_Ids[$fee['fee_id']];
            }
          }
        }

        // map Home Loan Compliance Scenarios
        $compliance_scenarios = json_decode(json_encode($lead->lead_home_loan_compliance->scenarios ?? []), true);
        if (!empty($post_data['lead_home_loan_compliance']['scenarios']) && !empty($compliance_scenarios)) {
          $compliance_scenario_Ids = array_column($compliance_scenarios, 'id', 'option_key');
          foreach ($post_data['lead_home_loan_compliance']['scenarios'] as &$scenario) {
            if (empty($scenario['id']) && isset($compliance_scenario_Ids[$scenario['option_key']])) {
              $scenario['id'] = $compliance_scenario_Ids[$scenario['option_key']];
            }
          }
        }
      }
      
      $lead_table->patchEntity($lead, $post_data, [
        'associated' => $associated
      ]);
      $result = $lead_table->save($lead);
      if (!$result) {
        throw new \Exception(json_encode($lead->getErrors()));
      }
      return $lead->toArray();
    } catch (\Exception $e) {
      throw $e;
    }
	}

  // Mapping Incomes to avoid duplicates
  private function mappingIncomes($existIncomes, $newIncomes)
  {
    //Prepare the data from existing records
    $salaryIncomesConfigIds = [Configure::read('Lend.SALARY_INCOME_CONFIG_ID'), Configure::read('Lend.SPOUSE_INCOME_CONFIG_ID')];
    $noShareableIncome = [];
    $shareableIncome = [];
    foreach ($existIncomes as $existIncome) {
      if (in_array($existIncome['config_income_id'], $salaryIncomesConfigIds)) {
        $noShareableIncome[$existIncome['owner_ref']][$existIncome['config_income_id']] = $existIncome;
      } else {
        $shareableIncome[$existIncome['config_income_id']] = $existIncome;
      }
    }

    foreach ($newIncomes as &$income) {
      if (empty($income['id'])) {
        if (in_array($income['config_income_id'], $salaryIncomesConfigIds) && isset($noShareableIncome[$income['owner_ref']][$income['config_income_id']])) {
          $matchedIncome = $noShareableIncome[$income['owner_ref']][$income['config_income_id']];
          $income['id'] = $matchedIncome['id'];
          if ($income['shared'] && $matchedIncome['shared']) {
            $income['shared'] = $this->mappingIncomeShares($income['id'], $matchedIncome['shared'], $income['shared']);
          }
        } elseif (isset($shareableIncome[$income['config_income_id']])) {
          $matchedIncome = $shareableIncome[$income['config_income_id']];
          $income['id'] = $matchedIncome['id'];
          if ($income['shared'] && $matchedIncome['shared']) {
            $income['shared'] = $this->mappingIncomeShares($income['id'], $matchedIncome['shared'], $income['shared']);
          }
        }
      }
    }

    return $newIncomes;
  }

  // Mapping Income Shares to avoid duplicates
  private function mappingIncomeShares($income_id, $existShares, $newShares)
  {
    $matchedIncomeShares = array_column($existShares, 'id', 'owner_ref');
    foreach ($newShares as &$share) {
      if (isset($matchedIncomeShares[$share['owner_ref']])) {
        $share['id'] = $matchedIncomeShares[$share['owner_ref']];
        $share['lead_income_id'] = $income_id;
        $share['income_id'] = $income_id;
      }
    }
    return $newShares;
  }

	private function _getTotalMonths($date_array){
		$total_months = 0;
		if(!empty($date_array)){
			foreach($date_array as $date_item){
				$date_from = (empty($date_item['date_from']) ? Chronos::now() : $date_item['date_from']);
				$date_to = (empty($date_item['date_to']) ? Chronos::now() : $date_item['date_to']);
				$total_months += $date_from->diffInMonths($date_to);
			}
		}
		return $total_months;
	}

	protected function validateApplicantAddressHistory($lead){
		$missing = [];
		if($lead['owners_all']){
			foreach ($lead['owners_all'] as $key => $owner) {
				if($owner['all_addresses']){
					$valid = $this->_getTotalMonths($owner['all_addresses']) >= 24;
					if(!$valid){
						$missing[] = $owner['owner_ref'];
					}
				}else{
					$missing[] = $owner['owner_ref'];
				}
			}
			if($missing){
				return $missing;
			}
		}else{
			return false;
		}
		return true;
	}

	// we might need this function depends on Phils request
	protected function checkEmptyAddress($lead){

		$missing = [];
		if($lead['owner_poc']){
			if($lead['owner_poc']['all_addresses']){
				$valid = $this->_getTotalMonths($lead['owner_poc']['all_addresses']) >= 24;
				if(!$valid){
					$missing[] = $lead['owner_poc']['owner_ref'];
				}
			}else{
				$missing[] = $lead['owner_poc']['owner_ref'];
			}
			if($missing){
				return $missing;
			}
		}else{
			return false;
		}
		return true;
	}

	protected function validateApplicantEmploymentHistory($lead){
		$missing = [];
		if($lead['owners_all']){
			foreach ($lead['owners_all'] as $key => $owner) {
				if($owner['all_employments']){
					$valid = ($this->_getTotalMonths($owner['all_employments']) >= 24);
					if(!$valid){
						$missing[] = $owner['owner_ref'];
					}
				}else{
					$missing[] = $owner['owner_ref'];
				}
			}
			if($missing){
				return $missing;
			}
		}else{
			return false;
		}
		return true;
	}

	private function _parseField($field_name, $condition_field, $condition_value, $data){
		if(strpos($field_name, '|') !== false){
			foreach (explode('|', $field_name) as $key => $sub_field_name) {
				$leadFieldValue = $this->_parseField(trim($sub_field_name), $condition_field, $condition_value, $data);
				if($leadFieldValue !== null){
					return $leadFieldValue;
				}
			}
			return null;
		}
		if(strpos($field_name, '[]') !== false){
			$fieldArr = explode('[]', $field_name);
			$multiple = [];
			foreach ($data[$fieldArr[0]] as $key => $item) {
				$leadFieldValue = $this->_parseField($key.$fieldArr[1], $condition_field, $condition_value, $data[$fieldArr[0]]);
				if($leadFieldValue !== null || $condition_field == ""){
					$multiple[] = ['value' => $leadFieldValue, 'owner_ref' => $item['owner_ref']];
				}
			}
			return $multiple;
		}
		if(strpos($field_name, '.') !== false){
			$fieldArr = explode('.', $field_name);
			return $this->_parseField($fieldArr[1], $condition_field, $condition_value, $data[$fieldArr[0]]);
		}else{
			if($condition_field){
				if($condition_value == $data[$condition_field]){
					return $data[$field_name];
				}
				return null;
			}else{
				return @$data[$field_name];
			}
		}
	}

	protected function checkLeadMissingField($lead,$type='consumer_lead_required_field',$consumer_version=1){

		$missingFields = [];

		if( $type == 'request_invoice'){
      if($consumer_version == 2){
        $consumer_lead_required_field = Configure::read('Lend.request_invoice_consumer_v2');
      }else{
        $consumer_lead_required_field = Configure::read('Lend.request_invoice');
      }
    }
		else
			$consumer_lead_required_field = Configure::read('Lend.consumer_lead_required_field'); // we will leave this as it is for now need to confirm from Phil and Josiah

		$owner_names = [];

		if($lead['owners_all']){
			foreach ($lead['owners_all'] as $key => $owner) {
				$owner_names[$owner['owner_ref']] = $owner['full_name'];
			}
		}


		foreach ($consumer_lead_required_field as $field) {
			if(!empty($field['filter_condition']) && !empty($field['filter_condition_value'])){
				$filterValue = $this->_parseField($field['filter_condition'], $field['condition_field'], $field['condition_value'], $lead);
				if($filterValue != $field['filter_condition_value']){
					continue;
				}
			}
			$leadFieldValue = $this->_parseField($field['field'], $field['condition_field'], $field['condition_value'], $lead);
			$isMissing = false;
      
      // Skip make, model, year if vehicle not found
      if (@$lead['asset_finance']['is_vehicle_found'] === false && in_array($field['field'], ['asset_finance.make', 'asset_finance.model', 'asset_finance.year'])) {
        continue;
      }

			if(!empty($field['custom_function'])){
				$validDates = $this->{$field['custom_function']}($lead);
				if(is_array($validDates)){
					foreach($validDates as $owner_ref){
						$missingFields[] = ['name' => $field['goto_field']??$field['field'], 'message' => str_replace("[applicant_name]", $owner_names[$owner_ref], $field['text']), 'route' => str_replace("[owner_ref]", $owner_ref, $field['route'])];
					}
				}elseif($validDates == false){
					$missingFields[] = ['name' => $field['goto_field']??$field['field'], 'message' => $field['text'], 'route' => $field['route']];
				}
			}
      else if (!empty($field['bulk_custom_function'])) {
        $missingFields = array_merge($missingFields, $this->{$field['bulk_custom_function']}($lead, $field, $owner_names));
      }
      else if(is_array($leadFieldValue)){
				foreach ($leadFieldValue as $key => $owner) {
					if(($field['value'] === "notEmpty" && $owner['value'] == null) || ($field['value'] !== "notEmpty" && $field['value'] != $owner['value'])){
						$missingFields[] = ['name' => $field['goto_field']??$field['field'], 'message' => str_replace("[applicant_name]", $owner_names[$owner['owner_ref']], $field['text']), 'route' => str_replace("[owner_ref]", $owner['owner_ref'], $field['route'])];
					}
				}
				$isMissing = true;
			}else if(($field['value'] === "notEmpty" && $leadFieldValue == null) || ($field['value'] !== "notEmpty" && $field['value'] != $leadFieldValue)){
				$missingFields[] = ['name' => $field['goto_field']??$field['field'], 'message' => $field['text'], 'route' => $field['route']];
			}
		}
		return $missingFields;
	}

  protected function validateEmployment($lead, $field, $owner_names) {
    $missingFields = [];

    foreach ($lead['owners_all'] as $owner) {
      foreach ($owner['all_employments'] as $employmentIndex => $employment) {
        $employmentName = 'Current Employment';
        if ($employmentIndex > 0) {
          $employmentName = "Previous Employment " . ($employmentIndex);
        }
        if (empty($employment['previous_occupation'])) {
          $missingFields[] = [
            'name' => "all_employments.{$employmentIndex}.previous_occupation",
            'message' => "Occupation of {$employmentName} for {$owner_names[$owner['owner_ref']]}",
            'route' => str_replace("[owner_ref]", array_keys($owner_names)[0], $field['route']),
          ];
        }
        if (empty($employment['employment_type'])) {
          $missingFields[] = [
            'name' => "all_employments.{$employmentIndex}.employment_type",
            'message' => "Occupation Type of {$employmentName} for {$owner_names[$owner['owner_ref']]}",
            'route' => str_replace("[owner_ref]", array_keys($owner_names)[0], $field['route']),
          ];
        }
      }
    }

    return $missingFields;
  }

  protected function validateAddress($lead, $field, $owner_names) {
    $missingFields = [];
    foreach ($lead['owners_all'] as $owner) {
      foreach ($owner['all_addresses'] as $addressIndex => $address) {
        $addressName = 'Current Address';
        if ($addressIndex > 0) {
          $addressName = "Previous Address " . ($addressIndex);
        }
        if (empty($address['full_address'])) {
          $missingFields[] = [
            'name' => "all_addresses.{$addressIndex}.full_address",
            'message' => "Address line of {$addressName} for {$owner_names[$owner['owner_ref']]}",
            'route' => str_replace("[owner_ref]", array_keys($owner_names)[0], $field['route']),
          ];
        }
        if (empty($address['date_from'])) {
          $missingFields[] = [
            'name' => "all_addresses.{$addressIndex}.date_from",
            'message' => "Date From of {$addressName} for {$owner_names[$owner['owner_ref']]}",
            'route' => str_replace("[owner_ref]", array_keys($owner_names)[0], $field['route']),
          ];
        }
        if ($addressIndex > 0 && empty($address['date_to'])) {
          $missingFields[] = [
            'name' => "all_addresses.{$addressIndex}.date_to",
            'message' => "Date To of {$addressName} for {$owner_names[$owner['owner_ref']]}",
            'route' => str_replace("[owner_ref]", array_keys($owner_names)[0], $field['route']),
          ];
        }
        if (empty($address['living_status'])) {
          $missingFields[] = [
            'name' => "all_addresses.{$addressIndex}.living_status",
            'message' => "Living Status of {$addressName} for {$owner_names[$owner['owner_ref']]}",
            'route' => str_replace("[owner_ref]", array_keys($owner_names)[0], $field['route']),
          ];
        }
        if ($address['living_status'] === 'Renting') {
          if (empty($address['landlord_name'])) {
            $missingFields[] = [
              'name' => "all_addresses.{$addressIndex}.landlord_name",
              'message' => "Landlord Name of {$addressName} for {$owner_names[$owner['owner_ref']]}",
              'route' => str_replace("[owner_ref]", array_keys($owner_names)[0], $field['route']),
            ];
          }
          if (empty($address['landlord_contact_number'])) {
            $missingFields[] = [
              'name' => "all_addresses.{$addressIndex}.landlord_contact_number",
              'message' => "Landlord Contact Number of {$addressName} for {$owner_names[$owner['owner_ref']]}",
              'route' => str_replace("[owner_ref]", array_keys($owner_names)[0], $field['route']),
            ];
          }
        }
        if ($address['living_status'] === 'Other' && empty($address['living_status_other'])) {
          $missingFields[] = [
            'name' => "all_addresses.{$addressIndex}.living_status_other",
            'message' => "Living Status of {$addressName} for {$owner_names[$owner['owner_ref']]}",
            'route' => str_replace("[owner_ref]", array_keys($owner_names)[0], $field['route']),
          ];
        }
      }
    }
    return $missingFields;
  }

	protected function _uploadDocRequest($data) {
    try {
      $devFolder = '';;
      if ((string)getenv('LEND_ENV') !== '2') {
        $devFolder = 'DevTeam/';
      }

			$fullpath = $devFolder . 'lead-uploads/' . $data['lead_ref'] . '/' . $data['file']['name'];

      $request = $this->_getS3SignedUploadRequest($fullpath,  $data['file']);
      return (string) $request->getUri();
    } catch(\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }

	protected function _getS3SignedUploadRequest($fullpath,  $data, $minutes='2') {
    try {
      $bucket = Configure::read('Lend.AWS.bucket');

      if (!$this->__validateUpload($data)) {
        return new \Exception("File is not a valid file.");
      }

      if (!$source_file = $this->__validatePdfUpload($data)) {
        throw new \Exception('File is not a valid PDF');
      }

      if (empty($this->s3Client)) {
				$this->prepareS3(); // Initiate connection if not already
			}
      // Prepare a PutObject command.
      $options = [
        'Bucket' => $bucket,
        'Key' => str_replace('\\', '/', $fullpath),
        'ContentType' => $data['type'],
        'SourceFile' => $source_file
      ];
      $this->s3Client->putObject($options);
      $cmd = $this->s3Client->getCommand('putObject', $options);
      
     // Create a special link that temporarily grants access for 2 minutes
      $request = $this->s3Client->createPresignedRequest($cmd, '+'.$minutes.' minutes');
      unlink($source_file);

      return $request;
    } catch(\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }

  protected function _generalUploadS3($fullpath, $data, $acl = null){
    $bucket = Configure::read('Lend.AWS.bucket');

    if (!$this->s3Client) $this->prepareS3(); // Initiate connection if not already
    try {
      $options = [
        'Bucket' => $bucket,
        'Key' => str_replace('\\', '/', $fullpath),
        'ContentType' => $data['type'],
        'SourceFile' => $data['tmp_name']
      ];
      if($acl){
        $options['ACL'] = $acl;
      }
        // Upload data.
        $result = $this->s3Client->putObject($options);
        // Print the URL to the object.
        return ['file' => $fullpath, 'url' => $result['ObjectURL'], 'size' => $data['size'], 'type' => $data['type']];
    } catch (S3Exception $e) {
      Log::error($e->getMessage());
    }
  }

  public function _uploadRequest($data) {
    try {
      if((string)getenv('LEND_ENV') !== '2'){
        $devFolder = 'DevTeam/';
      }else {
        $devFolder = '';
      }
      // $fullpath = $devFolder . 'account-uploads/' . $data['accountRef'] . '/' . $data['file']['name'];
      $fullpath = $devFolder . $data['path'];
      if($data['isPublic'] == 1){
        return $this->_getS3SignedUploadRequest($fullpath,  $data['file'], 'public-read');
      }else{
        return $this->_getS3SignedUploadRequest($fullpath,  $data['file']);
      }
    } catch(\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }

	public function _getS3ViewImageRequest($id) {
    try {
      if (!$id) {
				return $this->setJsonResponse(array('success'=>false, 'message'=>'No upload id supplied'));
			}

			$data = TableRegistry::getTableLocator()->get('PartnerLeadUploadsEntity')->get(["partner_lead_upload_id" => $id]);

      if (empty($data)) {
				return $this->setJsonResponse(array('success'=>true, 'message'=>'No file found with that upload id'));
			}

      $fullpath = $data['full_path'].(substr($data['full_path'], -1) == "/" ? '' : '/').$data['name'];
      $url = $this->createSignedRequest($fullpath, '2');

      return $url;
    } catch(\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }

  public function uploadToS3($data) {
    try {
      if((string)getenv('LEND_ENV') !== '2'){
        $devFolder = 'DevTeam/';
      }else {
        $devFolder = '';
      }
      // $fullpath = $devFolder . 'account-uploads/' . $data['accountRef'] . '/' . $data['file']['name'];
      $fullpath = $devFolder . $data['path'];
      if(!empty($data['isPublic']) && $data['isPublic'] == 1){
        return $this->_generalUploadS3($fullpath,  $data['file'], 'public-read');
      }else{
        return $this->_generalUploadS3($fullpath,  $data['file']);
      }
    } catch(\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }

  protected function generateTempPdf ($fileGenerationPayload, $pdfType, $s3Filename, $method = 'get')
  {
    $combined = json_encode($fileGenerationPayload) . getenv('PDF_SECRET');
    $signature = base64_encode(hash_hmac('sha256', $combined, getenv('PDF_SECRET'))); // to base64
    $queries = [
      'signature' => $signature,
    ];

    if ($method == 'get') {
      //Add data to url if method is get
      $queries['payload'] = json_encode($fileGenerationPayload);
    }

    $url = getenv('DOMAIN_PDF') . '/' . $pdfType . '?' . http_build_query($queries);

    $client = new Client([
      'http_errors' => false,
    ]);

    if($method == 'post'){
      $request = new Request(
        'post',
        $url,
        ['Content-Type' => 'application/json'],
        json_encode($fileGenerationPayload)
      );
    }else{
      $request = new Request(
        'get',
        $url
      );
    }
    
    $response = $client->send($request);
    $responseCode = $response->getStatusCode();
    $responseBodyRaw = $response->getBody()->getContents();

    $temp = tempnam(sys_get_temp_dir(), $s3Filename . '.pdf');
    

    if ($responseCode === 200) {
      $tempOpen = fopen($temp, 'w+');
      fwrite($tempOpen, $responseBodyRaw);
      fclose($tempOpen);
      return [
        'response_code' => $responseCode,
        'response_body' => $responseBodyRaw,
        'temp_file' => $temp,
        'size' => filesize($temp)
      ];
    }
    return [
      'response_code' => $responseCode,
      'response_body' => $responseBodyRaw,
    ];
  }


  protected function generatePdfAndUploadToS3($fileGenerationPayload, $pdfType, $s3Filename, $unlinkTmp = true, $method = 'get')
  {
    $pdfFile = $this->generateTempPdf($fileGenerationPayload, $pdfType, $s3Filename, $method);

    $year = date('Y');

    $s3Path = $pdfType . '/' . $year . '/' . $s3Filename . '.pdf';

    Log::info('AppController::generatePdfAndUploadToS3 => '.json_encode([
      'payload' => $fileGenerationPayload,
      's3Path'=> $s3Path,
      'PDF-Generator response code' => $pdfFile['response_code'],
      'PDF-Generator response body length' => strlen($pdfFile['response_body']),
    ]).PHP_EOL, ['scope' => ['debug']]);
    if ($pdfFile['response_code'] !== 200) {
      return [];
    }

    $temp = $pdfFile['temp_file'];
    $size = $pdfFile['size'];

    $uploadResult = $this->uploadToS3([
      'path' => $s3Path,
      'file' => [
        'type' => 'application/pdf',
        'tmp_name' => $temp,
        'size' => $size,
      ],
    ]);

    if ($unlinkTmp) {
      unlink($temp);
      return [
        's3_result' => $uploadResult,
        'size' => $size,
      ];
    }

    return [
      'temp_file' => $temp,
      'size' => $size,
      's3_result' => $uploadResult
    ];
  }

  protected function generatePdfPost($endpoint, $signaturePayload, $bodyPayload, $payloadInQueryString = true)
  {
    $combined  = json_encode($signaturePayload) . getenv('PDF_SECRET');
    $signature = base64_encode(hash_hmac('sha256', $combined, getenv('PDF_SECRET'))); // to base64
    $buildQuery = [
      'signature' => $signature,
    ];
    if($payloadInQueryString == true) {
      $buildQuery['payload'] = json_encode($signaturePayload);
    }
    $url = getenv('DOMAIN_PDF') . "/{$endpoint}?" . http_build_query($buildQuery);

    $client = new Client([
      'http_errors' => false,
    ]);

    $request = new Request(
      'post',
      $url,
      ['Content-Type' => 'application/json'],
      json_encode($bodyPayload)
    );
    return $client->send($request);
  }

  private function setPdfHeaders($displayedFileName, $contentDisposition) {
    header('Pragma: public');     // required
    header('Expires: 0');     // no cache
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Cache-Control: private', false);
    header('Content-Type: application/pdf');
    header("Content-Disposition: {$contentDisposition}; filename=\"{$displayedFileName}\"");
    header('Content-Transfer-Encoding: binary');
    header('Connection: close');
  }
  protected function displayPdf($displayedFileName, $rawPdf) {
    $this->setPdfHeaders($displayedFileName, 'inline');
    echo $rawPdf;
    exit;
  }

  protected function downloadPdf($displayedFileName, $rawPdf) {
    $this->setPdfHeaders($displayedFileName, 'attachment');
    echo $rawPdf;
    exit;
  }

  /**
   * Checks if the request has 'lead_ref'(POST) or 1st param for GET is lead_ref and user has access to this lead and if the correct method(GET/POST) has been called
   * @param array $functions
   * @throws \Exception
   * @return mixed
   */
  protected function checkAccessToLead(array $functions){
    try{
      $method = $this->request->getMethod();
      $lead_ref = null;
      $action = $this->request->getParam('action');
      //find what method is expected for this action
      $expectedMethod = null;
      foreach ($functions as $key => $function) {
          if (in_array($action, $function)) {
              $expectedMethod = $key;
          }
      }
      if ($expectedMethod === null){
        return;
      }
      else if($expectedMethod !== $method){
        throw new \Exception("Only ".$expectedMethod." requests are allowed.", 405);
      }
      if($method  === 'POST'){
        $requestJsonData = $this->request->input("json_decode", true);
        $requestData = $this->request->getData();
        if(!isset($requestJsonData['lead_ref']) && !isset($requestData['lead_ref'])){
          throw new \Exception("lead_ref is required.");
        }
        $lead_ref = $requestJsonData['lead_ref'] ?? $requestData['lead_ref'];
      }
      else if($method  === 'GET'){
        $param = $this->request->getParam('pass'); 
        if(!isset($param[0])){
          throw new \Exception("lead_ref is required.");
        }
        $lead_ref = $this->request->getParam('pass')[0];
      }
      $lend_internal_auth = new LendInternalAuth;
      $lead_id = $lend_internal_auth->unhashLeadId($lead_ref);
      if (empty($lead_id)) {
        throw new \Exception("Cannot find a lead.");
      }
      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }
      $permission_check = $this->checkPermission($lead_id, null, $user['account_type'] === 'Applicant');
      if (!$permission_check['success']) {
        throw new \Exception($permission_check['message'], $permission_check['code'] ?? 0);
      }
    }
    catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      $response = new Response();
      return $response->withStatus($e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400)
        ->withType('application/json')
        ->withStringBody(json_encode(['message' => $e->getMessage()]));
    }
  }


  public function _getS3ViewImageRequestByPath($full_path) {
    try {
      if ($full_path) {
        $url = $this->createSignedRequest($full_path, '2');
      } else {
        throw new \Exception("No file found with that upload id");
      }

      return $url;
    } catch(\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }


}
