<?php

namespace LeadApisV2\Controller;

use App\Lend\LendInternalAuth;
use Cake\Http\Client;
use Cake\Log\Log;
use Exception;
use Hashids\Hashids;
use Cake\Core\Configure;

class SendToLenderController extends AppController
{
  public function initialize()
  {
    parent::initialize();
    $this->Auth->allow();
  }

  public function beforeFilter($event)
  {
    parent::beforeFilter($event);
    $functions = [
      'GET' => [],
      'POST' => ['run']
    ];
    $ret = $this->checkAccessToLead($functions);
    if (!empty($ret))
      return $ret;
  }

  public function checkDuplicate($lead_id, $product_id){
    $leads_table = $this->getTableLocator()->get('LeadEntity');
    $lead = $leads_table->get($lead_id, ['contain' => ['PocOwner']]);
    $lenderProduct = $this->getTableLocator()->get('LenderProductEntity')->get($product_id);
    $hasDuplicated = false;
    // Check: sales table
    $others_exist = $this->getTableLocator()->get('Sales')->getPreviousSalesUsingTheseDetails(
      array(
        'email' => $lead['owner_poc']['email'],
        'phone' => $lead['owner_poc']['phone'],
        'mobile' => $lead['owner_poc']['mobile'],
        'organisation_name' => $lead['organisation_name'],
      )
    );
    // Go through each of the records and see if it was sent to this lender before...
    if ($others_exist):
      foreach ($others_exist as $other):
        if($lenderProduct['lender_id'] != $other['lender_id'])
          continue;
        if (strtotime($other['created']) < strtotime('-14 days'))
          continue;
        $hasDuplicated = true;
      endforeach;
    endif;
    return $hasDuplicated;
  }

  /*
  {
    "lead_ref": "abc1234",
    "product_id": 391,
    "match_result_ref": "def5678",
    "send_anyway": false,
    "additional_note": "",
    "submission_type": false,
    "intermediary_lender_id": 109 // only for consumer leads sending to intermediary
  }
  */
  public function run()
  {
    try {
      Log::debug('---- SendToLenderController::run() START ----');
      $sent = $this->request->getData();
      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }
      Log::debug(json_encode($sent));

      if (!empty($sent['intermediary_lender_id'])) {
        // get intermediary lender's partner_id:
        $lenders_table = $this->getTableLocator()->get('LenderEntity');
        $intermediary = $lenders_table->get($sent['intermediary_lender_id'], [
          'contain' => [
            'LenderProductEntity',
          ],
        ]);

        if (!$intermediary->partner_id) {
          throw new Exception("Intermediary lender is not associated with a partner.");
        }

        $sent['product_id'] = $intermediary->lender_products[0]->lender_product_id;
      }

      if (empty($sent['lead_ref']) || empty($sent['product_id'])) {
        throw new Exception("Missing required fields.", 400);
      }
      $sent['partner_user_id'] = $user['partner_user_id'];
      $sent['lead_id'] = (new LendInternalAuth)->unhashLeadId($sent['lead_ref']);

      if (empty($sent['lead_id'])) {
        throw new Exception("Cannot find a lead.", 400);
      }

      if ($user['is_test_acc']) {
        Log::debug('This is a test account. Finish here.');
        return $this->setJsonResponse(['success' => true, 'sale_ref' => null]);
      }

      $leads_table = $this->getTableLocator()->get('LeadEntity');

      $accountLevelLeadAutoSend = false;
      if (!empty($sent['account_ref'])) {
        $lead = $leads_table->get($sent['lead_id']);
        if (strtolower($lead->send_type) === "auto") {
          $accountLevelLeadAutoSend = true;
          $leads_table->patchEntity($lead, ['send_type' => 'Manual']);
          $leads_table->save($lead);
        }
      }

      // check if it's off panel lender:
      if ((int)$sent['product_id'] === Configure::read('Lend.OFF_PANEL_LENDER_PRODUCT_ID')) {
        Log::debug('This is an off panel lender. Getting match result details from matching engine service.');
        $header = [
          'type' => 'json',
          'headers' => ['x-api-key' => getenv('MATCHING_ENGINE_KEY')]
        ];
        $response = (new Client)->get(getenv('DOMAIN_MATCHING_ENGINE') . "/get-match-results-by-match-refs/" . $sent['match_result_ref'], null, $header);
        $match_result = $response->getJson();
        Log::debug(json_encode($match_result));
        $sent['off_panel_lender'] = ['lender_name' => $match_result['data']['matches'][0]['lender_name']];
      }

      if(!$sent['send_anyway'] && !$sent['is_intermediary'] && $sent['product_id'] !== Configure::read('Lend.OFF_PANEL_LENDER_PRODUCT_ID')){
        $isDup = $this->checkDuplicate($sent['lead_id'], $sent['product_id']);
        if($isDup){
          throw new Exception("duplicated", 400);
        }
      }

      Log::debug('Sending to Star /api/partners/manual-send-to-lender');
      $authSig = (new LendInternalAuth)->createSignature($sent);
      $response = (new Client)->post(
        getenv('DOMAIN_CRM') . '/api/partners/manual-send-to-lender',
        json_encode($sent),
        ['headers' => ['Content-Type' => 'application/json', 'Webhook-Signature' => $authSig], 'timeout' => 60]
      );
      $result = $response->getJson();
      Log::debug('result from Star: ' . json_encode($result));

      if($result['success'] && $sent['is_intermediary'] && $sent['product_id']){
        if(isset($intermediary->partner_id)){
          $intermediaryPartnerId = $intermediary->partner_id;
        }else{
          $intermediaryPartnerId = $this->getTableLocator()->get('LenderProductEntity')->find('all', [
            'contain' => ['LenderEntity']
          ])->where(['lender_product_id' => $sent['product_id']])->first()->lender->partner_id;
        }
        $newLeadId = $this->getTableLocator()
                          ->get('IntermediaryLenderMappingEntity')
                          ->find('all')
                          ->where(['original_lead_id' => $sent['lead_id']])
                          ->order(['id' => 'desc'])
                          ->first()->new_lead_id;
        if($newLeadId && $intermediaryPartnerId){
          $newLead = $leads_table->get($newLeadId);
          if($newLead->lead_type == "consumer"){
            $referrer_person_id = $this->loadModel('ReferrerPeople')->createReferrerAndPersonFromLendParterUserIfNotExist($user['partner_user_id'], $intermediaryPartnerId);
            $leads_table->patchEntity($newLead, ['referrer_person_id' => $referrer_person_id]);
            $leads_table->save($newLead);
          }
        }
      }

      // last API error for the product_id 
      // onyl for tha account level lead
      if (!empty($sent['account_ref'])) {
        if (!$result['success']) {
          $hashids = new Hashids('partner_accounts', 7);
          $account_id = $hashids->decode($sent['account_ref'])[0];
          $partner_accounts_failure_leads_log_table = $this->getTableLocator()->get('PartnerAccountsFailureLeadsLogEntity');
          $partner_accounts_failure_leads_log = $partner_accounts_failure_leads_log_table->newEntity([
            'account_id' => $account_id,
            'lead_id' => $sent['lead_id'],
            'sent_status' => 0,
          ]);
          $partner_accounts_failure_leads_log_table->save($partner_accounts_failure_leads_log);
        } elseif ($accountLevelLeadAutoSend === true) {
          $leads_table->patchEntity($lead, ['send_type' => 'Auto']);
          $leads_table->save($lead);
        }
      }

      if (!$result['success']) {
        $display_error = $result['error'];
        $lead_sent_log_table = $this->getTableLocator()->get('LeadSentLogEntity');
        $lead_sent_logs = $lead_sent_log_table->find('all')
          ->where([
            'lead_id' => $sent['lead_id'],
            'product_id' => $sent['product_id'],
            'status' => 'failed'
          ])
          ->order(['lead_sent_log_id' => 'desc']);
        if (!empty($lead_sent_logs)) {
          $details = $lead_sent_logs->first()->response;
        }
        throw new Exception(json_encode(['error' => $display_error, 'details' => @$details]), 400);
      }

      // Update `con_page_status.con_lender_pricing_status` to `submitted` if it is a consumer lead:
      $con_page_status_table = $this->getTableLocator()->get('ConPageStatusEntity');
      $con_page_status = $con_page_status_table->find('all')->where(['lead_id' => $sent['lead_id']])->first();
      if (!empty($con_page_status)) {
        $con_page_status_table->patchEntity($con_page_status, [
          'con_lender_pricing_status' => 'submitted'
        ]);
        $con_page_status_table->save($con_page_status);
      }

      // get latest sales record
      $sales_table = $this->getTableLocator()->get('SaleEntity');
      $sale = $sales_table->find('all')->where(['lead_id' => $sent['lead_id'], 'product_id' => $sent['product_id']])->order(['sale_id' => 'desc'])->first();

      Log::debug('---- Sale Ref: ' . (!empty($sale->sale_ref) ? $sale->sale_ref : null));
      Log::debug('---- SendToLenderController::run() END ----');
      return $this->setJsonResponse(['sale_ref' => (!empty($sale->sale_ref) ? $sale->sale_ref : null)]);
    } catch (\Exception $e) {
      $error = $e->getMessage();
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      if (is_array(json_decode($error, true))) {
        return $this->setJsonResponse(json_decode($error, true), $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
      } else {
        return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
      }
    }
  }
}
