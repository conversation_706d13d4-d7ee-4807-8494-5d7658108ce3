<?php

namespace LeadApisV2\Controller;

use App\Enums\LendSignTemplateUse;
use App\Lend\LendSignatureServiceBackendClient;
use Cake\Collection\Collection;
use Cake\ORM\TableRegistry;
use Cake\Log\Log;
use Hashids\Hashids;
use Cake\Http\Client;
use App\Lend\LendInternalAuth;
use Cake\Datasource\ConnectionManager;
use Cake\Core\Configure;
use App\Lend\CurlHelper;
use Cake\Http\Response;
use Cake\I18n\FrozenTime;
use DateTime;
use DateTimeZone;
use Laminas\Diactoros\Response\JsonResponse;
use LeadApisV2\Controller\HemController;
use App\Lend\SignatureServiceRequestBuilder;
use App\Lend\LeadHelper;
use App\Lend\SignatureServiceFactory;
use App\Lend\EquifaxService;
use Cake\Datasource\Exception\RecordNotFoundException;
use Firebase\JWT\JWT;
use Cake\Utility\Security;

class LeadsController extends AppController
{
  public function initialize()
  {
    parent::initialize();
    $this->Auth->allow(['clone', 'importLead', 'sendSmsVerificationCode','getLeadOwnerForRetargetMarketing','verifySmsCode']);
    $this->loadComponent('AddressValidator');
  }

  public function beforeFilter($event)
  {
    parent::beforeFilter($event);
    $functions = [
      'GET' => ['view', 'getMissingFields', 'getMissingFieldsForInvoice', 'getDisplayInfoInvoice', 'getQuoteRef', 'getBsa', 'getBs', 'getBrokerDetails', 'getSentLenders'],
      'POST' => ['updateLoanDetails', 'updateLeadProperty',  'updateLeadAsset', 'updateLeadPricing', 'updateLenderPreferences', 'updateStatusAndReferrer', 'addLeadNotes', 'assignUser', 'saveBrokerFlowId', 'askApplicantEmail', 'updateApplicantResponse', 'finaliseClientChanges', 'updateBusinessDetail']
    ];
    $ret = $this->checkAccessToLead($functions);
    if (!empty($ret))
      return $ret;
  }

  public function convertFromConsumerQuote()
  {
    try {
      $data = $this->request->getData();
      if (
        empty($data['first_name'])
        || empty($data['last_name'])
        || (empty($data['mobile']) && empty($data['phone']))
        || empty($data['email'])
      ) {
        throw new \Exception('Required fields are missing.');
      }
      if (!empty($data['quote_ref'])) {
        list($sel_proposal, $quote) = $this->_getQuote($data['quote_ref']);
      }
      if (empty($quote) || empty($sel_proposal)) {
        throw new \Exception('Please run quick quote first.');
      }
      if (getenv('LEND_ENV') == 0) {
        $user = $this->Auth->user();
      } else {
        $user = $this->Auth->identify();
      }
      $lead_data = [
        'partner_id' => $user['partner_id'],
        'source' => 'con_quick_quote',
        'status_id' => 1,
        'partner_status_id' => 1,
        'amount_requested' => $quote['loan_amount'],
        'loan_term_requested_months' => $quote['loan_term'],
        'purpose_id' => $this->__mapPurpose($quote),
        'product_type_id' => $quote['product_type_id'] ?? 25,
        // new ids for consumer, not added yet
        'call_me_first' => true,
        'send_type' => 'Manual',
        'force_send' => false,
        'lead_type' => 'consumer',
        'owners_all' => [
          [
            'first_name' => $data['first_name'],
            'last_name' => $data['last_name'],
            'mobile' => @$data['mobile'],
            'phone' => @$data['phone'],
            'email' => $data['email'],
            'residency_status' => $quote['con_residency_status'],
            'credit_history' => $quote['credit_history'],
            'point_of_contact' => true,
            'monthly_gross_income' => ($quote['con_pre_tax_income_pa'] / 12),
            'monthly_net_income' => ($quote['con_after_tax_income_pa'] / 12),
            'all_addresses' => [
              [
                'living_status' => $quote['con_living_status'],
              ]
            ],
            'incomes' => [
              [
                'config_income_id' => 1,
                'amount' => (((strtolower($quote['con_income_gross_net']) === 'net') ? $quote['con_after_tax_income_pa'] : $quote['con_pre_tax_income_pa']) / 12),
                'gross_amount' => ($quote['con_pre_tax_income_pa'] / 12),
                'net_amount' => ($quote['con_after_tax_income_pa'] / 12),
                'net' => (strtolower($quote['con_income_gross_net']) === 'net')
              ]
            ],
            'all_employments' => [
              [
                'employment_type' => $quote['con_employ_status'],
              ]
            ]
          ]
        ],
        'pricing' => [
          'rate_discount' => $quote['con_dial_down'],
          'include_fees_requested' => $quote['fees_included']
        ],
        'con_credit_prop_fees' => [
          [
            'config_con_fee_id' => 1,
            'fixed_amount' => $quote['origination_fee'],
            'partner_user_id' => $user['partner_user_id'],
            'fee_name' => 'Origination Fee',
          ],
          [
            'config_con_fee_id' => 8,
            'fixed_amount' => $quote['brokerage_fee_value_type'] == "$" ? $quote['brokerage_fee'] : null,
            'percent_of_loan' => $quote['brokerage_fee_value_type'] == "%" ? $quote['brokerage_fee'] : null,
            'partner_user_id' => $user['partner_user_id'],
            'fee_name' => 'Commission to Broker Firm',
          ]
        ],
        'lead_quote_ref' => [
          'quote_ref' => $data['quote_ref'],
          'has_accepted_proposal' => !empty($sel_proposal)
        ]
      ];

      if (!empty($user['partner_user_id'])) {
        $lead_data['partner_user_lead'] = [
          'partner_user_id' => $user['partner_user_id'],
          'status' => 'ACCESS'
        ];
      }

      if (!empty($quote['asset_id'])) {
        $lead_data['asset_finance'] = [
          'equipment_id' => $quote['asset_id'],
          'year' => $quote['year'],
          'condition' => $quote['condition'],
          'supplier_type' => $quote['source']
        ];
      }

      if (!empty($quote['con_purpose_child']) && $quote['con_purpose_child'] != "Personal Loan")  {
        $lead_data['asset_finance']['finance_amount'] = $quote['loan_amount'];
      }

      $lead = TableRegistry::getTableLocator()->get('LeadEntity')->createLead($lead_data);
      return $this->setJsonResponse(['lead_ref' => $lead->lead_ref]);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  // Get previous sales history
  private function _getPrevSales($lead_id)
  {
    $prev_sales = $this->_getWhoSent($this->loadModel('Sales')->getSalesWithLenderDetails($lead_id, false));

    return !empty($prev_sales) ? $prev_sales : [];
  }

  private function _getWhoSent($prev_sales)
  {
    if (empty($prev_sales))
      return $prev_sales;

    $this->loadModel('PartnerLeadHistory');

    // Get extra partner lead history and partner user details
    $partnerLeadHistoriesBySaleId = $this->PartnerLeadHistory->getPartnerLeadHistoryWithPartnerDetails(array_column($prev_sales, 'sale_id'));

    // If sender is found in partner lead history, the leads is sent via platform
    // otherwise sent by Star
    foreach ($prev_sales as $key => $sale) {
      if (!empty($prev_sales[$key]['off_panel_lender']))
        $prev_sales[$key]['off_panel_lender'] = json_decode($prev_sales[$key]['off_panel_lender'], true);
      if (!empty($partnerLeadHistoriesBySaleId[$sale['sale_id']])) {
        $prev_sales[$key]['who_sent'] = ucwords($partnerLeadHistoriesBySaleId[$sale['sale_id']]['name']);
      } else {
        $prev_sales[$key]['who_sent'] = 'Lend';
      }
    }
    return !empty($prev_sales) ? $prev_sales : [];
  }

	protected function checkLeadServiceability($lead){
    $result = HemController::getHemData($lead['lead_ref']);
    $monthlySurplus = $result['monthly_surplus'];
    $monthlySurplusAdjusted = $result['adjusted_monthly_surplus'];
    if($result['serviceability']){
      return ["result" => true, "field_name" => $result['field_name']];
    }
		return ["result" => false, "field_name" => "override_surplus", "error" => "Serviceability rules not met. The lowest of Monthly Surplus($monthlySurplus) and Monthly Surplus adjusted based on HEM($monthlySurplusAdjusted) need to be greater than $1 "];
	}

	protected function checkLeadExpensesBreakdownSum($lead){
		if(!empty($lead['expenses'])){
			foreach ($lead['expenses'] as $ik => $expense) {
				if($expense['amount'] && $expense['lead_owner_expenses_breakdown']){
					$totalBreakdown = 0;
					foreach ($expense['lead_owner_expenses_breakdown'] as $sk => $breakdown) {
						$totalBreakdown += $breakdown['amount'];
					}
					if($totalBreakdown > $expense['amount']){
						return ["result" => false, "error" => "Total breakdown of ".$expense['config_expense']['expenses_type']." ($totalBreakdown) is greater than expense amount ($expense[amount])"];
					}
				}
			}
		}
		return ["result" => true];
	}
  protected function validateLeadForLenderMatch($lead_id){
		try {
			$lead_table = TableRegistry::getTableLocator()->get('LeadEntity');
			$associated = [
				'Owners', 'PocOwner', 'PocOwner.OwnerAllEmployments', 'PocOwner.OwnerAllAddresses', 'Owners.OwnerAllEmployments', 'Owners.OwnerAllAddresses', 'Owners.CurrentAddress',
				'Owners.LendSignatureRequestEntity', 'Owners.ConCreditPropSendEntity', 'Owners.ConPrelimOwnerEntity',
				'Owners.PartnerLeadUploadsEntity', 'Owners.PartnerLeadUploadsEntity.PartnerLeadUploadsMetaEntity',
				'LeadOwnerIncomeEntity', 'LeadOwnerIncomeEntity.ConfigIncomeEntity', 'LeadOwnerIncomeEntity.ConIncomeShareEntity',
				'LeadOwnerExpenseEntity', 'LeadOwnerExpenseEntity.ConfigExpenseEntity', 'LeadOwnerExpenseEntity.LeadOwnerExpensesBreakdownEntity',
				'LeadAssetFinanceEntity',
				'LeadAssetsEntity', 'LeadAssetsEntity.ConAssetShareEntity',
				'LeadLiabilitiesEntity', 'LeadLiabilitiesEntity.LeadAssetsEntity', 'LeadLiabilitiesEntity.ConLiabilityShareEntity',
				'LeadReferenceEntity',
				'FrmPurposeEntity',
				'AllAddresses',
				'LeadAbnLookupEntity',
				'EntityTrustEntity',
				'ConPageStatusEntity',
				'ConPrelimEntity',
				'ConReqAndObjEntity',
				'ConCreditPropFeeEntity', 'ConCreditPropFeeEntity.ConfigConFeeEntity',
				'ConRequirementEntity', 'ConRequirementEntity.ConfigRequirementEntity',
				'LeadPricingEntity',
				'LeadLenderMatchEntity',
				'SelectedLenderMatch', 'SelectedLenderMatch.LenderProductEntity',  'SelectedLenderMatch.LenderProductEntity.LenderEntity',
				'PartnerLeadUploadsEntity', 'PartnerLeadUploadsEntity.PartnerLeadUploadsMetaEntity',
				'LeadQuoteRefEntity'
			];
			$lead = $lead_table->get($lead_id, [
				'contain' => $associated
			]);
			$serviceability = $this->checkLeadServiceability($lead);
			$expensesBreakdownSum = $this->checkLeadExpensesBreakdownSum($lead);
			$missingFields = $this->checkLeadMissingField($lead);
			if($serviceability['result'] === false){
				$missingFields[] = ['message' => $serviceability['error'], 'route' => 'income-expenses'];
			}
			if($expensesBreakdownSum['result'] === false){
				$missingFields[] = ['message' => $expensesBreakdownSum['error'], 'route' => 'income-expenses'];
			}
			return $this->setJsonResponse(['missing_fields' => $missingFields]);
		} catch (\Exception $e) {
			Log::error($e->getMessage());
			Log::error($e->getTraceAsString());
			return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
		}
	}

  protected function formatConCreditGuideQuotes($leadOwners) {
    $conCreditGuideQuotes = [];
    foreach ($leadOwners as $owner) {
      if (!empty($owner['con_credit_guide_quotes'])) {
        foreach ($owner['con_credit_guide_quotes'] as $quote) {
          $conCreditGuideQuotes[] = $quote;
        }
      }
    }
    $c = new Collection($conCreditGuideQuotes);
    return $c->sortBy('created')->toArray();
  }

  protected function logTime($lead_ref, $position = null){
    if(isset($_GET['log_time']) && "true" === $_GET['log_time']){
      $time = new DateTime('now', new DateTimeZone('Australia/Sydney'));
      Log::info("lead_ref: ".$lead_ref." Position:".$position." Time: ".$time->format('Y-m-d H:i:s'));
    }
  }

  public function view($lead_ref, $returnArray = false)
  {
    try {
      $this->logTime($lead_ref, 1);
      $lend_internal_auth = new LendInternalAuth;
      $lead_id = $lend_internal_auth->unhashLeadId($lead_ref);
      TableRegistry::getTableLocator()->remove('LeadEntity');
      $lead_table = TableRegistry::getTableLocator()->get('LeadEntity', [
        'connection' => ConnectionManager::get('reader_db')
      ]);
      $associated = [
        'PartnerEntity' => ['fields' => ['partner_id']],
        'PartnerEntity.PartnerFeatureFlagEntity' => ['fields' => ['id', 'partner_id', 'access_to_kanban_v2', 'LEAD_CHECKLISTS']],
        'LeadChecklistEntity',
        'LeadChecklistEntity.PartnerChecklistEntity' => ['fields' => ['checklist_name', 'checklist_description']],
        'LeadChecklistEntity.LeadChecklistItemEntity',
        'LeadChecklistEntity.LeadChecklistItemEntity.PartnerChecklistItemEntity' => ['fields' => ['item_name', 'item_description', 'order', 'created', 'updated']],
        
        'PartnerAccountEntity.PartnerAccountMetaEntity',
        'PartnerAccountLinkPeopleEntity.PartnerAccountEntity' => [
          'LeadAbnLookupEntity' => [
            'fields' => [
              'abn', 'organisation_name', 'business_name', 'gst_effective_from',
              'state', 'entity_type_desc', 'effective_from', 'entity_status'
            ]
          ],
          'PartnerAccountMetaEntity' => [
            'fields' => ['partner_account_id', 'organisation_name', 'business_name'],
            'ConfigIndustryEntity' => ['fields' => ['tree']],
          ]
        ],
        'Owners' => [
          'OwnerAllEmployments',
          'OwnerAllEmployments.LeadAbnLookupEntity',
          'OwnerAllEmployments.NzbnLookupEntity',
          'OwnerAllAddresses',
          'CurrentAddress',
          'MailingAddress',
          'SettlementPostAddress',
          'ConCreditGuideQuote' => [
            'PartnerUserEntity' => [
              'fields' => [
                'partner_user_id',
                'partner_user_ref',
                'name',
              ]
            ],
          ],
          'ConCreditPropSendEntity' => [
            'PartnerUserEntity' => [
              'fields' => [
                'partner_user_id',
                'partner_user_ref',
                'name',
              ]
            ],
          ],
          'ConPrelimOwnerEntity',
          'PartnerLeadUploadsEntity' => [
            'PartnerLeadUploadsMetaEntity',
          ],
          'CheckMobile' => ['fields' => ['id', 'value', 'result', 'next_checkable', 'CheckMobile__checked' => 'created']],
          'CheckEmail' => ['fields' => ['id', 'value', 'result', 'next_checkable', 'CheckEmail__checked' => 'created', 'CheckEmail__disposable' => 'email_disposable']],
          'LeadCreditScoreEntity' => [
            'queryBuilder' => function ($q) {
              return $q->where(['LeadCreditScoreEntity.source' => 'broker']);
            }
          ],
          'LeadOwnerIncomeEntity',
          'HomeLoanPayOffOption',
          'LatestIdCheck',
        ],
        'LeadOwnerIncomeEntity' => [
          'ConfigIncomeEntity',
          'ConIncomeShareEntity',
        ],
        'LeadOwnerExpenseEntity' => [
          'ConfigExpenseEntity',
          'LeadOwnerExpensesBreakdownEntity',
        ],
        'LeadAssetFinanceEntity',
        'LeadAssetsEntity' => [
          'ConAssetShareEntity',
          'ConfigAssetTypeEntity',
        ],
        'LeadLiabilitiesEntity' => [
          'LeadAssetsEntity',
          'ConLiabilityShareEntity',
          'ConfigLiabilityEntity',
        ],
        'LeadReferenceEntity',
        'LeadPreferredLenderEntity',
        'FrmPurposeEntity',
        'AllAddresses',
        'LeadAbnLookupEntity',
        'NzbnLookupEntity',
        'EntityTrustEntity',
        'ConPageStatusEntity',
        'ConPrelimEntity' => [
          'PartnerUserEntity' => [
            'fields' => [
              'partner_user_id',
              'partner_user_ref',
              'name',
            ]
          ],
        ],
        'ConReqAndObjEntity' => [
          'PartnerUserEntity' => [
            'fields' => [
              'partner_user_id',
              'partner_user_ref',
              'name',
            ]
          ],
        ],
        'ConCreditPropFeeEntity' => [
          'ConfigConFeeEntity',
        ],
        'ConRequirementEntity' => [
          'ConfigRequirementEntity',
        ],
        'LeadPricingEntity',
        'LeadLenderMatchEntity' => [
          'LenderProductEntity' => [
            'LenderEntity',
          ],
        ],
        'SelectedLenderMatch' => [
          'LenderProductEntity' => [
            'LenderEntity',
          ],
        ],
        'PartnerLeadUploadsEntity' => [
          'PartnerLeadUploadsMetaEntity',
        ],
        'LeadQuoteRefEntity',
        'LeadNotesEntity' => [
          'PartnerUserEntity' => ['fields' => ['name']]
        ],
        'PartnerCallbackEntity' => [
          'PartnerUserEntity' => [
            'fields' => ['name']
          ]
        ],
        'PartnerUserLeadsEntity' => [
          'fields' => ['partner_user_id', 'status', 'granted']
        ],
        'PartnerUserLeadsEntity.PartnerUserEntity' => ['fields' => ['partner_user_id', 'name', 'kanban_colour']],
        'PartnerUserLeadsEntity.PartnerUserEntity.ConPartnerUserSettingsEntity' => ['fields' => [
          'partner_user_id',
          'is_bid',
          'hl_no_sig_cg',
          'cg_upfront_comms_range_min',
          'cg_upfront_comms_range_max',
          'cg_trail_comms_range_min',
          'cg_trail_comms_range_max',
          'cg_vb_comms_range_min',
          'cg_vb_comms_range_max'
        ]],
        'AllPartnerUserLeadsEntity' => [
          'fields' => ['partner_user_id', 'status', 'granted', 'partner_user_lead_id', 'lead_id'],
          'PartnerUserEntity' => ['fields' => ['partner_user_id', 'name', 'kanban_colour']]
        ],
        'SaleEntity' => [
          'conditions' => ['SaleEntity.is_active' => 1],
          'queryBuilder' => function ($q) {
            return $q->where(['SaleEntity.status != 0']);
          },
          'DealsEntity' => [
            'conditions' => ['DealsEntity.is_active' => 1],
            'LendStatusEntity',
          ],
        ],
        'PartnerCommissionEntity' => [
          'conditions' => ['PartnerCommissionEntity.is_active' => 1],  // this filter all the associated data //
          'SettlementReviewsEntity',
          'SaleOutcomesEntity' => [
            'SaleEntity' => [
                'SaleDetailEntity'
            ]
          ]
        ],
        'SettlementReviewsEntity' => [
          'conditions' => ['SettlementReviewsEntity.is_active' => 1],
        ],
        'ManStatusHistoryEntity' => [
          'PartnerUserEntity' => [
            'fields' => ['name'],
          ],
          'ManStatusEntity' => [
            'ManStatusGroupEntity',
          ],
        ],
        'IntermediaryLenderMappingEntity' => [
          'fields' => ['id', 'lead_info', 'created','original_lead_id','new_lead_id'],
          'OriginalLead' => [
            'fields' => ['lead_id', 'partner_id', 'lead_ref', 'partner_status_id'],
            'PartnerEntity' => [
              'fields' => ['partner_id', 'organisation_name', 'company_name',]
            ],
            'LendStatusEntity' => [
              'fields' => ['lend_status_id', 'status_name']
            ],
            'PartnerCommissionEntity' => [
              'queryBuilder' => function ($q) {
                return $q->where(['PartnerCommissionEntity.is_intermediary' => 1]);
              }
            ]
          ],
        ],
        'LeadNotificationPartnerUsersEntity' => [
          'fields' => ['lead_notification_partner_users_id', 'lead_id', 'partner_user_id', 'created'],
          'conditions' => ['LeadNotificationPartnerUsersEntity.status' => 'ACCESS'],
          'PartnerUserEntity' => [
            'fields' => ['partner_user_id', 'name', 'email', 'mobile']
          ]
        ],

        'SentToIntermediary' => [
          'fields' => ['id', 'lead_info', 'created',],
          'NewLead' => [
            'fields' => ['lead_id', 'partner_id', 'lead_ref',],
            'PartnerEntity' => [
              'fields' => ['partner_id', 'organisation_name', 'company_name',]
            ],
            'LendStatusEntity' => [
              'fields' => ['lend_status_id', 'status_name', 'group_name',]
            ],
            'ManStatusEntity' => [
              'fields' => ['id', 'status_name', 'is_new_lead', 'is_settled',]
            ],
          ],
        ],
        'LeadCreditScoreEntity' => [
          'queryBuilder' => function ($q) {
            return $q->where(['LeadCreditScoreEntity.owner_id IS null', 'LeadCreditScoreEntity.source' => 'broker']);
          }
        ],
        'LenderMatchRequestEntity' => [
          'fields' => ['lead_id', 'match_ref', 'created', 'filter']
        ],
        'LeadHomeLoanDetail' => [
          'HomeLoanRefinanceReason' => [
            'fields' => ['id', 'name'],
          ],
          'HomeLoanFeature' => [
            'fields' => ['id', 'name']
          ]
        ],
        'LeadHomeLoanProperty',
        'LeadHomeLoanCompliance' => [
          'LeadHomeLoanComplianceFees',
          'LeadHomeLoanComplianceRefinanceRisks',
          'LeadHomeLoanComplianceScenarios',
          'LeadHomeLoanComplianceRequirements',
          "HomeLoanObjectives" => [
            'fields' => ['objective_name'],
          ],
        ],
        'LeadCallAttemptEntity' => [
          'PartnerUserEntity' => [
            'fields' => ['name']
          ]
        ],
        'PartnerLeadHistoryEntity' => [
          'fields' => ['partner_id', 'partner_user_id', 'lead_id', 'created', 'history_detail', 'ref_type', 'ref_id'],
          'PartnerUser' => [
            'fields' => ['name']
          ],
          'RefPartnerUser' => [
            'fields' => ['name', 'partner_user_id', 'email']
          ]
          ],
        'LeadMarketingEntity'=>[
          'fields'=>['utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content']
        ]
      ];
      $lead = $lead_table->get($lead_id, [
        'contain' => $associated
      ]);
      $this->logTime($lead_ref, 2);

      // Using the Latest Con Preliminary Owner relationship causes duplication.
      $equifaxService = new EquifaxService('ID Matrix');
      foreach ($lead['owners_all'] as &$owner) {
        $owner = $equifaxService->fillIdCheckInfo($lead['partner_id'], $lead['acn'], $owner, false);
        $owner['latest_con_preliminary_owner'] = null;
        if (!empty($owner['con_preliminary_owner'])) {
          $owner['latest_con_preliminary_owner'] = $owner['con_preliminary_owner'][0];
        }
        $owner['applicant_ref'] = ($owner['partner_account_people_id'] === null) ? null : $lend_internal_auth->hashPartnerAccountPeopleId($owner['partner_account_people_id']);
      }

      $lead['con_credit_guide_quotes'] = $this->formatConCreditGuideQuotes($lead['owners_all']);
      $this->logTime($lead_ref, 3);
      if (!empty($lead['assets'])) {
        foreach ($lead['assets'] as &$asset) {
          $assetType = $asset['asset_type']['asset_type'];
          $asset['is_personal'] = ($assetType === 'personal');
          $asset['is_business'] = ($assetType === 'business');
          unset($asset['asset_type']); // Unsetting the asset_type key
        }
      }

      if (!empty($lead['liabilities'])) {
        foreach ($lead['liabilities'] as &$liability) {
          $liabilityType = $liability['liability']['liability_type'];
          $liability['is_personal'] = ($liabilityType === 'personal');
          $liability['is_business'] = ($liabilityType === 'business');
          unset($liability['liability']); // Unsetting the liability key
        }
      }

      if (!empty($lead['settlement_reviews'])) {
        foreach ($lead['settlement_reviews'] as $k=>$settlement_review) {
          if (empty($settlement_review['commission_id']))
            unset($lead['settlement_reviews'][$k]);
        }
        $lead['settlement_reviews'] = array_values($lead['settlement_reviews']);
      }

      if (!empty($lead['intermediary_lender_mapping']['original_lead']['partner_commissions'][0]['partner_user_id'])) {
        $lead['intermediary_lender_mapping']['original_lead']['partner_commissions'][0]['confirmed_by'] = TableRegistry::getTableLocator()
        ->get('PartnerUserEntity')->find("all")
        ->where(["partner_user_id" => $lead['intermediary_lender_mapping']['original_lead']['partner_commissions'][0]['partner_user_id']])
        ->first()->name;
      }
      $this->logTime($lead_ref, 4);
      $lead = $this->__removeLeadId($lead);
      $this->logTime($lead_ref, 5);
      $serviceability = $this->checkLeadServiceability($lead);
      $lead['serviceability'] = $serviceability;
      $this->logTime($lead_ref, 6);
      $expensesBreakdownSum = $this->checkLeadExpensesBreakdownSum($lead);
      $lead['breakdown_sum_check'] = $expensesBreakdownSum['result'];
      if (!$expensesBreakdownSum['result']) {
        $lead['breakdown_sum_check_error'] = $expensesBreakdownSum['error'];
      }
      $this->logTime($lead_ref, 7);
      // if(!$returnArray)
      //   $this->validateLeadForLenderMatch($lead_id);
      $this->logTime($lead_ref, 8);
      $lead['account_ref'] = ($lead['account_id'] === null) ? null : $lend_internal_auth->hashPartnerAccountId($lead['account_id']);

      $options = @$lead['asset_finance']['options'];
      $lead['asset_finance']['options'] = json_decode($options);
      if (isset($lead['intermediary_lender_mapping']['original_lead']['lead_ref'])) {
        $originalLeadId = $lend_internal_auth->unhashLeadId($lead['intermediary_lender_mapping']['original_lead']['lead_ref']);
        $originalLeadPrevSales = $this->_getPrevSales($originalLeadId);
        $currentIntermediaryLenderId = TableRegistry::getTableLocator()->get('LenderEntity')
                                                                        ->find("all")
                                                                        ->where(["partner_id" => $lead['partner_id']])
                                                                        ->first()->lender_id;
        if(!empty($originalLeadPrevSales) && $currentIntermediaryLenderId){
          foreach($originalLeadPrevSales as $key => $originalLeadPrevSale){
            if($originalLeadPrevSale['lender_id'] == $currentIntermediaryLenderId){
              $lead['intermediary_lender_mapping']['original_lead']['prev_sales'][] = $originalLeadPrevSales[$key];
            }
          }
        }
        $lead['intermediary_lender_mapping']['original_lead']['prev_sales'] = [end($lead['intermediary_lender_mapping']['original_lead']['prev_sales'])];
      } else if (isset($lead['sent_to_intermediary']['new_lead']['lead_ref'])) {
        $prevSales = $this->_getPrevSales($lead_id);
        $currentIntermediaryLenderId = TableRegistry::getTableLocator()->get('LenderEntity')
                                                                        ->find("all")
                                                                        ->where(["partner_id" => $lead['sent_to_intermediary']['new_lead']['partner_id']])
                                                                        ->first()->lender_id;
        if(!empty($prevSales) && $currentIntermediaryLenderId){
          foreach($prevSales as $key => $originalLeadPrevSale){
            if($originalLeadPrevSale['lender_id'] == $currentIntermediaryLenderId){
              $lead['sent_to_intermediary']['prev_sales'][] = $prevSales[$key];
            }
          }
        }
        $lead['sent_to_intermediary']['prev_sales'] = [end($lead['sent_to_intermediary']['prev_sales'])];
      }
      $this->logTime($lead_ref, 9);
      if (!empty($lead['partner_account']['partner_account_meta'])) {
        $lead['partner_account_meta'] = $lead['partner_account']['partner_account_meta'];
      }

      if (!empty($lead['intermediary_lender_mapping']['original_lead_id'])) {
        $original_lead_id = $lead['intermediary_lender_mapping']['original_lead_id'];
        $PartnerUserLeadsEntity = TableRegistry::getTableLocator()->get('PartnerUserLeadsEntity');
        $partner_user_info_orginal_lead = $PartnerUserLeadsEntity->find()
          ->contain(['PartnerUserEntity' => function ($q) {
            return $q->select(['email', 'mobile','name']);
          }])
          ->where([
            'lead_id' => $original_lead_id,
            'status' => 'ACCESS'
        ])
          ->first();
        $lead['intermediary_lender_mapping']['original_lead']['partner_user_info'] = $partner_user_info_orginal_lead;
      }
      $this->logTime($lead_ref, 10);

      if (empty($lead['partner']['feature_flag']['LEAD_CHECKLISTS'])){
        unset($lead['lead_checklists']);
      }
      else{
        foreach($lead['lead_checklists'] as $i => $cl){
          if (is_array($lead['lead_checklists'][$i]['checklist_items']) && !empty($lead['lead_checklists'][$i]['checklist_items'])) {
            usort($lead['lead_checklists'][$i]['checklist_items'], function($a, $b) {
              return $a['partner_checklist_item']['order'] <=> $b['partner_checklist_item']['order'];
            });
          }
        }
      }
      unset($lead['partner']['feature_flag']['LEAD_CHECKLISTS']);


      if ($returnArray)
        return $lead;
      return $this->setJsonResponse($lead);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      if ($returnArray)
        return ['error' => $e->getMessage(), 'code' => $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400];
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }


  public function add()
  {
    try {
      if (!$this->request->is('post')) {
        throw new \Exception("Only POST request allowed.", 405);
      }

      $data = $this->request->getData();
      if (empty($data)) {
        throw new \Exception("No data provided.", 400);
      }

      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }

      $leadRef = LeadHelper::addLead($data, $user);
      return $this->setJsonResponse(['lead_ref' => $leadRef]);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  public function updateLeadProperty()
  {
    try {
      $db = ConnectionManager::get('default');
      $collection = $db->getSchemaCollection();
      $lead_asset_finance_schema = $collection->describe('lead_home_loan_properties');
      $schema = array_flip($lead_asset_finance_schema->columns());
      $schema['lead_ref'] = null;
      $lead_property_data = array_intersect_key($this->request->getData(), $schema);

      $saved_lead = $this->_saveLeadEntity($lead_property_data['lead_ref'], ["lead_home_loan_property" => $lead_property_data], ['LeadHomeLoanProperty']);
      return $this->setJsonResponse(array_intersect_key($saved_lead["lead_home_loan_property"], $lead_property_data));
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  public function updateLoanDetails()
  {
    try {
      $schema = [
        "lead_ref" => null,
        "additional_info" => null,
        "purpose_id" => null,
        "purpose_other" => null,
        "equipment_id" => null,
        "amount_requested" => null,
        "finance_amount" => null,
        "loan_term_requested_months" => null,
        "how_soon_id" => null,
        "product_type_id" => null,
        "private_lend_type" => null,
        "lead_owners" => [
          "security_type" => null,
          "security_type_detail" => null,
          "remaining_debt" => null,
          "estimated_value" => null,
          "owner_ref" => null,
          "lvr" => null,
        ],
        "private_lending_purpose_id" => null,
        "asset_finance" => [
          "asset_purchase_price" => null,
          "contract_type" => null,
        ],
        "books_package_is" => null,
        "invoice_amount_outstanding" => null,
        "number_of_invoices" => null,
        "existing_business" => null,
        "lead_home_loan_details" => [
          'id' => null,
          'loan_purpose' => null,
          'purchase_price' =>  null,
          'refinance_reasons' => null,
          'loan_amount_unsure' =>  null,
          'loan_amount' => null,
          'putting_deposit' => null,
          'deposit' => null,
          'loan_term' => null,
          'repayment_type' => null,
          'rate_type' => null,
          'repayment_frequency' => null,
          'loan_features' => null,
          'lvr' => null
        ]
      ];

      $data = $this->request->getData();

      // sepcial handling for e2e
      if (!empty($data['pricing'])) {
        if ($data['pricing']['application_type'] === 'basic') {
          $data['asset_finance']['contract_type'] = null;
        }
      }

      // Extract only the keys present in the schema
      $filteredData = [];
      foreach ($schema as $key => $value) {
        if (is_array($value)) { // Handle nested arrays
          $filteredData[$key] = array_intersect_key($data[$key] ?? [], $value);
        } else if (isset($data[$key])) {
          $filteredData[$key] = $data[$key];
        }
      }

      $lend_internal_auth = new LendInternalAuth;
      $lead_id = $lend_internal_auth->unhashLeadId($filteredData['lead_ref']);

      if (!empty($filteredData['finance_amount'])) {
        $filteredData['asset_finance']['finance_amount'] = $filteredData['finance_amount'];
      }

      if (!empty($filteredData['finance_amount']) && empty($filteredData['amount_requested'])) {
        $filteredData['amount_requested'] = $filteredData['finance_amount'];
      }

      if (!empty($filteredData['product_type_id'])) {
        if ($filteredData['product_type_id'] == 25) {
          unset($filteredData['amount_requested']);
        }
        // Personal loan Lead
        if ($filteredData['product_type_id'] == 26) {
          unset($filteredData['asset_finance']);
          //Remove Asset Finance record
          TableRegistry::getTableLocator()->get('LeadAssetFinanceEntity')->deleteAll(['lead_id' => $lead_id]);
        }
      }

      $lead_table = TableRegistry::getTableLocator()->get('LeadEntity');
      $entity = $lead_table->get($lead_id); // Retrieve the entity

      if (!empty($filteredData['lead_owners'])) {
        if (!empty($filteredData['lead_owners']['owner_ref'])) {
          $filteredData['owners_all'][0] = $filteredData['lead_owners'];
          $hashids = new Hashids('lead_owners', 7);
          $filteredData['owners_all'][0]['owner_id'] = $hashids->decode($filteredData['lead_owners']['owner_ref'])[0];
          $schema['owners_all'] = $schema['lead_owners'];
        }
        unset($filteredData['lead_owners']);
      }

      // Landry Data
      if (!empty($filteredData['lead_home_loan_details'])) {
        $home_loan_details = $filteredData['lead_home_loan_details'];
        $filteredData['lead_home_loan_details']['loan_amount_unsure'] = ($home_loan_details['loan_amount_unsure'] ?? false) ? 1 : 0;
        $filteredData['lead_home_loan_details']['putting_deposit'] = $home_loan_details['putting_deposit'] ? 1 : 0;
        $filteredData['purpose_id'] = $home_loan_details['loan_purpose'] == 'Refinance' ? 41 : 42;
        $filteredData['loan_term_requested_months'] = $home_loan_details['loan_term'];
        $filteredData['lead_home_loan_details']['deposit'] = $home_loan_details['putting_deposit'] ? $home_loan_details['deposit'] : 0;
        $filteredData['amount_requested'] = $home_loan_details['loan_amount'] - $filteredData['lead_home_loan_details']['deposit'];
      }

      $lead_table = TableRegistry::getTableLocator()->get('LeadEntity');
      $result = $lead_table->updateLeadEntity($lead_id, $filteredData);

      if (isset($result['owners_all']) && empty($result['owners_all'])) {
        unset($result['owners_all']);
      }

      return $this->setJsonResponse(is_array($result) ? array_intersect_key($result, $schema) : $result);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }



  public function updateLeadAsset()
  {
    try {
      $db = ConnectionManager::get('default');
      $collection = $db->getSchemaCollection();
      $lead_asset_finance_schema = $collection->describe('lead_asset_finance');
      $schema = array_flip($lead_asset_finance_schema->columns());
      $schema['lead_ref'] = null;
      $schema['cash_on_hand'] = null;
      $lead_asset_data = array_intersect_key($this->request->getData(), $schema);
      $lead_asset_data['options'] = json_encode($lead_asset_data['options']);
      // special lead field `cash_on_hand`:
      $cash_on_hand = @$lead_asset_data['cash_on_hand'];
      unset($lead_asset_data['cash_on_hand']);

      $saved_lead = $this->_saveLeadEntity($lead_asset_data['lead_ref'], ["cash_on_hand" => $cash_on_hand, "asset_finance" => $lead_asset_data], ['LeadAssetFinanceEntity']);
      return $this->setJsonResponse(array_intersect_key($saved_lead["asset_finance"], $lead_asset_data));
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  public function updateLenderPreferences()
  {
    try {
      $lead_ref = $this->request->getData('lead_ref');
      $lead_id = (new LendInternalAuth)->unhashLeadId($lead_ref);
      $preferences = $this->request->getData('preferences');
      $this->loadModel('LeadPreferredLender')->updateLenderPreferences($lead_id, $preferences);
      return $this->setJsonResponse(['success' => true]);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  public function updateLeadPricing()
  {
    try {
      $db = ConnectionManager::get('default');
      $collection = $db->getSchemaCollection();
      $lead_pricing_schema = $collection->describe('lead_pricing');
      $schema = array_flip($lead_pricing_schema->columns());
      $schema['lead_ref'] = null;
      $schema['additional_info'] = null;
      $lead_pricing_data = array_intersect_key($this->request->getData(), $schema);
      $additional_info = $lead_pricing_data["additional_info"];
      unset($lead_pricing_data["additional_info"]);
      $saved_lead = $this->_saveLeadEntity($lead_pricing_data['lead_ref'], ["pricing" => $lead_pricing_data, "additional_info" => $additional_info], ['LeadPricingEntity']);
      return $this->setJsonResponse(array_intersect_key($saved_lead["pricing"], $lead_pricing_data));
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  public function updateStatusAndReferrer()
  {
    try {
      $schema = [
        "lead_ref" => null,
        "partner_status_id" => null,
        "referer" => null,
        "referrer_person_id" => null,
        "man_status_id" => null,
        "is_closed" => null,
        "is_archived" => null,
        'call_me_first' => null,
        'send_type' => null,
      ];
      $data = array_intersect_key($this->request->getData(), $schema); //dump($data);die();
      $lend_internal_auth = new LendInternalAuth;
      $lead_id = $lend_internal_auth->unhashLeadId($data['lead_ref']);
      $lead_table = TableRegistry::getTableLocator()->get('LeadEntity');

      $reinstate_lead = null;
      if(isset($data['is_archived'])) {
        $reinstate_lead = $lead_table->reinstateLead($lead_id, 'archived');
      }
      $result = $lead_table->updateLeadEntity($lead_id, $data);

      return $this->setJsonResponse([
        'result' => is_array($result) ? array_intersect_key($result, $schema) : $result,
        'reinstate_lead' => $reinstate_lead
      ]);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }


  // Remove lead_id + owenr_id from the results
  protected function __removeLeadId($payload, $extra_keys = null, $unset_keys = null, $unset_objects = [])
  {
    $payload = json_decode(json_encode($payload), true);
    if (empty($unset_keys)) {
      $unset_keys = ['lead_id', 'owner_id', 'lead_owner_id', 'partner_account_id', 'partner_account_people_id', 'partner_user_id', 'sale_id', 'deal_id',];
    }
    if (!empty($extra_keys)) {
      $unset_keys = array_unique(array_merge($unset_keys, $extra_keys));
    }
    foreach ($payload as $data_source => $data) {
      if (is_string($data_source) && in_array($data_source, $unset_objects)) {
        unset($payload[$data_source]);
      } else {
        if (is_array($data)) {
          $payload[$data_source] = $this->__removeLeadId($data, $extra_keys, $unset_keys, $unset_objects);
        } elseif ($data_source !==0 && in_array($data_source, $unset_keys)) {
          unset($payload[$data_source]);
        }
      }
    }
    return $payload;
  }

  private function _getQuote($quote_ref)
  {
    // fetch quotes data
    $client = new Client;
    $sel_proposal = [];
    $quote = [];
    $url = getenv('QUOTES_LOOKUP') . 'quotes/' . $quote_ref;
    $response = $client->get($url, null, ['headers' => ['x-api-key' => getenv('QUOTES_API_KEY')]]);
    $result = $response->getJson();

    // find selected proposal
    if ($result['success']) {
      $proposals = $result['data']['proposals'];
      foreach ($proposals as $proposal) {
        if ($proposal['status'] == 'Selected') {
          $sel_proposal = $proposal;
          break;
        }
      }
      $quote = $result['data'];
      unset($quote['proposals']);
    }

    return [$sel_proposal, $quote];
  }

  protected function __mapPurpose($quote)
  {
    $purpose_table = TableRegistry::getTableLocator()->get('FrmPurposeEntity');
    if (!empty($quote['con_purpose_child'])) {
      $purpose = $purpose_table->find('all')->where(['purpose' => $quote['con_purpose_child']])->first();
    } elseif (!empty($quote['con_purpose_parent'])) {
      $purpose = $purpose_table->find('all')->where(['purpose' => $quote['con_purpose_parent']])->first();
    }

    if (!empty($purpose)) {
      return $purpose->purpose_id;
    }
    return null;
  }



  public function deleteLeadAsset()
  {
    try {
      if (!$this->request->is('post')) {
        throw new \Exception("Only POST request allowed.");
      }
      $data = $this->request->getData();
      $asset_id = $data['asset_id'];

      if ($asset_id) {
        $leadAssetsTable = TableRegistry::getTableLocator()->get('LeadAssetsEntity');
        $leadAsset = $leadAssetsTable->get($asset_id, ['contain' => ['LeadOwnersEntity']]);
        $leadId = $leadAsset->lead_id ?? $leadAsset->owner->lead_id;
        $user = $this->Auth->user();
        if (empty($user)) {
          $user = $this->Auth->identify();
        }

        $permission_check = $this->checkPermission($leadId, null, $user['account_type'] === 'Applicant');
        if (!$permission_check['success'])
          return $this->setJsonResponse(['success' => false, 'message' => $permission_check['message']]);

        $leadAssetsTable->patchEntity($leadAsset, ['status' => 'Deleted']);
        $leadAssetsTable->save($leadAsset);
        return $this->setJsonResponse(["success" => true, "message" => "deleted  asset"]);
      } else {
        return $this->setJsonResponse(["success" => false, "message" => "asset_id is required or invalid"]);
      }
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(["success" => false, "message" => $e->getMessage()], 400);
    }
  }

  public function deleteLeadliability()
  {
    try {
      if (!$this->request->is('post')) {
        throw new \Exception("Only POST request allowed.");
      }
      $data = $this->request->getData();
      $liability_id = $data['liability_id'];

      if ($liability_id) {
        $leadLiabilitiesTable = TableRegistry::getTableLocator()->get('LeadLiabilitiesEntity');
        $leadLiability = $leadLiabilitiesTable->get($liability_id, ['contain' => ['LeadOwnersEntity']]);

        $leadId = $leadLiability->lead_id ?? $leadLiability->owner->lead_id;
        $user = $this->Auth->user();
        if (empty($user)) {
          $user = $this->Auth->identify();
        }

        $permission_check = $this->checkPermission($leadId, null, $user['account_type'] === 'Applicant');
        if (!$permission_check['success'])
          return $this->setJsonResponse(['success' => false, 'message' => $permission_check['message']]);


        $leadLiabilitiesTable->patchEntity($leadLiability, ['status' => 'Deleted']);
        $leadLiabilitiesTable->save($leadLiability);
        return $this->setJsonResponse(["success" => true, "message" => "deleted  liability"]);
      } else {
        return $this->setJsonResponse(["success" => false, "message" => "liability_id is required or invalid"]);
      }
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(["success" => false, "message" => $e->getMessage()], 400);
    }
  }

  public function getMissingFields($lead_ref)
  {
    try {
      $lend_internal_auth = new LendInternalAuth;
      $lead_id = $lend_internal_auth->unhashLeadId($lead_ref);
      $lead_table = TableRegistry::getTableLocator()->get('LeadEntity');
      $associated = [
        'Owners',
        'PocOwner',
        'PocOwner.OwnerAllEmployments',
        'PocOwner.OwnerAllAddresses',
        'Owners.OwnerAllEmployments',
        'Owners.OwnerAllAddresses',
        'Owners.CurrentAddress',
        'Owners.LendSignatureRequestEntity',
        'Owners.ConCreditPropSendEntity',
        'Owners.ConPrelimOwnerEntity',
        'Owners.PartnerLeadUploadsEntity',
        'Owners.PartnerLeadUploadsEntity.PartnerLeadUploadsMetaEntity',
        'LeadOwnerIncomeEntity',
        'LeadOwnerIncomeEntity.ConfigIncomeEntity',
        'LeadOwnerIncomeEntity.ConIncomeShareEntity',
        'LeadOwnerExpenseEntity',
        'LeadOwnerExpenseEntity.ConfigExpenseEntity',
        'LeadOwnerExpenseEntity.LeadOwnerExpensesBreakdownEntity',
        'LeadAssetFinanceEntity',
        'LeadAssetsEntity',
        'LeadAssetsEntity.ConAssetShareEntity',
        'LeadLiabilitiesEntity',
        'LeadLiabilitiesEntity.LeadAssetsEntity',
        'LeadLiabilitiesEntity.ConLiabilityShareEntity',
        'LeadReferenceEntity',
        'FrmPurposeEntity',
        'AllAddresses',
        'LeadAbnLookupEntity',
        'EntityTrustEntity',
        'ConPageStatusEntity',
        'ConPrelimEntity',
        'ConReqAndObjEntity',
        'ConCreditPropFeeEntity',
        'ConCreditPropFeeEntity.ConfigConFeeEntity',
        'ConRequirementEntity',
        'ConRequirementEntity.ConfigRequirementEntity',
        'LeadPricingEntity',
        'LeadLenderMatchEntity',
        'SelectedLenderMatch',
        'SelectedLenderMatch.LenderProductEntity',
        'SelectedLenderMatch.LenderProductEntity.LenderEntity',
        'PartnerLeadUploadsEntity',
        'PartnerLeadUploadsEntity.PartnerLeadUploadsMetaEntity',
        'LeadQuoteRefEntity'
      ];
      $lead = $lead_table->get($lead_id, [
        'contain' => $associated
      ]);
      $missingFields = $this->checkLeadMissingField($lead);
      return $this->setJsonResponse($missingFields);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  public function addLeadNotes()
  {
    try {
      $lead_ref = $this->request->getData('lead_ref');
      $lend_internal_auth = new LendInternalAuth;
      $lead_id = $lend_internal_auth->unhashLeadId($lead_ref);
      $lead_notes_table = TableRegistry::getTableLocator()->get('LeadNotesEntity');
      $leadAssociatedDataTable = TableRegistry::getTableLocator()->get('LeadAssociatedDataEntity');
      $params['lead_id'] = $lead_id;
      $params['notes'] = $this->request->getData('notes');
      $params['created'] = date('Y-m-d H:i:s');
      $params['is_pinned'] = $this->request->getData('is_pinned') ?? false;
      $params['is_referrer'] = $this->request->getData('is_referrer') ?? false;
      $params['page_link'] = $this->request->getData('page_link') ?? false;
      $params['page_name'] = $this->request->getData('page_name') ?? false;

      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }
        $params['partner_user_id'] = $user['partner_user_id'];

      if($this->request->getData('add_to_original_lead')){
        $params['intermediary'] = 'to';
      }

      $lead_note = $lead_notes_table->newEntity($params);
      $lead_notes_table->save($lead_note);

      if($this->request->getData('add_to_original_lead') && $lead_note->note_id){
        $leadAssociatedData = $leadAssociatedDataTable->findByLeadId($lead_id)->first();
        $leadAssociatedData->max_intermediary_note_id = $lead_note->note_id;
        $leadAssociatedDataTable->save($leadAssociatedData);
      }

      if($this->request->getData('add_to_original_lead')){
        $lead_table = TableRegistry::getTableLocator()->get('LeadEntity');
        $associated = ['IntermediaryLenderMappingEntity'];
        $originalLead = $lead_table->get($lead_id, [
          'contain' => $associated
        ]);
        if($originalLead['intermediary_lender_mapping']['original_lead_id']){
          $params['lead_id'] = $originalLead['intermediary_lender_mapping']['original_lead_id'];
        }
        $params['intermediary'] = 'from';
        $original_lead_note = $lead_notes_table->newEntity($params);
        $lead_notes_table->save($original_lead_note);

        $originalLeadAssociatedData = $leadAssociatedDataTable->findByLeadId($originalLead['intermediary_lender_mapping']['original_lead_id'])->first();
        $originalLeadAssociatedData->max_intermediary_note_id = $original_lead_note->note_id;
        $leadAssociatedDataTable->save($originalLeadAssociatedData);
      }

      return $this->setJsonResponse(["success" => true, "message" => "added lead notes"]);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(["success" => false, "message" => $e->getMessage()], 400);
    }
  }

  public function getMissingFieldsForInvoice($lead_ref)
  {
    try {
      $lend_internal_auth = new LendInternalAuth;
      $lead_id = $lend_internal_auth->unhashLeadId($lead_ref);
      $lead_table = TableRegistry::getTableLocator()->get('LeadEntity');
      $associated = [
        'Owners',
        'PocOwner',
        'PocOwner.OwnerAllEmployments',
        'PocOwner.OwnerAllAddresses',
        'Owners.OwnerAllEmployments',
        'Owners.OwnerAllAddresses',
        'Owners.CurrentAddress',
        'Owners.LendSignatureRequestEntity',
        'Owners.ConCreditPropSendEntity',
        'Owners.ConPrelimOwnerEntity',
        'Owners.PartnerLeadUploadsEntity',
        'Owners.PartnerLeadUploadsEntity.PartnerLeadUploadsMetaEntity',
        'LeadAssetFinanceEntity',
        'LeadAssetsEntity',
        'LeadAssetsEntity.ConAssetShareEntity',
        'LeadQuoteRefEntity'
      ];
      $lead = $lead_table->get($lead_id, [
        'contain' => $associated
      ]);

      $missingFields = $this->checkLeadMissingField($lead, 'request_invoice');
      if (empty($lead['owner_poc']['all_addresses'][0]['full_address'])) {
        $missingFields[] = [
          'name' => 'all_addresses.0.date_from',
          'goto_field' => "all_addresses.0.date_from",
          'message' => 'Applicant ' . $lead['owner_poc']['full_name'] . 's Address',
          'route' => '../applicants?applicantId=' . $lead['owner_poc']['owner_ref']
        ];
      }
      $found_first_match = false;
      foreach ($missingFields as $key => $value) {
        if ($value['message'] == 'year' || $value['message'] == 'model' || $value['message'] == 'make') {
          if (!$found_first_match) {
            $missingFields[$key]['message'] = 'Vehicle Details (either make, model or year)';
            $found_first_match = true;
          } else {
            unset($missingFields[$key]);
          }
        }
      }
      $missingFields = array_values($missingFields);
      return $this->setJsonResponse($missingFields);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  public function getMissingFieldsForInvoiceConsumerV2($lead_ref)
  {
    try {
      $lend_internal_auth = new LendInternalAuth;
      $lead_id = $lend_internal_auth->unhashLeadId($lead_ref);
      $lead_table = TableRegistry::getTableLocator()->get('LeadEntity');
      $associated = [
        'Owners',
        'PocOwner',
        'PocOwner.OwnerAllEmployments',
        'PocOwner.OwnerAllAddresses',
        'Owners.OwnerAllEmployments',
        'Owners.OwnerAllAddresses',
        'Owners.CurrentAddress',
        'Owners.LendSignatureRequestEntity',
        'Owners.ConCreditPropSendEntity',
        'Owners.ConPrelimOwnerEntity',
        'Owners.PartnerLeadUploadsEntity',
        'Owners.PartnerLeadUploadsEntity.PartnerLeadUploadsMetaEntity',
        'LeadAssetFinanceEntity',
        'LeadAssetsEntity',
        'LeadAssetsEntity.ConAssetShareEntity',
        'LeadQuoteRefEntity'
      ];
      $lead = $lead_table->get($lead_id, [
        'contain' => $associated
      ]);

      $missingFields = $this->checkLeadMissingField($lead, 'request_invoice', 2);


      if (empty($lead['owner_poc']['all_addresses'][0]['full_address'])) {
        $missingFields[] = [
          'name' => 'all_addresses.0.date_from',
          'goto_field' => "all_addresses.0.date_from",
          'message' => 'Applicant ' . $lead['owner_poc']['full_name'] . 's Address',
          'route' => '../applicants?applicantId=' . $lead['owner_poc']['owner_ref']
        ];
      }

      $nameFields = [];
      $otherFields = [];
      $addressField = null;

      foreach ($missingFields as $field) {
        if ($field['name'] === 'all_addresses.0.date_from') {
            $addressField = $field;
        } else if (in_array($field['name'], ['first_name', 'last_name'])) {
            $nameFields[] = $field;
        } else {
            $otherFields[] = $field;
        }
      }

    $missingFields = $nameFields;
    if ($addressField) {
        $missingFields[] = $addressField;
    }
    $missingFields = array_merge($missingFields, $otherFields);


      if (!$this->validateVehicleDetailsOrQuoteRef($lead)) {
          $missingFields[] = [
              'name' => 'vehicle_or_quote_ref',
              'message' => 'Vehicle Details (either make, model year), or Quote Ref',
              'route' => ''
          ];
      }


      return $this->setJsonResponse($missingFields);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }


  public function validateVehicleDetailsOrQuoteRef($lead)
  {
    $vehicleDetailsProvided = !empty($lead['asset_finance']['make']) || !empty($lead['asset_finance']['model']) || !empty($lead['asset_finance']['year']);
    $quoteRefProvided = !empty($lead['asset_finance']['supplier_quote_ref']);

    if ($vehicleDetailsProvided || $quoteRefProvided) {
        return true;
    } else {
        return false;
    }
  }



  public function getDisplayInfoInvoice($lead_ref)
  {
    try {
      $lend_internal_auth = new LendInternalAuth;
      $lead_id = $lend_internal_auth->unhashLeadId($lead_ref);
      $lead_table = TableRegistry::getTableLocator()->get('LeadEntity');
      $associated = [
        'PocOwner',
        'LeadAssetFinanceEntity'
      ];
      $lead = $lead_table->get($lead_id, [
        'contain' => $associated
      ]);

      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }

      $owner_poc_email = $lead['owner_poc']['email'];
      $broker_email = $user['email'];
      $supplier_name = $lead['asset_finance']['supplier_contact_name'];
      $supplier_email = $lead['asset_finance']['supplier_email'];
      $display_info = [
        'owner_poc_email' => $owner_poc_email,
        'broker_email' => $broker_email,
        'supplier_name' => $supplier_name,
        'supplier_email' => $supplier_email
      ];
      return $this->setJsonResponse(array($display_info));
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  public function getQuoteRef($lead_ref)
  {
    try {
      $lend_internal_auth = new LendInternalAuth;
      $lead_id = $lend_internal_auth->unhashLeadId($lead_ref);
      $quote_ref = $this->loadModel('LeadQuotesRef')->getLeadQuoteRef(['lead_id' => $lead_id]);
      return $this->setJsonResponse($quote_ref);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(["success" => false, "message" => $e->getMessage()], 400);
    }
  }

  public function assignUser()
  {
    try {
      $data = $this->request->getData();
      $lead_id = (new LendInternalAuth)->unhashLeadId($data['lead_ref']);

      $hashids = new Hashids('partner_users', 7);
      $partner_user_id = $hashids->decode(@$data['partner_user_ref'])[0];

      $this->_assignUser([$lead_id], $partner_user_id);
      return $this->setJsonResponse($data);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  public function bulkAssignUser()
  {
    try {
      $data = $this->request->getData();
      if (empty($data['lead_refs'])) {
        throw new \Exception("lead_refs must be provided");
      }

      $lead_ids = [];
      foreach ($data['lead_refs'] as $lead_ref) {
        $lead_ids[] = (new LendInternalAuth)->unhashLeadId($lead_ref);
      }

      $hashids = new Hashids('partner_users', 7);
      $partner_user_id = $hashids->decode(@$data['partner_user_ref'])[0];

      $this->_assignUser($lead_ids, $partner_user_id);
      return $this->setJsonResponse(['success' => true]);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  private function _assignUser($lead_ids, $partner_user_id = null)
  {
    try {
      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }
      if (empty($user['account_admin']) && empty($user['permission_edit_assignee']) ) {
        throw new \Exception('You do not have access to this resource');
      }
      $lead_table = TableRegistry::getTableLocator()->get('LeadEntity');
      $leads = $lead_table->find('all')->where(['lead_id IN' => $lead_ids, 'partner_id' => $user['partner_id']]);
      if (empty($leads)) {
        throw new \Exception("Lead not found");
      }
      // Reset lead_ids
      $lead_ids = array_column($leads->toArray(), 'lead_id');

      //update partner_lead_users so that only new $partnerUserId has access to that lead
      $partner_user_leads_table = TableRegistry::getTableLocator()->get('PartnerUserLeadsEntity');

      if (empty($partner_user_id)) {
        return true;
      }

      $partner_users_table = TableRegistry::getTableLocator()->get('PartnerUserEntity');
      $active_users = $partner_users_table->find('all')->where(['partner_id' => $user['partner_id'], 'active' => true])->toArray();

      //check if the partnerUserId is contained in $active_users
      if (!in_array($partner_user_id, array_column($active_users, 'partner_user_id'))) {
        throw new \Exception('Selected user not found');
      }

      foreach ($lead_ids as $lead_id) {
        $new_partner_user_lead = $partner_user_leads_table->newEntity([
          'partner_user_id' => $partner_user_id,
          'lead_id' => $lead_id,
          'status' => 'ACCESS',
          'granted' => date('Y-m-d H:i:s'),
          'updated' => date('Y-m-d H:i:s'),
        ]);
        $partner_user_leads_table->save($new_partner_user_lead);
      }
      //email notification send from PartnerUserLeadsTable ===> afterSave()
      return true;
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      throw new \Exception($e->getMessage());
    }
  }

  public function sendToIntermediary()
  {
    try {
      TableRegistry::getTableLocator()->get('App')->postToSlack(":warning: Somewhere using this sending to intermediary API from broker platform. `/lead-apis/v2/leads/send-to-intermediary`", "lend_errors");
      $this->request->allowMethod(['POST']);
      $schema = [
        'original_lead_ref' => null,
        'intermediary_lender_id' => null,
        'note' => null,
      ];
      $data = array_intersect_key($this->request->getData(), $schema);

      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }
      if (empty($data['original_lead_ref']) || empty($data['intermediary_lender_id'])) {
        throw new \Exception("Missing required fields.");
      }
      $lend_internal_auth = new LendInternalAuth;
      $original_lead_id = $lend_internal_auth->unhashLeadId($data['original_lead_ref']);

      // get intermediary lender's partner_id:
      $lenders_table = TableRegistry::getTableLocator()->get('LenderEntity');
      $intermediary = $lenders_table->get($data['intermediary_lender_id'], [
        'contain' => [
          'PartnerEntity' => [
            'ManStatusEntity',
          ],
          'LenderProductEntity',
        ],
      ]);

      if(!$intermediary->partner_id){
        throw new \Exception("Intermediary lender is not associated with a partner.");
      }

      $original_lead = TableRegistry::getTableLocator()->get('LeadEntity')->get($original_lead_id);
      $extra_data = [
        'status_id' => 1,
        'partner_status_id' => 1,
        'man_status_id' => null,
        'partner_id' => $intermediary->partner_id,
        'call_me_first' => $intermediary->partner->call_me_first ?? true,
        'send_type' => $intermediary->partner->send_type?ucfirst($intermediary->partner->send_type):"Manual",
        'intermediary_lender_mapping' => [
          'original_lead_id' => $original_lead_id,
          'lead_info' => $data['note'],
        ],
        'intermediary_original_partner_id' => $original_lead->partner_id,
      ];
      if($original_lead->lead_type == "consumer"){
        $referrer_person_id = $this->loadModel('ReferrerPeople')->createReferrerAndPersonFromLendParterUserIfNotExist($user['partner_user_id'], $intermediary->partner_id);
        $extra_data['referrer_person_id'] = $referrer_person_id;
      }
      $new_lead = $this->_clone($original_lead_id, $extra_data);

      // Create a sale record
      $salesTable = TableRegistry::getTableLocator()->get('SaleEntity');
      $sale_data = [
        'lead_id' => $original_lead_id,
        'sender' => 7,
        'product_id' => $intermediary->lender_products[0]->lender_product_id,
        'product_type_id' => 25,
        // Consumer loan
        'status' => 1,
        'call_me_first' => true,
        'send_type' => 'Manual',
        'sent_from' => 'Partners',
        'send_anyway' => false,
        'partner_user_id'=>$user['partner_user_id'],
        'sale_outcomes' => [
          [
            'status' => 0,
            'received_time' => date('Y-m-d H:i:s'),
          ]
        ],
        'deals' => [
          [
            'status_id' => 1,
            'deal_number' => 1,
            'deal_type' => 'New',
          ]
        ]
      ];
      $sale_entity = $salesTable->newEntity($sale_data, [
        'associated' => [
          'SaleOutcomesEntity',
          'DealsEntity',
        ]
      ]);
      $salesTable->save($sale_entity);

      $lender_lead_updates_data = array(
        'created' => date('Y-m-d H:i:s'),
        'lead_id' => $original_lead_id,
        'product_id' => $intermediary->lender_products[0]->lender_product_id,
        'combined_status_string' => 'sent',
        'ref_id' => $sale_entity->sale_ref,
        'lender_modified_time' => date('Y-m-d H:i:s'),
      );
      $this->loadModel('LenderLeadUpdates')->addStatus($lender_lead_updates_data);

      return $this->setJsonResponse(['lead_ref' => $new_lead->lead_ref], 200);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  public function clone ($lead_ref)
  {
    try {
      if (empty($lead_ref)) {
        throw new \Exception("Missing required fields.");
      }
      $lead_id = (new LendInternalAuth)->unhashLeadId($lead_ref);
      if (empty($lead_id)) {
        throw new \Exception("Can't find a lead.");
      }

      $signature = $this->request->getHeaderLine('Webhook-Signature');
      if (!empty($signature)) {
        $extra_data = $this->request->getData();
        if (!(new LendInternalAuth)->checkSignature($signature, $extra_data)) {
          throw new \Exception("Invalid Signature.");
        }
        if ($extra_data['partner_id'] && $extra_data['intermediary_lender_mapping']['original_lead_id']) {
          $original_lead = TableRegistry::getTableLocator()->get('LeadEntity')->get($lead_id);
          if($original_lead->lead_type == "consumer"){
            $partner_user_id = $this->getReferrerPersonPartnerUserId($extra_data['intermediary_lender_mapping']['original_lead_id']);
            if ($partner_user_id) {
              $referrer_person_id = $this->loadModel('ReferrerPeople')->createReferrerAndPersonFromLendParterUserIfNotExist($partner_user_id, $extra_data['partner_id']);
              $extra_data['referrer_person_id'] = $referrer_person_id;
            }
          }
        }
      } else {
        $user = $this->Auth->user();
        if (empty($user)) {
          $user = $this->Auth->identify();
        }
        $permission_check = $this->checkPermission($lead_id, null, $user['account_type'] === 'Applicant');
        if (!$permission_check['success']) {
          throw new \Exception($permission_check['message']);
        }

        $extra_data = [
          'status_id' => 1,
          'partner_status_id' => 1,
          'man_status_id' => null,
          'duplicated_lead_ref' => $lead_ref
        ];
      }
      $new_lead = $this->_clone($lead_id, $extra_data);
      return $this->setJsonResponse(['lead_ref' => $new_lead->lead_ref], 200);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  public function searchByString()
  {
    try {
      if (!$this->request->is('post')) {
        throw new \Exception("only POST request is available.", 405);
      }
      if (getenv('LEND_ENV') == 0) {
        $user = $this->Auth->user();
      } else {
        $user = $this->Auth->identify();
      }
      if (!isset($user['partner_user_id'])) {
        throw new \Exception("Can't find a partner user.");
      }
      $searchString = $this->request->getData('search');
      if (is_numeric($searchString)) {
        if (strlen($searchString) < 5) {
          throw new \Exception("Please input more than 5 digits to search phone number.");
        }
        $where = ["(PocOwner.phone like '%" . $searchString . "%' OR PocOwner.mobile like '%" . $searchString . "%' )"];
      } else {
        if (strlen(str_replace(" ", "", $searchString)) < 3) {
          throw new \Exception("Please input more than 3 letters to search name.");
        }
        if ($searchString == trim($searchString) && strpos($searchString, ' ') !== false) {
          // if $searchString has space, check first_name and last_name separately
          $name = explode(" ", trim($searchString));
          $where = ["((PocOwner.first_name like '%" . $name[0] . "%' AND PocOwner.last_name like '%" . $name[1] . "%') OR organisation_name like '%" . $searchString . "%' OR business_name like '%" . $searchString . "%' OR lead_ref like '%" . $searchString . "%' )"];
        } else {
          $searchString = trim($searchString);
          $where = ["(PocOwner.first_name like '%" . $searchString . "%' OR PocOwner.last_name like '%" . $searchString . "%'  OR organisation_name like '%" . $searchString . "%' OR business_name like '%" . $searchString . "%' OR lead_ref like '%" . $searchString . "%' )"];
        }
      }
      $lead_table = TableRegistry::getTableLocator()->get('LeadEntity');
      $associated = [
        'PocOwner' => ['fields' => ['first_name', 'last_name', 'state', 'phone', 'mobile']],
        'PartnerProductTypeEntity' => ['fields' => ['product_type_id', 'product_type_name']],
        'PartnerUserLeadsEntity' => ['fields' => ['partner_user_lead_id']],
      ];
      $options = ['contain' => $associated];
      $options['fields'] = [
        'lead_ref',
        'organisation_name',
        'business_name',
        'created',
        'last_changed_date',
        'amount_requested',
        'lead_type',
        'source'
      ];
      $query = $lead_table->find('all', $options);
      if ($user['access_all_leads']) {
        $where['LeadEntity.partner_id'] = $user['partner_id'];
      } else {
        $where['LeadEntity.partner_id'] = $user['partner_id'];
        $where['PartnerUserLeadsEntity.partner_user_id'] = $user['partner_user_id'];
      }

      $query->where($where);
      $query->group("LeadEntity.lead_id");
      $leads = $query->toArray();
      return $this->setJsonResponse($leads);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  private function _clone($original_lead_id, $extra_data = null)
  {
    try {
      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }
      $lead_table = TableRegistry::getTableLocator()->get('LeadEntity');
      $associated = [
        'Owners' => [
          'OwnerAllEmployments',
          'OwnerAllEmployments.LeadAbnLookupEntity',
          'OwnerAllAddresses',
          'PartnerLeadUploadsEntity' => [
            'PartnerLeadUploadsMetaEntity',
          ],
          'MailingAddress',
          'SettlementPostAddress',
          'LeadOwnerHomeLoanPayOffOption',
        ],
        'LeadOwnerExpenseEntity' => [
          'LeadOwnerExpensesBreakdownEntity',
        ],
        'LeadAssetFinanceEntity',
        'LeadReferenceEntity',
        'AllAddresses',
        'LeadAbnLookupEntity',
        'EntityTrustEntity',
        'ConCreditPropFeeEntity',
        'PartnerLeadUploadsEntity' => [
          'PartnerLeadUploadsMetaEntity',
        ],
        'IntermediaryLenderMappingEntity',
        'LeadHomeLoanDetail' => [
          'LeadHomeLoanRefinanceReason',
          'LeadHomeLoanFeature',
        ],
        'LeadHomeLoanProperty',
      ];
      $lead = $lead_table->get($original_lead_id, [
        'contain' => $associated
      ])->toArray();

      if (isset($lead['campaign'])) {
        unset($lead['campaign']);
      }

      if ($lead['is_tick_and_flick']) {
        unset($lead['is_tick_and_flick']);
        unset($lead['tick_and_flick_agreed_date']);
      }

      $original_owners = $lead['owners_all'];

      if (!empty($extra_data)) {
        $lender_id = $extra_data['lender_id'];
        unset($extra_data['lender_id']);

        $lead = array_merge($lead, $extra_data);
        if($extra_data['intermediary_original_partner_id']){
          unset($lead['call_queue_status']);
        }
      }
      // Remove unnecessary fields - including PK , `created_at` and `updated_at`
      $lead = $this->__removeLeadId($lead, ['owner_ref', 'last_sent_by_user_id', 'last_completed_by_user_id', 'id', 'created_at', 'updated_at', 'partner_lead_upload_id', 'lead_owner_address_id', 'lead_owner_employment_id', 'partner_lead_upload_meta_id', 'abn_id', 'latest_commission_id']);
      $lead = $this->__removeComplianceDocs($lead);

      if (!empty($lender_id)) {
        $this->__trackDocSending($lead, $lender_id);
      }

      unset($lead['lead_ref']);
      if (!empty($user) && (empty($extra_data['partner_id']) || $extra_data['partner_id'] == $lead['partner_id'])) {
        $lead['partner_user_lead'] = [
          'partner_user_id' => @$user['partner_user_id'],
          'status' => 'ACCESS',
          'granted' => date('Y-m-d H:i:s'),
          'updated' => date('Y-m-d H:i:s'),
        ];
        // Prepend `Clone_` to the source if leads.source not already contain it:
        if (stripos($lead['source'], 'clone_') === false) {
          $lead['source'] = 'Clone_' . $lead['source'];
        }
      }
      $lead['is_closed'] = false;
      if($lead['is_archived'] == 1) {
        $lead['is_archived'] = 0;
      }
      $lead = $lead_table->createLead($lead, true);

      // Extra cloning
      $extra_associated = [
        'Owners',
        'LeadOwnerIncomeEntity',
        'LeadOwnerIncomeEntity.ConIncomeShareEntity',
        'LeadAssetsEntity',
        'LeadAssetsEntity.ConAssetShareEntity',
        'LeadLiabilitiesEntity',
        'LeadLiabilitiesEntity.LeadAssetsEntity',
        'LeadLiabilitiesEntity.ConLiabilityShareEntity',
      ];
      $extra = $lead_table->get($original_lead_id, [
        'contain' => $extra_associated
      ])->toArray();
      $extra = json_decode(json_encode($extra), true);

      $mappings = [
        'owner_id' => $this->_findOwnersId(json_decode(json_encode($lead->owners_all), true), $original_owners),
        'con_prelim_id' => [
          $extra['owners_all'][0]['con_preliminary_owner'][0]['con_prelim_id'] => $lead->con_preliminary[0]->id,
        ],
      ];
      $extra = $this->_reducePayload($extra);
      $extra = $this->_removeHistoryRecords($extra);
      $extra = $this->_replaceIds($mappings, $extra);
      $extra = $this->__removeLeadId($extra, null, ['id', 'lead_id', 'lead_asset_id', 'lead_liability_id', 'partner_account_id', 'partner_account_people_id',]);
      $lead = $lead_table->patchEntity($lead, $extra, [
        'associated' => $extra_associated,
      ]);
      $lead_table->save($lead);
      if($lead->is_archived === 2) {
        $lead_table->reinstateLead($lead->lead_id, 'archived');
      }

      return $lead;
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $e->getMessage();
    }
  }

  private function _findOwnersId($new_owners, $original_owners)
  {
    try {
      $result = [];
      foreach ($original_owners as $o) {
        foreach ($new_owners as $new) {
          if ($o['first_name'] === $new['first_name'] &&
              $o['last_name'] === $new['last_name'] &&
              $o['email'] === $new['email'] &&
              $o['mobile'] === $new['mobile']
            ) {
            $result[$o['owner_id']] = $new['owner_id'];
            break;
          }
        }
      }
      return $result;
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $e->getMessage();
    }
  }

  private function _reducePayload($extra)
  {
    try {
      $schema = [
        'liabilities' => null,
        'assets' => null,
        'incomes' => null,
        'owners_all' => null,
      ];
      $schema_owners = [
        'owner_id' => null,
        'con_preliminary_owner' => null,
      ];

      $extra = array_intersect_key($extra, $schema);
      // reduce owners object:
      foreach ($extra['owners_all'] as $key => $owner) {
        $extra['owners_all'][$key] = array_intersect_key($owner, $schema_owners);
      }
      return $extra;
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $e->getMessage();
    }
  }

  private function _removeHistoryRecords($payload)
  {
    // Remove all but the latest con_preliminary_owner.
    if (!empty($payload['owners_all'])) {
      foreach ($payload['owners_all'] as &$owner) {
        if (!empty($owner['con_preliminary_owner'])) {
          $owner['con_preliminary_owner'] = [$owner['con_preliminary_owner'][0]];
        }
      }
    }
    return $payload;
  }

  private function _replaceIds($mappings, $payload)
  {
    try {
      foreach ($payload as $field => $value) {
        if (is_array($value)) {
          $payload[$field] = $this->_replaceIds($mappings, $value);
        } elseif (in_array($field, ['owner_id', 'lead_owner_id']) && !empty($payload[$field])) {
          $payload[$field] = $mappings['owner_id'][$value];
        } elseif (in_array($field, ['con_prelim_id'])) {
          $payload[$field] = $mappings[$field][$value];
        }
      }
      return $payload;
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $e->getMessage();
    }
  }

  public function getBsa($lead_ref)
  {
    try {
      $lead_id = (new LendInternalAuth)->unhashLeadId($lead_ref);
      $lead_table = TableRegistry::getTableLocator()->get('LeadEntity');
      $lead = $lead_table->get($lead_id, [
        'fields' => 'account_id'
      ]);

      if (!$lead->account_id) {
        throw new \Exception("Can't find partner account.");
      }
      $lendconfig = Configure::read('Lend');
      $response = (new \Cake\Http\Client)->post(getenv('DOMAIN_BSS') . '/get-final-analysis-by-partner-account-id', ['partnerAccountId' => $lead->account_id, 'leadId' => $lead_id], ['headers' => ['x-api-key' => $lendconfig['bs_service_auth_key']]]);
      $allowedFields = [
        'avg_mto_180' => null,
        'avg_num_mth_deps_180' => null,
        'avg_day_end_bal_180' => null,
        'days_neg_180' => null,
        'days_dishonour_cnt_180' => null,
        'is_account_level'
      ];
      if ($response->json['success']) {
        return $this->setJsonResponse(!empty($response->json['data']) ? array_intersect_key($response->json['data'][0], $allowedFields) : false);
      } else {
        return $this->setJsonResponse(['error' => 'Get bank statements failed.'], 400);
      }
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  public function getBs($lead_ref)
  {
    try {
      $lead_id = (new LendInternalAuth)->unhashLeadId($lead_ref);
      $lead_table = TableRegistry::getTableLocator()->get('LeadEntity');
      $lead = $lead_table->get($lead_id, [
        'fields' => 'account_id'
      ]);

      if (!$lead->account_id) {
        throw new \Exception("Can't find partner account.");
      }
      $leadBrokerflowsTable = TableRegistry::getTableLocator()->get('LeadBrokerflowsEntity');
      $leadBankAccounts = $leadBrokerflowsTable
        ->find('all')
        ->where(['lead_id' => $lead_id, "status in ('brokerflow_submitted','bankfeeds_submitted')"])
        ->first();
      $bankAccounts = [];
      if (!empty($leadBankAccounts)) {
        if ($leadBankAccounts->status == 'brokerflow_submitted') {
          $bankAccounts[] = [
            'broker_flow_id' => $leadBankAccounts->broker_flow_id,
            'created' => $leadBankAccounts->created,
            'bank_name' => '',
            'account_name' => 'Brokerflow Id: ' . $leadBankAccounts->broker_flow_id,
            'account_number' => '',
            'account_bsb' => '',
            'account_balance' => '',
            'account_overdraft' => '',
            'is_active' => 1
          ];
        } else if ($leadBankAccounts->status == 'bankfeeds_submitted' && !empty($leadBankAccounts->bank_account_ids)) {
          $bankAccountIds = explode(",", $leadBankAccounts->bank_account_ids);
          $bankfeed_data = [
            'bankAccountIds' => $bankAccountIds,
          ];
          $curl = new CurlHelper(getenv('DOMAIN_BANKFEEDS') . "/get-bank-statements-by-bank-account-ids");
          $response = $curl->post($bankfeed_data, true, ["x-api-key" => getenv('BANK_STATEMENT_API_KEY')]);
          if ($response['success'] == true && !empty($response['data'])) {
            foreach ($response['data'] as $key => $bsid) {
              if (in_array($bsid['bank_statement_id'], $bankAccountIds)) {
                $bankAccounts[] = $bsid;
              }
            }
          }
        }
      }
      /* ****************************************************************************
      * TODO:: instead of calling 2 different apis, using lead_brokerflows.ref to get BS
      **************************************************************************** */
      $lendconfig = Configure::read('Lend');
      $response = (new \Cake\Http\Client)->post(getenv('DOMAIN_BSS') . '/statements-by-lead-id', ['leadId' => $lead_id], ['headers' => ['x-api-key' => $lendconfig['bs_service_auth_key']]]);
      if ($response->json['success'] && $response->json['data']) {
        $mergedAccounts = array_merge($bankAccounts, $response->json['data']);
        $removeDuplicatedAccounts = array_intersect_key($mergedAccounts, array_unique(array_column($mergedAccounts, 'bank_statement_id')));

        // format date:
        $removeDuplicatedAccounts = $this->_formatDate($removeDuplicatedAccounts);
        return $this->setJsonResponse(array_values($removeDuplicatedAccounts));
      }

      $curl = new CurlHelper(getenv('DOMAIN_BANKFEEDS') . "/get-customer-by-partner-account-id");
      $response = $curl->post(["partnerAccountId" => $lead->account_id], true, ["x-api-key" => getenv('BANK_STATEMENT_API_KEY')]);
      if ($response['success'] == true && !empty($response['data']) && !empty($response['data']['illion_error'])) {
        return $this->setJsonResponse(['error' => 'Error returned from Illion.', 'illion_error' => $response['data']['illion_error']], 400);
      }

      // format date:
      $bankAccounts = $this->_formatDate($bankAccounts);

      return $this->setJsonResponse($bankAccounts);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  private function _formatDate($bankAccounts) {
    return array_map(function ($bankAccount) {
      $bankAccount['created'] = date('Y-m-d H:i:s', strtotime(trim(preg_replace('/\T\Z/', ' ', $bankAccount['created']))));
      $bankAccount['updated'] = date('Y-m-d H:i:s', strtotime(trim(preg_replace('/\T\Z/', ' ', $bankAccount['updated']))));
      $bankAccount['submission_time'] = date('Y-m-d H:i:s', strtotime(trim(preg_replace('/\T\Z/', ' ', $bankAccount['submission_time']))));
      return $bankAccount;
    }, $bankAccounts);
  }

  /**
   * saveBrokerFlowId
   * referrerCode: A_xxxxxxx means account level, L_xxxxxxx means lead level
   *
   * @return void
   */
  public function saveBrokerFlowId()
  {
    try {
      $data = $this->request->getData();
      $leadBrokerflowsTable = TableRegistry::getTableLocator()->get('LeadBrokerflowsEntity');
      $leadBrokerflowData = [
        'lead_id' => (new LendInternalAuth)->unhashLeadId($data['lead_ref']),
        'broker_flow_id' => $data['documentId'],
        'status' => "brokerflow_submitted",
        'created' => date('Y-m-d H:i:s'),
        'updated' => null,
        'bank_account_ids' => '',
        'broker_flow_id_expired' => ''
      ];
      $leadBrokerflow = $leadBrokerflowsTable->newEntity($leadBrokerflowData);
      if ($leadBrokerflowsTable->save($leadBrokerflow)) {
        $id = $leadBrokerflow->id;
      }
      $sendData = ['referrerCode' => "L_" . $data['lead_ref'], "documentId" => $data['documentId']];
      $curl = new CurlHelper(getenv('DOMAIN_BANKFEEDS') . "/broker-flow");
      $response = $curl->post($sendData, true, ["x-api-key" => getenv('BANK_STATEMENT_API_KEY')]);
      if ($response['error']) {
        $leadBrokerflowsTable->patchEntity($leadBrokerflow, ['status' => 'brokerflow_failed']);
        $leadBrokerflowsTable->save($leadBrokerflow);
      }
      return $this->setJsonResponse($response);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  /**
   * Get broker(partner_user) details for a lead
   * @param mixed $lead_ref
   * @throws \Exception
   * @return void
   */
  public function getBrokerDetails($lead_ref)
  {
    try {
      $lead_id = (new LendInternalAuth)->unhashLeadId($lead_ref);
      $associated = [];
      $associated['PartnerUserLeadsEntity'] = ['fields' => ['partner_user_lead_id', 'partner_user_id']];
      $associated['PartnerUserLeadsEntity.PartnerUserEntity'] = ['fields' => ['partner_user_id', 'name', 'email', 'mobile', 'phone']];
      $options = ['contain' => $associated];
      $lead_table = TableRegistry::getTableLocator()->get('LeadEntity');
      $lead = $lead_table->get($lead_id, $options);
      if (empty($lead)) {
        throw new \Exception("Lead not found");
      }
      if (!is_null($lead['partner_user_lead']))
        $partnerUser = $lead['partner_user_lead']['user'];
      else
        $partnerUser = TableRegistry::getTableLocator()->get('PartnerUserEntity')->find('all', ['fields' => ['partner_user_id', 'name', 'email', 'mobile', 'phone']])->where(['partner_id' => $lead['partner_id'], 'point_of_contact' => 1])->first();
      $response = [
        'id' => $partnerUser->partner_user_id,
        'name' => $partnerUser->name,
        'phone' => $partnerUser->phone,
        'mobile' => $partnerUser->mobile,
        'email' => $partnerUser->email,
      ];
      return $this->setJsonResponse($response, 200);

    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      $this->setJsonResponse(['error' => "Unable to process request"], 400);
    }
  }

  public function updateLeadAskApplicantStatus(){
    try {
      $data = $this->request->getData();
      $leadId = (new LendInternalAuth)->unhashLeadId($data['lead_ref']);
      if($data['ask_applicant_status']){
        $lead_table = TableRegistry::getTableLocator()->get('LeadEntity');
        $leadAskApplicantPageStatus = ['ask_applicant_status' => $data['ask_applicant_status'], 'ask_applicant_last_action_time' => date('Y-m-d H:i:s')];
        $lead = $lead_table->get($leadId);
        $lead_table->patchEntity($lead, $leadAskApplicantPageStatus);
        $lead_table->save($lead);
      }
      return $this->setJsonResponse($leadAskApplicantPageStatus, 200);

    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      $this->setJsonResponse(['error' => "Unable to process request"], 400);
    }
  }

  private function _requestSignature($data, $leadId, $partnerId, $partnerUserId, $user){
    try {
      $lendSignatureRequestTable = TableRegistry::getTableLocator()->get('LendSignatureRequestEntity');
      $leadOwnersTable = $this->getTableLocator()->get('LeadOwnersEntity');
      foreach ($data['electronic_signature_documents'] as $document) {
        [$templateUseShortName, $templateRef] = explode('|', $document);
        $requestParams = [
          'contextType' => 'lead',
          'leadRef' => $data['lead_ref'],
          'ref' => $data['lead_ref'],
        ];
        if($data['electronic_signature_configuration'][$document]){
          $requestOwnerIds = [];
          $requestVia = [];
          $ownerRefs = $data['electronic_signature_configuration'][$document]['owners'];
          foreach ($ownerRefs as $ownerRef) {
            $ownerId = (new LendInternalAuth)->unhashOwnerId($ownerRef);
            $requestOwnerIds[] = $ownerId;
            $requestVia[$ownerId] = [
              "ref" => $ownerRef,
              "via_sms" => false,
              "via_email" => false,
              "via_link" => true
            ];
          }
          $requestParams['requestOwnerIds'] = $requestOwnerIds;
          $requestParams['via'] = $requestVia;
          if (!in_array($templateUseShortName, [LendSignTemplateUse::CreditGuideAndQuote, LendSignTemplateUse::CommercialPrivacyForms])){
            $requestParams['templateRef'] = $templateRef;
          }
          $sign = new SignatureServiceFactory($user, $templateUseShortName);
          $newEnvelope = $sign->callService($requestParams);
          if (is_array($newEnvelope) && !empty($newEnvelope['success']) && $newEnvelope['success'] == true) {
            $envelopeId = $newEnvelope['data'][0]['envelopeId'];
            $links = $newEnvelope['data'][0]['links'];
            $templateRef = $newEnvelope['data'][0]['templateRef'];
            foreach ($requestVia as $ownerId => $via) {
              $linkKey = array_search($via['ref'], array_column($links, 'ownerRef'));
              $ownerEntity = $leadOwnersTable->find('all', ['fields' => ['owner_id', 'first_name', 'last_name', 'email', 'mobile']])->where(['lead_id' => $leadId, 'owner_id' => $ownerId])->first();
              $lendSignatureRequestRecord = [
                'template_use_shortname' => $templateUseShortName,
                'template_ref' => $templateRef,
                'template_name' => $data['electronic_signature_configuration'][$document]['template_name'],
                'partner_id' => $partnerId,
                'partner_user_id' => $partnerUserId,
                'lead_id' => $leadId,
                'owner_id' => $ownerId,
                'status' => 'sent',
                'recipient_name' => $ownerEntity->full_name,
                'recipient_email' => $ownerEntity->email,
                'recipient_mobile' => $ownerEntity->mobile,
                'sent_time' => date('Y-m-d H:i:s'),
                'service_envelope_id' => $envelopeId,
                "via_sms" => false,
                "via_email" => false,
                "via_link" => true,
                "link" => $links[$linkKey]['shortLink'],
                'created' => date('Y-m-d H:i:s'),
                'updated' => date('Y-m-d H:i:s'),
              ];
              $lendSignatureRequest = $lendSignatureRequestTable->newEntity($lendSignatureRequestRecord);
              $lendSignatureRequestTable->save($lendSignatureRequest);
            }
          }
        }
      }
      return true;
    } catch (\Throwable $th) {
      Log::error($th->getMessage());
      Log::error($th->getTraceAsString());
      return false;
    }
  }

  /**
   * Send applicant(lead poc) an email to ask to complete the application and/or upload documentation
   * @return void
   */
  public function askApplicantEmail()
  {
    try {
      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }
      $partnerId = (int) $user['partner_id'];
      $partnerUserId = (int) $user['partner_user_id'];
      $accountType = $user['account_type'];

      $data = $this->request->getData();
      $getUrl = (isset($data['get_url']) && ($data['get_url'] === true));

      $data['send_type'] = isset($data['send_type']) ? $data['send_type'] : ['email'];
      $data['send_type'] = is_array($data['send_type']) ? $data['send_type'] : explode(',', $data['send_type']);

      $errorMessage = '';
      $successMessage = '';

      if (
        (!empty($partnerId) && $partnerId === 2)
        || ($accountType !== 'Lend Staff' && empty($partnerId))
        || (($getUrl !== true) && empty($partnerUserId))
      ) { //demo account or un-authorized
        return $this->setJsonResponse(array('success' => false));
      }

      $leadId = (new LendInternalAuth)->unhashLeadId($data['lead_ref']);
      $lead_table = TableRegistry::getTableLocator()->get('LeadEntity');
      $lead = $lead_table->get($leadId);
      $lendConfig = Configure::read('Lend');

      $configKey = $lead->is_home_loan ? 'homeloan' : $lead->lead_type;
      $pages = $lendConfig['ask_applicant_pages'][$configKey];
      $pages['/consent'] = 'Consent';
      $pages['/electronic-signature'] = 'Electronic Signature';

      // for getUrl requests (sent by fullapp) user partner must match the lead partner
      if (($getUrl === true) && ($lead->partner_id !== $partnerId)) {
        return $this->setJsonResponse(array('success' => false));
      }

      $additionalCode = [];
      $documents = [];

      $leadOwnersTable = $this->getTableLocator()->get('LeadOwnersEntity');
      $pocEntity = $leadOwnersTable->find('all', ['fields' => [
        'owner_id', 'first_name', 'last_name', 'email', 'mobile', 'last_asked_application', 'last_asked_bs', 'ask_applicant_page_status', 'ask_applicant_status']
        ])->where(['lead_id' => $leadId, 'point_of_contact' => true])
        ->first();
      $update = false;

      $askOtherApplicants = [];
      $nonSignatureCreditGuide = !empty($data['non_signature_credit_guide']) ? $data['non_signature_credit_guide'] : [];
      if ($nonSignatureCreditGuide) {
        foreach ($nonSignatureCreditGuide as $ownerRef) {
          $askOtherApplicants[$ownerRef][] = '/non-signature-credit-guide';
        }
      }

      $askForConsent = !empty($data['ask_for_consent']) ? $data['ask_for_consent'] : [];
      if (in_array($pocEntity->owner_ref, $askForConsent)) {
        $data['pages'][] = '/consent';
        $leadOwnersTable->patchEntity($pocEntity, ['last_asked_consent' => date('Y-m-d H:i:s')]);
        $update = true;
        $pocKey = array_search($pocEntity->owner_ref, $askForConsent);
        unset($askForConsent[$pocKey]);
      }

      if ($askForConsent) {
        foreach ($askForConsent as $ownerRef) {
          $askOtherApplicants[$ownerRef][] = '/consent';
        }
      }

      $code = ($lead->lead_type == "commercial" ? 'Commercial' : "") . 'AskApplicantLeadForm';
      if (isset($data['can_add_applicant']) && $data['can_add_applicant'] == true) {
        $additionalCode['canAddApplicant'] = true;
      }
      if (isset($data['display_direct_debit']) && $data['display_direct_debit'] == true) {
        $additionalCode['displayDirectDebit'] = true;
      }
      if (isset($data['pages']) && (count($data['pages']) > 0)) {
        $additionalCode['pages'] = implode('|', $data['pages']);
        if (in_array('/bank-statements', $data['pages'])) {
          $leadOwnersTable->patchEntity($pocEntity, ['last_asked_bs' => date('Y-m-d H:i:s')]);
          $additionalCode['bank_statement_frequency'] = $data['bank_statement_frequency'] ?? '12 Months';
        }else{
          $leadOwnersTable->patchEntity($pocEntity, ['last_asked_application' => date('Y-m-d H:i:s')]);
        }
        $ask_applicant_page_status = $pocEntity->ask_applicant_page_status ? $pocEntity->ask_applicant_page_status : [];
        foreach ($data['pages'] as $page) {
          if(!$ask_applicant_page_status[$page]){
            $ask_applicant_page_status[$page] = [];
          }
          if(!$ask_applicant_page_status[$page]['status']){
            $ask_applicant_page_status[$page]['status'] = 'Asked by Broker';
            $ask_applicant_page_status[$page]['updated'] = date('Y-m-d H:i:s');
          }
        }
        $leadOwnersTable->patchEntity($pocEntity, ['ask_applicant_page_status' => $ask_applicant_page_status, 'ask_applicant_status' => 'sent']);
        $update = true;
      }
      if (isset($data['documents']) && (count($data['documents']) > 0)) {
        if ($update !== true) {
          $leadOwnersTable->patchEntity($pocEntity, ['last_asked_application' => date('Y-m-d H:i:s')]);
        }
        $additionalCode['documents'] = implode('|', $data['documents']);
        $documents = TableRegistry::getTableLocator()->get('ConfigDocumentTypeEntity', ['fields' => ['id', 'type_name']])->find('all')->whereInList('id', $data['documents'])->toArray();
        $documents = array_column($documents, 'type_name', 'id');
      }
      $electronicSignatureDocuments = [];
      if (!empty($data['electronic_signature_documents']) && !empty($data['electronic_signature_configuration'])) {
        $requestSignatureResult = $this->_requestSignature($data, $leadId, $partnerId, $partnerUserId, $user);
        if ($requestSignatureResult === false) {
          return $this->setJsonResponse(['error' => 'Failed to request electronic signature'], 400);
        }

        foreach ($data['electronic_signature_configuration'] as $document => $config) {
          if ($config['owners']) {
            foreach ($config['owners'] as $ownerRef) {
              if($pocEntity->owner_ref !== $ownerRef){
                if(!$askOtherApplicants[$ownerRef]){
                  $askOtherApplicants[$ownerRef] = ['/electronic-signature'];
                } else if (!in_array('/electronic-signature', $askOtherApplicants[$ownerRef])) {
                  $askOtherApplicants[$ownerRef][] = '/electronic-signature';
                }
              }
              $electronicSignatureDocuments[$ownerRef][] = $config['template_name'];
            }
          }
        }
        $additionalCode['electronic_signature_documents'] = implode('|', $electronicSignatureDocuments[$pocEntity->owner_ref]);
      }

      if (!isset($data['pages']) && !isset($data['documents']) && !isset($data['electronic_signature_documents'])) {
        $this->setJsonResponse(['error' => "Must send pages or documents"], 400);
      }

      if (!empty($data['consumer_v2'])) {
        $additionalCode['consumer_v2'] = $data['consumer_v2'];
      }
      $poc = $pocEntity->toArray();
      $adhocData = [];
      $note = !empty($data['note_to_applicant']) ? $data['note_to_applicant'] : '';
      $adhocData['to'] = $poc['email'];
      $additionalCode['ownerRef'] = $pocEntity->owner_ref;

      if (!empty($additionalCode['documents'])) {
        if (isset($additionalCode['pages'])) {
            $additionalCode['pages'] .= '|/documentation';
        } else {
            $additionalCode['pages'] = '/documentation';
        }
      }

      if (!empty($additionalCode['electronic_signature_documents'])) {
        if (isset($additionalCode['pages'])) {
            $additionalCode['pages'] .= '|/electronic-signature';
        } else {
            $additionalCode['pages'] = '/electronic-signature';
        }
      }

      if($data['send_type'] && in_array('sms', $data['send_type'])) {
        $assignedBroker = $this->loadModel('PartnerUserLeads')->getCurrentPartnerUser($lead['lead_id']);
        $adhocData['broker_full_name'] = !empty($assignedBroker) ? reset($assignedBroker) : $user['name'];
      }
      $adhocData['additionalCode'] = http_build_query($additionalCode);
      $adhocData['applicant_note'] = ($note === '') ? '' : $note;
      $adhocData['applicant_note_text'] = ($note === '') ? '' : 'A note from your broker regarding these requirements:';
      $adhocData['ask_applicant_sections'] = isset($data['pages']) ? array_intersect_key($pages, array_flip($data['pages'])) : [];
      $adhocData['ask_applicant_sections_text'] = (isset($data['pages']) && (count($data['pages']) > 0)) ? 'Please complete the following sections of the application form:' : '';
      $adhocData['ask_applicant_documents'] = $documents;
      $adhocData['ask_applicant_documents_text'] = (count($documents) > 0) ? 'Please provide the following documents:' : '';
      $adhocData['ask_applicant_electronic_signature_documents'] = $electronicSignatureDocuments[$pocEntity->owner_ref] ?? [];
      $adhocData['ask_applicant_electronic_signature_documents_text'] = (count($electronicSignatureDocuments[$pocEntity->owner_ref]) > 0) ? 'Please sign the following documents:' : '';

      $resp = ['success' => true];

      if ($getUrl === true) {
        $url = getenv('DOMAIN_ASK_APPLICANT', true) . '/';
        $url .= TableRegistry::get('Leads')->createAutoLoginCode($leadId, $adhocData['additionalCode']);
        $url .= '&code=' . $code;
        $resp = ['url' => $url];
      } else {
        if ($additionalCode['pages']) {
          foreach ($data['send_type'] as $sendType) {
            if ($sendType === 'email') {
              $adhocData['delivery_type'] = 'Email';
            } elseif ($sendType === 'sms') {
              if (empty($poc['mobile'])) {
                $successMessage .= 'No mobile number provided for ' . $poc['first_name'] . ' ' . $poc['last_name'] . '.' . PHP_EOL;
                continue;
              }
              $adhocData['delivery_type'] = 'SMS';
            }
            $sendNotificationResults = $this->LoadModel('PartnerNotifications')->sendPartnerNotifications($user['partner_id'], $code, $leadId, $adhocData,$partnerUserId);
            $this->parseNotificationResult($sendType, $sendNotificationResults, $poc['first_name'] . ' ' . $poc['last_name'], $errorMessage, $successMessage);
          }
          
        }
      }
      if ($getUrl !== true) { //only update poc if email is sent
        $leadOwnersTable->save($pocEntity);
      }

      $partnerLeadUploadsData = [];
      foreach ($documents as $document) {
        $partnerLeadUploadsData[] = ['lead_id' => $leadId, 'owner_id' => $poc['owner_id'], 'specify' => $document];
      }
      if (count($partnerLeadUploadsData) > 0) {
        $partnerLeadUploadsTable = TableRegistry::getTableLocator()->get('PartnerLeadUploadsRequestedEntity');
        $partnerLeadUpload = $partnerLeadUploadsTable->newEntities($partnerLeadUploadsData);
        $partnerLeadUploadsTable->saveMany($partnerLeadUpload);
      }

      if (strlen($note) > 0) {
        $lead_table->patchEntity($lead, ['ask_applicant_additional_request' => $note]);
      }
      $lead_table->patchEntity($lead, ['ask_applicant_status' => "Asked by Broker", 'ask_applicant_last_action_time' => date('Y-m-d H:i:s')]);

      Log::error('askOtherApplicants=>'.json_encode($askOtherApplicants));
      // Other applicants
      if (!empty($askOtherApplicants)) {
        unset($adhocData['applicant_note']);
        unset($adhocData['applicant_note_text']);
        unset($adhocData['ask_applicant_documents']);
        unset($adhocData['ask_applicant_documents_text']);
        unset($additionalCode['documents']);
        $additionalCode['canAddApplicant'] = false;

        foreach ($askOtherApplicants as $ownerRef => $ownerPages) {

          $adhocData['ask_applicant_electronic_signature_documents'] = $electronicSignatureDocuments[$ownerRef] ?? [];
          $adhocData['ask_applicant_electronic_signature_documents_text'] = (count($electronicSignatureDocuments[$ownerRef]) > 0) ? 'Please sign the following documents:' : '';
          $hashids = new Hashids('lead_owners', 7);
          $owner_id = $hashids->decode($ownerRef)[0];
          $leadOwnersTable = $this->getTableLocator()->get('LeadOwnersEntity');
          $ownerEntity = $leadOwnersTable->find('all', ['fields' => ['owner_id', 'first_name', 'last_name', 'email', 'mobile','last_asked_consent','ask_applicant_status']])->where(['lead_id' => $leadId, 'owner_id' => $owner_id])->first();
          $leadOwnersTable->patchEntity($ownerEntity, ['last_asked_consent' => date('Y-m-d H:i:s'), 'ask_applicant_status' => 'sent']);
          $leadOwnersTable->save($ownerEntity);
          $additionalCode['ownerRef'] = $ownerRef;
          $adhocData['to'] = $ownerEntity->email;
          $adhocData['client_first_name'] = $ownerEntity->first_name;
          $adhocData['client_last_name'] = $ownerEntity->last_name;
          $adhocData['ask_applicant_sections'] = array_intersect_key($pages, array_flip($ownerPages));
          $additionalCode['pages'] = implode('|', $ownerPages);
          $adhocData['additionalCode'] = http_build_query($additionalCode);
          foreach ($data['send_type'] as $sendType) {
            if ($sendType === 'email') {
              $adhocData['delivery_type'] = 'Email';
            } elseif ($sendType === 'sms') {
              if (empty($ownerEntity->mobile)) {
                $successMessage .= 'No mobile number provided for ' . $ownerEntity->first_name . ' ' . $ownerEntity->last_name . '.' . PHP_EOL;
                continue;
              }
              $adhocData['delivery_type'] = 'SMS';
            }
            $sendNotificationResults =   $this->LoadModel('PartnerNotifications')->sendPartnerNotifications($user['partner_id'], $code, $leadId, $adhocData, $partnerUserId);
            $this->parseNotificationResult($sendType, $sendNotificationResults, $poc['first_name'] . ' ' . $poc['last_name'], $errorMessage, $successMessage);
          }
        }
      }
      $lead_table->save($lead);

      if (!empty($errorMessage)) {
        return $this->setJsonResponse([
          'success' => false,
          'error' => $successMessage . $errorMessage
        ], 400);
      }

      return $this->setJsonResponse($resp);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => "Unable to process request"], 400);
    }
  }

  protected function parseNotificationResult($sendType, $sendNotificationResults, $full_name, &$errorMessage, &$successMessage)
  {
    $sendResultResponsesFailed = [];
    foreach ($sendNotificationResults as $sendResult) {
      if (isset($sendResult['successfully_sent']) && $sendResult['successfully_sent']['success'] === false) {
        $sendResultResponsesFailed[] = $sendResult['successfully_sent'];
      }
    }
    if (!empty($sendResultResponsesFailed)) {
      $tempErrorMessage = '';
      foreach ($sendResultResponsesFailed as $sendResult) {
        $tempErrorMessage .= $sendResult['message'] . PHP_EOL;
      }
      if (empty($tempErrorMessage)) {
        $tempErrorMessage .= 'Failed to send Ask Applicant request via ' . $sendType . '.' . PHP_EOL;
      }
      $errorMessage .= 'Failed to send ' . strtoupper($sendType) . ' to ' . $full_name . ' because ' . $tempErrorMessage . PHP_EOL;
    } else {
      $successMessage .= ucfirst($sendType) . ' has been sent to ' . $full_name . '. ' . PHP_EOL;
    }
  }

  /**
   * Save Ask applicant reponse as a note on lead
   * @return void
   */
  public function updateApplicantResponse()
  {
    try {
      $data = $this->request->getData();
      $leadId = (new LendInternalAuth)->unhashLeadId($data['lead_ref']);
      $associated = [];
      $associated['PartnerEntity'] = ['fields' => ['partner_id', 'company_name']];
      $associated['PocOwner'] = ['fields' => ['owner_id', 'first_name', 'last_name']];
      $associated['PartnerUserLeadsEntity'] = ['fields' => ['partner_user_lead_id', 'partner_user_id']];
      $associated['PartnerUserLeadsEntity.PartnerUserEntity'] = ['fields' => ['partner_user_id', 'name', 'email', 'mobile', 'phone']];
      $options = ['contain' => $associated];
      $lead_table = TableRegistry::getTableLocator()->get('LeadEntity');
      $lead = $lead_table->get($leadId, $options)->toArray();
      $notes = $lead['partner']['company_name'] . ' (you) wrote this when requesting the Applicant to complete requirements:' . PHP_EOL;
      $notes .= ($lead['ask_applicant_additional_request'] ?? '') . PHP_EOL;
      $notes .= $lead['owner_poc']['first_name'] . ' ' . $lead['owner_poc']['last_name'] . ' Responded:' . PHP_EOL;
      $notes .= $data['response'];
      $note = ['lead_id' => $leadId, 'notes' => $notes];

      if (strlen($data['response']) > 0) {
        $lead_table = TableRegistry::getTableLocator()->get('LeadEntity');
        $lead = $lead_table->get($leadId);
        $lead_table->patchEntity($lead, ['ask_applicant_additional_response' => $data['response']]);
        $lead_table->save($lead);
      }

      $data['note_id'] = $this->loadModel('LeadNotes')->addNote($note);
      return $this->setJsonResponse(array('success' => true));
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      $this->setJsonResponse(['error' => "Unable to process request"], 400);
    }
  }

  /**
   * Send email to partner/broker when customer completes the lead form/completes documentation
   * @return void
   */
  public function finaliseClientChanges()
  {
    try {
      $data = $this->request->getData();
      $leadId = (new LendInternalAuth)->unhashLeadId($data['lead_ref']);
      $owner_id = (new LendInternalAuth)->unhashOwnerId($data['owner_ref']);

      if (empty($leadId) || empty($owner_id)) {
        return $this->setJsonResponse(['success' => false, 'error' => 'Invalid lead or owner reference'], 400);
      }

      //mark the owner as finalised
      $leadOwnersTable = TableRegistry::getTableLocator()->get('LeadOwnersEntity');
      $owner = $leadOwnersTable->find('all', ['fields' => ['owner_id', 'lead_id', 'ask_applicant_status', 'first_name', 'last_name']])
        ->where(['lead_id' => $leadId, 'owner_id' => $owner_id])
        ->first();
        
      if (empty($owner)) {
        return $this->setJsonResponse(['success' => false, 'error' => 'Owner not found'], 404);
      }
      $leadOwnersTable->patchEntity($owner, ['ask_applicant_status' => 'finalised']);
      $leadOwnersTable->save($owner);

      // Get lead data with associated entities
      $associated = [];
      $associated['Owners'] = ['fields' => ['owner_id', 'lead_id', 'ask_applicant_status', 'consent' , 'point_of_contact']];
      $associated['PartnerEntity'] = ['fields' => ['partner_id', 'company_name']];
      $associated['PocOwner'] = ['fields' => ['owner_id', 'first_name', 'last_name']];
      $associated['PartnerUserLeadsEntity'] = ['fields' => ['partner_user_lead_id', 'partner_user_id']];
      $associated['PartnerUserLeadsEntity.PartnerUserEntity'] = ['fields' => ['partner_user_id', 'name', 'email', 'mobile', 'phone']];
      $options = ['contain' => $associated];
      $lead_table = TableRegistry::getTableLocator()->get('LeadEntity');
      $lead = $lead_table->get($leadId, $options);

      if (empty($lead)) {
        return $this->setJsonResponse(['success' => false, 'error' => 'Lead not found'], 404);
      }

      // Check if all owners are finalised
      $allfinalised = true;
      foreach ($lead->owners_all as $owner) {
        if (!empty($owner->ask_applicant_status) 
              && $owner->ask_applicant_status != 'finalised' 
            && ( $owner->point_of_contact 
                  || (!$owner->point_of_contact && empty($owner->consent))
                )
        ) {
          $allfinalised = false;
          break;
        }
      }

      // Update Lead data
      $leadPatchData = ['ask_applicant_last_action_time' => date('Y-m-d H:i:s')];

      // If all owners are finalised, update the lead status (Lock the `ask applicant` access)
      if ($allfinalised) {
        $leadPatchData['ask_applicant_status'] = 'Marked as Complete by Client';
      }

      $isLendIqLead = false;
      if(isset($data['lend_iq_lead']) && $data['lend_iq_lead'] == "true" && $lead->lead_type == "commercial"){
        $leadPatchData['lend_iq_lead'] = true;
        $isLendIqLead = true;
      }
      $lead_table->patchEntity($lead, $leadPatchData);
      $lead_table->save($lead);

      // Send email to partner/broker after all owners are finalised
      if ($allfinalised) {
        $code = ($lead->lead_type == "commercial" ? 'Commercial' : "") . (isset($data['pages']) ? 'AskApplicantLeadFinl' : 'AskApplicantDocsFinl');
        if (!is_null($lead->partner_user_lead))
          $partnerUser = $lead->partner_user_lead->user;
        else
          $partnerUser = TableRegistry::getTableLocator()->get('PartnerUserEntity')->find('all', ['fields' => ['partner_user_id', 'name', 'email', 'mobile', 'phone']])->where(['partner_id' => $lead['partner_id'], 'point_of_contact' => true])->first();

        $adhocData = [];
        $poc = $lead->owner_poc;
        $adhocData['partner_company_name'] = $lead->partner->company_name;
        $adhocData['partner_user_name'] = $partnerUser->name;
        $adhocData['client_name'] = $poc->first_name . " " . $poc->last_name;

        $repsonse = $this->LoadModel('PartnerNotifications')->sendPartnerNotifications($lead->partner_id, $code, $leadId, $adhocData);
      }
      unset($_COOKIE['auth_token']);
      setcookie('auth_token', '', time() - 3600, "/");
      $this->Auth->logout();
      return $this->setJsonResponse(array('success' => true, 'is_lend_iq_lead' => $isLendIqLead));
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      $this->setJsonResponse(['success' => false, 'error' => "Unable to process request"], 400);
    }
  }

  protected function __removeComplianceDocs($lead)
  {
    try {
      if (!empty($lead['uploads'])) {
        foreach ($lead['uploads'] as $key => $upload) {
          if ($upload['is_compliance']) {
            unset($lead['uploads'][$key]);
          }
        }
      }
      return $lead;
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }

  private function __trackDocSending($lead, $lender_id)
  {
    try {
      if (!empty($lead['uploads'])) {
        foreach ($lead['uploads'] as $upload) {
          $leadLenderUploadTrackingTable = TableRegistry::getTableLocator()->get('LeadLenderUploadTracking');
          $trackingData = [
            'upload_id' => $upload['partner_lead_upload_id'],
            'lender_id' => $lender_id,
            'lead_id' => $lead['lead_id'],
            'source_table' => 'partner_lead_uploads',
            'created' => date('Y-m-d H:i:s'),
            'status' => 'Sent',
            'notes' => 'Document has been sent to intermediary',
          ];

          $trackingEntity = $leadLenderUploadTrackingTable->newEntity($trackingData);
          if (!$leadLenderUploadTrackingTable->save($trackingEntity)) {
            throw new \Exception("Failed to save LeadLenderUploadTracking record");
          }
        }
      }
    } catch (\Exception $e) {
        Log::error($e->getMessage());
        Log::error($e->getTraceAsString());
    }
  }


  public function updateReferences()
  {
    try {
      if (!$this->request->is('post')) {
        throw new \Exception("Only POST request is allowed.");
      }

      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }

      $data = $this->request->getData();
      if (empty($data['lead_ref'])) {
        throw new \Exception("lead_ref is required.");
      }

      $lend_internal_auth = new LendInternalAuth;
      $lead_id = $lend_internal_auth->unhashLeadId($data['lead_ref']);
      if (empty($lead_id)) {
        throw new \Exception("Can't find a lead.");
      }
      $permission_check = $this->checkPermission($lead_id, null, $user['account_type'] === 'Applicant');
      if (!$permission_check['success']) {
        throw new \Exception($permission_check['message']);
      }

      $leadReferenceTable = TableRegistry::getTableLocator()->get('LeadReferenceEntity');

      if (isset($data['references']) && is_array($data['references'])) {
        foreach ($data['references'] as $reference) {
          $lead_reference = null;
          if (isset($reference['lead_reference_id'])) {
            $lead_reference = $leadReferenceTable->find()->where(['lead_reference_id' => $reference['lead_reference_id']])->first();
          }
          if (empty($lead_reference)) {
            $lead_reference = $leadReferenceTable->newEntity();
          }

          $reference['lead_id'] = $lead_id;
          $lead_reference = $leadReferenceTable->patchEntity($lead_reference, $reference);

          if ($lead_reference->getErrors()) {
            throw new \Exception("Error in processing reference data");
          } else {
            $leadReferenceTable->save($lead_reference);
          }
        }
      }
      return $this->setJsonResponse(['success' => true, 'message' => 'References updated successfully.']);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()]);
    }
  }

  public function deleteReference()
  {
    try {
      if (!$this->request->is('post')) {
        throw new \Exception("Only POST request is allowed.");
      }
      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }
      $data = $this->request->getData();
      if (empty($data['lead_reference_id']) || empty($data['lead_ref'])) {
        throw new \Exception("Missed required field: reference_id, lead_ref");
      }
      $lend_internal_auth = new LendInternalAuth;
      $lead_id = $lend_internal_auth->unhashLeadId($data['lead_ref']);
      if (empty($lead_id)) {
        throw new \Exception("Can't find a lead.");
      }
      $permission_check = $this->checkPermission($lead_id, null, $user['account_type'] === 'Applicant');
      if (!$permission_check['success']) {
        throw new \Exception($permission_check['message']);
      }

      $referenceTable = TableRegistry::getTableLocator()->get('LeadReferenceEntity');
      $lead_reference = $referenceTable->find()->where(['lead_reference_id' => $data['lead_reference_id']])->first();
      if (empty($lead_reference)) {
        throw new \Exception("Can't find a reference.");
      }
      $lead_reference->status = 'inactive';
      $referenceTable->save($lead_reference);

      return $this->setJsonResponse(['success' => true, 'message' => 'Reference deleted successfully.']);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()]);
    }
  }

  public function updateBusinessDetail()
  {

    try {
      if (!$this->request->is('post')) {
        throw new \Exception("Only POST request is allowed.");
      }
      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }
      $data = $this->request->getData();
      if (empty($data['lead_ref'])) {
        throw new \Exception("lead_ref is required.");
      }
      $lend_internal_auth = new LendInternalAuth;
      $lead_id = $lend_internal_auth->unhashLeadId($data['lead_ref']);
      if (empty($lead_id)) {
        throw new \Exception("Can't find a lead.");
      }
      if (empty($lead_id)) {
        throw new \Exception("Can't find a lead.");
      }
      $permission_check = $this->checkPermission($lead_id, null, $user['account_type'] === 'Applicant');
      if (!$permission_check['success']) {
        throw new \Exception($permission_check['message']);
      }

      if (!empty($data['abn_lookup'])) {
        $data['abn'] = $data['abn_lookup']['abn'] ?? null;
        $data['acn'] = $data['abn_lookup']['acn'] ?? null;
        $data['business_type_abn'] = $data['abn_lookup']['entity_type_code'] ?? null;
        $data['r_state'] = $data['abn_lookup']['state'] ?? null;
        $data['organisation_name'] = $data['abn_lookup']['organisation_name'] ?? null;
        $data['business_name'] = $data['abn_lookup']['business_name'] ?? null;
        $data['company_registration_date'] = $data['abn_lookup']['effective_from'] ?? null;
      }

      if (!empty($data['nzbn_lookup'])) {
        unset($data['nzbn_lookup']['id']);
        $data['nzbn_lookup']['original_payload'] = json_decode($data['nzbn_lookup']['original_payload'], true);

        $nzbn_lookup_table = TableRegistry::getTableLocator()->get('NzbnLookupEntity');
        $nzbn_lookup = $nzbn_lookup_table->find('all')->where(['nzbn' => $data['nzbn_lookup']['nzbn']])->first();
        if (!empty($nzbn_lookup)) {
          $nzbn_lookup_table->patchEntity($nzbn_lookup, $data['nzbn_lookup']);
        } else {
          $nzbn_lookup = $nzbn_lookup_table->newEntity($data['nzbn_lookup']);
        }
        $nzbn_lookup_table->save($nzbn_lookup);

        $data['nzbn_id'] = $nzbn_lookup->id;
        $data['abn'] = $data['nzbn_lookup']['nzbn'];
        $data['organisation_name'] = $data['nzbn_lookup']['entity_name'];
        $data['business_name'] = $data['nzbn_lookup']['entity_name'];
        $data['business_type_abn'] = $data['nzbn_lookup']['entity_type_description'];
        $data['company_registration_date_from_abnlookup'] = $data['nzbn_lookup']['registration_date'];
        $data['company_registration_date'] = $data['nzbn_lookup']['registration_date'];
      }

      //modify all_addresses array to add date_from and date_to fields based on durations values
      $toDate = null;
      if (!empty($data['all_addresses'])) {
        foreach($data['all_addresses'] as $key => $address) {
          $processedAddress = $this->AddressValidator->processAddress($address);
          $data['all_addresses'][$key] = $processedAddress;
        }
        foreach($data['all_addresses'] as &$address){//most recent(current) is first
          if ($address['address_type'] !== "trading")
            continue;
          $fromDate = $this->getDurationBasedFromDate($address['duration_years'], $address['duration_months'], $toDate);
          $address['date_from'] = $fromDate;
          $address['date_to'] = $toDate;
          $toDate = $fromDate;//from is to for next record
        }
      }


      if (!empty($data['tax_outstanding'])) {
        $leadLiabilityTable = TableRegistry::getTableLocator()->get('LeadLiabilitiesEntity');

        $leadLiability = $leadLiabilityTable->find()
            ->where(['lead_id' => $lead_id, 'liability_id' => 1])
            ->order(['lead_liability_id' => 'DESC'])
            ->first();

        $liability_data = [
            'liability_id' => 1,
            'loan_balance' => $data['tax_overdue'],
            'repayment_pm' => $data['tax_outstanding_arrangement'] ? $data['tax_monthly_repayment'] : null,
            'ato_payplan_arranged' => $data['tax_outstanding_arrangement'] ? $data['tax_outstanding_arrangement'] : null,
            'status' => 'Active'
        ];

        if ($leadLiability) {
            $leadLiability = $leadLiabilityTable->patchEntity($leadLiability, $liability_data);
        } else {
            $leadLiability = $leadLiabilityTable->newEntity($liability_data + ['lead_id' => $lead_id]);
        }

        if (!$leadLiabilityTable->save($leadLiability)) {
            Log::write('debug', 'Lead Liability not saved: ' . print_r($leadLiability->getErrors(), true));
        } else {
            Log::write('debug', 'Lead Liability saved');
        }

      }
      $lead_table = TableRegistry::getTableLocator()->get('LeadEntity');

      $keep_bs = !empty($data['keep_bs']);
      unset($data['keep_bs']);
      if ($keep_bs) {
        $original_lead = $lead_table->get($lead_id);
        $original_account_id = $original_lead->account_id;
      }

      $result = $lead_table->updateLeadEntity($lead_id, $data);

      if ($keep_bs) {
        $new_lead = $lead_table->get($lead_id);
        $new_account_id = $new_lead->account_id;
        // update bank_feeds
        $bankfeed_data = [
          'origin_partner_account_id' => $original_account_id,
          'new_partner_account_id' => $new_account_id,
        ];
        $curl = new CurlHelper(getenv('DOMAIN_BANKFEEDS') . "/copy-account");
        $bank_feed_response = $curl->post($bankfeed_data, true, ["x-api-key" => getenv('BANK_STATEMENT_API_KEY')]);
        // update full_bs_analysis
        $bsa_data = [
          'origin_partner_account_id' => $original_account_id,
          'new_partner_account_id' => $new_account_id,
          'lead_id' => $lead_id,
        ];
        $curl = new CurlHelper(getenv('DOMAIN_FULL_BSA') . "/move-partner-account");
        $bsa_response = $curl->post($bsa_data, false, ["x-api-key" => getenv('FULL_BSA_API_KEY')]);
        if (empty($bank_feed_response['success']) || empty($bsa_response['success'])) {
          throw new \Exception("Fail to copy bank statements.");
        }
      }

      $leadTable = TableRegistry::getTableLocator()->get('LeadEntity');
      $reinstate_lead = $leadTable->reinstateLead($lead_id,'business_detail');

      return $this->setJsonResponse(["success" => true, 'data' => $result, 'reinstate_lead' => $reinstate_lead]);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(["success" => false, 'error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  public function deleteAddress()
  {
    try {
      if (!$this->request->is('post')) {
        throw new \Exception("Only POST request is allowed.");
      }
      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }
      $data = $this->request->getData();
      if (empty($data['lead_ref'])) {
        throw new \Exception("lead_ref is required.");
      }
      $lend_internal_auth = new LendInternalAuth;
      $lead_id = $lend_internal_auth->unhashLeadId($data['lead_ref']);
      if (empty($lead_id)) {
        throw new \Exception("Can't find a lead.");
      }

      if (empty($lead_id)) {
        throw new \Exception("Can't find a lead.");
      }
      $permission_check = $this->checkPermission($lead_id, null, $user['account_type'] === 'Applicant');
      if (!$permission_check['success']) {
        throw new \Exception($permission_check['message']);
      }

      $addressTable = TableRegistry::getTableLocator()->get('LeadAddressEntity');
      $address = $addressTable->get($data['lead_address_id']);
      $address->status = 'deleted';
      $addressTable->save($address);
      $this->readjustAddressEmploymentDurations('LeadAddressEntity', 'lead_id', $address->lead_id);
      return $this->setJsonResponse(['success' => true]);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()]);
    }
  }

  public function getBankTransaction()
  {

    try {
      $data = $this->request->getData();
      $lead_id = (new LendInternalAuth)->unhashLeadId($data['lead_ref']);
      if (!$lead_id) {
        throw new \Exception("Can't find a lead.");
      }

      $lead_table = TableRegistry::getTableLocator()->get('LeadEntity', [
        'connection' => ConnectionManager::get('reader_db')
      ])->get($lead_id)->toArray();
      $configExpenses = TableRegistry::getTableLocator()->get('ConfigExpenseEntity', [
        'connection' => ConnectionManager::get('reader_db')
      ])
        ->find('all')->toArray();

      $response = $this->fetchTransactionData($lead_table);

      if (!$response['success']) {
        throw new \Exception($response['message']);
      }

      list($sums, $total_expense) = $this->calculateExpenses($configExpenses, $response['data']);
      $sums[] = ['tag_category' => 'Total', 'amount' => $total_expense];
      return $this->setJsonResponse(['success' => true, 'data' => $sums]);



    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }


  public function importBankTransaction()
  {
    try {
      $data = $this->request->getData();
      $lead_id = (new LendInternalAuth)->unhashLeadId($data['lead_ref']);
      if (!$lead_id) {
        throw new \Exception("Can't find a lead.");
      }

      $lead_table = TableRegistry::getTableLocator()->get('LeadEntity');

      $associated = [
        'LeadOwnerExpenseEntity' => [
          'ConfigExpenseEntity',
          'LeadOwnerExpensesBreakdownEntity',
        ]
      ];

      $lead_table = $lead_table->get($lead_id, [
        'contain' => $associated
      ])->toArray();

      $configExpenses = TableRegistry::getTableLocator()->get('ConfigExpenseEntity')
        ->find('all')->toArray();

      $hasNewExpenses = false;
      foreach ($configExpenses as $key => $expense) {
        $hasThisExpense = false;
        if(!empty($lead_table['expenses'])){
          foreach ($lead_table['expenses'] as $leadExpense) {
            if ($leadExpense['config_expense_id'] === $expense['id']) {
              $hasThisExpense = true;
            }
          }
        }
        if (!$hasThisExpense) {
          $lead_table['expenses'][] = [
            'config_expense_id' => $expense['id'],
            'amount' => null,
            'note' => '',
            'shared' => false,
            'user_input_amount' => null,
          ];
          $hasNewExpenses = true;
        }
      }
      if($hasNewExpenses){
        $lead_table = $this->_saveLeadEntity($lead_table['lead_ref'], $lead_table, $associated);
      }

      $response = $this->fetchTransactionData($lead_table);

      if (!$response['success']) {
        throw new \Exception($response['message']);
      }
      list($sums, $total_expense) = $this->calculateExpenses($configExpenses, $response['data']);

      foreach ($lead_table['expenses'] as &$leadExpense) {
        foreach ($sums as $sum) {
          if ($leadExpense['config_expense_id'] === $sum['config_expense_id']) {
            $leadExpense['amount'] = $sum['amount'];
            $leadExpense['user_input_amount'] = $sum['amount'];
            break;
          }
        }
      }

      $keysToKeep = [
        'id',
        'config_expense_id',
        'amount',
        'note',
        'shared',
        'shared_percentage',
        'owner_ref',
        'user_input_amount'
      ];
      foreach ($lead_table['expenses'] as &$expense) {
        foreach ($expense as $key => $value) {
          if (!in_array($key, $keysToKeep)) {
            unset($expense[$key]);
          }
        }
      }

      $saved_lead = $this->_saveLeadEntity($lead_table['lead_ref'], $lead_table, [
        'LeadOwnerExpenseEntity',
        'LeadOwnerExpenseEntity.LeadOwnerExpensesBreakdownEntity',
      ]);

      return $this->setJsonResponse(['success' => true, 'data' => $sums]);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  public function getBankStatementList()
  {

    try {
      $data = $this->request->getData();

      $lead_id = (new LendInternalAuth)->unhashLeadId($data['lead_ref']);
      if (!$lead_id) {
        throw new \Exception("Can't find a lead.");
      }

      $lead_table = TableRegistry::getTableLocator()->get('LeadEntity', [
        'connection' => ConnectionManager::get('reader_db')
      ])->get($lead_id)->toArray();
      if (!empty($lead_table['account_id'])) {
        $bankfeed_data = [
          'partnerAccountId' => $lead_table['account_id'],
          'is_active'=>true
        ];

        $curl = new CurlHelper(getenv('DOMAIN_BANKFEEDS') . "/get-bank-account-info-by-partner-account-id");
        $response = $curl->post($bankfeed_data, true, ["x-api-key" => getenv('BANK_STATEMENT_API_KEY')]);

        if ($response['success']) {
          return $this->setJsonResponse($response);
        } else {
          throw new \Exception($response['message']);
        }
      } else {
        throw new \Exception('Not found account ID.');
      }
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }


  }



  private function calculateExpenses($configExpenses, $transaction)
  {
    list($mapping, $sub_category_mapping) = $this->getMappingArrays();

    $sums = [];
    $total_expense = 0;
    $firstTransactionDate = null;
    $lastTransactionDate = null;
    foreach ($configExpenses as $configExpense) {
      $expenseType = $configExpense['expenses_type'];
      if (isset($mapping[$expenseType])) {
        $tags = $mapping[$expenseType];
        $tags = is_array($tags) ? $tags : [$tags]; // Ensure $tags is an array
        $amount = 0;

        foreach ($tags as $tag) {
          foreach ($transaction as $trans) {
            if(!$firstTransactionDate || $trans['date'] < $firstTransactionDate){
              $firstTransactionDate = $trans['date'];
            }
            if(!$lastTransactionDate || $trans['date'] > $lastTransactionDate){
              $lastTransactionDate = $trans['date'];
            }
            if (is_array($trans) && isset($trans['tag_category']) && $trans['tag_category'] === $tag && isset($trans['tag_credit_debit']) && $trans['tag_credit_debit'] === "debit") {
              if (in_array($trans['tag_category'], $mapping['SubCategoryCheck'])) {
                if (isset($sub_category_mapping[$expenseType])) {
                  $sub_tags = $sub_category_mapping[$expenseType];
                  if (in_array($trans['tag_third_party'], $sub_tags)) {
                    $amount += abs(round($trans['amount']));
                  }
                }
              } else {
                $amount += abs(round($trans['amount']));
              }
              $total_expense += abs(round($trans['amount']));
            }
          }
        }
        $sums[] = ['tag_category' => $expenseType, 'amount' => $amount, "config_expense_id" => $configExpense['id']];
      }
    }
    $days = (new Datetime($lastTransactionDate))->diff(new Datetime($firstTransactionDate))->days;
    $total_expense = round($total_expense / $days * 30, 2);
    if(!empty($sums) && $days > 0){
      foreach ($sums as &$sum) {
        $sum['amount'] = round($sum['amount'] / $days * 30, 2);
      }
    }
    return [$sums, $total_expense];
  }

  private function fetchTransactionData($lead_table)
  {
    if (!empty($lead_table['account_id'])) {
      $bankfeed_data = [
        'partnerAccountId' => $lead_table['account_id']
      ];

      $curl = new CurlHelper(getenv('DOMAIN_BANKFEEDS') . "/get-transaction-by-partner-account-id");
      $response = $curl->post($bankfeed_data, true, ["x-api-key" => getenv('BANK_STATEMENT_API_KEY')]);

      if ($response['success']) {
        $data = $response['data'];
        if (isset($data) && is_array($data)) {
          $transaction = [];
          foreach ($data as $record) {
            if (is_array($record)) {
              $transaction = array_merge($transaction, $record);
            }
          }
          return ['success' => true, 'data' => $transaction];
        }
      } else {
        if (isset($response['error'])) {
          return ['success' => false, 'message' => $response['error']];
        }
      }
      return ['success' => false, 'message' => 'Unknown error'];
    } else {
      return ['success' => false, 'message' => 'Account ID not found.'];
    }
  }


  public function getMappingArrays()
  {

    $mapping = [];
    $sub_category_mapping = [];

    $categoryTable = TableRegistry::getTableLocator()->get('IllionLendExpensesMappingCategory');
    $categoryResults = $categoryTable->find()->toArray();
    foreach ($categoryResults as $result) {
      $mapping[$result->category][] = $result->mapped_category;
    }

    $subCategoryTable = TableRegistry::getTableLocator()->get('IllionLendExpensesMappingSubCategory');
    $subCategoryResults = $subCategoryTable->find()->toArray();
    foreach ($subCategoryResults as $result) {
      $sub_category_mapping[$result->sub_category][] = $result->mapped_sub_category;
    }

    return [$mapping, $sub_category_mapping];
  }

  public function saveOverdraft($leadRef)
  {
    try {
      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }
      $data = $this->request->getData();
      $lead_id = (new LendInternalAuth)->unhashLeadId($leadRef);
      if (!$lead_id) {
        throw new \Exception("Can't find a lead.");
      }
      $permission_check = $this->checkPermission($lead_id, null, $user['account_type'] === 'Applicant');
      if (!$permission_check['success']) {
        throw new \Exception($permission_check['message']);
      }
      $lead = TableRegistry::getTableLocator()->get('LeadEntity')->get($lead_id);
      $success = true;
      if (!empty($data)) {
        $totalOverdraft = 0;
        foreach ($data as $account_id => $overdraft) {
          $sendData = ['accountId' => $account_id, 'partnerAccountId' => $lead->account_id, 'leadId' => $lead_id, 'overdraft' => $overdraft];
          $curl = new CurlHelper(getenv('DOMAIN_BANKFEEDS') . "/update-overdraft");
          $response = $curl->post($sendData, true, ["x-api-key" => getenv('BANK_STATEMENT_API_KEY')]);
          $success = $success && $response['success'];
          $totalOverdraft += $overdraft;
        }
      }

      // we will record the total Overdraft that user declared and Lend admin will check and update the overdraft limit in star
      // 5 is held for review
      $saved_lead = $this->_saveLeadEntity($leadRef, ["overdraft_loc_limit" => $totalOverdraft, "partner_status_id" => 5], []);
      TableRegistry::getTableLocator()->get('PartnerNotifications')->sendPartnerNotifications($lead->partner_id, 'ODReview', $lead_id);
      // 7 is system user id
      TableRegistry::getTableLocator()->get('AdminJobs')->addAdminJob(['lead_id' => $lead_id, 'user_id' => 7, 'job_type' => 'held_for_review', 'job_status' => 'pending', 'reason' => json_encode(['Client updated overdraft.']), 'created' => date('Y-m-d H:i:s')]);
      return $this->setJsonResponse(['success' => $success]);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  public function getPartnerByLeadRef($leadRef)
  {
    try {
      $lead_id = (new LendInternalAuth)->unhashLeadId($leadRef);
      if (!$lead_id) {
        throw new \Exception("Can't find a lead.");
      }
      $lead_table = TableRegistry::getTableLocator()->get('LeadEntity')->get($lead_id)->toArray();
      $partner = TableRegistry::getTableLocator()->get('PartnerEntity')->get($lead_table['partner_id'])->toArray();
      return $this->setJsonResponse(['success' => true, 'data' => $partner]);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }


  public function deleteLeadNote()
  {
    try {
      $data = $this->request->getData();
      $note_id = $data['note_id'];

      $leadNotesTable = TableRegistry::getTableLocator()->get('LeadNotesEntity');
      $note = $leadNotesTable->get($note_id);

      $lead_id = $note['lead_id'];

      $note->status = 'Deleted';
      if ($leadNotesTable->save($note)) {

        $nextActiveNote = $leadNotesTable->find()
            ->where(['lead_id' => $lead_id, 'status !=' => 'Deleted'])
            ->order(['note_id' => 'DESC'])
            ->first();

        $leadAssociatedDataEntityTable = TableRegistry::getTableLocator()->get('LeadAssociatedDataEntity');

        $leadAssociatedDataEntity = $leadAssociatedDataEntityTable->find()
            ->where(['lead_id' => $lead_id])
            ->first();

        if ($leadAssociatedDataEntity) {
            if ($nextActiveNote) {
                $next_active_note_id = $nextActiveNote->note_id;
            } else {
                $next_active_note_id = null;
            }
            if ($leadAssociatedDataEntity->max_note_id == $note_id) {
                $leadAssociatedDataEntity->max_note_id = $next_active_note_id;
                $leadAssociatedDataEntityTable->save($leadAssociatedDataEntity);
            }
        }
        return $this->setJsonResponse(['success' => true]);
      } else {
        throw new \Exception("Error updating note status");
      }
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }


  public function editLeadNotes()
  {
    try {
      $data = $this->request->getData();
      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }

      $note_id = $data['note_id'];
      $leadNotesTable = TableRegistry::getTableLocator()->get('LeadNotesEntity');
      $note = $leadNotesTable->get($note_id);
      //No content changed
      if ($note->notes == $this->request->getData('notes')) {
        return $this->setJsonResponse(['success' => true]);
      }

      //Update statues for old note
      $note->status = 'Edited';
      $note->is_referrer = $data['is_referrer'] ?? false;
      if ( !$leadNotesTable->save($note) ) {
        throw new \Exception("Error updating note status");
      }

      //create new record using the same data
      $params = $note->toArray();
      $params['partner_user_id'] = $user['partner_user_id'];
      $params['notes'] = $this->request->getData('notes');
      $params['created'] = date('Y-m-d H:i:s');
      $params['status'] = 'Active';
      $lead_note = $leadNotesTable->newEntity($params);

      if ($leadNotesTable->save($lead_note)) {
        return $this->setJsonResponse(['success' => true]);
      } else {
        throw new \Exception("Error updating note status");
      }
    } catch (RecordNotFoundException $e) {
      return $this->setJsonResponse(['error' => 'Invalid note_id'], 400);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  public function toggleLeadNotes()
  {
      try {
          $data      = $this->request->getData();
          $note_id   = $data['note_id'];
          if (isset($data['is_pinned'])) {
              $is_pinned = $data['is_pinned'];
          }

          if (isset($data['is_referrer'])) {
              $is_referrer = $data['is_referrer'];
          }

          $is_pinned = $data['is_pinned'];
          $leadNotesTable = TableRegistry::getTableLocator()->get('LeadNotesEntity');
          $note = $leadNotesTable->get($note_id);
          if (isset($is_pinned)) {
              $note->is_pinned = $is_pinned;
          }
          if (isset($is_referrer)) {
              $note->is_referrer = $is_referrer;
          }

          if ($leadNotesTable->save($note)) {
              return $this->setJsonResponse(['success' => true], 200);
          } else {
              throw new \Exception("Error updating note status");
          }
      } catch (\Exception $e) {
          Log::error($e->getMessage());
          Log::error($e->getTraceAsString());
          return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
      }
  }

  public function getSentLenders($lead_ref)
  {
    try {
      $lend_internal_auth = new LendInternalAuth;
      $lead_id = $lend_internal_auth->unhashLeadId($lead_ref);

      $sales_table = $this->getTableLocator()->get('SaleEntity');
      $sales = $sales_table->find('all')
        ->select(['sale_id', 'sale_ref', 'match_result_ref', 'created', 'status', 'failed', 'rejected', 'sent_from',])
        ->contain([
          'LenderProductEntity' => [
            'fields' => ['product_name',]
          ],
          'LenderProductEntity.LenderEntity' => [
            'fields' => ['lender_name', 'shorthand', 'lender_logo', 'manual_lender_leads_status', 'integration_type', ]
          ],
          'DealsEntity',
          'DealsEntity.LendStatusEntity' => [
            'fields' => ['status_name', 'group_name']
          ],
          'DealsEntity.PartnerCommissionEntity',
        ])
        ->where([
          'SaleEntity.lead_id' => $lead_id,
          'SaleEntity.status != 0',
        ])
        ->toArray();
      $sales = $this->__removeLeadId($sales);
      // Get sales match details:
      if (!empty($sales)) {
        $match_result_refs = [];
        foreach ($sales as &$sale) {
          $sale['lender_match'] = null;
          if (!empty($sale['match_result_ref'])) {
            $match_result_refs[] = $sale['match_result_ref'];
          }
        }
        if (!empty($match_result_refs)) {
          $sales = $this->_mapMatchResult($this->_getMatchResultDetails($match_result_refs), $sales);
        }
      }

      return $this->setJsonResponse(['sales' => $sales]);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  public function getLeadUploadTracking($upload_id, $lead_ref)
  {
    try {
      $lend_internal_auth = new LendInternalAuth;
      $lead_id = $lend_internal_auth->unhashLeadId($lead_ref);

      $upload_table = TableRegistry::getTableLocator()->get('LeadLenderUploadTracking');
      $upload = $upload_table->find('all')->where(['upload_id' => $upload_id, 'lead_id' => $lead_id])->toArray();

      // Collect all the lender IDs that have received the upload
      $lenders = [];
      $upload_timestamps = [];
      foreach ($upload as $u) {
        $lenders[] = $u['lender_id'];
        $upload_timestamps[$u['lender_id']] = $u->created;
        $upload_status[$u['lender_id']] = $u->status;
      }

      $sales_table = $this->getTableLocator()->get('SaleEntity');
      $sales = $sales_table->find('all')
        ->select(['sale_id', 'sale_ref', 'product_id', 'match_result_ref', 'created', 'status', 'failed', 'rejected', 'sent_from'])
        ->contain([
          'LenderProductEntity' => [
            'fields' => ['product_name']
          ],
          'LenderProductEntity.LenderEntity' => [
            'fields' => ['lender_id', 'shorthand', 'lender_name', 'manual_lender_leads_status', 'integration_type', 'receive_uploads_email', 'has_document_api']
          ],
        ])
        ->where([
          'SaleEntity.lead_id' => $lead_id,
          'SaleEntity.status != 0',
        ]);

      $received_lenders = [];
      $unreceived_lenders = [];

      foreach ($sales as $sale) {
        $lender_entity = $sale->product->lender;
        if (in_array($lender_entity->lender_id, $lenders)) {
          $lender_entity->created = $upload_timestamps[$lender_entity->lender_id];
          $lender_entity->sale_id = $sale->sale_id;
          $lender_entity->status = $upload_status[$lender_entity->lender_id];
          $received_lenders[] = $lender_entity;
        } else {
          // Check for Intermediary integration type
          if ($lender_entity->integration_type === 'Intermediary') {
            // If it's an intermediary, the lender can receive uploads
            $lender_entity->sale_id = $sale->sale_id;
            $lender_entity->sale_ref = $sale->sale_ref;
            $lender_entity->product_id = $sale->product_id;
            $unreceived_lenders[] = $lender_entity;
            continue;
          }

          if (empty($lender_entity->receive_uploads_email)) {
            if ($lender_entity->has_document_api == 1) {
              // Lender has document API
              $lender_entity->sale_id = $sale->sale_id;
              $lender_entity->sale_ref = $sale->sale_ref;
              $lender_entity->product_id = $sale->product_id;
              $unreceived_lenders[] = $lender_entity;
              continue;
            }
          } else {
            // If the lender has a valid email, they can receive uploads
            $lender_entity->sale_id = $sale->sale_id;
            $lender_entity->sale_ref = $sale->sale_ref;
            $lender_entity->product_id = $sale->product_id;
            $unreceived_lenders[] = $lender_entity;
            continue;
          }
        }
      }

      return $this->setJsonResponse([
        'received_lenders' => $received_lenders,
        'unreceived_lenders' => $unreceived_lenders
      ]);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  private function _getMatchResultDetails($match_result_refs)
  {
    Log::info('---- START _getMatchResultDetails ----');
    Log::info($match_result_refs);
    $http = new Client();
    $header = [
        'type' => 'json',
        'headers' => ['x-api-key' => getenv('MATCHING_ENGINE_KEY')]
    ];
    $response = $http->get(getenv('DOMAIN_MATCHING_ENGINE') . "/get-match-results-by-match-refs/" . implode(',', $match_result_refs), null, $header);
    Log::info($response->getJson());
    return $response->getJson();
  }

  private function _mapMatchResult($match_results, $sales)
  {
    if (!empty($match_results['data']['matches'])) {
      foreach ($sales as &$sale) {
        foreach ($match_results['data']['matches'] as $match) {
          if ($match['match_result_ref'] === $sale['match_result_ref']) {
            $sale['lender_match'] = $match;
            break;
          }
        }
      }
    }
    return $sales;
  }



  public function sendPayoutLetterRequest(){
    return $this->sendPayoutLetterLoanStatementRequest('PayoutLetterRequest');
  }
  public function sendLoanStatementRequest(){
    return $this->sendPayoutLetterLoanStatementRequest('LoanStatementRequest');
  }

  private function sendPayoutLetterLoanStatementRequest($notifLookupCode){
    try {
      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }
      $data = $this->request->getData();
      if (empty($data['commission_id'])) {
        throw new \Exception("Required field is missing: commission_id");
      }
      $lead = TableRegistry::getTableLocator()->get('PartnerCommissionEntity')->get($data['commission_id']);
      if (empty($lead)) {
        throw new \Exception("Commission record not found");
      }
      //check if user has access to it.
      if (empty($lead->lead_id)) {
        throw new \Exception("Commission record is not linked to a lead");
      }
      $contain = [
        'LeadEntity'=> ['fields' => ['lead_id', 'loan_term_requested']],
        'DealsEntity'=> ['fields' => ['deal_id', 'sale_id']],
        'DealsEntity.SaleEntity'=> ['fields' => ['sale_id', 'product_type_id','product_id', 'sale_ref']],
        'DealsEntity.SaleEntity.PartnerProductTypeEntity'=> ['fields' => ['product_type_id', 'product_type_name']],
        'DealsEntity.SaleEntity.LenderProductEntity'=> ['fields' => ['lender_product_id', 'product_name', 'lender_id']],
        'DealsEntity.SaleEntity.LenderProductEntity.LenderEntity'=> ['fields' => ['lender_id', 'admin_email']],
      ];
      $options = ['contain' => $contain];

      $where = ['commission_id' => $data['commission_id']];
      $partner_commission = TableRegistry::getTableLocator()
        ->get('PartnerCommissionsEntity')
        ->find('all', $options)
        ->select(['lead_id', 'deal_id', 'contract_ref', 'funded_amount'])
        ->where($where)->distinct()->first();

      $data = $partner_commission->toArray();
      // return $this->setJsonResponse(['success' => true, 'data' => $data]);

      $to = $data['deal']['sale']['product']['lender']['admin_email']?? null;
      if(!$to){
        throw new \Exception("Lender admin_email is not configured");
      }

      $adhocData = [];
      $adhocData['contract_ref'] = $data['contract_ref'];
      $adhocData['product_type'] = $data['deal']['sale']['product_type']['product_type_name']??'';
      $adhocData['funded_amount'] = $data['funded_amount'];
      $adhocData['loan_term_requested'] = $data['lead']['loan_term_requested']??'';
      $adhocData['funded_date'] = $data['funded_date'];
      $adhocData['product_name'] = $data['deal']['sale']['product']['product_name']??'';
      $adhocData['sale_ref'] = $data['deal']['sale']['sale_ref']??'';

      $extraData = ['from' => $user['email'], 'to' => $to];
      $notificationSend = TableRegistry::getTableLocator()->get('PartnerNotifications')->sendPartnerNotifications($this->Auth->user('partner_id'), $notifLookupCode, $lead->lead_id, $adhocData, null, $extraData);
      return $this->setJsonResponse(['success' => true, 'data' => $notificationSend]);
      $success = true;
      if (empty($notificationSend)) {
        $success = false;
      } else {
        foreach ($notificationSend as $notification) {
          if ($notification['successfully_sent'] === false) {
            $success = false;
          }
        }
      }
      return $this->setJsonResponse(['success' => $success]);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  /**
   * Get the required fields data for matching and submission
   * @param mixed $lead_ref
   * @throws \Exception
   * @return void
   */
  public function getRequiredFieldsData($lead_ref)
  {
    try
    {
      $leadData = $this->view($lead_ref, true);
      if(!empty($leadData['error']))
        throw new \Exception($leadData['error'], $leadData['code']);

      //test lead id - 14e9ufP
      // $fv = $this->getValueByPath($leadData, 'owners_all[0].first_name');
      // $fv = $this->getValueByPath($leadData, 'owners_all.first_name');
      // $fv = $this->getValueByPath($leadData, 'all_addresses');
      // $fv = $this->getValueByPath($leadData, 'sales_monthly');
      // $fv = $this->getValueByPath($leadData, 'asset_finance.condition');
      // $fv = $this->getValueByPath($leadData, 'all_addresses{address_type=trading}.country');
      // Log::error($fv);
      // return $this->setJsonResponse([$fv]);

      $endToEnd = !is_null($this->getValueByPath($leadData, 'asset_finance.contract_type'));

      $mapTable = TableRegistry::getTableLocator()->get('ConfigFieldProductTypeMapEntity', [
        'connection' => ConnectionManager::get('reader_db')
        ]);
      $missingFields = [];
      foreach(['match', 'submission'] as $fieldType){
        $missingFields[$fieldType] = [];
        $requiredField = 'required_'.$fieldType.($endToEnd? '_e2e':'');
        $requiredJson = 'required_'.$fieldType.'_check'.($endToEnd? '_e2e':'');
        $messageField = 'message_'.$fieldType;
        $where = is_null($leadData['product_type_id']) ? ['product_type_id IS' => null] : ['product_type_id' => $leadData['product_type_id'], $requiredField => 1];
        $mappings = $mapTable->find('all', ['contain'=> ['ConfigFieldEntity']])->where($where);
        foreach ($mappings as $fieldMapping){
          $missingFields[$fieldType] = array_merge($missingFields[$fieldType], $this->validateField($leadData, $fieldMapping, $requiredJson, $messageField));
        }
      }
      return $this->setJsonResponse($missingFields);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  /**
   * Validate field data
   * Used by getRequiredFieldsData
   * @param mixed $leadData
   * @param mixed $fieldMapping
   * @param mixed $requiredJson
   * @param mixed $messageField
   * @return array
   */
  private function validateField($leadData, $fieldMapping, $requiredJson, $messageField){
    $path = $fieldMapping['field']['payload_check_path'];
    $criteria = $fieldMapping[$requiredJson];
    $return = [];

    $val = $this->getValueByPath($leadData, $path);
    $ownerIndex = $this->getOwnerIndex($path);
    $owner = ($ownerIndex !== null) ? $this->getOwner($leadData, $ownerIndex) : null;

    if ($criteria == null || !is_array($criteria)) {
      if(is_array($val)){
        if($this->getTotalNodesInPath($path) === 1){
          $return[] = $this->getMessage(empty($val), $leadData, $path, $fieldMapping, $messageField);
        }
        else{
          $lastNode = $this->getNthNodeFromEndByPath($path,1);
          if (is_null($ownerIndex)) {
            foreach ($val as $i => $arr) {
              $v = $this->getValueByPath($arr, $lastNode);
              $return[] = $this->getMessage(is_null($v) || (is_string($v) && (strlen($v) === 0)), $leadData, $path, $fieldMapping, $messageField, false, $i, $arr);
            }
          }
          else{
            foreach ($val as $i => $arr) {
              $return[] = $this->getMessage(is_null($arr[$lastNode]) || (is_string($arr[$lastNode]) && (strlen($arr[$lastNode]) === 0)), $leadData, $path, $fieldMapping, $messageField, false, $ownerIndex, $owner);
            }
          }
        }
      }
      else{
        $return[] = $this->getMessage(empty($val), $leadData, $path, $fieldMapping, $messageField, false, $ownerIndex, $owner);
      }
    }
    else
    {
      $ownerData = null;
      if (is_array($val) && isset($val[0]) && isset($val[0]['point_of_contact']))//means this is a lead owner
      {
        $pathRest = $this->getRestOfPathAfterOwnersAll($path);
        foreach($val as $oi => $o){
          $v = $this->getValueByPath($o, $pathRest);
          $ownerData[$oi] = ['value' => $v, 'missing' => false, 'exempt' => false, 'owner' => $o];
        }
      }
      $missing = false;
      $isExempt = false;
      foreach($criteria as $rule){
        $checkRule = true;
        if(isset($rule["fieldValue"])){
          if(isset($rule["fieldValue"]["field"])//single rule
            && isset($rule["fieldValue"]["value"]))
          {
            $checkRule = $this->fieldValueConditionCheck($leadData, $rule["fieldValue"]);
          } else { //array of field values expected
            // dump($rule["fieldValue"]);
            foreach($rule["fieldValue"] as $fvi){
              $res = $this->fieldValueConditionCheck($leadData, $fvi);
              if($res === false){
                $checkRule = $res;
                break;//if one fieldValue check fails, no point checking the rest
              }
            }
          }
        }
        if($checkRule === true){
          foreach($rule as $func => $params){
            if($func === "fieldValue")
              continue;
            if(is_array($params)){
              $functionName = 'validate_' . $func;
              if (method_exists($this, $functionName)){
                if($ownerData){
                  foreach($ownerData as $oi => $o){
                    $res = $this->$functionName($path, $leadData, $params, $o['value']);
                    if (!$res)
                      $ownerData[$oi]['missing'] = true;
                  }
                }
                else
                {
                  $result = $this->$functionName($path, $leadData, $params);
                  if (!$result)
                    $missing = true;
                }
              }
            }
          }
        }
        else{
          $isExempt = true;
          if (!empty($ownerData)) {
            foreach ($ownerData as &$o) {
              $o['exempt'] = true;
            }
          }
        }
      }
      if($ownerData){
        foreach($ownerData as $oi => $o){
          $return[] = $this->getMessage($o['missing'], $leadData, $path, $fieldMapping, $messageField, $o['exempt'], $oi, $o['owner']);
        }
      }
      else
        $return[] = $this->getMessage($missing, $leadData, $path, $fieldMapping, $messageField, $isExempt, $ownerIndex, $owner);
    }
    return $return;
  }
  /**
   * Checks if field value condition has passed or not
   * @param mixed $leadData
   * @param mixed $fvi
   * @return bool
   */
  private function fieldValueConditionCheck($leadData, $fvi)
  {
    $checkRule = false;
    $fv = $this->getValueByPath($leadData, $fvi["field"]);
    $valIsArray = is_array($fvi["value"]);
    //fix null values
    if($valIsArray){
      foreach($fvi["value"] as &$v){
        if(is_string($v) && (strtolower($v) === "null")){
          $v = NULL;
        }
      }
    }
    else if(is_string($fvi["value"]) && (strtolower($fvi["value"]) === "null")){
      $fvi["value"] = NULL;
    }
    if (isset($fvi["operator"]) && in_array($fvi["operator"], ['not_equal', 'not_in'])) {
      if((($valIsArray && !in_array($fv, $fvi["value"]))) || (!$valIsArray && ($fv !== $fvi["value"]))){
        $checkRule = true;
      }
    }
    else if((($valIsArray && in_array($fv, $fvi["value"]))) || (!$valIsArray && ($fv === $fvi["value"]))){
      $checkRule = true;
    }
    return $checkRule;
  }
  /**
   * Creates message for a field validation
   * Used by validateField
   *
   * @param boolean $isMissing
   * @param array $leadData
   * @param mixed $path
   * @param mixed $fieldMapping
   * @param mixed $messageField
   * @param boolean $isExempt
   * @param mixed $ownerIndex
   * @param mixed $owner
   * @return array
   */
  private function getMessage($isMissing, $leadData, $path, $fieldMapping, $messageField, $isExempt = false, $ownerIndex = null, $owner = null){
    $leadRef = $leadData['lead_ref'];
    $leadType = $leadData['lead_type'];
    $response = [];
    $appRef = $owner ? $owner['owner_ref'] : null;
    if (strtolower(getenv('REGION', true)) === 'nz') {
      // Replace 'ABN' with 'NZBN' in the field name
      $fieldMapping['field']['name'] = str_replace('ABN', 'NZBN', $fieldMapping['field']['name']);
    }
    $message = $fieldMapping[$messageField] ?? $fieldMapping['field']['name'] . ' is required'.($owner? ' for '.$owner['first_name'].' '.$owner['last_name']:'');
    $message = str_replace(['{leadRef}', '{applicantRef}'], [$leadRef, ($appRef??'{applicantRef}')], $message);
    $response['message'] = $message;
    $modPath = str_replace(["[", "]"], [".",""], $path);

    //inject specified owner index if $owner specified
    if($owner){
      $secondLastNode  = $this->getNthNodeFromEndByPath($modPath, 2);
      if(!is_numeric($secondLastNode)){
        $lastNode = $this->getNthNodeFromEndByPath($modPath, 1);
        $modPath = substr($modPath, 0, -(strlen($lastNode)));
        $modPath = $modPath . $ownerIndex .".". $lastNode;
      }
    }

    //replace owners_all numeric index with owner_ref
    // Define the regular expression pattern
    $pattern = '/^owners_all\.(\d+)\./';
    if (preg_match($pattern, $modPath, $matches)) {
      $number = $matches[1];
      $appRef = $this->getValueByPath($leadData, 'owners_all[' . $number . '].owner_ref');
      $response['message'] = str_replace('{applicantRef}', $appRef, $response['message']);
      // Replace the number value with the value from $appRef
      $modPath = 'owners_all.' . $appRef . '.' . substr($modPath, strlen($matches[0]));
    }

    $response['check_path'] = $modPath;
    $response['is_missing'] = $isMissing;
    $response['is_exempt'] = $isExempt;
    $route = $fieldMapping['field'][$leadType . '_path'];
    if($route)
      $route = str_replace(['{leadRef}', '{applicantRef}'], [$leadRef, ($appRef??'{applicantRef}')], $route);
    $response['route'] = $route;
    return $response;
  }

  /**
   * Validation function to check if fields are NOT empty
   * used by validateField
   * @param mixed $path
   * @param mixed $leadData
   * @param mixed $params
   * @return bool
   */
  private function validate_notEmpty($path, $leadData, $params, $ov = null)
  {
    $valid = false;
    foreach($params as $field){
      if(!empty($this->getValueByPath($leadData, $field)))
        $valid = true;
    }
    return $valid;
  }
  /**
   * Validation function to check if date range(s)have a minimum duration (in months)
   * used by validateField
   * @param mixed $path
   * @param mixed $leadData
   * @param mixed $params
   * @return bool
   */
  private function validate_minDurationMonths($path, $leadData, $params, $ov = null)
  {
    if(isset($params['months']) && is_int($params['months'])){
      $months = $params['months'];
      $obj = $ov ?? $this->getValueByPath($leadData, $path);
      if(is_null($obj)){
        return (0 >= $months);
      }
      if(!is_array($obj))
        $obj = [$obj];
      $conditions = [];
      if(isset($params['conditions']) && is_array($params['conditions'])){
        foreach($params['conditions'] as $condition){
          if(isset($condition['equal']) && is_array($condition['equal'])){
            foreach($condition['equal'] as $f => $v){
              $conditions[$f] = $v;
            }
          }
        }
      }

      //remove elements that don't meet conditions
      if (!empty($conditions)) {
        $obj = array_filter($obj, function($o) use ($conditions) {
            return empty(array_diff_assoc($conditions, $o));
        });
      }

      $indexesToRemove = [];
      $maxDurationRowValues = [];
      foreach($obj as $i => $o){
        $durationMonths = $this->getDurationMonths($o['date_from'], $o['date_to']);
        $obj[$i]['durationMonths'] = $durationMonths;

        //if maxDurationOnly has been specified, this is used to only retain row with max duration for a specified column value combination
        if(isset($params['maxDurationOnly']) && is_array($params['maxDurationOnly'])){
          foreach($params['maxDurationOnly'] as $col => $vals){
            if(isset($o[$col]) && in_array($o[$col], $vals)){//has a value we are looking for
              if(isset($maxDurationRowValues[$col]) && isset($maxDurationRowValues[$col][$o[$col]]))
              {
                if($maxDurationRowValues[$col][$o[$col]]['months'] < $durationMonths){//higher duration, discard old row
                  $indexesToRemove[] = $maxDurationRowValues[$col][$o[$col]]['index'];
                  $maxDurationRowValues[$col][$o[$col]]['months'] = $durationMonths;
                  $maxDurationRowValues[$col][$o[$col]]['index'] = $i;
                }
                else{//same or lesser duration, discard row
                  $indexesToRemove[] = $i;
                }
              }
              else if(!isset($maxDurationRowValues[$col])|| !isset($maxDurationRowValues[$col][$o[$col]])){
                $maxDurationRowValues[$col][$o[$col]]['months'] = $durationMonths;
                $maxDurationRowValues[$col][$o[$col]]['index'] = $i;
              }
            }
          }
        }
      }

      //remove any indexes that need to be removed
      foreach($indexesToRemove as $ind){
        unset($obj[$ind]);
      }

      $duration = array_sum(array_column($obj, 'durationMonths'));
      return ($duration >= $months);
    }
    return false;
  }

  /**
   * Get duration between dates in months
   * This can have issues with dates ending in feb end, will need to test wtih edge cases and fix
   */
  private function getDurationMonths($from, $to){
    $duration = 0;
    $toDate = !empty($to) ? new DateTime($to) : new DateTime();
    $fromDate = new DateTime($from);
    $fromYear = $fromDate->format('Y');
    $fromMonth = $fromDate->format('m');
    $toYear = $toDate->format('Y');
    $toMonth = $toDate->format('m');
    $yearDifference = $toYear - $fromYear;
    $monthDifference = $toMonth - $fromMonth;
    $totalMonths = ($yearDifference * 12) + $monthDifference;

    $toDateNextDate = (clone $toDate)->modify('+ 1 day');
    $interval = $toDateNextDate->diff($fromDate);
    $totalMonths = ($interval->y * 12) + $interval->m;

    if ($fromYear == $toYear && $fromMonth == $toMonth) {
      // Check if it's a complete month
      if ($fromDate->format('d') == 1 && $toDate->format('d') == $toDate->format('t')) {
        $totalMonths = 1; // Complete month
      }
      else{
        $totalMonths = 0; // Not a complete month
      }
    }
    $duration += $totalMonths;
    return $duration;
  }

  /**
   * Validation function to check if the value(s) matches condition
   * @param mixed $path
   * @param mixed $leadData
   * @param mixed $params
   * @return bool
   */
  private function validate_checkValue($path, $leadData, $params)
  {
    if(isset($params['field']) && !is_null($params['field']) && isset($params['value'])){
      $fv = $this->getValueByPath($leadData, $params['field']);
      $valIsArray = is_array($params['value']);
      //fix null values
      if($valIsArray){
        foreach($params['value'] as &$v){
          if(is_string($v) && (strtolower($v) === "null")){
            $v = NULL;
          }
        }
      }
      else if(is_string($params['value']) && (strtolower($params['value']) === "null")){
        $params['value'] = NULL;
      }
      if (isset($params["operator"]) && in_array($params["operator"], ['not_equal', 'not_in'])) {
        if((($valIsArray && !in_array($fv, $params["value"]))) || (!$valIsArray && ($fv !== $params["value"]))){
          return true;
        }
      }
      else if((($valIsArray && in_array($fv, $params["value"]))) || (!$valIsArray && ($fv === $params["value"]))){
        return true;
      }
    }
    return false;
  }

/**
 * Get value at path in lead/view payload
 *
 * @param mixed $array
 * @param mixed $path
 * @return mixed
 */
private function getValueByPath($array, $path) {
  // Split the path into individual keys
  $keys = preg_split('/\.(?![^\[]*\])/', $path, -1, PREG_SPLIT_NO_EMPTY);

  // Extract the first key
  $currentKey = array_shift($keys);

  // Extract the array index if present
  preg_match('/^(\w+)(?:\{([^}]+)\})?(?:\[(\d+)\])?$/', $currentKey, $matches);

  $currentKey = $matches[1];
  $filtersString = isset($matches[2]) ? $matches[2] : null;
  $index = isset($matches[3]) ? intval($matches[3]) : null;

  $filters = [];
  if ($filtersString !== null && strlen($filtersString) > 0) {
      // Split the filters string by comma
      $filterPairs = explode(',', $filtersString);
      foreach ($filterPairs as $pair) {
          // Split each pair by '=' to separate key and value
          list($key, $value) = explode('=', $pair, 2);
          // Convert numeric values to numbers if possible
          $value = is_numeric($value) ? ($value * 1) : $value;
          // Add key-value pair to filters array
          $filters[$key] = $value;
      }
  }

  //numeric indexed array
  if (!empty($array[$currentKey]) && is_array($array[$currentKey]) && array_keys($array[$currentKey]) === range(0, count($array[$currentKey]) - 1)) {
    //filter out elements which don't meet conditions
    foreach($array[$currentKey] as $ind => $el){
      foreach($filters as $filter => $val){
        if(!isset($el[$filter]) || $el[$filter] !== $val){
          unset($array[$currentKey][$ind]);
          continue 2;
        }
      }
    }
    //reindex array
    $array[$currentKey] = array_values($array[$currentKey]);
    if($index === null)
      return $array[$currentKey];
  }

  // If the key doesn't exist or if it's an indexed array access and index doesn't exist, return null
  if (!isset($array[$currentKey]) || ($index !== null && !isset($array[$currentKey][$index]))) {
    return null;
  }

  // If there are no more keys left, return the value
  if (empty($keys)) {
    // If the key represents an indexed array access, return the indexed element; otherwise return the value
    return ($index !== null) ? $array[$currentKey][$index] : $array[$currentKey];
  }

  // Update the array to the next level based on the key and index if present
  $array = ($index !== null) ? $array[$currentKey][$index] : $array[$currentKey];

  // Handle array access keys
  if (is_array($array)) {
      // Recursive call with the nested array and remaining keys
      $nextPath = implode('.', $keys);
      return $this->getValueByPath($array, $nextPath);
  }

  // If it's not an array, return null
  return null;
}

  /**
   * Get node at specified position in lead/view json path
   * @param mixed $path
   * @param mixed $n
   * @return mixed
   */
  private function getNthNodeFromEndByPath($path, $n) {
    // Split the path into individual keys
    $keys = preg_split('/[\[\]\.]+/', $path, -1, PREG_SPLIT_NO_EMPTY);

    // Get the length of the keys array
    $length = count($keys);

    // Calculate the index of the nth node from the end
    $index = $length - $n;

    // Return the nth node from the end
    return isset($keys[$index]) ? $keys[$index] : null;
  }

   /**
    * Get total nodes in lead/view json path
    * @param mixed $path
    * @return int
    */
  private function getTotalNodesInPath($path) {
    // Split the path into individual keys
    $keys = preg_split('/[\[\]\.]+/', $path, -1, PREG_SPLIT_NO_EMPTY);

    // Return the count of keys
    return count($keys);
  }

  /**
   * Get owner index from path (if present)
   * @param mixed $path
   * @return int|null
   */
  private function getOwnerIndex($path) {
    // Define the regular expression pattern
    $pattern = '/owners_all\[(\d+)\]/';
    // Perform the regular expression match
    if (preg_match($pattern, $path, $matches)) {
        // Extract the number from the matched pattern
        $number = $matches[1];
        if (is_numeric($number)) {
          return (int)$number; // Convert to integer and return
        }
    } else {
        // Return null if no match found
        return null;
    }
  }

  /**
   * Get owner from owner index
   * @param mixed $leadData
   * @param mixed $index
   * @return mixed
   */
  private function getOwner($leadData, $index){
    if (isset ($leadData['owners_all'][$index]))
      return $leadData['owners_all'][$index];
    return null;
  }

  /**
   * get rest of path after owners_all in a path
   * @param mixed $string
   * @return mixed
   */
  private function getRestOfPathAfterOwnersAll($string) {
    // Match "owners_all" followed by a dot and capture the rest of the path
    if (preg_match('/owners_all\.(.+)/', $string, $matches)) {
        return $matches[1]; // Return the captured rest of the path
    } else {
        return null; // Return null if "owners_all" is not found
    }
  }


  /**
   * getReferrerPersonPartnerUserId, for auto send lead to intermediary lender from star, create referrer and person if not exist
   *
   * @param  mixed $lead_id
   * @return int
   */
  public function getReferrerPersonPartnerUserId($lead_id)
  {
    $lead_table = TableRegistry::getTableLocator()->get('LeadEntity');
    $lead = $lead_table->get($lead_id, [
      'contain' => [
        'PartnerUserLeadsEntity',
        'PartnerEntity',
        'PartnerEntity.PointOfContact',
      ]
    ]);
    $userId = false;
    if ($lead->partner_user_lead) {
      $userId = $lead->partner_user_lead->partner_user_id;
    } elseif ($lead->partner->point_of_contact) {
      $userId = $lead->partner->point_of_contact->partner_user_id;
    }
    return $userId;
  }

  public function isCustomTemplate($templateUseName)
  {
    try {
      $partnerUser = $this->Auth->user();
      if (empty($partnerUser)) {
        $partnerUser = $this->Auth->identify();
      }
      $partner = $this->loadModel('Partners')->getPartner(['partner_id' => $partnerUser['partner_id']]);
      $partnerRef = LendInternalAuth::hashPartnerId($partner['partner_id']);
      $userRef = $partnerUser['partner_user_ref'];
      $requestBuilder = new SignatureServiceRequestBuilder("", $partnerUser);
      $data = $requestBuilder
          ->setFlowVariationOf($templateUseName)
          ->getRequestBody();
      $finalTemplateUseName = $data['flow'];
      $sign = new LendSignatureServiceBackendClient($partnerRef, $userRef);
      $sign->checkCreateLendSignatureUser($partnerUser);
      $response = $sign->call('GET', '/get-template-by-use/' . $finalTemplateUseName);
      return $this->setJsonResponse([
        'success' => true,
        'has_template' => $response['has_template'],
        'template_use_name' => $finalTemplateUseName,
      ]);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }

  }

  public function importLead()
  {
    $data = $this->request->getData();
    if (!(new LendInternalAuth)->checkSignature($this->request->getQuery('auth_sig'), $data['partner_id']))
      return $this->setJsonResponse(array('success' => false, 'message' => 'Invalid Signature'));

    $lead = $data['lead'];
    $lead = $this->__removeLeadId(
      $lead,
      [
        'owner_ref',
        'last_sent_by_user_id',
        'last_completed_by_user_id',
        'id',
        'partner_lead_upload_id',
        'lead_owner_address_id',
        'lead_owner_employment_id',
        'partner_lead_upload_meta_id',
        'abn_id',
        'lead_ref',
        'man_status_id',
        'account_id',
        'callback_id',
        'note_id',
        'account_ref',
        'breakdown_sum_check'
      ],
      [
        'id',
        'lead_id',
        'lead_asset_id',
        'lead_liability_id',
        'partner_account_id',
        'partner_account_people_id',
        'lead_address_id',
        'lead_asset_finance_id',
        'referrer_person_id',
        'lender_status_id',
        'latest_commission_id',
        'intermediary_original_partner_id',
        'nzbn_id',
        'outcome_id',
        'tag_id',
        'partner_crb_tran_id',
        'sale_id',
        'match_result_ref',
        'bs_analysis_id',
        'partner_applicant_group_id'
      ],
      [
        'man_status_histories',
        'partner_user_lead',
        'serviceability',
        'partner_account',
        'partner_account_meta',
        'purpose',
        'check_email',
        'check_mobile',
        'shared'
      ]);
      $lead['partner_id'] = $data['partner_id'];
      $lead = TableRegistry::getTableLocator()->get('LeadEntity')->createLead($lead);
      return $this->setJsonResponse($lead);
  }

  public function getLeadOwners() {
    try {

      if (!$this->request->is('post')) {
        throw new \Exception("Only POST request is allowed.");
      }

      $data = $this->request->getData();
      $leadRefs = $data['lead_refs'];

      $leadIds = [];
      $lend_internal_auth = new LendInternalAuth;

      foreach ($leadRefs as $leadRef) {
        $leadIds[] = $lend_internal_auth->unhashLeadId($leadRef);
      }

      $leadOwners = TableRegistry::getTableLocator()->get('LeadOwnersEntity')
        ->find('all')
        ->where(['lead_id IN' => $leadIds])
        ->toArray();

      $leadOwners = array_map(function($leadOwner) use ($lend_internal_auth) {
        $leadOwner['lead_ref'] = $lend_internal_auth->hashLeadId($leadOwner['lead_id']);
        return $leadOwner;
      }, $leadOwners);

      if (empty($leadOwners)) {
        return $this->setJsonResponse(['success' => false, 'message' => 'No account people found.']);
      } else {
        return $this->setJsonResponse(['success' => true, 'data' => $leadOwners]);
      }

    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  public function checkLeadOwnerHistory($email) {
    try {
      if (empty($email)) {
        throw new \Exception("Email is required.");
      }

      $leadOwnerCreated = TableRegistry::getTableLocator()->get('LeadOwnersEntity')
        ->find()
        ->select(['LeadOwnersEntity.email', 'LeadEntity.created'])
        ->leftJoinWith('LeadEntity')
        ->where([
          'LeadOwnersEntity.email' => $email,
          'LeadOwnersEntity.point_of_contact' => 1
        ])
        ->order(['LeadOwnersEntity.owner_id' => 'DESC'])
        ->first();

      if (!empty($leadOwnerCreated) && isset($leadOwnerCreated->_matchingData['LeadEntity']['created'])) {
        $createdDate = $leadOwnerCreated->_matchingData['LeadEntity']['created'];
        return $this->setJsonResponse(['success' => true, 'created' => $createdDate]);
      } else {
        return $this->setJsonResponse(['success' => true, 'created' => null]);
      }

    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }
  //updateLeadNotificationPartnerUsers
  public function updateLeadNotificationPartnerUsers() {
    try {
      $data = $this->request->getData();

      if (empty($data['lead_ref'])) {
        throw new \Exception("lead_ref must be provided");
      }

      $leadRef = $data['lead_ref'];
      $leadId = (new LendInternalAuth)->unhashLeadId($leadRef);
      $selectedIds = $data['selected_ids'] ?? [];
      $unselectedIds = $data['unselected_ids'] ?? [];

      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }

      if (empty($user) || empty($user['partner_user_id'])) {
        throw new \Exception("User authentication required");
      }

      $leadNotificationPartnerUsersTable = TableRegistry::getTableLocator()->get('LeadNotificationPartnerUsersEntity');
      $currentTime = date('Y-m-d H:i:s');
      $result = true;
      
      // Process selected_ids
      foreach ($selectedIds as $partnerUserRef) {
        $partnerUserId = (new LendInternalAuth)->unhashPartnerUserId($partnerUserRef);
        
        // Check if an ACCESS record already exists
        // we keep this one for safe side, frontend handles it
        $existingAccessRecord = $leadNotificationPartnerUsersTable->find()
          ->where([
            'lead_id' => $leadId,
            'partner_user_id' => $partnerUserId,
            'status' => 'ACCESS'
          ])
          ->first();
        
        if (!$existingAccessRecord) {
          $entityData = [
            'lead_id' => $leadId,
            'partner_user_id' => $partnerUserId,
            'assigned_by' => $user['partner_user_id'],
            'status' => 'ACCESS',
            'created' => $currentTime,
            'updated' => $currentTime
          ];
          
          $entity = $leadNotificationPartnerUsersTable->newEntity($entityData);
          if (!$leadNotificationPartnerUsersTable->save($entity)) {
            $result = false;
          }
        }
      }
      
      // Process unselected_ids
      foreach ($unselectedIds as $partnerUserRef) {
        $partnerUserId = (new LendInternalAuth)->unhashPartnerUserId($partnerUserRef);
        $accessRecords = $leadNotificationPartnerUsersTable->find()
          ->where([
            'lead_id' => $leadId,
            'partner_user_id' => $partnerUserId,
            'status' => 'ACCESS'
          ])
          ->toArray();
        foreach ($accessRecords as $record) {
          $record->status = 'REVOKED';
          $record->updated = $currentTime;
          
          if (!$leadNotificationPartnerUsersTable->save($record)) {
            $result = false;
          }
        }
      }

      return $this->setJsonResponse([
        'success' => $result,
        'message' => $result ? 'Lead notification partner users updated successfully' : 'Failed to update lead notification partner users',
        'data' => [
          'lead_ref' => $leadRef,
          'selected_ids' => $selectedIds,
          'unselected_ids' => $unselectedIds
        ]
      ]);

    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  public function getLeadOwnerForRetargetMarketing($owner_ref)
  {
    try {
      if (empty($owner_ref)) {
        throw new \Exception("owner_ref is required");
      }
      $lend_internal_auth = new LendInternalAuth;
      $owner_id = $lend_internal_auth->unhashOwnerId($owner_ref);
      
      if (empty($owner_id)) {
        throw new \Exception("Invalid owner_ref provided");
      }
      $leadOwnersTable = TableRegistry::getTableLocator()->get('LeadOwnersEntity');
      $leadOwner = $leadOwnersTable->find()
        ->contain([
          'LeadEntity' => [
            'fields' => ['lead_id', 'lead_ref', 'partner_id', 'source', 'created', 'lead_type', 'industry_id'],
            'LeadAbnLookupEntity',
            'NzbnLookupEntity',
            'PartnerEntity' => [
              'fields' => ['partner_id', 'logo']
            ]
          ],
          'OwnerAllEmployments' => [
            'LeadAbnLookupEntity',
            'NzbnLookupEntity'
          ],
          'OwnerAllAddresses',
          'CurrentAddress',
          'MailingAddress',
          'ConCreditGuideQuote',
          'ConCreditPropSendEntity',
          'ConPrelimOwnerEntity',
          'PartnerLeadUploadsEntity' => [
            'PartnerLeadUploadsMetaEntity'
          ],
          'CheckMobile' => [
            'fields' => ['id', 'value', 'result', 'next_checkable', 'CheckMobile__checked' => 'created']
          ],
          'CheckEmail' => [
            'fields' => ['id', 'value', 'result', 'next_checkable', 'CheckEmail__checked' => 'created', 'CheckEmail__disposable' => 'email_disposable']
          ],
          'LeadCreditScoreEntity' => [
            'queryBuilder' => function ($q) {
              return $q->where(['LeadCreditScoreEntity.source' => 'broker']);
            }
          ],
          'LeadOwnerIncomeEntity' => [
            'ConfigIncomeEntity',
            'ConIncomeShareEntity'
          ],
          'HomeLoanPayOffOption',
          'LatestIdCheck'
        ])
        ->where(['LeadOwnersEntity.owner_id' => $owner_id])
        ->first();

      if (empty($leadOwner)) {
        throw new \Exception("Lead owner not found");
      }

      // Remove sensitive IDs before returning
      $leadOwnerArray = $this->__removeLeadId($leadOwner->toArray(), ['owner_id', 'lead_id']);
      
      // Add complete logo URL if logo exists
      if (!empty($leadOwnerArray['lead']['partner']['logo'])) {
        $leadOwnerArray['lead']['partner']['logo_url'] = getenv('DOMAIN_FILES') . '/' . $leadOwnerArray['lead']['partner']['logo'];
      }

      return $this->setJsonResponse([
        'success' => true,
        'data' => $leadOwnerArray
      ]);

    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }
  // re-target marketing auth token verification only
  private function verifyAuthToken($receivedToken)
  {
    $authString = 'Bearer verification-auth-token-re-target-marketings';
    $salt = Security::getSalt();
    $saltedString = $salt . '#' . $authString;
    $expectedHash = sha1($saltedString);
    return hash_equals($expectedHash, $receivedToken);
  }

  public function sendSmsVerificationCode()
  {
    try {
      $authToken = $_SERVER['HTTP_X_AUTH_TOKEN'] ?? null;
      if (!$authToken || !$this->verifyAuthToken($authToken)) {
        throw new \Exception("Unauthorized - Invalid auth token", 401);
      }
      $data = $this->request->getData();
      $phoneNumber = $data['phone_number'];
      $smsVerificationTable = TableRegistry::getTableLocator()->get('SmsVerificationCodes');
      if ($smsVerificationTable->hasRecentCode($phoneNumber)) {
        throw new \Exception("Please wait 60 seconds before requesting another verification code");
      }

      $verificationCode = str_pad(rand(100000, 999999), 6, '0', STR_PAD_LEFT);
      
      // Prepare SMS data
      $data['phone_number'] = $phoneNumber;
      $data['message'] = 'Your Lend Platform verification code is: ' . $verificationCode . '. This code will expire in 10 minutes.';
      $data['notify_for_reply'] = 1;
      $data['recipient'] = 'Client';

      if (empty($data['phone_number'])) {
        throw new \Exception("phone_number is required");
      }

      if (empty($data['message'])) {
        throw new \Exception("message is required");
      }
      $recipient = [
        'mobile' => $data['phone_number']
      ];

      $item = [
        'recipient' => $data['recipient'] ?? 'Client',
        'body' => trim($data['message']),
        'notify_for_reply' => !empty($data['notify_for_reply']) ? $data['notify_for_reply'] : 0
      ];

      $partnerNotificationsTable = TableRegistry::getTableLocator()->get('PartnerNotifications');
      $ret = $partnerNotificationsTable->sendSMSNotification($recipient, $item);

      if (!$ret['success']) {
        throw new \Exception($ret['message']);
      }
      $verificationRecord = $smsVerificationTable->createVerificationCodeWithCode($phoneNumber, $verificationCode);

      if (!$verificationRecord) {
        Log::error("SMS sent successfully but failed to store verification code in database. Phone: {$phoneNumber}, Code: {$verificationCode}");
        throw new \Exception("SMS sent but system error occurred. Please try again.");
      }
      unset($_COOKIE['auth_token']);
      if (getenv('LEND_ENV') > 0) {
        setcookie('auth_token', '', time() - 3600, "/", "." . $_SERVER['SERVER_NAME'], true, true, 'None');
      } else {
        setcookie('auth_token', '', time() - 3600, "/", "." . $_SERVER['SERVER_NAME'], false, true);
      }
      $this->Auth->logout();

      return $this->setJsonResponse([
        'success' => true,
        'message' => 'Verification code sent successfully',
        'verification_id' => $verificationRecord->id,
        'expires_at' => $verificationRecord->expires_at->format('Y-m-d H:i:s'),
        'phone_number' => $phoneNumber
      ]);

    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  public function verifySmsCode()
  {
    try {
      $authToken = $_SERVER['HTTP_X_AUTH_TOKEN'] ?? null;
      if (!$authToken || !$this->verifyAuthToken($authToken)) {
        throw new \Exception("Unauthorized - Invalid auth token", 401);
      }
      
      if (!$this->request->is('post')) {
        throw new \Exception("Only POST request allowed.", 405);
      }

      $data = $this->request->getData();

      if (empty($data['phone_number'])) {
        throw new \Exception("phone_number is required");
      }

      if (empty($data['verification_code'])) {
        throw new \Exception("verification_code is required");
      }
      $smsVerificationTable = TableRegistry::getTableLocator()->get('SmsVerificationCodes');
      $result = $smsVerificationTable->verifyCode($data['phone_number'], $data['verification_code']);

      if ($result['success']) {
        // Create fresh applicant user session after successful SMS verification
        $user = [
          'account_type' => 'Applicant',
          'level' => -1
        ];
        
        // Add partner_id if provided in the request, otherwise default to 1
        $user['partner_id'] = !empty($data['partner_id']) ? $data['partner_id'] : 1;
        
        // Set user session
        $this->Auth->setUser($user);
        $user = $this->Auth->user();
        
        // Generate fresh JWT token
        $freshToken = JWT::encode([
          'sub' => 'Applicant',
          'exp' => (time() + 86400), // 24 hour expiry
          'user' => $user,
        ], Security::getSalt());
        
        // Set authentication cookie  ==> setCookieSameSite No need to delete existing cookie with the same name.
        $this->setCookieSameSite('auth_token', $freshToken, 0, "/", "." . $_SERVER['SERVER_NAME'], getenv('LEND_ENV') > 0, true, (getenv('LEND_ENV') > 0 ? "None" : false));
        
        return $this->setJsonResponse([
          'success' => true,
          'message' => $result['message'],
          'verified' => true,
          'logged_in' => true
        ]);
      } else {
        return $this->setJsonResponse([
          'success' => false,
          'message' => $result['message'],
          'verified' => false
        ], 400);
      }

    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  public function createLeadFromTargetApp()
  {
    try {
      if (!$this->request->is('post')) {
        throw new \Exception("Only POST request allowed.", 405);
      }

      $data = $this->request->getData();

      // Validate required fields based on new payload structure
      $requiredFields = ['owners_all', 'amount_requested', 'purpose_id', 'industry_id', 'sales_monthly'];
      foreach ($requiredFields as $field) {
        if (!isset($data[$field])) {
          throw new \Exception("Required field '{$field}' is missing");
        }
      }

      // Validate owners_all array has at least one owner with required fields
      if (empty($data['owners_all']) || !is_array($data['owners_all'])) {
        throw new \Exception("At least one owner is required");
      }

      $ownerRequiredFields = ['first_name', 'last_name', 'mobile', 'email'];
      foreach ($data['owners_all'] as $index => $owner) {
        foreach ($ownerRequiredFields as $field) {
          if (empty($owner[$field])) {
            throw new \Exception("Owner {$index}: Required field '{$field}' is missing");
          }
        }
      }

      // The payload is already in the correct format for createLead
      // Just pass it directly without any mapping
      $lead_data = $data;

      // Ensure default values for fields that might be missing
      $defaults = [
        'source' => 'retarget',
        'status_id' => 1,
        'partner_status_id' => 1,
        'lead_type' => 'commercial',
        'call_me_first' => true,
        'send_type' => 'Auto',
        'force_send' => true,
        'campaign' => null,
        'partner_id' => 1,
        'loan_term_requested_months' => 12, // Default to 12 months if not provided
        'how_soon_id' => 1,
      ];

      // Apply defaults only if keys are not set
      foreach ($defaults as $key => $value) {
        if (!isset($lead_data[$key])) {
          $lead_data[$key] = $value;
        }
      }
      
      if (!empty($lead_data['abn_lookup'])) {
        $lead_data['abn'] = $lead_data['abn_lookup']['abn'] ?? null;
        $lead_data['acn'] = $lead_data['abn_lookup']['acn'] ?? null;
        $lead_data['business_type_abn'] = $lead_data['abn_lookup']['entity_type_code'] ?? null;
        $lead_data['r_state'] = $lead_data['abn_lookup']['state'] ?? null;
        $lead_data['organisation_name'] = $lead_data['abn_lookup']['organisation_name'] ?? null;
        $lead_data['business_name'] = $lead_data['abn_lookup']['business_name'] ?? null;
        $lead_data['company_registration_date'] = $lead_data['abn_lookup']['effective_from'] ?? null;
      }

      if (!empty($lead_data['nzbn_lookup'])) {
        $lead_data['abn'] = $lead_data['nzbn_lookup']['nzbn'] ?? null;
        $lead_data['organisation_name'] = $lead_data['nzbn_lookup']['entity_name'] ?? null;
        $lead_data['business_name'] = $lead_data['nzbn_lookup']['entity_name'] ?? null;
        $lead_data['business_type_abn'] = $lead_data['nzbn_lookup']['entity_type_description'] ?? null;
        $lead_data['company_registration_date_from_abnlookup'] = $lead_data['nzbn_lookup']['registration_date'] ?? null;
        $lead_data['company_registration_date'] = $lead_data['nzbn_lookup']['registration_date'] ?? null;
      }

      $leadTable = TableRegistry::getTableLocator()->get('LeadEntity');
      $lead = $leadTable->createLead($lead_data);

      if (empty($lead) || !empty($lead->getErrors())) {
        $errors = !empty($lead) ? $lead->getErrors() : [];
        Log::error('Failed to create lead: ' . json_encode($errors));
        throw new \Exception("Failed to create lead: " . json_encode($errors));
      }

      try {
        $partnerUserLeadsTable = TableRegistry::getTableLocator()->get('PartnerUserLeadsEntity');
        $partnerUserTable = TableRegistry::getTableLocator()->get('PartnerUserEntity');
        
        $partnerId = $lead_data['partner_id'] ?? 1;
        $partnerUser = $partnerUserTable->find()
          ->where(['partner_id' => $partnerId, 'active' => 1])
          ->order(['partner_user_id' => 'ASC'])
          ->first();

        if (!empty($partnerUser)) {
          $partnerUserLead = $partnerUserLeadsTable->newEntity([
            'partner_user_id' => $partnerUser->partner_user_id,
            'status' => 'ACCESS',
            'lead_id' => $lead->lead_id
          ]);
          $partnerUserLeadsTable->save($partnerUserLead);
        }
      } catch (\Exception $e) {
        Log::warning('Failed to create partner user lead relationship: ' . $e->getMessage());
      }

      $primaryOwner = $data['owners_all'][0] ?? [];

      return $this->setJsonResponse([
        'success' => true,
        'message' => 'Lead created successfully',
        'lead_ref' => $lead->lead_ref,
        'lead_id' => $lead->lead_id,
        'data' => [
          'first_name' => $primaryOwner['first_name'] ?? '',
          'last_name' => $primaryOwner['last_name'] ?? '',
          'email' => $primaryOwner['email'] ?? '',
          'mobile' => $primaryOwner['mobile'] ?? '',
          'amount_requested' => $data['amount_requested'],
          'business_name' => $data['business_name'] ?? null,
          'organisation_name' => $data['organisation_name'] ?? null,
          'abn' => $data['abn'] ?? null,
        ]
      ]);

    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

}
