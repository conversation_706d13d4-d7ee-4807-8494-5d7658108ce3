<?php

namespace LeadApisV2\Controller;

use App\Lend\CurlHelper;
use App\Lend\LeadValidation;
use App\Lend\LendInternalAuth;
use Cake\Core\Configure;
use Cake\Datasource\ConnectionManager;
use Cake\Http\Client;
use Cake\Log\Log;
use Cake\ORM\TableRegistry;
use Exception;

class QuotesController extends AppController
{
    public function initialize()
    {
        parent::initialize();
    }

    public function beforeFilter($event)
    {
        parent::beforeFilter($event);
        $functions = [
            //   'GET'=> [],
            'POST' => ['run']
        ];
        $ret = $this->checkAccessToLead($functions);
        if (!empty($ret))
            return $ret;
    }

    public function generateQuote()
    {
        try {
            $this->request->allowMethod(['POST']);
            $user = $this->Auth->user();
            if (empty($user)) {
                $user = $this->Auth->identify();
            }
            $quoteType2ProductId = [
                "Consumer" => 26,
                "Commercial" => 1,
                "Commercial_Asset" => 3,
                "Consumer_Asset" => 25,
            ];
            $data = $this->request->getData();
            $data['partners'] = $this->getTableLocator()->get('PartnerEntity')->find("all")->where(['partner_id' => $user['partner_id']])->first()->toArray();
            if (empty($data['leads']['equipment_id']) && !empty($data['leads']['purpose']['equipment_id'])) {
                $data['leads']['equipment_id'] = $data['leads']['purpose']['equipment_id'];
            }
            // map `equipment_id`
            if (!empty($data['lead_asset_finance']['equipment_id'])) {
                $data['lead_associated_data']['equipment_id'] = $data['lead_asset_finance']['equipment_id'];
            }
            $payload = [
                'requested' => [
                    'from' => 'Partners',
                    'partner_id' => $user['partner_id'],
                    'partner_user_id' => $user['partner_user_id'],
                ],
                'payload' => $data,
                'is_quote' => true,
                'country' => strtoupper(getenv('REGION')),
                'type' => $data['quote_type'],
            ];
            // Save request record before request:
            $lender_match_requests_table = $this->getTableLocator()->get('LenderMatchRequestEntity');
            $lender_match_request = $lender_match_requests_table->newEntity([
                'partner_id' => $user['partner_id'],
                'partner_user_id' => $user['partner_user_id'],
                'product_type_id' => $quoteType2ProductId[$data['quote_type']],
                'payload' => $payload['payload'],
                'name' => $data['name'],
            ]);
            $lender_match_requests_table->save($lender_match_request);

            // Request matching engine service
            $http = new Client();
            $header = [
                'type' => 'json',
                'headers' => ['x-api-key' => getenv('MATCHING_ENGINE_KEY')]
            ];
            $response = $http->post(getenv('DOMAIN_MATCHING_ENGINE') . "/run", json_encode($payload), $header);
            $match_result = $response->getJson();
            $lender_match_requests_table->patchEntity($lender_match_request, [
                'match_ref' => @$match_result['data']['ref']
            ]);
            $lender_match_requests_table->save($lender_match_request);

            return $this->setJsonResponse($match_result);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
        }
    }

    public function getQuotes()
    {
        try {

            $user = $this->Auth->user();
            if (empty($user)) {
                $user = $this->Auth->identify();
            }
            $data = $this->request->getData();
            $where = [['partner_user_id' => $user['partner_user_id'], 'lead_id IS' => NULL]];
            if ($data['search_by'] == "lead_ref" && $data['search_value']) {
                $lend_internal_auth = new LendInternalAuth;
                $lead_id = $lend_internal_auth->unhashLeadId($data['search_value']);
                if (!$lead_id) {
                    throw new Exception("Invalid lead ref", 400);
                }
                $where[] = ["converted_lead_id" => $lead_id];
            } else if ($data['search_by'] == "quote_ref" && $data['search_value']) {
                $where[] = ["match_ref" => $data['search_value']];
            } else if ($data['search_by'] == "name" && $data['search_value']) {
                $where[] = ["name like " => "%" . $data['search_value'] . "%"];
            }
            if (!empty($data['created_by'])) {
                $where[] = ["created >" => $data['created_by'], "created <" => date("Y-m-d H:i:s", strtotime("-7 days"))];
            } else {
                $where[] = ["created >" => date("Y-m-d H:i:s", strtotime("-7 days"))];
            }
            // Save request record before request:
            $lender_match_requests_table = $this->getTableLocator()->get('LenderMatchRequestEntity');
            $quotes = $lender_match_requests_table->find("all")->where($where)->toArray();
            $lend_internal_auth = new LendInternalAuth;
            if (!empty($quotes)) {
                foreach ($quotes as $key => $quote) {
                    if ($quote->converted_lead_id) {
                        $quotes[$key]->converted_lead_ref = $lend_internal_auth->hashLeadId($quote->converted_lead_id);
                    }
                }
            }

            return $this->setJsonResponse($quotes);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
        }
    }

    public function getQuote($quoteRef)
    {
        try {
            $user = $this->Auth->user();
            if (empty($user)) {
                $user = $this->Auth->identify();
            }
            $lender_match_requests_table = $this->getTableLocator()->get('LenderMatchRequestEntity');
            $quote = $lender_match_requests_table->find("all")
            ->where(["match_ref" => $quoteRef])
            ->first();
            if (!$quote) {
                throw new Exception("Quote not found", 404);
            }
            if ($quote->converted_lead_id) {
                $lend_internal_auth = new LendInternalAuth;
                $quote->converted_lead_ref = $lend_internal_auth->hashLeadId($quote->converted_lead_id);
            }
            return $this->setJsonResponse($quote);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
        }
    }

    public function updateQuote()
    {
        try {
            $this->request->allowMethod(['POST']);
            $user = $this->Auth->user();
            if (empty($user)) {
                $user = $this->Auth->identify();
            }
            $quoteRef = $this->request->getData('quote_ref');
            $lender_match_requests_table = $this->getTableLocator()->get('LenderMatchRequestEntity');
            $quote = $lender_match_requests_table->find("all")->where(["match_ref" => $quoteRef])->first();
            if (!$quote) {
                throw new Exception("Quote not found", 404);
            }
            $data = $this->request->getData();
            unset($data['quote_ref']);
            $lender_match_requests_table->patchEntity($quote, $data);
            $lender_match_requests_table->save($quote);
            return $this->setJsonResponse($quote);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
        }
    }

    public function deleteQuote($quoteRef)
    {
        try {
            $this->request->allowMethod(['DELETE']);
            $user = $this->Auth->user();
            if (empty($user)) {
                $user = $this->Auth->identify();
            }
            $lender_match_requests_table = $this->getTableLocator()->get('LenderMatchRequestEntity');
            $quote = $lender_match_requests_table->find("all")->where(["match_ref" => $quoteRef])->first();
            if (!$quote) {
                throw new Exception("Quote not found", 404);
            }
            $lender_match_requests_table->delete($quote);
            return $this->setJsonResponse(["message" => "Quote deleted successfully"]);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
        }
    }

    public function cloneQuote($quoteRef)
    {
        try {
            $this->request->allowMethod(['POST']);
            $user = $this->Auth->user();
            if (empty($user)) {
                $user = $this->Auth->identify();
            }
            $lender_match_requests_table = $this->getTableLocator()->get('LenderMatchRequestEntity');
            $quote = $lender_match_requests_table->find("all")->where(["match_ref" => $quoteRef])->first();
            if (!$quote) {
                throw new Exception("Quote not found", 404);
            }
            $quote = $quote->toArray();
            unset($quote['id']);
            unset($quote['match_ref']);
            unset($quote['lead_id']);
            unset($quote['converted_lead_id']);
            unset($quote['created']);
            unset($quote['modified']);
            $quote['partner_user_id'] = $user['partner_user_id'];
            $quote['partner_id'] = $user['partner_id'];
            $quotePayload = json_decode($quote['payload'], true);
            $payload = [
                'requested' => [
                    'from' => 'Partners',
                    'partner_id' => $user['partner_id'],
                    'partner_user_id' => $user['partner_user_id'],
                ],
                'payload' => $quote['payload'],
                'is_quote' => true,
                'type' => $quotePayload['quote_type'],
            ];
            $http = new Client();
            $header = [
                'type' => 'json',
                'headers' => ['x-api-key' => getenv('MATCHING_ENGINE_KEY')]
            ];
            $response = $http->post(getenv('DOMAIN_MATCHING_ENGINE') . "/run", json_encode($payload), $header);
            $match_result = $response->getJson();
            $quote['match_ref'] = @$match_result['data']['ref'];
            $lender_match_request = $lender_match_requests_table->newEntity($quote);
            $lender_match_requests_table->save($lender_match_request);
            return $this->setJsonResponse($match_result);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
        }
    }
}
