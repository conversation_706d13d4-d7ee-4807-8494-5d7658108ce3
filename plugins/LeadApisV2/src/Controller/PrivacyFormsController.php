<?php

namespace LeadApisV2\Controller;

use App\Enums\LendSignTemplateUse;
use App\Lend\LendInternalAuth;
use App\Lend\SignatureService;
use Cake\Datasource\ConnectionManager;
use Cake\Http\Exception\HttpException;
use Cake\Log\Log;
use Cake\ORM\TableRegistry;
use App\Lend\SignatureServiceFactory;

class PrivacyFormsController extends BaseLendSignatureRequestController
{
    public $flowShortName = LendSignTemplateUse::CommercialPrivacyForms;
    protected $refFieldName = 'applicants';
    public function initialize()
    {
        parent::initialize();
    }

    public function beforeFilter($event)
    {
        parent::beforeFilter($event);
        $ret = $this->checkAccessToLead(['GET'=> ['recipients'], 'POST' => ['send', 'sendLend']]);
        if (!empty($ret)) {
            return $ret;
        }
    }

    public function recipients($leadRef)
    {
        try {
            $leadId = (new LendInternalAuth)->unhashLeadId($leadRef);
            $lead = TableRegistry::getTableLocator()->get('LeadEntity', [
            'connection' => ConnectionManager::get('reader_db')
            ])
            ->find('all')
            ->where(['lead_id' => $leadId])
            ->contain(['Owners', 'Owners.LatestPrivacy', 'Owners.PartnerRequestedPrivacyForm', 'PartnerRequestedPrivacyForm'])
            ->first();

            $ownerEmailsToRefMap = [];
            $ownerByRefMap = [];
            foreach ($lead['owners_all'] as $owner) {
                $ownerRef = (new LendInternalAuth)->hashOwnerId($owner['owner_id']);
                $ownerEmailsToRefMap[$owner['email']][] = $ownerRef; // allow multiple owners can have the same email 
                $ownerByRefMap[$ownerRef] = $owner;
            }

            $privacyRequested = [];
            foreach ($lead['owners_all'] as $owner) {
                if (!empty($owner['partner_requested_privacy_forms'])) {
                    foreach ($owner['partner_requested_privacy_forms'] as $privacy) {
                        $ownerCopy = $owner;
                        unset($ownerCopy['partner_requested_privacy_forms']);
                        $privacy['owner'] = $ownerCopy;
                        $privacyRequested[] = $privacy;
                    }
                }
            }
            $sign = new SignatureService('get_multiple_envelopes');
            $envelopeIdsForRecordsWithoutOwnerId = [];
            $allFiles = [];
            $privacyRequestsWithOwnerId = [];
            foreach ($privacyRequested as $pr) {
                if (!empty($pr['owner_id'])) {
                    $privacyRequestsWithOwnerId[$pr['owner_id']][] = $pr;
                    continue;
                }
                $envelopeId = $pr['service_envelope_id'];
                $envelopeIdsForRecordsWithoutOwnerId[] = $envelopeId;
                if (!empty($pr['signed_document_s3'])) {
                    $allFiles[$envelopeId] = "https://files.lend.com.au/".$pr['signed_document_s3'];
                }
            }
            // TODO Rework to pull from the database.
            // TODO Have to work for old leads, that were created without owner_id.
            $recipientByOwner = [];
            $envelopesByOwnerCounter = [];
            if (!empty($envelopeIdsForRecordsWithoutOwnerId)) {
                $allEnvelopesData = $sign->callService(urlencode(json_encode($envelopeIdsForRecordsWithoutOwnerId)));
                $allEnvelopes = [];
                if ($allEnvelopesData['success'] === true) {
                    $allEnvelopes = $allEnvelopesData['data'];
                }
                foreach ($allEnvelopes as $envelope) {
                    foreach ($envelope['recipients'] as $recipient) {
                        if (isset($ownerEmailsToRefMap[$recipient['email']])) {
                            foreach ($ownerEmailsToRefMap[$recipient['email']] as $ownerRef) {
                                $owner = $recipient;
                                $owner["file"] = $allFiles[$envelope['id']];
                                $recipientByOwner[$ownerRef][] = $owner;
                                if (empty($envelopesByOwnerCounter[$ownerRef])) {
                                    $envelopesByOwnerCounter[$ownerRef] = 1;
                                } else {
                                    $envelopesByOwnerCounter[$ownerRef]++;
                                }
                            }
                        }
                    }
                }
            }
            if (!empty($privacyRequestsWithOwnerId)) {
                foreach ($privacyRequestsWithOwnerId as $ownerId => $privacyList) {
                    $ownerRef = (new LendInternalAuth)->hashOwnerId($ownerId);
                    foreach ($privacyList as $privacy) {
                        if (empty($envelopesByOwnerCounter[$ownerRef])) {
                            $envelopesByOwnerCounter[$ownerRef] = 1;
                        } else {
                            $envelopesByOwnerCounter[$ownerRef]++;
                        }
                    }
                    $recipientArray = $privacyList[0]['owner']->toArray();
                    $recipientArray['created'] = $recipientArray['latest_privacy']['sent_time'];
                    $recipientByOwner[$ownerRef][] = $recipientArray;
                }
            }
            $flatRecipients = [];
            foreach ($ownerByRefMap as $ref => $owner) {
                if (!empty($recipientByOwner[$ref])) {
                    $rowSpanSet = true;
                    foreach ($recipientByOwner[$ref] as $recipient) {
                        $flatRecipients[] = [
                        'owner_ref' => $ref,
                        'full_name' => $owner['full_name'],
                        'email' => $recipient['email'],
                        'mobile' => $recipient['mobile'],
                        'sent' => $recipient['created'],
                        'latest_privacy_sent' => $owner['latest_privacy']['created'],
                        'signed' => $recipient['status'] === 'completed' ? $recipient['updated'] : null,
                        'selected' => false,
                        'rowSpan' => $rowSpanSet ? $envelopesByOwnerCounter[$ref] : null,
                        'file' => $recipient['file'],
                        ];
                        $rowSpanSet = false;
                    }
                } else {
                    $flatRecipients[] = [
                    'owner_ref' => $ref,
                    'full_name' => $owner['full_name'],
                    'email' => $owner['email'],
                    'mobile' => $owner['mobile'],
                    'sent' => null,
                    'latest_privacy_sent' => $owner['latest_privacy']['created'],
                    'signed' => null,
                    'selected' => false,
                    'rowSpan' => 1,
                    ];
                }
            }

            return $this->setJsonResponse($flatRecipients);
        } catch (\Exception $e) {
            return $this->setJsonResponse(['error' => $e->getMessage()], 500);
        }
    }

    public function records($leadRef)
    {

      $leadId = (new LendInternalAuth)->unhashLeadId($leadRef);
      $records = TableRegistry::getTableLocator()->get('LendSignatureRequestEntity', [
        'connection' => ConnectionManager::get('reader_db')
      ])
        ->find('all')
        ->where([
            'LendSignatureRequestEntity.lead_id' => $leadId,
            'LendSignatureRequestEntity.template_use_shortname' => $this->flowShortName,
          ]
        )
        ->order(['LendSignatureRequestEntity.created' => 'DESC'])
        ->contain(['LeadOwnersEntity'])
        ->toArray();

      $filteredRecords = [];
      $envelopeIdsForRecordsWithoutOwnerId = [];
      $recordsWithoutOwner = [];
      foreach ($records as $record) {
        if (!empty($record['owner'])) {
          $recordToArray = $record->toArray();
          $recordToArray['owner'] = $record['owner']->toArray();
          if (!empty($recordToArray['recipient_name'])) {
            $recordToArray['owner']['full_name'] = $recordToArray['recipient_name'];
            $recordToArray['owner']['email'] = $recordToArray['recipient_email'];
            $recordToArray['owner']['mobile'] = $recordToArray['recipient_mobile'];
          }
          $filteredRecords[] = $recordToArray;
        }
        else {
          $envelopeId = $record['service_envelope_id'];
          $envelopeIdsForRecordsWithoutOwnerId[] = $envelopeId;
          $recordsWithoutOwner[] = $record->toArray();
        }
      }

      $owners = TableRegistry::getTableLocator()->get('LeadOwnersEntity')
        ->find('all')
        ->where(['LeadOwnersEntity.lead_id' => $leadId])
        ->contain(['PartnerRequestedPrivacyForm'])
        ->toArray();

      $ownersByEmail = [];
      foreach ($owners as $owner) {
        $ownersByEmail[$owner['email']] = $owner;
      }

      $allDocuSignEnvelopesById = [];
      if (!empty($envelopeIdsForRecordsWithoutOwnerId)) {
        $sign = new SignatureService('get_multiple_envelopes');
        $allEnvelopesData = $sign->callService(urlencode(json_encode($envelopeIdsForRecordsWithoutOwnerId)));
        $allEnvelopes = [];
        if ($allEnvelopesData['success'] === true) {
          $allEnvelopes = $allEnvelopesData['data'];
        }
        foreach ($allEnvelopes as $envelope) {
          $allDocuSignEnvelopesById[$envelope['id']] = $envelope;
        }
      }

      $recordsWithOwnerFromDocuSign = [];
      foreach ($recordsWithoutOwner as $record) {
        $recordEnvelopeId = $record['service_envelope_id'];
        if (!empty($allDocuSignEnvelopesById[$recordEnvelopeId])) {
          $envelope = $allDocuSignEnvelopesById[$recordEnvelopeId];

          $recordDocuSignRecipient = $envelope['recipients'][0];
          $envelopeEvents = $envelope['events'];
          foreach ($envelope['recipients'] as $recipient) {
            if ($recipient['email'] === $recordDocuSignRecipient['email']) {
              $recordDocuSignRecipient = $recipient;
            }
            foreach ($envelopeEvents as $event) {
              if ($event['event'] === 'sent') {
                $record['sent_time'] = $event['created'];
              }
              if ($event['event'] === 'completed') {
                $record['signed_time'] = $event['created'];
              }
            }

            if (empty($ownersByEmail[$recipient['email']])) {
              continue;
            }
            $record['owner'] = $ownersByEmail[$recipient['email']];
            $recordsWithOwnerFromDocuSign[] = $record;
          }
        }
      }

      $formattedRecords = [];
      foreach ($owners as $owner) {
        if (empty($owner['partner_requested_privacy_forms'])) {
          $formattedRecords[] = [
            'id' => (new LendInternalAuth)->hashOwnerId($owner['owner_id']),
            'sent_time'=> null,
            'signed_time' => null,
            'via_email' => false,
            'via_sms' => false,
            'owner'=> [
              'owner_ref'=> (new LendInternalAuth)->hashOwnerId($owner['owner_id']),
              'full_name'=> $owner['full_name'],
              'email'=> $owner['email'],
              'mobile'=> $owner['mobile'],
              'status' => $owner['status'],
              'is_guarantor' => $owner['is_guarantor'],
              'con_spouse' => $owner['con_spouse'],
              'point_of_contact' => $owner['point_of_contact'],
            ],
          ];

        }
      }

      $formattedRecords = array_merge($formattedRecords, $filteredRecords, $recordsWithOwnerFromDocuSign);
      return $this->setJsonResponse($formattedRecords);
    }

    public static function lendSignatureErrorsToMessage($signatureServiceResponse, $defaultMessage = 'Failed to send request. Please try again.') {
      $errorMessage = '';
      if (!empty($signatureServiceResponse['data'][0]['transportResponsesJson'])) {
        $errorsJson = $signatureServiceResponse['data'][0]['transportResponsesJson'];
        $errors = json_decode($errorsJson, true);
        $errorMessagesArray = [];
        foreach ($errors as $error) {
          $message = '';
          if (!empty($error['error'])) {
            $message = $error['error'];
          }
          if (!empty($error['message'])) {
            $message = $error['message'];
          }
          if (!empty($error['debug_error'])) {
            $message = $error['debug_error']['description'];
          }

          $errorMessagesArray[] = strtoupper($error['transport']) . ' error: ' . $message;
        }
        $errorMessage = implode('. ', $errorMessagesArray);
      }
      if (empty($errorMessage)) {
        $errorMessage = $defaultMessage;
      }
      return $errorMessage;
    }

    public function sendLend()
    {
        try {
            $this->request->allowMethod(['POST']);
            $leadRef = $this->request->getData('lead_ref');
            if (empty($leadRef)) {
                throw new HttpException('Lead ref is required', 400);
            }
            $leadId = (new LendInternalAuth)->unhashLeadId($leadRef);

            $requestOwnerIds = $this->getRequestOwnerIds();

            $newEnvelope = $this->createEnvelope();

            if ($newEnvelope['success']) {

              foreach ($requestOwnerIds as $ownerId) {
                $this->saveLendSignatureRequestRecord($leadId, $ownerId, $newEnvelope);
              }
              return $this->setJsonResponse([
                'success' => true,
              ]);
            } else {
              $errorMessage = self::lendSignatureErrorsToMessage($newEnvelope, 'Failed to send privacy form. Please try again.');

              return $this->setJsonResponse([
                'success' => false,
                'message' => $errorMessage,
              ]);
            }
        } catch (\Exception $e) {
            throw new HttpException($e->getMessage(), 500);
        }
    }

    public function previewLend($leadRef){
        $leadId = (new LendInternalAuth)->unhashLeadId($leadRef);
        $owners = TableRegistry::getTableLocator()->get('LeadOwnersEntity')->find()->where(['lead_id' => $leadId])->toArray();
        $user = $this->Auth->user();
        if (empty($user)) {
            $user = $this->Auth->identify();
        }
        $requestOwnerIds = [];
        $requestVia = [];
        foreach ($owners as $owner) {
            $requestOwnerIds[] = $owner->owner_id;
            $requestVia[$owner->owner_id] = 'email';
        }
        $sign = new SignatureServiceFactory($user, $this->flowShortName);
        $requestParams = [
            'leadRef' => $leadRef,
            'requestOwnerIds' => $requestOwnerIds,
            'via' => $requestVia,
        ];
        $payload = $sign->getSignatureTemplatePayload($requestParams);
        $preview = $sign->generatePreview([
            "flow" => $this->flowShortName,
            "dataJson" => json_encode([
                "recipients" => $payload['recipients'],
                "payload" => $payload['payload'],
            ]),
        ]);
        if($preview['ref']){
            $this->redirect(getEnv("DOMAIN_LEND_SIGNATURE")."/preview/".$preview['ref']."?signature=".$preview['signatures']);
        }else{
            throw new HttpException('No privacy form found', 404);
        }
    }
  public function migrate() {
    $params = $this->request->getQueryParams();
    $pass = $params['pass'];
    if (empty($pass) || $pass !== 'JrkjNKSf4v0a1S') {
      throw new \Exception("Please specify the pass to run this command.");
    }
    $partnerRequestedPrivacyFormsTable = TableRegistry::getTableLocator()->get('PartnerRequestedPrivacyFormEntity');
    $lendSignatureRequestsTable = TableRegistry::getTableLocator()->get('LendSignatureRequestEntity');
    $notMigratedRecords = $partnerRequestedPrivacyFormsTable
      ->find()
      ->where(['migrated' => 0])
      ->contain(['LeadOwnersEntity', 'LeadEntity', 'LeadEntity.Owners'])
      ->limit(50)
      ->toArray();

    $sign = new SignatureService('get_multiple_envelopes');
    foreach ($notMigratedRecords as $record) {
      $newLendSignatureRequestRecord = $lendSignatureRequestsTable->newEntity();
      $newLendSignatureRequestRecord->template_use_shortname = $this->flowShortName;

      if (empty($record->owner_id)) {
        $lead = $record->lead;
        if (empty($lead)) {
          $debugData = [
            'requested_privacy_form_id' => $record->requested_privacy_form_id,
            'lead_id' => $record->lead_id,
          ];
          Log::debug('PrivacyFormMigration record lead does not exists: ' . json_encode($debugData));
          continue;
        }
        $leadOwners = $record->lead->owners_all;
        if (empty($leadOwners)) {
          $debugData = [
            'requested_privacy_form_id' => $record->requested_privacy_form_id,
            'lead_id' => $record->lead_id,
          ];
          Log::debug('PrivacyFormMigration record lead owners does not exists: ' . json_encode($debugData));
          continue;
        }

        $ownerIdByEmail = [];
        $pocOwnerId = $leadOwners[0]->owner_id;
        $pocOwner = $leadOwners[0];
        foreach ($leadOwners as $owner) {
          if ($owner->point_of_contact === true) {
            $pocOwnerId = $owner->owner_id;
            $pocOwner = $owner;
          }
          $ownerIdByEmail[$owner->email] = $owner->owner_id;
        }
        $envelopeId = $record->service_envelope_id;
        $envelopeIdsForRecordsWithoutOwnerId[] = $envelopeId;
        $allEnvelopesData = $sign->callService(urlencode(json_encode($envelopeIdsForRecordsWithoutOwnerId)));
        $allEnvelopes = [];
        if ($allEnvelopesData['success'] === true) {
          $allEnvelopes = $allEnvelopesData['data'];
        }

        $ownerFound = false;
        foreach ($allEnvelopes[0]['recipients'] as $recipient) {
          if (isset($ownerIdByEmail[$recipient['email']])) {
            $ownerFound = true;
            $newLendSignatureRequestRecord->owner_id = $ownerIdByEmail[$recipient['email']];
            $recipientName = $recipient['first_name'];
            if (isset($recipient['middle_name'])) {
              $recipientName .= ' ' . $recipient['middle_name'];
            }
            $recipientName .= ' ' . $recipient['last_name'];
            $newLendSignatureRequestRecord->recipient_name = $recipientName;
            $newLendSignatureRequestRecord->recipient_email = $recipient['email'];
            $newLendSignatureRequestRecord->recipient_mobile = $recipient['mobile'];
          }
        }
        if (!$ownerFound) {
          $newLendSignatureRequestRecord->owner_id = $pocOwnerId;
          $recipientName = $pocOwner['first_name'];
          if (isset($pocOwner['middle_name'])) {
            $recipientName .= ' ' . $pocOwner['middle_name'];
          }
          $recipientName .= ' ' . $pocOwner['last_name'];
          $newLendSignatureRequestRecord->recipient_name = $recipientName;
          $newLendSignatureRequestRecord->recipient_email = $pocOwner['email'];
          $newLendSignatureRequestRecord->recipient_mobile = $pocOwner['mobile'];
        }

      }
      else {
        $newLendSignatureRequestRecord->owner_id = $record->owner_id;

        if (empty($record->recipient_name)) {
          $newLendSignatureRequestRecord->recipient_name = $record->owner->full_name;
          $newLendSignatureRequestRecord->recipient_email = $record->owner->email;
          $newLendSignatureRequestRecord->recipient_mobile = $record->owner->mobile ?? '';
        }
        else {
          $newLendSignatureRequestRecord->recipient_name = $record->recipient_name ?? '';
          $newLendSignatureRequestRecord->recipient_email = $record->recipient_email ?? '';
          $newLendSignatureRequestRecord->recipient_mobile = $record->recipient_mobile ?? '';
        }
      }
      $newLendSignatureRequestRecord->lead_id = $record->lead_id;

      $statusMap = [
        'In Progress' => 'sent',
        'Opened' => 'opened',
        'Signed' => 'signed',
        'Declined' => 'declined',
        'Discarded' => 'sent',
        'All Signed' => 'signed',
      ];
      $savedStatus = $statusMap[$record->status];
      if (empty($savedStatus)) {
        $savedStatus = 'sent';
      }
      $newLendSignatureRequestRecord->status = $savedStatus;
      $newLendSignatureRequestRecord->partner_user_id = $record->partner_user_id;
      $newLendSignatureRequestRecord->sent_time = $record->send_date;
      $newLendSignatureRequestRecord->signed_time = $record->signed_date;
      $newLendSignatureRequestRecord->upload_url = $record->signed_document_s3;
      $newLendSignatureRequestRecord->service_envelope_id = $record->service_envelope_id;
      $newLendSignatureRequestRecord->via_email = $record->notification_email;
      $newLendSignatureRequestRecord->via_sms = $record->notification_sms;

      $newLendSignatureRequestRecord->created = $record->created;
      $newLendSignatureRequestRecord->updated = $record->updated;

      $lendSignatureRequestsTable->save($newLendSignatureRequestRecord);

      $record->migrated = 1;
      $partnerRequestedPrivacyFormsTable->save($record);
    }
    return $this->setJsonResponse([
      'success'=>true,
    ]);

  }
}
