<?php

namespace LeadApisV2\Controller;
use Cake\Log\Log;

use Cake\ORM\TableRegistry;
use Cake\I18n\Time;
use App\Lend\LendInternalAuth;

class ActivityLogController extends AppController
{
    public function initialize()
    {
        parent::initialize();
    }

    public function index(){
        try {
            $this->request->allowMethod(['POST']);
            $user = $this->Auth->user();
            if (empty($user)) {
              $user = $this->Auth->identify();
            }
            $schema = [
                'type' => ['sms', 'email', 'note'],
                'account_ref' => null,
                'applicant_ref' => null,
                'lead_ref' => null,
                'owner_refs' => null,
            ];
            $data = array_intersect_key($this->request->getData(), $schema);
            //Validate type
            if(!empty($data['type'])){
                if(!is_array($data['type'])){
                  throw new \Exception("type must be an array");
                }
                foreach($data['type'] as $type){
                    if(!in_array($type, $schema['type'])){
                        throw new \Exception("Invalid type value: ".$type.", value must be sms, email or note");
                    }
                }
            }
            //Validate provided ref
            if(empty($data['account_ref'])
                && empty($data['applicant_ref'])
                && empty($data['lead_ref'])
            ){
                throw new \Exception("One of the following is required: account_ref, applicant_ref, lead_ref");
            }
            $accountId = null;
            $peopleId = null;
            $leadId = null;
            $ownerIds = [];

            if($data['account_ref']){
                if (!$this->checkPartnerAccountBelongsToUser($data['account_ref'])) {
                    throw new \Exception("You don't have permission to see the account.");
                }
                $accountId = LendInternalAuth::unhashPartnerAccountId($data['account_ref']);
            }
            if($data['applicant_ref']){
                $peopleId = LendInternalAuth::unhashPartnerAccountPeopleId($data['applicant_ref']);
                if (!$peopleId) {
                    throw new \Exception("Invalid applicant_ref.");
                }
            }
            if($data['lead_ref']){
                $leadId = LendInternalAuth::unhashLeadId($data['lead_ref']);
                if(empty($leadId)){
                    throw new \Exception("Can't find a lead.");
                }
                $lead = TableRegistry::getTableLocator()->get('LeadEntity')->get($leadId);
                if($user['partner_id'] !== $lead['partner_id']){
                    throw new \Exception("You are not authorised to access that lead", 401);
                }

                //owners are only used with leads
                if(!empty($data['owner_refs'])){
                    if(!is_array($data['owner_refs'])){
                        throw new \Exception("owner_refs must be an array");
                    }
                    foreach($data['owner_refs'] as $ownerRef){
                        $ownerId = LendInternalAuth::unhashOwnerId($ownerRef);
                        if(!empty($ownerId)){
                            $ownerIds[] =  $ownerId;
                        }
                    }
                }
            }
            //fetch the data
            $entity = [
                'note'=> 'LeadNotesEntity',
                'sms' => 'PartnerSmsHistoryEntity',
                'email' => 'EmailHistoryEntity'
            ];
            $notesConditions = ['LeadNotesEntity.status' => 'Active'];
            $notesContain = ['PartnerUserEntity' => ['fields' => ['name']]];
            $smsConditions = ['PartnerSmsHistoryEntity.sms_origin_id IS NULL'];
            $emailConditions = ['status' => 'sent'];
            if(!empty($accountId)){
                $notesContain['PartnerAccountEntity'] = [];
                $notesConditions['LeadNotesEntity.partner_account_id'] = $accountId;
                $notesConditions['PartnerAccountEntity.partner_id'] = $user['partner_id'];

                $smsConditions[] = 'PartnerSmsHistoryEntity.partner_account_id = '.$accountId;
                $emailConditions['partner_account_id'] = $accountId;
                // echo "here".$accountId;die();
            }
            else if(!empty($peopleId)){
                $entity['note'] = 'PartnerAccountPeopleNotesEntity';
                $notesConditions = ['PartnerAccountPeopleNotesEntity.status !=' => 'Deleted'];
                $notesConditions['partner_account_people_id'] = $peopleId;
                $notesConditions[] = '(PartnerUserEntity.partner_id = ' . $user['partner_id'] . ' OR PartnerAccountPeopleNotesEntity.partner_user_id IS NULL)';

                $smsConditions[] = 'PartnerSmsHistoryEntity.partner_account_people_id = '.$peopleId;
                $emailConditions['partner_account_people_id'] = $peopleId;
            }
            else{//has to be lead
                $notesContain['LeadEntity'] = [];
                $notesConditions['LeadNotesEntity.lead_id'] = $leadId;
                $notesConditions['LeadEntity.partner_id'] = $user['partner_id'];
                
                $smsConditions[] = 'PartnerSmsHistoryEntity.lead_id = '.$leadId;
                $emailConditions['lead_id'] = $leadId;

                if(count($ownerIds) > 0){
                    $smsConditions[] = 'PartnerSmsHistoryEntity.owner_id IN ('.implode(",", $ownerIds).')';
                    $emailConditions['owner_id IN'] = $ownerIds;
                }
            }
            $activities = [];
            if(!isset($data['type'])|| in_array('note', $data['type'])){
                $notes = TableRegistry::getTableLocator()->get($entity['note'])->find()
                    ->contain($notesContain)
                    ->where($notesConditions);
                foreach($notes as $note){
                    $activity = [
                        'type' => 'note',
                        'created' => $note['created'],
                        'name' => $note['partner_user']['name'],
                        'notes' => $note['notes'],
                        'note' => $note,
                        'lead_ref' => $data['lead_ref'],
                    ];
                    $activities[] = $activity;
                }
            }
            if(!isset($data['type'])|| in_array('sms', $data['type'])){
                $smsHistory = TableRegistry::getTableLocator()->get($entity['sms'])->find()
                    ->contain([
                        'PartnerUserEntity' => ['fields' => ['name']]
                    ])
                    ->select(
                        [
                            'PartnerSmsHistoryEntity.sms_id',
                            'PartnerSmsHistoryEntity.sms_to',
                            'PartnerSmsHistoryEntity.created',
                            'PartnerSmsHistoryEntity.sms_message',
                            'PartnerSmsHistoryEntity.owner_id',
                            'Reply.sms_id',
                            'Reply.sms_to',
                            'Reply.created',
                            'Reply.sms_message',
                        ]
                    )
                    ->leftJoin(
                        ['Reply' => 'partner_sms_history'], // Aliased table for the same model
                        ['Reply.sms_origin_id = PartnerSmsHistoryEntity.sms_id']
                    )
                    ->where($smsConditions)
                    ->order(['PartnerSmsHistoryEntity.created' => 'ASC', 'Reply.created' => 'ASC']);
                //Have to execure raw SQL as for some strange reason ORM is removing the sms_message
                $sql =  $smsHistory->sql();
                $connection = TableRegistry::getTableLocator()->get($entity['sms'])->getConnection();
                $results = $connection->execute($sql)->fetchAll('assoc');
                $smses = [];
                $smsPrefix = 'PartnerSmsHistoryEntity__';
                $replyPrefix = 'Reply__';
                $userPrefix = 'PartnerUserEntity__';
                // dump($results);
                foreach($results as $result){
                    if(!isset($smses[$result[$smsPrefix.'sms_id']])){
                        $sms = [
                            'sms_to'=> $result[$smsPrefix.'sms_to'],
                            'created' => Time::parse($result[$smsPrefix.'created'])->setTimezone($user['timezone']),
                            'name'=> $result[$userPrefix.'name'],
                            'sms_message'=> $result[$smsPrefix.'sms_message'],
                            'owner_ref'=> LendInternalAuth::hashOwnerId($result[$smsPrefix.'owner_id']),
                            'replies' => []
                        ];
                        $smses[$result[$smsPrefix.'sms_id']] = $sms;
                    }
                    if(!empty($result[$replyPrefix.'sms_id'])){
                        $reply = [
                            'created'=> Time::parse($result[$replyPrefix.'created'])->setTimezone($user['timezone']),
                            'sms_message'=> $result[$replyPrefix.'sms_message'],
                        ];
                        $smses[$result[$smsPrefix.'sms_id']]['replies'][] = $reply;
                    }
                }
                foreach($smses as $sms){
                    $activity = [
                        'type' => 'sms',
                        'created' => $sms['created'],
                        'name' => $sms['name'],
                        'sms_to' => $sms['sms_to'],
                        'sms_message' => $sms['sms_message'],
                        'owner_ref' => $sms['owner_ref'],
                        'replies' => $sms['replies'],
                    ];
                    $activities[] = $activity;
                }
            }
            if(!isset($data['type'])|| in_array('email', $data['type'])){
                $emails = TableRegistry::getTableLocator()->get($entity['email'])->find()
                    ->contain([
                        'PartnerUserEntity' => ['fields' => ['name']]
                    ])
                    ->select(['created', 'from', 'to', 'cc', 'subject', 'html'])
                    ->where($emailConditions);
                foreach($emails as $email){
                    $activity = [
                        'type' => 'email',
                        'created' => $email['created'],
                        'name' => $email['partner_user']['name'],
                        'from' => $email['from'],
                        'to' => $email['to'],
                        'cc' => $email['cc'],
                        'subject' => $email['subject'],
                        'html' => $email['html'],
                    ];
                    $activities[] = $activity;
                }
            }
            //sort array elements by created (ascending)
            usort($activities, function ($a, $b) {
                return strtotime($a['created']) - strtotime($b['created']);
            });
            foreach($activities as $k => $activity){
                $activities[$k]['activity_id'] = $k+1;
            }

            return $this->setJsonResponse(['success' => true, 'activities' => $activities], 200);

        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
        }
    }
}