<?php

namespace LeadApisV2\Controller;

use Cake\ORM\TableRegistry;
use Cake\Log\Log;

class PartnerUsersController extends AppController
{
  public function initialize()
  {
      parent::initialize();
  }

  public function getMyDetails()
  {
    try {
      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }
      unset($user['partner_id']);
      unset($user['partner_user_id']);
      unset($user['token']);
      unset($user['token_expired']);
      unset($user['access_all_leads']);
      unset($user['kanban_filter']);
      unset($user['kanban_leads']);
      unset($user['closed_leads_filter']);
      return $this->setJsonResponse($user);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], 400);
    }
  }

  public function getUsers()
  {
    try {
      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }

      // if (empty($user['account_admin'])) {
      //   throw new \Exception('You do not have access to this resource', 403);
      // }

      $partner_users_table = TableRegistry::getTableLocator()->get('PartnerUserEntity');
      $partner_users = $partner_users_table->find('all', [
        'fields' => [
          "partner_user_ref",
          "created",
          "email",
          "email_verified",
          "account_type",
          "title",
          "name",
          "point_of_contact",
          "account_admin",
          "active",
          "kanban_colour",
          "access_all_leads"
        ]
      ])->where(['partner_id' => $user['partner_id']])->toArray();
      return $this->setJsonResponse($partner_users);
    } catch (\Exception $e) {
      if ($e->getMessage() !== 'You do not have access to this resource') {
        Log::error($e->getMessage());
        Log::error($e->getTraceAsString());
      }
      return $this->setJsonResponse(['error' => $e->getMessage()], 400);
    }
  }

  public function getPartnerByAffiliate($af)
  {
    try {

      $partner = TableRegistry::getTableLocator()->get('PartnerEntity')->find()
      ->where(['affiliate_id' => $af])
      ->first();

      if (empty($partner)) {
        throw new \Exception('Partner not found', 404);
      }

      $partnerAccount = TableRegistry::getTableLocator()->get('PartnerAccounts')->find()->where(['partner_id' => $partner['partner_id']])->last();
      $partner['account_id'] = $partnerAccount ? $partnerAccount['partner_account_id'] : null;

      return $this->setJsonResponse($partner);

    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], 400);
    }
  }
}
