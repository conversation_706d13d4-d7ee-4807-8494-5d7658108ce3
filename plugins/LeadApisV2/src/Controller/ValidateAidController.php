<?php

namespace LeadApisV2\Controller;
use App\Lend\LendInternalAuth;
use Cake\Log\Log;

use Cake\ORM\TableRegistry;
use Cake\Utility\Security;
use Exception;
use Firebase\JWT\JWT;
use Hashids\Hashids;

class ValidateAidController extends AppController
{
    public function initialize()
    {
        parent::initialize();
        $this->Auth->allow(['validateAid', 'generateAid', 'lendIqEmail', 'getOwnerByAid']);
    }

    public function _getOwnerByAid($aid)
    {
        $code = base64_decode( $aid ); // Grab the special auto login code
        $codearr = explode('#', $code); // Splits the user id from the signature sent
        //Check expiry date
        $lead_cred = explode('l3nd', $codearr[0]);
        if(empty($lead_cred) OR (count($lead_cred) < 2) OR (intval($lead_cred[1]) < time())){
            throw new Exception("Invalid credentials.", 401);
        }
        $expected_sig = Security::getSalt().'#'.$codearr[0]; // Build the signature we are expecting
        $expected_sig = hash('sha1', $expected_sig); // Build the signature we are expecting
    
        // Check if the signature sent matches the one we should be expecting
        if ($codearr[1] !== $expected_sig) {
            throw new Exception("Unauthorized.", 401);
        }
        $additionalCode = !empty($codearr[2]) ? $codearr[2] : null;
        $quoteRef = !empty($codearr[3]) ? $codearr[3] : null;
        $leadid = (new LendInternalAuth)->unhashLeadId($lead_cred[0]);
        if (empty($leadid)) {
            throw new Exception("Can't find a Lead.", 400);
        }

        $sections = '';
        if (isset($additionalCode)) {
            parse_str($additionalCode, $parseCode);
            $applicant = @$parseCode['applicant'];
            $first_name = @$parseCode['first_name'];
            $last_name = @$parseCode['last_name'];
            $sections = @$parseCode['sections'];
        }
        if (isset($quoteRef)) {
            extract(parse_ini_string($quoteRef));
        }

        $ownerId = !empty($applicant) ? (new LendInternalAuth)->unhashOwnerId($applicant) : '';
        $leadRef = (new LendInternalAuth)->hashLeadId($leadid);
        $fullName = '';
        $ownerUser = [];
        $owner = [];
        if (!empty($ownerId)) {
            $owner = TableRegistry::getTableLocator()->get('LeadOwnersEntity')->get($ownerId)->toArray();
            if(@$first_name != $owner['first_name'] OR @$last_name != $owner['last_name']){
                throw new Exception("Invalid Lead Owner.", 401);
            }
        }else{
            $owner = TableRegistry::getTableLocator()->get('LeadOwnersEntity')->find()
                ->where(['lead_id' => $leadid, 'point_of_contact' => 1])
                ->first()
                ->toArray();
        }

        if($owner['email']){
            $fullName = $owner['full_name'];
            $ownerUser = [
                'email' => $owner['email'],
                'phone' => $owner['phone'],
                'first_name' => $owner['first_name'],
                'last_name' => $owner['last_name'],
                'id' => $owner['owner_id'],
                'owner_ref' => $owner['owner_ref'],
            ];
        }

        // $lead = TableRegistry::getTableLocator()->get('LeadEntity')->find()->where(['lead_id' => $leadid])->first()->toArray();
        // $partner = TableRegistry::getTableLocator()->get('PartnerEntity')->find()->where(['partner_id' => $lead['partner_id']])->first()->toArray();
        $user = [
            'lead_id' => $leadid,
            'lead_ref' => $leadRef,
            'owner_id' => $ownerRef,
            'owner_ref' => @$applicant,
            'account_type' => 'Applicant',
            'level' => -1,
            'newFlow' => true,
            'name' => $fullName,
        ];
        return [
            'user' => array_merge($user, $ownerUser),
            'lead_cred' => $lead_cred,
        ];
    }

    public function validateAid($aidIn = null)
    {        
        try {
            $this->logout();
            if (is_null($aidIn) && !$this->request->is('get')) {
                throw new Exception("only GET request is available.", 405);
            }
            $aid = $aidIn??($this->request->getQuery('aid') ?? null);
            if (!is_null($aid)) {
                $aidResult = $this->_getOwnerByAid($aid);
                $user = $aidResult['user'];
                $lead_cred = $aidResult['lead_cred'];
                $this->Auth->setUser($user);
                $user = $this->Auth->user();
                $token = JWT::encode([
                    'sub' => 'Applicant',
                    'exp' =>  intval($lead_cred[1]),
                    'user' => $user,
                    // 'broker_name' => $partner['company_name']
                    ],
                    Security::getSalt());
                $this->setCookieSameSite('auth_token', $token, intval($lead_cred[1]), "/", "." . $_SERVER['SERVER_NAME'], getenv('LEND_ENV') > 0, true, (getenv('LEND_ENV') > 0 ? "None" : false));
                return $aidIn ? [['success' => true], 200] : $this->setJsonResponse(['success' => true]);
            }
            throw new Exception("Unauthorized", 401);
        }
        catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return $aidIn ? [['error' => $e->getMessage()], 400] : $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
        }
    }

    public function generateAid($af, $partnerUserRef = null) {
        if (!empty($af)) {
            $partnerId = TableRegistry::getTableLocator()->get('PartnerEntity')->find()
                ->where(['affiliate_id' => $af])
                ->first()['partner_id'];
        }

        if (!empty($partnerUserRef)) {
            $hashids = new Hashids('partner_users', 7);
            $partner_user_id = $hashids->decode($partnerUserRef)[0];
        }

        if (empty($partnerId) && empty($partner_user_id)) {
            return $this->setJsonResponse(['error' => 'Invalid partner reference'], 400);
        }

        // Add expiry date
        $expiry_date = strtotime('+ 4 Day');
        $login = implode('l3nd', [$partnerId, $expiry_date, 'FullApp', 'consumerQuote', $partner_user_id ?? null]);
        
        $build_sig = Security::salt() . '#' . $login; // Build the signature we are expecting on the receiving end
        $build_sig = hash('sha1', $build_sig); // Build the signature we are expecting on the receiving end
        $code = $login . '#' . $build_sig; // Join it via a hash
        $code = base64_encode($code); // Base64 Encode it
        return $this->setJsonResponse(['success' => true, 'aid' => $code]);
    }

    private function logout(){
        unset($_COOKIE['auth_token']);
        setcookie('auth_token', '', time()-3600, "/");
        $this->Auth->logout();
    }

    public function getOwnerByAid($aid)
    {
        $aidResult = $this->_getOwnerByAid($aid);
        $user = $aidResult['user'];
        if ($user) {
            return $this->setJsonResponse(['success' => true, 'user' => $user]);
        }
        return $this->setJsonResponse(['error' => 'Invalid aid'], 400);
    }

    public function lendIqEmail($email = null)
    {
        if (is_null($email) && !$this->request->is('get')) {
            throw new Exception("only GET request is available.", 405);
        }
        $email = $email ?? ($this->request->getQuery('email') ?? null);
        if (!is_null($email)) {
            $leadOwner = TableRegistry::getTableLocator()->get('LeadOwnersEntity')->find()
                ->contain(['LeadEntity'])
                ->where(['LeadOwnersEntity.email' => $email, 'LeadEntity.lend_iq_lead' => 1])
                ->first();
            if ($leadOwner) {
                return $this->setJsonResponse(['success' => true, 'user' => [
                    'lead_id' => $leadOwner->lead_id,
                    'lead_ref' => $leadOwner->lead->lead_ref,
                    'owner_ref' => $leadOwner->owner_ref,
                    'account_type' => 'Applicant',
                    'name' => $leadOwner->full_name,
                    'email' => $leadOwner->email,
                    'phone' => $leadOwner->phone,
                    'first_name' => $leadOwner->first_name,
                    'last_name' => $leadOwner->last_name,
                    'id' => $leadOwner->owner_id,
                    'level' => -1,
                    'newFlow' => true,
                ]]);
            }
        }
        return $this->setJsonResponse(['error' => 'Invalid email'], 400);
    }

    /**
     * Temporary function for testing
     * TO-DO remove
     * @return void
     */
    public function test(){
        try {
            if (!$this->request->is('post')) {
                return $this->setJsonResponse(['error' => "only POST request is available."], 405);
            }
            $data = $this->request->getData();
            $code = TableRegistry::get('Leads')->createAutoLoginCode($data['leadId']);
            return $this->setJsonResponse(['code' => $code]);
        }
        catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return $this->setJsonResponse(['error' => "Unable to process request"], 400);
        }
    }
}