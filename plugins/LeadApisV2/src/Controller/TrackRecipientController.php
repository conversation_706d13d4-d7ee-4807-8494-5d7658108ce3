<?php

namespace LeadApisV2\Controller;

use Cake\Log\Log;
use Cake\ORM\TableRegistry;
use LeadApisV2\Helper\CreditGuidePageStatusHelper;
use App\Lend\LendInternalAuth;
class TrackRecipientController extends AppController
{
  protected $adhocOwners;
  protected $callbackOwner;

  public function initialize()
  {
    parent::initialize();
    $this->Auth->allow([
      'creditGuide',
      'privacyForm',
      'adhoc',
    ]);
  }

  protected function getCallbackOwnerLeadContext($metaData)
  {
    if (isset($this->callbackOwner)) {
      return $this->callbackOwner;
    }
    $callbackEmail = $metaData['recipient_email'];
    $leadId = LendInternalAuth::unhashLeadId($metaData['lead_ref']);

    $owner = null;
    if ($metaData['customRef']) {
      if (strpos($metaData['customRef'], 'na-') === 0) {
        $lendSignatureRequestRef = str_replace('na-', '', $metaData['customRef']);
        $owner = TableRegistry::getTableLocator()
          ->get('LendSignatureRequestEntity')
          ->find('all')
          ->where([
            'id' => LendInternalAuth::unhashLendSignatureRequestId($lendSignatureRequestRef),
          ])
          ->first()
          ->toArray();
        $owner['lend_signature_requests_id'] = $owner['id'];
      } else {
        $ownerId = LendInternalAuth::unhashOwnerId($metaData['customRef']);
        $owner = TableRegistry::getTableLocator()
          ->get('LeadOwnersEntity')
          ->find('all')
          ->where([
            'owner_id' => $ownerId,
          ])
          ->first();
      }
    } else {
      $owner = TableRegistry::getTableLocator()
        ->get('LeadOwnersEntity')
        ->find('all')
        ->where([
          'email' => $callbackEmail,
          'lead_id' => $leadId,
        ])
        ->first();
    }
    $this->callbackOwner = $owner;
    return $owner;
  }

  protected function getCallbackOwnerApplicantContext($metaData)
  {
    if (isset($this->callbackOwner)) {
      return $this->callbackOwner;
    }
    $callbackEmail = $metaData['recipient_email'];

    $owner = null;
    if ($metaData['customRef']) {
      $ownerId = LendInternalAuth::unhashPartnerAccountPeopleId($metaData['customRef']);
      $owner = TableRegistry::getTableLocator()
        ->get('PartnerAccountPeopleEntity')
        ->find('all')
        ->where([
          'id' => $ownerId,
        ])
        ->first();
    } else {
      $owner = TableRegistry::getTableLocator()
        ->get('PartnerAccountPeopleEntity')
        ->find('all')
        ->where([
          'email' => $callbackEmail,
        ])
        ->first();
    }
    $this->callbackOwner = $owner;
    return $owner;
  }

  protected function getCallbackOwner($data)
  {
    if (isset($this->callbackOwner)) {
      return $this->callbackOwner;
    }
    $type = $data['meta_data']['type'] ?? "lead";
    switch ($type) {
      case 'lead':
        return $this->getCallbackOwnerLeadContext($data['meta_data']);
      case 'applicant':
      case 'account':
        return $this->getCallbackOwnerApplicantContext($data['meta_data']);
      default:
        return null;
    }
  }

  protected function getMainRecipient($data)
  {
    $ownerIds = json_decode($data['meta_data']['owner_ids']);
    if (count($ownerIds) === 1) {
      return $this->getCallbackOwner($data);
    }
    $type = $data['meta_data']['type'];
    // Consumer and Commercial flows do not pass 'type'.
    // Both Consumer and Commercial flows are lead type, so default value is 'lead'.
    if (empty($type)) {
      $type = 'lead';
    }
    switch ($type) {
      case 'lead':
        $owners = $this->getAdhocOwners($data);
        $mainApplicant = null;
        foreach ($owners as $owner) {
          if ($owner['point_of_contact'] == true) {
            $mainApplicant = $owner;
          }
        }
        if ($mainApplicant) {
          return $mainApplicant;
        }
        return $this->getCallbackOwner($data);
      case 'account':
        $accountPeople = $this->getAdhocOwners($data);
        $pointOfContact = null;
        foreach ($accountPeople as $person) {
          if ($person['is_point_of_contact'] == true) {
            $pointOfContact = $person;
          }
        }
        if ($pointOfContact) {
          return $pointOfContact;
        }
        return $this->getCallbackOwner($data);
      case 'applicant':
        return $this->getCallbackOwner($data);
      default:
        return null;
    }
  }

  protected function getAdhocOwners($data)
  {
    if (isset($this->adhocOwners)) {
      return $this->adhocOwners;
    }
    $type = $data['meta_data']['type'];
    if (empty($type)) {
      $type = 'lead';
    }
    $owners = [];
    switch ($type) {
      case 'lead':
        $ownerIds = json_decode($data['meta_data']['owner_ids']);
        $owners =  TableRegistry::getTableLocator()
          ->get('LeadOwnersEntity')
          ->find('all')
          ->where(['owner_id IN' => $ownerIds])
          ->toArray();
        break;
      case 'account':
        $partnerAccount = TableRegistry::getTableLocator()
          ->get('PartnerAccountEntity')
          ->find('all')
          ->where(['account_ref' => $data['meta_data']['account_ref']])
          ->contain([
            'PartnerEntity',
          ])
          ->first();
        $peopleIds = json_decode($data['meta_data']['applicant_ids']);
        $owners = TableRegistry::getTableLocator()
          ->get('PartnerAccountPeopleEntity')
          ->find('all')
          ->where(['partner_account_id' => $partnerAccount['partner_account_id'], 'PartnerAccountPeopleEntity.id IN' => $peopleIds])
          ->toArray();
        break;
      case 'applicant':
        $partnerAccountPeopleId = LendInternalAuth::unhashPartnerAccountPeopleId($data['meta_data']['applicant_ref']);
        $partnerAccountPeople = TableRegistry::getTableLocator()
          ->get('PartnerAccountPeopleEntity')
          ->find('all')
          ->where(['id' => $partnerAccountPeopleId])
          ->contain([
            'PartnerEntity',
          ])
          ->first();
        $owners = [$partnerAccountPeople];
        break;
      default:
        $owners = [];
        break;
    }
    $this->adhocOwners = $owners;
    return $owners;
  }

  protected function getAllOwners($data)
  {
    if($data['meta_data']['type']){
      return $this->getAdhocOwners($data);
    }
    $ownerIds = json_decode($data['meta_data']['owner_ids']);
    return TableRegistry::getTableLocator()
          ->get('LeadOwnersEntity')
          ->find('all')
          ->where(['owner_id IN' => $ownerIds])
          ->toArray();
  }

  protected function getSentRecords($data)
  {
    $envelopeId = $data['meta_data']['envelopeId'];

    $lendSignatureRequestRecordsTable = TableRegistry::getTableLocator()->get('LendSignatureRequestEntity');
    $conditions = [
      'service_envelope_id' => $envelopeId,
    ];

    $lendSignatureRequestRecords = $lendSignatureRequestRecordsTable->find('all')
      ->where($conditions)
      ->toArray();
    return $lendSignatureRequestRecords;
  }

  protected function getCallbackOwnerSentRecord($data)
  {
    $owner = $this->getCallbackOwner($data);
    $type = $data['meta_data']['type'];
    if (empty($type)) {
      $type = 'lead';
    }
    $envelopeId = $data['meta_data']['envelopeId'];
    switch ($type) {
      case 'lead':
        $ownerFieldName = 'owner_id';
        $entityIdName = 'owner_id';
        break;
      case 'account':
      case 'applicant':
        $ownerFieldName = 'partner_account_people_id';
        $entityIdName = 'id';
        break;
      default:
        return [];
    }
    $ownerId = $owner[$entityIdName];

    $lendSignatureRequestRecordsTable = TableRegistry::getTableLocator()->get('LendSignatureRequestEntity');
    $conditions = [
      'service_envelope_id' => $envelopeId,
    ];

    // Handle non-applicant recipients
    if (!empty($data['meta_data']['customRef']) && strpos($data['meta_data']['customRef'], 'na-') === 0) {
      $lendSignatureRequestRef = str_replace('na-', '', $data['meta_data']['customRef']);
      $conditions['id'] = LendInternalAuth::unhashLendSignatureRequestId($lendSignatureRequestRef);
    } else {
      $conditions["{$ownerFieldName}"] = $ownerId;
    }
    return $lendSignatureRequestRecordsTable->find('all')
      ->where( $conditions)
      ->first();
  }

  protected function getEnvelopeSentRecords($data)
  {
    $envelopeId = $data['meta_data']['envelopeId'];

    $lendSignatureRequestRecordsTable = TableRegistry::getTableLocator()->get('LendSignatureRequestEntity');
    return $lendSignatureRequestRecordsTable->find('all')
      ->where([
        'service_envelope_id' => $envelopeId,
      ])
      ->all();
  }

  protected function prepareUploadEntity($fullPath, $fileSize)
  {
    $pathParts = explode('/', $fullPath);
    $fileName = $pathParts[sizeof($pathParts) - 1];
    unset($pathParts[sizeof($pathParts) - 1]);
    return [
      'name' => $fileName,
      'full_path' => implode('/', $pathParts),
      'uploaded_by' => 'Partner User',
      'file_type' => 'application/pdf',
      'file_size' => $fileSize,
      'status' => 'Active',
      'include_for_lenders' => 0,
    ];

  }

  protected function createOrUpdateLeadUpload($envelopeId, $ownerId, $leadId, $filePath, $fileSize = 200000, $fileType = null)
  {
    $partnerLeadUploadsMetaTable = TableRegistry::getTableLocator()->get('PartnerLeadUploadsMetaEntity');
    $metaWithUpload = $partnerLeadUploadsMetaTable
      ->find('all')
      ->contain(['PartnerLeadUploadsEntity'])
      ->where([
        'field_name' => 'envelope_id',
        'value' => $envelopeId,
      ])
      ->first();

    if (!empty($metaWithUpload)) {
      $upload = $metaWithUpload['partner_lead_uploads'];
      $upload->file_size = $fileSize;
      $upload->full_path = $filePath;

      $partnerLeadUploadsTable = TableRegistry::getTableLocator()->get('PartnerLeadUploadsEntity');
      $partnerLeadUploadsTable->save($upload);
    }
    else {
      $this->saveLeadUpload($ownerId, $leadId, $filePath, $fileSize, $fileType, $envelopeId);
    }
  }

  protected function saveLeadUpload($ownerId, $leadId, $filePath, $fileSize = 200000, $fileType = null, $envelopeId = null)
  {
    $partnerLeadUploadsTable = TableRegistry::getTableLocator()->get('PartnerLeadUploadsEntity');
    $entityData = $this->prepareUploadEntity($filePath, $fileSize);
    $leadUploadData = [
      'lead_id' => $leadId,
      'owner_id' => $ownerId,
      'is_compliance' => 1,
      'partner_lead_uploads_meta' => [
        [
          'field_name' => 'acceptable',
          'value' => 'false',
        ],
      ],
    ];
    if ($fileType) {
      $leadUploadData['partner_lead_uploads_meta'][] = [
        'field_name' => 'specified',
        'value' => $fileType,
      ];
    }
    if (!empty($envelopeId)) {
      $leadUploadData['partner_lead_uploads_meta'][] = [
        'field_name' => 'envelope_id',
        'value' => $envelopeId,
      ];
    }
    $entityData = array_merge($leadUploadData, $entityData);
    $entity = $partnerLeadUploadsTable->newEntity($entityData, [
      'associated' => ['PartnerLeadUploadsMetaEntity'],
    ]);
    $partnerLeadUploadsTable->save($entity);
  }

  protected function saveAccountUpload($partnerAccountId, $filePath, $fileSize = 200000, $fileType = null)
  {
    $partnerAccountUploadsTable = TableRegistry::getTableLocator()->get('PartnerAccountUploadsEntity');
    $entityData = $this->prepareUploadEntity($filePath, $fileSize);
    $partnerAccountUploadData = [
      'partner_account_id' => $partnerAccountId,
      'is_compliance' => 1,
      'partner_account_uploads_meta' => [
        [
          'field_name' => 'acceptable',
          'value' => 'false',
        ],
      ],
    ];
    if ($fileType) {
      $partnerAccountUploadData['partner_account_uploads_meta'][] = [
        'field_name' => 'specified',
        'value' => $fileType,
      ];
    }
    $entityData = array_merge($partnerAccountUploadData, $entityData);
    $entity = $partnerAccountUploadsTable->newEntity($entityData, [
      'associated' => ['PartnerAccountUploadsMetaEntity'],
    ]);
    $partnerAccountUploadsTable->save($entity);
  }

  protected function saveApplicantUpload($partnerAccountPeopleId, $filePath, $fileSize = 200000, $fileType = null)
  {
    $partnerApplicantGroupUploadsTable = TableRegistry::getTableLocator()->get('PartnerApplicantGroupUploadsEntity');
    $entityData = $this->prepareUploadEntity($filePath, $fileSize);
    $partnerApplicantGroupUploadData = [
      'people_id' => $partnerAccountPeopleId,
      'is_compliance' => 1,
      'partner_applicant_group_uploads_meta' => [
        [
          'field_name' => 'acceptable',
          'value' => 'false',
        ],
      ],
    ];
    if ($fileType) {
      $partnerApplicantGroupUploadData['partner_applicant_group_uploads_meta'][] = [
        'field_name' => 'specified',
        'value' => $fileType,
      ];
    }
    $entityData = array_merge($partnerApplicantGroupUploadData, $entityData);
    $entity = $partnerApplicantGroupUploadsTable->newEntity($entityData, [
      'associated' => ['PartnerApplicantGroupUploadsMetaEntity'],
    ]);
    $savedEntity = $partnerApplicantGroupUploadsTable->save($entity);
  }

  protected function saveUpload($data, $owner, $fileType = 'Lend Sign Adhoc Doc')
  {
    $type = $data['meta_data']['type'];
    switch ($type) {
      case 'lead':
        $leadId = (new LendInternalAuth)->unhashLeadId($data['meta_data']['lead_ref']);
        $this->saveLeadUpload($owner['owner_id'], $leadId, $data['signedDocument'], $data['documentFileSize'] ?? 200000, $fileType);
        break;
      case 'account':
        $partnerAccountId = LendInternalAuth::unhashPartnerAccountId($data['meta_data']['account_ref']);
        $this->saveAccountUpload($partnerAccountId, $data['signedDocument'], $data['documentFileSize'] ?? 200000, $fileType);
        break;
      case 'applicant':
        $partnerAccountPeopleId = LendInternalAuth::unhashPartnerAccountPeopleId($data['meta_data']['applicant_ref']);
        $this->saveApplicantUpload($partnerAccountPeopleId, $data['signedDocument'], $data['documentFileSize'] ?? 200000, $fileType);
        break;
      default:
        # code...
        break;
    }
  }

  protected function setConsent($owner)
  {
    $dateFormatted = date('Y-m-d H:i:s', time());
    $owner['consent'] = $dateFormatted;
    TableRegistry::getTableLocator()
      ->get('LeadOwnersEntity')
      ->save($owner);
  }

  protected function handleSaveUpload($data, $owners, $fileType)
  {
    $callbackOwner = $this->getCallbackOwner($data);
    foreach ($owners as $owner) {
      if ($data['status'] === 'All Signed' || $data['status'] === 'signed') {
        if ($data['meta_data']['sendMethod'] === 'multiple' && $owner['owner_id'] === $callbackOwner['owner_id']) {
          $this->saveUpload($data, $owner, $fileType);
        }
      }
    }
    if ($data['meta_data']['sendMethod'] === 'single' && $data['status'] === 'All Signed') {
      $owner = $this->getMainRecipient($data);
      $this->saveUpload($data, $owner, $fileType);
    }
  }

  protected function amendedFields($data, $owner, $lead)
  {
    $fieldNames = [
      "first_name" => ["label" => "First name", "field" => "first_name"],
      "last_name" => ["label" => "Last name", "field" => "last_name"],
      "Recipient_First_Name" => ["label" => "First Name", "field" => "first_name"],
      "Recipient_Last_Name" => ["label" => "Last Name", "field" => "last_name"],
    ];
    $formData = $data['form_data'];
    $fields = [];
    $linkParams = [];
    $amendedFields = [];
    foreach ($formData as $key => $value) {
      if (isset($fieldNames[$key])) {
        if ($value == $owner[$fieldNames[$key]['field']]) {
          continue;
        }
        $amendedFields[$fieldNames[$key]['field']] = $value;
        $fields[] = $fieldNames[$key]['label'] . ": " . $owner[$fieldNames[$key]['field']] . " changed to " . $value;
        $linkParams[$fieldNames[$key]['field']] = $value;
      }
    }
    if(!empty($fields)){
      $leadOwnerTable = TableRegistry::getTableLocator()->get('LeadOwnersEntity');
      $ownerEntity = $leadOwnerTable->get($owner['owner_id']);
      foreach ($amendedFields as $key => $value) {
        $ownerEntity[$key] = $value;
      }
      $ownerEntity['keep_nccp_status'] = true;
      $leadOwnerTable->save($ownerEntity);
    }

    $ownerLink = $owner['owner_ref'] ? '?applicantId=' . $owner['owner_ref'] : '';
    return [
      'amended_fields' => implode("<br />", $fields),
      'view_applicant_details_page_link' => '<a href="' . getenv('DOMAIN_BRO', true) . '/lead/' . $lead['lead_ref'] . '/applicant' . $ownerLink . '" class="login-btn" target="_blank">View Lead Applicant</a>',
      'view_credit_guide_page_link' => '<a href="' . getenv('DOMAIN_BRO', true) . '/lead/' . $lead['lead_ref'] . '/application/credit-guide-quote-privacy" class="login-btn">View Lead</a>',
    ];
  }

  protected function amendedFieldsNotification($data, $owner, $templateUseName, $amendNotificationLookupCode)
  {
    if ($data['form_data']) {
      $lead = TableRegistry::getTableLocator()
        ->get('LeadEntity')
        ->find('all')
        ->where(['lead_ref' => $data['meta_data']['lead_ref']])
        ->first();
      $adhocData = $this->amendedFields($data, $owner, $lead);
      $adhocData['template_use_name'] = $templateUseName;
      if ($data['user_custom_ref'] && !empty($adhocData['amended_fields'])) {
        $partnerUser = TableRegistry::getTableLocator()->get('PartnerUserEntity')->find()
          ->where(['partner_user_ref' => $data['user_custom_ref']])->first();
        $this->LoadModel('PartnerNotifications')->sendPartnerNotifications(
          $lead['partner_id'],
          $amendNotificationLookupCode,
          $lead['lead_id'],
          $adhocData,
          $partnerUser->partner_user_id,
          [
            'applicant_name' => $owner['full_name'],
          ]
        );
      }
    }
  }

  protected function signNotification($type, $data, $owners, $template_use_name, $signType, $amendNotificationLookupCode = null)
  {
    $partner_user_ref = $data['user_custom_ref'];
    if (empty($type)) {
      $type = 'lead';
    }
    switch ($type) {
      case 'lead':
        $lead = TableRegistry::getTableLocator()
          ->get('LeadEntity')
          ->find('all')
          ->where(['lead_ref' => $data['meta_data']['lead_ref']])
          ->contain([
            'PartnerEntity',
          ])
          ->first();
        $partnerId = $lead['partner_id'];
        $leadId = $lead['lead_id'];
        $view_docs_page_link = '<a href="' . getenv('DOMAIN_BRO', true) . '/lead/' . $lead['lead_ref'] . '/documentation" class="login-btn">View Lead Documents</a>';
        foreach ($owners as $owner) {
          if($data['meta_data']['customRef'] == $owner['owner_ref'] && $amendNotificationLookupCode){
            $this->amendedFieldsNotification($data, $owner, $template_use_name, $amendNotificationLookupCode);
          }
        }
        break;
      case 'account':
        $partnerAccount = TableRegistry::getTableLocator()
          ->get('PartnerAccountEntity')
          ->find('all')
          ->where(['account_ref' => $data['meta_data']['account_ref']])
          ->contain([
            'PartnerEntity',
          ])
          ->first();
        $partnerId = $partnerAccount['partner_id'];
        $leadId = null;
        $view_docs_page_link = '<a href="' . getenv('DOMAIN_BRO', true) . '/accounts/' . $$data['meta_data']['account_ref'] . '/documentation" class="login-btn">View Account Documents</a>';
        break;
      case 'applicant':
        $partnerAccountPeopleId = LendInternalAuth::unhashPartnerAccountPeopleId($data['meta_data']['applicant_ref']);
        $partnerAccountPeople = TableRegistry::getTableLocator()
          ->get('PartnerAccountPeopleEntity')
          ->find('all')
          ->where(['id' => $partnerAccountPeopleId])
          ->contain([
            'PartnerEntity',
          ])
          ->first();
        $partnerId = $partnerAccountPeople['partner_id'];
        $leadId = null;
        $view_docs_page_link = '<a href="' . getenv('DOMAIN_BRO', true) . '/account-applicants/' . $data['meta_data']['applicant_ref'] . '/documentation" class="login-btn">View Applicant Documents</a>';
        break;
      default:
        return $this->setJsonResponse(['success' => false]);
        break;
    }

    $applicant_name = [];
    foreach ($owners as $owner) {
      $applicant_name[] = $owner['full_name'];
    }
    $partnerUser = TableRegistry::getTableLocator()->get('PartnerUserEntity')->find()
      ->where(['partner_user_ref' => $partner_user_ref])->first();
    switch ($signType) {
      case 'All Signed':
        $lookupCode = 'LSDocsSignedBroker';
        break;
      case 'declined':
        $lookupCode = 'LSClientDeclinedBroker';
        break;
      case 'partially_signed':
        $lookupCode = 'LSPartiallySignedBroker';
        break;
      default:
        $lookupCode = 'LSDocsSignedBroker';
        break;
    }
    $owner = $this->getCallbackOwner($data);
    $this->LoadModel('PartnerNotifications')->sendPartnerNotifications(
      $partnerId,
      $lookupCode,
      $leadId,
      [
        'template_use_name' => $template_use_name,
        'view_lead_docs_page_link' => $view_docs_page_link,
        'applicant_who_signed' => $owner['full_name'],
      ],
      $partnerUser->partner_user_id,
      [
        'applicant_name' => implode(", ", $applicant_name),
      ]
    );
  }

  protected function sentRecordToSigned($recipientRecord, $data) {
    $dateFormatted = date('Y-m-d H:i:s', time());
    $recipientRecord['signed_time'] = $dateFormatted;
    $recipientRecord['status'] = 'signed';
    $recipientRecord['upload_url'] = $data['signedDocument'];
    $lendSignatureRequestTable = TableRegistry::getTableLocator()->get('LendSignatureRequestEntity');
    $lendSignatureRequestTable->save($recipientRecord);
  }

  protected function handleDecline($data, $templateName) {
    $type = $data['meta_data']['type'];
    if (empty($type)) {
      $type = 'lead';
    }
    $sendMethod = $data['meta_data']['sendMethod'];
    $declinedOwner = $this->getCallbackOwner($data);
    $lendSignatureRequestRecordsTable = TableRegistry::getTableLocator()->get('LendSignatureRequestEntity');

    if (empty($declinedOwner)) {
      return $this->setJsonResponse(['success' => false]);
    }

    if ($sendMethod === 'multiple') {
      $recipientRecord = $this->getCallbackOwnerSentRecord($data);
      $recipientRecord['status'] = 'declined';
      $lendSignatureRequestRecordsTable->save($recipientRecord);
    }
    if ($sendMethod === 'single') {

      $allRecords = $this->getSentRecords($data);
      foreach ($allRecords as $record) {
        // Handle 3rd party applicant.
        if (empty($declinedOwner->owner_id)) {
          if ($record->id === $declinedOwner->id) {
            $record->status = 'declined';
            $lendSignatureRequestRecordsTable->save($record);
            continue;
          }
        }
        if ($record->owner_id === $declinedOwner->owner_id) {
          $record->status = 'declined';
          $lendSignatureRequestRecordsTable->save($record);
          continue;
        }
        if ($record->status === 'signed') {
          $record->status = 'invalidated_by_other';
        }
        if (in_array($record->status, ['sent', 'opened'])) {
          $record->status = 'cancelled_by_other';
        }
        $lendSignatureRequestRecordsTable->save($record);
      }
    }

    if ($data['user_custom_ref']) {
      $this->signNotification($type, $data, [$declinedOwner], $templateName, 'declined');
    }
  }

  public function creditGuide()
  {
    $data = $this->request->getData();
    Log::write('debug', 'creditGuide call back data: ' . json_encode($data));
    $fileType = 'Credit Guide / Privacy Statement (Electronic Signature)';
    if ($data['status'] === 'All Signed') {
      $leadId = LendInternalAuth::unhashLeadId($data['meta_data']['lead_ref']);
      $recipientRecord = $this->getCallbackOwnerSentRecord($data);
      Log::write('debug', 'recipientRecord: ' . json_encode($recipientRecord));
      if (!in_array($recipientRecord['status'], ['review required', 'signed'])) {
        $this->sentRecordToSigned($recipientRecord, $data);
      }

      $callbackOwner = $this->getCallbackOwner($data);
      $this->handleSaveUpload($data, [$callbackOwner], $fileType);
      $this->setConsent($callbackOwner);
      $allOwners = $this->getAllOwners($data);
      $this->signNotification("lead", $data, $allOwners,  'Credit Guide and Quote', 'All Signed', 'LSAmendPersonalDetailsCGQP');
      CreditGuidePageStatusHelper::updateStatusByLeadId($leadId, true);
    }
    if ($data['status'] === 'signed') {
      $callbackOwner = $this->getCallbackOwner($data);
      $this->handleSaveUpload($data, [$callbackOwner], $fileType);
      $this->setConsent($callbackOwner);
      $recipientRecord = $this->getCallbackOwnerSentRecord($data);
      if ($recipientRecord['status'] != 'review required') {
        $this->sentRecordToSigned($recipientRecord, $data);
      }
      $this->signNotification('lead', $data, [$callbackOwner], 'Credit Guide and Quote', 'partially_signed', 'LSAmendPersonalDetailsCGQP');
    }
    if ($data['status'] === 'open') {
      $recipientRecord = $this->getCallbackOwnerSentRecord($data);
      Log::write('debug', 'recipientRecord: ' . json_encode($recipientRecord));
      if ($recipientRecord['status'] === 'sent') {
        $recipientRecord['status'] = 'opened';
        $lendSignatureRequestTable = TableRegistry::getTableLocator()->get('LendSignatureRequestEntity');
        $lendSignatureRequestTable->save($recipientRecord);
      }
    }
    if ($data['status'] === 'declined') {
      $this->handleDecline($data, 'Credit Guide and Quote');
    }

    return $this->setJsonResponse(['success' => true]);
  }

  public function privacyForm()
  {
    $data = $this->request->getData();
    Log::write('debug', 'privacyForm call back data: ' . json_encode($data));

    $sendMethod = $data['meta_data']['sendMethod'];
    $lendSignatureRequestTable = TableRegistry::getTableLocator()->get('LendSignatureRequestEntity');
    $dateFormatted = date('Y-m-d H:i:s', time());
    $fileType = 'Privacy Form (Electronic Signature)';
    if ($data['status'] === 'All Signed') {
      $callbackOwner = $this->getCallbackOwner($data);
      $this->setConsent($callbackOwner);
      $leadOwnerIds = json_decode($data['meta_data']['owner_ids']);
      $envelopeSentRecords = $this->getEnvelopeSentRecords($data);
      $recordsByOwnerId = [];
      foreach ($envelopeSentRecords as $record) {
        $recordsByOwnerId[$record['owner_id']] = $record;
      }

      foreach ($leadOwnerIds as $ownerId) {
        $ownerRecord = $recordsByOwnerId[$ownerId];

        if ($ownerRecord) {
          $ownerRecord->status = 'signed';
          if ($ownerRecord->owner_id === $callbackOwner['owner_id']) {
            $ownerRecord->signed_time = $dateFormatted;
          }
          if ($sendMethod === 'single' || $ownerRecord->owner_id === $callbackOwner['owner_id']) {
            $ownerRecord->upload_url = $data['signedDocument'];
          }
          $lendSignatureRequestTable->save($ownerRecord);
        }
      }
      $owners = [$callbackOwner];
      if ($sendMethod === 'single') {
        $owners = $this->getAllOwners($data);
      }
      $this->handleSaveUpload($data, $owners, $fileType);
      $this->signNotification("lead", $data, $owners, 'Privacy Form', 'All Signed', 'LSAmendPersonalDetailsPriv');
    }
    if ($data['status'] === 'signed') {
      $callbackOwner = $this->getCallbackOwner($data);
      $this->setConsent($callbackOwner);
      $recipientRecord = $this->getCallbackOwnerSentRecord($data);
      if ($recipientRecord['status'] != 'review required') {
        $this->sentRecordToSigned($recipientRecord, $data);
      }
      $this->handleSaveUpload($data, [$callbackOwner], $fileType);
      $this->signNotification('lead', $data, [$callbackOwner], 'Privacy Form', 'partially_signed', 'LSAmendPersonalDetailsPriv');
    }
    if ($data['status'] === 'open') {
      $recipientRecord = $this->getCallbackOwnerSentRecord($data);
      Log::write('debug', 'recipientRecord: ' . json_encode($recipientRecord));
      if ($recipientRecord['status'] === 'sent') {
        $recipientRecord['status'] = 'opened';
        $lendSignatureRequestTable = TableRegistry::getTableLocator()->get('LendSignatureRequestEntity');
        $lendSignatureRequestTable->save($recipientRecord);
      }
    }
    if ($data['status'] === 'declined') {
      $this->handleDecline($data, 'Privacy Form');
    }
    return $this->setJsonResponse(['success' => true]);
  }

  public function adhoc()
  {
    $data = $this->request->getData();
    Log::write('debug', 'Adhoc call back data: ' . json_encode($data));

    $type = $data['meta_data']['type'];
    $sendMethod = $data['meta_data']['sendMethod'];

    if ($data['status'] === 'All Signed') {
      $dateFormatted = date('Y-m-d H:i:s', time());
      if ($sendMethod === 'multiple') {
        $record = $this->getCallbackOwnerSentRecord($data);
        Log::write('debug', 'sentRecord: ' . json_encode($record));
        if (!in_array($record['status'], ['review required', 'signed'])) {
          $this->sentRecordToSigned($record, $data);
        }
      }
      else {
        $sentRecords = $this->getSentRecords($data);
        $callbackOwner = $this->getCallbackOwner($data);
        foreach ($sentRecords as $record) {
          Log::write('debug', 'sentRecord: ' . json_encode($record));
          if (!in_array($record['status'], ['review required', 'signed'])) {
            if ($record['owner_id'] === $callbackOwner['owner_id'] || $record['id'] === $callbackOwner['lend_signature_requests_id']) {
              $record['signed_time'] = $dateFormatted;
              $record['status'] = 'signed';
            }
            $record['upload_url'] = $data['signedDocument'];
            $lendSignatureRequestRecordsTable = TableRegistry::getTableLocator()->get('LendSignatureRequestEntity');
            $lendSignatureRequestRecordsTable->save($record);
          }
        }
      }

      $pathParts = explode('/', $data['signedDocument']);
      unset($pathParts[sizeof($pathParts) - 1]);
      // Save Upload.
      if ($sendMethod === 'multiple') {
        $owner = $this->getCallbackOwner($data);
        $this->saveUpload($data, $owner);
      }
      else {
        $owner = $this->getMainRecipient($data);
        $this->saveUpload($data, $owner);
      }
      $owners = $this->getAdhocOwners($data);
      $this->signNotification($type, $data, $owners, $data['meta_data']['templateName'], 'All Signed');
    }
    if ($data['status'] === 'signed') {
      $owner = $this->getCallbackOwner($data);
      $dateFormatted = date('Y-m-d H:i:s', time());
      $record = $this->getCallbackOwnerSentRecord($data);
      Log::write('debug', 'sentRecord: ' . json_encode($record));
      if (!in_array($record['status'], ['review required', 'signed'])) {
        $record['signed_time'] = $dateFormatted;
        $record['status'] = 'signed';
        if ($sendMethod === 'multiple') {
          $record['upload_url'] = $data['signedDocument'];
        }
        $lendSignatureRequestRecordsTable = TableRegistry::getTableLocator()->get('LendSignatureRequestEntity');
        $lendSignatureRequestRecordsTable->save($record);
      }

      if ($sendMethod === 'multiple') {
        $pathParts = explode('/', $data['signedDocument']);
        unset($pathParts[sizeof($pathParts) - 1]);
        // Save Upload.
        $this->saveUpload($data, $owner);
      }
      $this->signNotification($type, $data, [$owners], $data['meta_data']['templateName'], 'partially_signed');
    }
    if ($data['status'] === 'open') {
      $record = $this->getCallbackOwnerSentRecord($data);
      Log::write('debug', 'sentRecord: ' . json_encode($record));
      if ($record['status'] === 'sent') {
        $record['status'] = 'opened';
        $lendSignatureRequestRecordsTable = TableRegistry::getTableLocator()->get('LendSignatureRequestEntity');
        $lendSignatureRequestRecordsTable->save($record);
      }
    }
    if ($data['status'] === 'declined') {
      $this->handleDecline($data, $data['meta_data']['templateName']);
    }
    return $this->setJsonResponse(['success' => true]);
  }
}
