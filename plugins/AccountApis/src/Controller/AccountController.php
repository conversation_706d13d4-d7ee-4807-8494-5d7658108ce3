<?php
namespace AccountApis\Controller;

use App\Lend\CsvImportHelper;
use App\Model\Table\PartnerAccountEntityTable;
use App\Model\Table\PartnerAccountMetaEntityTable;
use App\Model\Table\PartnerAccountPeopleEntityTable;
use Cake\ORM\TableRegistry;
use Cake\Core\Configure;
use Cake\Log\Log;
use Hashids\Hashids;
use App\Lend\LendInternalAuth;
use App\Lend\CurlHelper;
use Cake\Datasource\ConnectionManager;
use App\Model\Table\LeadAbnLookupEntityTable;
use App\Traits\S3Trait;

class AccountController extends AppController
{

  use S3Trait;

  public function initialize(){
    parent::initialize();
    $this->Auth->allow([
      'authenticateApplicants',
      'view', 
      'updateAccountUploads', 
      'updateUploadsRequested', 
      'getS3SignedUploadRequest', 
      'viewImage',
      'runImportAccountsApplicants',
      'search'
    ]);
    $this->loadComponent('AddressValidator');
  }

  // $page = 1, $pagesize = 10
  public function dashboard($page = 1, $pagesize = 10) {
    try {
      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }
      if($user['access_all_leads'] == true){
        $where = ['PartnerAccountEntity.partner_id' => $user['partner_id']];
      }else{
        $where = ['PartnerAccountEntity.partner_user_id' => $user['partner_user_id']];
      }
      $accounts = TableRegistry::getTableLocator()->get('PartnerAccountEntity', [
        'connection' => ConnectionManager::get('reader_db')
        ])->find('all')
                ->where($where)
                ->whereNotNull('PartnerAccountEntity.abn')
                ->contain(['PartnerAccountMetaEntity', 'PocPeople.PartnerAccountPeopleEntity', 'PartnerAccountLinkPeopleEntity.PartnerAccountPeopleEntity', 'LeadAbnLookupEntity', 'CurrentTradingAddress'])
                ->group(['PartnerAccountEntity.partner_account_id']);
                //->order(['PartnerAccountEntity.abn' => 'DESC']);
                //->order(['PartnerAccountEntity.abn IS NULL', "PartnerAccountEntity.abn" => 'ASC']);
      $accounts->select(['total_statements' => $accounts->func()->count('LeadUploadsEntity.upload_id')])
                ->leftJoinWith('LeadUploadsEntity')
                ->enableAutoFields(true);
      $data = $this->request->getData();

      if (!empty($data['search'])) {
        if (stripos($data['search']['field'], 'PartnerAccountPeopleEntity') !== false) {
          $accounts->innerJoinWith('PartnerAccountPeopleEntity', function ($q) use ($data) {
            return $q->where($this->_generateCondition($data['search']));
          });
        } else {
          $accounts->where($this->_generateCondition($data['search']));
        }
      }

      if (!empty($data['filters'])) {
        foreach ($data['filters'] as $field => $items) {
          switch ($field) {
            case 'PartnerAccountEntity.status':
              if (in_array('inactive', $items)) {
                $accounts->where(function ($exp) {
                  return $exp->in('PartnerAccountEntity.status', ['active', 'inactive']);
                });
              } else {
                $accounts->where(['PartnerAccountEntity.status' => 'active']);
              }
              break;
            case 'LeadAbnLookupEntity.gst_effective_from':
              $accounts->leftJoinWith('LeadAbnLookupEntity');
              $accounts->andWhere(function($exp) use ($field, $items) {
                return $exp->or(function ($exp2) use ($field, $items){
                  foreach ($items as $item) {
                    if ($item === 'no') {
                      $exp2->add([$field. ' IS null']);
                    }
                    if ($item === 'yes') {
                      $exp2->add([$field. ' IS NOT null']);
                    }
                  }
                  return $exp2;
                });
              });
              break;
            case 'PartnerAccountMetaEntity.registration_date':
              $accounts->andWhere(function($exp) use ($field, $items) {
                return $exp->or(function ($exp2) use ($field, $items){
                  foreach ($items as $item) {
                    $item = explode(',', $item);
                    $exp2->between($field, $item[0], $item[1]);
                  }
                  return $exp2;
                });
              });
              break;
            case 'PartnerAccountEntity.created':
              $accounts->andWhere(function($exp) use ($field, $items) {
                $exp->gte($field, $items[0]);
                $exp->lte($field, $items[1]);
                return $exp;
              });
              break;
            case 'PartnerAccountMetaEntity.sales_monthly':
              $accounts->andWhere(function($exp) use ($field, $items) {
                return $exp->or(function ($exp2) use ($field, $items){
                  foreach ($items as $item) {
                    $item = explode(',', $item);
                    $exp2->between($field, $item[0], $item[1]);
                  }
                  return $exp2;
                });
              });
              break;
            default:
              $accounts->where([$field.' IN' => $items]);
              break;
          }
        }
      }

      if (empty($data['filters']['PartnerAccountEntity.status'])) {
        $accounts->where(['PartnerAccountEntity.status' => 'active']);
      }

      if (!empty($data['extraFetchData'])) {
          foreach ($data['extraFetchData'] as $extraItem) {
              $accounts->where([$extraItem['field'] => $extraItem['value']]);
          }
      }

      if (!empty($data['sorting'])) {
        // ## WARNING:: SQL injection issue if $field contains something like: `; insert into...`.
        $sort = null;
        $whitelist = [
          'PartnerAccountMetaEntity.organisation_name',
          'PartnerAccountMetaEntity.business_name',
          'PartnerAccountMetaEntity.business_type_abn',
          'PartnerAccountMetaEntity.industry_id',
          'PartnerAccountEntity.abn',
          'PartnerAccountMetaEntity.registration_date',
          'LeadAbnLookupEntity.gst_effective_from',
          'PartnerAccountMetaEntity.sales_monthly',
          'PartnerAccountMetaEntity.business_credit_history',
          'PartnerAccountMetaEntity.b_state',
          'total_statements',
          'PartnerAccountEntity.created',
          'PartnerAccountEntity.updated',
        ];
        foreach ($data['sorting'] as $field => $items) {
          if (in_array($field, $whitelist)) {
            $direction = (strtolower($items[0]) === 'asc') ? 'asc' : 'desc';
            $sort = [$field => $direction];
            break;
          }
        }
        if ($sort) {
          $accounts->order($sort);
        } else {
          $accounts->order(['PartnerAccountEntity.created' => 'DESC']);
        }
      } else {
        $accounts->order(['PartnerAccountEntity.created' => 'DESC']);
      }
      $total = $accounts->count();
      $accounts->limit($pagesize)
                ->offset(($page-1) * $pagesize);
    
      $result = ['success' => true, 'total' => $total, 'accounts' => $accounts];
      return $this->setJsonResponse($result);
    } catch(\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success'=>false, 'message'=>$e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  public function view($account_ref) {
    try {
      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }

      $hashids = new Hashids('partner_accounts', 7);
      $account_id = $hashids->decode($account_ref)[0];

      $account = TableRegistry::getTableLocator()->get('PartnerAccountEntity', [
        'connection' => ConnectionManager::get('reader_db')
        ])->get($account_id, [
        'contain' => [
          'PartnerAccountLinkPeopleEntity.PartnerAccountPeopleEntity',
          'PartnerAccountLinkPeopleEntity.PartnerAccountPeopleEntity.LeadAssetsEntity',
              'PartnerAccountLinkPeopleEntity.PartnerAccountPeopleEntity.LeadAssetsEntity.ConfigAssetTypeEntity',
              'PartnerAccountLinkPeopleEntity.PartnerAccountPeopleEntity.LeadLiabilitiesEntity',
              'PartnerAccountLinkPeopleEntity.PartnerAccountPeopleEntity.LeadLiabilitiesEntity.ConfigLiabilityEntity',
              'PartnerAccountLinkPeopleEntity.PartnerAccountPeopleEntity.LeadOwnerFinancesEntity',
              'PartnerAccountLinkPeopleEntity.PartnerAccountPeopleEntity.LeadOwnerAddressesEntity',
              'PartnerAccountLinkPeopleEntity.PartnerAccountPeopleEntity.LeadOwnerEmploymentEntity',
              'PartnerAccountLinkPeopleEntity.PartnerAccountPeopleEntity.PartnerAccountPeopleCurrentAddress',
              'PartnerAccountMetaEntity',
              'PartnerAccountMetaEntity.ConfigIndustryEntity',
              'LeadEntity',
              'LeadEntity.PocOwner',
              'LeadEntity.LendStatusEntity',
              'LeadEntity.CurrentLenderStatus',
              'LeadEntity.CurrentLenderStatus.LenderProductEntity',
              'LeadEntity.CurrentLenderStatus.LenderProductEntity.LenderEntity',
              'LeadAssetsEntity',
              'LeadAssetsEntity.ConfigAssetTypeEntity',
              'LeadLiabilitiesEntity',
              'LeadLiabilitiesEntity.ConfigLiabilityEntity',
              'TradingAddresses',
              'MailingAddress',
              'RegisteredAddress',
              'LeadAbnLookupEntity',
              'LeadReferenceEntity',
              'PartnerAccountUploadsEntity',
              'PartnerAccountUploadsEntity.PartnerAccountUploadsMetaEntity',
              'LeadUploadsEntity',
              'EntityTrustEntity',
              'PartnerAccountUploadsRequestedEntity',
              'PartnerEntity' => ['fields' => ['send_type', 'call_me_first']],
              'LeadNotesEntity',
          ],
      ]);

      if (empty($user)) {
        $ref = $this->checkAutoLoginCode($this->request->query('code'));
        if (empty($ref['ref'])) {
          throw new \Exception("You don't have permission to see the account.");
        }
      } else {
        if($user['account_type'] == 'Applicant' && $user['lead_id']){
          $permission_check = $this->checkPermission($user['lead_id'], null, true);
          if (!$permission_check['success']) {
            throw new \Exception($permission_check['message'], $permission_check['code'] ?? 0);
          }
        }else if ((!$user['access_all_leads'] && (int)$account->partner_user_id !== (int)$user['partner_user_id']) 
        || ($user['access_all_leads'] && (int)$account->partner_id !== (int)$user['partner_id'])) {
          throw new \Exception("You don't have permission to see the account.");
        }
      }

      $account->bs_analysis = $this->_getBsa($account_id);
      $account->bs_accounts = $this->_getBs($account_id);
      $account->bs_customer = $this->_getBsCustomer($account_id);

      $leads = [];
      foreach($account->leads as $lead) {
        if ($lead->partner_id !== Configure::read('Lend.LEND_TRASHED_LEAD_PARTNER_ID')) {
          $leads[] = $lead;
        }
      }

      $account->leads = $leads;

      $this->loadModel('LeadUploads');
      $accountUploads = $this->LeadUploads->readRecord(['partner_account_id' => $account_id, 'type' => "bs", 'name LIKE' => "%.pdf"], ["created" => "DESC"], 1);
      $account->illion_pdfs = $accountUploads;

      foreach($account->partner_account_uploads as $key => $value){
        $account->partner_account_uploads[$key]['owner_name'] = $account['partner_account_meta']['organisation_name'];        
      }

      // remove inactive people:
      if (!empty($account->partner_account_link_people)) {
        $account->partner_account_link_people = array_values(array_filter($account->partner_account_link_people, function($person) {
          return !empty($person->partner_account_people);
        }));
      }

      $result = ['success' => true, 'account' => $account];
      return $this->setJsonResponse($result);
    } catch(\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success'=>false, 'message'=>$e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  /**
   * Get all leads associated with an account
   * @param mixed $account_ref
   * @throws \Exception
   * @return void
   */
  public function leads($account_ref){
    try {
      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }

        $accountId = (new LendInternalAuth)->unhashPartnerAccountId($account_ref);
        if (empty($accountId)) {
          throw new \Exception("Can't find an account.");
        }

        $account = TableRegistry::getTableLocator()->get('PartnerAccountEntity', [
          'connection' => ConnectionManager::get('reader_db')
          ])->get($accountId, [
            'contain' => [
              'LeadEntity'=> ['fields' => ['lead_ref','created', 'lead_type', 'amount_requested', 'is_closed', 'is_archived', 'account_id']],
              'LeadEntity.PartnerProductTypeEntity'=> ['fields' => ['product_type_name']],
              'LeadEntity.PartnerEntity'=> ['fields' => ['status_system']],
              'LeadEntity.LendStatusEntity'=> ['fields' => ['status_name']],
              'LeadEntity.ManStatusEntity'=> ['fields' => ['status_name']],
              'LeadEntity.PocOwner'=> ['fields' => ['owner_id', 'first_name', 'last_name']],
              'LeadEntity.LeadAssociatedDataEntity'=> ['fields' => ['id', 'max_lender_id', 'lender_name']],
              'LeadEntity.LeadAssociatedDataEntity.LenderEntity'=> ['fields' => ['lender_logo']]
            ]
          ]
        );

        // Define a function to keep 'first_name', 'last_name', and 'full_name' in 'owner_poc'
        $cleanOwnerPoc = function ($lead) {
          unset($lead['account_id']);
          if (isset($lead['owner_poc']['first_name'])) {
            $ownerPoc = [
              // 'first_name' => $lead['owner_poc']['first_name'],
              // 'last_name' => $lead['owner_poc']['last_name'],
              'full_name' => $lead['owner_poc']['full_name'],
            ];
            $lead['owner_poc'] = $ownerPoc;
          }
          return $lead;
        };

        $result = ['success' => true, 'leads' => array_map($cleanOwnerPoc, $account['leads'])];
        return $this->setJsonResponse($result);
    } catch(\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success'=>false, 'message'=>$e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  public function getHeaderWidget($account_ref) {
    try {
      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }

      $accountId = (new LendInternalAuth)->unhashPartnerAccountId($account_ref);
      if (empty($accountId)) {
        throw new \Exception("Can't find an account.");
      }

      // Calculate all totals in a single query
      $partnerCommissionsTable = TableRegistry::getTableLocator()->get('PartnerCommissionEntity');
      $query = $partnerCommissionsTable->find();
      $result = $query
          ->select([
            'total_amount' => $query->newExpr('SUM(PartnerCommissionEntity.funded_amount)'),
            'total_settled' => $query->newExpr('COUNT(DISTINCT PartnerCommissionEntity.lead_id)'),
            'total_remuneration' => $query->newExpr('SUM(PartnerCommissionEntity.commission)')
          ])
          ->join([
              'table' => 'leads',
              'alias' => 'LeadEntity',
              'conditions' => [
                  'LeadEntity.account_id = ' . $accountId,
                  'PartnerCommissionEntity.lead_id = LeadEntity.lead_id'
              ]
          ])
          ->where([
              'PartnerCommissionEntity.funded_amount IS NOT' => null,
              'PartnerCommissionEntity.funded_amount >' => 0,
              'PartnerCommissionEntity.sale_outcome_id IS NOT' => null
          ])
          ->first();

      $totals = [
          "total_amount" => (float)$result->total_amount,
          "total_settled_loans" => (int)$result->total_settled,
          "total_remuneration" => (float)$result->total_remuneration
      ];

      return $this->setJsonResponse(['success' => true, 'header_widget' => $totals]);
    } catch(\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success'=>false, 'message'=>$e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }




  public function accountPeople() {
    try {
      if (!$this->request->is('post')) {
        throw new \Exception("Only POST request is allowed.");
      }
      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }
      $data = $this->request->getData();
      $accountRefs = $data['account_refs'];
      $accountIds = [];
      foreach ($accountRefs as $accountRef) {
        $hashids = new Hashids('partner_accounts', 7);
        $accountIds[] = $hashids->decode($accountRef)[0];
      }
  
      $people = TableRegistry::getTableLocator()->get('PartnerAccountPeopleEntity', [
        'connection' => ConnectionManager::get('reader_db')
        ])->find('all')
                  ->where(['partner_account_id IN' => $accountIds])
                  ->toArray();

      if (empty($people)) {
        return $this->setJsonResponse(['success' => false, 'message' => 'No account people found.']);
      } else {
        return $this->setJsonResponse(['success' => true, 'people' => $people]);
      }

    } catch(\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success'=>false, 'message'=>$e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
      
  }

  public function search($abn = null) {
    try {
      if (empty($abn)) {
        throw new \Exception("ABN is required.");
      }
      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }
      // TODO:: temporary skipping account level check for NZ
      if (strtolower(getenv('REGION', true)) === 'nz') {
        $account = [];
      } else {
        $account = TableRegistry::getTableLocator()->get('PartnerAccountEntity', [
          'connection' => ConnectionManager::get('reader_db')
          ])->find('all')
                    ->where(['partner_id' => $user['partner_id'], 'abn' => $abn])
                    ->first();
      }
      return $this->setJsonResponse(['success' => true, 'account' => $account]);
    } catch(\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }


  public function add() {
    try {
      if (!$this->request->is('post')) {
        throw new \Exception("Only POST request is allowed.");
      }
      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }
      $data = [];
      $data['abn_lookup'] = $this->request->getData();

      $accountTable = TableRegistry::getTableLocator()->get('PartnerAccountEntity');

      $data['partner_account_meta'] = $this->_mapFromAbnLookupToAccountMeta($data['abn_lookup']);
      $data['partner_id'] = $user['partner_id'];
      $data['partner_user_id'] = $user['partner_user_id'];
      $data['abn'] = $data['abn_lookup']['abn'];

      //Rewrite boolean value to number, Fix error: `Cannot convert value of type `boolean` to integer`
      $data['abn_lookup']['active'] = 1;
      $data['abn_lookup']['is_abn_current'] = $data['abn_lookup']['is_abn_current'] ? 1 : 0;
      

      $account = $accountTable->newEntity($data, [
        'associated' => ['PartnerAccountMetaEntity', 'LeadAbnLookupEntity']
      ]);
      $accountTable->save($account);

      return $this->setJsonResponse(['success' => true, 'account_ref' => $account->account_ref]);
    } catch(\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }


  public function updateAsset() {
    try {
      $assets_liabilities_pair = Configure::read('Lend.assets_liabilities_pair');

      if (!$this->request->is('post')) {
        throw new \Exception("Only POST request is allowed.");
      }
      $data = $this->request->getData();
      if (empty($data['account_ref'])) {
        throw new \Exception("Missed required field: account_ref");
      }

      if (!$this->checkPartnerAccountBelongsToUser($data['account_ref'])) {
        throw new \Exception("You don't have permission to update.");
      }

      $hashids = new Hashids('partner_accounts', 7);
      $data['partner_account_id'] = $hashids->decode($data['account_ref'])[0];

      $leadAssetTable = TableRegistry::getTableLocator()->get('LeadAssetsEntity');
      $liability = null;

      if (!empty($data['lead_asset_id'])) {
        $lead_asset = $leadAssetTable->get($data['lead_asset_id'], [
          'contain' => ['LeadLiabilitiesEntity', 'LeadLiabilitiesEntity.ConfigLiabilityEntity', 'ConfigAssetTypeEntity']
        ]);

        if (@$data['status'] == 'Deleted' && !empty($lead_asset->liability)) {
          $lead_asset->liability->status = 'Deleted';
          $data['liability']['status'] = 'Deleted';
          $liability = $lead_asset->liability;
        } elseif (!empty($data['finance_outstanding']) && empty($lead_asset->liability)) {
          $liability = $leadAssetTable->LeadLiabilitiesEntity->newEntity([
            'status' => 'Active',
            'partner_account_id' => $data['partner_account_id'],
            'liability_id' => $assets_liabilities_pair[$lead_asset->asset_type_id]
          ]);
          $lead_asset->liability = $liability;
        } elseif (@$data['finance_outstanding'] === false && !empty($lead_asset->liability)) {
          $lead_asset->liability->status = 'Deleted';
          $data['liability']['status'] = 'Deleted';
          $liability = $lead_asset->liability;
        }

        $leadAssetTable->patchEntity($lead_asset, $data, [
          'associated' => ['LeadLiabilitiesEntity']
        ]);
      } else {
        $data['status'] = 'Active';
        $lead_asset = $leadAssetTable->newEntity($data);
      }

      $leadAssetTable->save($lead_asset);

      if (!empty($lead_asset->liability)) {
        unset($lead_asset->liability);
      }
      if (!empty($liability)) {
        $liability->liability = TableRegistry::getTableLocator()->get('ConfigLiabilityEntity')->get($liability->liability_id);
      }
      return $this->setJsonResponse(['success' => true, 'asset' => $lead_asset, 'liability' => $liability]);
    } catch(\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }



  public function addAccountNotes()
  {
    try {
      $account_ref = $this->request->getData('account_ref');
      if (empty( $account_ref)) {
        throw new \Exception("Missed required field: account_ref");
      }
      $hashids = new Hashids('partner_accounts', 7);
      $params['partner_account_id'] = $hashids->decode($account_ref)[0];

      $lead_notes_table = TableRegistry::getTableLocator()->get('LeadNotesEntity');

      $params['notes'] = $this->request->getData('notes');
      $params['created'] = date('Y-m-d H:i:s');
      $params['is_pinned'] = $this->request->getData('is_pinned') ?? false;

      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }
      $params['partner_user_id'] = $user['partner_user_id'];

      $lead_note = $lead_notes_table->newEntity($params);
      $lead_notes_table->save($lead_note);

      return $this->setJsonResponse(["success" => true, "message" => "added lead notes"]);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(["success" => false, "message" => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  public function deleteAccountNote()
  {
    try {
      $data = $this->request->getData();
      $note_id = $data['note_id'];
  
      $leadNotesTable = TableRegistry::getTableLocator()->get('LeadNotesEntity');
      $note = $leadNotesTable->get($note_id);
      $note->status = 'Deleted';
      if ($leadNotesTable->save($note)) {
        return $this->setJsonResponse(['success' => true]);
      } else {
        throw new \Exception("Error updating note status");
      }
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }


  public function editAccountNotes()
  {
    try {
      $data = $this->request->getData();
      $note_id = $data['note_id'];
      $leadNotesTable = TableRegistry::getTableLocator()->get('LeadNotesEntity');
      $note = $leadNotesTable->get($note_id);
      $note->status = 'Edited';
      if ($leadNotesTable->save($note)) {
        return $this->setJsonResponse(['success' => true]);
      } else {
        throw new \Exception("Error updating note status");
      }
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  public function togglePinAccountNotes()
  {
      try {
          $data      = $this->request->getData();
          $note_id   = $data['note_id'];
          $is_pinned = $data['is_pinned']; 
          $leadNotesTable = TableRegistry::getTableLocator()->get('LeadNotesEntity');
          $note = $leadNotesTable->get($note_id);
          $note->is_pinned = $is_pinned;

          if ($leadNotesTable->save($note)) {
              return $this->setJsonResponse(['success' => true], 200);
          } else {
              throw new \Exception("Error updating note status");
          }
      } catch (\Exception $e) {
          Log::error($e->getMessage());
          Log::error($e->getTraceAsString());
          return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
      }
  }

  public function updateLiability() {
    try {
      if (!$this->request->is('post')) {
        throw new \Exception("Only POST request is allowed.");
      }
      $data = $this->request->getData();
      if (empty($data['account_ref'])) {
        throw new \Exception("Missed required field: account_ref");
      }

      if (!$this->checkPartnerAccountBelongsToUser($data['account_ref'])) {
        throw new \Exception("You don't have permission to update.");
      }

      $hashids = new Hashids('partner_accounts', 7);
      $data['partner_account_id'] = $hashids->decode($data['account_ref'])[0];

      $leadLiabilityTable = TableRegistry::getTableLocator()->get('LeadLiabilitiesEntity');

      if (!empty($data['lead_liability_id'])) {
        $lead_liability = $leadLiabilityTable->get($data['lead_liability_id'], [
          'contain' => ['ConfigLiabilityEntity']
        ]);
        $leadLiabilityTable->patchEntity($lead_liability, $data);
      } else {
        $data['status'] = 'Active';
        $lead_liability = $leadLiabilityTable->newEntity($data);
      }

      $leadLiabilityTable->save($lead_liability);
      if (empty($lead_liability->liability)) {
        $lead_liability->liability = TableRegistry::getTableLocator()->get('ConfigLiabilityEntity')->get($lead_liability->liability_id);
      }

      return $this->setJsonResponse(['success' => true, 'liability' => $lead_liability]);
    } catch(\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  /**
   * Create/Update/Delete Assets and Liabilities for an account
   * @throws \Exception
   * @return void
   */
  public function updateAssetAndLiability()
  {
    try {
      if (!$this->request->is('post')) {
        throw new \Exception("Only POST request is allowed.");
      }
      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }
      $schema = [
        'account_ref' => null,
        'assets' => null,
        'liabilities' => null,
        'has_liabilities' => null,
      ];
      $asset_liability_data = array_intersect_key($this->request->getData(), $schema);
      if (empty($asset_liability_data['account_ref'])) {
        throw new \Exception("Missed required field: account_ref");
      }
      $hashids = new Hashids('partner_accounts', 7);
      $asset_liability_data['partner_account_id'] = $hashids->decode($asset_liability_data['account_ref'])[0];
      
      $partner_account_table = TableRegistry::getTableLocator()->get('PartnerAccountEntity');
      $partner_account_data = $partner_account_table->get($asset_liability_data['partner_account_id'], [
        'contain' => [
          'LeadAssetsEntity',
          'LeadAssetsEntity.ConAssetShareEntity',
          'LeadLiabilitiesEntity',
          'LeadLiabilitiesEntity.LeadAssetsEntity',
          'LeadLiabilitiesEntity.ConLiabilityShareEntity'
        ]
      ]);

      if ((int)$user['partner_id'] !== (int)$partner_account_data->partner_id) {
        throw new \Exception("You don't have permission to update.");
      }

      $ato_liability = []; // this is ID = 1
      if($asset_liability_data['liabilities']){
        foreach ($asset_liability_data['liabilities'] as $key => $liability) {
          if ($liability['liability_id'] == 1) {
            $ato_liability = $liability;
          }
          TableRegistry::getTableLocator()->get('ConLiabilityShareEntity')->deleteAll(['lead_liability_id' => $liability['lead_liability_id']]);
          if(!empty($liability['asset_fi_commenced'])){
            $asset_liability_data['liabilities'][$key]['asset_fi_commenced'] = date("Y-m-d H:i:s", strtotime($liability['asset_fi_commenced']));
          }
        }
      }

      // Create assets and update the fake ids that is used in liabilities
      $assetTable = TableRegistry::getTableLocator()->get('LeadAssetsEntity');
      $existAssets = [];
      foreach (($partner_account_data->assets ?? []) as $existAsset) {
        $existAssets[] = $existAsset->lead_asset_id;
      }

      $asset_liability_data['assets'] = $asset_liability_data['assets'] ?? [];
      $asset_liability_data['liabilities'] = $asset_liability_data['liabilities'] ?? [];

      foreach ($asset_liability_data['assets'] as &$asset) {
        $asset['partner_account_id'] = $asset_liability_data['partner_account_id'];
        $tempId = $asset['lead_asset_id'];
        $tempIndex = array_search($tempId, $existAssets);
        
        if ($tempIndex !== false) {
          TableRegistry::getTableLocator()->get('ConAssetShareEntity')->deleteAll(['lead_asset_id' => $asset['lead_asset_id']]);
          $assetEntity = $assetTable->patchEntity($partner_account_data->assets[$tempIndex], $asset);
        } else {
          // New Asset
          $asset['lead_asset_id'] = null;
          $assetEntity = $assetTable->newEntity($asset);
        }
        if ($assetTable->save($assetEntity) && $tempId != $assetEntity->lead_asset_id) {
          $asset['lead_asset_id'] = $assetEntity->lead_asset_id;
          foreach ($asset_liability_data['liabilities'] as &$liability) {
            if ($liability['lead_asset_id'] == $tempId) {
              $liability['lead_asset_id'] = $assetEntity->lead_asset_id;
            }
          }
        }
      }

      $partner_account_table->patchEntity($partner_account_data, $asset_liability_data, [
        'associated' => [
          'LeadLiabilitiesEntity',
          'LeadLiabilitiesEntity.LeadAssetsEntity',
          'LeadLiabilitiesEntity.ConLiabilityShareEntity'
        ]
      ]);
      $partner_account_table->save($partner_account_data);

      if($partner_account_data->account_type == 'company'){
        $partner_account_meta_update = ['monthly_repayment'=>$ato_liability['repayment_pm'], 'tax_overdue'=>$ato_liability['loan_balance']];
        $table = TableRegistry::getTableLocator()->get('PartnerAccountMetaEntity');
        $partner_account_meta_entity = $table->find()
            ->where(['partner_account_id' => $asset_liability_data['partner_account_id']])
            ->first();
        if ($partner_account_meta_entity) {
          $partner_account_meta_entity = $table->patchEntity($partner_account_meta_entity, $partner_account_meta_update);
          $table->save($partner_account_meta_entity);
        }

        $partner_account_data_sync = ['tax_monthly_repayment'=>$ato_liability['repayment_pm'], 
                            'tax_outstanding'=>true , 
                            'tax_overdue'=>$ato_liability['loan_balance'],
                            'tax_outstanding_arrangement'=>true,
                          ];

        if($partner_account_data){
          $partner_account_data_entity = $partner_account_table->patchEntity($partner_account_data, $partner_account_data_sync);
          $partner_account_table->save($partner_account_data_entity);
        }
      }

      $partner_account_data_array = $partner_account_data->toArray();
      return $this->setJsonResponse(array_intersect_key($partner_account_data_array, $schema));
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }




  public function updateBusinessDetails() {
    try {
      if (!$this->request->is('post')) {
        throw new \Exception("Only POST request is allowed.");
      }
      $data = $this->request->getData();
      if (empty($data['account_ref'])) {
        throw new \Exception("Missed required field: account_ref");
      }
      $hashids = new Hashids('partner_accounts', 7);
      $data['partner_account_id'] = $hashids->decode($data['account_ref'])[0];

      $partnerAccountTable = TableRegistry::getTableLocator()->get('PartnerAccountEntity');
      $partner_account = $partnerAccountTable->get($data['partner_account_id'], [
        'contain' => ['PartnerAccountMetaEntity', 'LeadAbnLookupEntity']
      ]);

      if (!$this->checkPartnerAccountBelongsToUser($data['account_ref'], null, $partner_account->partner_id)) {
        throw new \Exception("You don't have permission to update.");
      }

      if (!empty($data['abn_lookup']) && empty($data['abn_lookup']['abn_id'])) {
        $data['abn'] = $data['abn_lookup']['abn'];
        $partner_account->abn_lookup = $partnerAccountTable->LeadAbnLookupEntity->newEntity($data['abn_lookup']);
      }
      if (!empty($data['abn_lookup']) && empty($data['abn_lookup']['active'])) {
        $data['abn_lookup']['active'] = true;
      }

      $account_meta = $this->_mapFromAbnLookupToAccountMeta($data['abn_lookup']);
      if (!empty($account_meta)) {
        $data['partner_account_meta'] = array_merge($account_meta, $data['partner_account_meta']);
      }

      $partnerAccountTable->patchEntity($partner_account, $data, [
        'associated' => ['PartnerAccountMetaEntity', 'LeadAbnLookupEntity']
      ]);
      $partnerAccountTable->save($partner_account);

      return $this->setJsonResponse(['success' => true, 'account' => $partner_account]);
    } catch(\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  private function _mapFromAbnLookupToAccountMeta($abn_lookup) {
    $mapping = [
      'acn' => 'acn',
      'business_name' => 'business_name',
      'organisation_name' => 'organisation_name',
      'effective_from' => 'registration_date',
      'entity_type_desc' => 'business_type_abn'
    ];
    $meta = [];
    foreach ($mapping as $abn_field => $meta_field) {
      if (!empty($abn_lookup[$abn_field])) {
        $meta[$meta_field] = $abn_lookup[$abn_field];
      }
    }
    return $meta;
  }


  public function deleteAddress() {
    try {
      if (!$this->request->is('post')) {
        throw new \Exception("Only POST request is allowed.");
      }

      $data = $this->request->getData();
      if (empty($data['lead_address_id'])) {
        throw new \Exception("Missed required field: address_id");
      }

      $addressTable = TableRegistry::getTableLocator()->get('LeadAddressEntity');
      $address = $addressTable->get($data['lead_address_id'], [
        'contain' => ['PartnerAccountEntity']
      ]);

      if (!$this->checkPartnerAccountBelongsToUser(null, null, $address->partner_account->partner_id)) {
        throw new \Exception("You don't have permission to delete this address.");
      }

      $address->status = 'deleted';
      $addressTable->save($address);
      $this->readjustAddressEmploymentDurations('LeadAddressEntity', 'partner_account_id', $address->partner_account_id);

      return $this->setJsonResponse(['success' => true]);
    } catch(\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  public function updateAddresses() 
  {
    try {
      if (!$this->request->is('post')) {
        throw new \Exception("Only POST request is allowed.");
      }
      $data = $this->request->getData();
      if (empty($data['account_ref'])) {
        throw new \Exception("Missed required field: account_ref");
      }
      $hashids = new Hashids('partner_accounts', 7);
      $data['partner_account_id'] = $hashids->decode($data['account_ref'])[0];

      $partnerAccountTable = TableRegistry::getTableLocator()->get('PartnerAccountEntity');
      $partner_account = $partnerAccountTable->get($data['partner_account_id'], [
        'contain' => ['TradingAddresses', 'MailingAddress', 'RegisteredAddress']
      ]);

      if (!$this->checkPartnerAccountBelongsToUser($data['account_ref'], null, $partner_account->partner_id)) {
        throw new \Exception("You don't have permission to update.");
      }

      foreach ($data['trading_addresses'] as &$address) {
        if (empty($address['status'])) {
          $address['status'] = 'active';
        }
      }
      if (!empty($data['mailing_address']) && empty($data['mailing_address']['status'])) {
        $data['mailing_address']['status'] = 'active';
      }
      if (!empty($data['registered_address']) && empty($data['registered_address']['status'])) {
        $data['registered_address']['status'] = 'active';
      }

      $data['registered_address'] = $this->AddressValidator->processAddress($data['registered_address']);
      $data['mailing_address']    = $this->AddressValidator->processAddress($data['mailing_address']);
    
      $toDate = null;
      foreach($data['trading_addresses'] as &$address){//most recent(current) is first
        $fromDate = $this->getDurationBasedFromDate($address['duration_years'], $address['duration_months'], $toDate);
        $address['date_from'] = $fromDate;
        $address['date_to'] = $toDate;
        $toDate = $fromDate;//from is to for next record
      }

      foreach($data['trading_addresses'] as $key=>$trading_address)
      {
        $data['trading_addresses'][$key] = $this->AddressValidator->processAddress($trading_address);
      }

      $partnerAccountTable->patchEntity($partner_account, $data, [
        'associated' => ['TradingAddresses', 'MailingAddress', 'RegisteredAddress']
      ]);
      $partnerAccountTable->save($partner_account);

      return $this->setJsonResponse(['success' => true, 'account' => $partner_account]);
    } catch(\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false,'error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }


  public function updateFinances() {
    try {
      if (!$this->request->is('post')) {
        throw new \Exception("Only POST request is allowed.");
      }
      $data = $this->request->getData();
      if (empty($data['account_ref'])) {
        throw new \Exception("Missed required field: account_ref");
      }
      $hashids = new Hashids('partner_accounts', 7);
      $data['partner_account_id'] = $hashids->decode($data['account_ref'])[0];

      $partnerAccountTable = TableRegistry::getTableLocator()->get('PartnerAccountEntity');
      $partner_account = $partnerAccountTable->get($data['partner_account_id'], [
        'contain' => ['PartnerAccountMetaEntity']
      ]);

      if (!$this->checkPartnerAccountBelongsToUser($data['account_ref'], null, $partner_account->partner_id)) {
        throw new \Exception("You don't have permission to update.");
      }

      $partnerAccountTable->patchEntity($partner_account, $data, [
        'associated' => ['PartnerAccountMetaEntity']
      ]);
      $partnerAccountTable->save($partner_account);

      $lead_data = TableRegistry::getTableLocator()->get('LeadEntity')->find()
      ->where(['account_id' => $data['partner_account_id']])
      ->order(['lead_id' => 'DESC'])
      ->first();

      $ato_liability = [];
      if ($lead_data->lead_type == 'commercial') {
        $ato_liability = [
            'liability_id' => 1,
            'lead_id' => $lead_data->lead_id,
            'status' => 'Active'
        ];
    
        if (isset($data['partner_account_meta']['monthly_repayment']) || isset($data['partner_account_meta']['tax_overdue'])) {
            $ato_liability['repayment_pm'] = $data['partner_account_meta']['monthly_repayment'];
            $ato_liability['loan_balance'] = $data['partner_account_meta']['tax_overdue'];
            $lead_liability_table = TableRegistry::getTableLocator()->get('LeadLiabilitiesEntity');
            $lead_liability_table_entity = $lead_liability_table->find()
                ->where(['lead_id' => $lead_data->lead_id, 'status' => 'Active', 'liability_id' => 1])
                ->first();

    
            if ($lead_liability_table_entity) {
              $lead_liability_table_entity = $lead_liability_table->patchEntity($lead_liability_table_entity, $ato_liability);
              if (!$lead_liability_table->save($lead_liability_table_entity)) {
                Log::write('debug','udpate error message');
              }
            } else {
              $lead_liability_table_entity = $lead_liability_table->newEntity($ato_liability);
              if (!$lead_liability_table->save($lead_liability_table_entity)) {
                Log::write('debug','insert error message');
              }
            }
        }
    }
    
      return $this->setJsonResponse(['success' => true, 'account' => $partner_account]);
    } catch(\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }


  public function updateReferences() {
    try {
      if (!$this->request->is('post')) {
        throw new \Exception("Only POST request is allowed.");
      }

      $data = $this->request->getData();

      if (empty($data['account_ref'])) {
        throw new \Exception("Missed required field: account_ref");
      }
      $hashids = new Hashids('partner_accounts', 7);
      $data['partner_account_id'] = $hashids->decode($data['account_ref'])[0];
      if (!empty($data['references'][0])) {
        $data['references'][0]['status'] = 'active';
      }

      $partnerAccountTable = TableRegistry::getTableLocator()->get('PartnerAccountEntity');
      $partner_account = $partnerAccountTable->get($data['partner_account_id'], [
        'contain' => ['LeadReferenceEntity']
      ]);

      if (!$this->checkPartnerAccountBelongsToUser($data['account_ref'], null, $partner_account->partner_id)) {
        throw new \Exception("You don't have permission to update.");
      }

      $partnerAccountTable->patchEntity($partner_account, $data, [
        'associated' => ['LeadReferenceEntity']
      ]);
      $partnerAccountTable->save($partner_account);

      return $this->setJsonResponse(['success' => true, 'account' => $partner_account]);
    } catch(\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }


  public function deleteReference() {
    try {
      if (!$this->request->is('post')) {
        throw new \Exception("Only POST request is allowed.");
      }
      $data = $this->request->getData();
      if (empty($data['lead_reference_id'])) {
        throw new \Exception("Missed required field: reference_id");
      }

      $referenceTable = TableRegistry::getTableLocator()->get('LeadReferenceEntity');
      $reference = $referenceTable->get($data['lead_reference_id'], [
        'contain' => ['PartnerAccountEntity']
      ]);

      if (!$this->checkPartnerAccountBelongsToUser(null, null, $reference->partner_account->partner_id)) {
        throw new \Exception("You don't have permission to delete this reference.");
      }
      
      $reference->status = 'inactive';
      $referenceTable->save($reference);

      return $this->setJsonResponse(['success' => true]);
    } catch(\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }


  public function updateAccountUploads() {
    try {
      if ($this->request->is('options')) {
        die("ok");
      }
      if (!$this->request->is('post')) {
        throw new \Exception("Only POST and OPTIONS request is allowed.");
      }  
      $data = $this->request->getData();
      if (empty($data['account_ref'])) {
        throw new \Exception("Missed required field: account_ref");
      }
      $hashids = new Hashids('partner_accounts', 7);
      $data['partner_account_id'] = $hashids->decode($data['account_ref'])[0];

      $partnerAccountTable = TableRegistry::getTableLocator()->get('PartnerAccountEntity');
      $partner_account = $partnerAccountTable->get($data['partner_account_id']);

      if (!$this->checkPartnerAccountBelongsToUser(null, null, $partner_account->partner_id, true)) {
        throw new \Exception("You don't have permission to see the account.");
      }

      $other_doc_name = [];
      foreach ($data['partner_account_uploads']['partner_account_uploads_meta'] as $key => $meta) {
        if ($meta['field_name'] === 'other_doc_name') {
            $other_doc_name['other_doc_name'] = $meta['value'];
            unset($data['partner_account_uploads']['partner_account_uploads_meta'][$key]);
        }
        if ($meta['field_name'] === 'specified' && $meta['value'] == 'Other') {
            $other_doc_name['partner_account_upload_meta_id'] =  $meta['partner_account_upload_meta_id'];
         }
      }

      $partnerAccountUploadsTable = TableRegistry::getTableLocator()->get('PartnerAccountUploadsEntity');
      $partnerAccountUploadsMetaTable = TableRegistry::getTableLocator()->get('PartnerAccountUploadsMetaEntity');

      if (!empty($data['partner_account_uploads']['partner_account_upload_id'])) {
        $partner_account_uploads = $partnerAccountUploadsTable->get($data['partner_account_uploads']['partner_account_upload_id'], [
          'contain' => ['PartnerAccountUploadsMetaEntity']
        ]);
        $partnerAccountUploadsTable->patchEntity($partner_account_uploads, $data['partner_account_uploads'], [
          'associated' => ['PartnerAccountUploadsMetaEntity']
        ]);
      } else {
        if (getenv('LEND_ENV') != 2 && $data['partner_account_uploads']['full_path']) {
            $path = $data['partner_account_uploads']['full_path'];
            if (strpos($path, 'DevTeam/') !== 0) {
                $path = 'DevTeam/' . $path;
            }
            $data['partner_account_uploads']['full_path'] = $path;
        }
        $data['partner_account_uploads']['status'] = 'Active';
        $data['partner_account_uploads']['include_for_lenders'] = 1;
        $data['partner_account_uploads']['partner_account_id'] = $data['partner_account_id'];
        $partner_account_uploads = $partnerAccountUploadsTable->newEntity($data['partner_account_uploads'], [
          'associated' => ['PartnerAccountUploadsMetaEntity']
        ]);
      }

      $partnerAccountUploadsTable->save($partner_account_uploads);

      if($other_doc_name['other_doc_name']){
        $metaEntity = $partnerAccountUploadsMetaTable->get($other_doc_name['partner_account_upload_meta_id']);
        $metaEntity = $partnerAccountUploadsMetaTable->patchEntity($metaEntity, $other_doc_name);
        $partnerAccountUploadsMetaTable->save($metaEntity);
      }          
   
      

    return $this->setJsonResponse(['success' => true, 'partner_account_uploads' => $data['partner_account_uploads']]);
    } catch(\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }


  public function updateUploadsRequested() {
    try {
      if (!$this->request->is('post')) {
        throw new \Exception("Only POST request is allowed.");
      }
            
      $data = $this->request->getData();
      if (empty($data['account_ref'])) {
        throw new \Exception("Missed required field: account_ref");
      }
      $hashids = new Hashids('partner_accounts', 7);
      $data['partner_account_id'] = $hashids->decode($data['account_ref'])[0];

      $partnerAccountTable = TableRegistry::getTableLocator()->get('PartnerAccountEntity');
      $partner_account = $partnerAccountTable->get($data['partner_account_id'], [
        'contain' => ['PartnerAccountMetaEntity']
      ]);

      if (!$this->checkPartnerAccountBelongsToUser(null, null, $partner_account->partner_id, true)) {
        throw new \Exception("You don't have permission to see the account.");
      }

      $partnerAccountUploadsRequestedTable = TableRegistry::getTableLocator()->get('PartnerAccountUploadsRequestedEntity');

      foreach($data['partner_account_uploads_requested'] as $key => $item) {
        if (!empty($data['partner_account_uploads_requested'][$key]['partner_account_uploads_requested_id'])) {
          $partner_account_uploads_requested = $partnerAccountUploadsRequestedTable->get($data['partner_account_uploads_requested'][$key]['partner_account_uploads_requested_id']);
          $partnerAccountUploadsRequestedTable->patchEntity($partner_account_uploads_requested, $data['partner_account_uploads_requested'][$key]);
        } else {
          $data['partner_account_uploads_requested'][$key]['partner_account_id'] = $data['partner_account_id'];
          $data['partner_account_uploads_requested'][$key]['status'] = true;
          $partner_account_uploads_requested = $partnerAccountUploadsRequestedTable->newEntity($data['partner_account_uploads_requested'][$key]);  
        }
        $partnerAccountUploadsRequestedTable->save($partner_account_uploads_requested);
      }

      $sendEmail = null;
      if(!empty($data['partner_account_uploads_requested'])){
        foreach($data['partner_account_uploads_requested'] as $key => $item) {
          if ($data['partner_account_uploads_requested'][$key]['status'] == true) {
            $sendEmail = true;
          }
        }

        if ($sendEmail) {
          $notification_data = [
            'accountIds' => array_unique(array_column($data['partner_account_uploads_requested'], 'partner_account_id')),
            'accountRef' => $data['account_ref'],
            'is_attach' => true,
            'org_name' => $partner_account->partner_account_meta->organisation_name,
            'message' => @$data['partner_account_uploads_requested'][0]['notes'],
          ];
          if(!empty($notification_data['accountIds'])){
            $this->sendAskApplicant($notification_data);
          }
        }
      }

      return $this->setJsonResponse(['success' => true, 'partner_account_uploads_requested' => $data['partner_account_uploads_requested']]);
    } catch(\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }


  public function sendAskApplicant($manually_data = false) {
    /**
     * The email would contain the list of requested file which were not previously attached
     * If the privacy form is selected and was not attached previously, in the email there should be a link to download the privacy form
     */

     $user = $this->Auth->user();
     if (empty($user)) {
       $user = $this->Auth->identify();
     }

    $partnerId = (int)$user['partner_id'];
    $partnerUserId = (int)$user['partner_user_id'];
    $accountType = $user['account_type'];
    $requestedFiles = "";
    $partnerEmail = (empty($user['email'])) ? '' : $user['email'];

    if ((!empty($partnerId) && $partnerId === 2) || ($accountType !== 'Lend Staff' && empty($partnerId)) || empty($partnerUserId)){  //demo account or un-authorized
      if(empty($manually_data))
        return $this->setJsonResponse(array('success'=>false));
      else
        return false;
    }

    if(empty($manually_data))
      $data = $this->request->getData();
    else
      $data = $manually_data;

    $owners = $this->_decideEmailCode($data);

    $emailed = []; //prevent double email if owner id's share the same one

    foreach($owners as $peopleId => $task) {
        $applicant_note = !empty($data['message']) ?  $data['message'] : ''; //needs to be inside loop - Being mutated by getRequestedFiles below
        $applicant = (new LendInternalAuth)->hashLeadId($peopleId);

        $code = $task['code'];

        if($code == 'AppliAskAttach') {
          $partnerAccountLinkPeopleTable = TableRegistry::getTableLocator()->get('PartnerAccountLinkPeopleEntity');
          $partner_account_link_people = $partnerAccountLinkPeopleTable->find('all')
            ->contain(['PartnerAccountPeopleEntity'])
            ->where(['PartnerAccountLinkPeopleEntity.account_id' => $peopleId])
            ->where(['PartnerAccountLinkPeopleEntity.is_main_point_of_contact' => 1])
            ->toArray();
          $partner_account_people = json_decode(json_encode($partner_account_link_people['partner_account_people']), true)[0];
          $partner_account_people['is_main_point_of_contact'] = true;
          $to = $partner_account_people['email'];
        } else {
          $partnerAccountPeopleTable = TableRegistry::getTableLocator()->get('PartnerAccountPeopleEntity');
          $partner_account_people =  $partnerAccountPeopleTable->get($peopleId)->toArray();
          $to = $partner_account_people['email'];  
        }

        if(in_array($to,$emailed)) continue;
        $emailed[] = $to;

        if($code == 'AppliAskAttach') {
          $requestedFiles = $this->_getRequestedFiles($peopleId);
          $upload_login_code = $this->createAutoLoginCode('Accounts', 'Documents', $data['accountRef'], $partnerUserId);
          if(!$requestedFiles) continue; // this specific user has no files to request
        }

        if($applicant_note) $applicant_note =  '' . $applicant_note . "\r\n";
        $additionalCode = [];
        $additionalCode['applicant'] = $applicant;
        if(!empty($partner_account_people['first_name'])) $additionalCode['first_name'] = $partner_account_people['first_name'];
        if(!empty($partner_account_people['last_name'])) $additionalCode['last_name'] = $partner_account_people['last_name'];
        $additionalCode = http_build_query($additionalCode);

        if($code == 'AppBSRequest'){
          $additionalCode = false;
          $clientLoginCode = getenv('DOMAIN_BS_SPA')."?a=".$data['account_ref']."&signature=".CurlHelper::generateSignature(['a' => $data['account_ref']], getenv('BANKFEEDS_SECRET'));
        }

        // http_build_query
        $adhocData = [
          'percent_complete'      => @$data['percent_complete'][$peopleId],
          'to'                    => [$to],
          'additionalCode'        => @$additionalCode,
          'applicant_note'        => $applicant_note,
          'bs_spa_link'           => @$clientLoginCode,
          'client_first_name'     => @$partner_account_people['first_name'],
          'client_last_name'      => @$partner_account_people['last_name'],
          'requested_files'       => @$requestedFiles,
          'ask_applicant_tasks'   => @$task['tasks'],
          'partner_email'         => $partnerEmail,
          'upload_login_code'     => @$upload_login_code,
          'responder'             => @$data['org_name'],
        ];

        TableRegistry::getTableLocator()->get('PartnerNotifications')->sendPartnerNotifications($user['partner_id'], $code, false, $adhocData);
    }

    if(empty($manually_data))
      return $this->setJsonResponse(array('success'=>true));
    else
      return true;
  }


  public function getS3SignedUploadRequest() {//'code' will be in the post payload, check it 
    try {
      $data = $this->request->getData(); 
      
      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }
      if($data['code']){
        $code = $data['code'];
      }else{
        $code = $this->request->query('code');
      }
      if (empty($user)) {
        if($code){
          $ref = $this->checkAutoLoginCode($code);
        }
        if (!$ref || empty($ref['ref'])) {
          throw new \Exception("You don't have permission to see the account.");
        }
      }
      unset($data['code']);
      $s3_response = $this->_uploadDocRequest('Accounts', $data);

      if ($s3_response !== false) {
        return $this->setJsonResponse(array('success'=>true, 'url'=>$s3_response));  
      } else {
        return $this->setJsonResponse(['success' => false]);
      }
    } catch(\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  public function getS3SignedUploadRequestGeneric() {
    try {
      $data = $this->request->getData();  
      $s3_response = $this->_uploadRequest($data);

      if ($s3_response !== false) {
        return $this->setJsonResponse(array('success'=>true, 'url'=>$s3_response));  
      } else {
        return $this->setJsonResponse(['success' => false]);
      }
    } catch(\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }


  public function viewImage($id, $code = false) { //add code and check similar to above
    try {
      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }
      if (empty($user)) {
        if(!empty($code)){
          $ref = $this->checkAutoLoginCode($code);
          if (empty($ref['ref'])) {
            throw new \Exception("You don't have permission to see the account.");
          }
        }
        else
          throw new \Exception("You don't have permission to see the account.");
      }

      $s3_url = $this->_getS3ViewImageRequest('Accounts', $id);

      if ($s3_url !== false) {
        return $this->setJsonResponse(array('success'=>true, 'url'=>$s3_url));  
      } else {
        return $this->setJsonResponse(['success' => false]);
      }
    } catch(\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }


  public function viewFile() {
    try {
      $passedArgs = $this->request->getParam('pass');
      $url = $this->_createSignedRequest(implode("/",$passedArgs), '2');
      $this->redirect($url);  
    } catch(\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }


  public function getDownloadedFiles() {
    try {
      $data = $this->request->getData();  

      $s3_url = $this->_getS3ZipFile($data);

      if ($s3_url !== false) {
        return $this->setJsonResponse(array('success'=>true, 'url'=>$s3_url));  
      } else {
        return $this->setJsonResponse(['success' => false]);
      }
    } catch(\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }


  private function _getRequestedFiles($accountId) {
    $partnerAccountUploadsRequestedTable = TableRegistry::getTableLocator()->get('PartnerAccountUploadsRequestedEntity');
    $partnerAccountUploadsMetaTable = TableRegistry::getTableLocator()->get('PartnerAccountUploadsMetaEntity');
    $partnerAccountUploadsTable = TableRegistry::getTableLocator()->get('PartnerAccountUploadsEntity');
    $requested = '';

    $partner_account_uploads_requested = $partnerAccountUploadsRequestedTable->find('all')
      ->where(['partner_account_id' => $accountId])
      ->where(['status' => true])
      ->toArray();
    $partner_account_uploads_requested = json_decode(json_encode($partner_account_uploads_requested), true);

    foreach($partner_account_uploads_requested as $file) {
      $partner_account_uploads = $partnerAccountUploadsTable->find('all')
        ->leftJoinWith('PartnerAccountUploadsMetaEntity')
        ->where(['partner_account_id' => $accountId])
        ->where(['PartnerAccountUploadsMetaEntity.value' => $file['specify']])
        ->toArray();
      $partner_account_uploads = json_decode(json_encode($partner_account_uploads), true);

      // Log::error($partner_account_uploads);

      $partner_account_uploads_meta = $partnerAccountUploadsMetaTable->find('all')
        ->where(['partner_account_upload_id' => $partner_account_uploads[0]['partner_account_upload_id']])
        ->toArray();
      
      $partner_account_uploads_meta =  json_decode(json_encode($partner_account_uploads_meta), true);

      // Log::error($partner_account_uploads_meta);

      if (!empty($partner_account_uploads_meta)) {
        $uploaded = false;
        foreach($partner_account_uploads_meta as $meta) {
          if ($meta['field_name'] === 'acceptable' && $meta['value'] == 'true') $uploaded = true;
        }
  
        if (!$uploaded) {
          $requested.= '&#8226; ' . $file['specify'];
          $requested.= "\r\n";
        }
      } else {
        $requested.= '&#8226; ' . $file['specify'];
        $requested.= "\r\n";
      }
    }
    return $requested;
  }

  
  /**
   * saveBrokerFlowId
   * referrerCode: A_xxxxxxx means account level, L_xxxxxxx means lead level
   *
   * @return void
   */
  public function saveBrokerFlowId(){
    try {
      $data = $this->request->getData();
      if (!$this->checkPartnerAccountBelongsToUser($data['account_ref'], null, null)) {
        throw new \Exception("You don't have permission to see the account.");
      }
      $sendData = ['referrerCode' => "A_".$data['account_ref'], "documentId" => $data['documentId']];
      $curl = new CurlHelper(getenv('DOMAIN_BANKFEEDS')."/broker-flow");
      $response = $curl->post($sendData, true, ["x-api-key" => getenv('BANK_STATEMENT_API_KEY')]);
      return $this->setJsonResponse($response);
    } catch(\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'error' => $e->getMessage(), 'message' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  public function saveOverdraft($account_ref){
    try {
      $hashids = new Hashids('partner_accounts', 7);
      $partnerAccountId = $hashids->decode($account_ref)[0];
      $data = $this->request->getData();
      if (!$this->checkPartnerAccountBelongsToUser($account_ref, null, null)) {
        throw new \Exception("You don't have permission to see the account.");
      }
      $success = true;
      if(!empty($data)){
        foreach($data as $account_id=>$overdraft){
          $sendData = ['accountId' => $account_id, 'partnerAccountId' => $partnerAccountId, 'overdraft' => $overdraft];
          $curl = new CurlHelper(getenv('DOMAIN_BANKFEEDS')."/update-overdraft");
          $response = $curl->post($sendData, true, ["x-api-key" => getenv('BANK_STATEMENT_API_KEY')]);
          $success = $success && $response['success'];
        }
      }
      return $this->setJsonResponse(['success' => $success]);
    } catch(\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'error' => $e->getMessage(), 'message' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }
  

  public function updateTrusteeDetails() {
    try {
      if (!$this->request->is('post')) {
        throw new \Exception("Only POST request is allowed.");
      }
      
      $data = $this->request->getData();
      if (empty($data['account_ref'])) {
        throw new \Exception("Missed required field: account_ref");
      }
      $hashids = new Hashids('partner_accounts', 7);
      $data['partner_account_id'] = $hashids->decode($data['account_ref'])[0];

      $partnerAccountTable = TableRegistry::getTableLocator()->get('PartnerAccountEntity');
      $partner_account = $partnerAccountTable->get($data['partner_account_id'], [
        'contain' => ['EntityTrustEntity']
      ]);

      if (!$this->checkPartnerAccountBelongsToUser($data['account_ref'], null, $partner_account->partner_id)) {
        throw new \Exception("You don't have permission to see the account.");
      }

      $partnerAccountTable->patchEntity($partner_account, $data, [
        'associated' => ['EntityTrustEntity']
      ]);
      $partnerAccountTable->save($partner_account);
      return $this->setJsonResponse(['success' => true, 'account' => $partner_account]);
    } catch(\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }


  public function redirectStartAssetQuote() {
    $url = getenv('DOMAIN_BRO', true) . '/partners/dashboard?start_asset_quote=1';
    return $this->setJsonResponse(['success' => true, 'url' => $url]);
  }
  
  public function redirectStartAddLead() {
    $url = getenv('DOMAIN_BRO', true) . '/partners/dashboard?add_lead=1';
    return $this->setJsonResponse(['success' => true, 'url' => $url]);
  }

  public function authenticateApplicants() {
    try {
      $ref = $this->checkAutoLoginCode($this->request->query('code'));

      $partnerUserTable = TableRegistry::getTableLocator()->get('PartnerUserEntity');
      $partnerUser = $partnerUserTable->get($ref['partnerUserId']);

      $partnerTable = TableRegistry::getTableLocator()->get('PartnerEntity');
      $partner = $partnerTable->get($partnerUser->partner_id);

      if ($ref['source'] === 'Applicants') {
        $accountPeopleTable = TableRegistry::getTableLocator()->get('PartnerAccountPeopleEntity');

        $payload = $accountPeopleTable->find('all')
          ->where(['id' => (int)$ref['ref']])
          ->toArray();

        $payload = json_decode(json_encode($payload), true);
      } else {
        $partnerAccountTable = TableRegistry::getTableLocator()->get('PartnerAccountEntity');

        $payload = $partnerAccountTable->find('all')
          ->where(['account_ref' => $ref['ref']])
          ->toArray();
        
        $payload = json_decode(json_encode($payload), true);   
      }
      
      if (!empty($payload)) {
        return $this->setJsonResponse([
          'success' => true, 
          'ref'=>$ref['ref'], 
          'source' => $ref['source'], 
          'page' => $ref['page'], 
          'referrer' => $partner['organisation_name'],
          'partner_user_id' => (int)$ref['partnerUserId'],
          'partner_id' => $partnerUser->partner_id,
        ]);
      } else {
        return $this->setJsonResponse(['success' => false, 'message' => 'Access Denied']);
      }

    } catch(\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  public function importAccountsApplicants() {
    // Save CSV to AWS.
    $data = $this->request->getData();

    $user = $this->Auth->user();
    if (empty($user)) {
      $user = $this->Auth->identify();
    }

    $fileName = 'import-accounts-applicants-' . date('Y-m-d-His') . '.csv';
    $partnerRef = LendInternalAuth::hashPartnerId($user['partner_id']);
    if (empty($data['partner_user_ref'])) {
        $data['partner_user_ref'] = $user['partner_user_ref'];
    }
    if (empty($data['partner_user_ref'])) {
        return $this->setJsonResponse(['success' => false, 'message' => 'Partner User not found.']);
    }
    $fullPath = 'csv-import/' . $partnerRef . '/import-files';
    $data['path'] = $fullPath . '/' . $fileName;
    $uploadResult = $this->_uploadRequest($data);

    // Creat background job.
    $this->LoadModel('BackgroundJobs')->addBackgroundJob(array(
      'job_type'      => 'csvImport',
      'ref_id'        => json_encode([
        'callback_url' => '/account-apis/account/run-import-accounts-applicants',
        'partner_user_ref' => $data['partner_user_ref'],
        'file_path' => $data['path'],
      ]),
      'class_name'    => 'BackgroundJobProxy',
      'function_name' => 'proxyToBrokerPlatform',
      'created'       => date('Y-m-d H:i:s', time()),
    ));

    return $this->setJsonResponse(['success' => true, 'message' => 'Import started']);
  }

  public function runImportAccountsApplicants() {
    $data = $this->request->getData();

    $partnerUser = TableRegistry::getTableLocator()->get('PartnerUserEntity')->find()
      ->where(['partner_user_ref' => $data['partner_user_ref']])->first();

    if (empty($partnerUser)) {
      return $this->setJsonResponse(['success' => false, 'message' => 'Partner User not found.']);
    }

    $columnNames = [
      "First Name",
      "Middle Name",
      "Last Name",
      "Mobile (No Area Code)",
      "Phone (No Area Code)",
      "Email",
      "DOB (DD/MM/YYYY)",
      "Title (Mr, Mrs, Miss, Ms)",
      "Gender (Male, Female, or leave empty)",
      "Marital Status ( Single, Married, De Facto)",
      "Residency Status (Citizen, Permanent Resident, Visa)",
      "Drivers Licence Number",
      "Drivers Licence State (Abbreviated)",
      "Drivers Licence Expiry Date (DD/MM/YYYY)",
      "Drivers Licence Card Number",
      "Passport Number",
      "Passport Issuing Country (Abbreviated)",
      "Passport Expiry (DD/MM/YYYY)",
      "Medicare Card Number",
      "Medicare IRN",
      "Medicare Expiry (DD/MM/YYYY)",
      "Medicare Card Colour (Green, Yellow, Blue)",
      "Bank Account Name",
      "Bank Account BSB",
      "Bank Account Number",
      "Current Address Line 1 (E.g. 123 Fake St)",
      "Current Address Suburb",
      "Current Address State (Abbreviated)",
      "Current Address Postcode",
      "Current Address Country (Abbreviated)",
      "Current Address Date From (DD/MM/YYYY)",
      "Applicant Person Note 1",
      "Applicant Person Note 2",
      "Applicant Person Note 3",
      "Applicant Person Note 4",
      "Applicant Person Note 5",
      "ABN",
      "Business Name",
      "Business Current Trading  Address Line 1 (E.g. 123 Fake St)",
      "Business Current Trading Address Suburb",
      "Business Current Trading Address State (Abbreviated)",
      "Business Current Trading Address Postcode",
      "Business Country (Abbreviated)",
      "Business Monthly Revenue",
      "Business Monthly Expenses",
      "Business Note 1",
      "Business Note 2",
      "Business Note 3",
      "Business Note 4",
      "Business Note 5",
      "Lend User Ref (or leave empty to allow all users to access)"
    ];

    $fieldMap = [
      'PartnerAccountPeopleEntity.first_name' => ['First Name'],
      'PartnerAccountPeopleEntity.middle_name' => ['Middle Name'],
      'PartnerAccountPeopleEntity.last_name' => ['Last Name', 'Surname'],
      'PartnerAccountPeopleEntity.mobile' => ['Mobile Number', 'Mobile', 'Mobile (No Area Code)'],
      'PartnerAccountPeopleEntity.phone' => ['Phone Number', 'Phone', 'Phone (No Area Code)'],
      'PartnerAccountPeopleEntity.email' => ['Email'],
      'PartnerAccountPeopleEntity.dob' => ['Date of Birth', 'DOB (DD/MM/YYYY)', 'DOB'],
      'PartnerAccountPeopleEntity.title' => ['Title', 'Salutation', 'Title (Mr, Mrs, Miss, Ms)'],
      'PartnerAccountPeopleEntity.gender' => ['Gender', 'Sex', 'Gender (Male, Female, or leave empty)'],
      'PartnerAccountPeopleEntity.marital_status' => ['Marital Status', 'Marital Status ( Single, Married, De Facto)', 'Marital Status (Single, Married, De Facto)'],
      'PartnerAccountPeopleEntity.residency_status' => ['Residency Status', 'Residency Status (Citizen, Permanent Resident, Visa)', 'Residency'],
      'PartnerAccountPeopleEntity.driving_licence_num' => ['Driving Licence Number', 'Driving Licence', 'Drivers Licence Number', 'Drivers Licence'],
      'PartnerAccountPeopleEntity.driving_licence_state' => ['Drivers Licence State (Abbreviated)', 'Drivers Licence State'],
      'PartnerAccountPeopleEntity.driving_licence_expiry' => ['Drivers Licence Expiry Date (DD/MM/YYYY)', 'Drivers Licence Expiry Date', 'Drivers Licence Expiry'],
      'PartnerAccountPeopleEntity.driving_licence_card_number' => ['Drivers Licence Card Number'],
      'PartnerAccountPeopleEntity.passport_number' => ['Passport Number'],
      'PartnerAccountPeopleEntity.passport_country' => ['Passport Issuing Country (Abbreviated)', 'Passport Country', 'Passport Issuing Country'],
      'PartnerAccountPeopleEntity.passport_expiry' => ['Passport Expiry (DD/MM/YYYY)', 'Passport Expiry'],
      'PartnerAccountPeopleEntity.medicare_number' => ['Medicare Card Number'],
      'PartnerAccountPeopleEntity.medicare_ref' => ['Medicare IRN'],
      'PartnerAccountPeopleEntity.medicare_expiry' => ['Medicare Expiry (DD/MM/YYYY)', 'Medicare Expiry'],
      'PartnerAccountPeopleEntity.medicare_card_colour' => ['Medicare Card Colour (Green, Yellow, Blue)', 'Medicare Card Colour'],
      'PartnerAccountPeopleEntity.direct_debit_acc_holder_name' => ['Bank Account Name'],
      'PartnerAccountPeopleEntity.direct_debit_bsb' => ['Bank Account BSB'],
      'PartnerAccountPeopleEntity.direct_debit_acc_number' => ['Bank Account Number'],
      'LeadOwnerAddressesEntity.address' => ['Current Address Line 1 (E.g. 123 Fake St)', 'Current Address Line 1', 'Current Address'],
      'LeadOwnerAddressesEntity.suburb' => ['Current Address Suburb', 'Current Address'],
      'LeadOwnerAddressesEntity.state' => ['Current Address State (Abbreviated)', 'Current Address State'],
      'LeadOwnerAddressesEntity.postcode' => ['Current Address Postcode', 'Postcode'],
      'LeadOwnerAddressesEntity.country' => ['Current Address Country (Abbreviated)', 'Current Address Country'],
      'LeadOwnerAddressesEntity.date_from' => ['Current Address Date From (DD/MM/YYYY)', 'Current Address Date From'],
      'PartnerAccountPeopleNotesEntity.0.notes' => ['Applicant Person Note 1', 'Applicant Note', 'Note', 'Notes'],
      'PartnerAccountPeopleNotesEntity.1.notes' => ['Applicant Person Note 2'],
      'PartnerAccountPeopleNotesEntity.2.notes' => ['Applicant Person Note 3'],
      'PartnerAccountPeopleNotesEntity.3.notes' => ['Applicant Person Note 4'],
      'PartnerAccountPeopleNotesEntity.4.notes' => ['Applicant Person Note 5'],
      'PartnerAccountEntity.abn' => ['ABN'],
      'PartnerAccountMetaEntity.business_name' => ['Business Name', 'Entity Name', 'Company Name', 'Organisation Name', 'Business'],
      'PartnerAccountMetaEntity.b_address' => ['Business Current Trading  Address Line 1 (E.g. 123 Fake St)', 'Business Current Trading Address Line 1', 'Business Address', 'Business Current Trading Address'],
      'PartnerAccountMetaEntity.b_suburb' => ['Business Current Trading Address Suburb', 'Business Address Suburb'],
      'PartnerAccountMetaEntity.b_state' => ['Business Current Trading Address State (Abbreviated)', 'Business Address State', 'Business Current Trading Address State'],
      'PartnerAccountMetaEntity.b_postcode' => ['Business Current Trading Address Postcode', 'Business Address Postcode'],
      'PartnerAccountMetaEntity.b_country' => ['Business Country (Abbreviated)', 'Business Country'],
      'PartnerAccountMetaEntity.sales_monthly' => ['Business Monthly Revenue', 'Monthly Revenue', 'Revenue'],
      'PartnerAccountMetaEntity.monthly_expenses' => ['Business Monthly Expenses', 'Monthly Expenses', 'Expenses'],
      'LeadNotesEntity.0.notes' => ['Business Note 1', 'Business Note', 'Business Notes'],
      'LeadNotesEntity.1.notes' => ['Business Note 2'],
      'LeadNotesEntity.2.notes' => ['Business Note 3'],
      'LeadNotesEntity.3.notes' => ['Business Note 4'],
      'LeadNotesEntity.4.notes' => ['Business Note 5'],
      'PartnerAccountEntity.partner_user_ref' => ['Lend User Ref (or leave empty to allow all users to access)', 'Lend User Ref', 'Ref', 'User Ref'],
    ];

    $entitySavingOrder = [
      'PartnerAccountEntity',
      'PartnerAccountPeopleEntity',
      'PartnerAccountPeopleNotesEntity',
      'PartnerAccountMetaEntity',
      'LeadNotesEntity',
      'LeadOwnerAddressesEntity'
    ];

    $entityForeignKeys = [
      'PartnerAccountPeopleEntity' => 'PartnerAccountEntity.partner_account_id.partner_account_id',
      'PartnerAccountPeopleNotesEntity' => 'PartnerAccountPeopleEntity.id.partner_account_people_id',
      'PartnerAccountMetaEntity' => 'PartnerAccountEntity.partner_account_id.partner_account_id',
      'LeadNotesEntity' => 'PartnerAccountEntity.partner_account_id.partner_account_id',
      'LeadOwnerAddressesEntity' => 'PartnerAccountPeopleEntity.id.partner_account_people_id',
    ];

    $noAbnAccount = PartnerAccountEntityTable::getNoAbnAccount($partnerUser->partner_id);
    $entityDefaultValues = [
      'PartnerAccountEntity' => [
        'is_imported' => true,
        'partner_id' => $partnerUser->partner_id,
      ],
      'PartnerAccountPeopleEntity' => [
        'is_imported' => true,
        'partner_id' => $partnerUser->partner_id,
        'partner_account_id' => $noAbnAccount['partner_account_id'],
      ],
    ];

    $entityFieldValidation = [
      'PartnerAccountEntity' => [
        'abn' => [LeadAbnLookupEntityTable::class, 'isValidAbn'],
      ],
      'PartnerAccountPeopleEntity' => [
        'first_name' => [PartnerAccountPeopleEntityTable::class, 'isValidFirstName'],
        'last_name' => [PartnerAccountPeopleEntityTable::class, 'isValidLastName'],
        'phone' => [PartnerAccountPeopleEntityTable::class, 'isValidPhone'],
        'mobile' => [PartnerAccountPeopleEntityTable::class, 'isValidMobile'],
      ],
    ];

    $entityFieldFormat = [
      'PartnerAccountEntity' => [
        'abn' => [PartnerAccountEntityTable::class, 'formatAbn'],
      ],
      'PartnerAccountPeopleEntity' => [
        'mobile' => [PartnerAccountPeopleEntityTable::class, 'formatMobile'],
        'phone' => [PartnerAccountPeopleEntityTable::class, 'formatPhone'],
      ],
    ];

    $entityRequiredFields = [
      'PartnerAccountEntity' => [
        'abn',
      ],
      'PartnerAccountPeopleEntity' => [
        'first_name',
        'last_name',
        'email',
      ],
      'PartnerAccountPeopleNotesEntity' => [
        'partner_account_people_id',
      ],
    ];

    $entityDuplicationCheck = [
      'PartnerAccountEntity' => [PartnerAccountEntityTable::class, 'getDuplicate'],
      'PartnerAccountPeopleEntity' => [PartnerAccountPeopleEntityTable::class, 'getDuplicate'],
      'PartnerAccountMetaEntity' => [PartnerAccountMetaEntityTable::class, 'getDuplicate'],
    ];

    $entityAfterSaveCallbacks = [
      'PartnerAccountEntity' => [PartnerAccountEntityTable::class, 'createEmptyAbnRecordIfNotExists'],
      'PartnerAccountPeopleEntity' => [PartnerAccountPeopleEntityTable::class, 'createPartnerAccountLinkPeople'],
    ];

    $importSettings = [
      'partnerUser' => $partnerUser,
      'fieldMap' => $fieldMap,
      'columnNames' => $columnNames,
      'entitySavingOrder' => $entitySavingOrder,
      'entityForeignKeys' => $entityForeignKeys,
      'entityDefaultValues' => $entityDefaultValues,
      'entityFieldFormat' => $entityFieldFormat,
      'entityFieldValidation' => $entityFieldValidation,
      'entityRequiredFields' => $entityRequiredFields,
      'entityDuplicationCheck' => $entityDuplicationCheck,
      'entityAfterSaveCallbacks' => $entityAfterSaveCallbacks,
    ];

    if ((string)getenv('LEND_ENV') !== '2') {
      $devFolder = 'DevTeam/';
    } else {
      $devFolder = '';
    }
    $fullPath = $devFolder . $data['file_path'];

    $csvContent = $this->getFileContent($fullPath);
    if (empty($csvContent)) {
      return $this->setJsonResponse(['success' => false, 'message' => 'Failed to read the import file content.']);
    }
    $csvImporter = new CsvImportHelper($importSettings, $csvContent);

    try {
      $importResult = $csvImporter->import();
    }
    catch (\Exception $e) {
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()]);
    }

    $errorReportLink = '';
    if (!empty($importResult['errorCsv'])) {
      try {
        $tempFile = tempnam(sys_get_temp_dir(), 'csv');
        $fileHandle = fopen($tempFile, 'w');

        fwrite($fileHandle, $importResult['errorCsv']);
        fclose($fileHandle);
      }
      catch (\Exception $e) {
        return $this->setJsonResponse(['success' => false, 'message' => 'Error saving temporary error csv']);
      }

      $partnerRef = LendInternalAuth::hashPartnerId($partnerUser['partner_id']);
      $fileNameArray = explode('/', $data['file_path']);
      $fileName = $fileNameArray[count($fileNameArray) - 1];
      $filePath = 'csv-import/' . $partnerRef . '/error-reports';
      $fullFileName = 'errors-for-' . $fileName;
      $errorCsvPath = $filePath . '/' . $fullFileName;

      $size = filesize($tempFile);
      $data['path'] = $errorCsvPath;
      $data['type'] = 'application/csv';
      $data['file'] = [
        'type' => 'application/csv',
        'tmp_name' => $tempFile,
        'size' => $size,
      ];

      try {
        $this->_uploadRequest($data);
        $partnerFileLibraryUploadTable = TableRegistry::getTableLocator()->get('PartnerFileLibraryUploadEntity');
        $partnerFileLibraryUpload = $partnerFileLibraryUploadTable->newEntity([
          'partner_user_id' => $partnerUser['partner_user_id'],
          'name' => $fullFileName,
          'full_path' => $filePath,
          'file_size' => $data['file']['size'],
          'file_type' => $data['file']['type'],
        ]);

        $partnerFileLibraryUploadTable->save($partnerFileLibraryUpload);

        $errorReportUrl = '/partner-file-library/get/' . $partnerFileLibraryUpload->partner_file_library_upload_ref;
        $errorReportLink = '<a href="' . $errorReportUrl . '" class="login-btn" target="_blank">Click here to download error report</a>';
      }
      catch (\Exception $e) {
        return $this->setJsonResponse(['success' => false, 'message' => 'Error uploading error csv to s3']);
      }
    }

    $adhocData = [
      'total_imported' => $importResult['successCount'],
      'total_imported_with_warnings' => $importResult['warningsOnlyCount'],
      'total_duplicates' => $importResult['duplicateCount'],
      'total_failed' => $importResult['errorCount'],
      'total_exceptions' => $importResult['exceptionCount'],
      'error_report_link' => $errorReportLink,
      'not_found_fields' => implode(', ', $importResult['notFoundFields']),
    ];
    TableRegistry::getTableLocator()->get('PartnerNotifications')->sendPartnerNotifications(
      $partnerUser['partner_id'],
      'DataImportFinished',
      false,
      $adhocData,
      $partnerUser['partner_user_id']
    );
    unset($importResult['errorCsv']);
    unset($importResult['errorCsvLines']);
    return $this->setJsonResponse(['success' => true, 'import_results' => $importResult]);
  }
  public function updateLenderNotes()
  {
    try {
      $account_ref = $this->request->getData('account_ref');
      if (empty($account_ref)) {
        throw new \Exception("Missed required field: account_ref");
      }
      $hashids = new Hashids('partner_accounts', 7);
      $account_id = $hashids->decode($account_ref)[0];

      $partnerAccountTable = TableRegistry::getTableLocator()->get('PartnerAccountEntity');

      $params['additional_info'] = $this->request->getData('additional_info');

      $partnerAccount = $partnerAccountTable->get($account_id);
      $partnerAccount = $partnerAccountTable->patchEntity($partnerAccount, $params);
      if (!$partnerAccountTable->save($partnerAccount)) {
        throw new \Exception("Failed to save the updated lender notes");
      }

      return $this->setJsonResponse(["success" => true, "message" => "Lender notes updated successfully"]);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(["success" => false, "message" => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }
  public function deleteAccount()
  {
    try{
        if (!$this->request->is('post')) {
            throw new \Exception("Only POST request is allowed.");
        }
        $data = $this->request->getData();
        if (empty($data['account_ref'])) {
            throw new \Exception("Missed required field: account_ref");
        }
        $hashids   = new Hashids('partner_accounts', 7);
        $accountId = $hashids->decode($data['account_ref'])[0];
        $partnerAccountTable = TableRegistry::getTableLocator()->get('PartnerAccountEntity');
        $account = $partnerAccountTable->get($accountId, [
          'contain' => [
              'PartnerAccountLinkPeopleEntity.PartnerAccountPeopleEntity',
              'LeadEntity'
          ]
        ]);

        if (!$this->checkPartnerAccountBelongsToUser($data['account_ref'], null, $account->partner_id)) {
          throw new \Exception("You don't have permission to delete this account.");
        }

        if(!empty($account->partner_account_link_people)){
          $people_ids = collection($account->partner_account_link_people)
          ->map(function ($link_people) {
              return $link_people->people_id;
          })
          ->toArray();
          if(!empty($people_ids)){
            $partnerAccountPeopleTable = TableRegistry::getTableLocator()->get('PartnerAccountPeopleEntity');
            $partnerAccountPeopleTable->updateAll(
                ['status' => 'inactive'], 
                [ 
                'id IN' => $people_ids,
                'status' => 'active'  // skip the deleted people
              ]
            );
          }
        }
        // update lead status ==> is_archived 
        $leadTable = TableRegistry::getTableLocator()->get('LeadEntity');
        $leadTable->updateAll(
          ['is_archived' => 2],
          [
              'account_id' => $accountId,
              'is_archived' => 0 
          ]
        );
        $account->status = 'inactive';
        if ($partnerAccountTable->save($account)) {
          return $this->setJsonResponse(['success' => true, 'message' => 'Account successfully removed']); 
        } else {
            throw new \Exception("Failed to update account status.");
        }
    } catch (\Exception $e) {
        Log::error($e->getMessage());
        Log::error($e->getTraceAsString());
        return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()]);
    }
  }

  public function reinstateAccount()
  {
    try {
      if (!$this->request->is('post')) {
          throw new \Exception("Only POST request is allowed.");
      }

      $data = $this->request->getData();
      if (empty($data['account_ref'])) {
          throw new \Exception("Missed required field: account_ref");
      }

      $hashids = new Hashids('partner_accounts', 7);
      $accountId = $hashids->decode($data['account_ref'])[0];

      $partnerAccountTable = TableRegistry::getTableLocator()->get('PartnerAccountEntity');
      $account = $partnerAccountTable->get($accountId, [
          'contain' => [
              'PartnerAccountLinkPeopleEntity.PartnerAccountPeopleEntity',
              'LeadEntity'
          ]
      ]);

      if (!$this->checkPartnerAccountBelongsToUser($data['account_ref'], null, $account->partner_id)) {
          throw new \Exception("You don't have permission to reinstate this account.");
      }

      // Get the people IDs linked to this account
      if (!empty($account->partner_account_link_people)) {
          $people_ids = collection($account->partner_account_link_people)
              ->map(function ($link_people) {
                  return $link_people->people_id;
              })
              ->toArray();

          if (!empty($people_ids)) {
              $partnerAccountPeopleTable = TableRegistry::getTableLocator()->get('PartnerAccountPeopleEntity');
              $partnerAccountPeopleTable->updateAll(
                  ['status' => 'active'],
                  [
                      'id IN' => $people_ids,
                      'status' => 'inactive'  // only update inactive records
                  ]
              );
          }
      }

      // Rollback lead archived status
      $leadTable = TableRegistry::getTableLocator()->get('LeadEntity');
      $leadTable->updateAll(
          ['is_archived' => 0],
          [
              'account_id' => $accountId,
              'is_archived' => 2  // Only update leads that were archived by deleteAccount
          ]
      );

      // Rollback account status
      $account->status = 'active';
      if ($partnerAccountTable->save($account)) {
          return $this->setJsonResponse(['success' => true, 'message' => 'Account successfully reinstated']); 
      } else {
          throw new \Exception("Failed to update account status.");
      }

    } catch (\Exception $e) {
        Log::error($e->getMessage());
        Log::error($e->getTraceAsString());
        return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()]);
    }
  }


}
