<?php

namespace AccountApis\Controller;

use Cake\ORM\TableRegistry;
use Cake\Core\Configure;
use Cake\Log\Log;
use Hashids\Hashids;
use Cake\Event\Event;
use App\Lend\LendInternalAuth;
use Cake\Datasource\ConnectionManager;
use Exception;

class ApplicantController extends AppController
{
  public function initialize()
  {
    parent::initialize();
    $this->Auth->allow([
      'view',
      'getS3SignedUploadRequest',
      'updateApplicantUploads',
      'updateUploadsRequested',
      'viewImage',
      'updateApplicantSubscription' //subscribe/unsubscribe
      // 'getDownloadedFiles'//only used in broker platform
    ]);
    $this->loadComponent('AddressValidator');
  }

  public function beforeFilter(Event $event)
  {
    parent::beforeFilter($event);
    $data = $this->request->getData();
    if (empty($data)) $data = $this->request->input("json_decode", true);
    
    if ($data['applicant_ref'] && !isset($data['people_id'])) {
      foreach ($data as $key => $value) {
        $this->request = $this->request->withData($key, $value);
      }
      $hashids = new Hashids('partner_account_people', 7);
      $people_id = $hashids->decode($data['applicant_ref'])[0];
      $this->request = $this->request->withData("people_id", $people_id)->withoutData('applicant_ref');
    }
  }

  // $page = 1, $pagesize = 10
  public function dashboard($page = 1, $pagesize = 10)
  {
    try {
      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }
      if($user['access_all_leads'] == true){
        $where = ['PartnerAccountPeopleEntity.partner_id' => $user['partner_id']];
      }
      $applicants = TableRegistry::getTableLocator()->get('PartnerAccountPeopleEntity', [
        'connection' => ConnectionManager::get('reader_db')
      ])->find('all')
        ->where($where)
        ->contain([ 
          'PartnerAccountLinkPeopleEntity.PartnerAccountEntity.PartnerAccountMetaEntity', 
          'LeadOwnerEmploymentEntity'
        ])
        ->enableAutoFields(true);
      if($user['access_all_leads'] == false){
        $applicants->matching('PartnerAccountLinkPeopleEntity.PartnerAccountEntity', function ($q) use ($user) {
          return $q->where(['PartnerAccountEntity.partner_user_id' => $user['partner_user_id']]);
        });
      }
      $data = $this->request->getData();
      // Sanitize data
      $data = $this->_sanitizeData($data);
      
      if (!empty($data['search'])) {
        $applicants->where($this->_generateCondition($data['search']));
      }

      if (!empty($data['filters'])) {
        foreach ($data['filters'] as $field => $items) {
          switch ($field) {
            case 'PartnerAccountPeopleEntity.home_owner':
              if (in_array('yes', $items) && !in_array('no', $items)) {
                $applicants->where([$field . ' IS NOT null', $field . ' = 1']);
              } elseif (in_array('no', $items)) {
                $applicants->where([$field . ' IS null']);
              }
              break;
            case 'PartnerAccountPeopleEntity.created':
              $applicants->andWhere(function ($exp) use ($field, $items) {
                $exp->gte($field, $items[0]);
                $exp->lte($field, $items[1]);
                return $exp;
              });
              break;

            case 'PartnerAccountPeopleEntity.status':
              // Handle inclusion of both active and inactive statuses
              if (in_array('inactive', $items)) {
                  $applicants->where(function ($exp) use ($items) {
                      return $exp->in('PartnerAccountPeopleEntity.status', ['active', 'inactive']);
                  });
              } else {
                  $applicants->where(['PartnerAccountPeopleEntity.status' => 'active']);
              }
              break;
            default:
              $applicants->where([$field . ' IN' => $items]);
              break;
          }
        }
      }

      if (empty($data['filters']['PartnerAccountPeopleEntity.status'])) {
        $applicants->where(['PartnerAccountPeopleEntity.status' => 'active']);
      }

      if (!empty($data['extraFetchData'])) {
        foreach ($data['extraFetchData'] as $extraItem) {
          $applicants->where([$extraItem['field'] => $extraItem['value']]);
        }
      }

      if (!empty($data['sorting'])) {
        $sort = null;
        $whitelist = [
          'PartnerAccountPeopleEntity.first_name',
          'PartnerAccountPeopleEntity.email',
          'PartnerAccountPeopleEntity.state',
          'PartnerAccountPeopleEntity.consent',
          'PartnerAccountPeopleEntity.credit_history',
          'PartnerAccountPeopleEntity.residency_status',
          'PartnerAccountPeopleEntity.created',
          'PartnerAccountPeopleEntity.updated',
        ];
        foreach ($data['sorting'] as $field => $items) {
          if (in_array($field, $whitelist)) {
            $direction = (strtolower($items[0]) === 'asc') ? 'asc' : 'desc';
            $sort = [$field => $direction];
            break;
          }
        }
        $applicants->order($sort);
      } else {
        $applicants->order(['PartnerAccountPeopleEntity.created' => 'DESC']);
      }

      $total = $applicants->count();
      $applicants->limit($pagesize)
        ->offset(($page - 1) * $pagesize);

      // Convert `partner_accounts` from string to array & remove `partner_account` and `partner_account_meta`
      $applicants->formatResults(function (\Cake\Collection\CollectionInterface $results) {
        return $results->map(function ($row) {
          $row['partner_accounts'] = array_map(function ($row) {
            return [
              'status' => $row['status'],
              'people_id' => $row['id'],
              'account_ref' => $row['partner_account']['account_ref'],
              'business_name' => $row['partner_account']['partner_account_meta']['business_name'],
              'organisation_name' => $row['partner_account']['partner_account_meta']['organisation_name'],
              'partner_account_id' => $row['partner_account']['partner_account_id'],
            ];
          }, $row['partner_account_link_people']);
          unset($row['partner_account_link_people']);
          return $row;
        });
      });

      $result = ['success' => true, 'total' => $total, 'applicants' => $applicants];
      return $this->setJsonResponse($result);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success'=>false, 'message'=>$e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  public function view($applicant_ref)
  {
    try {
      ConnectionManager::alias('reader_db', 'default');
      if (is_numeric($applicant_ref)) {
        $applicant_id = $applicant_ref;
      } else {
        $hashids = new Hashids('partner_account_people', 7);
        $applicant_id = $hashids->decode($applicant_ref)[0];
      }
      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }
      $accountPeople = TableRegistry::getTableLocator()->get('PartnerAccountPeopleEntity')->get($applicant_id, [
        'contain' => [
          'PartnerAccountPeopleIncomesEntity',
          'PartnerAccountPeopleExpensesEntity',
          'PartnerAccountPeopleExpensesEntity.PartnerAccountPeopleExpensesBreakdownEntity',
          'PartnerAccountPeopleAssetsEntity',
          'PartnerAccountPeopleLiabilitiesEntity',
          'PartnerAccountLinkPeopleEntity.PartnerAccountEntity' => [
              'LeadAbnLookupEntity' => [
                'fields' => [
                  'abn', 'organisation_name', 'business_name', 'gst_effective_from',
                  'state', 'entity_type_desc', 'effective_from', 'entity_status'
                ]
              ],
              'PartnerAccountMetaEntity' => [
                'fields' => ['partner_account_id', 'organisation_name', 'business_name'],
                'ConfigIndustryEntity' => ['fields' => ['tree']],
              ]
            ],
          'PartnerAccountLinkPeopleEntity.PartnerAccountEntity', 
          'LeadAssetsEntity', 
          'LeadAssetsEntity.ConfigAssetTypeEntity',
          'LeadLiabilitiesEntity',
          'LeadLiabilitiesEntity.ConfigLiabilityEntity',
          'LeadOwnerFinancesEntity',
          'LeadOwnerAddressesEntity',
          'PartnerAccountPeopleCurrentAddress',  
          'LeadOwnerEmploymentEntity',
          'LeadOwnerEmploymentEntity.LeadAbnLookupEntity',
          'CheckMobile'=> ['fields' => ['id', 'value', 'result', 'next_checkable', 'CheckMobile__checked'=>'created']],
          'CheckEmail'=> ['fields' => ['id', 'value', 'result', 'next_checkable', 'CheckEmail__checked'=>'created', 'CheckEmail__disposable'=>'email_disposable']],
          'PartnerAccountPeopleNotesEntity',
          'PartnerApplicantGroupUploadsEntity.PartnerApplicantGroupUploadsMetaEntity',
          'PartnerApplicantGroupUploadsRequestedEntity',
        ]
      ]);

      if (empty($user)) {
        $ref = $this->checkAutoLoginCode($this->request->query('code'));
        if (empty($ref['ref'])) {
          throw new \Exception("You don't have permission to see the account.");
        }
      } else {
        if (!$user['access_all_leads']) {
          $accountPeople->partner_account_link_people = array_filter($accountPeople->partner_account_link_people, function ($account) use ($user) {
            return (int)$account->partner_account->partner_user_id === (int)$user['partner_user_id'];
          });
          if ((int)$accountPeople->partner_user_id !== (int)$user['partner_user_id']) {
            throw new \Exception("You don't have permission to see the account.");
          }
        }
      }

      $result = ["success" => true, "people" => $accountPeople];
      ConnectionManager::dropAlias('default');
      return $this->setJsonResponse($result);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success'=>false, 'message'=>$e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  /**
   * Get all leads associated with an applicant
   * @param mixed $account_ref
   * @throws \Exception
   * @return void
   */

  public function getHeaderWidget($applicant_ref) {
    try {
      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }

      $applicant_id = (new LendInternalAuth)->unhashPartnerAccountPeopleId($applicant_ref);
      if (empty($applicant_id)) {
        throw new \Exception("Can't find an applicant.");
      }

      // Calculate total financed amount, settled loans count, and total remuneration
      $partnerCommissionsTable = TableRegistry::getTableLocator()->get('PartnerCommissionEntity');
      $query = $partnerCommissionsTable->find();
      $totalsQuery = $query
          ->select([
              'total_amount' => $query->newExpr('SUM(PartnerCommissionEntity.funded_amount)'),
              'total_settled' => $query->newExpr('COUNT(DISTINCT PartnerCommissionEntity.lead_id)'),
              'total_remuneration' => $query->newExpr('SUM(PartnerCommissionEntity.commission)')
          ])
          ->join([
              'table' => 'leads',
              'alias' => 'LeadEntity',
              'type' => 'INNER',
              'conditions' => [
                  'LeadEntity.lead_id = PartnerCommissionEntity.lead_id'
              ]
          ])
          ->join([
              'table' => 'lead_owners',
              'alias' => 'LeadOwnerEntity',
              'type' => 'INNER',
              'conditions' => [
                  'LeadOwnerEntity.lead_id = LeadEntity.lead_id',
                  'LeadOwnerEntity.status' => 'active',
                  'LeadOwnerEntity.partner_account_people_id' => $applicant_id
              ]
          ])
          ->where([
              'PartnerCommissionEntity.funded_amount IS NOT' => null,
              'PartnerCommissionEntity.funded_amount >' => 0,
              'PartnerCommissionEntity.sale_outcome_id IS NOT' => null,
          ]);
    
      $totalsResult = $totalsQuery->first();

      $totals = [
          "total_amount" => (float)$totalsResult->total_amount,
          "total_settled_loans" => (int)$totalsResult->total_settled,
          "total_remuneration" => (float)$totalsResult->total_remuneration
      ];

      return $this->setJsonResponse(['success' => true, 'header_widget' => $totals]);
    } catch(\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success'=>false, 'message'=>$e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }


  public function leads($applicant_ref)
  {
    try {
      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }
      $peopleId = (new LendInternalAuth)->unhashPartnerAccountPeopleId($applicant_ref);
      if (empty($peopleId)) {
        throw new \Exception("Can't find an applicant.");
      }
      $leadsTable = TableRegistry::getTableLocator()->get('LeadEntity');
      $contain = [
        'PartnerProductTypeEntity' => ['fields' => ['product_type_name']],
        'PartnerEntity' => ['fields' => ['status_system']],
        'LendStatusEntity' => ['fields' => ['status_name']],
        'ManStatusEntity' => ['fields' => ['status_name']],
        'PocOwner' => ['fields' => ['owner_id', 'first_name', 'last_name']],
        'LeadAssociatedDataEntity' => ['fields' => ['id', 'max_lender_id', 'lender_name']],
        'LeadAssociatedDataEntity.LenderEntity' => ['fields' => ['lender_logo']],
      ];
      $options = ['contain' => $contain];
      $options['fields'] = [
        'lead_id',
        'lead_ref',
        'created',
        'lead_type',
        'amount_requested',
        'is_closed',
        'is_archived',
      ];
      $leads = $leadsTable->find('all', $options)->join([
        'table' => 'lead_owners',
        'alias' => 'lo',
        'type' => 'INNER',
        'conditions' => [
          'lo.lead_id = LeadEntity.lead_id',
          'lo.status' => 'active',
        ],
      ])->where(['lo.partner_account_people_id' => $peopleId]);

      // Define a function to keep 'first_name', 'last_name', and 'full_name' in 'owner_poc'
      $cleanOwnerPoc = function ($lead) {
        unset($lead['lead_id']);
        if (isset($lead['owner_poc']['first_name'])) {
          $ownerPoc = [
            // 'first_name' => $lead['owner_poc']['first_name'],
            // 'last_name' => $lead['owner_poc']['last_name'],
            'full_name' => $lead['owner_poc']['full_name'],
          ];
          $lead['owner_poc'] = $ownerPoc;
        }
        return $lead;
      };

      $result = ['success' => true, 'leads' => array_map($cleanOwnerPoc, $leads->toArray())];
      return $this->setJsonResponse($result);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success'=>false, 'message'=>$e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  public function add()
  {
    try {
      if (!$this->request->is('post')) {
        throw new \Exception("Only POST request is allowed.");
      }
      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }
      $data = $this->request->getData();

      $exists = $this->_checkExists($data);
      if (!empty($exists)) {
        return $this->setJsonResponse(['success' => false, 'message' => "An Applicant with these details already exists. Import {$exists[0]['first_name']} {$exists[0]['last_name']} instead.", 'exists_people_id' => $exists[0]['id'], 'exists_applicant_ref' => $exists[0]['applicant_ref']], 208);
      }
      
      $accountTable = TableRegistry::getTableLocator()->get('PartnerAccountEntity');
      $accountPeopleTable = TableRegistry::getTableLocator()->get('PartnerAccountPeopleEntity');

      if (!empty($data['account_ref'])) {
        $hashids = new Hashids('partner_accounts', 7);
        $data['partner_account_link_people'] = [
          [
          'account_id' => $hashids->decode($data['account_ref'])[0],
          'is_main_point_of_contact' => false,
          ]
        ];

        $account = $accountTable->get($data['partner_account_link_people'][0]['account_id'], [
          'contain' => ['PartnerAccountLinkPeopleEntity'],
        ]);
        $total_people = count($account->partner_account_link_people);
        if ($total_people === 0) {
          $data['partner_account_link_people'][0]['is_main_point_of_contact'] = true;
        }
      }

      $data['partner_id'] = $user['partner_id'];
      if (!empty($data['account_ref'])) {
        $account_people = $accountPeopleTable->newEntity($data, [
          'associated' => ['PartnerAccountLinkPeopleEntity']
        ]);
      } else {
        $account_people = $accountPeopleTable->newEntity($data);
      }
      $accountPeopleTable->save($account_people);
      
      return $this->setJsonResponse([
        'success' => true,
        'people_id' => $account_people->id,
        'applicant_ref' => $account_people->applicant_ref,
        'account_ref' => !empty($data['account_ref']) ? $data['account_ref'] : null
      ]);
    } catch(\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }


  public function search()
  {
    try {
      if (!$this->request->is('post')) {
        throw new \Exception("Only POST request is allowed.");
      }
      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }
      $data = $this->request->getData();
      $data['search_str'] = preg_replace('/ /', '|', $data['search_str']);
      if (substr($data['search_str'], -1) === '|') {
        $data['search_str'] = rtrim($data['search_str'], '|');
      }

      $accountPeopleTable = TableRegistry::getTableLocator()->get('PartnerAccountPeopleEntity', [
        'connection' => ConnectionManager::get('reader_db')
      ]);

      $account_people = $accountPeopleTable->find('all')
                                          ->where([
                                            'PartnerAccountPeopleEntity.partner_id' => $user['partner_id'],
                                            'PartnerAccountPeopleEntity.status' => 'active',
                                            'OR' => [
                                              'PartnerAccountPeopleEntity.first_name regexp' => $data['search_str'],
                                              'PartnerAccountPeopleEntity.last_name regexp' => $data['search_str'],
                                              'PartnerAccountPeopleEntity.email regexp' => $data['search_str'],
                                              'PartnerAccountPeopleEntity.mobile regexp' => $data['search_str'],
                                              'PartnerAccountPeopleEntity.phone regexp' => $data['search_str']
                                            ]
                                          ])
                                          ->map(function ($row) use ($data) {
                                            $score = preg_grep(("/".$data['search_str']."/i"), [$row->first_name, $row->last_name, $row->email, $row->mobile, $row->phone]);
                                            $row->match_score = count($score) * 5;
                                            return $row;
                                          })
                                          ->toList();
      if (!empty($account_people)) {
        usort($account_people, function ($a, $b) {
          return $a->match_score < $b->match_score;
        });
      }

      return $this->setJsonResponse(['success' => true, 'account_people' => $account_people]);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }


  // This is meant to be adding applicant to an existing account
  public function clone() {
    try {
      if (!$this->request->is('post')) {
        throw new \Exception("Only POST request is allowed.");
      }
      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }
      $data = $this->request->getData();

      if (empty($data['account_ref'])) {
        throw new \Exception("Required field is missing: account_ref");
      }
      if (empty($data['people_id'])) {
        throw new \Exception("Required field is missing: people_id");
      }

      $hashids = new Hashids('partner_accounts', 7);
      $data['partner_account_id'] = $hashids->decode($data['account_ref'])[0];
      $partner_account = TableRegistry::getTableLocator()->get('PartnerAccountEntity')->get($data['partner_account_id'], [
        'contain' => ['PartnerAccountLinkPeopleEntity'],
      ]);
      if ((int)$user['partner_id'] !== (int)$partner_account->partner_id) {
        throw new \Exception("You don't have permission to clone.");
      }
      if (!empty($partner_account->partner_account_link_people)) {
        foreach ($partner_account->partner_account_link_people as $link) {
          if ((int)$link->people_id === (int)$data['people_id']) {
            throw new \Exception("Applicant already exists in this account.");
          }
        }
      }

      $accountLinkPeopleTable = TableRegistry::getTableLocator()->get('PartnerAccountLinkPeopleEntity');
      $account_link_people = $accountLinkPeopleTable->newEntity([
        'account_id' => $data['partner_account_id'],
        'people_id' => $data['people_id'],
        'is_main_point_of_contact' => empty($partner_account->partner_account_link_people) ? true : false
      ]);
      $accountLinkPeopleTable->save($account_link_people);

      return $this->setJsonResponse(['success' => true, 'account_link_people' => $account_link_people]);
    } catch(\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }


  private function _removeAllUnnecessaryFields($exists)
  {
    unset($exists['is_main_point_of_contact']);
    unset($exists['partner_account_id']);
    unset($exists['created']);
    unset($exists['updated']);
    unset($exists['status']);

    if (!empty($exists['finance'])) {
      unset($exists['finance']['lead_owner_finance_id']);
    }

    if (!empty($exists['employments'])) {
      foreach ($exists['employments'] as $key => $employment) {
        unset($exists['employments'][$key]['lead_owner_employment_id']);
        unset($exists['employments'][$key]['abn_id']);
      }
    }

    if (!empty($exists['addresses'])) {
      foreach ($exists['addresses'] as $key => $address) {
        unset($exists['addresses'][$key]['lead_owner_address_id']);
      }
    }

    if (!empty($exists['liabilities'])) {
      foreach ($exists['liabilities'] as $key => $liability) {
        unset($exists['liabilities'][$key]['lead_liability_id']);
      }
    }

    if (!empty($exists['assets'])) {
      foreach ($exists['assets'] as $key => $asset) {
        unset($exists['assets'][$key]['lead_asset_id']);
      }
    }

    return $exists;
  }


  private function _inactiveAllAssociatedTables($person, $exists)
  {
    if (!empty($person->employments)) {
      foreach ($person->employments as $employment) {
        $exists['employments'][] = [
          'lead_owner_employment_id' => $employment->lead_owner_employment_id,
          'status' => 'deleted'
        ];
      }
    }

    if (!empty($person->addresses)) {
      foreach ($person->addresses as $address) {
        $exists['addresses'][] = [
          'lead_owner_address_id' => $address->lead_owner_address_id,
          'status' => 'deleted'
        ];
      }
    }

    if (!empty($person->liabilities)) {
      foreach ($person->liabilities as $liability) {
        $exists['liabilities'][] = [
          'lead_liability_id' => $liability->lead_liability_id,
          'status' => 'Deleted'
        ];
      }
    }

    if (!empty($person->assets)) {
      foreach ($person->assets as $asset) {
        $exists['assets'][] = [
          'lead_asset_id' => $asset->lead_asset_id,
          'status' => 'Deleted'
        ];
      }
    }

    return $exists;
  }

  // this is remove applicant from account
  public function delete()
  {
    try {
      if (!$this->request->is('post')) {
        throw new \Exception("Only POST request is allowed.");
      }
      $data = $this->request->getData();

      if (empty($data['account_ref'])) {
        throw new \Exception("Required field is missing: account_ref");
      }
      if (empty($data['people_id'])) {
        throw new \Exception("Required field is missing: people_id");
      }

      $hashids = new Hashids('partner_accounts', 7);
      $data['partner_account_id'] = $hashids->decode($data['account_ref'])[0];
      
      $accountLinkPeopleTable = TableRegistry::getTableLocator()->get('PartnerAccountLinkPeopleEntity');
      $account_link_people = $accountLinkPeopleTable->find('all')
                              ->where([
                                'account_id' => $data['partner_account_id'],
                                'people_id' => $data['people_id'],
                              ])
                              ->contain([
                                'PartnerAccountEntity',
                              ])
                              ->first();

      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }
      if ((int)$user['partner_id'] !== (int)$account_link_people->partner_account->partner_id) {
        throw new \Exception("You don't have permission to delete.");
      }
      $accountLinkPeopleTable->delete($account_link_people);

      return $this->setJsonResponse(['success' => true], 200);
    } catch(\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }


  public function updateApplicantAccountLevel()
  {
    try {
      if (!$this->request->is('post')) {
        throw new \Exception("Only POST request is allowed.");
      }
      $data = $this->request->getData();

      if (empty($data['people_id'])) {
        throw new \Exception("Required field is missing: people_id");
      }

      $accountPeopleTable = TableRegistry::getTableLocator()->get('PartnerAccountPeopleEntity');

      $account_people = $accountPeopleTable->get($data['people_id']);

      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }
      if ((int)$user['partner_id'] !== (int)$account_people->partner_id) {
        throw new \Exception("You don't have permission to update.");
      }

      $accountPeopleTable->patchEntity($account_people, $data, [
        'fieldList' => ['owner_type', 'owner_type_other_detail', 'is_guarantor', 'directorship_start_date', 'equity', 'class_of_beneficiary']
      ]);
      $accountPeopleTable->save($account_people);

      return $this->setJsonResponse(['success' => true, 'account_people' => $account_people]);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  public function updateApplicantSubscription($applicantRef, $subscribeType="email", $subscribed="true", $showThankYou=false)
  {
    try {
      if ($subscribeType !== 'email' && $subscribeType !== 'sms') {
        throw new \Exception("Invalid subscribe type.");
      }
      $accountPeopleTable = TableRegistry::getTableLocator()->get('PartnerAccountPeopleEntity');
      $accountPeopleId = (new LendInternalAuth)->unhashPartnerAccountPeopleId($applicantRef);
      $account_people = $accountPeopleTable->get($accountPeopleId, [
        'contain' => [
          'PartnerAccountLinkPeopleEntity',
          'PartnerAccountLinkPeopleEntity.PartnerAccountEntity',
          'PartnerAccountLinkPeopleEntity.PartnerAccountEntity.PartnerAccountMetaEntity',
        ]
      ]);

      $accountPeopleTable->patchEntity($account_people, [
        $subscribeType."_subscribed" => $subscribed
      ]);
      $partnerAccountOrganisationName = $account_people->partner_account_link_people[0]->partner_account->partner_account_meta->organisation_name;
      $accountPeopleTable->save($account_people);
      if ($showThankYou) {
        if ( $subscribed == 'false' || !$subscribed) {
          $this->set("message", "You have successfully unsubscribed from {$subscribeType} automation notification of {$partnerAccountOrganisationName}.");
        } else {
          $this->set("message", "You have successfully subscribed to {$subscribeType} automation notification from {$partnerAccountOrganisationName}.");
        }
        $this->viewBuilder()->setLayout('empty_html');
        $this->viewBuilder()->setTemplate('/Element/Flash/success');
      }else{
        return $this->setJsonResponse(['success' => true, 'account_people' => $account_people]);
      }
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }


  public function updateApplicantDetails()
  {
    try {
      if (!$this->request->is('post')) {
        throw new \Exception("Only POST request is allowed.");
      }
      $data = $this->request->getData();

      if (empty($data['people_id'])) {
        throw new \Exception("Required field is missing: people_id");
      }

      $accountPeopleTable = TableRegistry::getTableLocator()->get('PartnerAccountPeopleEntity');

      $account_people = $accountPeopleTable->get($data['people_id'], [
        'contain' => [
          'PartnerAccountPeopleAssetsEntity',
        ]
      ]);

      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }

      //Associated data deletions
      if (!empty($data['people_assets'])) {
        foreach ($data['people_assets'] as $assetData) {
          if (!empty($assetData['liabilities']))
            $this->deleteLiabilities($assetData['liabilities']);
        }
      }

      if (!empty($data['people_liabilities']))
        $this->deleteLiabilities($data['people_liabilities']);

      if ((int)$user['partner_id'] !== (int)$account_people->partner_id) {
        throw new \Exception("You don't have permission to update.");
      }

      if ((!empty($data['first_name']) || !empty($data['last_name']) || !empty($data['dob']))
        && (
          strtolower($account_people->first_name) !== strtolower($data['first_name'])
          || strtolower($account_people->last_name) !== strtolower($data['last_name'])
          || date('Y-m-d', strtotime($account_people->dob)) !== date('Y-m-d', strtotime($data['dob']))
        )
      ) {
        $exists = $this->_checkExists($data);
        if (!empty($exists)) {
          return $this->setJsonResponse(['success' => false, 'message' => "An Applicant with these details already exists. Import {$exists[0]['first_name']} {$exists[0]['last_name']} instead.", 'exists_people_id' => $exists[0]['id']]);
        }
      }


      $toDate = null;
      if (isset($data['addresses']) && is_array($data['addresses'])) {
        foreach ($data['addresses'] as &$address) { //most recent(current) is first
          $fromDate = $this->getDurationBasedFromDate($address['duration_years'], $address['duration_months'], $toDate);
          $address['date_from'] = $fromDate;
          $address['date_to'] = $toDate;
          $toDate = $fromDate; //from is to for next record
          $address = $this->AddressValidator->processAddress($address);
        }
      }

   

      $toDate = null;
      if (isset($data['employments']) && is_array($data['employments'])) {
        $currentEmployments = [];
        $previousEmployments = [];
        $first_previous_to_date = null;

        foreach ($data['employments'] as $employment) {
            if ($employment['employment_status'] === 'current') {
                $currentEmployments[] = $employment;
            } else {
                $previousEmployments[] = $employment;
            }
        }
        
        $data['employments'] = array_merge($currentEmployments, $previousEmployments);
        $firstPrevious = true;
        
        foreach ($data['employments'] as &$employment) {
            if ($employment['employment_status'] === 'current') {
                $fromDate = $this->getDurationBasedFromDate($employment['duration_years'], $employment['duration_months'], $toDate);
                $employment['date_from'] = $fromDate;
                $employment['date_to'] = null;

                if($fromDate < $first_previous_to_date || $first_previous_to_date == null){
                  $first_previous_to_date = $fromDate;
                }

            } else {
                if ($firstPrevious) {
                    $toDate = $first_previous_to_date; // Reset toDate for the first previous employment
                    $firstPrevious = false;
                }
                $employment['date_to'] = $toDate;
                $fromDate = $this->getDurationBasedFromDate($employment['duration_years'], $employment['duration_months'], $toDate);
                $employment['date_from'] = $fromDate;
                $toDate = $fromDate; // Set next 'to' date to this 'from' date
            }
        }

      }

      // Create assets and update the fake ids that used in liabilities
      $assetTable = TableRegistry::getTableLocator()->get('PartnerAccountPeopleAssetsEntity');
      $existAssets = [];
      foreach (($account_people->people_assets ?? []) as $existAsset) {
        $existAssets[] = $existAsset->id;
      }
      $data['people_assets'] = $data['people_assets'] ?? [];
      $data['people_liabilities'] = $data['people_liabilities'] ?? [];

      foreach ($data['people_assets'] as &$asset) {
        $asset['partner_account_people_id'] = $data['people_id'];
        $tempIndex = array_search($asset['id'], $existAssets);
        $tempId = $asset['id'];
        if ($tempIndex !== false) {
          $assetEntity = $assetTable->patchEntity($account_people->people_assets[$tempIndex], $asset);
        } else {
          // New Asset
          $asset['id'] = null;
          $assetEntity = $assetTable->newEntity($asset);
        }
        if ($assetTable->save($assetEntity) && $tempId != $assetEntity->id) {
          $asset['id'] = $assetEntity->id;
          foreach ($data['people_liabilities'] as &$liability) {
            if ($liability['asset_id'] == $tempId) {
              $liability['asset_id'] = $assetEntity->id;
            }
          }
        }
      }

      // Remove Invaild incomes
      $validIncomeIds = [];
      foreach ($data['people_incomes'] as $income) {
        if ($income['id']) {
          $validIncomeIds[] = $income['id'];
        }
      }
      $condition = ['partner_account_people_id' => $account_people->id,];
      if ($validIncomeIds) {
        $condition['id NOT IN'] = $validIncomeIds;
      }
      TableRegistry::getTableLocator()->get('PartnerAccountPeopleIncomesEntity')->deleteAll($condition);

      // Remove Expenses if no longer valid
      $validExpenseIds = [];
      foreach ($data['people_expenses'] as $expense) {
        if ($expense['id']) {
          $validExpenseIds[] = $expense['id'];
        }
      }
      // Filter for Invalid expenses
      $condition = ['partner_account_people_id' => $account_people->id,];
      if ($validExpenseIds) {
        $condition['id NOT IN'] = $validExpenseIds;
      }
      $peopleExpenseTable = TableRegistry::getTableLocator()->get('PartnerAccountPeopleExpensesEntity');
      $invalidExpenseIds = $peopleExpenseTable->find()
        ->where($condition)
        ->all()
        ->extract('id')
        ->toArray();

      if ($invalidExpenseIds) {
        // Remove breakdowns if expense is invaild
        TableRegistry::getTableLocator()->get('PartnerAccountPeopleExpensesBreakdownEntity')->deleteAll(
          [
            'expense_id IN' => $invalidExpenseIds
          ]
        );
        // Remove Expenses
        $peopleExpenseTable->deleteAll(
          [
            'id IN' =>  $invalidExpenseIds
          ]
        );
      }

      //Patch all changes
      $associated = [
        'PartnerAccountPeopleIncomesEntity',
        'PartnerAccountPeopleExpensesEntity',
        'PartnerAccountPeopleExpensesEntity.PartnerAccountPeopleExpensesBreakdownEntity',
        'PartnerAccountPeopleAssetsEntity.PartnerAccountPeopleLiabilitiesEntity',
        'PartnerAccountPeopleLiabilitiesEntity',
        'LeadOwnerAddressesEntity',
        'LeadOwnerEmploymentEntity',
        'LeadOwnerEmploymentEntity.LeadAbnLookupEntity',
      ];

      $accountPeopleTable->patchEntity($account_people, $data, ['associated' => $associated]);
      $accountPeopleTable->save($account_people);
      

      if (!empty($data['marital_status']) && $data['marital_status'] === 'Single' && !empty($account_people->id)) {
        $leadOwnerIncomeTable = TableRegistry::getTableLocator()->get('PartnerAccountPeopleIncomesEntity');
        $leadOwnerIncomeTable->deleteAll([
          'partner_account_people_id' => $account_people->id,
          'config_income_id' => 7
        ]);
      }

      return $this->setJsonResponse(['success' => true, 'account_people' => $account_people]);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  private function deleteLiabilities($liabilities)
  {
    $liabilitiesTable = TableRegistry::getTableLocator()->get('PartnerAccountPeopleLiabilitiesEntity');
    foreach ($liabilities as $liabilityData) {
      if (isset($liabilityData['id']) && $liabilityData['delete'] === true) {
        $liabilitiesTable->deleteAll(['id' => $liabilityData['id']]);
      }
    }
  }


  public function updateIdentification()
  {
    try {
      if (!$this->request->is('post')) {
        throw new \Exception("Only POST request is allowed.");
      }
      $data = $this->request->getData();

      if (empty($data['people_id'])) {
        throw new \Exception("Required field is missing: people_id");
      }

      $accountPeopleTable = TableRegistry::getTableLocator()->get('PartnerAccountPeopleEntity');

      $account_people = $accountPeopleTable->get($data['people_id']);

      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }
      if ((int)$user['partner_id'] !== (int)$account_people->partner_id) {
        throw new \Exception("You don't have permission to update.");
      }

      $accountPeopleTable->patchEntity($account_people, $data);
      $accountPeopleTable->save($account_people);

      return $this->setJsonResponse(['success' => true, 'account_people' => $account_people]);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }


  public function updateAddresses()
  {
    try {
      if (!$this->request->is('post')) {
        throw new \Exception("Only POST request is allowed.");
      }
      $data = $this->request->getData();

      if (empty($data['people_id'])) {
        throw new \Exception("Required field is missing: people_id");
      }

      $accountPeopleTable = TableRegistry::getTableLocator()->get('PartnerAccountPeopleEntity');

      $account_people = $accountPeopleTable->get($data['people_id'], [
        'contain' => ['LeadOwnerAddressesEntity']
      ]);

      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }
      if ((int)$user['partner_id'] !== (int)$account_people->partner_id) {
        throw new \Exception("You don't have permission to update.");
      }

      $accountPeopleTable->patchEntity($account_people, $data, [
        'associated' => ['LeadOwnerAddressesEntity']
      ]);
      $accountPeopleTable->save($account_people);

      return $this->setJsonResponse(['success' => true, 'account_people' => $account_people]);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }


  public function deleteAddress()
  {
    try {
      if (!$this->request->is('post')) {
        throw new \Exception("Only POST request is allowed.");
      }
      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }
      $data = $this->request->getData();
      if (empty($data['lead_owner_address_id'])) {
        throw new \Exception("Missed required field: address_id");
      }

      $addressTable = TableRegistry::getTableLocator()->get('LeadOwnerAddressesEntity');
      $address = $addressTable->get($data['lead_owner_address_id'], [
        'contain' => ['PartnerAccountPeopleEntity']
      ]);
      if ((int)$user['partner_id'] !== (int)$address->partner_account_people->partner_id) {
        throw new \Exception("You don't have permission to delete this address.");
      }
      $address->status = 'deleted';
      $addressTable->save($address);

      return $this->setJsonResponse(['success' => true]);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }


  public function updateEmployments()
  {
    try {
      if (!$this->request->is('post')) {
        throw new \Exception("Only POST request is allowed.");
      }
      $data = $this->request->getData();

      if (empty($data['people_id'])) {
        throw new \Exception("Required field is missing: people_id");
      }

      $accountPeopleTable = TableRegistry::getTableLocator()->get('PartnerAccountPeopleEntity');

      $account_people = $accountPeopleTable->get($data['people_id'], [
        'contain' => ['LeadOwnerEmploymentEntity', 'LeadOwnerEmploymentEntity.LeadAbnLookupEntity']
      ]);

      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }
      if ((int)$user['partner_id'] !== (int)$account_people->partner_id) {
        throw new \Exception("You don't have permission to update.");
      }

      $accountPeopleTable->patchEntity($account_people, $data, [
        'associated' => ['LeadOwnerEmploymentEntity', 'LeadOwnerEmploymentEntity.LeadAbnLookupEntity']
      ]);
      $accountPeopleTable->save($account_people);

      return $this->setJsonResponse(['success' => true, 'account_people' => $account_people]);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }


  public function deleteEmployment()
  {
    try {
      if (!$this->request->is('post')) {
        throw new \Exception("Only POST request is allowed.");
      }
      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }
      $data = $this->request->getData();
      if (empty($data['lead_owner_employment_id'])) {
        throw new \Exception("Missed required field: employment_id");
      }

      $employmentTable = TableRegistry::getTableLocator()->get('LeadOwnerEmploymentEntity');
      $employment = $employmentTable->get($data['lead_owner_employment_id'], [
        'contain' => ['PartnerAccountPeopleEntity']
      ]);
      if ((int)$user['partner_id'] !== (int)$employment->partner_account_people->partner_id) {
        throw new \Exception("You don't have permission to delete this employment history.");
      }
      $employment->status = 'deleted';
      $employmentTable->save($employment);

      return $this->setJsonResponse(['success' => true]);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }


  // NOTE:: Seems this function not used anywhere
  public function updateAsset() {
    try {
      // post to slack
      TableRegistry::getTableLocator()->get('App')->postToSlack(":zzz: Please check this is still used from somewhere. Broker Platform:[AccountApis\ApplicantController::updateAsset]", "lend_errors");
      $assets_liabilities_pair = Configure::read('Lend.assets_liabilities_pair');
      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }
      if (!$this->request->is('post')) {
        throw new \Exception("Only POST request is allowed.");
      }
      $data = $this->request->getData();

      if (empty($data['account_ref'])) {
        throw new \Exception("Missed required field: account_ref");
      }
      if (empty($data['people_id'])) {
        throw new \Exception("Required field is missing: people_id");
      }

      $hashids = new Hashids('partner_accounts', 7);
      $data['partner_account_id'] = $hashids->decode($data['account_ref'])[0];
      $data['partner_account_people_id'] = $data['people_id'];
      unset($data['people_id']);

      $partner_account = TableRegistry::getTableLocator()->get('PartnerAccountEntity')->get($data['partner_account_id']);
      if ((int)$user['partner_id'] !== (int)$partner_account->partner_id) {
        throw new \Exception("You don't have permission to update.");
      }

      $leadAssetTable = TableRegistry::getTableLocator()->get('LeadAssetsEntity');
      $liability = null;

      if (!empty($data['lead_asset_id'])) {
        $lead_asset = $leadAssetTable->get($data['lead_asset_id'], [
          'contain' => ['LeadLiabilitiesEntity', 'LeadLiabilitiesEntity.ConfigLiabilityEntity', 'ConfigAssetTypeEntity']
        ]);

        if (@$data['status'] == 'Deleted' && !empty($lead_asset->liability)) {
          $lead_asset->liability->status = 'Deleted';
          $data['liability']['status'] = 'Deleted';
          $liability = $lead_asset->liability;
        } elseif (!empty($data['finance_outstanding']) && empty($lead_asset->liability)) {
          $liability = $leadAssetTable->LeadLiabilitiesEntity->newEntity([
            'status' => 'Active',
            'partner_account_id' => $data['partner_account_id'],
            'partner_account_people_id' => $data['partner_account_people_id'],
            'liability_id' => $assets_liabilities_pair[$lead_asset->asset_type_id]
          ]);
          $lead_asset->liability = $liability;
        } elseif (@$data['finance_outstanding'] === false && !empty($lead_asset->liability)) {
          $lead_asset->liability->status = 'Deleted';
          $data['liability']['status'] = 'Deleted';
          $liability = $lead_asset->liability;
        }

        $leadAssetTable->patchEntity($lead_asset, $data, [
          'associated' => ['LeadLiabilitiesEntity']
        ]);
      } else {
        $data['status'] = 'Active';
        $lead_asset = $leadAssetTable->newEntity($data);
      }

      $leadAssetTable->save($lead_asset);

      if (!empty($lead_asset->liability)) {
        unset($lead_asset->liability);
      }
      if (!empty($liability)) {
        $liability->liability = TableRegistry::getTableLocator()->get('ConfigLiabilityEntity')->get($liability->liability_id);
      }
      return $this->setJsonResponse(['success' => true, 'asset' => $lead_asset, 'liability' => $liability]);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  // NOTE:: Seems this function not used anywhere
  public function updateLiability() {
    try {
      // post to slack
      TableRegistry::getTableLocator()->get('App')->postToSlack(":zzz: Please check this is still used from somewhere. Broker Platform:[AccountApis\ApplicantController::updateAsset]", "lend_errors");

      if (!$this->request->is('post')) {
        throw new \Exception("Only POST request is allowed.");
      }
      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }
      $data = $this->request->getData();
      if (empty($data['account_ref'])) {
        throw new \Exception("Missed required field: account_ref");
      }
      if (empty($data['people_id'])) {
        throw new \Exception("Required field is missing: people_id");
      }

      $hashids = new Hashids('partner_accounts', 7);
      $data['partner_account_id'] = $hashids->decode($data['account_ref'])[0];
      $data['partner_account_people_id'] = $data['people_id'];
      unset($data['people_id']);

      $partner_account = TableRegistry::getTableLocator()->get('PartnerAccountEntity')->get($data['partner_account_id']);
      if ((int)$user['partner_id'] !== (int)$partner_account->partner_id) {
        throw new \Exception("You don't have permission to update.");
      }

      $leadLiabilityTable = TableRegistry::getTableLocator()->get('LeadLiabilitiesEntity');

      if (!empty($data['lead_liability_id'])) {
        $lead_liability = $leadLiabilityTable->get($data['lead_liability_id'], [
          'contain' => ['ConfigLiabilityEntity']
        ]);
        $leadLiabilityTable->patchEntity($lead_liability, $data);
      } else {
        $data['status'] = 'Active';
        $lead_liability = $leadLiabilityTable->newEntity($data);
      }

      $leadLiabilityTable->save($lead_liability);
      if (empty($lead_liability->liability)) {
        $lead_liability->liability = TableRegistry::getTableLocator()->get('ConfigLiabilityEntity')->get($lead_liability->liability_id);
      }

      return $this->setJsonResponse(['success' => true, 'liability' => $lead_liability]);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }


  public function updateFinances()
  {
    try {
      if (!$this->request->is('post')) {
        throw new \Exception("Only POST request is allowed.");
      }
      $data = $this->request->getData();
      if (empty($data['people_id'])) {
        throw new \Exception("Missed required field: people_id");
      }

      // calc income_yearly_gross
      $income_fields = [
        'income_monthly_net',
        'income_monthly_government',
        'income_monthly_investment',
        'income_monthly_investment_property',
        'income_monthly_superannuation',
        'income_monthly_other'
      ];
      $total_income = 0;
      foreach ($income_fields as $f) {
        $total_income += ($data['finance'][$f] ?? 0);
      }
      $data['finance']['income_yearly_gross'] = $total_income * 12;

      $accountPeopleTable = TableRegistry::getTableLocator()->get('PartnerAccountPeopleEntity');
      $account_people = $accountPeopleTable->get($data['people_id'], [
        'contain' => ['LeadOwnerFinancesEntity']
      ]);

      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }
      if ((int)$user['partner_id'] !== (int)$account_people->partner_id) {
        throw new \Exception("You don't have permission to update.");
      }

      $accountPeopleTable->patchEntity($account_people, $data, [
        'associated' => ['LeadOwnerFinancesEntity']
      ]);
      $accountPeopleTable->save($account_people);

      return $this->setJsonResponse(['success' => true, 'account' => $account_people]);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }


  public function switchMainPoc()
  {
    try {
      if (!$this->request->is('post')) {
        throw new \Exception("Only POST request is allowed.");
      }
      $data = $this->request->getData();
      if (empty($data['account_ref'])) {
        throw new \Exception("Missed required field: account_ref");
      }
      if (empty($data['people_id'])) {
        throw new \Exception("Required field is missing: people_id");
      }

      $hashids = new Hashids('partner_accounts', 7);
      $data['partner_account_id'] = $hashids->decode($data['account_ref'])[0];


      $accountTable = TableRegistry::getTableLocator()->get('PartnerAccountEntity');
      $account = $accountTable->get($data['partner_account_id'], [
        'contain' => ['PartnerAccountLinkPeopleEntity']
      ]);

      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }
      if ((int)$user['partner_id'] !== (int)$account->partner_id) {
        throw new \Exception("You don't have permission to update.");
      }

      $people = [];
      foreach ($account->partner_account_link_people as $p) {
        $tmp = ['id' => $p->id];
        if ((int)$p->people_id === (int)$data['people_id']) {
          $tmp['is_main_point_of_contact'] = true;
        } else {
          $tmp['is_main_point_of_contact'] = false;
        }
        $people[] = $tmp;
      }

      $accountTable->patchEntity($account, ['partner_account_link_people' => $people], [
        'associated' => ['PartnerAccountLinkPeopleEntity']
      ]);
      $accountTable->save($account);

      return $this->setJsonResponse(['success' => true, 'account' => $account]);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }


  public function getS3SignedUploadRequest()
  {
    try {
      $data = $this->request->getData();
      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }
      if ($data['code']) {
        $code = $data['code'];
      } else {
        $code = $this->request->query('code');
      }
      if (empty($user)) {
        if ($code) {
          $ref = $this->checkAutoLoginCode($code);
        }
        if (!$ref || empty($ref['ref'])) {
          throw new \Exception("You don't have permission to see the account.");
        }
      }
      unset($data['code']);
      $s3_response = $this->_uploadDocRequest('Applicants', $data);

      if ($s3_response !== false) {
        return $this->setJsonResponse(array('success' => true, 'url' => $s3_response));
      } else {
        return $this->setJsonResponse(['success' => false]);
      }
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }


  public function updateApplicantUploads()
  {
    try {
      if (!$this->request->is('post')) {
          // Don't throw an exception for OPTIONS requests (preflight CORS)
          if ($this->request->is('options')) {
              return $this->setJsonResponse(['success' => true], 200);
          }
          throw new \Exception("Only POST request is allowed.");
      }
      $data = $this->request->getData();

      if (empty($data['people_id'])) {
        throw new \Exception("Required field is missing: people_id");
      }

      $accountPeopleTable = TableRegistry::getTableLocator()->get('PartnerAccountPeopleEntity');

      $account_people = $accountPeopleTable->get($data['people_id']);

      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }

      if (empty($user)) {
        $ref = $this->checkAutoLoginCode($this->request->query('code'));
        if (empty($ref['ref'])) {
          throw new \Exception("You don't have permission to see the account.");
        }
      } else {
        if ((int)$user['partner_id'] !== (int)$account_people->partner_id) {
          throw new \Exception("You don't have permission to update.");
        }
      }

      $partnerApplicantGroupUploadsTable = TableRegistry::getTableLocator()->get('PartnerApplicantGroupUploadsEntity');

      if (!empty($data['partner_applicant_group_uploads']['partner_applicant_group_upload_id'])) {
        $partner_applicant_group_uploads = $partnerApplicantGroupUploadsTable->get($data['partner_applicant_group_uploads']['partner_applicant_group_upload_id'], [
          'contain' => ['PartnerApplicantGroupUploadsMetaEntity']
        ]);
        $partnerApplicantGroupUploadsTable->patchEntity($partner_applicant_group_uploads, $data['partner_applicant_group_uploads'], [
          'associated' => ['PartnerApplicantGroupUploadsMetaEntity']
        ]);
      } else {
        if (getenv('LEND_ENV') != 2 && $data['partner_applicant_group_uploads']['full_path']) {
            $path = $data['partner_applicant_group_uploads']['full_path'];
            if (strpos($path, 'DevTeam/') !== 0) {
              $path = 'DevTeam/' . $path;
            }
            $data['partner_applicant_group_uploads']['full_path'] = $path;
        }
        $data['partner_applicant_group_uploads']['status'] = 'Active';
        $data['partner_applicant_group_uploads']['include_for_lenders'] = 1;
        $data['partner_applicant_group_uploads']['people_id'] = $account_people->id;
        $partner_applicant_group_uploads = $partnerApplicantGroupUploadsTable->newEntity($data['partner_applicant_group_uploads'], [
          'associated' => ['PartnerApplicantGroupUploadsMetaEntity']
        ]);
      }
      $partnerApplicantGroupUploadsTable->save($partner_applicant_group_uploads);

      return $this->setJsonResponse(['success' => true, 'partner_applicant_group_uploads' => $partner_applicant_group_uploads]);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }


  public function updateUploadsRequested()
  {
    try {
      if (!$this->request->is('post')) {
        throw new \Exception("Only POST request is allowed.");
      }
      $data = $this->request->getData();

      if (empty($data['people_id'])) {
        throw new \Exception("Required field is missing: people_id");
      }

      $accountPeopleTable = TableRegistry::getTableLocator()->get('PartnerAccountPeopleEntity');
  
      $account_people = $accountPeopleTable->get($data['people_id']);
  
      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }

      if (empty($user)) {
        $ref = $this->checkAutoLoginCode($this->request->query('code'));
        if (empty($ref['ref'])) {
          throw new \Exception("You don't have permission to see the account.");
        }
      } else {
        if ((int)$user['partner_id'] !== (int)$account_people->partner_id) {
          throw new \Exception("You don't have permission to update.");
        }
      }

      $partnerApplicantGroupUploadsRequestedTable = TableRegistry::getTableLocator()->get('PartnerApplicantGroupUploadsRequestedEntity');

      foreach ($data['partner_applicant_group_uploads_requested'] as $key => $item) {
        if (!empty($data['partner_applicant_group_uploads_requested'][$key]['partner_applicant_group_uploads_requested_id'])) {
          $partner_applicant_group_uploads_requested = $partnerApplicantGroupUploadsRequestedTable->get($data['partner_applicant_group_uploads_requested'][$key]['partner_applicant_group_uploads_requested_id']);
          $partnerApplicantGroupUploadsRequestedTable->patchEntity($partner_applicant_group_uploads_requested, $data['partner_applicant_group_uploads_requested'][$key]);
        } else {
          $data['partner_applicant_group_uploads_requested'][$key]['status'] = true;
          $partner_applicant_group_uploads_requested = $partnerApplicantGroupUploadsRequestedTable->newEntity($data['partner_applicant_group_uploads_requested'][$key]);
        }
        $partnerApplicantGroupUploadsRequestedTable->save($partner_applicant_group_uploads_requested);
      }

      $sendEmail = null;
      if (!empty($data['partner_applicant_group_uploads_requested'])) {
        foreach ($data['partner_applicant_group_uploads_requested'] as $key => $item) {
          if ($data['partner_applicant_group_uploads_requested'][$key]['status'] == true) {
            $sendEmail = true;
          }
        }
        if ($sendEmail) {
          $notification_data = [
            'applicantIds' => array($data['people_id']),
            'groupId' => array_unique(array_column($data['partner_applicant_group_uploads_requested'], 'people_id')),
            'is_attach' => true,
            'name' => $account_people->first_name . ' ' . $account_people->last_name,
            'message' => @$data['partner_applicant_group_uploads_requested'][0]['notes'],
          ];
          if (!empty($notification_data['applicantIds'])) {
            $this->sendAskApplicant($notification_data);
          }
        }
      }

      return $this->setJsonResponse(['success' => true, 'partner_account_uploads_requested' => $partner_applicant_group_uploads_requested]);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }


  public function sendAskApplicant($manually_data = false)
  {
    /**
     * The email would contain the list of requested file which were not previously attached
     * If the privacy form is selected and was not attached previously, in the email there should be a link to download the privacy form
     */
    $user = $this->Auth->user();
    if (empty($user)) {
      $user = $this->Auth->identify();
    }
    $partnerId = (int)$user['partner_id'];
    $partnerUserId = (int)$user['partner_user_id'];
    $accountType = $user['account_type'];
    $requestedFiles = "";
    $partnerEmail = (empty($user['email'])) ? '' : $user['email'];
    if ((!empty($partnerId) && $partnerId === 2)
      || ($accountType !== 'Lend Staff' && empty($partnerId))
      || empty($partnerUserId)
    ) {  //demo account or un-authorized
      if (empty($manually_data))
        return $this->setJsonResponse(array('success' => false));
      else
        return false;
    }

    if (empty($manually_data))
      $data = $this->request->getData();
    else
      $data = $manually_data;
    $applicant = $this->_decideEmailCode($data);

    $emailed = []; //prevent double email if owner id's share the same one
    foreach ($applicant as $applicantId => $task) {
      $applicant_note = !empty($data['message']) ?  $data['message'] : ''; //needs to be inside loop - Being mutated by getRequestedFiles below
      $applicantCode = (new LendInternalAuth)->hashLeadId($data['applicantIds'][0]);

      $accountPeopleTable = TableRegistry::getTableLocator()->get('PartnerAccountPeopleEntity');
      $account_people = $accountPeopleTable->get($data['applicantIds'][0]);

      $to = $account_people->email;
      if (in_array($to, $emailed)) continue;
      $emailed[] = $to;
      $code = $task['code'];
      if ($code == 'AppliAskAttach') {
        $requestedFiles = $this->_getRequestedFiles($applicantId);
        $upload_login_code = $this->createAutoLoginCode('Applicants', 'Documents', $applicantId, $partnerUserId);
        if (!$requestedFiles) continue; // this specific user has no files to request
      }

      if ($applicant_note) $applicant_note =  '' . $applicant_note . "\r\n";
      $additionalCode = [];
      $additionalCode['applicant'] = $applicantCode;
      if (!empty($account_people->first_name)) $additionalCode['first_name'] = $account_people->first_name;
      if (!empty($account_people->last_name)) $additionalCode['last_name'] = $account_people->last_name;
      $additionalCode = http_build_query($additionalCode);

      if ($code == 'AppBSRequest') {
        $additionalCode = false;
        $clientLoginCode = getenv('DOMAIN_BS_SPA') . '?a=' . $data['account_ref'];
      }

      // http_build_query
      $adhocData = [
        'percent_complete'      => @$data['percent_complete'][$applicantId],
        'to'                    => [$to],
        'additionalCode'        => @$additionalCode,
        'bs_spa_link'           => @$clientLoginCode,
        'applicant_note'        => $applicant_note,
        'client_first_name'     => @$account_people->first_name,
        'client_last_name'      => @$account_people->last_name,
        'requested_files'       => @$requestedFiles,
        'ask_applicant_tasks'   => @$task['tasks'],
        'partner_email'         => $partnerEmail,
        'responder'             => @$data['name'],
        'upload_login_code'     => @$upload_login_code,
      ];
      TableRegistry::getTableLocator()->get('PartnerNotifications')->sendPartnerNotifications($user['partner_id'], $code, false, $adhocData);
    }

    if (empty($manually_data))
      return $this->setJsonResponse(array('success' => true));
    else
      return true;
  }


  private function _getRequestedFiles($groupId)
  {

    $partnerApplicantGroupUploadsRequestedTable = TableRegistry::getTableLocator()->get('PartnerApplicantGroupUploadsRequestedEntity');
    $partnerApplicantGroupUploadsMetaTable = TableRegistry::getTableLocator()->get('PartnerApplicantGroupUploadsMetaEntity');
    $partnerApplicantGroupUploadsTable = TableRegistry::getTableLocator()->get('PartnerApplicantGroupUploadsEntity');
    $requested = '';

    $partner_applicant_group_uploads_requested = $partnerApplicantGroupUploadsRequestedTable->find('all')
      ->where(['people_id' => $groupId])
      ->where(['status' => true])
      ->toArray();
    $partner_applicant_group_uploads_requested = json_decode(json_encode($partner_applicant_group_uploads_requested), true);

    foreach ($partner_applicant_group_uploads_requested as $file) {

      $partner_applicant_group_uploads = $partnerApplicantGroupUploadsTable->find('all')
        ->join([
            'table' => 'partner_applicant_group_uploads_meta',
            'alias' => 'meta',
            'type' => 'LEFT',
            'conditions' => 'meta.partner_applicant_group_upload_id = PartnerApplicantGroupUploadsEntity.people_id',
        ])
        ->where(['people_id' => $groupId])
        ->where(['meta.value' => $file['specify']])
        ->toArray();
      $partner_applicant_group_uploads = json_decode(json_encode($partner_applicant_group_uploads), true);

      $partner_applicant_group_uploads_meta = $partnerApplicantGroupUploadsMetaTable->find('all')
        ->where(['partner_applicant_group_upload_id' => $partner_applicant_group_uploads[0]['partner_applicant_group_upload_id']])
        ->toArray();
      $partner_applicant_group_uploads_meta =  json_decode(json_encode($partner_applicant_group_uploads_meta), true);

      if (!empty($partner_applicant_group_uploads_meta)) {
        $uploaded = false;
        foreach ($partner_applicant_group_uploads_meta as $meta) {
          if ($meta['field_name'] === 'acceptable' && $meta['value'] == 'true') $uploaded = true;
        }

        if (!$uploaded) {
          $requested .= '&#8226; ' . $file['specify'];
          $requested .= "\r\n";
        }
      } else {
        $requested .= '&#8226; ' . $file['specify'];
        $requested .= "\r\n";
      }
    }

    return $requested;
  }


  public function viewImage($id, $code="")
  {
    try {
      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }
      if (empty($user)) {
        if (!empty($code)) {
          $ref = $this->checkAutoLoginCode($code);
          if (empty($ref['ref'])) {
            throw new \Exception("You don't have permission to see the account.");
          }
        } else
          throw new \Exception("You don't have permission to see the account.");
      }
      $s3_url = $this->_getS3ViewImageRequest('Applicants', $id);

      if ($s3_url !== false) {
        return $this->setJsonResponse(array('success' => true, 'url' => $s3_url));
      } else {
        return $this->setJsonResponse(['success' => false]);
      }
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }


  public function getDownloadedFiles()
  {
    try {
      $data = $this->request->getData();

      $s3_url = $this->_getS3ZipFile($data);

      if ($s3_url !== false) {
        return $this->setJsonResponse(array('success' => true, 'url' => $s3_url));
      } else {
        return $this->setJsonResponse(['success' => false]);
      }
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }


  private function _checkExists($data)
  {
    try {
      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }
      $accountPeopleTable = TableRegistry::getTableLocator()->get('PartnerAccountPeopleEntity');
      $orWhere = [];
      if (!empty($data['dob'])) {
        $orWhere[] = ['PartnerAccountPeopleEntity.dob' => date('Y-m-d', strtotime($data['dob']))];
      }
      if (!empty($data['mobile'])) {
        $orWhere[] = ['PartnerAccountPeopleEntity.mobile' => $data['mobile']];
      }
      if (!empty($data['phone'])) {
        $orWhere[] = ['PartnerAccountPeopleEntity.phone' => $data['phone']];
      }
      if (!empty($data['email'])) {
        $orWhere[] = ['PartnerAccountPeopleEntity.email' => $data['email']];
      }
      $exists = $accountPeopleTable->find('all')
                                    ->where(['PartnerAccountPeopleEntity.partner_id' => $user['partner_id']])
                                    ->where(['PartnerAccountPeopleEntity.first_name' => $data['first_name']])
                                    ->where(['PartnerAccountPeopleEntity.last_name' => $data['last_name']])
                                    ->where(['PartnerAccountPeopleEntity.dob' => date('Y-m-d', strtotime($data['dob']))])
                                    ->where(['PartnerAccountPeopleEntity.status' => 'active'])
                                    ->where(['or' => $orWhere])
                                    ->toArray();
      return $exists;
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }



  public function addApplicantNotes()
  {

    try {
      $data = $this->request->getData();
      //appicant_ref is converting to people_id ==> beforeFilter
      if (empty($data['people_id'])) {
        throw new \Exception("Required field is missing: people_id");
      }

      $partner_account_people_note_table = TableRegistry::getTableLocator()->get('PartnerAccountPeopleNotesEntity');

      $params['notes'] = $this->request->getData('notes');
      $params['created'] = date('Y-m-d H:i:s');
      $params['is_pinned'] = $this->request->getData('is_pinned') ?? false;
      $params['partner_account_people_id'] = $data['people_id'];

      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }
      $params['partner_user_id'] = $user['partner_user_id'];

      $lead_note = $partner_account_people_note_table->newEntity($params);
      $partner_account_people_note_table->save($lead_note);

      return $this->setJsonResponse(["success" => true, "message" => "added lead notes"]);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(["success" => false, "message" => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  public function deleteApplicantNote()
  {
    try {
      $data = $this->request->getData();
      $note_id = $data['note_id'];

      $leadNotesTable = TableRegistry::getTableLocator()->get('PartnerAccountPeopleNotesEntity');
      $note = $leadNotesTable->get($note_id);
      $note->status = 'Deleted';
      if ($leadNotesTable->save($note)) {
        return $this->setJsonResponse(['success' => true]);
      } else {
        throw new \Exception("Error updating note status");
      }
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }


  public function editApplicantNotes()
  {
    try {
      $data = $this->request->getData();
      $note_id = $data['note_id'];
      $leadNotesTable = TableRegistry::getTableLocator()->get('PartnerAccountPeopleNotesEntity');
      $note = $leadNotesTable->get($note_id);
      $note->status = 'Edited';
      if ($leadNotesTable->save($note)) {
        return $this->setJsonResponse(['success' => true]);
      } else {
        throw new \Exception("Error updating note status");
      }
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  public function togglePinApplicantNotes()
  {
    try {
      $data      = $this->request->getData();
      $note_id   = $data['note_id'];
      $is_pinned = $data['is_pinned'];
      $leadNotesTable = TableRegistry::getTableLocator()->get('PartnerAccountPeopleNotesEntity');
      $note = $leadNotesTable->get($note_id);
      $note->is_pinned = $is_pinned;

      if ($leadNotesTable->save($note)) {
        return $this->setJsonResponse(['success' => true], 200);
      } else {
        throw new \Exception("Error updating note status");
      }
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  public function updateLenderNotes()
  {
    try {
      $data = $this->request->getData();
      //appicant_ref is converting to people_id ==> beforeFilter
      if (empty($data['people_id'])) {
        throw new \Exception("Required field is missing: people_id");
      }


      $partnerAccountPeopleTable = TableRegistry::getTableLocator()->get('PartnerAccountPeopleEntity');

      $params['additional_info'] = $this->request->getData('additional_info');

      $partnerAccountPeople = $partnerAccountPeopleTable->get(['id' => $data['people_id']]);
      $partnerAccountPeople = $partnerAccountPeopleTable->patchEntity($partnerAccountPeople, $params);
      if (!$partnerAccountPeopleTable->save($partnerAccountPeople)) {
        throw new \Exception("Failed to save the updated lender notes");
      }

      return $this->setJsonResponse(["success" => true, "message" => "Lender notes updated successfully"]);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(["success" => false, "message" => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  // delete applicants directly
  public function deleteApplicant()
  {
    try {
      if (!$this->request->is('post')) {
        throw new \Exception("Only POST request is allowed.");
      }

      $data = $this->request->getData();
      if (empty($data['people_id'])) {
        throw new \Exception("Required field is missing: people_id");
      }

      $applicantTable = TableRegistry::getTableLocator()->get('PartnerAccountPeopleEntity');
      $applicant = $applicantTable->get($data['people_id']);


      // get lead_owners where partner_account_people_id
      $leadOwnersTable = TableRegistry::getTableLocator()->get('LeadOwnersEntity');
      $leadOwners = $leadOwnersTable->find('all')
        ->where(['partner_account_people_id' => $data['people_id']])
        ->toArray();

      // check how many lead_owners with lead_id
      foreach ($leadOwners as $leadOwner) {
        $leadOwnersCount = $leadOwnersTable->find('all')
          ->where(['lead_id' => $leadOwner->lead_id])
          ->count();
        if ($leadOwnersCount == 1) {
          $leadTable = TableRegistry::getTableLocator()->get('LeadEntity');
          $lead = $leadTable->get($leadOwner->lead_id);
          $lead->is_archived = 2;
          $leadTable->save($lead);
        }
      }
      $leadOwnersTable->updateAll(
        ['status' => 'inactive'],
        [
          'partner_account_people_id' => $data['people_id'],
          'status' => 'active' // skip the deleted lead_owner
        ]
      );

      $applicant->status = 'inactive';

      if ($applicantTable->save($applicant)) {
        return $this->setJsonResponse(['success' => true, 'message' => 'Applicant successfully removed']);
      } else {
        throw new \Exception("Failed to update applicant status.");
      }
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()]);
    }
  }

  public function rollBackDeletedApplicant()
  {
    try {
      if (!$this->request->is('post')) {
        throw new \Exception("Only POST request is allowed.");
      }

      $data = $this->request->getData();
      if (empty($data['people_id'])) {
        throw new \Exception("Required field is missing: people_id");
      }

      // Get the applicant
      $applicantTable = TableRegistry::getTableLocator()->get('PartnerAccountPeopleEntity');
      $applicant = $applicantTable->get($data['people_id']);

      // Get lead owners associated with this applicant
      $leadOwnersTable = TableRegistry::getTableLocator()->get('LeadOwnersEntity');
      $leadOwners = $leadOwnersTable->find('all')
        ->where([
          'partner_account_people_id' => $data['people_id'],
          'status' => 'inactive'
        ])
        ->toArray();

      // Reactivate leads
      $leadTable = TableRegistry::getTableLocator()->get('LeadEntity');
      foreach ($leadOwners as $leadOwner) {
        $lead = $leadTable->get($leadOwner->lead_id);
        if ($lead->is_archived === 2) { // Only restore if it was archived due to deletion
          $lead->is_archived = 0;
          $leadTable->save($lead);
        }
      }

      // Reactivate lead owners
      $leadOwnersTable->updateAll(
        ['status' => 'active'],
        [
          'partner_account_people_id' => $data['people_id'],
          'status' => 'inactive'
        ]
      );

      // Reactivate applicant
      $applicant->status = 'active';
      
      if ($applicantTable->save($applicant)) {
        return $this->setJsonResponse([
          'success' => true, 
          'message' => 'Applicant successfully restored',
          'applicant' => $applicant
        ]);
      } else {
        throw new \Exception("Failed to restore applicant status.");
      }

    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse([
        'success' => false, 
        'message' => $e->getMessage()
      ], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  private function _sanitizeData($data)
  {
    // make white list for columns
    $whitelist = TableRegistry::getTableLocator()->get('PartnerAccountPeopleEntity')->getSchema()->columns();
    // prepend entity name to the column name
    $whitelist = array_map(function ($col) {
      return 'PartnerAccountPeopleEntity.'.$col;
    }, $whitelist);

    // Sanitize sorting field:
    if (!empty($data['sorting'])) {
      $sorting_field = array_keys($data['sorting'])[0];
      if (!in_array($sorting_field, $whitelist)) {
        unset($data['sorting']);
      }
    }

    // Sanitize filters field:
    if (!empty($data['filters'])) {
      foreach ($data['filters'] as $key => $value) {
        if (!in_array($key, $whitelist)) {
          unset($data['filters'][$key]);
        }
      }
      if (empty($data['filters'])) {
        unset($data['filters']);
      }
    }

    // Sanitize search field:
    if (!empty($data['search'])) {
      if (!in_array($data['search']['field'], $whitelist)) {
        unset($data['search']);
      }
    }

    // Sanitize extraFetchData field:
    if (!empty($data['extraFetchData'])) {
      foreach ($data['extraFetchData'] as $key => $value) {
        if (!in_array($value['field'], $whitelist)) {
          unset($data['extraFetchData'][$key]);
        }
      }
      if (empty($data['extraFetchData'])) {
        unset($data['extraFetchData']);
      }
    }
    return $data;
  }
}
