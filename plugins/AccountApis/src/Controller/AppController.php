<?php

namespace AccountApis\Controller;

use Cake\ORM\TableRegistry;
use Cake\Event\Event;
use Cake\Utility\Security;
use Firebase\JWT\JWT;
use Cake\Log\Log;
use Cake\Core\Configure;
use \Aws\S3\S3Client;
use App\Lend\LendInternalAuth;
use App\Controller\AppController as BaseController;
use App\Traits\S3Trait;
use App\Lend\CurlHelper;

class AppController extends BaseController
{
  use S3Trait;
  
  public function initialize() {
		parent::initialize();
	}


	public function beforeFilter(Event $event){
		parent::beforeFilter($event);
	}

	
	public function _generateCondition($data) {
    $condition = [];
    switch ($data['operation']) {
      case 'equal':
        $condition = [$data['field'] => $data['value']];
        break;

      case 'contains':
          if ($data['field'] === 'PartnerAccountPeopleEntity.first_name'){
            $condition = [];
            $searchWords = explode(' ', $data['value']);
            if (count($searchWords) >= 2) {
                $condition = [
                    'AND' => [
                        'PartnerAccountPeopleEntity.first_name LIKE' => '%' . $searchWords[0] . '%',
                        'PartnerAccountPeopleEntity.last_name LIKE' => '%' . $searchWords[1] . '%'
                    ]
                ];
            } else {
                $condition = [
                  'OR' => [
                      'PartnerAccountPeopleEntity.first_name LIKE' => '%' . $data['value'] . '%',
                      'PartnerAccountPeopleEntity.last_name LIKE' => '%' . $data['value'] . '%'
                  ]
              ];
            }
          } else {
              if (is_array($data['value'])) {
                  $condition = [$data['field'] . ' IN' => $data['value']];
              } else {
                  $condition = [$data['field'] . ' LIKE' => '%' . $data['value'] . '%'];
              }
          }
          break;
          
      case 'more_than':
        $condition = [$data['field'].' >=' => $data['value']];
        break;

      case 'less_than':
        $condition = [$data['field'].' <=' => $data['value']];
        break;
    }

    return $condition;
  }


	public function _getBsa($account_id) {
    try {
      $lendconfig = Configure::read('Lend');
      $response = (new \Cake\Http\Client)->post(getenv('DOMAIN_BSS').'/get-final-analysis-by-partner-account-id', ['partnerAccountId'=>$account_id], ['headers'=>['x-api-key'=>$lendconfig['bs_service_auth_key']]]);
      if($response->json['success']){
        $bs_summary = false;
        if (!empty($response->json['data']) && !empty($response->json['data'][0])) {
          $bsa = $response->json['data'][0];
          $bs_summary = $bsa;
          $bs_summary['avg_monthly_rev_180']     = $bsa['avg_mto_180'];
          $bs_summary['avg_monthly_rev_dep_180'] = $bsa['avg_num_mth_deps_180'];
          $bs_summary['avg_day_end_bal_180']     = $bsa['avg_day_end_bal_180'];
          $bs_summary['days_neg_180']            = $bsa['days_neg_180'];
          $bs_summary['dishonours_180']          = $bsa['days_dishonour_cnt_180'];
          $bs_summary['is_account_level']        = $bsa['is_account_level'];
          $bs_summary['cfl_detected']            = $bsa['total_lenders'] > 0 ? true : false;
          $bs_summary['total_lenders']           = $bsa['total_lenders'];
        }
        return $bs_summary;
      }else{
        return false;
      }
    } catch(\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }


  public function _getBs($account_id) {
    try {
      $lendconfig = Configure::read('Lend');
      $response = (new \Cake\Http\Client)->post(getenv('DOMAIN_BSS').'/statements-account-level/', ['partnerAccountId'=>$account_id], ['headers'=>['x-api-key'=>$lendconfig['bs_service_auth_key']]]);
      if($response->json['success']){
        return $response->json['data'];
      }else{
        return [];
      }
    } catch(\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }

  public function _getBsCustomer($account_id) {
    try {
      $curl = new CurlHelper(getenv('DOMAIN_BANKFEEDS') . "/get-customer-by-partner-account-id");
      $response = $curl->post(["partnerAccountId" => $account_id], true, ["x-api-key" => getenv('BANK_STATEMENT_API_KEY')]);
      if ($response['success'] == true && !empty($response['data'])) {
        return $response['data'];
      }
      return [];
    } catch(\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }
  
  public function _getS3SignedUploadRequest($fullpath,  $data, $minutes='2') {
    try {
      if (!$this->__validateUpload($data)) {
        return new \Exception("File is not a valid file.");
      }

      if (!$source_file = $this->__validatePdfUpload($data)) {
        throw new \Exception('File is not a valid PDF');
      }

      $bucket = Configure::read('Lend.AWS.bucket');
      // $endpoint = 'https://s3.'.$region.'/amazonaws.com/' .$bucket;

      if (!$this->s3Client) $this->prepareS3(); // Initiate connection if not already
      // Prepare a PutObject command.
      $options = [
        'Bucket' => $bucket,
        'Key' => str_replace('\\', '/', $fullpath),
        'ContentType' => $data['type'],
        'SourceFile' => $source_file
      ];
      //this is just a logo so needs to be publicly viewable
      // if(stripos($fullpath, 'partner_logos')!==false)
        // $options['ACL'] = 'public-read';

      $this->s3Client->putObject($options);
  
      $cmd = $this->s3Client->getCommand('putObject', $options);
  
      // Create a special link that temporarily grants access for 2 minutes
      $request = $this->s3Client->createPresignedRequest($cmd, '+'.$minutes.' minutes');
      unlink($source_file);

      return $request;
    } catch(\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }

  public function _createSignedRequest($fullpath, $minutes='2', $forcedownload=false) {
    if (!$this->s3Client) $this->prepareS3(); // Initiate connection if not already

    // This is the command we want to run
    $cmd = $this->s3Client->getCommand('GetObject', [
        // 'Bucket' => 'lend.com.au-leaduploads',
        'Bucket' => Configure::read('Lend.AWS.bucket'),
      	'Key' => $fullpath,
      ]);

    // Create a special link that temporarily grants access for 2 minutes
    $request = $this->s3Client->createPresignedRequest($cmd, '+'.$minutes.' minutes');

    return (string)$request->getUri();
  }

  public function _uploadDocRequest($source, $data) {
    try {
      $devFolder = '';
      if((string)getenv('LEND_ENV') !== '2'){
        $devFolder = 'DevTeam/';
      }

      if ($source === 'Accounts') {
        $fullpath = $devFolder . 'account-uploads/' . $data['accountRef'] . '/' . $data['file']['name'];
      } else {
        $fullpath = $devFolder . 'applicant-uploads/' . $data['groupId'] . '/' . $data['file']['name'];
      }

      $request = $this->_getS3SignedUploadRequest($fullpath,  $data['file']);

      return [
        'method' => $request->getMethod(),
        'url' => (string) $request->getUri(),
        'fields' => [],
        'headers' => [
          'content-type' => $data['contentType'],
        ],
      ];
    } catch(\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }

  public function _uploadRequest($data) {
    try {
      $devFolder = '';
      if((string)getenv('LEND_ENV') !== '2'){
        $devFolder = 'DevTeam/';
      }
      // $fullpath = $devFolder . 'account-uploads/' . $data['accountRef'] . '/' . $data['file']['name'];
      $fullpath = $devFolder . $data['path'];
      if($data['isPublic'] == 1){
        return $this->_generalUploadS3($fullpath,  $data['file'], 'public-read');
      }else{
        return $this->_generalUploadS3($fullpath,  $data['file']);
      }
    } catch(\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }

  public function _generalUploadS3($fullpath, $data, $acl = null){
    $bucket = Configure::read('Lend.AWS.bucket');
            
    if (!$this->s3Client) $this->prepareS3(); // Initiate connection if not already
    try {
      $options = [
        'Bucket' => $bucket,
        'Key' => str_replace('\\', '/', $fullpath),
        'ContentType' => $data['type'],
        'SourceFile' => $data['tmp_name']
      ];
      if($acl){
        $options['ACL'] = $acl;
      }
        // Upload data.
        $result = $this->s3Client->putObject($options);
        // Print the URL to the object.
        return ['file' => $fullpath, 'url' => $result['ObjectURL'], 'size' => $data['size'], 'type' => $data['type']];
    } catch (\Exception $e) {
      Log::error($e->getMessage());
    }
  }

  public function _getS3ViewImageRequest($source, $id) {
    try {
      if (!$id) return $this->setJsonResponse(array('success'=>false, 'message'=>'No upload id supplied'));

      if ($source === 'Accounts') {
        $data = TableRegistry::getTableLocator()->get('PartnerAccountUploadsEntity')->get(["partner_account_upload_id" => $id]);
      } else {
        $data = TableRegistry::getTableLocator()->get('PartnerApplicantGroupUploadsEntity')->get(["partner_applicant_group_upload_id" => $id]);
      }

      if (empty($data)) return $this->setJsonResponse(array('success'=>true, 'message'=>'No file found with that upload id'));

      $fullpath = $data['full_path'].($data['full_path'][-1] === '/' ? "": '/').$data['name'];
      $url = $this->_createSignedRequest($fullpath, '2');

      return $url;
    } catch(\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }

  public function _getS3ZipFile($data) {
    $url = 'https://znhmt5qzw5.execute-api.ap-southeast-2.amazonaws.com/production/run';

    try {
      if (empty($data)) throw new \Exception('Missing file data');

      $result = $this->curl($url, $data, 'POST');

      if (empty($result)) throw new \Exception('Could not reach the function temporarily');

      return $result;
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }

  private function curl($url, $post, $type, $headers = []) {
    $curl = curl_init();
    $entries = ["accept: application/json", "content-type: application/json"];

    foreach($headers as $header) {
      $entries[] = $header;
    }
    curl_setopt_array($curl, array(
      CURLOPT_URL             => $url,
      CURLOPT_RETURNTRANSFER  => true,
      CURLOPT_ENCODING        => "",
      CURLOPT_MAXREDIRS       => 10,
      CURLOPT_TIMEOUT         => 30,
      CURLOPT_HTTP_VERSION    => CURL_HTTP_VERSION_1_1,
      CURLOPT_CUSTOMREQUEST   => $type,
      CURLOPT_HTTPHEADER      => $entries
    ) + ($type === 'POST' ? array(
      CURLOPT_POST            => true,
      CURLOPT_POSTFIELDS      => json_encode($post),
    ) : array()));

    $result = curl_exec($curl);

    if($this->isJson($result)) {
      $result = json_decode($result, true);
    }

    return $result;
  }

  private function isJson($string) {
    if (is_object(json_decode($string))) {
        return true;
    }else{
        return false;
    }
  }

    /* ************************************************
    Example of the input from `$data['ask_applicants']`:
    {
      ...,
      "ask_applicants": {
        "application": [owner_id1, owner_id2, ...],
        "bs": [owner_id1, owner_id2, ...],
        "consent": [owner_id1, owner_id2, ...]
      },
      ...
    }
    -----------------------------------------------------
    Example of the response:
    {
      "owner_id1": {
        "code": "SendToAppl"
      },
      "owner_id2": {
        "code": "AppBSRequest"
      },
      "owner_id3": {
        "code": "SendToApplMultiTasks",
        "tasks": ["application", "bs", "consent", "privacy"]
      }
    }
    ************************************************ */
  public function _decideEmailCode($data){
    $this->loadModel('LeadOwners');
    $result = [];
    $grouped = [];


    if(!empty($data['is_attach']) && !empty($data['ownerIds'])){
      foreach($data['ownerIds'] as $owner_id){
        $result[$owner_id] = ['code' =>'AskAttach'];
      }
      return $result;
    }

    // For file attchments:
    if(!empty($data['is_attach']) && !empty($data['accountIds'])){
      foreach($data['accountIds'] as $owner_id){
        $result[$owner_id] = ['code' =>'AppliAskAttach'];
      }
      return $result;
    }

    // For file attchments:
    if(!empty($data['is_attach']) && !empty($data['groupId'])){
      foreach($data['groupId'] as $owner_id){
        $result[$owner_id] = ['code' =>'AppliAskAttach'];
      }
      return $result;
    }

    if(empty($data['ask_applicants'])) return false;

    // Group it by owner_id
    foreach($data['ask_applicants'] as $task=>$owner_ids){
      foreach($owner_ids as $owner_id){
        $grouped[$owner_id][] = $task;
      }
    }

    foreach($grouped as $owner_id=>$tasks){
      // Make consent field empty if `consent` is in task:
      if(in_array('consent', $tasks)){
        $this->LeadOwners->updateLeadOwner(['owner_id'=>$owner_id, 'consent'=>null]);
      }
      // Decide notifiation code:
      if(count($tasks)===1){
        switch(strtolower($tasks[0])){
          case 'application':   $code = 'SendToAppl'; break;
          case 'bs':            $code = 'AppBSRequest'; break;
          case 'consent':       $code = 'AppConsentRequest'; break;
        }
      }else{
        $code = 'SendToApplMultiTasks';
      }
      $result[$owner_id] = ['code' => $code, 'tasks' => $tasks];
    }
    return $result;
  }

  public function createAutoLoginCode($source, $pages, $id, $partnerUserId) {
    // Add expiry date
    $expiry_date = strtotime('+ 14 days');

    if ($source === 'Applicants') {
      $login = (new LendInternalAuth)->hashLeadId($id).'l3nd'.$expiry_date.'l3nd'.$source.'l3nd'.$pages.'l3nd'.$partnerUserId;
    } else {
      $login = $id.'l3nd'.$expiry_date.'l3nd'.$source.'l3nd'.$pages.'l3nd'.$partnerUserId;
    }

    $build_sig = Security::getSalt().'#'.$login; // Build the signature we are expecting on the receiving end
    $build_sig = hash('sha1', $build_sig); // Build the signature we are expecting on the receiving end
    $code = $login.'#'.$build_sig; // Join it via a hash
    if (!empty($additionalCode)) {
      $code .= '#'.$additionalCode;
    }
    $code = base64_encode( $code ); // Base64 Encode it
    $code = "?code=".$code; // Send it back
    
    return $code;
  }
}