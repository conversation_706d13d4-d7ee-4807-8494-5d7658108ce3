<?php
namespace AccountApis\Controller;

use App\Enums\Frequency;
use Cake\ORM\TableRegistry;
use Cake\Log\Log;
use Hashids\Hashids;
use Cake\Http\Client;
use App\Lend\LendInternalAuth;
use App\Service\TaxCalculator;
use Cake\Core\Configure;
use Exception;

// use leadsAPI\Lend

class LeadController extends AppController
{
  public function initialize(){
    parent::initialize();
  }


  public function _generateLeadData($account, $data_in_lead = false, $lead_type="commercial") {
    try {
      if ($data_in_lead) {
        $lead['lead'] = $this->_mapLead($account, $lead_type);
      } else {
        $lead = $this->_mapLead($account, $lead_type);
      }

      $people = array_map(function ($p) {
        $person = $p['partner_account_people'];
        $person['is_main_point_of_contact'] = $p['is_main_point_of_contact'];
        return $person;
      }, $account['partner_account_link_people']);
      
      $lead['owners_all'] = !empty($account['partner_account_link_people']) ? $this->_mapLeadOwners($people, $lead_type) : [];
      if($lead_type == "commercial"){
        $lead['assets'] = !empty($account['assets']) ? $this->_mapAssets($account['assets']) : [];
        $lead['liabilities'] = !empty($account['liabilities']) ? $this->_mapLiabilities($account['liabilities']) : [];
        $lead['references'] = !empty($account['references']) ? $this->_mapReferences($account['references']) : [];
        $lead['all_addresses'] = !empty($account['all_addresses']) ? $this->_mapAddresses($account['all_addresses']) : [];
        $lead['abn_lookup'] = !empty($account['abn_lookup']) ? $this->_mapAbn($account['abn_lookup']) : [];
        $lead['entity_trust'] = !empty($account['entity_trust']) ? $this->_mapEntityTrust($account['entity_trust']) : [];
      }
      return $lead;
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }


  private function _addPartnerAccountUploads($uploads, $lead_id) {
    try {
      $unset_fields = ['partner_account_upload_id', 'created'];
      foreach ($uploads as $k => $e) {
        foreach ($unset_fields as $f) {
          unset($uploads[$k][$f]);
        }
        $uploads[$k]['lead_id'] = $lead_id;
      }

      $partnerLeadUploadsTable = TableRegistry::getTableLocator()->get('PartnerLeadUploadsEntity');
      $partner_lead_uploads = $partnerLeadUploadsTable->newEntities($uploads);
      $partnerLeadUploadsTable->saveMany($partner_lead_uploads);

      return $uploads;
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }    
  }


  private function _addPartnerApplicantGroupUploads($group_id, $lead_id) {
    try {
      $partnerApplicantGroupUploadsTable = TableRegistry::getTableLocator()->get('PartnerApplicantGroupUploadsEntity');

      $partner_applicant_group_uploads = $partnerApplicantGroupUploadsTable->find("all")
        ->where(["people_id" => $group_id])
        ->where(["status" => "Active"])
        ->toArray();

      $unset_fields = ['partner_applicant_group_upload_id', 'created'];
      foreach ($partner_applicant_group_uploads as $k => $e) {
        foreach ($unset_fields as $f) {
          unset($partner_applicant_group_uploads[$k][$f]);
        }
        $partner_applicant_group_uploads[$k]['lead_id'] = $lead_id;
      }

      $partner_applicant_group_uploads = json_decode(json_encode($partner_applicant_group_uploads), true);

      $partnerLeadUploadsTable = TableRegistry::getTableLocator()->get('PartnerLeadUploadsEntity');
      $partner_lead_uploads = $partnerLeadUploadsTable->newEntities($partner_applicant_group_uploads);

      $partnerLeadUploadsTable->saveMany($partner_lead_uploads);

      return $partner_applicant_group_uploads;
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }      
  }



  public function _mapLead($account, $lead_type) {
    try {
      $lead = !empty($account['partner_account_meta']) ? $account['partner_account_meta'] : [];
      $convert_fields = ['registration_date' => 'company_registration_date', 'sub_equipment_id' => 'equipment_id'];
      $unset_fields = ['id', 'partner_account_id', 'industry', 'created', 'updated'];

      foreach ($convert_fields as $account_field => $lead_field) {
        if (isset($lead[$account_field])) {
          $lead[$lead_field] = $lead[$account_field];
          unset($lead[$account_field]);
        }
      }
      foreach ($unset_fields as $f) {
        unset($lead[$f]);
      }
      $lead['partner_id'] = $account['partner_id'];
      $lead['account_id'] = @$account['partner_account_id'];
      if($lead_type === 'commercial') {
        $lead['abn'] = @$account['abn'];
      }
      $lead['status_id'] = 1;
      $lead['partner_status_id'] = 1;
      $lead['source'] = 'Account';
      $lead['call_me_first'] = !empty($account['partner']['call_me_first']);
      // $lead['lead_type'] = $account['account_type'] === 'company' ? 'commercial' : $account['account_type'];
      $lead['lead_type'] = $lead_type;
      $lead['send_type'] = 'Manual';
      //map address
      if($lead_type === 'commercial'){
        if (!empty($account['all_addresses'])) {
          foreach ($account['all_addresses'] as $key => $address) {
            if($address['address_type'] == 'trading' && !$address['date_to']){
              $lead['b_address'] = $address['address'];
              $lead['b_suburb'] = $address['suburb'];
              $lead['b_state'] = $address['state'];
              $lead['b_postcode'] = $address['postcode'];
              $lead['b_country'] = $address['country'];
            }
            if($address['address_type'] == 'business'){
              $lead['r_address'] = $address['address'];
              $lead['r_suburb'] = $address['suburb'];
              $lead['r_state'] = $address['state'];
              $lead['r_postcode'] = $address['postcode'];
              $lead['r_country'] = $address['country'];
            }
          }
        }
        $lead['additional_info'] = @$account['additional_info'];
      }

      // Check BS
      if (!empty($lead['account_id'])) {
        $bs_accounts = $this->_getBs($lead['account_id']);
        if (!empty($bs_accounts)) {
          $lead['statements_uploaded'] = true;
        }
      }

      return $lead;
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }
  
  
  private function _mapLeadOwners($people, $lead_type, $lead = null) {
    try {
      $owners = [];
      $convert_fields = ['id' => 'partner_account_people_id',  
                        'is_main_point_of_contact' => 'point_of_contact'];
      $unset_fields = ['partner_account_id', 'created', 'updated', 'group_id'];
      $unset_associated = ['people_liabilities', 'people_assets', 'people_expenses', 'people_incomes', 'status'];

      foreach ($people as $p) {
        foreach ($convert_fields as $people_field => $owner_field) {
          $p[$owner_field] = $p[$people_field];
          unset($p[$people_field]);
        }
        foreach ($unset_fields as $f) {
          unset($p[$f]);
        }
       
        $incomeShares = $this->_mapOwnerIncomes($p['people_incomes'], $lead);
        if ($incomeShares['incomes']){//owner incomes
          $p['incomes'] = $incomeShares['incomes'];
          unset($incomeShares['incomes']);
        }
        if ($lead !== null)//existing lead
          $p['income_shared'] = $incomeShares;

        $p['expenses'] = $this->_mapOwnerExpenses($p['people_expenses']);
        $p['lead_assets'] = $this->_mapOwnerAssetsLiabilities($p['people_assets']);
        $p['liabilities'] = $this->_mapOwnerAssetsLiabilities($p['people_liabilities']);

        $p['all_employments'] = $this->_mapOwnerEmployments($p['employments']);
        $p['all_addresses'] = $this->_mapOwnerAddresses($p['addresses']);
        // $p['lead_assets'] = $this->_mapAssets($p['assets']);
        // $p['liabilities'] = $this->_mapLiabilities($p['liabilities']);
        foreach ($unset_associated as $f) {
          unset($p[$f]);
        }
        $owners[] = $p;
      }
      return $owners;
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }


  private function _mapOwnerFinance($finance) {
    try {
      $unset_fields = ['lead_owner_finance_id', 'owner_id', 'partner_account_people_id'];
      foreach ($unset_fields as $f) {
        unset($finance[$f]);
      }
      return $finance;
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }

  private function _convertOwnerFinance($incomes, $expenses){
    try {
      $finances = [
        'income_yearly_gross' => 0,//(sum of all incomes) *12
        'income_monthly_net' => 0,//this is gross amount(what user entered)
        'income_monthly_government' => 0,
        'income_monthly_investment' => 0,
        'income_monthly_superannuation' => 0,
        'income_monthly_investment_property' => 0,
        'income_monthly_other' => 0,
        'income_monthly_net_surplus' => 0,//difference between income and expense - monthly

        'expense_monthly_rent_board' => 0,
        'expense_transport' => 0,
        'expense_groceries_pet_care' => 0,
        'expense_clothing_personal_care' => 0,
        'expense_out_of_pocket_healthcare_costs' => 0,
        'expense_private_schooling_and_tuition' => 0,
        'expense_telephone_internet_tv' => 0,
        'expense_utilities' => 0,
        'expense_recreational_entertainment' => 0,
        'expense_private_health_insurance' => 0,
        'expense_child_support' => 0,
        'expense_childcare_and_adult_education' => 0,
        'expense_other_insurance' => 0,
        'expense_other' => 0,
      ];
      //incomes
      $income_yearly_gross = 0;
      $income_monthly_net_surplus = 0;
      $incomeMap = [
        1 => 'income_monthly_net',//Salary/Wages
        2 => 'income_monthly_investment_property',//Rental income
        3 => 'income_monthly_investment',//Other Investments
        4 => 'income_monthly_government',//Centrelink and Family Benefits
        5 => 'income_monthly_superannuation',//Superannuation
        6 => 'income_monthly_other'//Other Income
      ];
      $taxCalculator = new TaxCalculator();
      foreach ($incomes as $income) {
        //all commercial lead - finances amounts are gross amounts
        $tYearleyAmount = $taxCalculator->convertToYearlyAmount(Frequency::Monthly, $income['amount']);
        if ($income['net'] == true)
          $tYearleyAmount = $taxCalculator->netToGross($tYearleyAmount);

        $income_yearly_gross += ($tYearleyAmount);

        $tMonthlyGross = $taxCalculator->convertToMonthlyAmount(Frequency::Annually, $tYearleyAmount);
        $income_monthly_net_surplus +=  $tMonthlyGross;
        if(isset($incomeMap[$income['config_income_id']]))
          $finances[$incomeMap[$income['config_income_id']]] += $tMonthlyGross;
      }
      //expenses
      $expenseMap = [
        1 => 'expense_monthly_rent_board',//Rent/Board
        2 => 'expense_transport',//Vehicles/Transport
        3 => 'expense_groceries_pet_care',//Food/Groceries
        4 => 'expense_clothing_personal_care',//Clothing/Personal care
        5 => 'expense_private_schooling_and_tuition',//Private School Fees
        6 => 'expense_out_of_pocket_healthcare_costs',//Health Insurance and Medical
        7 => 'expense_other_insurance',//Other Insurances
        8 => 'expense_telephone_internet_tv',//Phone, Internet and Cable
        9 => 'expense_utilities',//Utilities, Rates and Body Corp
        10 => 'expense_recreational_entertainment',//Recreational and Entertainment
        11 => 'expense_child_support',//Child and Spouse Support
        12 => 'expense_childcare_and_adult_education',//Childcare and Public School Fees
        13 => 'expense_other',//Other Monthly Expenses
      ];

      foreach ($expenses as $k => $e) {
        $amount = $expenses[$k]['amount'];
        $income_monthly_net_surplus -= $amount;
        if(isset($expenseMap[$expenses[$k]['config_expense_id']]))
          $finances[$expenseMap[$expenses[$k]['config_expense_id']]] += $amount;
      }
      $finances['income_yearly_gross'] = $income_yearly_gross;
      if($income_monthly_net_surplus > 0)
        $finances['income_monthly_net_surplus'] = $income_monthly_net_surplus;

      return $finances;
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }

  private function _mapOwnerIncomes($incomes, $lead){
    try {
      $income_shared = [];
      if($lead !== null){
        $taxCalculator = new TaxCalculator();
        $papIncomes = [];
        foreach($incomes as $papIncome){
          //Convert to Yearly Amount
          $pGrossYearlyAmount = $taxCalculator->convertToYearlyAmount($papIncome['frequency'], $papIncome['user_input_amount']);
          if($papIncome['net']){
            $pGrossYearlyAmount = $taxCalculator->netToGross($pGrossYearlyAmount);
          }
          
          $grossAmount = 0;
          $netAmount = 0;

          if($papIncome['net'] == true){
            $netAmount = $papIncome['user_input_amount'];
            $grossAmount = $taxCalculator->convertFrequencyAmount(Frequency::Annually, $papIncome['frequency'], $pGrossYearlyAmount);
          } elseif ($taxCalculator->isNonTaxable($papIncome['config_income_id'])){
            $netAmount = $papIncome['user_input_amount'];
            $grossAmount = $papIncome['user_input_amount'];
          } else {
            $grossAmount = $papIncome['user_input_amount'];
            $netAmount = $taxCalculator->convertFrequencyAmount(Frequency::Annually, $papIncome['frequency'], $taxCalculator->grossToNet($pGrossYearlyAmount));
          }

          $papIncomes[$papIncome['config_income_id']] = [
            'gross_user_input'=> $grossAmount, 
            'net_user_input'=> $netAmount, 
            'is_net'=>$papIncome['net'], 
            'config_income_id'=> $papIncome['config_income_id'],
            'frequency'=> $papIncome['frequency'],
            'user_input_amount'=> $papIncome['user_input_amount']
          ];
        }
        $incomeData = [];
        $updatedShares = [];
        //get active owners
        $activeOwnerIds = [];
        foreach($lead['owners_all'] as $owner){
          $activeOwnerIds[] = $owner['owner_id'];
        }

        $leadIncomeConfigIds = [];
        foreach($lead['incomes'] as $income){
          if ($income['config_income_id'] === 1) continue;

          $leadIncomeConfigIds[] = $income['config_income_id'];
          if(isset($papIncomes[$income['config_income_id']])){
            $originalAmount = $income['user_input_amount'];
            $shareAmount = 0;
            $incomeRow = [];
            $incomeRow['id'] = $income['id'];
            $incomeRow['frequency'] = $income['frequency'];
            $incomeRow['config_income_id'] = $income['config_income_id'];

            $papIncome = $papIncomes[$income['config_income_id']];

            if($papIncome['frequency'] == $income['frequency']){
              // Frequencies are same
              $shareAmount = $papIncome['user_input_amount'];
              $income['user_input_amount'] += $shareAmount;
            } elseif ($income['net']) {
              // Net Amount Required
              $shareAmount = $taxCalculator->convertFrequencyAmount($papIncome['frequency'], $income['frequency'], $papIncome['net_user_input']);
              $income['user_input_amount'] += $shareAmount;
            } else {
              // Gross Amount Requried
              $shareAmount = $taxCalculator->convertFrequencyAmount($papIncome['frequency'], $income['frequency'], $papIncome['gross_user_input']);
              $income['user_input_amount'] += $shareAmount;
            }
            
            $incomeRow['user_input_amount'] = $income['user_input_amount'];
            $incomeData[$incomeRow['id']] = $incomeRow;
            
            //recalculate share percentage based on new amount
            foreach($income['shared'] as $incomeShare){
              if (!in_array($incomeShare['owner_id'], $activeOwnerIds))
                continue;
              $amount = $originalAmount * ($incomeShare['percent']/100);
              $incomeShare['percent'] = ($amount / $income['user_input_amount']) * 100;
              $updatedShares[$incomeShare['id']] = $incomeShare->toArray();
            }
            //add share for new lead owner
            $income_shared[] = ['income_id' => $income['id'], 'percent' => (($shareAmount / $income['user_input_amount']) * 100)];
          }
        }
        
        //find income config ids that are in new incomes but don't exist in existing lead incomes
        $missingConfigIds = array_diff(array_keys($papIncomes), $leadIncomeConfigIds);
        if(count($missingConfigIds) > 0){//create income rows
          $incomeTable = TableRegistry::getTableLocator()->get('LeadOwnerIncomeEntity');
          $incomesData = [];
          foreach($missingConfigIds as $configId){
            $incomePap = $papIncomes[$configId];
            $incomeRow = [];
            $incomeRow['lead_id'] = $lead['lead_id'];
            $incomeRow['config_income_id'] = $incomePap['config_income_id'];
            $incomeRow['net'] = $incomePap['is_net'];
            $incomeRow['frequency'] = $incomePap['frequency'];
            $incomeRow['user_input_amount'] = $incomePap['user_input_amount'];
            if ($configId === 1){
              $income_shared['incomes'][] = $incomeRow;//save owner income
            }
            else{
              $incomesData[] = $incomeRow;//save lead income
            }
          }
          $incomeRecords = $incomeTable->newEntities($incomesData);
          $incomeTable->saveMany($incomeRecords);
          //create 0% shares for existing owners
          $incomeSharedTable = TableRegistry::getTableLocator()->get('ConIncomeShareEntity');
          $shares = [];
          foreach($incomeRecords as $incomeRecord){
            foreach($activeOwnerIds as $activeOwnerId){
              $shares[] = ['owner_id' => $activeOwnerId, 'income_id' => $incomeRecord->id, 'percent'=> 0];
            }
            //add share for new lead owner
            $income_shared[] = ['income_id' => $incomeRecord->id, 'percent' => 100];
          }
          $shareRecords = $incomeSharedTable->newEntities($shares);
          $incomeSharedTable->saveMany($shareRecords);
        }

        if(count($updatedShares)>0){
          $incomeSharedTable = TableRegistry::getTableLocator()->get('ConIncomeShareEntity');
          $shares = $incomeSharedTable->find('all')->where(['id IN' => array_keys($updatedShares)]);
          $shares = $incomeSharedTable->patchEntities($shares, $updatedShares);
          $incomeSharedTable->saveMany($shares);
        }
        if(count($incomeData)>0){
          $incomeTable = TableRegistry::getTableLocator()->get('LeadOwnerIncomeEntity');
          $incomes = $incomeTable->find('all')->where(['id IN' => array_keys($incomeData)])->contain(['ConIncomeShareEntity']);
          $incomes = $incomeTable->patchEntities($incomes, $incomeData, ['associated' => ['ConIncomeShareEntity']]);
          $incomeTable->saveMany($incomes);
        }
      }
      else{
        foreach($incomes as $papIncome){
          $income = [];
          $income['config_income_id'] = $papIncome['config_income_id'];
          if($papIncome['net'] == true){
            $income['amount'] = $papIncome['net_amount'];
            $income['net_amount'] = $papIncome['net_amount'];
          }
          else{
            $income['amount'] = $papIncome['gross_amount'];
            $income['gross_amount'] = $papIncome['gross_amount'];
          }
          $income['net'] = $papIncome['net'];
          $income_shared['incomes'][] = $income;
        }
      }
      return $income_shared;
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }

  /**
   * Get the ratio for net to gross and gross to net conversions based on income
   * @param mixed $incomes
   * @return array
   */

  private function _mapOwnerExpenses($expenses){
    try {
      $unset_fields = ['id', 'source_id', 'partner_account_people_id', 'created', 'updated'];
      $unset_fields_breakdown = ['id', 'source_id', 'expense_id', 'created', 'updated'];
      foreach ($expenses as $k => $e) {
        foreach ($unset_fields as $f) {
          unset($expenses[$k][$f]);
        }
        foreach($expenses[$k]['breakdown'] as $i => $b){
          foreach ($unset_fields_breakdown as $f) {
            unset($expenses[$k]['breakdown'][$f]);
          }
        }
        $expenses[$k]['lead_owner_expenses_breakdown'] = $expenses[$k]['breakdown'];
        unset($expenses[$k]['breakdown']);
      }
      return $expenses;
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }

  private function _recalculateIncome($lead){
    $allIncomes = [];
    if($lead['owners_all']){
      foreach($lead['owners_all'] as $owner){
        if($owner['incomes']){
          foreach($owner['incomes'] as $income){
            $allIncomes[] = [
              "config_income_id" => $income['config_income_id'],
              "amount" => $income['amount'],
              "lead_id" => $lead['lead_id'],
              "owner_id" => $owner['owner_id'],
              "net" => $income['net'],
            ];
          }
        }
      }
    }
    $mergedIncomes = [];
    $taxCalculator = new TaxCalculator();
    foreach($allIncomes as $income){
      if($income['amount'] <= 0){
        continue;
      }
      
      if($income['config_income_id'] == Configure::read('Lend.SALARY_INCOME_CONFIG_ID') || $income['config_income_id'] == Configure::read('Lend.SPOUSE_INCOME_CONFIG_ID')){
        $mergedIncomes[] = $income;
        continue;
      }
      $income['amount'] = $income['net'] ? $taxCalculator->convertNetToGrossWithFrequency($income['amount'], Frequency::Monthly): $income['amount'];
      $income['net'] = 0;
      if(isset($mergedIncomes[$income['config_income_id']])){
        $mergedIncomes[$income['config_income_id']+"_"]['amount'] += $income['amount'];
      }else{
        $mergedIncomes[$income['config_income_id']+"_"] = $income;
      }
      $mergedIncomes[$income['config_income_id']+"_"]['shared'][] = ["owner_id" => $income['owner_id'],"amount" => $income['amount']];
    }
    foreach ($mergedIncomes as $type => $income){
      if($income['shared']){
        foreach ($income['shared'] as $key => $share) {
          $mergedIncomes[$type]["shared"][$key] = [
            "owner_id" => $share['owner_id'],
            "percent" => $share['amount']  * 100 / $income['amount']
          ];
        }
      }
    }
    $incomeTable = TableRegistry::getTableLocator()->get('LeadOwnerIncomeEntity');
    $incomeTable->deleteAll(['lead_id' => $lead['lead_id']]);
    $entities = $incomeTable->newEntities(array_values($mergedIncomes));
    $incomeTable->saveMany($entities);
  }

  private function _recalculateExpenses($lead){
    $allExpenses = [];
    if($lead['owners_all']){
      foreach($lead['owners_all'] as $owner){
        if($owner['expenses']){
          foreach($owner['expenses'] as $expense){
            $allExpenses[] = [
              "config_expense_id" => $expense['config_expense_id'],
              "amount" => $expense['shared_amount'],
              "lead_id" => $lead['lead_id'],
            ];
          }
        }
      }
    }
    $mergedExpenses = [];
    foreach($allExpenses as $expense){
      if(isset($mergedExpenses[$expense['config_expense_id']])){
        $mergedExpenses[$expense['config_expense_id']]['amount'] += $expense['amount'];
      }else{
        $mergedExpenses[$expense['config_expense_id']] = $expense;
      }
    }
    $expenseTable = TableRegistry::getTableLocator()->get('LeadOwnerExpenseEntity');
    $expenseTable->deleteAll(['lead_id' => $lead['lead_id']]);
    $entities = $expenseTable->newEntities(array_values($mergedExpenses));
    $expenseTable->saveMany($entities);
  }

  private function _recalculateIncomeAndExpenses($lead){
    $this->_recalculateIncome($lead);
    $this->_recalculateExpenses($lead);
  }

  private function _mapOwnerAssetsLiabilities($data){
    try {
      $unset_fields = ['id', 'source_id', 'partner_account_people_id', 'created', 'updated'];
      foreach ($data as $k => $e) {
        foreach ($unset_fields as $f) {
          unset($data[$k][$f]);
        }
      }
      return $data;
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }

  private function _mapOwnerEmployments($employments) {
    try {
      $unset_fields = ['lead_owner_employment_id', 'lead_owner_id', 'partner_account_people_id','abn_id', 'created', 'updated'];
      foreach ($employments as $k => $e) {
        foreach ($unset_fields as $f) {
          unset($employments[$k][$f]);
        }
      }
      return $employments;
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }


  private function _mapOwnerAddresses($addresses) {
    try {
      $unset_fields = ['lead_owner_address_id', 'lead_owner_id', 'partner_account_people_id', 'created', 'updated'];
      foreach ($addresses as $k => $e) {
        foreach ($unset_fields as $f) {
          unset($addresses[$k][$f]);
        }
      }
      return $addresses;
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }

  
  private function _mapAssets($assets) {
    try {
      $unset_fields = ['lead_asset_id', 'lead_id', 'lead_owner_id', 'partner_account_id', 'partner_account_people_id', 'asset_type', 'created', 'updated'];
      foreach ($assets as $k => $e) {
        foreach ($unset_fields as $f) {
          unset($assets[$k][$f]);
        }
      }
      return $assets;
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }


  private function _mapLiabilities($liabilities) {
    try {
      $unset_fields = ['lead_liability_id', 'lead_id', 'lead_owner_id', 'partner_account_id', 'partner_account_people_id', 'liability', 'created', 'updated'];
      foreach ($liabilities as $k => $e) {
        foreach ($unset_fields as $f) {
          unset($liabilities[$k][$f]);
        }
      }
      return $liabilities;
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }


  private function _mapReferences($references) {
    try {
      $unset_fields = ['lead_reference_id', 'lead_id', 'partner_account_id', 'created', 'updated'];
      foreach ($references as $k => $e) {
        foreach ($unset_fields as $f) {
          unset($references[$k][$f]);
        }
      }
      return $references;
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }


  private function _mapAddresses($addresses) {
    try {
      $unset_fields = ['lead_address_id', 'lead_id', 'partner_account_id', 'created', 'updated'];
      foreach ($addresses as $k => $a) {
        foreach ($unset_fields as $f) {
          unset($addresses[$k][$f]);
        }
      }
      return $addresses;
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }


  private function _mapAbn($abn) {
    try {
      $unset_fields = ['abn_id', 'lead_id', 'partner_account_id', 'created'];
      foreach ($unset_fields as $f) {
        unset($abn[$f]);
      }
      return $abn;
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }


  private function _mapEntityTrust($entity_trust) {
    try {
      $unset_fields = ['entity_trust_id', 'lead_id', 'partner_account_id'];
      foreach ($unset_fields as $f) {
        unset($entity_trust[$f]);
      }
      return $entity_trust;
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }


  public function view($lead_id) {
    try {
      $lead = TableRegistry::getTableLocator()->get('LeadEntity')->get($lead_id, [
        'contain' => [
          'Owners', 'Owners.LeadOwnerFinancesEntity', 'Owners.OwnerAllEmployments', 'Owners.OwnerAllAddresses', 'Owners.LeadAssetsEntity', 'Owners.LeadLiabilitiesEntity',
          'LeadAssetsEntity',
          'LeadLiabilitiesEntity',
          'LeadReferenceEntity',
          'AllAddresses',
          'LeadAbnLookupEntity',
          'EntityTrustEntity'
        ]
      ]);
  
      return $this->setJsonResponse(['success' => true, 'lead' => $lead]);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(["success" => false, "message" => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
    
  }

  public function accountLeadInit(){
    $data               = $this->request->getData();

    $account_ref        = $data['account_ref'];
    $product_type_id    = $data['product_type_id'];
    $lender_product_id  = $data['lender_product_id'];

    $upload_ids         = $data['popup_option']['documents'];
    $upload_aids        = $data['popup_option']['applicant_documents'];

    $applicant_refs = $data['applicant_refs'] ?? [];

    $lend_internal_auth = new LendInternalAuth;
    $account_id = $lend_internal_auth->unhashPartnerAccountId($account_ref);

    $applicants = [];
    foreach($applicant_refs as $applicant_ref){
      $applicants[] = $lend_internal_auth->unhashPartnerAccountPeopleId($applicant_ref);
    }
    
    $is_pass_pre_check =$this->loadModel('Leads')->isPassPrecheckLeadCreation($account_id, $product_type_id, $lender_product_id);
   
    if($is_pass_pre_check !== true){
      return $this->setJsonResponse(['success'=>false, 'message'=>$is_pass_pre_check]);
    }

    $fail_account_lead = $this->loadModel('Leads')->checkFailedLeadExist($account_id);

    if($fail_account_lead){
      $response = ['success' => true, 'lead_ref' => $fail_account_lead['lead_ref']];
    }else{
      $response = $this->create($account_ref, "commercial", ($data['applicant_refs'] ?? []), false, $data);
    }

    $lead_ref = $response['lead_ref'];
    $lead_id  = $lend_internal_auth->unhashLeadId($lead_ref);

    
    $update_resonse = $this->loadModel('ConfigProductTypeQuestions')->updateLeadDataBeforeSend($account_id, $product_type_id, $lead_id);

    $update_documnet_response    = $this->loadModel('LenderMatchingUtil')->copyAccountUploadToLead($upload_ids,$lead_id);
    $update_a_documnet_response  = $this->loadModel('LenderMatchingUtil')->copyApplicantUploadToLead($upload_aids,$lead_id);
    $non_end_to_end_address      = $this->loadModel('LenderMatchingUtil')->addToLeadDataFromLeadAddress($lead_id);

    $call_me_first = $data['popup_option']['contact'] == "lender" ? 1 : 0;
    $send_type = $data['popup_option']['autoSend'] == "yes" ? "Auto" : "Manual";
    $other_notes = $data['popup_option']['otherNotes'];
    $this->loadModel('Leads')->updateLead(array('lead_id' => $lead_id,
    'call_me_first' => $call_me_first, 'send_type' => $send_type, 'additional_info' => $other_notes) );
    $is_end_to_end = $data['popup_option']['isEndToEnd'];
    if($is_end_to_end){
      $answers = $this->loadModel('ConfigProductTypeQuestions')->retrieveQuestion($account_id, $product_type_id);
      $answers = json_decode($answers,true);
      $tables = $this->loadModel('ConfigProductTypeQuestions')->getQustionMapping($answers);
      $this->loadModel('LeadAssetFinance')->addLeadAssetFinance(array(
        'lead_id' => $lead_id,
        'contract_type' => "Low Doc", 
        'equipment_id' => $tables['leads']['sub_equipment_id']) 
      );
    }

    if($update_resonse == false){
      $response_fuzzy    = (new LenderMatchingController)->getFuzzyValuesBeforeSend($account_ref,$product_type_id);
      if($response_fuzzy['success']){
          $update_resonse = $this->loadModel('ConfigProductTypeQuestions')->setQustionMappingFromFuzzy($lead_id,$response_fuzzy['fuzzy']);
      }
    }

    if($update_resonse){
      return $this->setJsonResponse($response);
    }
    
    return $this->setJsonResponse(['success' => false, 'message' => 'Lead data not updated']);
  }


  public function create() {
    try {
      if (!$this->request->is('post')) {
        throw new \Exception("Only POST request is allowed or account_ref is not empty");
      }
      $data = $this->request->getData();
      
      if (empty($data['account_ref'])) {
        throw new \Exception("Missed required field: account_ref");
      }

      $result = $this->_create($data, false);
      
      return $this->setJsonResponse($result, 200);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(["success" => false, "message" => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }
  private function _create($data, $returnLead = false) {
    $lead_types = ['commercial','consumer'];
    try {
      $account_ref = !empty($data['account_ref']) ? $data['account_ref'] : null;
      $lead_type = !empty($data['lead_type']) ? $data['lead_type'] : 'commercial';
      $applicant_refs = !empty($data['applicant_refs']) ? $data['applicant_refs'] : [];
      if (!empty($data['people_account_ref']) && empty($account_ref)) {
        $account_ref = $data['people_account_ref'];
      }

      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }
      
      if (!in_array($lead_type, $lead_types)) {
        throw new \Exception("lead_type must be commercial or consumer");
      }

      $lend_internal_auth = new LendInternalAuth;
      if (!empty($account_ref)) {
        $account_id = $lend_internal_auth->unhashPartnerAccountId($account_ref);
        $contain = [
          'PartnerAccountLinkPeopleEntity.PartnerAccountPeopleEntity.LeadAssetsEntity', 
          'PartnerAccountLinkPeopleEntity.PartnerAccountPeopleEntity.LeadLiabilitiesEntity', 
          'PartnerAccountLinkPeopleEntity.PartnerAccountPeopleEntity.LeadOwnerFinancesEntity', 
          'PartnerAccountLinkPeopleEntity.PartnerAccountPeopleEntity.LeadOwnerAddressesEntity', 
          'PartnerAccountLinkPeopleEntity.PartnerAccountPeopleEntity.LeadOwnerEmploymentEntity', 
          'PartnerAccountLinkPeopleEntity.PartnerAccountPeopleEntity.LeadOwnerEmploymentEntity.LeadAbnLookupEntity',
          'PartnerAccountLinkPeopleEntity.PartnerAccountPeopleEntity.PartnerAccountPeopleIncomesEntity',
          'PartnerAccountLinkPeopleEntity.PartnerAccountPeopleEntity.PartnerAccountPeopleExpensesEntity',
          'PartnerAccountLinkPeopleEntity.PartnerAccountPeopleEntity.PartnerAccountPeopleExpensesEntity.PartnerAccountPeopleExpensesBreakdownEntity',
          'PartnerAccountLinkPeopleEntity.PartnerAccountPeopleEntity.PartnerAccountPeopleAssetsEntity',
          'PartnerAccountLinkPeopleEntity.PartnerAccountPeopleEntity.PartnerAccountPeopleLiabilitiesEntity',
          'PartnerAccountMetaEntity',
          'LeadAssetsEntity',
          'LeadLiabilitiesEntity',
          'AllAddresses',
          'LeadAbnLookupEntity',
          'LeadReferenceEntity',
          'EntityTrustEntity',
          'EntityTrustEntity',
          'PartnerAccountUploadsEntity',
          'LeadUploadsEntity',
          'CurrentTradingAddress',
          'PartnerEntity'
        ];
        $options = ['contain' => $contain];
        $account = TableRegistry::getTableLocator()->get('PartnerAccountEntity')->get($account_id, $options);
        $account = json_decode(json_encode($account), true);
        if (count($applicant_refs) > 0) {
          $applicants = [];
          foreach($applicant_refs as $applicant_ref){
            $applicants[] = $lend_internal_auth->unhashPartnerAccountPeopleId($applicant_ref);
          }
          $account['partner_account_link_people'] = array_filter($account['partner_account_link_people'], function($a) use ($applicants) {
            return in_array($a['people_id'], $applicants);
          });
          $account['partner_account_link_people'] = array_values($account['partner_account_link_people']);
        }
      } else { // no account_id
        $people_id = $lend_internal_auth->unhashPartnerAccountPeopleId($applicant_refs[0]);
        $contain = [
          'LeadAssetsEntity',
          'LeadLiabilitiesEntity',
          'LeadOwnerFinancesEntity',
          'LeadOwnerAddressesEntity',
          'LeadOwnerEmploymentEntity',
          'LeadOwnerEmploymentEntity.LeadAbnLookupEntity',
          'PartnerAccountPeopleIncomesEntity',
          'PartnerAccountPeopleExpensesEntity',
          'PartnerAccountPeopleExpensesEntity.PartnerAccountPeopleExpensesBreakdownEntity',
          'PartnerAccountPeopleAssetsEntity',
          'PartnerAccountPeopleLiabilitiesEntity',
        ];
        $people = TableRegistry::getTableLocator()->get('PartnerAccountPeopleEntity')->get($people_id, ['contain' => $contain]);
        $people = json_decode(json_encode($people), true);
        $account = [
          'partner_id' => $people['partner_id'],
          'partner_account_link_people' => [
            [
              'is_main_point_of_contact' => true,
              'partner_account_people' => $people,
            ],
          ]
        ];
      }

      //ensure there is a point of contact
      $poc = false;
      foreach($account['partner_account_link_people'] as $pap){
        if ($pap['is_main_point_of_contact'] === true) {
          $poc = true;
          break;
        }
      }
      if($poc === false) {
        $account['partner_account_link_people'][0]['is_main_point_of_contact'] = true;
      }

      $lead_data = $this->_generateLeadData($account, false, $lead_type);
      if (isset($data['product_type_id']) && ((ctype_digit($data['product_type_id']) === true) || (is_integer($data['product_type_id']) === true)))
        $lead_data['product_type_id'] = $data['product_type_id'];
      if (isset($data['purpose_id']))
        $lead_data['purpose_id'] = $data['purpose_id'];
      if (isset($data['loan_term_requested_months']))
        $lead_data['loan_term_requested_months'] = $data['loan_term_requested_months'];
      if (isset($data['asset_finance']))
        $lead_data['asset_finance'] = $data['asset_finance'];
      if (isset($data['pricing']))
        $lead_data['pricing'] = $data['pricing'];

      $leadTable = TableRegistry::getTableLocator()->get('LeadEntity');
      $associated = [
          'Owners',
          'Owners.OwnerAllEmployments',
          'Owners.OwnerAllEmployments.LeadAbnLookupEntity',
          'Owners.OwnerAllAddresses',
          'Owners.LeadAssetsEntity',
          'Owners.LeadLiabilitiesEntity',
          'LeadAssetsEntity',
          'LeadLiabilitiesEntity',
          'LeadReferenceEntity',
          'AllAddresses',
          'LeadAbnLookupEntity',
          'EntityTrustEntity',
          'LeadAssetFinanceEntity',
          'LeadPricingEntity',
          'Owners.LeadOwnerIncomeEntity',
          'Owners.LeadOwnerIncomeEntity.ConIncomeShareEntity',
          'Owners.LeadOwnerExpenseEntity',
          'Owners.LeadOwnerExpenseEntity.LeadOwnerExpensesBreakdownEntity',
      ];

      foreach ($lead_data['owners_all'] as $key => $owner) {
        $totalGrossAmount = 0;
        $totalAmount = 0; 
        if (!empty($owner['incomes'])) {
          foreach ($owner['incomes'] as $income) {
              $totalGrossAmount += $income['gross_amount'];
              $totalAmount += $income['amount']; 
          }
        }
        $lead_data['owners_all'][$key]['monthly_gross_income'] = $totalGrossAmount;
        $lead_data['owners_all'][$key]['monthly_net_income'] = $totalAmount;
      }

      // This is used to sync data from the applicant, as only 1 applicant can be found when we sync from the applicant level
      if($lead_type === 'commercial'){
        $lead_data['additional_info'] = $lead_data['additional_info'];
      }else{
        $lead_data['additional_info'] = $lead_data['owners_all'][0]['additional_info'];
      }

      $lead = $leadTable->newEntity($lead_data, ['associated' => $associated]);
      $leadTable->save($lead);

      $this->_recalculateIncomeAndExpenses($lead);

      if (!empty($account->partner_account_id)) {
        $lead->bsa = $this->_createBSA($account->partner_account_id, $lead->lead_id);
      }
      $partnerUserLeadsTable = TableRegistry::getTableLocator()->get('PartnerUserLeadsEntity');
      $partnerUserLead = $partnerUserLeadsTable->newEntity([
        'partner_user_id' => $user['partner_user_id'],
        'status' => 'ACCESS',
        'lead_id' => $lead->lead_id
      ]);
      $partnerUserLeadsTable->save($partnerUserLead);

      $result = ['success' => true, 'lead_ref' => $lead->lead_ref];

      return $returnLead ? array_merge($result, ['lead'=> $lead]) : $result;
        
    } catch(\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      throw new Exception($e->getMessage());
    }
  }

  public function addApplicant(){
    try{
      if (!$this->request->is('post')) {
        throw new \Exception("Only POST request is allowed.");
      }
      $data = $this->request->getData();
      if (empty($data['lead_ref'])) {
        throw new \Exception("lead_ref must be provided");
      }
      if (empty($data['applicant_ref'])) {
        throw new \Exception("applicant_ref must be provided");
      }
      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }
      $lend_internal_auth = new LendInternalAuth;
      $lead_id = $lend_internal_auth->unhashLeadId($data['lead_ref']);
      if (empty($lead_id)) {
        throw new \Exception("Can't find a lead.");
      }
      $applicant_id = $lend_internal_auth->unhashPartnerAccountPeopleId($data['applicant_ref']);
      if (empty($applicant_id)) {
        throw new \Exception("Can't find an applicant.");
      }
      $lead_table = TableRegistry::getTableLocator()->get('LeadEntity');
      $associated = [
        'LeadOwnerIncomeEntity',
        'LeadOwnerIncomeEntity.ConIncomeShareEntity',
        'LeadAssetsEntity',
        'LeadAssetsEntity.ConAssetShareEntity',
        'LeadLiabilitiesEntity',
        'LeadLiabilitiesEntity.ConLiabilityShareEntity',
        'Owners',
        'Owners.LeadOwnerFinancesEntity',
        'Owners.LeadOwnerIncomeEntity',
        'Owners.LeadOwnerExpenseEntity',
        'Owners.LeadOwnerExpenseEntity.LeadOwnerExpensesBreakdownEntity',
        'Owners.LeadAssetsEntity',
        'Owners.LeadLiabilitiesEntity',
        'Owners.OwnerAllEmployments',
        'Owners.OwnerAllAddresses',
      ];
      $lead = $lead_table->get($lead_id, ['contain'=> $associated]);
      if (empty($lead)) {
        throw new \Exception("Lead not found");
      }
      $partner_account_people_table = TableRegistry::getTableLocator()->get('PartnerAccountPeopleEntity');
      $associatedPeople = [
        'PartnerAccountPeopleIncomesEntity',
        'PartnerAccountPeopleExpensesEntity',
        'PartnerAccountPeopleExpensesEntity.PartnerAccountPeopleExpensesBreakdownEntity',
        'PartnerAccountPeopleAssetsEntity',
        'PartnerAccountPeopleLiabilitiesEntity',
        'LeadOwnerEmploymentEntity',
        'LeadOwnerAddressesEntity',
      ];
      $applicant = $partner_account_people_table->get($applicant_id, ['contain' => $associatedPeople]);
      if (empty($applicant)) {
        throw new \Exception("Applicant not found");
      }
      if ($lead->partner_id !== $user['partner_id']) {
        throw new \Exception("You do not have access to this lead");
      }
      if ($applicant->partner_id !== $user['partner_id']) {
        throw new \Exception("You do not have access to this applicant");
      }
      $leadData = [];

      $applicant = $applicant->toArray();
      $applicant['is_main_point_of_contact'] = 0;
      $leadData['owners_all'] = $this->_mapLeadOwners([$applicant], $lead->lead_type, $lead);

      $lead = $lead_table->patchEntity($lead, $leadData, ['associated' => $associated]);
      $lead_table->save($lead);
      $this->_recalculateIncomeAndExpenses($lead);
      $this->createAssetsLiabitiesShares($lead);
      return $this->setJsonResponse(['success' => true, 'message' => 'Applicant has been added to the lead']);
    }
    catch(\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(["success" => false, "message" => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  /**
   * Create Assets and Liabities shares from a saved lead object (with assets and liabilities)
   * Shares are 100% percentage
   * Does not consider existing shares
   * @param mixed $lead
   * @return void
   */
  private function createAssetsLiabitiesShares($lead){
    if($lead->lead_type === 'consumer'){//create shares for consumer leads only
      $assetShares = [];
      $liabilityShares = [];
      $leadData = $lead->toArray();
      foreach($leadData['owners_all'] as $owner){
        foreach($owner['lead_assets'] as $asset){
          $assetShares[] = ['lead_asset_id' => $asset['lead_asset_id'], 'owner_id'=> $owner['owner_id'], 'percent'=>100];
        }
        foreach($owner['liabilities'] as $liability){
          $liabilityShares[] = ['lead_liability_id' => $liability['lead_liability_id'], 'owner_id'=> $owner['owner_id'], 'percent'=>100];
        }
        if(count($assetShares) > 0){
          $conAssetShareTable = TableRegistry::getTableLocator()->get('ConAssetShareEntity');
          $assetShareEntities = $conAssetShareTable->newEntities($assetShares);
          $conAssetShareTable->saveMany($assetShareEntities);
        }
        if(count($liabilityShares) > 0){
          $conLiabilitiesShareTable = TableRegistry::getTableLocator()->get('ConLiabilityShareEntity');
          $liabilityShareEntities = $conLiabilitiesShareTable->newEntities($liabilityShares);
          $conLiabilitiesShareTable->saveMany($liabilityShareEntities);
        }
      }
    }
  }

  public function createFromApplicant(){
    try{
      if (!$this->request->is('post')) {
        throw new \Exception("Only POST request is allowed.");
      }
      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }
      $data = $this->request->getData();
      if (empty($data['applicant_ref'])) {
        throw new \Exception("applicant_ref must be provided");
      }
      if (empty($data['lead_type'])) {
        throw new \Exception("lead_type must be provided");
      }
      if (!in_array($data['lead_type'], ['commercial','consumer'])) {
        throw new \Exception("lead_type must be commercial or consumer");
      }
      // $lend_internal_auth = new LendInternalAuth;
      // $people_id = $lend_internal_auth->unhashPartnerAccountPeopleId($data['applicant_ref']);
      
      // if (!empty($data['account_ref'])) {

      // } else {
      //   $accountPeople = TableRegistry::getTableLocator()->get('PartnerAccountPeopleEntity')->get($people_id);
      // }
      //check that user has access to this applicant
      // if ((int)$user['partner_id'] !== (int)$accountPeople->partner_id) {
      //   throw new \Exception("You do not have access to this applicant.");
      // }

      // if ($data['lead_type'] === 'commercial' && empty($accountPeople->partner_account_link_people)) {
      //   throw new \Exception("Applicant is not linked to any account.");
      // }

      $data['applicant_refs'] = [$data['applicant_ref']];
      $resp = $this->_create(@$data, true);
      if(isset($resp['lead'])){
        $this->createAssetsLiabitiesShares($resp['lead']);
        unset($resp['lead']);
      }
      
      return $this->setJsonResponse($resp);
    }
    catch(\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(["success" => false, "message" => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  private function _createBSA($account_id, $lead_id) {
    try {
      $headers = ['headers'=>['x-api-key' => getenv('BANK_STATEMENT_API_KEY'), 'content-type' => 'application/json']];
      $payload = ['partnerAccountId' => $account_id];
      $signature = $this->_generateSignature($payload);
      $response = (new Client)->post(getenv('DOMAIN_BANKFEEDS').'/get-bank-statements-by-partner-account-id?signature='.$signature, json_encode($payload), $headers);
      if ($response->json['success']) {
        $account_ids = !empty($response->json['data']) ? array_column($response->json['data'], 'account_id') : false;
      }

      if (empty($account_ids)) {
        return false;
      }

      $payload = [
        'account_ids' => $account_ids,
        'partner_account_id' => $account_id,
        'lead_id' => $lead_id,
        'is_auto_merge' => true
      ];
      $signature = $this->_generateSignature($payload);
      $response = (new Client)->post(getenv('DOMAIN_BANKFEEDS').'/analyse-bs?signature='.$signature, json_encode($payload), $headers);
      if ($response->json['success']) {
        return true;
      }
      return false;
    } catch(\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }

  private function _generateSignature($payload){
    $sign = base64_encode(hash_hmac('sha256', json_encode($payload, JSON_UNESCAPED_SLASHES) . getenv('BANKFEEDS_SECRET'), getenv('BANKFEEDS_SECRET'))); // to base64
    return $sign;
  }

}