<?php

namespace Ka<PERSON>ban\Controller;

use Cake\Database\Expression\QueryExpression;
use Ka<PERSON>ban\Controller\AppController;

use <PERSON>ake\Datasource\ConnectionManager;
use Cake\Datasource\EntityInterface;
use Cake\Http\Exception\MethodNotAllowedException;
use Cake\Http\Exception\NotFoundException;
use Cake\ORM\TableRegistry;
use Cake\Core\Configure;
use Cake\Log\Log;
use App\Lend\Config;
use App\Lend\KanbanHelper;
use App\Lend\LendInternalAuth;
use App\Lend\SocketHelper;
use App\Auth\LendAuthenticate;

/**
 * Summary of LeadsController
 */
class LeadsController extends AppController
{
    private $nullSwimLaneId = 99999;//used for indexing in partner user's kanban_leads position data when a swimlane id is null
    public $swimlanes = [
        ['id' => 1, 'order' => 1, 'name' => 'Asset Finance', 'product_types' => [10, 20, 21, 22, 23]],
        ['id' => 2, 'order' => 2, 'name' => 'Unsecured', 'product_types' => [1]],
        ['id' => 3, 'order' => 3, 'name' => 'Secured', 'product_types' => [2, 7, 15, 16, 17, 18]],
        ['id' => 4, 'order' => 4, 'name' => 'Overdraft', 'product_types' => [4]],
        ['id' => 5, 'order' => 5, 'name' => 'Line of Credit', 'product_types' => [3]],
        ['id' => 6, 'order' => 6, 'name' => 'Invoice Finance', 'product_types' => [8]],
        ['id' => 7, 'order' => 7, 'name' => 'Merchant Cash Advance', 'product_types' => [9]],
        ['id' => 8, 'order' => 8, 'name' => 'Development & Construction', 'product_types' => [24]],
        ['id' => 9, 'order' => 9, 'name' => 'SMSF', 'product_types' => [6]],
        ['id' => 10, 'order' => 10, 'name' => 'Combination / Multiple', 'product_types' => [14]],
        ['id' => 11, 'order' => 11, 'name' => 'Other', 'product_types' => [11]],
        ['id' => 12, 'order' => 12, 'name' => 'Consumer', 'product_types' => [25, 26]],
    ['id' => 13, 'order' => 13, 'name' => 'Home Loan', 'product_types' => [27]],
    ];

    public function initialize()
    {
        parent::initialize();
    }

    /**
     * Generate conditions for query
     * @param mixed $conditions_data
     * @return QueryExpression
     */
    private function generateConditions($conditions_data)
    {
        $exp = new QueryExpression();
        foreach ($conditions_data as $data) {
            switch ($data['operation']) {
                case 'equal':
                    //must return only open leads if 'is_closed' isn't explicitly set to 1/true
                    if (($data['field'] === 'LeadEntity.is_closed') && ($data['value'] != 1)) {
                        $orConditions = $exp->or([$data['field'] . " IS NULL"])->eq($data['field'], 0);
                        $exp = $exp->add($orConditions);
                    } else if (is_array($data['value'])) {
                        $exp = $exp->in($data['field'], $data['value']);
                    } else if ($data['value'] === null) {
                        $exp = $exp->isNull($data['field']);
                    } else {
                        $exp = $exp->eq($data['field'], $data['value']);
                    }
                    break;
                case 'contains':
                    if (is_array($data['value'])) {
                        //find if null value exists
                        $nullKeys = array_keys($data['value'], null, true);
                        if (count($nullKeys) > 0) {
                            foreach ($nullKeys as $nullKey)
                                unset($data['value'][$nullKey]);
                            if (count($data['value']) > 0) {
                                $orConditions = $exp->or([$data['field'] . " IS NULL"])->in($data['field'], $data['value']);
                                $exp = $exp->add($orConditions);
                            } else
                                $exp = $exp->isNull($data['field']);
                        } else
                            $exp = $exp->in($data['field'], $data['value']);
                    } else {
                        $exp = $exp->like($data['field'], '%' . $data['value'] . '%');
                    }
                    break;
                case 'more_than':
                    $exp = $exp->gte($data['field'], $data['value']);
                    break;
                case 'less_than':
                    $exp = $exp->lte($data['field'], $data['value']);
                    break;
                case 'between':
                    //unsure why we are doing this, removing it
                    // if ($data['settled_date'] && ($data['settled_date']['value'] == true)) {
                    //     $andCond = (new QueryExpression())->isNull($data['settled_date']['field'])->between($data['field'], $data['value']['from'], $data['value']['to']);//not settled, in range
                    //     $and2 = (new QueryExpression())->between($data['settled_date']['field'], $data['value']['from'], $data['value']['to']);
                    //     $exp2 = (new QueryExpression())->or($andCond)->add($and2);
                    //     $exp->add($exp2);
                    // } else

                    $isFundedDate = ($data['field'] === 'date(PartnerCommission.funded_date)');
                    $hasSettledMode = isset($data['settled_mode']);

                    if ($isFundedDate) {
                        $exp->add(['PartnerCommission.funded_date IS NOT' => null]);
                    }

                    $mainBetween = (new QueryExpression())->between(
                        $data['field'],
                        $data['value']['from'],
                        $data['value']['to']
                    );

                    if (!$isFundedDate && $hasSettledMode) {
                        // OR the main between with the settled_mode between
                        $settledBetween = (new QueryExpression())->between(
                            $data['settled_mode']['field'],
                            $data['settled_mode']['value']['from'],
                            $data['settled_mode']['value']['to']
                        );
                        $orExpr = (new QueryExpression())->or($mainBetween)->add($settledBetween);
                        $exp->add($orExpr);
                    } else {
                        $exp->add($mainBetween);
                    }
                    break;
            }
        }
        return $exp;
    }

    /**
     * Get data for Kanban board
     * @return void
     */
    public function getKanbanLeads()
    {
        try {
            $leadsData = $this->getLeadsData(true);
            $leadsData['leads'] = $this->generateKanbanData($leadsData['leads']);
            unset($leadsData['full_total']);
            return $this->setJsonResponse($leadsData);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    /**
     * Get lead data for a specific lead
     */
    public function getLeadData($leadId)
    {
        try {
            $leadsData = $this->getLeadsData(false, null, 10, $leadId);
            return ($leadsData['leads'] ? $leadsData['leads'][0] ?? [] : []);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
        }
    }

    /**
     * Generate position data for the user
     */
    public function generatePositionData()
    {
        try {
            if (getenv('LEND_ENV') == 0) {
                $user = $this->Auth->user();
            } else {
                $user = $this->Auth->identify();
            }
            return $this->setJsonResponse(KanbanHelper::createPositionData($user['partner_user_id']));
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
        }
    }

    /**
     * Move a lead on the kanban board to a new position
     * Only required to be called to move the lead to a specific position in the new column
     * This must be called after the Lead entity has already been updated
     */
    public function moveLead()
    {
        try {
            //validate inputs
            if (getenv('LEND_ENV') == 0) {
                $user = $this->Auth->user();
            } else {
                $user = $this->Auth->identify();
            }
            $leadRef = $this->request->getData('lead_ref');
            if (empty($leadRef)) {
                throw new \Exception("Missed required field: 'lead_ref'");
            }

            $socketId = $this->request->getData('socket_id');

            $manStatusId = $this->request->getData('man_status_id');
            $options = [
                'contain' => [
                    'ManStatusEntity' => ['fields' => ['id']],
                    'ManStatusEntity.ManStatusGroupEntity' => ['fields' => ['id']],
                ]
            ];
            $lead_table = TableRegistry::getTableLocator()->get('LeadEntity');
            $lead = $lead_table->get(LendInternalAuth::unhashLeadId($leadRef), $options);
            if (!$lead) {
                throw new \Exception("Invalid 'lead_ref' value");
            } else if ($lead->partner_id !== $user['partner_id']) {
                throw new \Exception("Unauthorized", 401);
            }

            $location = $this->request->getData('relative_location');
            $relativeLeadRef = $this->request->getData('relative_lead_ref');
            if (!empty($location)) {
                $locationOptions = ['above', 'below'];
                if (!in_array($location, $locationOptions)) {
                    throw new \Exception("Invalid 'relative_location' value, must be 'above' or 'below'(if provided)");
                }
                if (empty($relativeLeadRef)) {
                    throw new \Exception("Missing 'relative_lead_ref' value");
                }
            }
            $relativeLead = null;
            if (!empty($relativeLeadRef)) {
                $relativeLead = $lead_table->get(LendInternalAuth::unhashLeadId($relativeLeadRef));
                if (!$relativeLead) {
                    throw new \Exception("Invalid 'relative_lead_ref' value");
                } else if ($relativeLead->partner_id !== $user['partner_id']) {
                    throw new \Exception("Unauthorized", 401);
                }
            }

            //status change - update the lead entity if required
            $addOtherUsers = false;
            $existingManStatusGroupId = $lead->man_status->man_status_group->id;
            $manStatusGroupId = $existingManStatusGroupId;
            if (!empty($manStatusId)) {
                $manStatus = TableRegistry::getTableLocator()->get('ManStatusEntity')->get($manStatusId);
                if ($manStatus && $manStatus->partner_id !== $user['partner_id']) {
                    throw new \Exception("Unauthorized: You aren't allowed to use that 'man_status_id'", 401);
                }
                $lead = $lead_table->patchEntity($lead, ['man_status_id' => $manStatusId]);
                $lead->set('_noKanbanChanges', true);//do not notify socket or do any kanban board changes (as moveLead will do kanban changes)
                $lead_table->save($lead);
                $manStatusGroupId = $manStatus->man_status_group_id;
                if ($manStatusGroupId !== $existingManStatusGroupId) {
                    $addOtherUsers = true;
                }
            }

            //move lead on boards and notify sockets
            KanbanHelper::moveLead($user['partner_user_id'], $leadRef, $location, $relativeLeadRef, $manStatusGroupId, $addOtherUsers);
            if ($socketId) {
                if($relativeLead){
                    SocketHelper::kanbanMoveLeadSpecificPosition($lead->lead_id, $socketId, ($addOtherUsers ? null : $user['partner_user_id']), $existingManStatusGroupId, $relativeLeadRef, $location);
                }
                else{
                    SocketHelper::kanbanMoveLeadTop($lead->lead_id, $socketId, ($addOtherUsers ? null : $user['partner_user_id']), $existingManStatusGroupId);
                }
            }

            $resp = ['success' => true, 'message' => 'lead has been moved'];
            if($lead->man_status->id){
                $manStatus = TableRegistry::getTableLocator()->get('ManStatusEntity')->get($manStatusId,
                    [
                        'fields' => [
                            'ManStatusEntity__status_id' => 'ManStatusEntity.id',
                            'status_name',
                            'is_fake_lead',
                            'is_call_q_not_proceeding',
                            'ManStatusGroupEntity__group_id' => 'ManStatusGroupEntity.id',
                            'ManStatusGroupEntity.group_name',
                            'ManStatusGroupEntity.color'
                        ],
                        'contain' => [
                            'ManStatusGroupEntity'
                        ]
                    ]
                );
                if($manStatus){
                    $resp['man_status'] = $manStatus;
                }
            }
            return $this->setJsonResponse($resp);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return $this->setJsonResponse(['success' => false, 'error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
        }
    }

    /**
     * getLeads (need to modify to make join ORM based, will need to convert something to Entity)
     *
     * @param mixed $page
     * @param mixed $pagesize
     * @return void
     */
    public function getLeads($page = null, $pagesize = 10)
    {
        try {
            $leadsData = $this->getLeadsData(false, $page, $pagesize);
            return $this->setJsonResponse($leadsData);

        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    /**
     * Fetch users filtered Kanban leads and their positions for Kanban board
     */
    public function getKanbanLeadsFiltered()
    {
        try {
            if (getenv('LEND_ENV') == 0) {
                $user = $this->Auth->user();
            } else {
                $user = $this->Auth->identify();
            }
            $leadsData = $this->getLeadsData(false, null, null, null, false, true, true, true);
            $data = $this->request->getData();
            $response = KanbanHelper::getPositionData($user['partner_user_id'], $user['partner_id'], $leadsData['lead_refs'], null, false,$data['combineSwimlanes']);
            $response['swimlaneMap']['*']['groupTotals'] = $leadsData['manStatusGroupTotals'];
            //create empty filler group totals
            foreach($response['swimlaneMap']['*']['groupIds'] as $groupId){
                if(!isset($response['swimlaneMap']['*']['groupTotals'][$groupId])){
                    $response['swimlaneMap']['*']['groupTotals'][$groupId] = [
                        'total' => 0,
                        'totalValue' => 0
                    ];
                }
            }
            // $response['leadDataMap'] = $leadsData['leads'];
            $response['leadDataMap'] = $leadsData['kanbanV3'];
            $response['filters'] = $leadsData['filters'];
            return $this->setJsonResponse($response);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    /**
     * Fetch users filtered Kanban leads and their positions for Kanban board
     */
    public function getSwimlaneColumnData()
    {
        try {
            if (getenv('LEND_ENV') == 0) {
                $user = $this->Auth->user();
            } else {
                $user = $this->Auth->identify();
            }
            $requestData = $this->request->getData();
            if (!isset($requestData['man_status_group_id'])) {
                throw new \Exception("Missed required field: 'man_status_group_id'");
            }
            if (!isset($requestData['product_type_id'])) {
                throw new \Exception("Missed required field: 'product_type_id'");
            }
            $swimlaneId = KanbanHelper::getSwimlaneId($user['partner_id'], $requestData['product_type_id']);
            $productTypeIds = KanbanHelper::getSwimlaneProducts($swimlaneId);
            $this->request = $this->request->withData('product_type', $productTypeIds);
            $this->request = $this->request->withData('status', [$requestData['man_status_group_id']]);

            $leadsData = $this->getLeadsData(false, null, null, null, true);
            $response = KanbanHelper::getPositionData(
                $user['partner_user_id'],
                $user['partner_id'],
                $leadsData['lead_refs'],
                $requestData['man_status_group_id'],
                $swimlaneId
            );
            $response['leadDataMap'] = $leadsData['leads'];
            unset($response['swimlaneIds']);
            unset($response['swimlaneNames']);
            return $this->setJsonResponse($response);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    /**
     * Get leads data
     * @param mixed $kanban
     * @param mixed $page
     * @param mixed $pagesize
     * @return array
     */
    private function getLeadsData($kanban = false, $page = null, $pagesize = 10, $leadId = null, $onlyLeads = false, $getLeadRefs = false, $getManStatusGroupTotals = false, $kanbanV3 = false)
    {
        $user = $this->Auth->user();
        if (empty($user)) {
            $user = $this->Auth->identify();
        }
        // socketHelper calling this function and not able to get user from $this->Auth() in this case so get it from cookie:
        if (empty($user) && !empty($_COOKIE['auth_token'])) {
            $result = LendAuthenticate::decode(['allowedAlgs' => ['HS256']], $_COOKIE['auth_token']);
            if ($result['success']) {
              $user = TableRegistry::getTableLocator()->get('PartnerUserEntity')->find('all')
                ->where(['email' => $result['payload']->sub])
                ->first()
                ->toArray();
            }
        }
        $partner_user_id = $user['partner_user_id'];
        $schema = [
            "lead_id" => null,
            "start_date" => null,
            "end_date" => null,
            "filter_by_date" => null,
            "status" => null,//man_status_id array
            "customer_type" => null,
            "assignee" => null,
            "product_type" => null,
            "lender" => null,
            "is_closed" => null,
            "is_archived" => null,
            "sort" => null,
            "no_leads" => null,
            "settled_date" => null,
            "referrer" => null,
            "tag_id" => null,
            "settled_mode" => null
        ];
        $data = $this->request->getData();
        if(!isset($data['filters'])){//filters would be in body if not sent in 'filters'
            $data['filters'] = $data;
        }
        if($data['filters']['status'] === []){
            $data['filters']['status'] = null;
        }
        $filter_data = !$kanban ? array_intersect_key($data['filters'] ?? [], $schema) : [];
        if (isset($filter_data['assignee']) && (($key = array_search(0, $filter_data['assignee'])) !== FALSE)) {
            $filter_data['assignee'][$key] = null;
        }
        if (!$kanban && !isset($filter_data['settled_date']))
            $filter_data['settled_date'] = true;
        //test filters
        // $filter_data = [
        // "start_date" => "2023-03-07",// tested - single and between
        // "end_date" => "2023-03-10",//tested - single and between
        // "filter_by_date" => "created", //"created || updated",//tested both
        // // "funded_filter_by_date" => "created || updated || funded",//what is this - not used
        // // "status" => ["0","1"],//manual status id - tested
        // // "customer_type" =>"consumer", //"commercial || consumer", //tested both
        // // "assignee" => ["1","40"],//array of partner user ids(numbers or strings) - tested
        // // "product_type" => ["1"],//array of numbers or strings - tested
        // // "lender" => ["0","1", 6],// tested
        // // "is_closed" => "1",//is_closed - to be shown on the closed leads view and not on the kanban board
        // // "sort" => "LeadEntity.created DESC", //only for closed leads - sorted by default
        // ];

        $getArchivedList = (isset($filter_data['is_archived']) && (intval($filter_data['is_archived']) == 1));
        if ($getArchivedList) {
            $filter_data['is_archived'] = [1, 2];
            unset($filter_data['is_closed']);
        } else {
            $getOpenList = !(isset($filter_data['is_closed']) && (intval($filter_data['is_closed']) == 1));
            if ($getOpenList) {
                // Send only open leads by default unless closed are requested
                $filter_data['is_closed'] = 0;
            }
            // Exclude archived leads from non-archived lists
            $filter_data['is_archived'] = 0;
        }
        $noLeads = (isset($filter_data['no_leads']) && (intval($filter_data['no_leads']) == 1));

        if ($user['access_all_leads']) {
            $where = ['LeadEntity.partner_id' => $user['partner_id']];
        } else {
            $where = ['LeadEntity.partner_id' => $user['partner_id'], 'PartnerUserLeadsEntity.partner_user_id' => $user['partner_user_id']];
        }
        $where[] = ['LeadEntity.call_queue_status in ' => Configure::read('Lend.NOT_IN_CALL_QUEUE_STATUS')]; // exclude -2, -2 is temporarily removed from call queue

        if ($leadId)
            $where = ['LeadEntity.lead_id' => intval($leadId)];


        // $partnerTable = TableRegistry::getTableLocator()->get('PartnerEntity', [
        //   'connection' => ConnectionManager::get('reader_db')
        // ]);
        $partnerTable = TableRegistry::getTableLocator()->get('PartnerEntity');
        $partner = $partnerTable->get($user['partner_id']);
        $isManual = ($partner->status_system === "manual");
        // $isManual = false;

        $contain = [
            'ReferrerPeople' => ['fields' => ['id', 'first_name', 'last_name']],
            'ManStatusEntity' => ['fields' => ['ManStatusEntity__status_id' => 'ManStatusEntity.id', 'status_name', 'is_fake_lead', 'is_call_q_not_proceeding']],
            'ManStatusEntity.ManStatusGroupEntity' => ['fields' => ['ManStatusGroupEntity__group_id' => 'ManStatusGroupEntity.id', 'group_name', 'color']],
            'PartnerProductTypeEntity' => ['fields' => ['product_type_id', 'product_type_name']],
            'PocOwner' => ['fields' => ['first_name', 'last_name', 'phone', 'mobile', 'email', 'avatar_image_url']],
            'PartnerUserLeadsEntity' => ['fields' => ['granted']],
            'PartnerUserLeadsEntity.PartnerUserEntity' => ['fields' => ['partner_user_id', 'name', 'kanban_colour']],
            'PartnerCommission' => ['fields' => ['funded_amount', 'funded_date', 'funded_type']],
            'PartnerTagEntity' => ['fields' => ['id', 'tag', 'color']],
            'ConPageStatusEntity' => ['fields' => ['con_cgqp_status']],
            'LeadAssociatedDataEntity' => ['fields' => ['LeadAssociatedDataEntity__has_submitted_sale' => '!ISNULL(LeadAssociatedDataEntity.max_sale_id)', 'lender_name', 'max_lender_id']],
            'LeadAssociatedDataEntity.LenderEntity' => ['fields' => ['lender_logo']],
        ];
        if ($getOpenList) {
            $contain['LatestLenderStatuses'] = ['fields' => ['lead_id', 'lender_status_id', 'combined_status_string', 'lender_modified_time']];
            $contain['LeadNotesEntity'] = ['fields' => ['lead_id', 'notes', 'created']];
            $contain['LendScoreEntity'] = ['fields' => ['lend_score']];//only one ever
            $contain['LenderLeadUpdateEntity'] = ['fields' => ['lender_status_id']];//only one ever
            $contain['LenderLeadUpdateEntity.LenderStatusEntity'] = ['fields' => ['combined_status_string']];//only one ever
            $contain['LenderLeadUpdateEntity.LenderStatusEntity.LendStatusEntity'] = ['fields' => ['lend_status_id', 'status_name', 'group_name']];//only one ever
            // $contain['NextTask'] = ['fields' =>  ['lead_id','callback_id','scheduled_time'], 'sort' => ['callback_id' => 'ASC']];
        }

        if($kanbanV3){
            $contain['PartnerProductTypeEntity.KanbanSwimlaneProductTypesEntity'] = ['fields' => ['product_type_id', 'kanban_swimlane_id']];
        }

        if ($user['account_type'] == 'Intermediary') {
            $contain['IntermediaryLenderMappingEntity'] = [
                'fields' => ['id', 'lead_info', 'created',],
                'OriginalLead' => [
                    'fields' => ['lead_id', 'partner_id', 'lead_ref', 'partner_status_id'],
                    'PartnerEntity' => [
                        'fields' => ['partner_id', 'organisation_name', 'company_name',]
                    ],
                ],
            ];
        }
        $options = ['contain' => $contain];

        $leadEntityFields = ['lead_id', 'lead_ref', 'kanban_colour', 'is_closed', 'lead_type', 'created', 'last_changed_date', 'amount_requested', 'organisation_name', 'call_queue_status', 'tag_id', 'referrer_person_id'];
        if ($getOpenList)
            $leadEntityFields = array_merge($leadEntityFields, ['sales_monthly', 'statements_uploaded', 'campaign', 'b_state']);
        $leadEntityFields = array_merge($leadEntityFields, ['lender_name' => 'LeadEntity.lead_id', 'lender_logo' => 'LeadEntity.lead_id']);
        $leads = TableRegistry::getTableLocator()
            // ->get('LeadEntity', [
            //   'connection' => ConnectionManager::get('reader_db')
            // ])
            ->get('LeadEntity')
            ->find('all', $options)
            ->select($leadEntityFields)
            ->where($where)->distinct();
        if ($getOpenList) {
            $leads = $leads->order(['lender_name' => 'DESC']);
        }

        //create and apply filters
        if (!empty($filter_data)) {
            $conditions = $this->convertConditions($filter_data);
            // dump($conditions);
            $leads->where($conditions);
        }
        // ## WARNING:: SQL injection issue if $field contains something like: `; insert into...`.
        // if (!empty($filter_data['sort'])) {
        //   $sort = array();
        //   foreach ($filter_data['sort'] as $field => $items) {
        //     if ($items[0] === 'ASC') {
        //       $sort[$field] = 'ASC';
        //     } else {
        //       $sort[$field] = 'DESC';
        //     }
        //   }
        //   $leads->order($sort);
        // } else {
        $leads->order(['LeadEntity.created' => 'DESC']);
        // }
        $leads->enableHydration(false);//need array
        if ($page)
            $leads->limit($pagesize)
                ->offset(($page - 1) * $pagesize);

        $fullCount = $leads->count();
        $filters = [];//counter
        $leadRefs = [];
        $manStatusGroupTotals = [];
        $data = $leads->toList();
        $retainFields = array_merge($leadEntityFields, ['man_status', 'owner_poc', 'product', 'sale_current', 'partner_user_lead', 'partner_commission', 'tag', 'partner_product_type', 'partner_commission', 'tasks', 'lender_id', 'lender_name', 'lender_logo', 'con_page_status', 'lead_associated_data', 'is_archived', 'referrer_person', 'intermediary_lender_mapping']);
        if ($getOpenList)
            $retainFields = array_merge($retainFields, ['lend_score', 'latest_lender_status', 'latest_note', 'next_task', 'lender_status_ent']);
        $cleanedData = [];
        $cleanedDataV3 = [];

        $filters['Status'] = [];
        $filters['CustomerType'] = [];
        $filters['Assignee'] = [];
        $filters['ProductType'] = [];
        $filters['Lender'] = [];
        $filters['Tag'] = [];
        $filters['Referrer'] = [];
        if ($isManual === true) {
            //Get status groups and statuses
            $groupOptions = [];
            $groupOptions['fields'] = ['id', 'group_name', 'order', 'color'];
            $groupOptions['contain'] = ['ManStatusEntity' => ['fields' => ['man_status_group_id', 'ManStatusEntity__status_id' => 'ManStatusEntity.id', 'status_name', 'ManStatusEntity__status_order' => 'ManStatusEntity.order', 'is_settled', 'is_fake_lead', 'is_call_q_not_proceeding'], 'sort' => ['ManStatusEntity.order' => 'ASC']]];
            $groupOptions['order'] = ['ManStatusGroupEntity.order' => 'ASC'];
            $groupOptions['conditions'] = ['partner_id' => $user['partner_id'], 'active' => 1];
            $statusGroups = TableRegistry::getTableLocator()->get('ManStatusGroupEntity')->find('all', $groupOptions);
            $statusGroups->disableHydration();
            $statusGroups = $statusGroups->toArray();
            foreach ($statusGroups as $statusGroup) {
                if(!is_array($statusGroup['man_status']) || count($statusGroup['man_status']) === 0){
                    continue;
                }
                $group = ['label' => $statusGroup['group_name'], 'id' => $statusGroup['id'], 'color' => $statusGroup['color']];
                $group['statuses'] = null;
                foreach ($statusGroup['man_status'] as $status)
                    $group['statuses'][$status['status_id']] = ['label' => $status['status_name'], 'id' => $status['status_id'], 'is_settled' => $status['is_settled'], 'is_fake_lead' => $status['is_fake_lead'], 'is_call_q_not_proceeding' => $status['is_call_q_not_proceeding'], 'count' => 0];
                $filters['Status'][$statusGroup['id']] = $group;
            }
        }

        $grouped_fields = ['latest_lender_status' => 'latest_lender_statuses'];
        // $grouped_fields = [];
        $lender_filter = !empty($filter_data['lender']) ? (is_array($filter_data['lender']) ? $filter_data['lender'] : [$filter_data['lender']]) : [];
        $filterLenderIds = [];
        $filterLenderStrings = [];
        foreach ($lender_filter as $value) {
            if (is_int($value)) {
                $filterLenderIds[] = $value;
            } else {
                $filterLenderStrings[] = $value;
            }
        }
        
        $productTypeIds = [];
        foreach ($data as $in => $row) {
            $rowData = [];
            $row['lender_id'] = $row['lead_associated_data']['max_lender_id'];
            $row['lender_name'] = $row['lead_associated_data']['lender_name'] ? ucwords(strtolower(trim($row['lead_associated_data']['lender_name']))) : null;
            $row['lender_logo'] = $row['lead_associated_data']['lender'] ? $row['lead_associated_data']['lender']['lender_logo'] : null;
            $row['latest_note'] = [
                'note' => $row['lead_notes'][$row['lead_notes'] ? count($row['lead_notes']) - 1 : 0]['notes'] ?? null,
                'created' => $row['lead_notes'][$row['lead_notes'] ? count($row['lead_notes']) - 1 : 0]['created'] ?? null
            ];
            unset($row['lead_associated_data']['max_lender_id']);
            unset($row['lead_associated_data']['lender_name']);
            unset($row['lead_associated_data']['lender']);
            unset($row['lead_associated_data']['note']);

            //check lender filter
            if(!empty($lender_filter)){//there are filter values
                $found = false;
                if(($row['lender_id'] !== Configure::read('Lend.OFF_PANEL_LENDER_ID')) //not an off panel lender
                    && !empty($filterLenderIds)
                    && in_array($row['lender_id'], $filterLenderIds)){
                        $found = true;
                }
                else if(!empty($filterLenderStrings)
                    && in_array($row['lender_name'], $filterLenderStrings)){
                        $found = true;
                }
                if($found === false){
                    continue;
                }
            }

            foreach ($row as $index => $field) {
                if (in_array($index, $retainFields)) {
                    $rowData[$index] = $field;
                }
            }
            //add grouped fields
            foreach ($grouped_fields as $fieldName => $groupName) {
                if (isset($row[$groupName])) {
                    $rowData[$fieldName] = $row[$groupName] ? $row[$groupName][0] : null;
                    if ($rowData[$fieldName])
                        unset($rowData[$fieldName]['lead_id']);
                }
            }
            //count filters
            $prodType = $row['partner_product_type']['product_type_id'] ?? null;
            if (isset($productTypeIds[$prodType]))
                $productTypeIds[$prodType]++;
            else
                $productTypeIds[$prodType] = 1;
            if (($isManual !== true) && isset($row['lender_status_ent'])) {
                $lenderStatusId = $row['lender_status_ent']['lender_status_id'];
                $lendStatusId = $row['lender_status_ent']['lender_status']['lend_status']['lend_status_id'];
                $statusName = $row['lender_status_ent']['lender_status']['combined_status_string'];
                if (!isset($filters['Status'][$lendStatusId])) {
                    // $group = [];
                    $group['name'] = $row['lender_status_ent']['lender_status']['lend_status']['status_name'];
                    $group['group_name'] = $row['lender_status_ent']['lender_status']['lend_status']['group_name'];
                    $group['statuses'] = [];
                    $filters['Status'][$lendStatusId] = $group;
                }
                if (!isset($filters['Status'][$lendStatusId]['statuses'][$lenderStatusId]))
                    $filters['Status'][$lendStatusId]['statuses'][$lenderStatusId] = ['id' => $lenderStatusId, 'name' => $statusName, 'count' => 1];
                else
                    $filters['Status'][$lendStatusId]['statuses'][$lenderStatusId]['count']++;
            } else if (isset($row['man_status']) && ($isManual === true)) {
                $filters['Status'][$row['man_status']['man_status_group']['group_id']]['statuses'][$row['man_status']['status_id']]['count']++;
            }

            if (isset($row['lead_type'])) {
                if (isset($filters['CustomerType'][$row['lead_type']]))
                    $filters['CustomerType'][$row['lead_type']]['count']++;
                else
                    $filters['CustomerType'][$row['lead_type']] = ['label' => $row['lead_type'], 'count' => 1];
            }
            if (isset($row['partner_user_lead'])) {
                if (isset($filters['Assignee']) && isset($filters['Assignee'][$row['partner_user_lead']['user']['partner_user_id']]))
                    $filters['Assignee'][$row['partner_user_lead']['user']['partner_user_id']]['count']++;
                else
                    $filters['Assignee'][$row['partner_user_lead']['user']['partner_user_id']] = ['label' => $row['partner_user_lead']['user']['name'], 'id' => $row['partner_user_lead']['user']['partner_user_id'], 'count' => 1];
            } else {//not assigned
                if (isset($filters['Assignee']) && isset($filters['Assignee'][0]))
                    $filters['Assignee'][0]['count']++;
                else
                    $filters['Assignee'][0] = ['label' => 'Unassigned', 'id' => 0, 'count' => 1];
            }
            if (isset($row['lender_name'])) {
                $str = $row['lender_name']."+".$row['lender_id'];
                if (isset($filters['Lender'][$str]))
                    $filters['Lender'][$str]['count']++;
                else
                    $filters['Lender'][$str] = ['label' => $row['lender_name'], 'id' => $row['lender_id'], 'count' => 1];
            }
            if (isset($row['tag_id'])) {
                if (isset($filters['Tag'][$row['tag_id']]))
                    $filters['Tag'][$row['tag_id']]['count']++;
                else
                    $filters['Tag'][$row['tag_id']] = [
                        'label' => $row['tag']['tag'],
                        'id' => $row['tag_id'],
                        'count' => 1,
                        'color' => $row['tag']['color']
                    ];
            }
            if (isset($row['referrer_person_id'])) {
                if (isset($filters['Referrer']["r_" . $row['referrer_person_id']]))
                    $filters['Referrer']["r_" . $row['referrer_person_id']]['count']++;
                else
                    $filters['Referrer']["r_" . $row['referrer_person_id']] = [
                        'label' => $row['referrer_person']['first_name'] . " " . $row['referrer_person']['last_name'],
                        'id' => $row['referrer_person_id'],
                        'type' => "referrer",
                        'count' => 1
                    ];
            }
            if ($user['account_type'] == 'Intermediary') {
                if (isset($row['intermediary_lender_mapping']) && isset($row['intermediary_lender_mapping']['original_lead']['partner'])) {
                    if (isset($filters['Referrer']["i_" . $row['intermediary_lender_mapping']['original_lead']['partner']['partner_id']]))
                        $filters['Referrer']["i_" . $row['intermediary_lender_mapping']['original_lead']['partner']['partner_id']]['count']++;
                    else
                        $filters['Referrer']["i_" . $row['intermediary_lender_mapping']['original_lead']['partner']['partner_id']] = [
                            'label' => $row['intermediary_lender_mapping']['original_lead']['partner']['organisation_name'],
                            'id' => $row['intermediary_lender_mapping']['original_lead']['partner']['partner_id'],
                            'type' => "intermediary",
                            'count' => 1
                        ];
                }
            }

            $leadRefs[] = $rowData['lead_ref'];
            if($getManStatusGroupTotals){
                $groupId = $rowData['man_status']['man_status_group']['group_id'] ?? null;
                if(!isset($manStatusGroupTotals[$groupId])){
                    $manStatusGroupTotals[$groupId]['total'] = 0;
                    $manStatusGroupTotals[$groupId]['totalValue'] = 0;
                }
                $manStatusGroupTotals[$groupId]['total']++;
                $manStatusGroupTotals[$groupId]['totalValue'] += intval($rowData['amount_requested']);
            }
            if($kanbanV3){//create kanban V3 data
                $rowDataV3 = [];
                $rowDataV3['lead_ref'] = $rowData['lead_ref'];
                $rowDataV3['lead_type'] = $rowData['lead_type'];
                $rowDataV3['organisation_name'] = $rowData['organisation_name'];
                $rowDataV3['amount_requested'] = $rowData['amount_requested'];
                $rowDataV3['last_changed_date'] = $rowData['last_changed_date'];
                $rowDataV3['status'] = $rowData['man_status']?$rowData['man_status']['status_name']: null;
                $rowDataV3['status_id'] = $rowData['man_status']?$rowData['man_status']['status_id']: null;
                $rowDataV3['status_group_id'] = $rowData['man_status']? ($rowData['man_status']['man_status_group']?$rowData['man_status']['man_status_group']['group_id']: null):null;
                $rowDataV3['status_group_colour'] = $rowData['man_status']? ($rowData['man_status']['man_status_group']?$rowData['man_status']['man_status_group']['color']: null):null;
                $rowDataV3['lender'] = $rowData['lender_name'];
                $rowDataV3['lender_logo'] = $rowData['lender_logo'];
                $rowDataV3['poc_first_name'] = $rowData['owner_poc']?$rowData['owner_poc']['first_name']:null;
                $rowDataV3['poc_last_name'] = $rowData['owner_poc']?$rowData['owner_poc']['last_name']:null;
                $rowDataV3['poc_mobile'] = $rowData['owner_poc']?$rowData['owner_poc']['mobile']:null;
                $rowDataV3['poc_phone'] = $rowData['owner_poc']?$rowData['owner_poc']['phone']:null;
                $rowDataV3['poc_avatar_image_url'] = $rowData['owner_poc']?$rowData['owner_poc']['avatar_image_url']:null;
                $rowDataV3['assignee'] = $rowData['partner_user_lead']? ($rowData['partner_user_lead']['user']?$rowData['partner_user_lead']['user']['name']: null):null;
                $rowDataV3['assignee_kanban_colour'] = $rowData['partner_user_lead']? ($rowData['partner_user_lead']['user']?$rowData['partner_user_lead']['user']['kanban_colour']: null):null;
                $rowDataV3['assignee_user_id'] = $rowData['partner_user_lead']? ($rowData['partner_user_lead']['user']?$rowData['partner_user_lead']['user']['partner_user_id']: null):null;
                $rowDataV3['latest_note'] = $rowData['latest_note']? $rowData['latest_note']['note']:null;
                $rowDataV3['latest_note_created'] = $rowData['latest_note']? $rowData['latest_note']['created']:null;
                $rowDataV3['tag_colour'] = $rowData['tag']? $rowData['tag']['color']:null;
                $rowDataV3['tag'] = $rowData['tag']? $rowData['tag']['tag']:null;
                $rowDataV3['product_type_name'] = $rowData['partner_product_type']? $rowData['partner_product_type']['product_type_name']:null;
                $rowDataV3['is_off_panel'] = $rowData['lender_id']? (($rowData['lender_id'] === 30) ? true: false):false;
                $rowDataV3['swimlane_id'] = null;
                if($rowData['partner_product_type'] 
                    && $rowData['partner_product_type']['kanban_swimlane_product_types'] 
                    && (count($rowData['partner_product_type']['kanban_swimlane_product_types']) > 0)){
                        $rowDataV3['swimlane_id'] = $rowData['partner_product_type']['kanban_swimlane_product_types'][0]['kanban_swimlane_id'];
                    }
                $cleanedDataV3[] = $rowDataV3;
            }
            if ($onlyLeads) {
                $cleanedData['leads'][$rowData['lead_ref']] = $rowData;
            } else
                $cleanedData[] = $rowData;
        }
        //populate product type labels
        $productTypes = [];
        if (count($productTypeIds) > 0) {
            $productTypes = TableRegistry::getTableLocator()->get('PartnerProductTypeEntity')->find('all', ['fields' => ['product_type_id', 'product_type_name'], 'conditions' => ['product_type_id IN' => array_keys($productTypeIds)]]);
            $productTypes->disableHydration();
            $productTypes = $productTypes->toArray();
            $productTypes[] = ['product_type_name' => 'Unknown', 'product_type_id' => null];
            foreach ($productTypes as $productType) {
                $filters['ProductType'][] = ['label' => $productType['product_type_name'], 'id' => $productType['product_type_id'], 'count' => $productTypeIds[$productType['product_type_id']] ?? 0];
            }
        }
        foreach ($filters as $i => $filter) {
            if ($i === 'Status') {
                foreach ($filter as $j => $group)
                    $filter[$j]['statuses'] = array_values($group['statuses']);
            } else if ($i === 'Assignee') {
                $unassigned = [];
                if (isset($filter[0])) {
                    $unassigned = $filter[0];
                    unset($filter[0]);
                }
                usort($filter, function ($a, $b) {
                    return strtolower($a['label']) <=> strtolower($b['label']);
                });
                if (count($unassigned) > 0)
                    $filter = array_merge([$unassigned], $filter);
                $filters[$i] = $filter;
            }
            $filters[$i] = array_values($filter);
        }
        if (!$kanban) {
            $partner_user_table = TableRegistry::getTableLocator()->get('PartnerUserEntity');
            $partner_user = $partner_user_table->get($partner_user_id);
            if (!$getOpenList)
                $partner_user->set('closed_leads_filter', json_encode($filter_data));
            else
                $partner_user->set('kanban_filter', json_encode($filter_data));
            $partner_user_table->save($partner_user);
        }
        $result = ['success' => true, 'full_total' => $fullCount, 'total' => count($cleanedData), 'filters' => $filters];
        if($getLeadRefs === true){
            $result['lead_refs'] = $leadRefs; 
        }
        if($getManStatusGroupTotals === true){
            $result['manStatusGroupTotals'] = $manStatusGroupTotals; 
        }
        if($kanbanV3 === true){
            $result['kanbanV3'] = $cleanedDataV3;
        }
        if ($noLeads !== true)
            $result['leads'] = $cleanedData;
        if ($onlyLeads){
            $cleanedData['lead_refs'] = $leadRefs;
            return $cleanedData;
        }
        return $result;
    }

    /**
     * Create data for Kanban board with positions
     * @param mixed $leadsData
     * @return array
     */
    private function generateKanbanData($leadsData)
    {
        if (getenv('LEND_ENV') == 0) {
            $user = $this->Auth->user();
        } else {
            $user = $this->Auth->identify();
        }
        $prodTypeSwimlaneMap = [];
        foreach ($this->swimlanes as $swimlane)
            foreach ($swimlane['product_types'] as $prodType)
                $prodTypeSwimlaneMap[$prodType] = $swimlane['id'];
        $groupedData = [];
        $kanbanUpdated = false;
        $kanbanData = [];
        $partner_user_table = TableRegistry::getTableLocator()->get('PartnerUsers');
        $partner_user_entity = $partner_user_table->get($user['partner_user_id'], ['fields' => ['partner_user_id', 'kanban_leads']]);
        $partner_user = $partner_user_entity->toArray();
        $leadIds = array_column($leadsData, 'lead_id');//indexes would match those of leadsData
        $dataExists = false;
        if ($partner_user['kanban_leads'] !== null) {
            $kanbanData = is_array($partner_user['kanban_leads']) ? $partner_user['kanban_leads'] : json_decode($partner_user['kanban_leads'], true);
            if (count($kanbanData) > 0)
                $dataExists = true;
            else {//set to null, shouldn't have blank arrays
                $partner_user_table->patchEntity($partner_user_entity, ['kanban_leads' => null]);
                $partner_user_table->save($partner_user_entity);
            }
        }
        if ($dataExists === true) {
            $kanbanData = is_array($partner_user['kanban_leads']) ? $partner_user['kanban_leads'] : json_decode($partner_user['kanban_leads'], true);
            //check for leads where status group/swimlane has changed. Add leads to new group/swimlane at top and remove from old group/swimlane and reindex
            foreach ($leadsData as $lead) {
                $oldGroupId = isset($kanbanData['leads'][$lead['lead_id']]) ? $kanbanData['leads'][$lead['lead_id']]['group_id'] : null;
                $oldSwimlaneId = isset($kanbanData['leads'][$lead['lead_id']]) ? $kanbanData['leads'][$lead['lead_id']]['swimlane_id'] : null;
                $groupId = $lead['man_status']['man_status_group']['group_id'];
                $prodTypeId = $lead['partner_product_type']['product_type_id'];
                $swimlaneId = $prodTypeId ? $prodTypeSwimlaneMap[$prodTypeId] : null;
                if (!is_null($oldGroupId) && (($oldGroupId != $groupId) || ($oldSwimlaneId != $swimlaneId))) {
                    $keys = array_keys($kanbanData['positions'][$oldSwimlaneId ?? $this->nullSwimLaneId][$oldGroupId], $lead['lead_id']);
                    foreach ($keys as $k) {
                        array_splice($kanbanData['positions'][$oldSwimlaneId ?? $this->nullSwimLaneId][$oldGroupId], $k, 1);
                    }
                    if (!isset($kanbanData['positions'][$swimlaneId ?? $this->nullSwimLaneId])
                        || !isset($kanbanData['positions'][$swimlaneId ?? $this->nullSwimLaneId][$groupId])
                        || !is_array($kanbanData['positions'][$swimlaneId ?? $this->nullSwimLaneId][$groupId])) {
                        $kanbanData['positions'][$swimlaneId ?? $this->nullSwimLaneId][$groupId] = [$lead['lead_id']];
                    } else if (!in_array($lead['lead_id'], $kanbanData['positions'][$swimlaneId ?? $this->nullSwimLaneId][$groupId])) {
                        array_unshift($kanbanData['positions'][$swimlaneId ?? $this->nullSwimLaneId][$groupId], $lead['lead_id']);//add lead_id to beginning of the positions array(for swimlane+group)
                    }
                    $kanbanData['leads'][$lead['lead_id']] = ['group_id' => $groupId, 'swimlane_id' => $swimlaneId];//update group_id, swimlane_id
                    $kanbanUpdated = true;
                }
            }

            //check for new leads and add to top of groups
            $new_leads = array_diff($leadIds, array_keys($kanbanData['leads']));
            if (count($new_leads) > 0) {//have to add these new leads to the top/beginning of the group/column
                foreach ($new_leads as $leadId) {
                    $key = array_search($leadId, $leadIds);
                    $groupId = $leadsData[$key]['man_status']['man_status_group']['group_id'];
                    $prodTypeId = $leadsData[$key]['partner_product_type']['product_type_id'];
                    $swimlaneId = $prodTypeId ? $prodTypeSwimlaneMap[$prodTypeId] : null;
                    if (!isset($kanbanData['positions'][$swimlaneId ?? $this->nullSwimLaneId])
                        || !isset($kanbanData['positions'][$swimlaneId ?? $this->nullSwimLaneId][$groupId])
                        || !is_array($kanbanData['positions'][$swimlaneId ?? $this->nullSwimLaneId][$groupId])) {
                        $kanbanData['positions'][$swimlaneId ?? $this->nullSwimLaneId][$groupId] = [$leadId];
                    } else if (!in_array($leadId, $kanbanData['positions'][$swimlaneId ?? $this->nullSwimLaneId][$groupId])) {
                        array_unshift($kanbanData['positions'][$swimlaneId ?? $this->nullSwimLaneId][$groupId], $leadId);//add lead_id to beginning of the positions array(for group)
                    }
                    $kanbanData['leads'][$leadId] = ['group_id' => $groupId, 'swimlane_id' => $swimlaneId];//add lead id, group_id, product_type_id to leads
                }
                $kanbanUpdated = true;
            }
        } else {//first use of kanban/first time leads exist - generate position data
            foreach ($leadsData as $lead) {
                $groupId = $lead['man_status']['man_status_group']['group_id'];
                $prodTypeId = $lead['partner_product_type']['product_type_id'];
                $swimlaneId = $prodTypeId ? $prodTypeSwimlaneMap[$prodTypeId] : null;
                $kanbanData['leads'][$lead['lead_id']] = ['group_id' => $groupId, 'swimlane_id' => $swimlaneId];//add lead id, group_id, product_type_id to leads
                if (!isset($kanbanData['positions'][$swimlaneId ?? $this->nullSwimLaneId])
                    || !isset($kanbanData['positions'][$swimlaneId ?? $this->nullSwimLaneId][$groupId])
                    || !is_array($kanbanData['positions'][$swimlaneId ?? $this->nullSwimLaneId][$groupId])) {
                    $kanbanData['positions'][$swimlaneId ?? $this->nullSwimLaneId][$groupId] = [$lead['lead_id']];
                } else if (!in_array($lead['lead_id'], $kanbanData['positions'][$swimlaneId ?? $this->nullSwimLaneId][$groupId]))
                    $kanbanData['positions'][$swimlaneId ?? $this->nullSwimLaneId][$groupId][] = $lead['lead_id'];//most recent records at top on the group
            }
            if (count($kanbanData) > 0)
                $kanbanUpdated = true;
        }
        $manStatusGroups = TableRegistry::getTableLocator()->get('ManStatusGroupEntity')->find('all', ['fields' => ['id', 'order'], 'conditions' => ['partner_id' => $user['partner_id'], 'active' => true]]);
        $manStatusGroups->disableHydration();
        $manStatusGroups = $manStatusGroups->toArray();
        $manStatusGroups = array_column($manStatusGroups, 'order', 'id');

        //created nested array (swimlane > status groups > leads)
        foreach ($kanbanData['positions'] as $swimlane_id => $group_ids) {
            foreach ($group_ids as $group_id => $lead_ids) {
                foreach ($lead_ids as $position => $lead_id) {
                    if (!in_array($lead_id, $leadIds))
                        continue;
                    $key = array_search($lead_id, $leadIds);
                    $lead = $leadsData[$key];
                    $swimlane_name = isset($this->swimlanes[$swimlane_id - 1]) ? $this->swimlanes[$swimlane_id - 1]['name'] : "No Product";
                    $groupedData[$swimlane_id]['swimlane_id'] = $swimlane_id;
                    $groupedData[$swimlane_id]['swimlane_name'] = $swimlane_name;
                    $groupedData[$swimlane_id]['groups'][$manStatusGroups[$group_id]]['group_id'] = $lead['man_status']['man_status_group']['group_id'];
                    $groupedData[$swimlane_id]['groups'][$manStatusGroups[$group_id]]['group_name'] = $lead['man_status']['man_status_group']['group_name'];
                    $groupedData[$swimlane_id]['groups'][$manStatusGroups[$group_id]]['color'] = $lead['man_status']['man_status_group']['color'];
                    if (!isset($groupedData[$swimlane_id]['groups'][$manStatusGroups[$group_id]]['leads']))
                        $groupedData[$swimlane_id]['groups'][$manStatusGroups[$group_id]]['leads'] = [];
                    $groupedData[$swimlane_id]['groups'][$manStatusGroups[$group_id]]['leads'][] = $lead;
                }
            }
        }
        ksort($groupedData);
        $groupedData = array_values($groupedData);
        foreach ($groupedData as $prodTypeId => $prodType) {
            ksort($prodType['groups']);
            $groupedData[$prodTypeId]['groups'] = array_values($prodType['groups']);
        }
        //update user kanban data if required
        if ($kanbanUpdated) {
            $partner_user_table->patchEntity($partner_user_entity, ['kanban_leads' => json_encode($kanbanData)]);
            $partner_user_table->save($partner_user_entity);
        }
        return $groupedData;
    }

    public function removeDuplicates($remove = false)
    {
        $users = TableRegistry::getTableLocator()->get('PartnerUsers')->find('all', ['conditions' => ['kanban_leads IS NOT NULL']]);
        $users->disableHydration();
        $users = $users->toArray();
        $duplicates = false;
        foreach ($users as $user) {
            $count = [];
            $kanban_leads = is_array($user['kanban_leads']) ? $user['kanban_leads'] : json_decode($user['kanban_leads'], true);
            foreach ($kanban_leads['positions'] as $swimlaneId => $groups) {
                foreach ($groups as $groupId => $values) {
                    if ($remove !== false)
                        $kanban_leads['positions'][$swimlaneId][$groupId] = array_values(array_unique($values));
                    foreach ($values as $value) {
                        if (isset($count[$value]))
                            $count[$value]++;
                        else
                            $count[$value] = 1;
                    }
                }
            }
            if ($remove !== false) {
                $partner_user_table = TableRegistry::getTableLocator()->get('PartnerUsers');
                $partnerUserEntity = $partner_user_table->get($user['partner_user_id']);
                $partner_user_table->patchEntity($partnerUserEntity, ['kanban_leads' => json_encode($kanban_leads)]);
                $partner_user_table->save($partnerUserEntity);
            } else {
                foreach ($count as $l => $c) {
                    if ($c > 1) {
                        $duplicates = true;
                    }
                }
            }
        }
        if ($remove !== false)
            $result = ['success' => true, 'message' => 'Duplicates removed'];
        else
            $result = ['success' => true, 'duplicates found: ' => ($duplicates === true ? "true" : "false")];
        return $this->setJsonResponse($result);
    }

    /**
     * Get data for quick view
     * @param mixed $lead_ref
     * @return void
     * @throws \Exception
     */
    public function quickView($lead_ref)
    {
        try {
            if (empty($lead_ref)) {
                throw new \Exception("Lead ref is required.");
            }
            $lend_internal_auth = new LendInternalAuth;
            $lead_id = $lend_internal_auth->unhashLeadId($lead_ref);
            if (empty($lead_id)) {
                throw new \Exception("Can't find a lead.");
            }
            $lead_table = TableRegistry::getTableLocator()->get('LeadEntity');
            $associated = [
                'PocOwner' => ['fields' => ['first_name', 'last_name', 'phone', 'mobile', 'email', 'avatar_image_url']],
                'ManStatusEntity' => ['fields' => ['ManStatusEntity__status_id' => 'ManStatusEntity.id', 'status_name']],//manual status
                'PartnerProductTypeEntity' => ['fields' => ['product_type_id', 'product_type_name']],//product tyoe
                'PartnerUserLeadsEntity' => ['fields' => ['partner_user_id']],//assignee
                'PartnerUserLeadsEntity.PartnerUserEntity' => ['fields' => ['partner_user_id', 'name']],//assignee
                'PartnerTagEntity' => ['fields' => ['tag', 'color', 'PartnerTagEntity.id']],//rags
                'LendStatusEntity' => ['fields' => ['status_name']]
            ];

            $options = ['contain' => $associated];
            $options['fields'] = ['lead_ref', 'organisation_name', 'created', 'last_changed_date', 'amount_requested', 'lead_type', 'is_closed'];
            if (getenv('LEND_ENV') == 0) {
                $user = $this->Auth->user();
            } else {
                $user = $this->Auth->identify();
            }
            if ($user['access_all_leads'])
                $options['conditions'] = ['LeadEntity.partner_id' => $user['partner_id']];
            else {
                $options['conditions'] = ['PartnerUserLeadsEntity.partner_user_id' => $user['partner_user_id']];
            }

            $lead = $lead_table->get($lead_id, $options);

            $user = $this->Auth->user();
            if (empty($user)) {
                $user = $this->Auth->identify();
            }
            $permission_check = $this->checkPermission($lead_id, null, $user['account_type'] === 'Applicant');
            if (!$permission_check['success']) {
                throw new \Exception($permission_check['message']);
            }
            return $this->setJsonResponse($lead);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
        }
    }

    /**
     * Update a lead
     * @return void
     * @throws \Exception
     */
    public function update()
    {
        try {
            if (!$this->request->is('post')) {
                throw new \Exception("only POST request is available.", 405);
            }
            $schema = [
                "lead_ref" => null,
                "man_status_id" => null,
                "kanban_colour" => null,
                "assignee" => null,
                "is_closed" => null,
                "tag_id" => null,
                "kanban_data" => null,
            ];
            $data = array_intersect_key($this->request->getData(), $schema);
            $schema['lead_id'] = null;
            if (empty($data['lead_ref'])) {
                throw new \Exception("lead_ref is required.");
            }
            $lend_internal_auth = new LendInternalAuth;
            $lead_id = $lend_internal_auth->unhashLeadId($data['lead_ref']);
            if (empty($lead_id)) {
                throw new \Exception("Can't find a lead.");
            }
            unset($data['lead_ref']);
            if (getenv('LEND_ENV') == 0) {
                $user = $this->Auth->user();
            } else {
                $user = $this->Auth->identify();
            }

            $lead_table = TableRegistry::getTableLocator()->get('LeadEntity');
            $lead = $lead_table->get($lead_id, ['contain' => ['PartnerUserLeadsEntity']]);

            if ((int)$user['partner_id'] !== (int)$lead->partner_id) {
                throw new \Exception("You don't have permission to update.");
            }

            if (($user['access_all_leads'] != true) && ((int)$user['partner_user_id'] !== (int)$lead->partner_user_lead->partner_user_id)) {
                throw new \Exception("You don't have permission to update.");
            }

            if ($data['tag_id'] === 0) {
                $data['tag_id'] = null;
            }

            $assignee = null;

            if (isset($lead['partner_user_lead']) && isset($lead['partner_user_lead']['partner_user_id']))
                $assignee = $lead['partner_user_lead']['partner_user_id'];
            $options = [];
            if (isset($data['assignee']) && $data['assignee'] != $assignee) {
                $partner_user_leads_table = TableRegistry::getTableLocator()->get('PartnerUserLeadsEntity');
                $new_assignment = $partner_user_leads_table->newEntity([
                    'partner_user_id' => $data['assignee'],
                    'status' => 'ACCESS',
                    'lead_id' => $lead_id
                ]);
                if ($partner_user_leads_table->save($new_assignment)) {
                    $assignee = $data['assignee'];
                    unset($data['assignee']);
                }
            }
            if (isset($data['kanban_data']) || ($data['is_closed'] && ($data['is_closed'] == 1))) {
                $partner_user_table = TableRegistry::getTableLocator()->get('PartnerUsers');
                $partnerUserEntity = $partner_user_table->get($user['partner_user_id'], ['fields' => ['partner_user_id', 'kanban_leads']]);
                $partnerUser = $partnerUserEntity->toArray();
                $kanbanData = is_array($partnerUser['kanban_leads']) ? $partnerUser['kanban_leads'] : json_decode($partnerUser['kanban_leads'], true);
                $kanbanDataIn = $data['kanban_data'];
                //update kanban data that comes through
                if ($kanbanDataIn) {
                    $swimlaneId = $kanbanDataIn['swimlane_id'];
                    $groups = $kanbanDataIn['groups'];
                    $kanbanData['positions'][$swimlaneId ?? $this->nullSwimLaneId] = [];
                    $duplicates = [];
                    foreach ($groups as $groupId => $leadRefs) {
                        //detect duplicates if any
                        $val_count = array_count_values($leadRefs);
                        foreach ($val_count as $val => $count) {
                            if ($count > 1)
                                $duplicates[$groupId][] = $val;
                        }
                        $leadRefs = array_unique($leadRefs);
                        $leadIds = [];
                        foreach ($leadRefs as $leadRef) {
                            $leadId = $lend_internal_auth->unhashLeadId($leadRef);
                            $leadIds[] = $leadId;
                            $kanbanData['leads'][$leadId] = ['group_id' => $groupId, 'swimlane_id' => $swimlaneId];
                        }
                        $kanbanData['positions'][$swimlaneId ?? $this->nullSwimLaneId][$groupId] = $leadIds;
                    }
                    if (count($duplicates) > 0) {
                        $message = "Following duplicate leads were found within groups:" . PHP_EOL;
                        foreach ($duplicates as $g => $l) {
                            $message .= "Group: " . $g . PHP_EOL;
                            $message .= "Leads: " . implode(",", $l) . PHP_EOL;
                        }
                        return $this->setJsonResponse(['error' => $message], 400);
                    }
                }
                //remove lead from kanban data if it's closed
                if ($data['is_closed'] && $data['is_closed'] == 1) {
                    if (is_array($kanbanData)) {
                        $leadData = $kanbanData['leads'][$lead_id] ?? null;
                        if ($leadData) {
                            if (($key = array_search($lead_id, $kanbanData['positions'][$leadData['swimlane_id'] ?? $this->nullSwimLaneId][$leadData['group_id']])) !== false) {
                                unset($kanbanData['positions'][$leadData['swimlane_id'] ?? $this->nullSwimLaneId][$leadData['group_id']][$key]);
                            }
                            unset($kanbanData['leads'][$lead_id]);
                        }
                    }
                }
                $partner_user_table->patchEntity($partnerUserEntity, ['kanban_leads' => json_encode($kanbanData)]);
                $partner_user_table->save($partnerUserEntity);
                unset($data['kanban_data']);
            }


            $lead_table->patchEntity($lead, $data, $options);
            $lead_table->save($lead);

            unset($lead['lead_id'], $lead['partner_user_lead']);
            $lead = $lead->toArray();
            $lead['assignee'] = $assignee;
            //email notification send from PartnerUserLeadsTable ===> afterSave()
            return $this->setJsonResponse(is_array($lead) ? array_intersect_key($lead, $schema) : $lead);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
        }
    }

    /**
     * Convert conditions based on filter data
     * @param mixed $filterData
     * @return QueryExpression|array
     */
    private function convertConditions($filterData)
    {
        $filterData = $this->checkFilterData($filterData);
        $acceptedFields = [
            'start_date',
            'end_date',
            'customer_type',
            'assignee',
            'product_type',
            'lender',
            'is_closed',
            'is_archived',
            'status',
            'closedTotal',
            'tag_id',
            'referrer',
            'settled_mode'
        ];
        $operationMap = [
            'start_date' => 'more_than',
            'end_date' => 'less_than',
            'status' => 'contains',
            'customer_type' => 'contains',
            'assignee' => 'contains',
            'lender' => 'contains',
            'product_type' => 'contains',
            'is_closed' => 'equal',
            'is_archived' => 'equal',
            'tag_id' => 'contains',
            'referrer' => 'contains',
        ];
        $fieldMap['filter_by_date'] = ['created' => 'date(LeadEntity.created)', 'updated' => 'date(LeadEntity.last_changed_date)', 'funded' => 'date(PartnerCommission.funded_date)'];
        $fieldMap['funded_filter_by_date'] = ['created' => 'LeadEntity.created', 'updated' => 'LeadEntity.last_changed_date'];
        $fieldMap['status'] = 'LeadEntity.man_status_id';
        $fieldMap['lender'] = 'LenderProductEntity.lender_id';
        $fieldMap['is_closed'] = 'LeadEntity.is_closed';
        $fieldMap['is_archived'] = 'LeadEntity.is_archived';
        $fieldMap['customer_type'] = 'LeadEntity.lead_type';
        $fieldMap['assignee'] = 'PartnerUserLeadsEntity.partner_user_id';
        $fieldMap['settled_date'] = 'PartnerCommission.funded_date';
        $fieldMap['tag_id'] = 'LeadEntity.tag_id';
        $fieldMap['referrer'] = 'LeadEntity.referrer_person_id';

        $fieldMap['product_type'] = 'LeadEntity.product_type_id';
        $conditions_data = [];
        $conditions = [];

        foreach ($acceptedFields as $acceptedField) {
            if (array_key_exists($acceptedField, $filterData)) {
                switch ($acceptedField) {
                    case 'start_date':
                        if (isset($filterData['end_date'])){
                            $condition = [
                                'operation' => 'between', 
                                'field' => $fieldMap['filter_by_date'][$filterData['filter_by_date']], 
                                'value' => [
                                    'from' => $filterData['start_date'], 
                                    'to' => $filterData['end_date']
                                ], 
                                'settled_date' => [
                                    'field' => $fieldMap['settled_date'], 
                                    'value' => $filterData['settled_date']
                                ], 
                                'status' => isset($filterData['status']) ? ['field' => $fieldMap['status'], 'value' => $filterData['status']] : null
                            ];
                            if (isset($filterData['settled_mode']) && ($filterData['settled_mode'] === true)){
                                $condition['settled_mode'] = [
                                    'field' => $fieldMap['filter_by_date']['funded'],
                                    'value' => [
                                        'from' => $filterData['start_date'], 
                                        'to' => $filterData['end_date']
                                    ], 
                                ];
                            }
                            $conditions_data[] = $condition;
                        }
                        else
                            $conditions_data[] = ['operation' => $operationMap[$acceptedField], 'field' => $fieldMap['filter_by_date'][$filterData['filter_by_date']], 'value' => $filterData[$acceptedField]];
                        break;

                    case 'end_date':
                        if (!isset($filterData['start_date']))
                            $conditions_data[] = ['operation' => $operationMap[$acceptedField], 'field' => $fieldMap['filter_by_date'][$filterData['filter_by_date']], 'value' => $filterData[$acceptedField]];
                        break;

                    case 'lender':
                        break;

                    default:
                        $conditions_data[] = ['operation' => $operationMap[$acceptedField], 'field' => $fieldMap[$acceptedField], 'value' => $filterData[$acceptedField]];
                        break;
                }
            }
        }
        if (!array_key_exists('is_archived', $filterData)) {
            $conditions_data[] = ['operation' => 'equal', 'field' => 'LeadEntity.is_archived', 'value' => 0];
        }
        $conditions = $this->generateConditions($conditions_data);
        return $conditions;
    }

    /**
     * Sanity check incoming filter data
     * @param mixed $filterData
     * @return array
     */
    private function checkFilterData($filterData)
    {
        $acceptedFields = [
            'filter_by_date' => ['values' => ['created', 'updated']],
            'start_date' => ['type' => 'date'],
            'end_date' => ['type' => 'date'],
            'funded_filter_by_date' => null,
            'status' => ['Entity' => 'ManStatus'],
            'customer_type' => ['values' => ['commercial', 'consumer']],
            'assignee' => ['Entity' => 'PartnerUser'],
            'product_type' => ['Entity' => 'PartnerProductType'],
            'lender' => ['Entity' => 'Lender'],
            'is_closed' => ['values' => ['0', '1', 0, 1]],
            'is_archived' => ['values' => ['0', '1', 0, 1]],
            'sort' => null
        ];
        // $filterData = array_intersect_key($filterData, $acceptedFields);
        //TODO Add checks to validate values

        //$partner_users = TableRegistry::get('PartnerUsers')->getPartnerUser(array('partner_id'=>$lead['lead']['partner_id'],'active'=>'1'), true);
        return $filterData;
    }
}