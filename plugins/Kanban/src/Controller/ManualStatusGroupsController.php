<?php
namespace Ka<PERSON><PERSON>\Controller;

use <PERSON><PERSON><PERSON>\Controller\AppController;

use Cake\Event\Event;
use Cake\Core\Configure;
use Cake\Log\Log;
use App\Lend\LendInternalAuth;
use Cake\ORM\TableRegistry;

class ManualStatusGroupsController extends AppController
{

  public function initialize()
  {
    parent::initialize();
  }

  /**
   * addGroup
   * Add manualStatusGroup
   *
   * @return void
   */
  public function add(){
    try {
        if (!$this->request->is('post')) {
            throw new \Exception("only POST request is available.", 405);
        }
        $schema = [
            "group_name" => null,
            "color" => null,
            "order" => null,
            "active" => null,
            "after_group_id" => null,
        ];
        if (getenv('LEND_ENV') == 0) {
            $user = $this->Auth->user();
        } else {
            $user = $this->Auth->identify();
        }
        $man_status_group_data = array_intersect_key($this->request->getData(), $schema);
        $man_status_group_data['partner_id'] = $user['partner_id'];
        $man_status_group_data['active'] = 1;

        $man_status_group_table = TableRegistry::getTableLocator()->get('ManStatusGroupEntity');

        // Determine the order based on after_group_id
        if (!empty($man_status_group_data['after_group_id'])) {
            $afterGroup = $man_status_group_table->find()
                ->where(['id' => $man_status_group_data['after_group_id'], 'partner_id' => $user['partner_id']])
                ->first();
            if (!$afterGroup) {
                throw new \Exception("Invalid after_group_id.");
            }
            $man_status_group_data['order'] = $afterGroup->order + 1;

            // Increment the order of subsequent groups
            $man_status_group_table->updateAll(
                ['`order` = `order` + 1'], // Backtick the `order` column
                ['partner_id' => $user['partner_id'], '`order` >=' => $man_status_group_data['order']] // Backtick the `order` column
            );
        } else {
            // If no after_group_id is provided, place the new group at the end
            $maxOrder = $man_status_group_table->find()
                ->where(['partner_id' => $user['partner_id']])
                ->select(['max_order' => 'MAX(order)'])
                ->first()
                ->max_order;
            $man_status_group_data['order'] = $maxOrder + 1;
        }

        $man_status_group = $man_status_group_table->newEntity($man_status_group_data);
        $man_status_group_table->save($man_status_group);
        return $this->setJsonResponse($man_status_group);
    } catch (\Exception $e) {
        Log::error($e->getMessage());
        Log::error($e->getTraceAsString());
        return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }
  
  /**
   * edit
   *
   * @return void
   */
  public function edit($force_change = false){
    //TODO need to check if the status mapped to this group is in use by current lead, if yes, popup warning
    //do we need to do anything to the statuses?
    try{
      if (!$this->request->is('post')) {
        throw new \Exception("only POST request is available.", 405);
      }
      if (getenv('LEND_ENV') == 0) {
        $user = $this->Auth->user();
      } else {
        $user = $this->Auth->identify();
      }
      $partner_id = $user['partner_id'];
      $schema = [
        "id" => null,
        "group_name" => null,
        "color" => null,
        "order" => null,
        "after_group_id" => null,
        "active" => null,
     ];
      $man_status_group_data = array_intersect_key($this->request->getData(), $schema);
      $man_status_group_data['partner_id'] = $partner_id;
      if(!isset($man_status_group_data["id"])){
        throw new \Exception("id is required.");
      }
      $man_status_group_table = TableRegistry::getTableLocator()->get('ManStatusGroupEntity');
      if($force_change === false || $man_status_group_data['active'] == false){
        $leads_table = TableRegistry::getTableLocator()->get('LeadEntity');
        $options['fields'] = ['lead_id'];
        $options['contain'] = ['ManStatusEntity.ManStatusGroupEntity'];
        $options['conditions'] = ['LeadEntity.partner_id'=> $man_status_group_data["partner_id"], 'man_status_id IS NOT NULL', 'ManStatusGroupEntity.partner_id'=>$partner_id];
        $affectedLeads = $leads_table->find('all', $options)->where(['ManStatusGroupEntity.id'=> $man_status_group_data["id"]])->count();
        if($affectedLeads > 0)
          throw new \Exception($affectedLeads. " existing leads status would be affected");
      }
      unset($schema['after_group_id']);
      $man_status_group = $man_status_group_table->get($man_status_group_data["id"], ['fields'=> array_keys($schema), 'conditions'=>['partner_id'=>$partner_id]]);
      
      // Determine the order based on after_group_id
      if (!empty($man_status_group_data['after_group_id'])) {
        $afterGroup = $man_status_group_table->find()
            ->where(['id' => $man_status_group_data['after_group_id'], 'partner_id' => $partner_id])
            ->first();
        if (!$afterGroup) {
            throw new \Exception("Invalid after_group_id.");
        }
        
        $oldOrder = $man_status_group->order;
        $newOrder = $afterGroup->order + 1;
        $man_status_group_data['order'] = $newOrder;

        // Reorder groups based on the new position
        if ($oldOrder != $newOrder) {
          if ($oldOrder < $newOrder) {
            // Moving down - decrement groups between old and new positions
            $man_status_group_table->updateAll(
                ['`order` = `order` - 1'],
                ['partner_id' => $partner_id, '`order` >' => $oldOrder, '`order` <=' => $newOrder]
            );
          } else {
            // Moving up - increment groups between new and old positions
            $man_status_group_table->updateAll(
                ['`order` = `order` + 1'],
                ['partner_id' => $partner_id, '`order` >=' => $newOrder, '`order` <' => $oldOrder]
            );
          }
        }
      }
      unset($man_status_group_data['after_group_id']);
      $man_status_group_table->patchEntity($man_status_group, $man_status_group_data);
      $man_status_group_table->save($man_status_group);
      return $this->setJsonResponse(['success' => true, 'status_group' => $man_status_group]);

    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  /**
   * Reorder statuses within groups
   * @throws \Exception
   * @return void
   */
  public function editGroupsStatusesOrder(){
    try{
      if (!$this->request->is('post')) {
        throw new \Exception("only POST request is available.", 405);
      }
      if (getenv('LEND_ENV') == 0) {
        $user = $this->Auth->user();
      } else {
        $user = $this->Auth->identify();
      }
      $partner_id = $user['partner_id'];
      $data = $this->request->getData();
      if(!is_array($data))
        throw new \Exception("Invalid input data");
      $man_status_group_table = TableRegistry::getTableLocator()->get('ManStatusGroupEntity');
      $man_status_table = TableRegistry::getTableLocator()->get('ManStatusEntity');
      $man_status_ids = $man_status_table->find('list', [
        'conditions' => ['partner_id' => $partner_id, 'active' => 1], 
        'keyField' => 'id', 
        'valueField' => 'id'
        ])
        ->toList();
      $orderData = [];
      $manStatusIds = [];
      foreach ($data as $groupText => $statuses) {
        $groupId = intval($groupText);
        $group = $man_status_group_table->get($groupId);
        if ($group->partner_id !== $user['partner_id'])
          throw new \Exception('Invalid group present in list');
          if(!empty(array_diff($statuses, $man_status_ids)))
            throw new \Exception('Invalid statuses present in list');
        $manStatusIds = array_merge($manStatusIds, $statuses);
        foreach ($statuses as $i => $statusId)
          $orderData[] = ['id' => $statusId, 'order' => $i+1, 'man_status_group_id' => $groupId];
      }
      if(count($orderData) > 0)
      {
        $statusData = $man_status_table->find('all')->where(['id IN' => $manStatusIds]);
        $statusData = $man_status_table->patchEntities($statusData, $orderData);
        $man_status_table->saveMany($statusData);
        return $this->setJsonResponse(['success' => true]);
      }
      else
        throw new \Exception('No data to update');
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }
}