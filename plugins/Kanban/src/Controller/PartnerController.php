<?php
namespace Ka<PERSON>ban\Controller;
use Cake\Log\Log;
use Cake\ORM\TableRegistry;
use App\Lend\KanbanHelper;


use App\Lend\LendInternalAuth;

class PartnerController extends AppController
{
    public function initialize()
    {
        parent::initialize();
        if ($this->request->is('post') && $this->request->getQuery('auth_sig') !== null) {
            $requestData = $this->request->getData();//whitelist signed requests
            if ((new LendInternalAuth)->checkSignature($this->request->getQuery('auth_sig'), $requestData)){
                $this->Auth->allow([
                    'createData', 'deleteData'
                ]);
            }
            else{
                return $this->setJsonResponse(array('success' => false, 'message' => 'Invalid Signature'));
            }
        }
    }

    public function createData() {
        return $this->createDeleteData(true);
    }

    public function deleteData() {
        return $this->createDeleteData(false);
    }

    private function createDeleteData($create){
        try{
            if (!$this->request->is('post')) {
                throw new \Exception("only POST request is available.", 405);
            }
            $requestData = $this->request->getData();
            if(!isset($requestData['partner_id'])){
                throw new \Exception("partner_id must be provided");
            }
            $whereConditions  = [
                'partner_id' => (int) $requestData['partner_id'],
            ];
            if($create){
                $whereConditions['active'] = 1;
            }
            $users = TableRegistry::getTableLocator()->get('PartnerUserEntity')->find('all')->where($whereConditions);
            if($create){
                foreach($users as $user){
                    KanbanHelper::createPositionData($user->partner_user_id);
                }
            }
            else{//delete
                $deleteUsers = [];
                foreach($users as $user){
                    $deleteUsers[] = $user->partner_user_id;
                }
                if(count($deleteUsers) > 0){
                    $kanbanLeadPositionTable = TableRegistry::getTableLocator()->get('KanbanLeadPositionsEntity');
                    $kanbanLeadPositionTable->deleteAll(['partner_user_id IN' => $deleteUsers]);
                }
            }
            return $this->setJsonResponse(['success' => true]);
        }
        catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return $this->setJsonResponse(['success' => false, 'error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
        }
    }
}