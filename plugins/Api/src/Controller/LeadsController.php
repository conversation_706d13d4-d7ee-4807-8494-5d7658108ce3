<?php
namespace Api\Controller;

use Api\Controller\AppController;
use Cake\Event\Event;
use Cake\Core\Configure;
use Cake\Log\Log;
use App\Lend\Config;
use App\Lend\LendInternalAuth;
use App\Lend\LeadValidation;
use App\Lend\FakeDetection;
use App\Service\TaxCalculator;
use Cake\ORM\Table;
use Cake\ORM\TableRegistry;
use \Cake\Datasource\ConnectionManager;
use Cake\Routing\Router;
use Exception;

class LeadsController extends AppController {

    public function initialize(){
      parent::initialize();
      $this->loadModel('FrmPurposeEntity');
    }
    //
    // public function beforeFilter(Event $event) {
    //   parent::beforeFilter($event);
    // }

    public function index() {
      $this->request->allowMethod(['GET']);
      $since = $this->request->getQuery('since');
      if ($since) {
        $since .= ' 00:00:00';
      }

      $partnerId = $this->apiAuth->partner['partner_id'];

      $leads = $this->loadModel('Api.Leads')->getAllLeadsSince($partnerId, $since);
      $aliasRefMap = $this->loadModel('Api.PartnerAliases')->getPartnerAliasByPartnerId($partnerId);

      $leadValidation = new LeadValidation;
      $data = [];
      if (count($leads) > 0) {

        $leadIds = array_column($leads, 'lead_id');
        $leadOwners = $this->loadModel('Api.LeadOwners')->getLeadOwners($leadIds);

        foreach ($leads as $lead) {
          $leadInfo = [];
          $leadInfo['lead'] = $lead;

          if (!empty($leadOwners[$lead['lead_id']]))
            $leadInfo ['owner'] = $leadOwners[$lead['lead_id']];

          // Find alias_ref by partner_alias_id
          $aliasKey = array_search($leadInfo['lead']['partner_alias_id'], array_column($aliasRefMap, 'partner_alias_id'));
          $leadInfo['lead']['alias_ref'] = ($aliasKey !== false) ? $aliasRefMap[$aliasKey]['alias_ref'] : null;

          $currentLeadStatusInfo = $this->getCurrentLeadStatusInfo($leadInfo['lead']['lead_id']);
          if ($currentLeadStatusInfo) {
            $leadInfo['current'] = $currentLeadStatusInfo;
          }

          $leadInfo = $leadValidation->onlyShowTheseFields($leadInfo, $this->whiteListFields());
          if (array_key_exists('call_me_first', $leadInfo['lead'])) {
            $leadInfo['lead']['who_to_contact'] = !empty($leadInfo['lead']['call_me_first']) ? 'Broker' : 'Client';
            unset($leadInfo['lead']['call_me_first']);
          }
          if (getenv('REGION', true) === 'nz') {
            $leadInfo['lead']['nzbn'] = $leadInfo['lead']['abn'];
            unset($leadInfo['lead']['abn']);
          }
          $data[] = $leadInfo;
        }
      }

      $this->apiResponse(true, array('data'=>$data));
    }

    private function whiteListFields() {
      return array(
        'lead' => array(
          'lead_ref', 'partner_external_ref', 'abn', 'business_name', 'monthly_expenses', 'organisation_name', 'amount_requested', 'purpose_id',
          'loan_term_requested', 'loan_term_requested_months', 'trading_since', 'company_registration_date', 'industry_id', 'b_address', 'b_suburb', 'b_state', 'b_postcode', 'sales_monthly',
          'sales_annual', 'overdraft_loc_limit', 'tax_outstanding_arrangement', 'bs_doc_id', 'alias_ref', 'call_me_first', 'force_send', 'product_type_id'
        ),
        'owner' => array(
          'first_name', 'middle_name', 'last_name', 'contact_number', 'mobile', 'phone', 'email', 'home_owner', 'home_owner_detail', 'dob', 'driving_licence_num', 'driving_licence_expiry', 'driving_licence_state', 'address', 'suburb', 'state', 'postcode', 'owner_type', 'equity', 'credit_history'
        ),
        'current' => array(
          'lender', 'lend_status', 'lender_status', 'last_status_update', 'sent_to_lender'
        )
      );
    }

    //http://partners.lend.local/api/leads/asset_finance/lead_ref
    public function assetFinance($id){
      Log::info('=========== Start Adding Asset Finance ==========='.PHP_EOL, ['scope' => ['api']]);

      $this->request->allowMethod(['POST']);
      $data = $this->request->getData();
      $leadValidation = new LeadValidation;
      $header = 'asset_finance_details';
      $clone_data = unserialize(serialize($data));     // deep clone
      list($success,$error) = $leadValidation->validateAssetFinance($clone_data, $header);
      Log::info('** Validation Result: '.json_encode($error).PHP_EOL, ['scope' => ['api']]);

      if(!$success){
        return $this->apiResponse(false, array('errors'=>$error), 422);
      }

      $test = json_encode(unserialize(serialize($this->request->getData())));
      $this->LoadModel('Leads')->saveAssetFinance($data['asset_finance_details'], $id);

      Log::info('** Completed Adding Asset Finance: '.json_encode($data), ['scope' => ['api']]);

      $this->apiResponse(true, array('ref' => $id));
    }
    
    private function getRequiredFields($leadType, $product_type_id, $data) 
    {
      $requiredFields  = [
        'lead' => [
          'amount_requested'
        ],
        'lead_owner' => [
          'first_name', 'last_name', 'email',
        ],
      ];

      if(strtolower($leadType)  == 'consumer'){
        switch($product_type_id) {
          case 27:
            //Home loan Leads
            break;
        }
      } else{
        //Consummer leads
        $requiredFields['lead'] = [
          'amount_requested', 'industry_id', 'organisation_name', 'company_registration_date',  'sales_monthly'
        ];
      }

      // Conditional check contact number
      if (!empty($data['lead_owner']['mobile']))
        $requiredFields['lead_owner'][] = 'mobile';
      elseif (!empty($data['lead_owner']['phone']))
        $requiredFields['lead_owner'][] = 'phone';
      else
        $requiredFields['lead_owner'][] = 'contact_number';
      
      return $requiredFields;
    }

    public function add(){
      try {
        $timestamp = time();
        $currentUrl = $this->request->getRequestTarget();
        Log::info('=========== Start Adding Lead =========== '.$timestamp, ['scope' => ['api']]);
        $this->request->allowMethod(['POST']);
        $internalAuth   = new LendInternalAuth;
        $leadValidation = new LeadValidation;
        $data           = $this->request->getData();

        if(empty($data)){
          return $this->apiResponse(false, ['error' => 'data is empty'], 403);
        }

        $original_payload = $data;
        $partnerId = $this->apiAuth->partner['partner_id'];
        Log::info(json_encode($data).' = '.$timestamp, ['scope' => ['api']]);
        $partner = TableRegistry::getTableLocator()->get('PartnerEntity')->get($partnerId, [
          'contain' => [
            'PartnerFeatureFlagEntity'
          ],
        ]);

        // Phil wanted "contact_number" instead of mobile or phone
        if (getenv('REGION') === 'au') {
          $data['owner']['mobile'] = empty($data['owner']['contact_number']) ? '' : (strlen($data['owner']['contact_number']) == 9 ? '0'.$data['owner']['contact_number'] : $data['owner']['contact_number']);
        } elseif (getenv('REGION') === 'nz') {
          $data['owner']['mobile'] = $data['owner']['contact_number'];
        }
        if (isset($data['owner']['contact_number'])) unset($data['owner']['contact_number']);

        //trading_since populated into company_registration_date where possible
        if (empty($data['lead']['company_registration_date'])) {
          $data['lead']['company_registration_date'] = empty($data['lead']['trading_since']) ? '' : $data['lead']['trading_since'];
        }
        // Add company_registration_date into trading_since until we remove it from DB.
        $data['lead']['trading_since'] = !empty($data['lead']['company_registration_date']) ? $data['lead']['company_registration_date'] : '';

        if (!empty($data['lead']['nzbn'])) {
          $data['lead']['abn'] = $data['lead']['nzbn'];
          unset($data['lead']['nzbn']);
        }

        if (!empty($data['lead']['preferred_lender'])) {
          $preferred_lender = strtolower($data['lead']['preferred_lender']);
          unset($data['lead']['preferred_lender']);
        }

        //From Home loan Endpoint, set default values to the request
        if ($currentUrl == Router::url(['_name' => 'api_leads_homeloan'])) {
          $data['lead']['lead_type'] = 'consumer';
          $data['lead']['product_type_id'] = 27;
          $data['lead']['purpose_id'] = $data['lead']['purpose_id'] == 42 ? 42: 41;
          $data['lead']['force_send'] = 0;
          $data['lead']['send_type'] = 'Manual';
        }
        
        $data   = $this->Leads->formatLeadData($data, [], true);
      
        $isHomeLoanLead = $data['lead']['product_type_id'] === 27;

        $tradingAddress = $data['trading_address'] ?? null;
        unset($data['trading_address']);
        

        if (!empty($data['lead']['referrer_person_ref'])) {
          $data['lead']['referrer_person_id'] = TableRegistry::getTableLocator()->get('ReferrerPeople')->ref2id($data['lead']['referrer_person_ref']);
          unset($data['lead']['referrer_person_ref']);
        }

        $requiredFields = $this->getRequiredFields($data['lead']['lead_type'], $data['lead']['product_type_id'], $data);
        $leadValidation->setRequiredFields($requiredFields);
        // Set Accepted Fields for this specific API:
        $leadValidation->setAcceptedFields(array(
          'lead' => array(
            'abn', 'partner_external_ref', 'campaign', 'business_name', 'organisation_name', 'amount_requested', 'purpose_id', 'purpose_other', 'loan_term_requested', 'loan_term_requested_months',
            'trading_since','years_in_business','company_registration_date', 'industry_id', 'industry_detail', 'b_address', 'b_suburb', 'b_state', 'b_city', 'b_postcode', 'b_country', 'r_address', 'r_suburb', 'r_state', 'r_city', 'r_postcode', 'r_country', 'sales_monthly', 'monthly_expenses',
            'sales_annual', 'overdraft_loc_limit', 'tax_outstanding_arrangement', 'bs_doc_id', 'send_type', 'alias_ref',
            'who_to_contact', 'force_send', 'product_type_id', 'smsf_property_type', 'smsf_property_postcode', 'smsf_property_balance', 'private_lending_purpose_id', 'invoice_amount_outstanding', 'number_of_invoices', 'books_package_is', 'books_package_detail', 'equipment_id', 'equipment_condition', 'equipment_year', 'equipment_details', 'equipment_found', 'equipment_source', 'equipment_source_detail',
            'business_credit_history', 'existing_business', 'tax_outstanding', 'tax_overdue', 'premise_type_id', 'premise_other_detail', 'security_status', 'show_security_other', 'director_status_select', 'consent_status', 'prev_business_name', 'trading_period_select', 
            'invoice_lvr', 'customer_type', 'has_customer_type', 'property_purpose', 
            'bank_statements', 'ato_portal', 'bas_statements', 'prev_asset_finance', 'comp_financial_statements', 'personal_business_tax_returns', 'pl_balance_sheet',
            'lead_type', 'how_soon_id', 'ask_applicant_additional_request', 'referrer_person_id','referer', 'lendsize_estimate',
          ),
          'lead_owner' => array(
            'first_name', 'middle_name', 'last_name', 'contact_number', 'mobile', 'phone', 'email', 'home_owner',
            'home_owner_detail', 'dob', 'driving_licence_num', 'driving_licence_expiry', 'driving_licence_state', 'address', 'suburb', 'state', 'city', 'postcode', 'country',
            'owner_type', 'equity', 'credit_history', 'security_type', 'security_type_detail', 'estimated_value', 'remaining_debt', 'notes',
            'title', 'gender', 'marital_status', 'driving_licence_type', 'current_value', 'lvr', 'security_value', 'credit_score', 'medicare_number', 'medicare_ref', 'medicare_card_colour', 'medicare_expiry', 'passport_number', 'passport_country', 'passport_expiry', 'is_guarantor', 'number_of_dependants', 'dependant_details', 'residency_status', 
            'consent', 'point_of_contact', 
            'first_home_buyer', 'retire_age', 'retire_pay_off_plan', 'retired_pay_off_options'
          ),
          'lead_owner_employments' => array(
            'lead_owner_occupation_id', 'employer', 'employment_type', 'date_from', 'date_to', 'previous_contact_phone',
            'gross_annual_income', 'website', 'profit_current_fy', 'profit_previous_fy', 'addbacks_current_fy', 'addbacks_previous_fy',
            'accountant_company_name', 'accountant_contact_name','accountant_contact_number','trustee', 'trustee_beneficiaries'
          ),
          'lead_owner_incomes' => array(
            'config_income_id', 'amount', 'gross_amount', 'net_amount', 'note', 'net', 'frequency', 'user_input_amount'
          ),
          'lead_owner_addresses' => array(
            'living_status', 'living_status_other', 'landlord_name', 'landlord_contact_number', 'address_type', 'address', 
            'unit_number', 'street_number', 'street_name', 'suburb', 'state', 'city', 'postcode', 'country', 'date_from', 'date_to',
          ),
          'lead_asset_finance' => array(
            'equipment_id', 'asset_description', 'number_of_vehicles', 'contract_type', 'use_glass', 'year', 'make', 'model', 'fuel_type', 'condition', 
            'transmission', 'engine_transmission', 'variant', 'reason_for_purchase', 'reason_description', 'supplier_found', 'supplier', 'supplier_type', 
            'supplier_address', 'supplier_suburb', 'supplier_state', 'supplier_postcode', 'supplier_country', 'supplier_email', 'supplier_contact_number', 
            'delivery_address', 'delivery_suburb', 'delivery_state', 'delivery_postcode', 'delivery_country', 'asset_purchase_price', 'asset_deposit', 
            'asset_tradein', 'asset_tradein_value', 'asset_tradein_debt', 'asset_balloon', 'balloon_reason', 'balloon_reason_explanation', 'ltv', 'brokerage', 
            'origination_fees', 'percent_or_value', 'sale_type', 'green_electric_vehicle', 'odometer', 'nvic', 'options', 'valuation', 'annual_km', 'hours', 
            'personal_service', 'operating_type', 'is_vehicle_found', 'intention_after_loan', 'rego', 'vin', 'engine', 'valuation_type', 'manual_valuation', 
            'outstanding_loan', 'insurance_addon', 'other_addons', 'balloon_percent_or_value', 'balloon_pay_method', 'finance_amount', 'num_cyl', 'rego_state', 
            'colour', 'supplier_contact_name', 'supplier_quote_ref',
          ),
          'lead_home_loan_property' => [
            'property_found', 'property_use', 
            'address', 'unit_number', 'street_number', 'street_name', 'suburb', 'city', 'state', 'postcode', 'country', 
            'estimated_value', 'property_type', 'development_type', 'rent_frequency', 'rent'
          ],
          'lead_home_loan_detail' => [
            'purchase_price',
            'deposit',
            'repayment_type',
            'rate_type',
            'repayment_frequency',
            'refinance_reasons',
            'loan_features',
            'lvr'
          ],
          'lead_notes' => array('notes'),
          'marketing' => ['ga_client_id', 'gclid', 'utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content'],
        ));
        $errors = $leadValidation->validate($data, true); // Check validation

        // Remove lead_owner errors if they are in lead_owner 2024-12-13
        foreach($errors as $k => $error){
          if($error['table'] == "lead_owner"){
            unset($errors[$k]);
            unset($data['lead_owner'][str_replace(["[","]"],["",""], $error['field'])]);
          }
        }
        // Check alias ref
        if (isset($data['lead']['alias_ref'])) {
          $partnerAliasDetail = $this->loadModel('Api.PartnerAliases')->getPartnerAliasByRef($partnerId, $data['lead']['alias_ref']);
          if (!$partnerAliasDetail) {
            $errors[] = ['field' => "alias_ref", "error" => "{$data['alias_ref']} could not be found"];
          } else {
            $data['lead']['partner_alias_id'] = $partnerAliasDetail['partner_alias_id'];
            unset($data['lead']['alias_ref']);
          }
        }

        //check product_type_id
        if (isset($data['lead']['product_type_id'])) {
          $validApiProductTypeIds = $this->loadModel('PartnerProductTypes')->getValidApiProductTypeIds();
          if (!in_array($data['lead']['product_type_id'], $validApiProductTypeIds))
            $errors[] = ['field' => 'lead[product_type_id]', 'error' => "The Product Type ID you provided is not a valid ID"];
        }

        // check purpose_id
        if (!empty($data['lead']['purpose_id']) && !empty($data['lead']['product_type_id'])) {
          $partner_product_types_table = TableRegistry::getTableLocator()->get('PartnerProductTypeEntity');
          $partner_product_type = $partner_product_types_table->get($data['lead']['product_type_id']);
          if (!empty($partner_product_type->sub_product)) {
            $product_type_id = $partner_product_type->sub_product;
          } else {
            $product_type_id = $partner_product_type->product_type_id;
          }
          $purpose_product_table = TableRegistry::getTableLocator()->get('PurposeProductEntity');
          $purpose_products = $purpose_product_table->find('all')->where(['purpose_id' => $data['lead']['purpose_id']])->toArray();
          if (array_search($product_type_id, array_column($purpose_products, 'product_type_id')) === false) {
            Log::info('Invalid purpose and product type so making purpose_id NULL from here. = ' . $timestamp, ['scope' => ['api']]);
            $data['lead']['purpose_id'] = null;
          }
        }

        Log::info('** Validation Result: '.json_encode($errors). ' = ' . $timestamp, ['scope' => ['api']]);

        // Force send the lead even if not 100%?
        // To maintain professionalism with "options/settings" being OUT of the lead object, I've put force_send on the same level as send_email_to_client.
        if (isset($data['force_send'])) {
          $data['lead']['force_send'] = $data['force_send'];
          unset($data['force_send']);
        }

        if (!empty($errors)) {
            foreach ($errors as &$err) {
              if ($err['field'] == "owner[mobile]" || $err['field'] == "owner[phone]") {
                //overwrite the field name with ...
                $err['field'] = 'owner[contact_number]';
              }
            }
            unset($err);
          return $this->apiResponse(false, array('errors'=>$errors), 422);
        }

        // Discussed with Phil at 5:50pm on 15th May 2019, even though statements are not
        // officially uploaded, we'll trust the `bs_doc_id` is valid and we can get it from BankFeeds
        if (!empty($data['lead']['bs_doc_id']))
          $data['lead']['statements_uploaded']  = 1;

        $data['lead']['partner_id']             = $this->apiAuth->partner['partner_id'];
        $data['lead']['created']                = date('Y-m-d H:i:s');
        $data['lead']['status_id']              = 1;
        $data['lead']['partner_status_id']      = 1;
        $data['lead']['source']                 = 'PartnerAPI';
        $data['lead']['lead_type']              = (!empty($data['lead']['lead_type']) && in_array(strtolower($data['lead']['lead_type']), ['consumer', 'commercial'])) ? strtolower($data['lead']['lead_type']) : 'commercial';
        $data['lead_owner']['point_of_contact'] = 1;

        //loan_term_requested is deprecated, now using loan_term_requested_months
        if($data['lead']['loan_term_requested'] && !$data['lead']['loan_term_requested_months']){
          $data['lead']['loan_term_requested_months'] = $data['lead']['loan_term_requested'];
        }

        // Set default term if it's empty:
        if (empty($data['lead']['loan_term_requested_months'])) {
          if (empty($data['lead']['product_type_id'])) {
            $data['lead']['loan_term_requested_months'] = 12;
          } else {
            $partner_product_types_table = TableRegistry::getTableLocator()->get('PartnerProductTypeEntity');
            $partner_product_type = $partner_product_types_table->get($data['lead']['product_type_id']);
            if (!empty($partner_product_type)) {
              $data['lead']['loan_term_requested_months'] = $partner_product_type->default_term;
            } else {
              $data['lead']['loan_term_requested_months'] = 12;
            }
          }
        }

        // Make term to closest year:
        if (!in_array((int)$data['lead']['loan_term_requested_months'], [6, 18]) && (int)$data['lead']['loan_term_requested_months'] % 12 !== 0) {
          $data['lead']['loan_term_requested_months'] = round((int)$data['lead']['loan_term_requested_months'] / 12) * 12;
        }
        
        //if industry_id is a parent category, use Others
        $con = new Config;
        $industries = $con->getConfig('config_industries');
        $grouped    = $con->groupIndustry($industries);
        if(isset($grouped[$data['lead']['industry_id']]) && !empty($grouped[$data['lead']['industry_id']]['children'])){
          foreach ($grouped[$data['lead']['industry_id']]['children'] as $subIndustry) {
            if($subIndustry['child'] == "Other"){
              $data['lead']['industry_id'] = $subIndustry['industry_id'];
            }
          }
        }

        //map field to actual field name in db
        if(isset($data['lead']['who_to_contact'])) {
          $data['lead']['call_me_first'] = strtolower($data['lead']['who_to_contact']) === 'broker' ? '1' : '0';
          unset($data['lead']['who_to_contact']);
        }

        // if status_system is manual or home loan leads, force to make a lead manual send type + call me first ON
        if ($partner->status_system === 'manual' || $isHomeLoanLead) {
          $data['lead']['send_type'] = 'Manual';
          $data['lead']['call_me_first'] = '1';
        } elseif (!isset($data['lead']['send_type'])) {
          $data['lead']['send_type'] = ucwords($partner->send_type);
        }

        Log::info('-- Dupe check START ='. $timestamp, ['scope' => 'api']);
        //dup check
        if (false !== $this->Leads->partnerSameOwnerLead($data, $partnerId, ['lead_owner' => ['phone', 'mobile']])) {
          $days = Configure::read('Lend.partner_honoured_lookup.days');
          throw new Exception('You have already submitted a lead within the last '.$days.' days with these same details.', 400);
        }
        Log::info('-- Dupe check PASSED ='. $timestamp, ['scope' => 'api']);

        if (@$data['lead']['lead_type'] === 'consumer') {
          $data['lead']['send_type'] = 'Manual';
          if (empty($data['lead']['how_soon_id'])) {
            $data['lead']['how_soon_id'] = 4;
          }
        }

        if (!empty($data['lead']['sales_monthly'])) {
          $data['lead']['client_declared_sales_monthly'] = $data['lead']['sales_monthly'];
        }

        // Set Default Values for Home loan if not set
        if($isHomeLoanLead){
          // Property details
          $data['lead_home_loan_property'] = $this->formatHomeLoanProperty($data['lead_home_loan_property']);

          // Loan Details
          $purposeEntity =  $this->FrmPurposeEntity->getPurposeByID($data['lead']['purpose_id']);
          $homeLoanDetail = $data['lead_home_loan_detail'];
          $homeLoanDetail['loan_term'] = $data['lead']['loan_term_requested_months'];
          $homeLoanDetail['loan_purpose'] = $purposeEntity->purpose;
          $homeLoanDetail['deposit'] = $homeLoanDetail['deposit'] ?? 0;
          $homeLoanDetail['purchase_price'] = $homeLoanDetail['purchase_price'] ?? $data['lead_home_loan_property']['estimated_value'] ?? 0;

          $homeLoanDetail['loan_amount'] = $data['lead']['amount_requested'] + $homeLoanDetail['deposit'] ?? 0;

          $homeLoanDetail['loan_amount_unsure'] = !($homeLoanDetail['loan_amount']);
          $homeLoanDetail['putting_deposit'] = $homeLoanDetail['deposit'] ?? 0 ? true : false;
          $homeLoanDetail['repayment_type'] = $homeLoanDetail['repayment_type'] ?: 'Principle & Interest';
          $homeLoanDetail['rate_type'] = $homeLoanDetail['rate_type'] ?: 'Variable';
          $homeLoanDetail['repayment_frequency'] = $homeLoanDetail['repayment_frequency'] ?: 'Monthly';
          if (empty($homeLoanDetail['lvr']) && !empty($data['lead_home_loan_property']['estimated_value'])) {
            $homeLoanDetail['lvr'] = $data['lead']['amount_requested'] / $data['lead_home_loan_property']['estimated_value'];
          }

          // prepare the data `refinance_reasons` from an ID array & save the many to many relations to db later
          $homeLoanDetail['refinance_reasons'] = ['_ids' => !empty($homeLoanDetail['refinance_reasons']) ? $homeLoanDetail['refinance_reasons'] : []];
          $homeLoanDetail['loan_features'] = ['_ids' => !empty($homeLoanDetail['loan_features']) ? $homeLoanDetail['loan_features'] : []];

          $data['lead_home_loan_detail'] = $homeLoanDetail;
        }

      // Reset Address Data
      if (!empty($data['lead_owner_addresses'])) {
        $has_mailing_address = false;
        $has_settlement_post_address = false;
        $finalAddresses = [];
        foreach ($data['lead_owner_addresses'] as $address) {
          $address = $this->formatAddressData($address);
          $address['is_google_maps_valid'] = 0;
          $address['is_manually_added'] = 1;

          // only keep one mailing address
          if ($address['address_type'] == 'mailing') {
            if ($has_mailing_address) {
              continue;
            } else {
              $has_mailing_address = true;
            }
          }
          // only keep one settlement_post address
          if ($address['address_type'] == 'settlement_post') {
            if ($has_settlement_post_address) {
              continue;
            } else {
              $has_settlement_post_address = true;
            }
          }
          $finalAddresses[] = $address;
        }
        $data['lead_owner_addresses'] = $finalAddresses;
      }

        // Reset Owner Data
        $data['lead_owner']['first_home_buyer'] = ($data['lead_owner']['first_home_buyer'] ?? 0) ? 1 : 0;
        $data['lead_owner']['retired_pay_off_options'] = ['_ids' => !empty($data['lead_owner']['retired_pay_off_options']) ? $data['lead_owner']['retired_pay_off_options'] : []];
        
        // Reset Employment data
        if (!empty($data['lead_owner_employments'])) {
          $data['lead_owner_employments'] = $this->formatEmploymentHistories($data['lead_owner_employments']);
        }
        $owner = $data['lead_owner'];

        $completeData = array_merge($data['lead'], [
            'owners_all' => [
                array_merge(
                    [
                        'point_of_contact' => 1,
                        'status' => 'active',
                    ],
                    $owner,
                    [
                        'all_employments' => $data['lead_owner_employments'] ?? [],
                        'all_addresses' => $data['lead_owner_addresses'] ?? [],
                        'incomes' => $data['lead_owner_incomes'] ?? [],
                    ]
                )
            ]
        ]);
        
        // Add the lead and owner:
        Log::info('-- Creating lead START ='. $timestamp, ['scope' => 'api']);
        $leadId =  $this->Leads->addLead($completeData);
        $lead = TableRegistry::getTableLocator()->get('LeadEntity')->get($leadId);

        $data['lead_owner']['lead_id'] = $leadId;
        Log::info('-- Created lead FINISH ='. $timestamp, ['scope' => 'api']);
        if(empty($leadId)){
          return $this->apiResponse(false, ['error' => 'Failed to create lead'], 500);
        }
        //Add lead_address (trading)
        if ($tradingAddress)
          $this->Leads->addTradingAddress($leadId, $tradingAddress);
        if (!empty($preferred_lender)) {
          $this->_AddPreferredLender($preferred_lender, $leadId, $original_payload);
        }
        
        if (!empty($data['lead_asset_finance'])) {
          Log::info('-- Adding asset finance START ='. $timestamp, ['scope' => 'api']);
          $data['lead_asset_finance']['lead_id'] = $leadId;
          $lead_asset_finance_table = TableRegistry::getTableLocator()->get('LeadAssetFinanceEntity');
          $lead_asset_finance = $lead_asset_finance_table->find('all')->where(['lead_id' => $leadId])->first();
          if ($lead_asset_finance) {
            $lead_asset_finance_table->patchEntity($lead_asset_finance, $data['lead_asset_finance']);
          } else {
            $lead_asset_finance = $lead_asset_finance_table->newEntity($data['lead_asset_finance']);
          }
          $lead_asset_finance_table->save($lead_asset_finance);
          Log::info('-- Adding asset finance FINISH ='. $timestamp, ['scope' => 'api']);
        }

        // update lead_notes
        if (!empty($data['lead_notes'])) {
          // get admin user id
          $partner_user_table = TableRegistry::getTableLocator()->get('PartnerUserEntity');
          $partner_user = $partner_user_table->find('all')->where(['partner_id' => $data['lead']['partner_id'], 'account_admin' => true])->first();
          foreach ($data['lead_notes'] as &$note) {
            $note['lead_id'] = $leadId;
            $note['partner_user_id'] = @$partner_user->partner_user_id;
          }
          $lead_notes_table = TableRegistry::getTableLocator()->get('LeadNotesEntity');
          $lead_notes = $lead_notes_table->newEntities($data['lead_notes']);
          $lead_notes_table->saveMany($lead_notes);
        }

        //create marketing data if present
        if (!empty($data['marketing'])) {
          $data['marketing']['lead_id'] = $leadId;
          $lead_marketing_table = TableRegistry::getTableLocator()->get('LeadMarketingEntity');
          $marketing = $lead_marketing_table->newEntity($data['marketing']);
          $lead_marketing_table->save($marketing);
        }

        // Create property profile if present
        if (!empty($data['lead_home_loan_property']) && $isHomeLoanLead){
          $data['lead_home_loan_property']['lead_id'] = $leadId;
          $lead_home_loan_property_table = TableRegistry::getTableLocator()->get('LeadHomeLoanProperty');
          $lead_home_loan_property = $lead_home_loan_property_table->newEntity($data['lead_home_loan_property']);
          $lead_home_loan_property_table->save($lead_home_loan_property);
        }

        // Create Home Loan Detail
        if (!empty($data['lead_home_loan_detail']) && $isHomeLoanLead) {
          $data['lead_home_loan_detail']['lead_id'] = $leadId;
          $lead_home_loan_detail_table = TableRegistry::getTableLocator()->get('LeadHomeLoanDetail');
          $lead_home_loan_detail = $lead_home_loan_detail_table->newEntity($data['lead_home_loan_detail']);
          $lead_home_loan_detail_table->save($lead_home_loan_detail);
        }

        $percentComplete = $this->Leads->checkPercentage($data, (!empty($data['lead']['bs_doc_id'])));

        $this->loadModel('PartnerLeadHistory')->addPartnerLeadHistory(array('partner_id'=>$data['lead']['partner_id'], 'lead_id'=>$leadId, 'history_detail'=>'Lead Added'));

        // Add partner user leads
        if(!empty($data['broker_assigned'])){
          Log::info('-- Broker assigned START ='. $timestamp, ['scope' => 'api']);
          if($partner_user = $this->loadModel('PartnerUsers')->getPartnerUser(['partner_user_ref'=>$data['broker_assigned']])){
            $partnerUserLeadsTable = TableRegistry::getTableLocator()->get('PartnerUserLeadsEntity');
            $partnerUserLead = $partnerUserLeadsTable->newEntity([
              'partner_user_id' => $partner_user['partner_user_id'],
              'status' => 'ACCESS',
              'lead_id' => $leadId
            ]);
            $partnerUserLeadsTable->save($partnerUserLead);
          }
          Log::info('-- Broker assigned FINISH ='. $timestamp, ['scope' => 'api']);
        }

        if (!empty($data['lead']['bs_doc_id'])){
          // Add background job to retrieve it:
          $this->LoadModel('BackgroundJobs')->addBackgroundJob(array(
                                  'lead_id'       => $leadId,
                                  'job_type'      => 'brokerFlowDocIdRetrieval',
                                  'ref_id'        => $data['lead']['bs_doc_id'],
                                  'class_name'    => 'Bankstatements',
                                  'function_name' => 'brokerFlowDocIdRetrieval',
                                  'created'       => date('Y-m-d H:i:s', time()),
                                ));
          // $this->Leads->saveLeadBrokerFlowId($data['lead']['lead_id'], $data['lead']['bs_doc_id']);
        }

        Log::info('** Completed Adding Lead: = '. $timestamp . json_encode($data).PHP_EOL.'** Percent Complete: '.json_encode($percentComplete).PHP_EOL, ['scope' => ['api']]);

        $emailSent = false;
        if (!empty($data['send_email_to_client']) AND $data['send_email_to_client']) {
          // Eventually we'll also restrict this to just Brokers (xxxx AND $this->apiAuth->thepartner['partner_type']!=='Web Affiliate')
          if ($percentComplete['current_percentage']<100) {
            // Send email to the applicant asking for more details / bs
            $pages = [];
            $adhocData = [];
            $additionalCode = [];
            
            if (@$data['lead']['lead_type'] == "consumer") {
              $code =  'AskApplicantLeadForm';
              $pages = [
                '/non-signature-credit-guide'=> 'Terms',
                '/property-details' => 'Property Details',
                '/loan-details' => 'Loan Details',
                '/asset-details' => 'Asset Details',
                '/applicants' => 'Applicant Details',
                '/income-expenses' => 'Applicant Income & Expenses',
                '/assets-liabilities' => 'Applicant Assets & Liabilities',
                '/consent' => 'Consent',
              ];
            } else {
              $code = 'CommercialAskApplicantLeadForm';
              if (!empty($data['lead_asset_finance'])) {
                $pages = [
                  '/business-loan-details' => 'Loan Details',
                  '/business-details' => 'Business Details',
                  '/applicants' => 'Applicant Details',
                  '/asset-details' => 'Asset Details',
                  '/business-assets-liabilities' => 'Business Assets & Liabilities',
                  '/income-expenses' => 'Applicant Income & Expenses',
                  '/assets-liabilities' => 'Applicant Assets & Liabilities',
                  '/consent' => 'Consent',
                ];
              } else {
                $pages = [
                  '/business-loan-details' => 'Loan Details',
                  '/business-details' => 'Business Details',
                  '/applicants' => 'Applicant Details',
                ];
              }
            }

            if ($partner->feature_flag->access_bank_statements) {
              $pages['/bank-statements'] = 'Bank Statements';
            }
            $lead_owner = TableRegistry::getTableLocator()->get('LeadOwnersEntity')->find()
              ->where(['lead_id' => $leadId, 'status' => 'active', 'point_of_contact' => 1])
              ->first();
            $additionalCode['pages'] = implode('|', array_keys($pages));
            $additionalCode['ownerRef'] = $lead_owner->owner_ref;

            $adhocData['to'] = $lead_owner->email;
            $adhocData['additionalCode'] = http_build_query($additionalCode);
            $adhocData['ask_applicant_sections'] = $pages;
            $adhocData['ask_applicant_sections_text'] = 'Please complete the following sections of the application form:';

            $emailSent = (bool)$this->LoadModel('PartnerNotifications')->sendPartnerNotifications($data['lead']['partner_id'], $code, $leadId, $adhocData);

            $sent_to_client = getenv('DOMAIN_ASK_APPLICANT', true) . '/';
            $sent_to_client .= TableRegistry::getTableLocator()->get('Leads')->createAutoLoginCode($leadId, $adhocData['additionalCode']);
            $sent_to_client .= '&code=' . $code;
          }
        }
        Log::info('** Email Sent: = ' . $timestamp . ($emailSent?'Sent':'Not Sent').PHP_EOL, ['scope' => ['api']]);

        //detect fake words
        // $fakeClass = new FakeDetection;
        // if (strtolower($this->environment) === 'live' && $fakeClass->detectFakeWords($data)) {
        //   $potentialFakeStatusRecord = $this->LoadModel('LendStatuses')->getLendStatusByName('Potential Fake');
        //   if (!empty($potentialFakeStatusRecord['lend_status_id'])) {
        //     $this->Leads->updatePartnerStatusId($leadId, $potentialFakeStatusRecord['lend_status_id']);
        //   }

        //   $this->Leads->markAsFake($leadId, '1'); // Mark as fake, Staff can change via email if it wasn't fake.

        //   // Send notification to partner
        //   $this->LoadModel('PartnerNotifications')->sendPartnerNotifications($data['lead']['partner_id'], 'PotFakeLead', $leadId);
        // }

        $internalAuth->leadData = $data['lead'];

        // pass to checkExistsLead() to update partner_status
        $this->Leads->checkExistsLead($data, $leadId);
        $partnerStatus = $this->Leads->getPartnerStatus($leadId);

        // Update lead owner incomes
        if (!empty($data["lead_owner_incomes"])) {
          $leadOwners = $this->getTableLocator()->get('LeadOwnersEntity')->find()
          ->where(['lead_id' => $leadId, 'status' => 'active'])
          ->contain([
            'LeadOwnerIncomeEntity',
            'LeadOwnerIncomeEntity.ConIncomeShareEntity',
          ])
          ->all();
          
          $leadOwnersArray = $leadOwners->toArray();

          $taxCalculator = new TaxCalculator();
          $ownersTaxResult = $taxCalculator->calculateForOwners($leadOwnersArray);

          foreach ($leadOwnersArray as $owner) {
            $owner['monthly_gross_income'] = $ownersTaxResult[$owner['owner_ref']]['monthly_gross_income'];
            $owner['monthly_net_income'] = $ownersTaxResult[$owner['owner_ref']]['monthly_net_income'];
            $table = TableRegistry::getTableLocator()->get('LeadOwnersEntity');
            $table->save($owner);
          }
        }

        $poc = TableRegistry::getTableLocator()->get('LeadOwnersEntity')->find()
          ->where(['lead_id' => $leadId, 'point_of_contact' => 1])  
          ->first();

        $res = [
          'ref' => $lead->lead_ref,
          'lead_completion'      => $percentComplete['current_percentage'].'%',
          'email_sent_to_client' => $emailSent,
          'owner' =>[
            'ownerRef' => $poc->owner_ref ?? '',
            'firstName' => $poc->first_name ?? '',
            'lastName' => $poc->last_name ?? '',
          ],
          
          'status'               => $partnerStatus,
        ];
        if (!empty($sent_to_client)) {
          $res['sent_to_client'] = $sent_to_client;
        }

        $this->apiResponse(true, $res);
      } catch (\Exception $e) {
        Log::error($e->getMessage());
        Log::error($e->getTraceAsString());
        return $this->apiResponse(false, ['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
      }
    }

    /**
     * Format home loan property details
     *
     * @param array $property
     * @return array $property
     */
    protected function formatHomeLoanProperty($property)
    {
      $property['property_found'] = $property['property_found'] ?: 'Yes';

      $property['property_type'] = $property['property_type'] ?: 'Detached House';

      // Initial data based on the proerty type
      if ($property['property_type'] != 'Vacant Land') {
        $property['development_type'] = $property['development_type'] ?: 'Established';
        $property['property_use'] = $property['property_use'] ?: 'Live In';
        if ($property['Investment'] == 'Live In') {
          $property['rent_frequency'] = $property['rent_frequency'] ?: 'Weekly';
        } else {
          $property['rent_frequency'] = null;
        }
      } else {
        $property['development_type'] = null;
        $property['property_use'] = null;
        $property['rent_frequency'] = null;
      }

      $property = $this->formatAddressData($property, true);
      return $property;
    }

    /**
     * Format address data. If address is not provided, but having structured address information (street_name, street_number, unit_number, suburb, city, state, country, postcode), generate address and full address.
     *
     * @param array $item
     * @param bool $fullAddressNeeded
     * @return array
     */
    protected function formatAddressData($item, $fullAddressNeeded = false){
      // Generate Address if address is not provide but having structrued address information
      if (empty($item['address']) && ($item['street_name'])) {
        $item['address'] = $item['unit_number'] ? $item['unit_number'] . ' / ' : '';
        $item['address'] .=  $item['street_number'] ? $item['street_number'] . ' ' : '';
        $item['address'] .=  $item['street_name'] ? $item['street_name'] : '';
        $item['address'] = trim($item['address']);
      }

      // Generate Full address
      if (!empty($item['address']) && $fullAddressNeeded) {
        $item['full_address'] .=  $item['address'];
        $item['full_address'] .=  $item['suburb'] ? ', ' . $item['suburb'] : '';
        $item['full_address'] .=  $item['city'] ? ', ' . $item['city'] : '';
        $item['full_address'] .=  $item['state'] ? ', ' . $item['state'] : '';
        $item['full_address'] .=  $item['country'] ? ', ' . $item['country'] : '';
        $item['full_address'] .=  $item['postcode'] ? ' ' . $item['postcode'] : '';
      }
      return $item;
    }

    public function view($id){
      ConnectionManager::alias('reader_db', 'default');
      Log::info('=========== Start Viewing Lead '.$id.' ==========='.PHP_EOL, ['scope' => ['api']]);
      $this->request->allowMethod(['GET']);
      $partnerId = $this->apiAuth->partner['partner_id'];

      $leadid        = (new LendInternalAuth)->unhashLeadId($id);
      $data['lead']  = $this->Leads->getLead(array('lead_id'=>$leadid));
      unset($data['lead']['loan_term_requested_months']);
      // Return an error if needed
      $this->apiAuth->canIAccessThisLead($data['lead']);

      $data['owner'] = $this->Leads->lead_owner->getLeadOwner(array('lead_id'=>$leadid, 'point_of_contact'=>1));

      // Find alias_ref by partner_alias_id
      $aliasRefMap = $this->loadModel('Api.PartnerAliases')->getPartnerAliasByPartnerId($partnerId);
      $aliasKey = array_search($data['lead']['partner_alias_id'], array_column($aliasRefMap, 'partner_alias_id'));
      $data['lead']['alias_ref'] = ($aliasKey !== false) ? $aliasRefMap[$aliasKey]['alias_ref'] : null;

      $currentLeadStatusInfo = $this->getCurrentLeadStatusInfo($data['lead']['lead_id']);
      if ($currentLeadStatusInfo) {
        $data['current'] = $currentLeadStatusInfo;
      }

      // Prepare to remove some fields for the API response:
      $data = (new LeadValidation)->onlyShowTheseFields($data, $this->whiteListFields());
      if (array_key_exists('call_me_first', $data['lead'])) {
        $data['lead']['who_to_contact'] = !empty($data['lead']['call_me_first']) ? 'Broker' : 'Client';
        unset($data['lead']['call_me_first']);
      }
      // Details for Asset Finance
      $lead_asset_finance = $this->loadModel('LeadAssetFinance')->getLeadAssetFinanceForAPI($leadid);
      if(!empty($lead_asset_finance)){
          $data['asset_finance_data']['people'] = $this->Leads->lead_owner->getOwnersMoreDetails($leadid);
          $data['asset_finance_data']['the_purchase'] = $lead_asset_finance;
          $data['asset_finance_data']['business_assets'] = $this->loadModel('LeadAssets')->getLeadAssetsForAPI($leadid, 'business');
          $data['asset_finance_data']['business_liabilities'] = $this->loadModel('LeadLiabilities')->getLeadLiabilitiesForAPI($leadid, 'business');
          $data['asset_finance_data']['references'] = $this->loadModel('LeadReferences')->getLeadReferencesForAPI($leadid);
      }
      if (getenv('REGION', true) === 'nz') {
        $data['lead']['nzbn'] = $data['lead']['abn'];
        unset($data['lead']['abn']);
      }
      Log::info('** Success Viewing Lead: '.json_encode($data).PHP_EOL, ['scope' => ['api']]);

      $data['lead']['lead_ref'] = $id; // Show the ref they use
      ConnectionManager::dropAlias('default');
      $this->apiResponse(true, array('data'=>$data));
    }

    public function edit($id){
      Log::info('=========== Start Editing Lead '.$id.' ==========='.PHP_EOL, ['scope' => ['api']]);
      $this->request->allowMethod(['PATCH','PUT']);
      $this->apiResponse(true, array('ref'=>$id));
    }

    public function delete($id){
      Log::info('=========== Start Deleting Lead '.$id.' ==========='.PHP_EOL, ['scope' => ['api']]);
      $this->request->allowMethod(['DELETE']);
      $this->apiResponse(false, array('error' => 'Unused method'), 400);
    }

    protected function getCurrentLeadStatusInfo($leadId) {
      $currentLeadStatusInfo = $this->loadModel('Api.LenderLeadUpdates')->getCurrentLeadStatusInfo($leadId);
      if ($currentLeadStatusInfo) {
        // 'Manually Added Funded' is not actual lender status, so set them to 'Funded'
        if (!strcasecmp($currentLeadStatusInfo['lender_status'], 'Manually Added Funded')) {
          $currentLeadStatusInfo['lender_status'] = 'Funded';
          $currentLeadStatusInfo['lend_status'] = 'Funded';
        }
      }

      return $currentLeadStatusInfo;
    }

    protected function formatEmploymentHistories($employments) {
      if (empty($employments)) {
        return null;
      }
      $occupation_table = TableRegistry::getTableLocator()->get('Occupation');
      foreach ($employments as &$e) {
        if (!empty($e['lead_owner_occupation_id'])) {
          $occupation = $occupation_table->get($e['lead_owner_occupation_id']);
          $e['previous_occupation'] = $occupation->occupations_name;
        }
        $e['employment_status'] = empty($e['date_to']) ? 'current' : 'previous';
      }
      return $employments;
    }

    /*
    private function _generateDefaultFee($partner_id, $lead_id)
    {
      $user_default_fees = TableRegistry::getTableLocator()->get('ConPartnerUserFeeDefaultEntity')->find('all')
            ->contain(['PartnerUserEntity'])
            ->where([
              'ConPartnerUserFeeDefaultEntity.partner_id' => $partner_id,
              'ConPartnerUserFeeDefaultEntity.active' => true,
            ])
            ->toArray();
      if (empty($user_default_fees)) {
        return false;
      }
      // convert the result to array
      $user_default_fees = json_decode(json_encode($user_default_fees), true);

      // find best prior user's default fees:
      $partner_user_ids = array_unique(array_column($user_default_fees, 'partner_user_id'));
      if (count($partner_user_ids) > 1) {
        $account_admin = [];
        $access_all_leads = [];
        $others = [];
        foreach ($user_default_fees as $fee) {
          if (!empty($fee['partner_user']['account_admin'])) {
            $account_admin[$fee['partner_user']['partner_user_id']][] = $fee;
          } elseif (!empty($fee['partner_user']['access_all_leads'])) {
            $access_all_leads[$fee['partner_user']['partner_user_id']][] = $fee;
          } else {
            $others[$fee['partner_user']['partner_user_id']][] = $fee;
          }
        }

        if (!empty($account_admin)) {
          $user_default_fees = array_shift($account_admin);
        } elseif (!empty($access_all_leads)) {
          $user_default_fees = array_shift($access_all_leads);
        } else {
          $user_default_fees = array_shift($others);
        }
      }

      $con_credit_prop_fees = [];
      foreach ($user_default_fees as $item) {
        $con_credit_prop_fees[] = [
          'lead_id' => $lead_id,
          'config_con_fee_id' => $item['config_con_fee_id'],
          'partner_user_id' => $item['partner_user_id'],
          'explanation' => $item['explanation'],
          'fee_name' => $item['fee_name'],
          'paid_by' => $item['paid_by'],
          'paid_to' => $item['paid_to'],
          'fixed_amount' => $item['fixed_amount'],
          'fixed_amount_max' => $item['fixed_amount_max'],
          'percent_of_loan' => $item['percent_of_loan'],
          'percent_of_loan_max' => $item['percent_of_loan_max'],
        ];
      }
      
      // create records using $user_default_fees
      $con_credit_prop_fee_table = TableRegistry::getTableLocator()->get('ConCreditPropFeeEntity');
      $con_credit_prop_fees = $con_credit_prop_fee_table->newEntities($con_credit_prop_fees);
      $con_credit_prop_fee_table->saveMany($con_credit_prop_fees);
    }
    */

    private function _AddPreferredLender($preferred_lender, $lead_id, $original_payload)
    {
      $lender_table = TableRegistry::getTableLocator()->get('LenderEntity');
      $lender = $lender_table->find('all')->where(['shorthand' => $preferred_lender, 'country' => getenv('REGION', true)])->first();

      if (!empty($lender)) {
        $lead_preferred_lender_table = TableRegistry::getTableLocator()->get('LeadPreferredLenderEntity');
        $lead_preferred_lender = $lead_preferred_lender_table->newEntity([
          'lead_id' => $lead_id,
          'lender_id' => $lender->lender_id,
          'shorthand' => $lender->shorthand,
          'order' => 1,
        ]);
        $lead_preferred_lender_table->save($lead_preferred_lender);
      } else {
        $msg = "*Partners API - `" . ($this->apiAuth->partner['company_name'] . ' ' . $this->apiAuth->partner['partner_id']) . "`* can't find a lender from `preferred_lender` field. ```" . json_encode($original_payload, JSON_PRETTY_PRINT) . "```";
        TableRegistry::getTableLocator()->get('App')->postToSlack($msg, "lend_errors");
      }
    }
}