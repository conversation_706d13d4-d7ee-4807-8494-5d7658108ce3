<?php

namespace Hooks\Controller;

use <PERSON><PERSON>\Controller\AppController;
use Cake\Core\Configure;
use Cake\Log\Log;
use Cake\ORM\TableRegistry;

class CustomPrivacyUpdatesController extends AppController
{
    public function initialize()
    {
        parent::initialize();
        $this->Auth->allow(['index', 'envelope']);
    }

    /**
     * callback functions
     */
    public function index($addAdminJob = 0)
    {
        $this->loadModel('AdminJobs');
        $this->loadModel('PartnerCustomPrivacyForms');
        $data = $this->request->getData();
        $privacy_form = $this->PartnerCustomPrivacyForms->getForm(['service_template_id' => $data['template_id']]);

        if ($privacy_form['status'] !== 'Deleted' && $addAdminJob == 1) {
            $this->PartnerCustomPrivacyForms->updateForm($privacy_form['partner_custom_privacy_form_id'], ['status' => 'Staff Review']);
            $this->AdminJobs->addAdminJob(['job_type' => 'review_docusign', 'job_status' => 'pending', 'reason' => 'Review a docusign template <b>' . $privacy_form['file_name'] . '</b> for broker ' . $privacy_form['partner_id'], 'created' => date('Y-m-d H:i:s'), 'meta' => json_encode(['partner_custom_privacy_form_id' => $privacy_form['partner_custom_privacy_form_id'], 'service_template_id' => $privacy_form['service_template_id']])]);
        }

        return $this->setJsonResponse(['success' => true]);
    }

    public function envelope()
    {
        $this->loadModel('PartnerRequestedPrivacyForms');
        $data = $this->request->getData();

        Log::write('debug', "signatire new log === > ".json_encode($data));

        if(isset($data['meta_data']['raw_registration_data'])){
            $metaData = json_decode($data['meta_data']['raw_registration_data']);
            if($data['status'] === 'All Signed'){
                $this->loadModel('App')->postToSlack(":heavy_exclamation_mark: *We currently have a partner who has completed onboarding.*\nPlease verify this broker (with attached docusign accreditation)...\n*Name is*: " . $metaData->partner_user->name . ", \n*Email is*: " . $metaData->partner_user->email . ", \n*Phone is*: " . $metaData->partner_user->mobile . ", \n*Partner Id*: " . $metaData->partner_user->partner_id . ".",
                    (getenv('REGION', true) === 'au' ? 'partner_verification' : 'partner_verification_nz'));
                $this->loadModel('PartnerUsers')->updatePartnerUser((int)$metaData->partner_user->partner_user_id, ['is_accredited'=> 'Passed', 'signed_accreditation_S3' => $data['signedDocument']]);
            }else{
                $this->loadModel('PartnerUsers')->updatePartnerUser((int)$metaData->partner_user->partner_user_id, ['is_accredited'=> 'Failed']);
            }
            return $this->setJsonResponse(['success' => true, 'data' => ['envelopeId' => $data['envelope_id']]]);
        }else{
            if ($data['status'] === "All Signed" || $data['status'] === "Declined") {
                
                $result = $this->PartnerRequestedPrivacyForms->updateRequestedPrivacyForm($data['envelope_id'], ['status' => $data['status'], 'signed_document_s3' => $data['signedDocument']]);
                $lead_id =  (new \App\Lend\LendInternalAuth)->unhashLeadId(trim($data['meta_data']['lead_ref']));

                $notificatioanInfo = $this->PartnerRequestedPrivacyForms->getPartnerRequestedPrivacyFormsByLeadID($lead_id); 

                if($data['status'] === "All Signed"){
                    if(!$this->_makeOwnersConsent($data)){
                        $message = "Something went wrong from Docusign envelope endpoint: ```".json_encode($data)."```";
                        $this->PartnerRequestedPrivacyForms->postToSlack($message, 'lend_errors');
                    }
                    $this->LoadModel('PartnerNotifications')->sendPartnerNotifications($notificatioanInfo['partner_id'],'DocusignaCompNotify',$lead_id);
                    
                    $pathParts = explode('/', $data['signedDocument']);
                    $fileName = $pathParts[sizeof($pathParts) - 1];
                    unset($pathParts[sizeof($pathParts) - 1]);
                    $partnerLeadUploadsTable = TableRegistry::getTableLocator()->get('PartnerLeadUploadsEntity');
                    $entityData = [
                        'lead_id' => $lead_id,
                        'owner_id' => null,
                        'uploaded_by' => 'Partner User',
                        'name' => $fileName,
                        'full_path' => implode('/', $pathParts),
                        'file_type' => 'application/pdf',
                        'file_size' => 200000,
                        'status' => 'Active',
                        'include_for_lenders' => 0,
                        'is_compliance' => 1,
                        'partner_lead_uploads_meta' => [
                            [
                                'field_name' => 'specified',
                                'value' => 'Privacy Form (Electronic Signature)',
                            ],
                            [
                                'field_name' => 'acceptable',
                                'value' => 'true',
                            ],
                        ],
                    ];
                    $entity = $partnerLeadUploadsTable->newEntity($entityData, [
                        'associated' => ['PartnerLeadUploadsMetaEntity'],
                    ]);
                    $partnerLeadUploadsTable->save($entity);
                } 
                
                return $this->setJsonResponse(['success' => true, 'data' => ['envelopeId' => $data['envelope_id']]]);
            }
        }

    }

    private function _makeOwnersConsent($data){
      if(empty($data['meta_data']['owner_ids'])) return false;
      $owner_ids = json_decode($data['meta_data']['owner_ids'], true);
      if(empty($owner_ids)) return false;

      $this->loadModel('LeadOwners');
      foreach($owner_ids as $owner_id){
        $this->LeadOwners->updateLeadOwner(['owner_id'=>$owner_id, 'consent'=>date('Y-m-d H:i:s')]);
      }
      return true;
    }


}
