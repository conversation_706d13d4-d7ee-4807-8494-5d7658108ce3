<?php
namespace LeadApis\Controller;

use App\Enums\IDDocumentType;
use App\Model\Entity\LeadGlassSecurity;
use App\Model\Table\LeadGlassSecurityTable;
use Cake\Datasource\ConnectionManager;
use Cake\Datasource\EntityInterface;
use Cake\Http\Exception\MethodNotAllowedException;
use Cake\Http\Exception\NotFoundException;
use Cake\ORM\TableRegistry;
use Cake\Core\Configure;
use Cake\Log\Log;
use App\Lend\Config;
use App\Lend\LendInternalAuth;
use App\Lend\LeadValidation;
use App\Lend\MakeDecisionV2;
use App\Lend\LendSize;
use App\Lend\SignatureService;
use App\Lend\ProductPricing;
use App\Lend\EquifaxService;
use App\Lend\CreditorWatchService;
use App\Lend\CurlHelper;
use App\Lend\LendHEMServiceClient;
use \Aws\S3\S3Client;
use Cake\Utility\Security;
use Cake\Chronos\Chronos;
use Cake\Http\Exception\HttpException;
use Exception;
use Hashids\Hashids;
use LeadApisV2\Helper\SurplusHelper;
use Cake\Http\Client;
use LeadApisV2\Controller\ApplicationPdfController;
use App\Traits\S3Trait;
use Cake\Cache\Cache;
use Cake\ORM\Table;

class LeadApisController extends AppController
{
  use S3Trait;

    public function initialize(){
    parent::initialize();
    $allow = ['pdfGenerator', 'pdfGeneratorConsumer', 'privacyConsent', 'getRctiPayload', 'clone', 'findEntityType', 'configs'];
    $this->Auth->allow($allow);
    $this->loadComponent('AvatarAPI');
    $this->loadComponent('AddressValidator');
  }


  /**
   * This connects to the service below to fetch quotes
   * https://gitlab.com/lend_com_au/quotes/-/blob/master/README.md
   * The endpoint expects a trailing slash e.g. http://localhost:3003/
   * */
    public function quotes() {
    $url = getenv('QUOTES_LOOKUP') . str_ireplace('/lead-apis/quotes/', '', $this->request->here());
    $result = $this->curl($url, $this->request->getData(), $this->request->getMethod(), ['x-api-key:' . getenv('QUOTES_API_KEY')]);
    return $this->setJsonResponse($result);
  }

    public function getS3Zip($fileName) {
    try {
      if (!$this->s3Client) $this->prepareS3(); // Initiate connection if not already
      $cmd = $this->s3Client->getCommand('GetObject', [
        'Bucket' => Configure::read('Lend.AWS.bucket'),
        'Key' =>  'zipped/' . $fileName
      ]);

      // Create a special link that temporarily grants access for 2 minutes
      $request = $this->s3Client->createPresignedRequest($cmd, '+' . '20' . ' minutes');

      return $this->setJsonResponse(['success' => true, 'url' => (string)$request->getUri()]);
    } catch (\Exception $e) {
      return $this->setJsonResponse(['error' => $e->getMessage()]);
    }
  }

    public function privacyConsent(){
    $this->request->allowMethod(['GET']);

    $lead_ref = $this->request->query['lead_ref'];
    $lead_id = (new LendInternalAuth)->unhashLeadId($lead_ref);
    $lead = new \stdClass();

      $getParams = $this->request->getQueryParams();
      unset($getParams['signature']);
      if (!($this->validatePDFSignature($getParams, $this->request->query['signature']))) {
        return $this->setJsonResponse(['error' => 'Invalid signature']);
      }


      try {
      // fetch lead_owners data
      $lead_owners = $this->loadModel('LeadOwners')->getLeadOwners(array('lead_id' => $lead_id, 'status' => 'active'));
      $lead_owners_normal = [];
      $lead_owners_guarantor = [];
      foreach ($lead_owners as $lead_owner) {
        if ($lead_owner['is_guarantor'] == 1)
          array_push($lead_owners_guarantor, $lead_owner);
        else
          array_push($lead_owners_normal, $lead_owner);
      }
      $lead->lead_owners_normal = $lead_owners_normal;
      $lead->lead_owners_guarantor = $lead_owners_guarantor;

    } catch (\Exception $e) {
      return $this->setJsonResponse(['error' => $e->getMessage()]);
    }

    return $this->setJsonResponse($lead);

  }

  public static function getProposedFinanceData($lead_id) {
    $lenderMatchRequestsTable = TableRegistry::getTableLocator()->get('LenderMatchRequestEntity');
    $matchRequest = $lenderMatchRequestsTable
      ->find("all")
      ->where([
        'lead_id' => $lead_id,
      ])
      ->order(['created' => 'DESC'])
      ->first();

    if (empty($matchRequest)) {
      return [];
    }

    $http = new Client();
    $header = [
      'type' => 'json',
      'headers' => ['x-api-key' => getenv('MATCHING_ENGINE_KEY')]
    ];
    $url = getenv('DOMAIN_MATCHING_ENGINE') . "/get-match-results-selected/" . $matchRequest->match_ref;
    $response = $http->get($url, [], $header);
    $status = $response->getStatusCode();
    $match_result = $response->getJson();
    if ($status != '200') {
      throw new \Exception($match_result['error']['message']);
    }
    if (empty($match_result['data']['matches'])) {
      return [];
    }
    $result = ['proposed_finance' => []];
    foreach ($match_result['data']['matches'] as $selectedMatch) {
      $establishmentFee = 0;
      foreach ($selectedMatch['lender_match_result_fees'] as $fee) {
        if ($fee['category'] == 'Establishment') {
          $establishmentFee += $fee['fee'];
        }
      }
      $data = [
        [
          'name' => 'Lender Name',
          'value' => $selectedMatch['lender_name'],
          'name_tag_extra' => 'style="width: 50%;background-color: #C6C9E8;"',
          'value_tag_extra' => 'style="background-color: #C6C9E8;"',
        ],
        [
          'name' => 'Tier',
          'value' => $selectedMatch['lender_tiers']['tier_name'],
        ],
        [
          'name' => 'Product',
          'value' => $selectedMatch['lender_product']['results_name'],
        ],
        [
          'name' => 'Financed Amount',
          'value' => $selectedMatch['financed_amount'],
        ],
        [
          'name' => 'Origination Fee',
          'value' => $establishmentFee,
        ],
        [
          'name' => 'APR',
          'value' => $selectedMatch['apr'],
        ],
        [
          'name' => 'Interest Rate',
          'value' => $selectedMatch['customer_rate'],
        ],
        [
          'name' => 'Repayment Frequency',
          'value' => $selectedMatch['repayment_freq'],
        ],
        [
          'name' => 'Repayment',
          'value' => $selectedMatch['repayment_amt'],
        ],
      ];
      $result['proposed_finance'][] = $data;
    }

    return $result;
  }
    public function _preparePdf( $lead_ref){

      $lead_id = (new LendInternalAuth)->unhashLeadId($lead_ref);
      $defaults = [
        'financeType' => '',
        'product_type' => '',
        'selected_proposal' => '',
        'lead_asset_finance' => '',
        'lead_owner' => '',
        'asset_types' => '',
        'lead_assets' => '',
        'lead_liabilities' => '',
        'liabilities' => '',
        'lead_owner_type' => '',
        'abnlookup' => '',
        'entity_trust' => '',
        'lead_addresses' => [],
        'lead_references' => '',
        'lead' => [
          'lead_call_me_first' => [
            'Who to Contact' => '',
          ],
        ],
      ];
      try {
        // fetch leads data
        $config_model = new Config;

        $lead = $this->_getLead($lead_id, true);

        if (!empty($lead['lead']['partner_alias_id'])) {
          $referrer = $this->loadModel('PartnerAliases')->getPartnerAlias(['partner_alias_id' => $lead['lead']['partner_alias_id']])['company_name'];
        } elseif (!empty($lead['lead']['partner_id'])) {
          $referrer = $this->loadModel('Partners')->getPartner(['partner_id' => $lead['lead']['partner_id']])['company_name'];
        } else {
          $referrer = 'Lend.com.au';
        }
        $lead['lead']['referrer'] = $referrer;

        $lead_call_me_first = $this->_getLenderAboutLeadCallMeFirst($lead, $lead['lead']['partner_id'], $lead_id);

        $lead['lead']['lead_call_me_first'] = $lead_call_me_first;
  
        $lead['lead']['current_industry'] = $config_model->getConfig('config_industries', array('industry_id' => $lead['lead']['industry_id']))[0];
  
        $lead['asset_types'] = [
          'business' => $config_model->putOtherToEndOfTheList($config_model->getConfig('config_asset_types', ['asset_type' => 'business', 'status' => 1]), 'asset_type_name'),
          'personal' => $config_model->putOtherToEndOfTheList($config_model->getConfig('config_asset_types', ['asset_type' => 'personal', 'status' => 1]), 'asset_type_name'),
        ];
        $lead['liabilities'] = [
          'business' => $config_model->putOtherToEndOfTheList($config_model->getConfig('config_liabilities', ['liability_type' => 'business', 'status' => 1]), 'liability_name'),
          'personal' => $config_model->putOtherToEndOfTheList($config_model->getConfig('config_liabilities', ['liability_type' => 'personal', 'status' => 1]), 'liability_name'),
        ];

        $lead_data = $this->loadModel('LeadEntity')->getApplicationData($lead_ref);
        $lead['lead'] += $lead_data;
  
        $asset_product_ids = [10, 20, 21, 22, 23];
        $lead['is_asset'] = in_array($lead['product_type']['product_type_id'], $asset_product_ids);
        $lead['lead_owner_type'] = Configure::read('Lend.lead_owner_type');
        $lead['lead_owner_type']['1'] = str_replace('&amp;', '&', $lead['lead_owner_type']['1']);
        $lead['logo'] = $lead['lead']['partner']['logo'];

      } catch (\Exception $e) {
        Log::error($e->getMessage());
        Log::error($e->getTraceAsString());
        return $defaults;
      }
      $proposedFinanceData = self::getProposedFinanceData($lead_id);
      return $lead + $proposedFinanceData + $defaults;
      
    }

  public function pdfGenerator(){

    $this->request->allowMethod(['GET']);

    if (empty($this->request->query['signature']) || empty($this->request->query['lead_ref']) )
      return $this->setJsonResponse(['error' => 'Missing params']);

    $lead_ref = $this->request->query['lead_ref'];
    // validate signature
    if (!($this->validatePDFSignature(['lead_ref' => $lead_ref], $this->request->query['signature'])))
      return $this->setJsonResponse(['error' => 'Invalid signature']);

    $payload = $this->_preparePdf($lead_ref);
    return $this->setJsonResponse($payload);
  }

  protected function _convertMonthsToYearsString($months) {
    $fullYears = intval($months / 12);
    $remainingMonths = $months % 12;
    if ($remainingMonths !== 0) {
      $yearString = "{$fullYears} years {$remainingMonths} months";
      return $yearString;
    }
    return "{$fullYears} years";
  }

  public function pdfGeneratorConsumer() {

    try {
      $this->request->allowMethod(['GET']);
      if (empty($this->request->query['type']) || empty($this->request->query['signature'])) {
        return $this->setJsonResponse(['error' => 'Missing params']);
      }
      $getParams = $this->request->getQueryParams();
      unset($getParams['signature']);
      unset($getParams['type']);
      if (!($this->validatePDFSignature($getParams, $this->request->query['signature']))) {
        return $this->setJsonResponse(['error' => 'Invalid signature']);
      }

      if($this->request->query['partner_user_ref']){
        $partnerUser = $this->loadModel('PartnerUsers')->getPartnerUser(['partner_user_ref'=>$this->request->query['partner_user_ref']]);
        $brokerName = $partnerUser['name'];
      }

      if ($this->request->query['type'] === 'credit-guide') {
        if (empty($this->request->query['partner_user_ref'])) {
          return $this->setJsonResponse(['error' => 'Missing Partner User ID']);
        }
        $partnerUserRef = $this->request->query['partner_user_ref'];
        $partnerUserId = LendInternalAuth::unhashPartnerUserId($partnerUserRef);
        $conPartnerUserSettings = TableRegistry::getTableLocator()
          ->get('ConPartnerUserSettingsEntity')
          ->find()
          ->where(['partner_user_id' => $partnerUserId])
          ->first();

        $conPartnerUserFeeDefaults = TableRegistry::getTableLocator()
          ->get('ConPartnerUserFeeDefaultEntity')
          ->find()
          ->where(['partner_user_id' => $partnerUserId, 'active' => 1])
          ->contain(['ConfigConFeeEntity'])
          ->all();

        $leadId = LendInternalAuth::unhashLeadId($this->request->query['lead_ref']);

        $lead = TableRegistry::getTableLocator()->get('LeadEntity')->get($leadId, [
          'contain' => [
            'PartnerEntity',
            'PocOwner',
          ]
        ]);

        $partner = $lead['partner'];
        $owner = $lead['owner_poc'];

        $brokerFeesCount = 0;
        $thirdPartyFeesCount = 0;
        $brokerFees = [];
        $thirdPartyFees = [];
        foreach ($conPartnerUserFeeDefaults as $feeDefault) {
          if ($feeDefault['config_con_fee']['group'] === 'broker' && $brokerFeesCount < 3) {
            $brokerFees[$brokerFeesCount]['fee_name'] = $feeDefault['display_fee_name'];
            $brokerFees[$brokerFeesCount]['explanation'] = $feeDefault['explanation'];
            $brokerFees[$brokerFeesCount]['amount'] = $feeDefault['display_amount_string'];
            $brokerFeesCount++;
          }
          if ($feeDefault['config_con_fee']['group'] === 'third party' && $thirdPartyFeesCount < 3) {
            $thirdPartyFees[$thirdPartyFeesCount]['fee_name'] = $feeDefault['display_fee_name'];
            $thirdPartyFees[$thirdPartyFeesCount]['explanation'] = $feeDefault['explanation'];
            $thirdPartyFees[$thirdPartyFeesCount]['amount'] = $feeDefault['display_amount_string'];
            $thirdPartyFeesCount++;
          }
        }

        if (empty($conPartnerUserSettings)) {
          $conPartnerUserSettings = [
            'entity_name' => '',
            'address' => '',
            'email' => '',
            'phone' => '',
            'acl' => '',
            'acr' => '',
            'cg_upfront_comms_range' => '',
            'cg_trail_comms_range' => '',
            'cg_vb_comms_range' => '',
            'cg_complain_phone' => '',
            'cg_complain_email' => '',
            'cg_complain_address' => '',
            'cd_afca_member_num' => '',
            'cg_top_lenders' => [],
            'is_bid' => false,
          ];
        }
  
        $topLenderNames = array_column($conPartnerUserSettings['cg_top_lenders'], 'name');
        $topLenderNames = array_filter($topLenderNames, function($name) {
          return !empty($name);
        });
        $conPartnerUserSettings['cg_top_lenders_string'] = implode(', ', $topLenderNames);

        $creditGuideData = [
          'date' => date('jS F o'),
          'broker_fees' => $brokerFees,
          'third_party_fees' => $thirdPartyFees,
          'con_partner_user_settings' => $conPartnerUserSettings,
          'business_name' => $partner->company_name,
          'abn_acn' => $partner->abn,
          'business_address' => "{$partner->address} {$partner->suburb} {$partner->state} {$partner->postcode}",
          'contact_number' => $partner->mobile ?? "",
          'applicant_name' => $owner['full_name'],
          'partner_user_id' => $partnerUserId,
          'broker_name' => $brokerName,
        ];

        return $this->setJsonResponse($creditGuideData);
      } else if ($this->request->query['type'] === 'preliminary-assessment') {
        $leadRef = $this->request->query['lead_ref'];
        $leadId = (new LendInternalAuth)->unhashLeadId($leadRef);
        $lead = TableRegistry::getTableLocator()->get('LeadEntity')->get($leadId, [
          'contain' => [
            'ConReqAndObjEntity',
            'ConRequirementEntity',
            'ConRequirementEntity.ConfigRequirementEntity',
            'ConPrelimEntity',
            'PartnerEntity',
            'PocOwner',
            'PocOwner.ConPrelimOwnerEntity',
            'PocOwner.CurrentAddress',
            'Owners',
            'Owners.CurrentAddress',
            'Owners.OwnerAllEmployments',
            'Owners.ConPrelimOwnerEntity',
            'Owners.LeadOwnerExpenseEntity',
            'Owners.LeadOwnerExpenseEntity.ConfigExpenseEntity',
            'Owners.LeadOwnerIncomeEntity',
            'Owners.LeadOwnerIncomeEntity.ConfigIncomeEntity',
            'Owners.LeadAssetsEntity',
            'Owners.LeadAssetsEntity.ConfigAssetTypeEntity',
            'Owners.LeadLiabilitiesEntity',
            'Owners.LeadLiabilitiesEntity.ConfigLiabilityEntity',
            'SelectedLenderMatch',
            'SelectedLenderMatch.LenderProductEntity',
            'SelectedLenderMatch.LenderProductEntity.LenderEntity',
            'LeadAssetsEntity',
            'LeadAssetsEntity.ConAssetShareEntity',
            'LeadAssetsEntity.ConfigAssetTypeEntity',
            'LeadLiabilitiesEntity',
            'LeadLiabilitiesEntity.ConLiabilityShareEntity',
            'LeadLiabilitiesEntity.ConfigLiabilityEntity',
            'LeadReferenceEntity',
            'AllAddresses',
            'LeadAbnLookupEntity',
            'EntityTrustEntity',
            'FrmPurposeEntity',
            'LeadOwnerIncomeEntity',
            'LeadOwnerIncomeEntity.ConfigIncomeEntity',
            'LeadOwnerIncomeEntity.LeadOwnersEntity',
            'LeadOwnerIncomeEntity.ConIncomeShareEntity',
            'LeadOwnerExpenseEntity',
            'LeadOwnerExpenseEntity.ConfigExpenseEntity',
            'LenderMatchRequestEntity',
          ]
        ]);

        // new matching engine
        $selected_new_match = null;
        if (!empty($lead->lender_match_requests)) {
          // $lead->lender_match_requests[0]->match_ref
          $http = new Client();
          $header = [
              'type' => 'json',
              'headers' => ['x-api-key' => getenv('MATCHING_ENGINE_KEY')]
          ];
          $response = $http->get(getenv('DOMAIN_MATCHING_ENGINE') . "/get-match-results-selected/" . $lead->lender_match_requests[0]->match_ref, [], $header);
          $status = $response->getStatusCode();
          $match_result = $response->getJson();
          if ($status != '200') {
              throw new Exception($match_result['error']['message']);
          }

          $selected_new_match = $match_result['data']['matches'][0];
        }
        $ownerAddresses = [];
        $applicantEmploymentLength = [];
        $incomeTotals = [];
        $expensesTotals = [];
        $assetsTotalValue = 0;
        $liabilitiesTotal = [];
        $liabilities = [];
        $monthlySurplus = [];
        $monthlyHem = [];
        $monthlySurplusAdjusted = [];

        $monthlyIncomeTotalGross = 0;
        foreach ($lead['owners_all'] as $owner) {
            $monthlyIncomeTotalGross += $owner['monthly_gross_income'];
        }

        $ownerPocPostcode = NULL;
        $ownerPoc = $lead['owner_poc'];
        if (!empty($ownerPoc['current_address'])) {
          $ownerPocPostcode = $ownerPoc['current_address']['postcode'];
        }
        $ownerPocPreliminaryOwnerStatement = '';
        if (!empty($ownerPoc['con_preliminary_owner'][0]) && !empty($ownerPoc['con_preliminary_owner'][0]['statement'])) {
          $ownerPocPreliminaryOwnerStatement = $ownerPoc['con_preliminary_owner'][0]['statement'];
        }

        $repayment_amount = 0;
        if (!empty($selected_new_match)) {
          $repayment_amount = $selected_new_match['repayment_with_brokerage']; // CONSUMER_v2
        } else {
          $repayment_amount = $lead['selected_lender_match']['repayment_amt']; // CONSUMER_v1
        }



        $dob = [];
        foreach ($lead['owners_all'] as $index => $owner) {
          if (!empty($owner['dob'])) {
            $owner['dob']->setToStringFormat('d/MM/Y');
            $dob[] = $owner['dob']->__toString();
          }
          else {
            $dob[] = '';
          }
          if (!empty($owner['current_address'])) {
            $a = $owner['current_address'];
            $ownerAddresses[] = implode(', ', [$a['address'], $a['suburb'], $a['state'], $a['postcode']]);
          }
          else {
            $ownerAddresses[] = '';
          }
          $applicantEmploymentLength[$index] = 'No employment data';
          if (!empty($owner['employment'])) {
            $from = $owner['employment']['date_from'];
            $to = $owner['employment']['date_to'] ?? Chronos::now();
            $yearsInt = $from->diffInYears($to);
            $monthsInt = $from->diffInMonths($to) % 12;
            $applicantEmploymentLength[$index] = "{$yearsInt} years";
            if ($monthsInt !== 0) {
              $applicantEmploymentLength[$index] .= " {$monthsInt} months";
            }
          }

          $incomeTotals[$index] = 0;
          foreach ($owner['incomes'] as $income) {
            $incomeTotals[$index] += $income['amount'];
          }
          $expensesTotals[$index] = 0;
          foreach ($owner['expenses'] as $expense) {
            $expensesTotals[$index] += $expense['amount'];
          }

          $liabilitiesTotal[$index] = [
            'limit' => 0,
            'loan_balance' => 0,
            'repayment_pm' => 0,
          ];
          $liabilities[$index] = [];
          if (!empty($owner['liabilities'])) {
            foreach ($owner['liabilities'] as $liability) {
              $liabilities[$index][] = [
                'limit' => $liability['limit'] ?? 0,
                'repayment_pm' => $liability['repayment_pm'] ?? 0,
                'loan_balance' => $liability['loan_balance'] ?? 0,
              ];
              $liabilitiesTotal[$index]['loan_balance'] += $liability['shared_loan_balance'];
              $liabilitiesTotal[$index]['limit'] += $liability['limit'];
              $liabilitiesTotal[$index]['repayment_pm'] += $liability['shared_repayment_pm'];
            }
          }
        }

        $incomes = [];
        $incomeTotal = 0;
        $incomeNetTotal = 0;
        $primaryApplicantName = $lead['owner_poc']['first_name'];
        foreach ($lead['incomes'] as $incomeRaw) {
          $income = $incomeRaw->toArray();
          $income['label'] = "{$primaryApplicantName}'s " . $incomeRaw['config_income']['income_type'];
          if ($income['config_income']['id'] === Configure::read('Lend.SALARY_INCOME_CONFIG_ID')) {
            $income['label'] = '<b>' . $incomeRaw['owner']['first_name'] . '\'s</b> ' . $incomeRaw['config_income']['income_type'];
          }
          if ($income['config_income']['id'] === Configure::read('Lend.SPOUSE_INCOME_CONFIG_ID')) {
            $income['label'] = '<b>' . $incomeRaw['owner']['first_name'] . '\'s</b> ' . $incomeRaw['config_income']['income_type'];
          }

          if ($incomeRaw['net']) {
            $incomeNetTotal += $income['shared_amount'];
          }
          else {
            $incomeTotal += $income['shared_amount'];
          }

          $incomes[] = $income;
        }

        $expenses = [];
        $expensesTotal = 0;
        foreach ($lead['expenses'] as $expenseRaw) {
          $expense = $expenseRaw->toArray();
          $expenseType = $expenseRaw['config_expense']['expenses_type'];
          $expense['amount_calculated'] = $expense['amount'] ?? 0;
          if ($expense['shared'] === true) {
            $expense['amount_calculated'] = $expense['shared_amount'];
          }
          if (empty($expenses[$expenseType])) {
            $expenses[$expenseType] = $expense;
          }
          else {
            $expenses[$expenseType]['amount_calculated'] += $expense['amount_calculated'];
          }
          $expensesTotal += $expense['amount_calculated'];
        }

        $ownerNamesById = [];
        foreach ($lead['owners_all'] as $owner) {
          $ownerNamesById[$owner['owner_id']] = $owner['full_name'];
        }

        $assets = [];
        $pocOwnerName = $lead['owner_poc']['full_name'];
        foreach ($lead['assets'] as $assetRaw) {
          $asset = $assetRaw->toArray();
          $totalApplicantsShare = 100;
          $assetOwnershipArray = [$pocOwnerName . ' - 100%'];
          if (!empty($asset['shared'])) {
            $totalApplicantsShare = 0;
            $assetOwnershipArray = [];
            foreach ($asset['shared'] as $share) {
              $assetOwnershipArray[] = $ownerNamesById[$share['owner_id']] . ' - ' . $share['percent'] . '%';
              $totalApplicantsShare += $share['percent'];
            }
            if ($totalApplicantsShare < 100) {
              $assetOwnershipArray[] = 'Non-Applicants - ' . (100 - $totalApplicantsShare) . '%';
            }
          }
          $asset['ownership'] = implode('<br>', $assetOwnershipArray);
          $assets[] = $asset;
          $assetsTotalValue += $assetRaw['value']*$totalApplicantsShare/100;
        }

        $liabilities = [];
        $liabilitiesTotalLimit = 0;
        $liabilitiesTotalMonthly = 0;
        $liabilitiesTotalOwing = 0;
        foreach ($lead['liabilities'] as $liabilityRaw) {
          $totalApplicantsShare = 100;
          $ownershipArray = [$pocOwnerName . ' - 100%'];
          if (!empty($liabilityRaw['shared'])) {
            $totalApplicantsShare = 0;
            $ownershipArray = [];
            foreach ($liabilityRaw['shared'] as $share) {
              $ownershipArray[] = $ownerNamesById[$share['owner_id']] . ' - ' . $share['percent'] . '%';
              $totalApplicantsShare += $share['percent'];
            }
            if ($totalApplicantsShare < 100) {
              $ownershipArray[] = 'Non-Applicants - ' . (100 - $totalApplicantsShare) . '%';
            }
          }

          $liabilities[] = [
            'limit' => $liabilityRaw['limit'] ?? 0,
            'repayment_pm' => $liabilityRaw['repayment_pm'] ?? 0,
            'loan_balance' => $liabilityRaw['loan_balance'] ?? 0,
            'liability_name' => $liabilityRaw['liability']['liability_name'],
            'ownership' => implode('<br>', $ownershipArray),
          ];
          $liabilitiesTotalLimit += $liabilityRaw['limit'];
          $liabilitiesTotalMonthly += $liabilityRaw['repayment_pm']*$totalApplicantsShare/100;
          $liabilitiesTotalOwing += $liabilityRaw['loan_balance'];
        }

        if (!empty($selected_new_match)) {
          $loanTermOverride = $selected_new_match['term_months'];
        } else {
          $loanTermOverride = $lead['selected_lender_match']['term_months'];
        }
        if (!empty($lead['con_preliminary'])) {
          $lead['latest_con_preliminary'] = $lead['con_preliminary'][0];
        }
        if (!empty($lead['latest_con_preliminary']['loan_term_manual']) && $lead['latest_con_preliminary']['loan_term_manual'] > 0) {
          $loanTermOverride = $lead['latest_con_preliminary']['loan_term_manual'];
        }
        $loanTermHuman = $this->_convertMonthsToYearsString($loanTermOverride);

        if (!empty($lead['purpose'])) {
          $purpose = $lead['purpose']['purpose'];
        }
        else {
          $purpose = $lead['purpose_other'];
        }

        $verificationChecklist = TableRegistry::getTableLocator()
          ->get('PartnerLeadUploadsEntity')
          ->getPreliminaryAssessmentVerificationChecklist($leadId);

        $leadOwnersWithDefaults = [];
        $ownerDefaults = [
          'dob' => '',
          'email' => '',
          'mobile' => '',
          'residency_status' => '',
          'marital_status' => '',
          'number_of_dependants' => '',
        ];
        foreach ($lead['owners_all'] as $owner) {
          $ownerWithDefaults = array_intersect_key($owner->toArray(), $ownerDefaults);
          if (empty($owner['employment'])) {
            $ownerWithDefaults['employment'] = [
              'employment_type' => '',
            ];
          }
          else {
            $ownerWithDefaults['employment'] = array_intersect_key($owner['employment']->toArray(), [
              'employment_type' => '',
            ]);
          }
          $conPrelimOwnerDefaults = [
            'statement' => '',
            'judgement_info' => '',
            'bad_credit_info' => '',
            'pep_info' => '',
            'other_info' => '',
            'forsee_inc_desc' => '',
            'forsee_exp_desc' => '',
            'forsee_additional_desc' => '',
            'forsee_other_desc' => ''
          ];
          $owner['con_preliminary_owner'] = $owner['latest_con_preliminary_owner'];
          if (empty($owner['con_preliminary_owner'])) {
            $ownerWithDefaults['con_preliminary_owner'] = $conPrelimOwnerDefaults;
          }
          else {
            $ownerWithDefaults['con_preliminary_owner']
              = array_intersect_key($owner['con_preliminary_owner']->toArray(), $conPrelimOwnerDefaults);
          }
          $leadOwnersWithDefaults[] = $ownerWithDefaults;
        }

        $leadArray = $lead->toArray();
        $leadArray['con_preliminary'] = $leadArray['con_preliminary'][0];
        $conPreliminaryDefaults = [
          'created' => '',
            "description" =>  '',
            "support_desc" =>  '',
            "support_amt" =>  0,
            "support_name" =>  '',
            "support_relation" =>  '',
            "support_acknowledged" =>  '',
            "support_verify" =>  '',
            "support_other" =>  '',
            "assumptions" =>  '',
            "no_conflict" => '',
            "fin_hardship" => '',
            "user_name" => '',
            "signature" => '',
            "signed_date_text" => '',
            "prelim_owners" => null,
            "finance_amount_manual" => 0,
            "loan_term_manual" => 0,
            "interest_rate_manual" => 0,
            "monthly_repayments_manual" => 0,
        ];

        if (empty($leadArray['con_preliminary'])) {
          $leadArray['con_preliminary'] = $conPreliminaryDefaults;
        } else {
          $leadArray['con_preliminary'] = array_intersect_key($leadArray['con_preliminary'], $conPreliminaryDefaults);
          
          // Convert signed_date_text from UTC to Australia/Sydney timezone
          if (!empty($leadArray['con_preliminary']['signed_date_text'])) {
            // Only convert if it's in UTC format (contains 'T' and ends with 'Z')
            if (strpos($leadArray['con_preliminary']['signed_date_text'], 'T') !== false && 
                (substr($leadArray['con_preliminary']['signed_date_text'], -1) === 'Z' || 
                 strpos($leadArray['con_preliminary']['signed_date_text'], '+') !== false)) {
              $utcDate = new \DateTime($leadArray['con_preliminary']['signed_date_text']);
              $utcDate->setTimezone(new \DateTimeZone('Australia/Sydney'));
              $leadArray['con_preliminary']['signed_date_text'] = $utcDate->format('Y-m-d');
            }
          }
        }

        $selectedLenderMatchDefaults = [
          'lender_prod_name' => '',
          'loan_amt' => '',
          'financed_amount' => '',
          'base_rate' => '',
          'adjusted_rate' => '',
          'repayment_amt' => '',
          'lender_product' => [
            'product_name' => '',
          ],
        ];
        if (!empty($selected_new_match)) {
          $leadArray['selected_lender_match'] = array_intersect_key($selected_new_match, $selectedLenderMatchDefaults);
        } elseif (empty($leadArray['selected_lender_match'])) {
          $leadArray['selected_lender_match'] = $selectedLenderMatchDefaults;
        } else {
          $leadArray['selected_lender_match'] = array_intersect_key($leadArray['selected_lender_match'], $selectedLenderMatchDefaults);
        }

        $conRequirementsAndObjectives = [
          'loan_by_date' => '',
          'budgeted_repayments' => '',
          'description' => '',
          'other' => '',
        ];
        if (empty($leadArray['con_req_and_obj'])) {
          $leadArray['con_req_and_obj'] = $conRequirementsAndObjectives;
        } else {
          if (!empty($leadArray['con_req_and_obj'][0]['loan_by_date'])) {
            $leadArray['con_req_and_obj'][0]['loan_by_date']->setToStringFormat('d/MM/Y');
            $leadArray['con_req_and_obj'][0]['loan_by_date'] = $leadArray['con_req_and_obj'][0]['loan_by_date']->__toString();
          }

          $leadArray['con_req_and_obj'][0] = array_intersect_key($leadArray['con_req_and_obj'][0], $conRequirementsAndObjectives);
        }

        $partnerUserRef = $this->request->query['partner_user_ref'];
        $partnerUserId = LendInternalAuth::unhashPartnerUserId($partnerUserRef);

        $conPartnerUserSettings = TableRegistry::getTableLocator()
          ->get('ConPartnerUserSettingsEntity')
          ->find()
          ->where(['partner_user_id' => $partnerUserId])
          ->first();
        if (!empty($selected_new_match)) {
          $amountRequestedWithOverride = $selected_new_match['financed_amount'];
        } else {
          $amountRequestedWithOverride = $leadArray['selected_lender_match']['financed_amount'];
        }

        if (
          !empty($leadArray['con_preliminary']['finance_amount_manual'])
          && $leadArray['con_preliminary']['finance_amount_manual'] > 0
        ) {
          $amountRequestedWithOverride = $leadArray['con_preliminary']['finance_amount_manual'];
        }


        $isPurposePersonalLoan = !empty($lead['purpose']) && $lead['purpose']['is_personal_loan'];
        $interestRateManual    = $leadArray['con_preliminary']['interest_rate_manual'];
        $loanRateOverride = null;

        if (isset($interestRateManual) && $interestRateManual !== null) {
            $loanRateOverride = $interestRateManual;
        } else {
            if ($isPurposePersonalLoan) {
                $loanRateOverride = $selected_new_match['customer_rate'] ?? null;
            } else {
                $loanRateOverride = $selected_new_match['adjusted_rate'] ?? null;
            }
        }

        if (!empty($selected_new_match)) {
          $loanRepaymentOverride = $selected_new_match['repayment_with_brokerage'];
        } else {
          $loanRepaymentOverride = $leadArray['selected_lender_match']['repayment_amt'];
        }

        if (
          !empty($leadArray['con_preliminary']['monthly_repayments_manual'])
          && $leadArray['con_preliminary']['monthly_repayments_manual'] > 0
        ) {
          $loanRepaymentOverride = $leadArray['con_preliminary']['monthly_repayments_manual'];
        }

        $product_title = null;
        $lender_name = null;
        if (!empty($selected_new_match)) {
          $product_title = $selected_new_match['lender_prod_name'] ?? $selected_new_match['lender_product']['product_name'];
          $lender_name = $selected_new_match['lender_name'] ?? $selected_new_match['lenders']['lender_name'];
        } else {
          $product_title = $lead['selected_lender_match']['lender_prod_name'];
          $lender_name = $lead['selected_lender_match']['lender_name'];
        }

        if(isset($leadArray['con_req_and_obj'][0])){
          $leadArray['con_req_and_obj'] = $leadArray['con_req_and_obj'][0];
        }


        $yearlyIncomeGross = $monthlyIncomeTotalGross * 12;

        $monthlySurplus = (SurplusHelper::getMonthlySurplus($lead)) - $loanRepaymentOverride;
        $weeklyHem = $this->getHem($leadRef, $yearlyIncomeGross, $ownerPoc['marital_status'], $ownerPoc['number_of_dependants'], $ownerPocPostcode);
        $monthlyHem = $weeklyHem * 52 / 12;
        $monthlySurplusAdjusted = (SurplusHelper::getMonthlySurplusAdjusted($lead, $monthlyHem)) - $loanRepaymentOverride;

        $preliminaryAssessmentData = [
          'lead' => $leadArray,
          'incomes' => $incomes,
          'expenses' => array_values($expenses),
          'assets' => $assets,
          'liabilities' => $liabilities,
          'amount_requested' => $amountRequestedWithOverride,
          'product_title' => $product_title,
          'lender' => $lender_name,
          'applicants' => $leadOwnersWithDefaults,
          'con_preliminary_owner_statement' => $ownerPocPreliminaryOwnerStatement,
          'loan_term' => $loanTermHuman,
          'loan_rate' => $loanRateOverride,
          'loan_repayment' => $loanRepaymentOverride,
          'partner' => $lead['partner'],
          'applicant_addresses' => $ownerAddresses,
          'applicant_employment_length' => $applicantEmploymentLength,
          'applicant_income_totals' => $incomeTotals,
          'income_total' => $incomeTotal,
          'income_total_net' => $incomeNetTotal,
          'expenses_total' => $expensesTotal,
          'applicant_expenses_totals' => $expensesTotals,
          'assets_total_value' => $assetsTotalValue,
          'liabilities_total_limit' => $liabilitiesTotalLimit,
          'liabilities_total_monthly' => $liabilitiesTotalMonthly,
          'liabilities_total_owing' => $liabilitiesTotalOwing,
          'purpose' => $purpose,
          'monthly_surplus' => $monthlySurplus,
          'monthly_hem' => $monthlyHem,
          'monthly_surplus_adjusted' => $monthlySurplusAdjusted,
          'verification_checklist' => $verificationChecklist,
          'dob' => $dob,
          'other_req_obj' => $leadArray['con_req_and_obj']['other'],
          'con_req_and_obj_description' => $leadArray['con_req_and_obj']['description'],
          'con_partner_user_settings' => $conPartnerUserSettings,
          'broker_name' => $brokerName,
        ];

        return $this->setJsonResponse($preliminaryAssessmentData);
      } else if ($this->request->query['type'] === 'credit-proposal') {
        $ownerRefsString = $this->request->query['owner_ref'];
        $ownerRefs = explode(',', $ownerRefsString);
        $ownerId = LendInternalAuth::unhashOwnerId($ownerRefs[0]);
        $owner = TableRegistry::getTableLocator()->get('LeadOwnersEntity')->get($ownerId, ['contain' => 'CurrentAddress']);
        if (empty($owner)) {
          throw new \Exception('Owner not found');
        }
        $leadId = $owner['lead_id'];
        $lead = TableRegistry::getTableLocator()->get('LeadEntity')->get($leadId, [
          'contain' => [
            'LeadAssetFinanceEntity',
            'PartnerEntity',
            'SelectedLenderMatch',
            'SelectedLenderMatch.LenderProductEntity',
            'SelectedLenderMatch.LenderProductEntity.LenderEntity',
            'ConCreditPropFeeEntity',
            'ConCreditPropFeeEntity.ConfigConFeeEntity',
            'ConPrelimEntity',
            'LeadPricingEntity',
            'LenderMatchRequestEntity',
          ]
        ]);

        // new matching engine
        $selected_new_match = null;
        if (!empty($lead->lender_match_requests)) {
          // $lead->lender_match_requests[0]->match_ref
          $http = new Client();
          $header = [
              'type' => 'json',
              'headers' => ['x-api-key' => getenv('MATCHING_ENGINE_KEY')]
          ];
          $response = $http->get(getenv('DOMAIN_MATCHING_ENGINE') . "/get-match-results-selected/" . $lead->lender_match_requests[0]->match_ref, [], $header);
          $status = $response->getStatusCode();
          $match_result = $response->getJson();
          if ($status != '200') {
              throw new Exception($match_result['error']['message']);
          }

          $selected_new_match = $match_result['data']['matches'][0];
        }

        $pricing_commission = $lead['pricing']['commission'];


    



        $partnerUserRef = $this->request->query['partner_user_ref'];
        $partnerUserId = LendInternalAuth::unhashPartnerUserId($partnerUserRef);
        $partnerUser = TableRegistry::getTableLocator()
          ->get('PartnerUserEntity')
          ->find()
          ->contain([
            'ConPartnerUserSettingsEntity'
          ])
          ->where([
            'PartnerUserEntity.partner_user_id' => $partnerUserId,
          ])
          ->first();

        $conPartnerUserSettings = $partnerUser['user_settings'];

        $match = $lead['selected_lender_match'];

        $amountWithOverride = @$match['financed_amount'];

        if(!empty($selected_new_match['financed_amount'])){
          $amountWithOverride = $selected_new_match['financed_amount'];
        }

        if (
          !empty($lead['con_preliminary'][0]['finance_amount_manual'])
          && $lead['con_preliminary'][0]['finance_amount_manual'] > 0
        ) {
          $amountWithOverride = $lead['con_preliminary'][0]['finance_amount_manual'];
        }
        $pricing_commission = @$lead['pricing']['commission'];
        if (@$lead['pricing']['commission_type'] === '%') {
          $pricing_commission = $amountWithOverride * $pricing_commission / 100;
        }

        $fees = [];
        $companyName = @$lead['partner']['company_name'];
        $feeGroupTitlesMap = [
          'broker' => "Amounts Paid to $companyName",
          'lender' => 'Amounts Paid to the Credit Provider',
          'third party' => 'Amounts Paid to Other Third Parties',
        ];

        $selected_lender_name = null;
        if (!empty($selected_new_match)) {
          $selected_lender_name = $selected_new_match['lender_name'] ?? $selected_new_match['lenders']['lender_name'];
        } else {
          $selected_lender_name = @$lead['selected_lender_match']['lender_name'];
        }
        $commissions = [];
        $referrals = [];

        $selected_product_commission      = $selected_new_match['commission'] ?? 0.0;
        $selected_product_commission_type = $selected_new_match['commission_type'] ?? 'dollar';
        $selected_product_origination_fee = $selected_new_match['origination_fee'] ?? 0.0;
        $selected_product_financed_amount = $selected_new_match['financed_amount'] ?? 0.0;


        if (!empty($lead['con_credit_prop_fees'])) {
          foreach ($lead['con_credit_prop_fees'] as $fee) {
            if (empty($feeGroupTitlesMap[$fee['config_con_fee']['group']])) {
              if ($fee['config_con_fee']['group'] === 'commissions') {
                $commissions[] = [
                  'name' => $fee['display_fee_name'],
                  'paid_by' => $selected_lender_name,
                  'paid_to' => $fee['paid_to'],
                  'explanation' => $fee['explanation'],
                  'amount' => $fee['display_amount_string'],
                ];
              }
              if ($fee['config_con_fee']['group'] === 'referral') 
              {
                $fee_range_or_fixed = 'FIXED';
                $fee_percent_or_amount = 'fixed_amount';

                if ($fee['percent_or_amount'] === 'percent_of_loan') {
                  if($fee['range_or_fixed'] === 'Fixed')
                  {
                    if ($fee['referer_fee_percentage_of'] === 'BOTH') {
                        if ($selected_product_commission_type === 'dollar') {
                            $referrer_commission =    
                                ($selected_product_commission / 100) * ($fee['percent_of_loan'] ?? 0) +
                                ($selected_product_origination_fee * ($fee['percent_of_loan'] ?? 0)) / 100;
                        } else {
                            $referrer_commission = 
                                $selected_product_financed_amount * ($selected_product_commission / 10000) * ($fee['percent_of_loan'] ?? 0) +
                                $selected_product_origination_fee * ($fee['percent_of_loan'] ?? 0) / 100;
                        }
                        $ref_amount = '$' . number_format($referrer_commission, 2);
                    }
                    else if ($fee['referer_fee_percentage_of'] === 'COMMISSION') 
                    {
                      if ($selected_product_commission_type === 'dollar') {
                          $referrer_commission = 
                              ($selected_product_commission / 100) * ($fee['percent_of_loan'] ?? 0);
                      } else {
                          $referrer_commission = 
                              $selected_product_financed_amount * ($selected_product_commission / 10000) * ($fee['percent_of_loan'] ?? 0);
                      }
                      $ref_amount = '$' . number_format($referrer_commission, 2);
                    }
                    else if($fee['referer_fee_percentage_of'] === 'ORIGINATION_FEE')
                    {
                      $referrer_commission =    
                                ($selected_product_origination_fee * ($fee['percent_of_loan'] ?? 0)) / 100;
                      $ref_amount = '$' . number_format($referrer_commission, 2);
                    }
                  }
                  else if($fee['range_or_fixed'] === 'Range')
                  {
                      if (isset($fee['percent_of_loan_max']) && is_numeric($fee['percent_of_loan_max'])) {
                          $minPercent = isset($fee['percent_of_loan']) ? $fee['percent_of_loan'] : 0;
                          $maxPercent = $fee['percent_of_loan_max'];
                          $minCommission = 0;
                          $maxCommission = 0;
                  
                          if ($fee['referer_fee_percentage_of'] === 'COMMISSION') {
                              if ($selected_product_commission_type === 'dollar') {
                                  $minCommission = ($selected_product_commission * $minPercent) / 100;
                                  $maxCommission = ($selected_product_commission * $maxPercent) / 100;
                              } else {
                                  $minCommission = $selected_product_financed_amount * ($selected_product_commission / 100) * ($minPercent / 100);
                                  $maxCommission = $selected_product_financed_amount * ($selected_product_commission / 100) * ($maxPercent / 100);
                              }
                          } elseif ($fee['referer_fee_percentage_of'] === 'ORIGINATION_FEE') {
                              $minCommission = $selected_product_origination_fee * ($minPercent / 100);
                              $maxCommission = $selected_product_origination_fee * ($maxPercent / 100);
                          } elseif ($fee['referer_fee_percentage_of'] === 'BOTH') {
                              if ($selected_product_commission_type === 'dollar') {
                                  $minCommission = ($selected_product_commission * $minPercent) / 100 + ($selected_product_origination_fee * $minPercent) / 100;
                                  $maxCommission = ($selected_product_commission * $maxPercent) / 100 + ($selected_product_origination_fee * $maxPercent) / 100;
                              } else {
                                  $minCommission = $selected_product_financed_amount * ($selected_product_commission / 100) * ($minPercent / 100) + $selected_product_origination_fee * ($minPercent / 100);
                                  $maxCommission = $selected_product_financed_amount * ($selected_product_commission / 100) * ($maxPercent / 100) + $selected_product_origination_fee * ($maxPercent / 100);
                              }
                          } else {
                              $minCommission = ($selected_product_financed_amount * $minPercent) / 100;
                              $maxCommission = ($selected_product_financed_amount * $maxPercent) / 100;
                          }
                  
                          if ($selected_product_financed_amount === 0) {
                            $ref_amount = "$0.00 - $0.00";
                          }
                  
                          $minAud = number_format($minCommission, 2);
                          $maxAud = number_format($maxCommission, 2);
                  
                          //$ref_amount = "{$minPercent}% - {$maxPercent}% = \${$minAud} - \${$maxAud}";
                          $ref_amount = "\${$minAud} - \${$maxAud}";
                      }
                  }
                } else {
                  $ref_amount =  $fee['display_amount_string'];
                }


                
                $paidBy = $fee['paid_by'];
                if (empty($paidBy) || $paidBy === 'broker') {
                  $paidBy = $companyName;
                }
                $referrals[] = [
                  'name' => $fee['display_fee_name'],
                  'paid_by' => $paidBy,
                  'paid_to' => $fee['paid_to'],
                  'explanation' => $fee['explanation'],
                  'amount' => $ref_amount,
                ];
              }
              continue;
            }
            if (empty($fees[$fee['config_con_fee']['group']])) {
              $fees[$fee['config_con_fee']['group']] = [
                'title' => $feeGroupTitlesMap[$fee['config_con_fee']['group']],
                'fees' => [],
              ];
            }
            $fees[$fee['config_con_fee']['group']]['fees'][] = [
              'name' => $fee['display_fee_name'],
              'explanation' => $fee['explanation'],
              'amount' => $fee['display_amount_string'],
            ];
          }
        }

        if (!empty($selected_new_match)) {
          $match = $selected_new_match;
        } else {
          $match = $lead['selected_lender_match'];
        }

        $logoFullUrl = '';
        if (!empty($lead['partner']['logo'])) {
          $logoFullUrl = 'https://files.lend.com.au/' . $lead['partner']['logo'];
        }

        $loanTermOverride = $match['term_months'];
        if (!empty($lead['con_preliminary'][0]['loan_term_manual']) && $lead['con_preliminary'][0]['loan_term_manual'] > 0) {
          $loanTermOverride = $lead['con_preliminary'][0]['loan_term_manual'];
        }


        $date = date('jS F o');
        $queryData = $this->request->getQuery();
        if (!empty($queryData['date'])) {
          $date = $queryData['date'];
        }




        $creditProposalData = [
          'logo' => $logoFullUrl,
          'date' => $date,
          'company_name' => $conPartnerUserSettings['entity_name'],
          'address' => $conPartnerUserSettings['address'],
          'email' => $conPartnerUserSettings['email'],
          'phone' => $conPartnerUserSettings['phone'],
          'acl' => $conPartnerUserSettings['acl'],
          'acr' => $conPartnerUserSettings['acr'],
          'customer_name' => $owner['full_name'],
          'customer_address' => $owner['current_address']['full_address'],
          'customer_email' => $owner['email'],
          'customer_phone' => $owner['mobile_or_phone'],
          'credit_provider' => $selected_lender_name,
          'amount' => '$' . number_format($amountWithOverride, 2),
          'term' => $loanTermOverride . ' months',
          'fees' => array_values($fees),
          'commissions' => $commissions,
          'referrals' => $referrals,
          'con_partner_user_settings' => $conPartnerUserSettings,
          'broker_name' => $brokerName,
        ];
        return $this->setJsonResponse($creditProposalData);

      } else if ($this->request->query['type'] === 'vehicle-tax-invoice') {
        $leadRef = $this->request->query['lead_ref'];
        $partnerUserRef = $this->request->query['partner_user_ref'];
        $leadId = (new LendInternalAuth)->unhashLeadId($leadRef);
        $lead = TableRegistry::getTableLocator()->get('LeadEntity')->get($leadId, [
          'contain' => [
            'AllAddresses',
            'LeadAssetFinanceEntity',
            'PartnerEntity',
            'PocOwner',
            'PocOwner.CurrentAddress',
            'LeadAbnLookupEntity',
          ]
        ]);

        $owner = $lead['owner_poc'];
        $finance = $lead['asset_finance'];

        $partnerUserId = LendInternalAuth::unhashPartnerUserId($partnerUserRef);
        $partnerUser = TableRegistry::getTableLocator()
          ->get('PartnerUserEntity')
          ->get($partnerUserId, ['contain' => 'ConPartnerUserSettingsEntity']);

        $logoFullUrl = '';
        if (!empty($lead['partner']['logo'])) {
          $logoFullUrl = 'https://files.lend.com.au/' . $lead['partner']['logo'];
        }


        $data = [
          'logo' => $logoFullUrl,
          'date' => date('jS F o'),
          'full_name' => $owner['full_name'],
          'address' => $owner['current_address']['full_address'],
          'supplier_quote_ref' => $finance['supplier_quote_ref'],
          'condition' => $finance['condition'],
          'make_model' => implode(' ', [$finance['make'], $finance['model']]),
          'year' => $finance['year'],
          'series_variant' => $finance['variant'],
          'colour' => $finance['colour'],
          'rego' => $finance['rego'],
          'vin' => $finance['vin'],
          'engine' => $finance['engine'],
          'odometer' => $finance['odometer'],
          'broker_email' => $partnerUser['email'],
          'broker_mobile' => $partnerUser['mobile'],
          'asset_purchase_price' => $finance['asset_purchase_price'] ?? '',
          'asset_deposit' => $finance['asset_deposit'] ?? '',
          'asset_tradein_value' => $finance['asset_tradein_value'] ?? '',
        ];

        $data['is_commercial'] = false;
        if ($lead['lead_type'] === 'commercial') {
          $data['is_commercial'] = true;
          $data['entity_name'] = $lead['abn_lookup']['organisation_name'] ?? '';
          $data['business_name'] = $lead['abn_lookup']['business_name'] ?? '';
          $abnAcn = $lead['abn_lookup']['abn'] ?? '';
          if (!empty($lead['abn_lookup']['acn'])) {
            $abnAcn .= ' / ' . $lead['abn_lookup']['acn'];
          }
          $data['abn_acn'] = $abnAcn;

          $currentTradingAddress = [];
          foreach ($lead['all_addresses'] as $address) {
            if ($address['address_type'] !== 'trading' || !empty($address['date_to'])) {
              continue;
            }
            if (empty($currentTradingAddress) || $address['date_from']->timestamp > $currentTradingAddress['date_from']->timestamp) {
              $currentTradingAddress = $address;
            }
          }
          if (!empty($currentTradingAddress)) {
            $data['trading_address'] = $currentTradingAddress['full_address'];
          } else {
            $data['trading_address'] = '';
          }
        }

        return $this->setJsonResponse($data);
      }
    } catch (\Exception $e) {
      return $this->setJsonResponse(['error' => $e->getMessage()]);
    }
  }

  private function getHem($leadRef, $income, $maritalStatus, $numberOfDependants, $postcode) {
    $lendHemClient = new LendHEMServiceClient();
    if (!$numberOfDependants) {
      $numberOfDependants = 0;
    }
    if (empty($maritalStatus)) {
      $maritalStatus = false;
    }
    return $lendHemClient->calculateOrRetrieveHem($leadRef, $income, $maritalStatus, $numberOfDependants, $postcode);
  }

  private function validatePDFSignature($payload, $signature){
    $combined  = json_encode($payload) . getenv('PDF_SECRET');
    $sig = base64_encode(hash_hmac('sha256', $combined, getenv('PDF_SECRET'))); // to base64
    return $signature == $sig;
  }

  public function clone(){
    try {
      $this->request->allowMethod(['POST']);

      $user = $this->Auth->identify();
      $data = $this->request->getData();

      if (!isset($data['partner_lead_uploads'])) {
        $data['partner_lead_uploads'] = array();
      }

      if (!isset($data['ask_for_statements'])) {
        $data['ask_for_statements'] = array();
      }

      if (!isset($data['lead_ref']) || !isset($data['ask_for_statements']) || !isset($data['partner_lead_uploads']) || !is_array($data['ask_for_statements']) || !is_array($data['partner_lead_uploads'])) {
        return $this->setJsonResponse(['success' => false, 'message' => 'Missing Data']);
      }

      // if(empty($user) OR $user['account_type']==='Applicant') {
      //   return $this->setJsonResponse(['success'=>false, 'message'=>'User Credentials failed']);
      // }

      $old_lead_id = (new LendInternalAuth)->unhashLeadId($data['lead_ref']);

      if ($data['inter_lender_id']) {
        $lender = $this->loadModel('Lenders')->getLender(array('lender_id' => $data['inter_lender_id']));
      }

      // clone leads
      $new_lead_id = $this->loadModel('Leads')->clone($old_lead_id, @$lender['partner_id']);
      if (!$new_lead_id)   return  $this->setJsonResponse(['success' => false, 'message' => 'Clone Lead failed']);
      $new_lead_ref = (new LendInternalAuth)->hashLeadId($new_lead_id);
      $new_lead = $this->loadModel('Leads')->getLeadDetails($new_lead_id, ['get_all_owners' => false]);

      // clone lead_owners (only “Active” people)
      $lead_owners_list = [];
      $percent_complete = [];
      $request_bs_owner_ids = [];
      $all_lead_owners = $this->loadModel('LeadOwners')->getLeadOwners(array('lead_id' => $old_lead_id, 'status' => 'active'));
      foreach ($all_lead_owners as $old_lead_owner) {
        $old_lead_owner_id = $old_lead_owner['owner_id'];
        unset($old_lead_owner['owner_id']);
        $old_lead_owner['lead_id'] = $new_lead_id;
        $new_lead_owner_id = $this->loadModel('LeadOwners')->addLeadOwner($old_lead_owner);

        // clone lead_owner_employment (only “Active” records)
        $all_employment = $this->loadModel('LeadOwnerEmployment')->getLeadOwnerEmployments(array('lead_owner_id' => $old_lead_owner_id, 'status' => 'active'));
        foreach ($all_employment as $employment) {
          unset($employment['lead_owner_employment_id']);
          unset($employment['created']);
          unset($employment['updated']);
          $employment['lead_owner_id'] = $new_lead_owner_id;
          $this->loadModel('LeadOwnerEmployment')->addLeadOwnerEmployment($employment);
        }

        // clone lead_owner_addresses (only “Active” records)
        $all_addresses = $this->loadModel('LeadOwnerAddress')->getLeadOwnerAddresss(array('lead_owner_id' => $old_lead_owner_id, 'status' => 'active'));
        foreach ($all_addresses as $address) {
          unset($address['lead_owner_address_id']);
          unset($address['created']);
          unset($address['updated']);
          $address['lead_owner_id'] = $new_lead_owner_id;
          $this->loadModel('LeadOwnerAddress')->addLeadOwnerAddress($address);
        }

        if ($old_lead_owner_id != null)
          $lead_owners_list[$old_lead_owner_id] = $new_lead_owner_id;

        // Prepare for Request Bank Statements
        if (in_array($old_lead_owner_id, $data['ask_for_statements'])) {
          $employmentNeeded = true;
            if ((!empty($new_lead['product_type']['product_type_id']) AND $new_lead['product_type']['product_type_id']==10)
                OR (!empty($new_lead['product_type']['sub_product']) AND $new_lead['product_type']['sub_product']==10)
                OR (!empty($new_lead['lead']['product_type_id']) && $new_lead['lead']['product_type_id'] ==10)
          ) {
            $employmentNeeded = false;
          }
          $ownerPercentage = $this->loadModel('LeadOwners')->checkPercentage($old_lead_owner, false, $employmentNeeded);
          $percent_complete[$new_lead_owner_id]['percent_complete'] = $ownerPercentage['current_percentage'];
          array_push($request_bs_owner_ids, $new_lead_owner_id);
        }

      }

      // clone lead_assets (only “Active” records)
      $lead_asset_list = array();
      $lead_assets = $this->loadModel('LeadAssets')->getLeadAssets(array('lead_id' => $old_lead_id, 'status' => 'Active'));
      foreach ($lead_assets as $lead_asset) {
        if (array_key_exists($lead_asset['lead_owner_id'], $lead_owners_list))
          $lead_asset['lead_owner_id'] = $lead_owners_list[$lead_asset['lead_owner_id']];
        else
          unset($lead_asset['lead_owner_id']);
        $lead_asset['lead_id'] = $new_lead_id;
        $old_lead_asset_id = $lead_asset['lead_asset_id'];
        unset($lead_asset['lead_asset_id']);
        unset($lead_asset['created']);
        unset($lead_asset['updated']);
        $new_lead_asset_id = $this->loadModel('LeadAssets')->addLeadAsset($lead_asset);

        if ($old_lead_asset_id != null)
          $lead_asset_list[$old_lead_asset_id] = $new_lead_asset_id;
      }

      // clone lead_liabilities (only “Active” records)
      $lead_liabilities = $this->loadModel('LeadLiabilities')->getLeadLiabilities(array('lead_id' => $old_lead_id, 'status' => 'Active'));
      foreach ($lead_liabilities as $lead_liability) {
        if (array_key_exists($lead_liability['lead_owner_id'], $lead_owners_list))
          $lead_liability['lead_owner_id'] = $lead_owners_list[$lead_liability['lead_owner_id']];
        else
          unset($lead_liability['lead_owner_id']);
        if (array_key_exists($lead_liability['lead_asset_id'], $lead_asset_list))
          $lead_liability['lead_asset_id'] = $lead_asset_list[$lead_liability['lead_asset_id']];
        else
          unset($lead_liability['lead_asset_id']);
        $lead_liability['lead_id'] = $new_lead_id;
        unset($lead_liability['lead_liability_id']);
        unset($lead_liability['created']);
        unset($lead_liability['updated']);
        $this->loadModel('LeadLiabilities')->addLeadLiability($lead_liability);
      }

      // clone lead_asset_finance (if applic)
      $lead_asset_finance = $this->loadModel('LeadAssetFinance')->getLeadAssetFinance(array('lead_id' => $old_lead_id));
      if (!empty($lead_asset_finance)) {
        unset($lead_asset_finance['lead_asset_finance_id']);
        unset($lead_asset_finance['created']);
        unset($lead_asset_finance['updated']);
        $lead_asset_finance['lead_id'] = $new_lead_id;
        $this->loadModel('LeadAssetFinance')->addLeadAssetFinance($lead_asset_finance);
      }

      // clone lead_addresses (only “Active” records)
      $lead_addresses = $this->loadModel('LeadAddresses')->getLeadAddresses(array('lead_id' => $old_lead_id, 'status' => 'active'));
      foreach ($lead_addresses as $lead_address) {
        unset($lead_address['lead_address_id']);
        unset($lead_address['created']);
        unset($lead_address['updated']);
        $lead_address['lead_id'] = $new_lead_id;
        $this->loadModel('LeadAddresses')->addLeadAddress($lead_address);
      }

      // clone lead_references (only “active” records)
      $lead_references = $this->loadModel('LeadReferences')->getLeadReferences(array('lead_id' => $old_lead_id, 'status' => 'active'));
      foreach ($lead_references as $lead_reference) {
        unset($lead_reference['lead_reference_id']);
        unset($lead_reference['created']);
        unset($lead_reference['updated']);
        $lead_reference['lead_id'] = $new_lead_id;
        $this->loadModel('LeadReferences')->addLeadReference($lead_reference);
      }

      // clone lead_abn_lookup (only the LATEST record)
      $lead_abn_lookup = $this->loadModel('LeadAbnLookup')->getLeadABN($old_lead_id);
      if (!empty($lead_abn_lookup)) {
        unset($lead_abn_lookup['abn_id']);
        unset($lead_abn_lookup['created']);
        $lead_abn_lookup['lead_id'] = $new_lead_id;
        $this->loadModel('LeadAbnLookup')->addLookup($lead_abn_lookup);
      }

      // clone lead notes
      $lead_notes = $this->loadModel('LeadNotes')->getNotes(array('lead_id' => $old_lead_id));

      foreach ($lead_notes as $lead_note) {

        unset($lead_note['note_id']);
        unset($lead_note['created']);

        $lead_note['lead_id']         = $new_lead_id;
        $lead_note['partner_user_id'] = $user['partner_user_id'];

        $this->loadModel('LeadNotes')->addNote($lead_note);
      }

      // clone partner_lead_uploads (only the records requests in the API)
      foreach ($data['partner_lead_uploads'] as $partner_lead_upload_id) {
        $old_partner_lead_upload = $this->loadModel('PartnerLeadUploads')->getPartnerLeadUpload($partner_lead_upload_id);
        if ($old_partner_lead_upload['lead_id'] == $old_lead_id) {
          unset($old_partner_lead_upload['partner_lead_upload_id']);
          unset($old_partner_lead_upload['created']);
          $old_partner_lead_upload['lead_id'] = $new_lead_id;
          if (array_key_exists($old_partner_lead_upload['owner_id'], $lead_owners_list))
            $old_partner_lead_upload['owner_id'] = $lead_owners_list[$old_partner_lead_upload['owner_id']];
          else
            unset($old_partner_lead_upload['owner_id']);
          $new_partner_lead_upload_id = $this->loadModel('PartnerLeadUploads')->addPartnerLeadUpload($old_partner_lead_upload);

          // clone partner_lead_uploads_meta (child table of the partner_lead_uploads)
          $partner_lead_uploads_meta = $this->loadModel('PartnerLeadUploadsMeta')->getPartnerLeadUploadsMetas(array('partner_lead_upload_id' => $partner_lead_upload_id));
          foreach ($partner_lead_uploads_meta as $meta) {
            unset($meta['partner_lead_upload_meta_id']);
            unset($meta['created']);
            unset($meta['updated']);
            $meta['partner_lead_upload_id'] = $new_partner_lead_upload_id;
            $this->loadModel('PartnerLeadUploadsMeta')->addPartnerLeadUploadsMeta($meta);
          }
        }
      }

      // clone partner_user_leads (only the “ACCESS” records)
      if ($user) {
        $partnerUserLeadsTable = TableRegistry::getTableLocator()->get('PartnerUserLeadsEntity');
        $partnerUserLead = $partnerUserLeadsTable->newEntity([
          'partner_user_id' => $user['partner_user_id'],
          'status' => 'ACCESS',
          'lead_id' => $new_lead_id
        ]);
        $partnerUserLeadsTable->save($partnerUserLead);
      }

      // and add partner_lead_history
      $this->loadModel('PartnerLeadHistory')->addPartnerLeadHistory(
            array('partner_id'=> (!empty($user['partner_id']) ? $user['partner_id'] : (!empty($lender['partner_id']) ? $lender['partner_id'] : $data['partner_id'])),
          'lead_id' => $new_lead_id,
                   'history_detail'=>'Lead Added'));

      // remove broker flow background job and include BsAnalysis/ LendScore when clone
      $copyBsAnalysis = ["old_lead_id" => $old_lead_id,"new_lead_id" => $new_lead_id];
      $copyBsAnalysisResponse = $this->curl(getenv('DOMAIN_FULL_BS_ANALYSIS').'/copy-bs-analysis', $copyBsAnalysis, 'POST', ['x-api-key:'.getenv('FULL_BSA_API_KEY')]);
      // if (!$copyBsAnalysisResponse['success']) {
      //   throw new \Exception("Copy BsAnalysis/LendScore error: ".$copyBsAnalysisResponse['error']);
      // }

      if ($data['inter_lender_id']) {
        // clone lead uploads
        $lead_uploads = $this->loadModel('LeadUploads')->getLeadUploadedFiles($old_lead_id);
        if (!empty($lead_uploads)) {
          foreach ($lead_uploads as $bs) {
            if ($bs['include_for_lenders'] == 1) {
              unset($bs['upload_id']);
              unset($bs['lead_id']);
              $bs['lead_id'] = $new_lead_id;
              $this->loadModel('LeadUploads')->addLeadUploads($bs);
            }
          }
        }
      }

      // clone privacy forms
      $requested_privacy_form = $this->loadModel('PartnerRequestedPrivacyForms')->getPartnerRequestedPrivacyForms(array('lead_id' => $old_lead_id));
      if (!empty($requested_privacy_form)) {
        foreach ($requested_privacy_form as $privacy) {
          if ($privacy['include_for_lenders'] == 1) {
            unset($privacy['requested_privacy_form_id']);
            unset($privacy['created']);
            unset($privacy['updated']);
            unset($privacy['lead_id']);
            $privacy['lead_id'] = $new_lead_id;
            $this->loadModel('PartnerRequestedPrivacyForms')->addPartnerRequestedPrivacyForms($privacy);
          }
        }
      }

      // clone creditorwatch
      $cw_report = $this->loadModel('PartnerCrbCwReports')->getPartnerCrbCwReports(array('lead_id' => $old_lead_id));
      if (!empty($cw_report)) {
        foreach ($cw_report as $cw) {
          if ($cw['include_for_lenders'] == 1) {
            unset($cw['partner_crb_cw_report_id']);
            unset($cw['lead_id']);
            $cw['lead_id'] = $new_lead_id;
            $this->loadModel('PartnerCrbCwReports')->addPartnerCrbCwReport($cw);
          }
        }
      }

      // clone equifax
      $ef_report = $this->loadModel('PartnerCrbEfReports')->getPartnerCrbEfReports(array('lead_id' => $old_lead_id));
      if (!empty($ef_report)) {
        foreach ($ef_report as $ef) {
          if ($ef['include_for_lenders'] == 1) {
            unset($ef['partner_crb_ef_report_id']);
            unset($ef['lead_id']);
            $ef['lead_id'] = $new_lead_id;
            $this->loadModel('PartnerCrbEfReports')->addPartnerCrbEfReport($ef);
          }
        }
      }

      //clone quote
      $old_lead_quote = $this->loadModel('LeadQuotesRef')->getLeadQuoteRef(['lead_id' => $old_lead_id]);
      if (isset($old_lead_quote['quote_ref']) && !empty($old_lead_quote['quote_ref'])) {
        $url = getenv('QUOTES_LOOKUP') . 'quote-clone/' . $old_lead_quote['quote_ref'];
        $result = $this->curl($url, [], 'POST', ['x-api-key:' . getenv('QUOTES_API_KEY')]);
        if ($result['success']) {
          $leadQuoteInfo = array('quote_ref' => $result['data']['quote_ref'], 'lead_id' => $new_lead_id);
          $results = $this->loadModel('LeadQuotesRef')->addLeadQuoteRef($leadQuoteInfo);
          if (!$results) {
            return $this->setJsonResponse(['success' => false, 'message' => 'Entry already exist']);
          }
        }
      }

      // lead owner finances
      if (!empty($old_lead_owner_id)) {
        $all_finance = $this->loadModel('LeadOwnersFinances')->getLeadOwnersFinances($old_lead_owner_id);
      }

      foreach ($all_finance as $finance) {
        unset($finance['lead_owner_finance_id']);
        unset($finance['owner_id']);
        $finance['owner_id'] = $new_lead_owner_id;
        $this->loadModel('LeadOwnersFinances')->addLeadOwnerFinance($finance);
      }

      // Request Bank Statements

      if (!$data['inter_lender_id']) {

        $bs_data = ['lead_ref' => $new_lead_ref, 'ask_applicants' => ['bs' => $request_bs_owner_ids], 'percent_complete' => $percent_complete];

        $result = $this->sendLead($bs_data);
        if (!$result)
          return $this->setJsonResponse(['success' => false, 'message' => 'Request Bank Statements failed']);

      }


      if ($data['inter_lender_id']) {
        $lender = $this->loadModel('Lenders')->getLender(array('lender_id' => $data['inter_lender_id']));

        $intermediary_setting['original_lead_id'] =  $old_lead_id;
        $intermediary_setting['new_lead_id']      =  $new_lead_id;
        $intermediary_setting['inter_lender_id']  =  $lender['partner_id'];
        $intermediary_setting['product_id']       =  $data['product_id'];

        $this->loadModel('Leads')->intermediaryLeadSettingsUpdate($intermediary_setting);

        // $leadsController =  new \App\Controller\LeadsController();
        // $sent_to_lender = $leadsController->sendToInterMediaryLender($intermediary_setting);


        return $this->setJsonResponse(['success' => true, 'lead_ref' => $new_lead_ref]);


        //$this->curl(getenv('DOMAIN_CRM').'/intermediary/interm-lead-update-status',$intermediary_setting, 'POST');

      }

      return $this->setJsonResponse(['success' => true, 'lead_ref' => $new_lead_ref]);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()]);
    }
  }



    public function sendLead($manually_data = false) {
    /**
     * The email would contain the list of requested file which were not previously attached
     * If the privacy form is selected and was not attached previously, in the email there should be a link to download the privacy form
     */
    $user = $this->Auth->identify();
    $partnerId = (int)$user['partner_id'];
    $partnerUserId = (int)$user['partner_user_id'];
    $accountType = $user['account_type'];
    $requestedFiles = "";
    $partnerEmail = (empty($user['email'])) ? '' : $user['email'];

    if ((!empty($partnerId) && $partnerId === 2)
      || ($accountType !== 'Lend Staff' && empty($partnerId))
      || empty($partnerUserId)){  //demo account or un-authorized
      if (empty($manually_data))
        return $this->setJsonResponse(array('success' => false));
      else
        return false;
    }

    if (empty($manually_data))
      $data = $this->request->getData();
    else
      $data = $manually_data;

    $owners = $this->_decideEmailCode($data);
    $leadId = $this->loadModel('leads')->findLeadIdByLeadRef($data['lead_ref']);

    $emailed = []; //prevent double email if owner id's share the same one

    foreach ($owners as $ownerId => $task) {
      $applicant_note = !empty($data['message']) ?  $data['message'] : ''; //needs to be inside loop - Being mutated by getRequestedFiles below
      $applicant = (new LendInternalAuth)->hashLeadId($ownerId);
      $to = $this->getOwnerIdEmail($ownerId);
      $name = $this->getOwnerIdName($ownerId);

      if (in_array($to, $emailed)) continue;
      $emailed[] = $to;
      $code = $task['code'];
      if ($code == 'AskAttach') {
        $requestedFiles = $this->getRequestedFiles($applicant_note, $leadId, $data['lead_ref'], $ownerId);
        if (!$requestedFiles) continue; // this specific user has no files to request
      }

      if ($applicant_note) $applicant_note =  '' . $applicant_note . "\r\n";
      $additionalCode = [];
      $additionalCode['applicant'] = $applicant;
          if($task['sections']){
            $additionalCode['sections'] = $task['sections'];
          }
      if (!empty($name['first_name'])) $additionalCode['first_name'] = $name['first_name'];
      if (!empty($name['last_name'])) $additionalCode['last_name'] = $name['last_name'];
      // http_build_query
      $adhocData = [
        'percent_complete'      => @$data['percent_complete'][$ownerId],
        'to'                    => [$to],
        'additionalCode'        => http_build_query($additionalCode),
        'applicant_note'        => $applicant_note,
        'client_first_name'     => @$name['first_name'],
        'client_last_name'      => @$name['last_name'],
        'requested_files'       => @$requestedFiles,
        'ask_applicant_tasks'   => @$task['tasks'],
        'partner_email'         => $partnerEmail
      ];

      $this->LoadModel('PartnerNotifications')->sendPartnerNotifications($user['partner_id'], $code, $leadId, $adhocData);

    }

    if (empty($manually_data))
      return $this->setJsonResponse(array('success' => true));
    else
      return true;
  }


  /*
    * THIS WILL NEED TO BE UPDATED TO HANDLE OTHER PRODUCT TYPES
    * e.g. export Asset Finance PDF: http://partners.lend.local/lead-apis/export-as-pdf/04C9DL1/export-lead/Asset Finance
    * e.g. export Operating Lease Privacy Consent PDF: http://partners.lend.local/lead-apis/export-as-pdf/04C9DL1/privacy-consent/Operating Lease
    */
    public function exportAsPdf($payload_params=false, $pdf_type=false, $template=false){
      if (!is_array($payload_params)) {
        $lead_ref = $payload_params;
      }
      if (is_array($payload_params)) {
        extract($payload_params);
      }

    if (empty($pdf_type))
      throw new \Exception("Missing pdf type.");

    if (!$pdf_type == 'export-credit-guide') {
      if (empty($lead_ref))
        throw new \Exception("Missing lead ref.");

      if (empty($template))
        throw new \Exception("Missing template.");
    }

    $partner_user = $this->Auth->identify();
    if ($partner_user === false) {
      $partner_user = $this->Auth->user();
    }
    $is_lender_portal = $partner_user['account_type'] == 'Lender' ? true : false;
    if (empty($partner_user_ref)) {
      $partner_user_ref = $partner_user['partner_user_ref'];
    }

    if (!$is_lender_portal) {
      // Get logo if present for broker account
      $logo = $this->loadModel('Partners')->getPartnerField($partner_user['partner_id'], "logo");
    }

    if (!$pdf_type == 'export-credit-guide') {
      // Check this lead is belong to this partner
      $lead_id = (new LendInternalAuth)->unhashLeadId($lead_ref);
      $permission_check = $this->checkPermission($lead_id, false, false, $is_lender_portal);
      if (!$permission_check['success']) throw new \Exception($permission_check['message']);
    }

    if ($pdf_type == 'export-lead' || $pdf_type == 'export-lead-post' )
      $payload = array(
        'lead_ref' => $lead_ref,
        // 'expires' => time() + (24 * 60 * 60); // TODO: Expire 24hrs from now
        'finance_type' => $template
      );
    else if ($pdf_type == 'export-credit-guide')
      $payload = $payload_params;
    else if ($pdf_type == 'export-credit-proposal') {
      $payload = [
        'owner_ref' => $owner_ref,
        'partner_user_ref' => $partner_user_ref,
      ];

      if (!empty($date)) {
        $payload['date'] = $date;
      }
    }
    else if ($pdf_type == 'export-vehicle-tax-invoice') {
      $payload = [
        'lead_ref' => $lead_ref,
        'partner_user_ref' => $partner_user_ref,
      ];
    }
    else if ($pdf_type == 'export-preliminary-assessment') {
      $payload = [
        'lead_ref' => $lead_ref,
        'partner_user_ref' => $partner_user_ref,
      ];
    }
    else
      $payload = array(
        'lead_ref' => $lead_ref,
        // 'expires' => time() + (24 * 60 * 60); // TODO: Expire 24hrs from now
        'consent_type' => $template
      );

    // Append the logo to payload if not null
    if ($logo) {
      $payload['logo'] = $logo;
    }

    $combined  = json_encode($payload) . getenv('PDF_SECRET');
    $signature = base64_encode(hash_hmac('sha256', $combined, getenv('PDF_SECRET'))); // to base64

    // Construct the URL
    $url = getenv('DOMAIN_PDF') . '/' . $pdf_type . '?' . http_build_query(array(
      'payload' => json_encode($payload),
      'signature' => $signature
    ));

    // and redirect to it...
    if ($pdf_type == 'export-lead-post'){
      $postPayload = $this->_preparePdf($lead_ref);
      $http = new Client();
      $responseBodyRaw = $http->post($url, json_encode($postPayload, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES), ['http_errors' => false]);
      $dateFormatted = date('Y-m-d-H-i-s', time());
      $displayName = "application-{$lead_ref}-{$dateFormatted}.pdf";
      header('Pragma: public');     // required
      header('Expires: 0');     // no cache
      header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
      header('Cache-Control: private', false);
      header('Content-Type: application/pdf');
      header("Content-Disposition: inline; filename=\"{$displayName}\"");
      header('Content-Transfer-Encoding: binary');
      header('Connection: close');
      echo $responseBodyRaw->getBody()->getContents();
      exit;
    } else {
      return $this->redirect($url);
    }
    
  }


  public function init($lead_ref = false, $version = 0)
  {
    try {
      ConnectionManager::alias('reader_db', 'default');
      $partner_user = $this->Auth->identify();
      if (empty($lead_ref)) throw new \Exception("Missing lead ref.");

      $lead_id = (new LendInternalAuth)->unhashLeadId($lead_ref);

      // Check if Intermediary Cloned
      $original = $this->loadModel('IntermediaryLenderMapping')->getIntermediaryLenderMapping(array('new_lead_id' => $lead_id));

      if (!empty($original)) {
        $original_lead = $this->_getLead($original['original_lead_id'], true);
        $leadAddedHistoryRecord = $this
          ->loadModel('PartnerLeadHistory')
          ->getPartnerLeadHistory(['lead_id' => $original['original_lead_id'], 'history_detail' => 'Lead Added']);
        if (empty($leadAddedHistoryRecord[0]['partner_user_id'])) {
          $referrer_user = $this->loadModel('PartnerUsers')->getPartnerUser(array('partner_id' => $original_lead['lead']['partner_id']));
          $referrer = $this->loadModel('Partners')->getpartner(array('partner_id' => $original_lead['lead']['partner_id']));
        } else {
          $referrer_user = $this
            ->loadModel('PartnerUsers')
            ->getPartnerUser(['partner_user_id' => $leadAddedHistoryRecord[0]['partner_user_id']]);
          $referrer = $this->loadModel('Partners')->getpartner(array('partner_id' => $original_lead['lead']['partner_id']));
        }
      }

      $configs = $this->_getConfigs();
      $lead = $this->_getLead($lead_id);

      $partner_account = false;
      if ($lead['lead']['account_id']) {
        $partner_account = $this->loadModel("Leads")->getLeadAccount($lead['lead']['account_id']);
      }

      $intermediary_group = false;
      if (!empty($lead['lender'])) {
        $intermediary_group = $this->loadModel('IntermediaryLenders')->getInterLender(array('id' => $lead['lender'][0]['intermediary_lender_id']));
      }
      $partner = $this->loadModel('Partners')->getpartner(['partner_id' => $lead['lead']['partner_id']]);
      $partner_feature = $this->loadModel('PartnerFeatureFlags')->getFeatureFlag(['partner_id' => $lead['lead']['partner_id']]);
      $partner_user['clients'] = !empty($lead['lead']['partner_id']) && !empty($partner_user['partner_user_id'])
        ? $this->loadModel('PartnerClients')->getVisibleClients([
          'partner_id' => $lead['lead']['partner_id'],
          'partner_user_id' => $partner_user['partner_user_id']
        ])
        : [];
      $partner_users = $this->_getPartnerUsers();
      $partner_aliases = $this->_getPartnerAliases($lead['lead']['partner_id']);

      $manual_status = TableRegistry::getTableLocator()
      ->get('ManStatusGroupEntity')
      ->find('all', ['conditions' => ['ManStatusGroupEntity.partner_id'=>$lead['lead']['partner_id'], 'ManStatusGroupEntity.active'=> 1]])
      ->contain([
        'ManStatusEntity'
      ]);
      unset($lead['bank_statements_analysis']);
      ConnectionManager::dropAlias('default'); 

      $lead_associated = TableRegistry::getTableLocator()
      ->get('LeadAssociatedDataEntity')
      ->find('all', [
        'conditions' => ['lead_id' => $lead_id],
        'order' => ['id DESC']
      ])
      ->first();

      $intermediary_record = TableRegistry::getTableLocator()->get('IntermediaryLenderMapping')
      ->find('all', ['conditions' => ['new_lead_id' => $lead_id]])->first();
      
      return $this->setJsonResponse(['success'              => true,
        'configs'             => $configs['configVersion'] > $version ? $configs : false, //only return if newer
        'partner'             => $partner,
        'partner_feature'     => $partner_feature,
        'partner_user'        => $partner_user,
        'partner_users'       => $partner_users,
        'partner_aliases'     => $partner_aliases,
        'lead'                => $lead,
        'intermediary_group'  => !empty($intermediary_group) ? $intermediary_group : [],
        'original_lead'       => !empty($original_lead) ? $original_lead : [],
        'referrer'            => !empty($referrer) ? $referrer : [],
        'referrer_user'       => !empty($referrer_user) ? $referrer_user : [],
        'broker_contact'      => $this->_getBrokerContact($lead['lead']['partner_id'], $lead_id),
        'account'             => !empty($partner_account) ? $partner_account : [],
        'manual_status'       => $manual_status,
        'lead_associated'     => !empty($lead_associated) ? $lead_associated->toArray() : [],
        'is_intermediary'     => $intermediary_record ? true : false,
      ]);
    } catch (\Exception $e) {
      if (!in_array($e->getMessage(), ['Missing lead ref.'])) {
        Log::error($e->getMessage());
        Log::error($e->getTraceAsString());
      }
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()]);
    }
  }

    public function configs () {
    ConnectionManager::alias('reader_db', 'default');
    $configs_var = $this->_getConfigs();
    ConnectionManager::dropAlias('default');
    return $this->setJsonResponse([
      'success' => true,
      'data' => $configs_var
    ]);
  }

    public function refresh($lead_ref) {
      ConnectionManager::alias('reader_db', 'default');
    //sometimes it's necessary to refresh lead details from the client
    $lead_id = (new LendInternalAuth)->unhashLeadId($lead_ref);
    $lead = $this->_getLead($lead_id);
    $partner_account = [];
    if ($lead['lead']['account_id']) {
      $partner_account = $this->loadModel("Leads")->getLeadAccount($lead['lead']['account_id']);
    }
    ConnectionManager::dropAlias('default');
    return $this->setJsonResponse(['success' => true, 'lead' => $lead, 'account' => $partner_account]);
  }

    public function refreshCloneLead($lead_ref) {
    //sometimes it's necessary to refresh lead details from the client
    $lead_id = (new LendInternalAuth)->unhashLeadId($lead_ref);
    $lead = $this->_getLead($lead_id);
    return $this->setJsonResponse(['success' => true, 'original_lead' => $lead]);
  }

    private function getOwnerIdEmail($ownerId) {
    $email = $this->loadModel('LeadOwners')->getLeadOwnerEmail($ownerId);
    return $email ? $email :  false;
  }

    private function getOwnerIdName($ownerId) {
    $name = $this->loadModel('LeadOwners')->getOwnerIdName($ownerId);
    return $name ? $name :  false;
  }

    private function getRequestedFiles(&$note, $leadId, $leadRef , $ownerId) {

    $this->loadModel('PartnerCustomPrivacyForms');
    $s3Link = $this->PartnerCustomPrivacyForms->getS3Link($leadId);

    //return requested files that haven't been uploaded
    //attach the general $note - if it's empty, attach the note in the requested file (if present)
    $requested = '';
    $base = explode('://', $this->request->getUri())[0] . '://' . $this->request->host();
    $pending = $this->loadModel('PartnerLeadUploads')->getRequestedFiles($leadId, $leadRef, $ownerId, $base, $s3Link);

    foreach ($pending['pending'] as $file) {
      $requested .= '&#8226; ' . $file;
      $requested .= "\r\n";
    }

    if (count($pending['pending']) && $pending['note']) { //only add a specific note if general note is empty
      $note = $pending['note'];
    }

    return  $requested;
  }

  private function _getLenderAboutLeadCallMeFirst($lead, $partner_id, $lead_id)
  {
    $ret = [];
    if (!empty($lead['lead']['call_me_first'])) {
      $ret['Who to Contact'] = 'Broker';
      $ret['Broker Details'] = $this->_getBrokerContact($partner_id, $lead_id);
    } else {
      $ret['Who to Contact'] = 'Client';
      //Note: Call on behalf of will only be needed if "Who to Contact" is "Client"
      if (!empty($lead['lead']['partner_alias_id'])) {
        $ret['Call on behalf of'] = $this->loadModel('Leads')->getLeadAliasName($lead['lead']['partner_alias_id']);
      }
    }

    return $ret;
  }

    private function _getBrokerContact($partnerId, $leadId) {
    $broker = [
      'company' => 'Unknown',
      'person' => '',
      'phone' => '',
      'email' => '',
    ];
    if (empty($partnerId)) return $broker;

    $partner_company_name  = $this->loadModel('Partners')->getPartnerCompanyName($partnerId);

    if (!empty($partner_company_name)) {
      $broker['company'] = $partner_company_name;
    }

    // Get the assigned broker
    $partnerUser = $this->loadModel('PartnerUserLeads')->getCurrentPartnerUser($leadId, true);
    if (!empty($partnerUser)) reset($partnerUser);

    if (empty($partnerUser)) {
      $partner_users = $this->loadModel('PartnerUsers')->getPartnerUsers($partnerId);
      if ($partner_users) {
        $key = array_search('1', array_column($partner_users, 'point_of_contact'));
        if ($key !== false) {
          $partnerUser[$key] = $partner_users[$key];
        }
      }
    }

    if (!empty($partnerUser)) {
      foreach ($partnerUser as $id => $user) {
        if (!empty($user['name'])) $broker['person'] = $user['name'];
        if (!empty($user['mobile'])) $broker['phone'] = $user['mobile'];
        elseif (!empty($user['phone'])) $broker['phone'] = $user['phone'];
        if (!empty($user['email'])) $broker['email'] = $user['email'];
        break;
      }
    }

    return $broker;
  }

    private function _getConfigs(){
      
    /** NOTE: Please check app_config_version of config_other has been updated, after the config change, so vue can update itself */
    $config_model = new Config;
    $configs = [];
    $configs['configVersion'] = $config_model->getAppConfigVersion();
    $configCacheVersion = "V1_".$configs['configVersion'];
    $configs = Cache::read($configCacheVersion, 'lend_config');
    if(!empty($configs)){
      $configs = json_decode($configs, true);
    }else{
      $this->loadModel('PartnerProductTypes');
      $this->loadModel('LendStatuses');
      $this->loadModel('occupations');
      $configs['Lend'] = [
        'homeowner_types'                      => Configure::read('Lend.homeowner_types'),
        'lead_owner_type'                      => Configure::read('Lend.lead_owner_type'),
        'AU_states'                            => Configure::read('Lend.AU_states'),
        'bs_slugs_logo_available'              => Configure::read('Lend.bs_slugs_logo_available'),
        'pf_delivery_costs'                    => Configure::read('Lend.pf_delivery_costs'),
        'ReportCost'                           => Configure::read('Lend.ReportCost'),
      ];

      //$configs['purpose'] = $config_model->putOtherToEndOfTheList($config_model->getConfig('frm_purpose', ['status' => 1]), 'purpose');
      $configs['purpose'] = $config_model->getCommercialOnly($config_model->getSpecialConfig('frm_purpose', ['status' => 1]), 'uses', 'set');
      $configs['purpose_private'] = $config_model->putOtherToEndOfTheList($config_model->getConfig('frm_private_lending_purpose', ['status' => 1]), 'purpose');
      $configs['equipment'] = $config_model->groupEquipment($config_model->getCommercialOnly($config_model->getAllEquipment(), 'uses', 'set'));
      $configs['equipment_condition'] = $config_model->getEnumValues('leads', 'equipment_condition');
      $configs['equipment_source'] = $this->loadModel("Leads")->getAvailableEquipmentSource();
      $configs['loan_terms'] = $config_model->putOtherToEndOfTheList($config_model->getConfig('frm_loan_terms', ['status' => 1]), 'loan_term');
      $configs['operating_type'] = $config_model->getEnumValues('lead_asset_finance', 'operating_type');
      $configs['personal_service'] = $config_model->getEnumValues('lead_asset_finance', 'personal_service');
      $configs['green_electric_vehicle'] = $config_model->getEnumValues('lead_asset_finance', 'green_electric_vehicle');
      $configs['third_party_evaluation'] = $config_model->getEnumValues('frm_equipment', 'third_party_evaluation');
      $configs['asset_reading'] = $config_model->getEnumValues('frm_equipment', 'asset_reading');
      $configs['trading_period'] = $config_model->getEnumValues('partner_account_meta', 'trading_period_select');
      $configs['industries'] = $config_model->groupIndustry($config_model->getConfig('config_industries'));
      $configs['occupations'] = $config_model->getOccupationlist();


      $configs['product_types'] = $this->PartnerProductTypes->groupProductType($this->PartnerProductTypes->getPartnerProductTypes(['active' => 1, 'product_type_name <>' => 'Consumer Loan' ], true));
      $configs['all_product_types'] = $this->PartnerProductTypes->getPartnerProductTypes([], true);
      // Ari added the line below and tested successfully
      $configs['residency_status'] = $config_model->getEnumValues('lead_owners', 'residency_status');
      $configs['trading_period_select'] = $config_model->getEnumValues('leads', 'trading_period_select');

      $configs['asset_types'] = [
        'business' => $config_model->putOtherToEndOfTheList($config_model->getConfig('config_asset_types', ['asset_type' => 'business', 'status' => 1]), 'asset_type_name'),
        'personal' => $config_model->putOtherToEndOfTheList($config_model->getConfig('config_asset_types', ['asset_type' => 'personal', 'status' => 1]), 'asset_type_name'),
      ];
      $configs['liabilities'] = [
        'business' => $config_model->putOtherToEndOfTheList($config_model->getConfig('config_liabilities', ['liability_type' => 'business', 'status' => 1]), 'liability_name'),
        'personal' => $config_model->putOtherToEndOfTheList($config_model->getConfig('config_liabilities', ['liability_type' => 'personal', 'status' => 1]), 'liability_name'),
      ];
      $configs['entity_types'] = $config_model->getConfig('frm_entity_type', ['status' => 1]);
      $configs['abn_entity_types'] = $config_model->getConfig('abn_entity_types');
      $configs['asset_sale_types'] = $config_model->getConfig('asset_calc_sale_types');
      $configs['reference_types'] = $config_model->getEnumValues('lead_references', 'reference_type');
      $configs['lead_asset_finance_configs'] = [
        'condition' => $config_model->getEnumValues('lead_asset_finance', 'condition'),
        'reason_for_purchase' => $config_model->getEnumValues('lead_asset_finance', 'reason_for_purchase'),
        'supplier_type' => $config_model->getEnumValues('lead_asset_finance', 'supplier_type'),
        'personal_service_options' => $config_model->getEnumValues('lead_asset_finance', 'personal_service'),
        'sale_type' => $this->_getAssetSaleTypes(),
        'fuel_type' => $config_model->getEnumValues('lead_asset_finance', 'fuel_type'),
        'transmission' => $config_model->getEnumValues('lead_asset_finance', 'transmission'),
      ];
      $configs['contract_types'] = $config_model->getEnumValues('lead_asset_finance', 'contract_type');
      $configs['employment_types'] = $config_model->getEnumValues('lead_owner_employment', 'employment_type');
      $configs['living_status'] = $config_model->getEnumValues('lead_owner_addresses', 'living_status');
      $configs['marital_status'] = $config_model->getEnumValues('lead_owners', 'marital_status');
      $configs['credit_history'] = $config_model->getEnumValues('lead_owners', 'credit_history');
      $customer_types = $config_model->getEnumValues('leads', 'customer_type');
      $customer_types = array_diff($customer_types, ['Businesses you invoice']);
      $customer_type = array_values($customer_types);
      $configs['customer_type'] = $customer_type;
      $configs['lease_or_finance'] = $config_model->getEnumValues('leads', 'lease_or_finance');
      $configs['security_type'] = $config_model->getEnumValues('lead_owners', 'security_type');
      $configs['gender'] = $config_model->getEnumValues('lead_owners', 'gender');
      $configs['driving_licence_type'] = $config_model->getEnumValues('lead_owners', 'driving_licence_type');
      $configs['owner_titles'] = $config_model->getEnumValues('lead_owners', 'title');
      $configs['requested_specifies'] = $this->_formatDocumentTypes(TableRegistry::getTableLocator()->get('ConfigDocumentTypeEntity')->find('list')->where(['is_account_level' => 1])->order("type_name ASC")->toArray());
      $configs['partner_lead_uploads_specifies'] = $this->_formatDocumentTypes(TableRegistry::getTableLocator()->get('ConfigDocumentTypeEntity')->find('list')->where(['is_lead_level' => 1])->order("type_name ASC")->toArray());
      $configs['partner_lead_uploads_specifies_sub'] = $this->filterSpecifics($configs['partner_lead_uploads_specifies']);
      $configs['partner_account_uploads_specifies'] = $this->_formatDocumentTypes(TableRegistry::getTableLocator()->get('ConfigDocumentTypeEntity')->find('list')->where(['is_account_level' => 1])->order("type_name ASC")->toArray());
      $configs['partner_applicant_group_uploads_specifies'] = $this->_formatDocumentTypes(TableRegistry::getTableLocator()->get('ConfigDocumentTypeEntity')->find('list')->where(['is_applicant_level' => 1])->order("type_name ASC")->toArray());
      $configs['books_package'] = $config_model->putOtherToEndOfTheList($config_model->getConfig('frm_books_package', ['status' => 1]), 'books_package');
      $configs['trustee_type'] = $config_model->getEnumValues('entity_trust', 'trustee_type');
      $configs['trust_settor'] = $config_model->getEnumValues('entity_trust', 'trust_settor');
      $configs['max_amount'] = ********;
      $configs['max_invoices'] = 127;
      $configs['domains'] = [
        'domain_wp'     => getenv('DOMAIN_WP'),
        'domain_crm'    => getenv('DOMAIN_CRM'),
        'domain_app'    => getenv('DOMAIN_APP', true),
        'domain_bro'    => getenv('DOMAIN_BRO', true),
        'domain_bss'    => getenv('DOMAIN_BSS'),
        'domain_files'  => getenv('DOMAIN_FILES'),
        'domain_glass_service' => getenv('DOMAIN_GLASS_SERVICE'),
        'domain_bs_spa' => getenv('DOMAIN_BS_SPA'),
      ];
      $configs['max_lender_preferences'] = 5;
      $configs['lend_statuses'] = $this->LendStatuses->filterByFields($this->LendStatuses->getLendStatuses(['portal_selectable' => 1], ['group_name' => 'asc', 'portal_order' => 'asc']), ['lend_status_id', 'status_name', 'group_name', 'off_panel_lender']);
      $configs['required_docs'] = $this->loadModel('ProductRequiredDocs')->getRequiredDocs();
      $configs['config_requirements'] = TableRegistry::getTableLocator()
        ->get('config_requirements')
        ->find('list', ['valueField' => 'requirement'])
        ->where(['active' => true])
        ->toArray();
      Cache::write($configCacheVersion, json_encode($configs), 'lend_config');
    }
    return $configs;
  }


    private function _getLead($lead_id, $skip_permission_check=false){

    $this->loadModel('Leads');
    if (getenv('LEND_ENV') == 0) {
      $partner_user = $this->Auth->user();
    } else {
      $partner_user = $this->Auth->identify();
    }
    $lead = $this->Leads->getLeadDetails($lead_id, ['get_all_owners' => true]);
    if (empty($lead)) throw new \Exception("Can't find a lead.");
    // Check this lead is belong to this partner
    if (!$skip_permission_check) {
      $permission_check = $this->checkPermission($lead_id, $lead['lead'], $partner_user['account_type'] === 'Applicant');
      if (!$permission_check['success']) throw new \Exception($permission_check['message']);
    }

    // Organise lead_owners details
    $lead['lead_owner'] = $this->_getOwnersMoreDetails($lead);

    // Get partner lead history and who added the lead
    $lead['history'] = $this->_getHistory($lead_id);
    $lead['added_by'] = $this->_getAddedBy($lead['history']);
    $lead['current_user'] = $this->_getCurrentUser($lead_id);
    $lead['current_referer'] = $this->_getCurrentReferer($lead['lead']['referrer_person_id']);
    $lead['all_referer'] = $this->_getAllReferer($lead['lead']);
    $lead['prev_sales'] = $this->_getPrevSales($lead_id, $lead);
    $lead['illion_pdfs'] = $this->_getBSPdfs($lead_id);
    $lead['cw_reports'] = $this->_getCwCreditReports($lead_id);
    $lead['ef_reports'] = $this->_getEfCreditReports($lead_id);
    $lead['partner_lead_uploads_requested'] = $this->_getRequested($lead_id);
    $lead['partner_lead_uploads_requested_details'] = $this->_getRequestedDetails($lead['partner_lead_uploads_requested'],  $lead['lead']['lead_ref']);
    // BS status
    $accountLevelBSAccountData = ["leadId" => $lead_id];
    // $accountLevelBSAccountSignature = CurlHelper::generateSignature($accountLevelBSAccountData);
    // $accountLevelBSAccountUrl = getenv('DOMAIN_BANKFEEDS')."/get-bank-account-info-by-partner-account-id?signature=".$accountLevelBSAccountSignature;
    // $bs_accounts = $this->curl($accountLevelBSAccountUrl, $accountLevelBSAccountData, 'POST', ["x-api-key: ".getenv('BANK_STATEMENT_API_KEY')]);
    $curl = new CurlHelper(getenv('DOMAIN_BSS') . "/statements-by-lead-id");
    $bs_accounts = $curl->post($accountLevelBSAccountData, false, ["x-api-key" => $this->lendconfig['bs_service_auth_key']]);

    if (isset($bs_accounts['success']) && $bs_accounts['success'] == true) {
      $lead['bs_accounts'] = array_merge($lead['bs_accounts'], $bs_accounts['data']);
    }
    list($lead['lead_activities'], $lead['bs_status'], $lead['bs_summary']) = $this->_getBSStatus($lead_id, $lead);

    // Get addresses. * at the moment, it's only for Asset Finance > mailing address
    $lead['lead_addresses'] = $this->_getLeadAddresses($lead_id);

    // Lend Score
    $lead['lend_score'] = $this->_getLendScore($lead_id);
    $lead['lend_score_config'] = $this->loadModel('LendScoreConfig')->getLendScoreConfig();
    $lead['lend_score_config'] = array_intersect_key($lead['lend_score_config'], ['boundaries' => 1]);

    // Partner Lead Uploads
    $lead['partner_lead_uploads'] = $this->loadModel('PartnerLeadUploads')->getPartnerLeadUploadsWithMeta($lead_id, true);

    // Details for Asset Finance
    $lead['lead_asset_finance'] = $this->_getLeadAssetFinance($lead_id);
    if (empty($lead['lead_asset_finance'])) $lead['lead_asset_finance']  = (object)null;

    $lead['lead_assets'] = $this->_getLeadAssets($lead_id);
    $lead['lead_liabilities'] = $this->loadModel('LeadLiabilities')->getLeadLiabilities(['lead_id' => $lead_id]);
    $lead['lead_references'] = $this->loadModel('LeadReferences')->getLeadReferences(array('lead_id' => $lead_id, 'status' => 'active'));

      if(!empty($partner_user) AND $partner_user['account_type']!=='Applicant'){
      $lead['lead_notes'] = $this->loadModel('LeadNotes')->getNotesByLeadId($lead_id, 0, 1000, $partner_user['partner_user_id']);
      $lead['sms_communications'] = $this->loadModel('PartnerSmsHistory')->getAllSmsHistoryWithPartnerUserByLeadId($lead_id);
      $lead['tasks'] = $this->loadModel('PartnerCallbacks')->getPartnerCallbacksWithDetails(array('pc.lead_id' => $lead_id), array('pc.status' => 'asc', 'pc.scheduled_time' => 'desc'), 0, 1000);
    }

    // Status
    $lead['status']['readonly'] = $this->_checkReadOnly($lead_id, $lead);
    $sentPreventRow = $this->Leads->send_prevention->getSendPreventionWithUser(array('p.lead_id' => $lead_id));
    $lead['status']['stop_sending'] = $sentPreventRow ? true : false;
    $lead['status']['stop_sending_by_lend'] = !empty($sentPreventRow) && !empty($sentPreventRow['user_id']) ? true : false;
    // Commented due to serious performance implications. Used in Vue UI only.
       // $lead['status']['number_of_edits'] = $this->_checkNumberOfEdits($lead_id);
    $lead['status']['number_of_edits'] = 0;
    //$lead['status']['partner_honoured'] = $this->_checkPartnerHonoured($lead_id);
    
    $lead['status']['lend_status'] = $this->loadModel('LendStatuses')->getLendStatusById($lead['lead']['partner_status_id']);
    if($lead['lead']['man_status_id']){
      $lead['status']['man_status'] = TableRegistry::getTableLocator()->get('ManStatusEntity')->get($lead['lead']['man_status_id'])->toArray();
    }
    if (!empty($partner_user['partner_id'])) {
      $partner_feature = $this->loadModel('PartnerFeatureFlags')->getFeatureFlag(['partner_id' => $partner_user['partner_id']]);
      if ($partner_feature['allow_to_spray']) {
        $lead['status']['send_available'] = true;
      } else {
          $lead['status']['send_available'] = ($lead['lead']['send_type']=='Manual' AND !$lead['status']['stop_sending'] AND ($lead['status']['lend_status']['group_name'] == 'Pending' || (int)$lead['lead']['partner_status_id']==55 || $lead['status']['lend_status']['group_name'] == 'Rejected'));
      }
    }

    // Lender Preferences
    $lead['lender_preferences'] = $this->loadModel('LeadPreferredLender')->getLenderPreferencesInOrder($lead_id, true);
    $lead['all_available_lender_preferences'] = $this->_getAllAvailableLenderPreferences();
    // Entity Trust
    $lead['entity_trust'] = $this->loadModel('EntityTrust')->getEntityTrust(array('lead_id' => $lead_id));

    // Lead Quote
    $lead['quote_ref'] = $this->loadModel('LeadQuotesRef')->getQuoteRef(array('lead_id' => $lead_id));

    $quoteRefInfo = $this->loadModel('LeadQuotesRef')->getLeadQuoteRef(['lead_id' => $lead_id]);
    $lead['lead_quote_ref'] = (!empty($quoteRefInfo)) ? $quoteRefInfo : (object)null;
    // Partner Alias
    $lead['partner_alias'] = $this->_getPartnerAlias($lead['lead']['partner_alias_id']);

    list($sel_proposal, $quote) = $this->_getQuote($lead);

    $lead['selected_proposal'] = $sel_proposal;
    $lead['quote'] = $quote;

    $lead['lead_security'] = $this->_getAssetSecurity($lead_id);
    if (!empty($lead['lead']['account_id'])) {
      $lead['partner_account'] = TableRegistry::getTableLocator()->get('PartnerAccounts')->get(['partner_account_id' => $lead['lead']['account_id']], [
        'contain' => [
          'PartnerAccountMeta', 'partnerAccountMeta.ConfigIndustries', 'PartnerAccountPeople', 'PartnerAccountBs', 'LeadEntity'
        ]
      ]);
      $lead['bs_spa_link'] = getenv('DOMAIN_BS_SPA') . "?a=" . $lead['partner_account']['account_ref'] . "&l=" . $lead_id . "&c=" . getenv('REGION', true) . "&signature=" . CurlHelper::generateSignature(['a' => $lead['partner_account']['account_ref'], 'l' => $lead_id, 'c' => getenv('REGION', true)], getenv('BANKFEEDS_SECRET'));

    }

    $lead['manual_status_history'] = TableRegistry::getTableLocator()
    ->get('ManStatusHistoryEntity')
    ->find('all', [
      'contain' => ['ManStatusEntity', 'ManStatusEntity.ManStatusGroupEntity'],
      'conditions' => ['ManStatusHistoryEntity.lead_id' => $lead_id]
    ]);

    $lead['partner_commissions'] = $this->loadModel('PartnerCommissions')->getCommissionsDataForSettlement($lead_id);


    $lead = $this->__removeLeadId($lead);

    return $lead;
  }

    private function _getLeadAddresses($lead_id){
    $lead_addresses = $this->loadModel('LeadAddresses')->getLeadAddresses(array('lead_id' => $lead_id), array('lead_address_id' => 'asc'));
    return !empty($lead_addresses) ? $lead_addresses : [];
  }

    private function _getAllAvailableLenderPreferences(){

    $lenders = $this->loadModel('Lenders')->getLenders(['status' => 1], ['shorthand' => 'ASC']);
    $available_lenders = [];
    foreach ($lenders as $key => $lender) {
      // if($lender['notes'] != '**DO_NOT_SHOW_IN_LENDER_PREFS**' && $lender['shorthand'] != null && $lender['lender_logo'] != null)
      if ($lender['is_visible'] == 1 && $lender['shorthand'] != null && $lender['lender_logo'] != null)
          $available_lenders[$lender['lender_id']] = ['shorthand'=> $lender['shorthand'],
          'lender_logo' => $lender['lender_logo'],
          'manual_lender_leads_status' => $lender['manual_lender_leads_status']
        ];
    }

    return $available_lenders;
  }

  // Exclude file types for request documents
    private function filterSpecifics($data){
    $excludeTypes = ["Privacy Form (Electronic Signature)"];
    $result = array_filter($data, function ($k) use ($excludeTypes) {
      return !in_array($k, $excludeTypes);
    });
    return $result;
  }

    private function _formatDocumentTypes($data) {
    unset($data[array_search('Other', $data)]);
    usort($data, 'strnatcasecmp');
    array_push($data, 'Other');
    return $data;
  }

    public function _getOwnersMoreDetails($lead){
    $this->loadModel('LeadOwnerEmployment');
    $this->loadModel('LeadOwnerAddress');
    $this->loadModel('LeadOwners');
    $financeTable = $this->loadModel('LeadOwnerFinances');

    $owners = $lead['lead_owner'];
    $leadId = !empty($lead['lead_id']) ? $lead['lead_id'] : (!empty($lead['lead']['lead_id']) ? $lead['lead']['lead_id'] : null);
    if (empty($leadId)) {
      $leadId = !empty($lead['lead_ref'])
        ? (new LendInternalAuth)->unhashLeadId($lead['lead_ref'])
        : (!empty($lead['lead']['lead_ref']) ? (new LendInternalAuth)->unhashLeadId($lead['lead']['lead_ref']) : null);
    }
    foreach ($owners as $key => $o) {
      $asset_finance = false;
      $employmentNeeded = true;

        if ((!empty($lead['product_type']['product_type_id']) AND $lead['product_type']['product_type_id']==10)
          OR (!empty($lead['product_type']['sub_product']) AND $lead['product_type']['sub_product']==10)
          OR (!empty($lead['lead']['product_type_id']) && $lead['lead']['product_type_id'] ==10)
      ) {
        $employmentNeeded = false;
        $asset_finance = ['lead_assets' => [], 'lead_liabilities' => []];
        if (empty($lead['lead_assets']) && !empty($leadId)) {
          //try to fetch from db here
          $lead['lead_assets'] = $this->loadModel('LeadAssets')->getLeadAssets(array('lead_id' => $leadId));
        }
        if (empty($lead['lead_liabilities']) && !empty($leadId)) {
          //try to fetch from db here
          $lead['lead_liabilities'] = $this->loadModel('LeadLiabilities')->getLeadLiabilities(array('lead_id' => $leadId));

        }
        if (!empty($lead['lead_assets'])) {
          foreach ($lead['lead_assets'] as $assLiability) {
            if ($assLiability['lead_owner_id'] == $o['owner_id']) {
              $asset_finance['lead_assets'][] = $assLiability;
            }
          }
        }
        if (!empty($lead['lead_liabilities'])) {
          foreach ($lead['lead_liabilities'] as $assLiability) {
            if ($assLiability['lead_owner_id'] == $o['owner_id']) {
              $asset_finance['lead_liabilities'][] = $assLiability;
            }
          }
        }
      }

      $owners[$key]['finances'] = $financeTable->findByOwnerId($o['owner_id'])->first();
      $owners[$key]['employment'] = $this->LeadOwnerEmployment->getLeadOwnerEmployments(['lead_owner_id' => $o['owner_id'], 'status' => 'active']);
      $owners[$key]['addresses'] = $this->LeadOwnerAddress->getLeadOwnerAddresss(['lead_owner_id' => $o['owner_id'], 'status' => 'active']);
      $ownerPercentage = $this->LeadOwners->checkPercentage($owners[$key], $asset_finance, $employmentNeeded);

      $owners[$key]['percent_complete'] = $ownerPercentage['current_percentage'];
    }
    return $owners;
  }


    private function _getHistory($lead_id){
    $history = $this->loadModel('PartnerLeadHistory')->getPartnerLeadHistoryWithDetails(array('lead_id' => $lead_id), array('history_id' => 'asc'));
    return !empty($history) ? $history : [];
  }


    private function _getAddedBy($history){
    $added_by = [];
    if (empty($history)) return $added_by;

    foreach ($history as $h) {
      if ($h['history_detail'] == 'Lead Added') {
        $added_by = ['id' => $h['partner_user_id'], 'name' => $h['partner_user_name']];
        break;
      }
    }
    return $added_by;
  }

    private function _getCurrentUser($lead_id){
    $this->loadModel('PartnerUserLeads');
    $current_user = [];
    $currentUser = $this->PartnerUserLeads->getCurrentPartnerUser($lead_id);
    if (!empty($currentUser)) {
      foreach ($currentUser as $id => $user_name) {
        $current_user = ['id' => $id, 'name' => $user_name];
        break;
      }
    }
    return $current_user;
  }

  private function _getCurrentReferer($referrer_person_id){

    $ReferrerPeopleTable = TableRegistry::getTableLocator()->get('ReferrerPeople');
    $referrerPeople = $ReferrerPeopleTable->find()
        ->where(['id' => $referrer_person_id])
        ->toArray();
    
    if (empty($referrerPeople)) 
      return [];

    return $referrerPeople;
  }

  private function _getAllReferer() {
    $partner_user = $this->Auth->user();
    if (empty($partner_user)) {
      $partner_user = $this->Auth->identify();
    }

    $allReferrerPeople = [];
    if (!empty($partner_user['partner_id'])) {
      $partner_id = $partner_user['partner_id'];
      
      $ReferrerTable = TableRegistry::getTableLocator()->get('Referrers');
      $referrers = $ReferrerTable->find('all', [
          'contain' => ['ReferrerPeople'],
          'conditions' => ['Referrers.partner_id' => $partner_id]
      ])->toArray();
      
      foreach ($referrers as $referrer) {
          if (!empty($referrer->referrer_people)) {
              foreach ($referrer->referrer_people as $referrerPerson) {
                  $allReferrerPeople[] = $referrerPerson;
              }
          }
      }
    }
    return $allReferrerPeople;
}



  // Get previous sales history
    private function _getPrevSales($lead_id, $lead){
    $prev_sales = $this->_getWhoSent($this->loadModel('Sales')->getSalesWithLenderDetails($lead_id, false));

    return !empty($prev_sales) ? $prev_sales : [];
  }

    private function _getWhoSent($prev_sales){
    if (empty($prev_sales)) return $prev_sales;

    $this->loadModel('PartnerLeadHistory');

    // Get extra partner lead history and partner user details
    $partnerLeadHistoriesBySaleId = $this->PartnerLeadHistory->getPartnerLeadHistoryWithPartnerDetails(array_column($prev_sales, 'sale_id'));

    // If sender is found in partner lead history, the leads is sent via platform
    // otherwise sent by Star
    foreach ($prev_sales as $key => $sale) {
      if (!empty($prev_sales[$key]['off_panel_lender'])) $prev_sales[$key]['off_panel_lender'] = json_decode($prev_sales[$key]['off_panel_lender'], true);
      if (!empty($partnerLeadHistoriesBySaleId[$sale['sale_id']])) {
        $prev_sales[$key]['who_sent'] = ucwords($partnerLeadHistoriesBySaleId[$sale['sale_id']]['name']);
      } else {
        $prev_sales[$key]['who_sent'] = 'Lend';
      }
    }
    return !empty($prev_sales) ? $prev_sales : [];
  }


    public function _getBSPdfs($lead_id){
    $illion_pdfs = $this->loadModel('LeadUploads')->getLatestBankStatementPDF($lead_id, true, true);
    return !empty($illion_pdfs) ? $illion_pdfs : [];
  }

    public function _getCwCreditReports($lead_id) {
    $cw_reports = $this->loadModel('PartnerCrbCwReports')->getPartnerCrbCwReports(array('lead_id' => $lead_id));
    return !empty($cw_reports) ? $cw_reports : [];
  }

    public function _getEfCreditReports($lead_id){
    $ef_reports = $this->loadModel('PartnerCrbEfReports')->getPartnerCrbEfReports(array('lead_id' => $lead_id, 'status !=' => 'Deleted'));
    return !empty($ef_reports) ? $ef_reports : [];
  }

    private function _getRequested($lead_id){
    $partner_lead_uploads_requested = $this->loadModel('LeadUploadsRequested')->getRequested($lead_id);
    return !empty($partner_lead_uploads_requested) ? $partner_lead_uploads_requested : [];
  }

    private function _getRequestedDetails($requested, $leadRef){
    $ownerIds = [];
    foreach ($requested as $request) {
      $ownerIds[] = $request['owner_id'];
    }

    $ownerIds = array_unique(($ownerIds));

    if ($ownerIds) {
      $partner_lead_uploads_requested_details = $this->loadModel('LeadUploadsRequestedDetails')->getRequested($leadRef, $ownerIds);
    }

    return !empty($partner_lead_uploads_requested_details) ? $partner_lead_uploads_requested_details : [];
  }


  // BS status
    private function _getBSStatus($lead_id, $lead){
    $lead_activities = [];
    $bs_status = false;
    $bs_summary = [];
    if (empty($lead['bs_accounts'])) {
      $lead_activities = $this->loadModel('Leads')->activity->getActivities(array('lead_id' => $lead_id), array('lead_activity_id' => 'desc'));
    } else {
      $bs_statuses = array_column($lead['bs_accounts'], 'retrieved');
      $bs_status = 'Ready to retrieve.';
      $bankfeeds_errors = Configure::read('Lend.BankFeedsErros');
      foreach ($bs_statuses as $s) {
        // Map BankFeeds status after retrieving:
        switch ((int)$s) {
            case 0: $bs_status = 'The client has provided their bank statements, and they are now being processed by Illion. This can take up to an hour, but normally takes 5-10 minutes.'; break 2;
            case 1: $bs_status = 'Success';
            // BS Summary
            if ($bsa = $lead['bank_statements_analysis']) {
              $bs_summary['avg_monthly_rev_180']     = $bsa['avg_mto_180'];
              $bs_summary['avg_monthly_rev_dep_180'] = $bsa['avg_num_mth_deps_180'];
              $bs_summary['avg_day_end_bal_180']     = $bsa['avg_day_end_bal_180'];
              $bs_summary['days_neg_180']            = $bsa['days_neg_180'];
              $bs_summary['dishonours_180']          = $bsa['days_dishonour_cnt_180'];
              $bs_summary['is_account_level']        = $bsa['is_account_level'];
              $bs_summary['cfl_detected']            = $bsa['total_lenders'] > 0 ? true : false;
            }
            break 2;
            case 2: $bs_status = 'In progress.'; break 2;
            case 5: $bs_status = 'Client uploaded an account, but it was not transaction account.'; break 2;
          default:
            if (in_array((int)$s, array_keys($bankfeeds_errors))) {
              $bs_status = $bankfeeds_errors[$bs_accounts[0]['retrieved']];
            } else {
              $bs_status = 'There was an error while retrieving.';
            }
            break 2;
        }
      }
      if($bs_status != "Success" && !empty($lead['bs_accounts'][0]['illion_error'])){
        $bs_status = 'Unfortunately Illion has returned the following the error. <br>
        We understand that not all error messages are helpful, but they can often help resolve issues.<br>
        <b>Error code:</b> '.$lead['bs_accounts'][0]['illion_error']['error_code'].' <b>Error message:</b> '.$lead['bs_accounts'][0]['illion_error']['error_message'];
      }
    }
    return [$lead_activities, $bs_status, $bs_summary];
  }

    private function _updateLeadAddresses($addresses, $lead_id){
    $this->loadModel('LeadAddresses');
    foreach ($addresses as $key => $address) {

      if (empty($address['lead_address_id'])) { // Add new item
        $addresses[$key]['lead_id'] = $lead_id;
        $addresses[$key]['lead_address_id'] = $this->LeadAddresses->addLeadAddress($addresses[$key]);
        // $this->loadModel('Leads')->updateLead(['lead_id'=>$address['lead_id'], 'mailing_address_id'=>$address['lead_address_id']]);
      } else { // Update existing one
        $this->LeadAddresses->updateLeadAddress($address);
      }
      if($address['address_type'] == 'trading' && !$address['date_to']){
        $this->Leads->updateLead([
          'lead_id' => $lead_id,
          'b_address' => $address['address'],
          "b_suburb" => $address['suburb'],
          "b_state" => $address['state'],
          "b_postcode" => $address['postcode'],
          "b_country" => $address['country'],
        ]);
      }
    }
    return $addresses;
  }


    private function _getLendScore($lead_id){
    $lend_score = $this->loadModel('LendScore')->getLendScore(array('lead_id' => $lead_id));

      if(!empty($lend_score['total']) AND $lend_score['total'] < 0) $lend_score['total'] = 0;

    return $lend_score !== false ? $lend_score : (object)null;
  }


    private function _getLeadAssetFinance($lead_id){
    $lead_asset_finance = $this->loadModel('LeadAssetFinance')->getLeadAssetFinanceByLeadID($lead_id);
    return $lead_asset_finance;
  }


    private function _getLeadAssets($lead_id){
    $lead_assets = $this->loadModel('LeadAssets')->getLeadAssets(['lead_id' => $lead_id]);
    return !empty($lead_assets) ? $lead_assets : [];
  }


  // Check already sent to lender && rejected or not
  private function _checkReadOnly($lead_id, $lead){
    $partner_user = $this->Auth->identify();
    if(!empty($partner_user['status_system']) && $partner_user['status_system'] == 'manual'){
      return false;
    }
    
    $readonly = false;
    if ($this->loadModel('Sales')->getSales(array('lead_id'=>$lead_id))
      && !in_array($this->loadModel('LendStatuses')->getLendStatusesOldFashion()[$lead['lead']['partner_status_id']]['groupName'], ['Rejected', 'Pending'])
        && (empty($partner_user) || $partner_user['account_type'] !== 'Lend Staff') ) {
      $readonly = true;
    }
    return $readonly;
  }


  // Check number of edits of the lead
    private function _checkNumberOfEdits($lead_id){
    $partner_lead_edits = $this->loadModel('PartnerLeadEdits')->getPartnerLeadEdits(['lead_id' => $lead_id]);
    return count($partner_lead_edits);
  }


  // Check partner honoured returning true or false
    private function _checkPartnerHonoured($lead_id){
    $partner_honoured = $this->loadModel('PartnerHonoured')->existingRecord(['partner_lead_id' => $lead_id]);
    return !empty($partner_honoured);
  }


    private function _getPartnerUsers(){
    $partner_user = $this->Auth->identify();
    $partner_users = (!empty($partner_user['account_admin']) || !empty($partner_user['permission_edit_assignee'])) ? $this->loadModel('PartnerUsers')->getPartnerUser(['partner_id' => $partner_user['partner_id']], true) : [];
    if (!empty($partner_users)) {
      foreach ($partner_users as $key => $pu) {
        unset($partner_users[$key]['password']);
      }
    }
    return $partner_users;
  }


    private function _getPartnerAliases($partner_id){
    if ($partner_id == null) return [];

    $partner_aliases = $this->loadModel('PartnerAliases')->getPartnerAliases(array('partner_id' => $partner_id));
    return $partner_aliases;
  }

    private function _getPartnerAlias($partner_alias_id){
    $result = new \stdClass();
    $result->partner_alias_id = $partner_alias_id;
    $result->partner_alias_name = 'None';
    $result->partner_alias_status = 'None';

    if ($partner_alias_id == null) return $result;

    $partner_alias = $this->loadModel('PartnerAliases')->getPartnerAlias(array('partner_alias_id' => $partner_alias_id));
    $result->partner_alias_name = $partner_alias['company_name'];
    $result->partner_alias_status = $partner_alias['status'];

    return $result;
  }

  // Remove lead_id from the results
    protected function __removeLeadId($payload){
    foreach ($payload as $data_source => $data) {
      if (!is_array($data)) continue;
      if (count($data) !== count($data, 1)) // multidimentional array check
        $payload[$data_source] = $this->__removeLeadId($data);

      if (array_key_exists('lead_id', $data)) unset($payload[$data_source]['lead_id']);
    }
    return $payload;
  }

    public function getHighlightedProducts() {
    ConnectionManager::alias('reader_db', 'default');
    $leadRef = $this->request->getData()['lead_ref'];
    $leadId = $this->loadModel('Leads')->getLead(['lead_ref' => $leadRef])['lead_id'];
    $highlights = $this->loadModel('AssetCalcProductHighlights')->getMatchingHighlights($leadId);
    ConnectionManager::dropAlias('default');
    return $this->setJsonResponse($highlights);

  }

    public function getSettlementDetails() {
        $leadRef = $this->request->getData()['lead_ref'];
        $leadId = $this->loadModel('Leads')->getLead(['lead_ref'=>$leadRef])['lead_id'];
        $commissions = $this->loadModel('PartnerCommissions')->getCommissionsDataForSettlement($leadId);
        $settlementReviews = $this->loadModel('SettlementReviews')->getSettlementsForLead($leadId);
        foreach ($settlementReviews as $index => $settlementReview) {
            unset($settlementReviews[$index]['lead_id']);
        }
        return $this->setJsonResponse([
            'commissions' => $commissions,
            'settlements_reviews' => $settlementReviews,
        ]);
    }

    public function saveSettlementReviews()
    {
        $data = $this->request->getData();

        if (!$this->request->is('post')) {
            throw new \Exception("POST request only available.");
        }

        $db = ConnectionManager::get('default');
        $collection = $db->getSchemaCollection();
        $tableSchema = $collection->describe('settlement_reviews');
        $columns = $tableSchema->columns();

        $lead_id = (new LendInternalAuth)->unhashLeadId($data['lead_ref']);
        $data['lead_id'] = $lead_id;

        foreach ($data as $key => $value) {
            if (!in_array($key, $columns)) {
                unset($data[$key]);
                continue;
            }
            if ($value === false) {
                $data[$key] = 0;
            }
            if ($value === true) {
                $data[$key] = 1;
            }
        }
        if (isset($data['settlement_review_id'])) {
            $this
              ->loadModel('SettlementReviews')
              ->update($data['settlement_review_id'], $data);
        } else {
            $newSettlementId = $this->loadModel('SettlementReviews')->add($data);
            $data['settlement_review_id'] = $newSettlementId;
        }

        unset($data['lead_id']);

        return $this->setJsonResponse(['success' => true, 'data' => $data]);
    }

    /* =============================================================
    * Update function
    * ============================================================== */
    public function update($lead_ref=false){

    try {
      $db = ConnectionManager::get('default');
      $db->execute("SET @disable_trigger=true;");
      $partner_user = $this->Auth->identify();
      if (empty($lead_ref)) throw new \Exception("Missing lead ref.");

      $lead_id = (new LendInternalAuth)->unhashLeadId($lead_ref);
      if (!$this->request->is('post')) throw new \Exception("POST request only available.");

      $data = $this->request->getData();
      // =========   address related modifications START =================

      $users_country = strtolower(getenv('REGION', true));
      
      if($users_country == 'nz'){
        $validate_address_comp = ['address','suburb', 'postcode','city'];
      }else{
        $validate_address_comp = ['address','suburb', 'postcode','state'];
      }

      //trading address if end to end  * this is multiple //
      if (!empty($data['lead_addresses'])) {
        foreach($data['lead_addresses'] as $key => $modified_address){
          if($modified_address['lead_address_id']){
            $existing_lead_address_record = TableRegistry::getTableLocator()->get('LeadAddressEntity')->get($modified_address['lead_address_id']);
            foreach ($validate_address_comp as $validate_key) {
              if (empty($modified_address[$validate_key])) {
                  $modified_address[$validate_key] = $existing_lead_address_record->$validate_key;
              }
            }
          }
          if(!empty($data['lead_addresses'][$key]['address']) || !empty($data['lead_addresses'][$key]['suburb'])
          || !empty($data['lead_addresses'][$key]['postcode']) || !empty($data['lead_addresses'][$key]['state'])
          || !empty($data['lead_addresses'][$key]['city']))
          {
            $addressFeedback = $this->AddressValidator->checkAddress($modified_address);
            //add cake PHP log 
            Log::write("debug", "trading address OR mailing address=======".json_encode($addressFeedback));
            if(isset($addressFeedback['error'])) {
              return $this->setJsonResponse(['success' => false, 'message' => $addressFeedback['error']]);
            } else {
              $data['lead_addresses'][$key]['street_number'] = $addressFeedback['street_number'];
              $data['lead_addresses'][$key]['street_name'] = $addressFeedback['street_name'];
            }
          }
        }
      }

      $original = $this->loadModel('Leads')->getLead(['lead_id' => $lead_id]);

      //---------REGISTER Address----------------------------------//
      $registerd_addresses['address']  = $data['lead']['r_address'];
      $registerd_addresses['suburb']   = $data['lead']['r_suburb'];
      $registerd_addresses['postcode'] = $data['lead']['r_postcode'];
    
      if($users_country == 'nz'){
        $registerd_addresses['city']    = $data['lead']['r_city'];
        $validate_address_comp = ['r_address','r_suburb', 'r_postcode','r_city'];
      }else{
        $registerd_addresses['state']    = $data['lead']['r_state'];
        $validate_address_comp = ['r_address','r_suburb', 'r_postcode','r_state'];
      }

      if(!empty($registerd_addresses['address']) || !empty($registerd_addresses['suburb']) 
      || !empty($registerd_addresses['postcode']) || !empty($registerd_addresses['state'])
      || !empty($registerd_addresses['city']))
      {
        foreach ($validate_address_comp as $key) {
          $registerd_key = substr($key, 2); // Remove the "r_" prefix
          if (empty($registerd_addresses[$registerd_key]) && isset($original[$key])) {
              $registerd_addresses[$registerd_key] = $original[$key];
          }
        }
        if(!empty($registerd_addresses)){
          $addressFeedback = $this->AddressValidator->checkAddress($registerd_addresses);
          Log::write("debug", "registerd address=======".json_encode($addressFeedback));
            if(isset($addressFeedback['error'])) {
              return $this->setJsonResponse(['success' => false, 'message' => $addressFeedback['error']]);
          } else {
              $data['lead']['r_street_number'] = $addressFeedback['street_number'];
              $data['lead']['r_street_name']   = $addressFeedback['street_name'];
          }
        }
      }
      //---------BUSINESS Address----------------------------------//

      $business_addresses['address']  = $data['lead']['b_address'];
      $business_addresses['suburb']   = $data['lead']['b_suburb'];
      $business_addresses['postcode'] = $data['lead']['b_postcode'];

      if($users_country == 'nz'){
        $business_addresses['city']    = $data['lead']['b_city'];
        $validate_address_comp = ['b_address','b_suburb', 'b_postcode','b_city'];
      }else{
        $business_addresses['state']    = $data['lead']['b_state'];
        $validate_address_comp = ['b_address','b_suburb', 'b_postcode','b_state'];
      }
      

      // this is from manual address if not from payload //
      if(!empty($business_addresses['address']) || !empty($business_addresses['suburb']) 
          || !empty($business_addresses['postcode']) || !empty($business_addresses['state'])
          || !empty($business_addresses['city'])){
          foreach ($validate_address_comp as $key) {
            $business_key = substr($key, 2); // Remove the "b_" prefix
            if (empty($business_addresses[$business_key]) && isset($original[$key])) {
                $business_addresses[$business_key] = $original[$key];
            }
          }
          $addressFeedback = $this->AddressValidator->checkAddress($business_addresses);
          Log::write("debug", "business address=======".json_encode($addressFeedback));
          if(isset($addressFeedback['error'])) {
            return $this->setJsonResponse(['success' => false, 'message' => $addressFeedback['error']]);
          } else {
            $data['lead']['b_street_number'] = $addressFeedback['street_number'];
            $data['lead']['b_street_name']   = $addressFeedback['street_name'];
          }
      }

      // =========   address related modifications END =================


      // Force to turn call_me on when update to end-2-end
      if (!empty($data['lead_asset_finance']['contract_type'])) $data['lead']['call_me_first'] = 1;

      if (isset($data['abnlookup_force_add']['acn']) && !empty($data['abnlookup_force_add']['acn']) && !empty($data['lead']) && empty($data['lead']['acn'])) {
        $data['lead']['acn'] = $data['abnlookup_force_add']['acn'];
      }
        if(!empty($data['lead']) OR !empty($data['lead_owner']) OR !empty($data['lead_asset_finance'])) $data = $this->_formatLeadData($data);
      $data = $this->_addLeadIdIntoPayload($data, $lead_id);
      $data = $this->_filterNotAllowedFields($data);


      $original_owners = $this->loadModel('LeadOwners')->getLeadOwners(['lead_id' => $lead_id]);
      if ($original['partner_id'] == 2) throw new \Exception("Not allowed to create leads or update leads for DEMO account.");

      $permission_check = $this->checkPermission($lead_id, $original, $partner_user['account_type'] === 'Applicant');
      if (!$permission_check['success']) throw new \Exception($permission_check['message']);

      $this->_dupeCheck($data, $original, $original_owners);
      $this->_mobileCheck($data);
      if (isset($data['referrer_person_id'])) {
        $data['lead']['referrer_person_id'] = $data['referrer_person_id'];
        $data['lead']['lead_id'] = $lead_id;
        unset($data['referrer_person_id']);
      }
      if (!empty($data['lead'])) {
        $this->_updateLead($data, $original);
      }
      if (!empty($data['lead_owner'])) {
        $data['lead_owner']             = $this->_updateLeadOwners($data['lead_owner']);
        if (!empty($data['created_by']))
          $this->_noticeBrokerNewApplicantAdded($original, current($data['lead_owner']));
      }

      if (isset($data['lead_asset_finance'])) {
        if (!$this->validateLeadAssetFinance($data['lead_asset_finance']))
          throw new \Exception('Invalid lead asset finance');
      }

      $data['lead_security'] = $this->_saveAssetSecurity($lead_id);

      if (!empty($data['abnlookup_force_add']))    $data['abnlookup_force_add']    = $this->_updateAbnLookup($data['abnlookup_force_add']);
      if (!empty($data['partner_lead_uploads']))   $data['partner_lead_uploads']   = $this->_updatePartnerLeadUploads($data['partner_lead_uploads']);
      if (!empty($data['lead_asset_finance']))     $data['lead_asset_finance']     = $this->_updateLeadAssetFinance($data['lead_asset_finance']);
      if (!empty($data['lead_assets']))            $data['lead_assets']            = $this->_updateLeadAssets($data['lead_assets']);
      if (!empty($data['lead_liabilities']))       $data['lead_liabilities']       = $this->_updateLeadLiabilities($data['lead_liabilities']);
      if (!empty($data['lead_references']))        $data['lead_references']        = $this->_updateLeadReferences($data['lead_references']);
      if (!empty($data['partner_lead_uploads_requested']))                $data['partner_lead_uploads_requested']         = $this->_updateLeadUploadsRequested($data['partner_lead_uploads_requested']);
      if (!empty($data['partner_lead_uploads_requested_details']))        $data['partner_lead_uploads_requested_details'] = $this->_updateLeadUploadsDetailsRequested([$data['partner_lead_uploads_requested_details']]);
      if (!empty($data['bs_accounts']))            $this->_updateBSAccounts($data['bs_accounts'], $lead_id);
      if (!empty($data['status']))                 $this->_updateStatus($data['status'], $original, $partner_user);
      if (!empty($data['current_user']))           $this->_reassign($data['current_user'], $partner_user);
      if (!empty($data['update_lender_preferences']))  $this->_updateLenderPreferences($lead_id, $data['update_lender_preferences']);
      if (!empty($data['entity_trust']))           $data['entity_trust']           = $this->_updateEntityTrust($data['entity_trust'], $lead_id);
      // if(!empty($data['lead_notes']))             $data['lead_notes']             = $this->_updateLeadNotes($data['lead_notes']);
      // if(!empty($data['sms_communications']))     $data['sms_communications']     = $this->_updateSmsCommunications($data['sms_communications']);
      // if(!empty($data['tasks']))                  $data['tasks']                  = $this->_updateTasks($data['tasks']);

      if (!empty($data['lead_addresses'])) {
        $data['lead_addresses'] = $this->_updateLeadAddresses($data['lead_addresses'], $lead_id);
        // $data['lead']['mailing_address_id'] = $data['lead_addresses']['lead_address_id'];
      }
      $db->execute("SET @disable_trigger=false;");

      if (!empty($data['partner_lead_uploads_requested'])) {
        $notification_data = [
          'ownerIds' => array_unique(array_column($data['partner_lead_uploads_requested'], 'owner_id')),
          'is_attach' => true,
          'lead_ref' => $lead_ref,
          'message' => @$data['partner_lead_uploads_requested_details'][0]['notes'],
        ];
        if (!empty($notification_data['ownerIds'])) {
          $this->sendLead($notification_data);
        }
      }

      // Save lead edits
      $this->_saveAnEdit($lead_id, ['lead' => $original, 'lead_owner' => $original_owners], $data);

      // Once update ABN, assign to right account
      if (!empty($data['abnlookup_force_add'])) {
        // Serch abn in account level
        $partnerAccountTable = TableRegistry::getTableLocator()->get('PartnerAccountEntity');
        $search_account = $partnerAccountTable->find('all')
        ->where([
          'partner_id' => $original['partner_id'],
          'abn' => $data['abnlookup_force_add']['abn']
        ])->first();
        // If exists, replace account_id for this lead.
        // Otherwise, create new account then sync from lead to account
        $leadTable = TableRegistry::getTableLocator()->get('LeadEntity');
        if (!empty($search_account)) {
          $lead = $leadTable->get($lead_id);
          $leadTable->patchEntity($lead, [
            'account_id' => $search_account->partner_account_id
          ]);
          $leadTable->save($lead);
        } else {
          // get current account details:
          $current_account = $partnerAccountTable->get($original['account_id']);
          // if current account doesn't set ABN yet, sync all from lead to account.
          // else if new ABN is NOT same as current one, create new one then sync all from lead to account.
          // Otherwise, leave it as it.
          if (empty($current_account->abn)) {
            $this->syncToAccount($lead_id);
          } elseif ($current_account->abn !== $data['abnlookup_force_add']['abn']) {
            // create new account:
            $new_account = $partnerAccountTable->newEntity([
              'partner_id' => $original['partner_id'],
              'partner_user_id' => @$partner_user['partner_user_id'],
              'abn' => $data['abnlookup_force_add']['abn']
            ]);
            $partnerAccountTable->save($new_account);
            // assign new account_id to this lead:
            $lead = $leadTable->get($lead_id);
            $leadTable->patchEntity($lead, [
              'account_id' => $new_account->partner_account_id
            ]);
            $leadTable->save($lead);
            // sync:
            $this->syncToAccount($lead_id);
          }
        }
      }

      if(!$partner_user OR $partner_user['account_type']==='Applicant') $this->_sendNotification($data, $original);

      $data = $this->__removeLeadId($data);

      // avatar image url //
      $lead_poc_owner = $this->getTableLocator()->get('LeadOwnersEntity')->find('all')->where(['lead_id' => $lead_id, 'point_of_contact' => true])->first();
      if(!empty($lead_poc_owner['email']) &&  empty($lead_poc_owner['avatar_image_url'])){
        $avatar_image_url = $this->AvatarAPI->getImageURL(trim($lead_poc_owner['email']));
        $this->getTableLocator()->get('LeadOwnersEntity')->updateAll(['avatar_image_url' => $avatar_image_url], ['owner_id' => $lead_poc_owner['owner_id']]);
      }
      
      //PLAT-4052 Logic //
      $call_state ='';

      $lead_table = TableRegistry::getTableLocator()->get('LeadEntity');
      $associated = [
          'Owners',
          'PocOwner',
          'PocOwner.OwnerAllEmployments',
          'PocOwner.OwnerAllAddresses',
          'AllAddresses'
      ];
      $lead = $lead_table->get($lead_id, [
          'contain' => $associated
      ]);
      
      $check_state = $lead->toArray();
      $call_state = '';
      
      // Search for the trading state first
      foreach ($check_state['all_addresses'] as $lead_address) {
          if (empty($lead_address['date_to']) && $lead_address['address_type'] == 'trading') {
              $call_state = $lead_address['state'];
              break;
          }
      }
      // If no trading state, use r_state
      if (!$call_state) {
          $call_state = $check_state['r_state'];
      }
      // If no r_state, search for mailing state
      if (!$call_state) {
          foreach ($check_state['all_addresses'] as $lead_address) {
              if ($lead_address['address_type'] == 'mailing') {
                  $call_state = $lead_address['state'];
                  break;
              }
          }
      }
      // If no mailing state, check owner_poc's addresses
      if (!$call_state) {
          foreach ($check_state['owner_poc']['all_addresses'] as $owner_poc_address) {
              if (empty($owner_poc_address['date_to'])) {
                  $call_state = $owner_poc_address['state'];
                  break;
              }
          }
      }
      $update_lead = $this->getTableLocator()->get('LeadEntity')->query()
      ->update()
      ->set(['call_state' => $call_state])
      ->where(['lead_id' => $lead_id])
      ->execute();



      return $this->setJsonResponse(['success' => true, 'message' => '', 'data' => $data]);
    } catch (\Exception $e) {
      if (!in_array($e->getMessage(), ['Missing lead ref.', 'POST request only available.', 'Not allowed to create leads or update leads for DEMO account.'])) {
        Log::error($e->getMessage());
        Log::error($e->getTraceAsString());
      }
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()]);
    }
  }

  /**
   * Add new lead and respective entry into lead_quote_ref
   */
  public function addLeads()
  {
    try {
      $internalAuth   = new LendInternalAuth;
      $results = array();
      $leadData = $this->request->getData();

      $partnerUser = $this->Auth->identify();
      if (isset($partnerUser['partner_id'])) {
        $leadData['lead']['source'] = 'Partners';
        $leadData['lead']['partner_id'] = $partnerUser['partner_id'];
        $leadData['lead']['partner_status_id'] = 1;
        $leadData['lead']['status_id'] = 1;
      } else {
        throw new \Exception('You are not authorised to access.');
      }

      if (!$this->request->is('post'))
        throw new MethodNotAllowedException("Invalid Request Method.");

      if (empty($leadData))
        throw new NotFoundException("Data not provided");


      $formatLeadInfo = $this->_organiseLeadInfo($leadData);
      if (isset($formatLeadInfo['lead'])) {
        $results['lead_id'] = $this->loadModel('Leads')->addLead($formatLeadInfo['lead']);
      }

      if (!isset($results['lead_id'])) {
        throw new \Exception('Lead has not saved.');
      }

      $this->loadModel('PartnerLeadHistory')->addPartnerLeadHistory(
                                                        array('partner_id'=> $partnerUser['partner_id'],
          'partner_user_id' => $partnerUser['partner_user_id'],
          'lead_id' => $results['lead_id'],
                                                               'history_detail'=>'Lead Added'));

      $partnerUserLeadsTable = TableRegistry::getTableLocator()->get('PartnerUserLeadsEntity');
      $partnerUserLead = $partnerUserLeadsTable->newEntity([
        'partner_user_id' => $partnerUser['partner_user_id'],
        'status' => 'ACCESS',
        'lead_id' => $results['lead_id']
      ]);
      $partnerUserLeadsTable->save($partnerUserLead);

      if (isset($formatLeadInfo['quote_ref'])) {
        $formatLeadInfo['quote_ref']['lead_id'] = $results['lead_id'];
        $results['quote_ref_id'] = $this->loadModel('LeadQuotesRef')->addLeadQuoteRef($formatLeadInfo['quote_ref']);
      }

      if (isset($formatLeadInfo['lead_owners'])) {
        $formatLeadInfo['lead_owners']['lead_id'] = $results['lead_id'];
        $results['lead_owner_id'] = $this->loadModel('LeadOwners')->addLeadOwner($formatLeadInfo['lead_owners']);
      }

      if (isset($formatLeadInfo['abn_lookup'])) {
        $formatLeadInfo['abn_lookup']['lead_id'] = $results['lead_id'];
        $results['abn_lookup_id'] = $this->loadModel('LeadAbnLookup')->addLookup($formatLeadInfo['abn_lookup']);
      }

      if (isset($formatLeadInfo['asset_details'])) {
        $formatLeadInfo['asset_details']['lead_id'] = $results['lead_id'];
        $results['asset_finance_id'] = $this->loadModel('LeadAssetFinance')->addLeadAssetFinance($formatLeadInfo['asset_details']);
      }

      $results['lead_ref'] = $internalAuth->hashLeadId($results['lead_id']);


      return $this->setJsonResponse(['success' => true, 'message' => 'Record saved', 'data' => $results]);
    } catch (\Exception $e) {
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()]);
    }

  }

  /**
   * POST Endpoint - /lead-apis/check-bulk-request
   * Bulk-Request Pre-Check
   * return - JSON payload
   */
    public function checkBulkRequest(){
    try {
      if (!$this->request->is('post'))
        throw new MethodNotAllowedException("Invalid Request Method.");

      $request_types = ['Ask Applicants' => 'askApplicants'];

      $data = $this->request->getData();
      $success = $this->bulkRequestFormatCheck($data, $request_types);
      if (!$success['success']) return $this->setJsonResponse(['success' => false, 'message' => $success['message']]);

      $lead_refs = array_unique($data['lead_refs']);
      $last_7_days = [];
      $warnings = new \stdClass;

      // get lead owners detail:
      $lead_ids = [];
      foreach ($lead_refs as $lead_ref) {
        $lead_ids[] = (new LendInternalAuth)->unhashLeadId($lead_ref);
      }
      $owners = $this->loadModel('LeadOwners')->getLeadOwnersByLeadIds($lead_ids);
      foreach ($owners as $lead_owner) {
        $lead_ref = $lead_refs[array_search($lead_owner['lead_id'], $lead_ids)];
        $details = implode(' ', [@$lead_owner['first_name'], @$lead_owner['middle_name'], @$lead_owner['last_name']]);
          if(!empty($data['tasks']['application']) AND !empty($lead_owner['last_asked_application']) AND strtotime('-7 days')<=strtotime($lead_owner['last_asked_application'])){
          array_push($last_7_days, ['lead_ref' => $lead_ref, 'details' => $details]);
          }elseif(!empty($data['tasks']['consent']) AND !empty($lead_owner['last_asked_consent']) AND strtotime('-7 days')<=strtotime($lead_owner['last_asked_consent'])){
          array_push($last_7_days, ['lead_ref' => $lead_ref, 'details' => $details]);
          }elseif(!empty($data['tasks']['bs']) AND !empty($lead_owner['last_asked_bs']) AND strtotime('-7 days')<=strtotime($lead_owner['last_asked_bs'])){
          array_push($last_7_days, ['lead_ref' => $lead_ref, 'details' => $details]);
        }
      }

      if (count($last_7_days) > 0)
        $warnings = ['last_7_days' => $last_7_days];

      return $this->setJsonResponse(['success' => true, 'warnings' => $warnings]);

    } catch (\Exception $e) {
      Log::error($e->getMessage());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()]);
    }
  }

  /**
   * POST Endpoint - /lead-apis/bulk-request
   * The Actual Bulk-Request
   * return - JSON payload
   */
    public function bulkRequest(){
    try {
      if (!$this->request->is('post'))
        throw new MethodNotAllowedException("Invalid Request Method.");

      $request_types = ['Ask Applicants' => 'askApplicants', 'Archiving Leads' => 'bulkRequestArchive', 'Unarchiving Leads' => 'bulkRequestUnarchive'];

      $data = $this->request->getData();
      $success = $this->bulkRequestFormatCheck($data, $request_types);
      if (!$success['success']) return $this->setJsonResponse(['success' => false, 'message' => $success['message']]);

      $lead_refs = array_unique($data['lead_refs']);

      if ($data['bulk_request_type'] == 'Archiving Leads') {
        $this->loadModel('Leads')->archiveBulkLeads($lead_refs);
        }
        elseif($data['bulk_request_type'] == 'Unarchiving Leads'){
        $this->loadModel('Leads')->unArchiveBulkLeads($lead_refs);
        }
        else{
        foreach ($lead_refs as $lead_ref) {
          $lead_id = (new LendInternalAuth)->unhashLeadId($lead_ref);
          $this->loadModel('BackgroundJobs')->addBackgroundJob(array(
            'lead_id'       => $lead_id,
            'job_type'      => $request_types[$data['bulk_request_type']],
            'ref_id'        => ($data['bulk_request_type'] == 'Ask Applicants' ? json_encode(['tasks' => $data['tasks']]) : $lead_ref),
            'class_name'    => 'BulkRequest',
            'function_name' => $request_types[$data['bulk_request_type']],
            'created'       => date('Y-m-d H:i:s', time()),
          ));
        }
      }

      return $this->setJsonResponse(['success' => true]);

    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()]);
    }
  }

    private function bulkRequestFormatCheck($data, $request_types){
    if (!array_key_exists('bulk_request_type', $data) || !array_key_exists('lead_refs', $data))
      return ['success' => false, 'message' => 'Bulk-Request format incorrect'];

    if (!array_key_exists($data['bulk_request_type'], $request_types)) return ['success' => false, 'message' => 'Bulk-Request Type does not exist'];

    if (!is_array($data['lead_refs']))
      return ['success' => false, 'message' => 'Lead Refs format incorrect'];
    if (count($data['lead_refs']) > 100)
      return ['success' => false, 'message' => 'Too many lead refs, you can request a maximum of 100'];

    return ['success' => true];
  }

  /**
   * POST API end point - Send emails with quote link attached
   *
   * return - JSON payload
   */
    public function sendQuoteEmail(){
    try {
      $this->loadModel('LeadOwners');
      $internal_auth = new LendInternalAuth;
      if (!$this->request->is('post'))
        throw new MethodNotAllowedException("Invalid Request Method.");

      $data = $this->request->getData();
      $this->_validateSendQuoteEmails($data);

      $leadId = $this->loadModel('LeadQuotesRef')->getLeadIdByQuoteRef($data['quote_ref']);

      if (!isset($leadId[0]['lead_id']))
        throw new \Exception("Invalid property value: quote_ref");

      $invalidEmails = array();
      foreach ($data['owners'] as $owner_id) {
        $leadOwner = $this->LeadOwners->getLeadOwner(['owner_id' => $owner_id]);
        $applicant = $internal_auth->hashLeadId($leadOwner['owner_id']);

        $additionalCode = [];
        $additionalCode['applicant'] = $applicant;
        if (!empty($leadOwner['first_name'])) $additionalCode['first_name'] = $leadOwner['first_name'];
        if (!empty($leadOwner['last_name'])) $additionalCode['last_name'] = $leadOwner['last_name'];

        $adhocData = array(
          'additionalCode' => http_build_query($additionalCode) . '#quoteRef=' . $data['quote_ref'],
          'client_first_name' => $leadOwner['first_name'],
          'to' => [$leadOwner['email']],
        );

        $this->LoadModel('PartnerNotifications')->sendPartnerNotifications(
          $this->Auth->user('partner_id'),
          'QuoteLink',
          $leadId[0]['lead_id'],
          $adhocData,
          null,
          null
        );
      }

      if (count($invalidEmails) > 0) {
        return $this->setJsonResponse(['success' => true, 'message' => 'Email(s) Sent Successful', 'failedToSend' => $invalidEmails]);
      } else {
        return $this->setJsonResponse(['success' => true, 'message' => 'Email(s) Sent Successful',]);
      }


    } catch (\Exception $e) {
      Log::error($e->getMessage());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()]);
    }
  }

  /**
   * Delete Lead owner - set status to inactive
   *
   * @param string $ownerId
   * @return void - JSON response
   */

    public function deleteApplicant($ownerId=''){
    try {
      if (!$this->request->is('delete'))
        throw new MethodNotAllowedException("Invalid Request Method.");

      if (empty($ownerId) || !is_numeric($ownerId))
        throw new \Exception("Invalid value: owner_id");

      $this->loadModel('LeadOwners');

      $partner_user = $this->Auth->identify();
      $owner = $this->LeadOwners->getLeadOwner(['owner_id' => $ownerId]);
      $permission_check = $this->checkPermission($owner['lead_id'], false, $partner_user['account_type'] === 'Applicant');
      if (!$permission_check['success'])
        throw new \Exception($permission_check['message']);

      $params['owner_id'] = $ownerId;
      $params['status'] = 'inactive';
      $params['point_of_contact'] = 0;
      $this->LeadOwners->updateLeadOwner($params);

      return $this->setJsonResponse(['success' => true, 'message' => 'Status Updated']);
    } catch (\Exception $error) {
      Log::error($error->getMessage());
      return $this->setJsonResponse(['success' => false, 'message' => $error->getMessage()]);
    }
  }


  /**
   * Add new lead_quote_ref
   * @return object json response
   */
  public function addLeadQuoteRef()
  {
    try {
      if (!$this->request->is('post'))
        throw new MethodNotAllowedException("Invalid Request Method.");

      $original = false;
      $partner_user = $this->Auth->identify();
      $leadData = $this->request->getData();

      $this->loadModel('LeadQuotesRef')->validateAddLead($leadData);

      $lead = $this->loadModel('Leads')->getLead(['lead_ref' => $leadData['lead_ref']]);

      $permission_check = $this->checkPermission($lead['lead_id'], $original, $partner_user['account_type'] === 'Applicant');
      if (!$permission_check['success'])
        throw new \Exception($permission_check['message']);

      $leadInfo = array('quote_ref' => $leadData['quote_ref'], 'lead_id' => $lead['lead_id']);

      try {
        $results = $this->loadModel('LeadQuotesRef')->addLeadQuoteRef($leadInfo);
      } catch (\Exception $e) {
        return $this->setJsonResponse(['success' => false, 'message' => 'Entry already exist']);
      }

      return $this->setJsonResponse(['success' => true, 'message' => 'Record saved', 'data' => ['lead_quote_ref_id' => $results]]);
    } catch (\Exception $e) {
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()]);
    }

  }

  /**
   * Update lead_quote_ref
   * @param string $quoteRef
   * @return object json response
   */
  public function updateLeadQuoteRefByQuoteRef($quoteRef = '')
  {
    try {
      if (!$this->request->is('put'))
        throw new MethodNotAllowedException("Invalid Request Method.");

      $leadRefData = $this->request->getData();
      $leadRef = $this->loadModel('LeadQuotesRef')->validateUpdateLeadRef($leadRefData, $quoteRef);
      $leadRefInfo = array('quote_ref' => $quoteRef, 'has_accepted_proposal' => $leadRefData['has_accepted_proposal']);

      try {
        $results = $this->loadModel('LeadQuotesRef')->updateLeadRef($leadRefInfo);
      } catch (\Exception $e) {
        return $this->setJsonResponse(['success' => false, 'message' => 'Entry already exist']);
      }

      return $this->setJsonResponse(['success' => true, 'message' => 'Record saved', 'data' => ['lead_quote_ref_id' => $leadRef['lead_quote_ref_id']]]);
    } catch (\Exception $e) {
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()]);
    }

  }

    private function _updateLenderPreferences($lead_id, $preferences){
    $this->loadModel('LeadPreferredLender')->updateLenderPreferences($lead_id, array_values($preferences)[0]);
  }

    private function _noticeBrokerNewApplicantAdded($lead, $lead_owner){
    $brokers = $this->loadModel('PartnerUserLeads')->getCurrentPartnerUser($lead['lead_id'], true);
    if (!empty($brokers)) {
      foreach ($brokers as $key => $value) {
        $broker = ['email' => $value['email'], 'name' => $value['name']];
        break;
      }
      }
      else{
      $broker = $this->loadModel('PartnerUsers')->getPartnerUser(array('partner_id' => $lead['partner_id'], 'point_of_contact' => 1));
    }
    if (!empty($broker)) {
      // found broker, then send email
      $combination_name = $this->loadModel('PartnerProductTypes')->getCombinationNameBySubTypeID($lead['product_type_id']);
      $this->LoadModel('PartnerNotifications')->sendPartnerNotifications($lead['partner_id'], 'ApplAdded', $lead['lead_id'], [
            'sub_applicant_name' => $lead_owner['first_name'] . ' '.$lead_owner['last_name'], 'product_type'=> $combination_name['combination_name'], 'delivery_type'=> 'Email'], null, ['lead_ref' => $lead['lead_ref']]);
    }

  }


    private function _formatLeadData($data){
    if (!empty($data['lead']))
      $data['lead'] = $this->loadModel('Leads')->formatLeadData(['lead' => $data['lead']], [], true)['lead'];

    if (!empty($data['lead_asset_finance']))
      $data['lead_asset_finance'] = $this->loadModel('Leads')->formatLeadData(['lead_asset_finance' => $data['lead_asset_finance']], [], true)['lead_asset_finance'];

    if (!empty($data['lead_owner'])) {
      foreach ($data['lead_owner'] as $key => $owner) {
        $data['lead_owner'][$key] = $this->loadModel('Leads')->formatLeadData(['lead_owner' => $owner], [], true)['lead_owner'];
        unset($data['lead_owner'][$key]['percent_complete']);
      }
    }

    unset($data['lead']['hashed_lead_id']);
    unset($data['lead']['client_login_code']);
    unset($data['lead']['percent_complete']);
    return $data;
  }


    private function _addLeadIdIntoPayload($data, $lead_id){
    if (!empty($data['lead']))                   $data['lead']['lead_id']                = $lead_id;
    if (!empty($data['abnlookup_force_add']))    $data['abnlookup_force_add']['lead_id'] = $lead_id;
    if (!empty($data['lead_asset_finance']))     $data['lead_asset_finance']['lead_id']  = $lead_id;
    if (!empty($data['current_user']))           $data['current_user']['lead_id']        = $lead_id;

    if (!empty($data['lead_owner']))                     $data['lead_owner']                     = $this->_addLeadIdIntoMultiDementionArray($data['lead_owner'], $lead_id);
    if (!empty($data['partner_lead_uploads']))           $data['partner_lead_uploads']           = $this->_addLeadIdIntoMultiDementionArray($data['partner_lead_uploads'], $lead_id);
    if (!empty($data['lead_assets']))                    $data['lead_assets']                    = $this->_addLeadIdIntoMultiDementionArray($data['lead_assets'], $lead_id);
    if (!empty($data['lead_liabilities']))               $data['lead_liabilities']               = $this->_addLeadIdIntoMultiDementionArray($data['lead_liabilities'], $lead_id);
    if (!empty($data['lead_references']))                $data['lead_references']                = $this->_addLeadIdIntoMultiDementionArray($data['lead_references'], $lead_id);
    if (!empty($data['lead_notes']))                     $data['lead_notes']                     = $this->_addLeadIdIntoMultiDementionArray($data['lead_notes'], $lead_id);
    if (!empty($data['sms_communications']))             $data['sms_communications']             = $this->_addLeadIdIntoMultiDementionArray($data['sms_communications'], $lead_id);
    if (!empty($data['tasks']))                          $data['tasks']                          = $this->_addLeadIdIntoMultiDementionArray($data['tasks'], $lead_id);
    if (!empty($data['partner_lead_uploads_requested'])) $data['partner_lead_uploads_requested'] = $this->_addLeadIdIntoMultiDementionArray($data['partner_lead_uploads_requested'], $lead_id);

    return $data;
  }

    private function _filterNotAllowedFields($data){
    // leads.partner_status_id only allow to update 4 and 55
      if(!empty($data['lead']['partner_status_id']) AND !in_array($data['lead']['partner_status_id'], [4, 55])){
      unset($data['lead']['partner_status_id']);
    }
    return $data;
  }


    private function _addLeadIdIntoMultiDementionArray($data, $lead_id){
    foreach ($data as $key => $item) {
      $data[$key]['lead_id'] = $lead_id;
    }
    return $data;
  }


  //check if the partner has created same lead before
    private function _dupeCheck($data, $original, $original_owners){
    $partner_user = $this->Auth->identify();

    // overwrite new lead data
    if (!empty($data['lead'])) {
      foreach ($data['lead'] as $field => $value) {
        $original[$field] = $value;
      }
    }

    // Init Payloads
    $payload = [];
    $poc_owner_id = 0;
    foreach ($original_owners as $owner) {
      if ($owner['point_of_contact']) {
        $poc_owner_id = $owner['owner_id'];
        $payload = ['lead' => $original, 'lead_owner' => $owner];
        break;
      }
    }

    // Overwrite lead owner data:
    if (!empty($data['lead_owner'])) {
      foreach ($data['lead_owner'] as $owner) {
          if(!empty($owner['owner_id']) AND $owner['owner_id'] == $poc_owner_id){
          foreach ($owner as $field => $value) {
            $payload['lead_owner'][$field] = $value;
          }
          break;
        }
      }
    }

    $days = Configure::read('Lend.partner_honoured_lookup.days');
    if (false !== ($existingLead = $this->loadModel('Leads')->partnerSameOwnerLead($payload, $original['partner_id']))) {
      $href = '/leads/view/' . (new LendInternalAuth)->hashLeadId($existingLead['lead_id']);
        if($partner_user AND $partner_user['account_type']!=='Applicant') $message = "You have already submitted this business within the last $days days, for the same product type. Please change the product type. <a class='button' href='$href'>View Existing Lead</a>";
      else              $message = "You have already submitted an application within the last $days days with these  same details.";
      throw new \Exception($message);
    }
  }


  // Check user mobile number is same as partner user
    private function _mobileCheck($data){
    $partner_user = $this->Auth->identify();
      if (!empty($data['lead_owner']) AND !empty($partner_user) AND $partner_user['account_type']!=='Applicant') {
      foreach ($data['lead_owner'] as $owner) {
        $leadOwnerMobile = !empty($owner['mobile']) ? preg_replace('/\s+/', '', $owner['mobile']) : '';
        if (!empty($leadOwnerMobile) && ($leadOwnerMobile == $partner_user['phone'] || $leadOwnerMobile == $partner_user['mobile']))
          throw new \Exception("Lead's Mobile number cannot be the same as the Broker's number");
      }
    }
  }


  private function _updateLead($data, $original)
  {
    $newBsDocId = (!empty($data['lead']['bs_doc_id']) and $original['bs_doc_id'] != $data['lead']['bs_doc_id']);
    if ($newBsDocId)
      $data['lead']['bs_doc_retrieved'] = '0';
    TableRegistry::getTableLocator()->get('LeadEntity')->updateLeadEntity($data['lead']['lead_id'], $data['lead']);
    // and we need to save a bg job
    if ($newBsDocId) {
      $this->loadModel('BackgroundJobs')->addBackgroundJob(array(
        'lead_id' => $data['lead']['lead_id'],
        'job_type' => 'brokerFlowDocIdRetrieval',
        'ref_id' => $data['lead']['bs_doc_id'],
        'class_name' => 'Bankstatements',
        'function_name' => 'brokerFlowDocIdRetrieval',
        'created' => date('Y-m-d H:i:s', time()),
      ));
      $leadBrokerflowsTable = TableRegistry::getTableLocator()->get('LeadBrokerflowsEntity');
      $leadBrokerflowData = [
        'lead_id' => $data['lead']['lead_id'],
        'broker_flow_id' => $data['lead']['bs_doc_id'],
        'status' => "brokerflow_submitted",
        'created' => date('Y-m-d H:i:s'),
        'updated' => null,
        'bank_account_ids' => '',
        'broker_flow_id_expired' => ''
      ];
      $leadBrokerflow = $leadBrokerflowsTable->newEntity($leadBrokerflowData);
      $leadBrokerflowsTable->save($leadBrokerflow);
    }
    // $this->loadModel('Leads')->saveLeadBrokerFlowId($data['lead']['lead_id'], $data['lead']['bs_doc_id']);
  }



    private function _updateStatus($data, $original, $partnerUser) {
    if (array_key_exists('stop_sending', $data)) {
      if (empty($data['stop_sending'])) { //re-open
        $this->loadModel('PartnerLeadHistory')->addPartnerLeadHistory(
            array('partner_id'=>isset($partnerUser['partner_id']) ? $partnerUser['partner_id']: null,
            'partner_user_id' => isset($partnerUser['partner_user_id']) ? $partnerUser['partner_user_id'] : null,
            'lead_id' => $original['lead_id'],
                   'history_detail'=>'Lead Re-Opened'));
        //remove the record in lead_send_prevention if exist
        $this->loadModel('LeadSendPrevention')->removeSendPrevention($original['lead_id']);

        // check if lead status is 'Rejected'
        $lenderStatuses = $this->loadModel('LendStatuses')->getLendStatusById(@$original['partner_status_id']);
        if ($lenderStatuses['group_name'] === 'Rejected') {
          if ($this->__checkIfAvailableProductsExist($original['lead_id'], $original))
            $this->Leads->updateLead(array('lead_id' => $original['lead_id'], 'partner_status_id' => 55));   //if has available products, then update status to `Ready to Send`
          else
            $this->Leads->updateLead(array('lead_id' => $original['lead_id'], 'partner_status_id' => 25));   // otherwise update status to `Rejected - No more lenders found`
        }
      } else {  //stop sending
        $this->loadModel('PartnerLeadHistory')->addPartnerLeadHistory(
            array('partner_id'=>isset($partnerUser['partner_id']) ? $partnerUser['partner_id'] : null,
            'partner_user_id' => isset($partnerUser['partner_user_id']) ? $partnerUser['partner_user_id'] : null,
            'lead_id' => $original['lead_id'],
              'history_detail'=>'Lead Closed'));
        //add a record into lead_send_prevention
        $row = array();
        $row['lead_id'] = $original['lead_id'];
        $row['user_id'] = null;
        $row['partner_user_id'] = isset($partnerUser['partner_user_id']) ? $partnerUser['partner_user_id'] : null;
        $row['reason']  = null;
        $row['created'] = date('Y-m-d H:i:s');
        $this->loadModel('LeadSendPrevention')->addSendPrevention($row);

        //check if the status is Rejected, In Progress, Attempting or Pending
        $lenderStatuses = $this->loadModel('LendStatuses')->getLendStatusesOldFashion();
        $lead = $this->loadModel('Leads')->getLead(['lead_id' => $original['lead_id']]);
          if (!empty($lenderStatuses[$lead['partner_status_id']]['groupName'])
            && !in_array($lenderStatuses[$lead['partner_status_id']]['groupName'], ['Attempting', 'In Progress'])) {
          //close lead directly
          $this->Leads->updateLead(array('lead_id' => $original['lead_id'], 'partner_status_id' => 58));
        }
      }
    }

  }


  private function _reassign($current_user, $partner_user)
  {
    $this->loadModel('Leads');
    $this->loadModel('PartnerUserLeads');
    $this->loadModel('PartnerUsers');
    if (empty($partner_user['account_admin']) && empty($user['permission_edit_assignee']))
      throw new \Exception('You do not have access to this resource');

    $lead = $this->Leads->getLead(['lead_id' => $current_user['lead_id']]);
    if (empty($lead))
      throw new \Exception("Lead not found");
    if ($lead['partner_id'] != $partner_user['partner_id'])
      throw new \Exception("You do not have access to this lead");

    $manageActiveUsers = $this->PartnerUsers->getActiveUsers($partner_user['partner_id']);

    //check if the partnerUserId is contained in $manageActiveUsers
    if (empty($manageActiveUsers[$current_user['id']]))
      throw new \Exception('Selected user not found');

    //update partner_lead_users so that only new $partnerUserId has access to that lead
    $partnerUserLeadsTable = TableRegistry::getTableLocator()->get('PartnerUserLeadsEntity');
    $partnerUserLead = $partnerUserLeadsTable->newEntity([
      'partner_user_id' => $current_user['id'],
      'status' => 'ACCESS',
      'lead_id' => $current_user['lead_id']
    ]);
    $partnerUserLeadsTable->save($partnerUserLead);
    // Send notification
    $adhocData['prev_partner_name'] = $partner_user['name'];
    $this->LoadModel('PartnerNotifications')->sendPartnerNotifications(
      $partner_user['partner_id'],
      'AssignLead',
      $current_user['lead_id'],
      $adhocData,
      $current_user['id'],
      null
    );
  }


    private function __checkIfAvailableProductsExist($leadId, $lead) {
    $foundAvailableProduct = false;
    $availableProducts = [];

    $partnerId = $this->Auth->user('partner_id');

    $lead_asset_finance = $this->_getLeadAssetFinance($leadId);

    $makeDecisionV2 = new MakeDecisionV2;
    $products = $makeDecisionV2->run(['lead' => $lead['lead'], 'lead_owners' => array($lead['lead_owner']), 'bank_statements_analysis' => $lead['bank_statements_analysis']], array('lead' => true, 'lead_owners' => true, 'bank_statements_analysis' => true, 'lead_abn_lookup' => true, 'partners' => !empty($lead['lead']['partner_id']), 'lead_asset_finance' => !empty($lead_asset_finance)), null, false);
    $products = $makeDecisionV2->checkPossibleDupe($products);
    $products = $makeDecisionV2->rank($products, $lead['lender']);
    $products = $makeDecisionV2->lender($products, $lead);

    // $makeDecision = new MakeDecision;
    // $makeDecision->init(['lead'=>$lead['lead'], 'lead_owners'=>array($lead['lead_owner']), 'bank_statements_analysis'=>$lead['bank_statements_analysis']], array('lead'=>true, 'lead_owners'=>true, 'bank_statements_analysis'=>true, 'lead_abn_lookup'=>true), null, false);
    // $makeDecision->criteriaChecks();
    // $makeDecision->checkPossibleDupe();
    //
    // $products = $makeDecision->rankProductByPriority();
    // $products = $makeDecision->setPartnerPreferLender($partnerId, $products);
    $lendSize = new LendSize;
    $products = $lendSize->calculateLendSize($lead, $products);

    $passedProducts = array_filter($products, function ($product) {
      return $product['pass'] === 'passed';
    });

    // Collect only one product per each lender which are already sorted by priority
    $tmpLenders = [];
    $productPerLender = [];
    foreach ($passedProducts as $product) {
      if (!in_array($product['lender_id'], $tmpLenders)) {
        $productPerLender[] = $product;
        $tmpLenders[] = $product['lender_id'];
      }
    }
    unset($tmpLenders);

    // Get the lender list to which the lead has already been sent
    $alreadySentLenderIds = [];
    $saleDetails = $this->LoadModel('Sales')->getSalesWithLenderDetails($leadId);
    if ($saleDetails) {
      $alreadySentLenderIds = array_unique(array_column($saleDetails, 'lender_id'));
    }

    $doNotShowLenders = $this->LoadModel('Lenders')->getListOfDoNotShowInLenderPrefs();
    $doNotShowLenderIds = array_column($doNotShowLenders, 'lender_id');

    foreach ($productPerLender as $product) {
      if (!empty($product['intermediary_lender_id']) && ($partner_user['account_type'] == 'Intermediary')) {
        continue;
      }

      // Eliminate lenders with **DO_NOT_SHOW_IN_LENDER_PREFS**
      if (in_array($product['lender_id'], $doNotShowLenderIds)) continue;

      $alreadySent = in_array($product['lender_id'], $alreadySentLenderIds)  ? true : false;

      $availableProducts[] = [
        'product_id' => $product['lender_product_id'],
        'already_sent' => $alreadySent,
        'duplicate' => !empty($product['duplicate']),
        'sent_more_than_config' => !empty($product['sent_more_than_config']),
      ];
    }

    $maxLenders = Configure::read('Lend.partner.max_num_lender_to_send');

    $leadDupeSales = $this->loadModel('Sales')->getleadDupeSales($lead['lead']['lead_id']);

    // Don't count duplicate sales
    $numOfLendersSendTo = count($saleDetails) - count($leadDupeSales);

    $no_send = $numOfLendersSendTo >= $maxLenders;
    if (!empty($availableProducts) && (empty($partnerIdsInWhileListOfLendSize) || in_array((int)$partnerId, $partnerIdsInWhileListOfLendSize)))
      $availableProducts = $makeDecision->setProductsInOrder($availableProducts, $no_send, $partnerId);

    foreach ($availableProducts as $availableProduct) {
      if ($availableProduct['already_sent']) continue;
      if (!empty($availableProduct['sent_more_than_config'])) continue;
      if (!empty($availableProduct['duplicate']) && !$no_send)  continue;

      $foundAvailableProduct = true;
      break;
    }
    return $foundAvailableProduct;

  }

  private function _updateLeadOwners($owners){
    $this->loadModel('LeadOwners');
    $peopleTable = TableRegistry::getTableLocator()->get('PartnerAccountPeopleEntity');
    $ownerTable = TableRegistry::getTableLocator()->get('LeadOwnersEntity');
    $partner_user = $this->Auth->identify();
    foreach ($owners as $key => $owner) {
      $employment = [];
      $addresses = [];
      if (isset($owner['employment'])) {
        $employment = $owner['employment'];
        unset($owner['employment']);
      }
      if (isset($owner['addresses'])) {
        $addresses = $owner['addresses'];
        unset($owner['addresses']);
      }

      if (isset($owner['finances'])) {
        $finances = $owner['finances'];
        unset($owner['finances']);
      }

      if (empty($owner['owner_id'])) { // Add new owner
        $owners[$key]['owner_id'] = $this->LeadOwners->addLeadOwner($owner);
      } else { // Update existing one
        $this->LeadOwners->updateLeadOwner($owner);
      }
      if (!empty($employment)) $owners[$key]['employment'] = $this->_updateLeadOwnerEmployment($employment, $owners[$key]['owner_id']);
      if (!empty($addresses))  $owners[$key]['addresses'] = $this->_updateLeadOwnerAddresses($addresses,  $owners[$key]['owner_id']);
      if (!empty($finances))  $owners[$key]['finances'] = $this->_updateOwnerFinances($finances,  $owners[$key]['owner_id']);

      // if one of first_name, last_name, email, mobile or phone changed, re-assign to account_people
      if (!empty($owner['owner_id']) && (!empty($owner['first_name']) || !empty($owner['last_name']) || !empty($owner['email']) || !empty($owner['phone']) || !empty($owner['mobile']))) {
        // get current owner details:
        $current_owner = $ownerTable->get($owner['owner_id']);
        // generate conditions
        $conditions = [
          'PartnerAccountPeopleEntity.partner_id' => $partner_user['partner_id'],
          'PartnerAccountPeopleEntity.first_name' => !empty($owner['first_name']) ? $owner['first_name'] : $current_owner->first_name,
          'PartnerAccountPeopleEntity.last_name' => !empty($owner['last_name']) ? $owner['last_name'] : $current_owner->last_name,
          'OR' => [
            'PartnerAccountPeopleEntity.email' => !empty($owner['email']) ? $owner['email'] : $current_owner->email,
            'PartnerAccountPeopleEntity.phone' => !empty($owner['phone']) ? $owner['phone'] : $current_owner->phone,
            'PartnerAccountPeopleEntity.mobile' => !empty($owner['mobile']) ? $owner['mobile'] : $current_owner->mobile,
            'PartnerAccountPeopleEntity.dob' => !empty($owner['dob']) ? $owner['dob'] : $current_owner->dob,
          ]
        ];
        // if first_name, last_name or email, phone and mobile haven't set, skip to assign to people.
        if (empty($conditions['PartnerAccountPeopleEntity.first_name']) ||
          empty($conditions['PartnerAccountPeopleEntity.last_name']) ||
          (
            empty($conditions['OR']['PartnerAccountPeopleEntity.email']) &&
            empty($conditions['OR']['PartnerAccountPeopleEntity.phone']) &&
            empty($conditions['OR']['PartnerAccountPeopleEntity.mobile']) &&
            empty($conditions['OR']['PartnerAccountPeopleEntity.dob'])
          )
        ) {
          continue;
        }
        // find matched people
        $person = $peopleTable->find('all')
          ->where($conditions)
          ->order(['PartnerAccountPeopleEntity.id' => 'desc'])
          ->first();
        // if not exists, create one.
        if (empty($person)) {
          $person = $peopleTable->newEntity($current_owner->toArray());
          $peopleTable->save($person);
        }
        // assign people_id to owner.
        $ownerTable->patchEntity($current_owner, [
          'partner_account_people_id' => $person->id
        ]);
        $ownerTable->save($current_owner);
      }
    }
    return $owners;
  }


    private function _updateLeadOwnerEmployment($employment, $owner_id){
    $this->loadModel('LeadOwnerEmployment');
    foreach ($employment as $k => $e) {
      if (empty($e['lead_owner_employment_id'])) { // Add new item
        $employment[$k]['lead_owner_id'] = $owner_id;
        $employment[$k]['lead_owner_employment_id'] = $this->LeadOwnerEmployment->addLeadOwnerEmployment($employment[$k]);
      } else { // Update existing one
        $this->LeadOwnerEmployment->updateLeadOwnerEmployment($e);
      }
    }
    return $employment;
  }


    private function _updateLeadOwnerAddresses($addresses, $owner_id){
    $this->loadModel('LeadOwnerAddress');
    foreach ($addresses as $k => $a) {
      if (empty($a['lead_owner_address_id'])) { // Add new item
        $addresses[$k]['lead_owner_id'] = $owner_id;
        $addresses[$k]['lead_owner_address_id'] = $this->LeadOwnerAddress->addLeadOwnerAddress($addresses[$k]);
      } else { // Update existing one
        $this->LeadOwnerAddress->updateLeadOwnerAddress($a);
      }
    }
    return $addresses;
  }

  private function _saveAssetSecurity($lead_id): ?EntityInterface
  {
    if ($this->request->getData('security.nvic')) {
      /** @var LeadGlassSecurityTable $leadGlassSecurityTable */
      $leadGlassSecurityTable = TableRegistry::getTableLocator()->get('LeadGlassSecurity');
      if (!$security = $leadGlassSecurityTable->findByLeadId($lead_id)->first()) {
        $security = $leadGlassSecurityTable->newEntity();
      }
      $security->lead_id = $lead_id;
      return $leadGlassSecurityTable->save($security->set($this->request->getData('security')));
    }
    return null;
  }

  private function _getAssetSecurity($lead_id): ?EntityInterface
  {
    return TableRegistry::getTableLocator()->get('LeadGlassSecurity')->findByLeadId($lead_id)->first();
  }

    private function _updateAbnLookup($data){
    $partner_user = $this->Auth->identify();
    $params = $data;
    if (!empty($params['abn_id'])) unset($params['abn_id']);
    if (isset($params['other_trading_names'])) {
      unset($params['other_trading_names']);
    }

    $data['abn_id'] = $this->loadModel('LeadAbnLookup')->addLookup($params);
    // Mark job as complete if there is a job in AdminJobs:
    $this->loadModel('AdminJobs')->markAsComplete($data['lead_id'], 'abn_lookup', @$partner_user['partner_user_id']);
    return $data;
  }


  // Handle for attached files
    private function _updatePartnerLeadUploads($data){
    $this->loadModel('PartnerLeadUploads');
    foreach ($data as $key => $item) {
      unset($data[$key]['ref']);
      if(isset($item['shared_to'])){
        $meta_id = $this->_sharePartnerLeadUploads($item['partner_lead_upload_id'], $item['shared_to']);
        $data[$key]['shared_to_meta_id'] = $meta_id;
        continue;
      }
      $owner_id = $item['owner_id'];
      $meta = [];
      if (!empty($item['meta'])) {
        $meta = $item['meta'];
        unset($data[$key]['meta']);
      }
      if (empty($item['partner_lead_upload_id'])) { // Add new item
        $data[$key]['partner_lead_upload_id'] = $this->PartnerLeadUploads->addPartnerLeadUpload($data[$key]);
      } else { // Update existing one
        $this->PartnerLeadUploads->updatePartnerLeadUpload($data[$key]);
      }
      if (!empty($meta)) {
        $data[$key]['meta'] = $this->_updatePartnerLeadUploadsMeta($meta, $data[$key]['partner_lead_upload_id'], $owner_id);
      }
    }
    return $data;
  }

  // Handle for attached files' meta data
    private function _updatePartnerLeadUploadsMeta($meta, $partner_lead_upload_id, $owner_id = null){
    $this->loadModel('PartnerLeadUploadsMeta');
    foreach ($meta as $key => $item) {
      if (($item['value'] == 'Privacy Form (Wet Signature)' || $item['value'] == 'Privacy Form (Electronic Signature)') && $owner_id) {
        $this->loadModel('LeadOwners')->updateOwnerConsent($owner_id);
      } else {
        //$this->loadModel('LeadOwners')->updateOwnerConsent($lead_owners_id);
        //There should be a ROLLBACK but did nit need for this ticket
      }

      if (empty($item['partner_lead_upload_meta_id'])) { // Add new item
        $meta[$key]['partner_lead_upload_id'] = $partner_lead_upload_id;
        $meta[$key]['partner_lead_upload_meta_id'] = $this->PartnerLeadUploadsMeta->addPartnerLeadUploadsMeta($meta[$key]);
      } else { // Update existing one
        $this->PartnerLeadUploadsMeta->updatePartnerLeadUploadsMeta($meta[$key]);
      }
    }
    return $meta;
  }

  // Handle for share document
  private function _sharePartnerLeadUploads($partner_lead_upload_id, $shared_to){
    $this->loadModel('PartnerLeadUploadsMeta');
    if($shared_to == "account"){
      $this->_addPartnerAccountUploads($partner_lead_upload_id);
    }elseif($shared_to == "applicant"){
      $this->_addPartnerApplicantGroupUploads($partner_lead_upload_id);
    }
    $meta = ['partner_lead_upload_id' => $partner_lead_upload_id, 'field_name' => "shared_to", 'value' => $shared_to];
    $partner_lead_upload_meta_id = $this->PartnerLeadUploadsMeta->addPartnerLeadUploadsMeta($meta);
    return $partner_lead_upload_meta_id;
  }

  private function _addPartnerAccountUploads($partner_lead_upload_id) {
    try {
      $partnerLeadUploadsTable = TableRegistry::getTableLocator()->get('PartnerLeadUploadsEntity');
      $partner_lead_upload = $partnerLeadUploadsTable->get($partner_lead_upload_id,[
        'contain' => [
          'PartnerLeadUploadsMetaEntity',
          'LeadEntity'
        ]])->toArray();

      $unset_fields = ['partner_lead_upload_id', 'lead_owner', 'partner_lead_uploads_meta', 'created'];
      $meta_unset_fields = ['partner_lead_upload_meta_id', 'partner_lead_upload_id', 'created', 'updated'];
      $partner_lead_upload['partner_account_id'] = $partner_lead_upload['lead']['account_id'];
      $partner_lead_upload['partner_account_uploads_meta'] = $partner_lead_upload['partner_lead_uploads_meta'];
      foreach ($unset_fields as $f) {
        unset($partner_lead_upload[$f]);
      }
      foreach ($partner_lead_upload['partner_account_uploads_meta'] as $k => $meta) {
        foreach ($meta_unset_fields as $f) {
          unset($partner_lead_upload['partner_account_uploads_meta'][$k][$f]);
        }
      }
      $partnerAccountUploadsTable = TableRegistry::getTableLocator()->get('PartnerAccountUploadsEntity');
      $partner_account_upload_entity = $partnerAccountUploadsTable->newEntity($partner_lead_upload);
      $partner_account_upload = $partnerAccountUploadsTable->save($partner_account_upload_entity);
      return $partner_account_upload;
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }

  private function _addPartnerApplicantGroupUploads($partner_lead_upload_id) {
    try {

      $partnerLeadUploadsTable = TableRegistry::getTableLocator()->get('PartnerLeadUploadsEntity');
      $partner_lead_upload = $partnerLeadUploadsTable->get($partner_lead_upload_id,[
        'contain' => [
          'PartnerLeadUploadsMetaEntity',
          'LeadOwnersEntity',
          'LeadOwnersEntity.PartnerAccountPeopleEntity'
        ]])->toArray();

      $unset_fields = ['partner_lead_upload_id', 'lead_owner', 'partner_lead_uploads_meta', 'created'];
      $meta_unset_fields = ['partner_lead_upload_meta_id', 'partner_lead_upload_id', 'created', 'updated'];
      $partner_lead_upload['people_id'] = $partner_lead_upload['lead_owner']['partner_account_people']['id'];
      $partner_lead_upload['partner_applicant_group_uploads_meta'] = $partner_lead_upload['partner_lead_uploads_meta'];
      foreach ($unset_fields as $f) {
        unset($partner_lead_upload[$f]);
      }
      foreach ($partner_lead_upload['partner_applicant_group_uploads_meta'] as $k => $meta) {
        foreach ($meta_unset_fields as $f) {
          unset($partner_lead_upload['partner_applicant_group_uploads_meta'][$k][$f]);
        }
      }
      $partnerApplicantGroupUploadsTable = TableRegistry::getTableLocator()->get('PartnerApplicantGroupUploadsEntity');
      $partner_applicant_group_upload_entity = $partnerApplicantGroupUploadsTable->newEntity($partner_lead_upload);
      $partner_applicant_group_upload = $partnerApplicantGroupUploadsTable->save($partner_applicant_group_upload_entity);
      return $partner_applicant_group_upload;
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }


    private function _updateLeadAssetFinance($data){
    $this->loadModel('LeadAssetFinance');
    if (empty($data['lead_asset_finance_id'])) // Add new item
      $data['lead_asset_finance_id'] = $this->LeadAssetFinance->addLeadAssetFinance($data);
    else // Update existing one
      $this->LeadAssetFinance->updateLeadAssetFinance($data);
    return $data;
  }


    private function _updateLeadAssets($data){
    $this->loadModel('LeadAssets');
    foreach ($data as $key => $item) {
      if (empty($item['lead_asset_id'])) { // Add new item
        $data[$key]['lead_asset_id'] = $this->LeadAssets->addLeadAsset($item);
      } else { // Update existing one
        $this->LeadAssets->updateLeadAsset($item);
      }
    }
    return $data;
  }


  private function _updateOwnerFinances($data, $owner_id)
  {
    $leadOwnerFinances = $this->loadModel('LeadOwnerFinances');
    $finances = $leadOwnerFinances->findByOwnerId($owner_id)->first() ?? $leadOwnerFinances->newEntity([
      'owner_id' => $owner_id
    ]);
    return $leadOwnerFinances->save($finances->set($data));
  }


    private function _updateLeadLiabilities($data){
    $this->loadModel('LeadLiabilities');
    foreach ($data as $key => $item) {
      if (empty($item['lead_liability_id'])) { // Add new item
        $data[$key]['lead_liability_id'] = $this->LeadLiabilities->addLeadLiability($item);
      } else { // Update existing one
        $this->LeadLiabilities->updateLeadLiability($item);
      }
    }
    return $data;
  }

    private function _updateLeadUploadsRequested($data) {
    $leadUploadsRequested = $this->loadModel('LeadUploadsRequested');
    foreach ($data as $key => $item) {
        if(!empty($item['id']) AND $item['specify']=='-1'){ // Update existing one
        $leadUploadsRequested->deleteUploadRequested($item);
      } elseif (!empty($item['id'])) { // Update existing one
        $leadUploadsRequested->updateUploadRequested($item);
      } else { // Add new item
        $data[$key]['id'] = $leadUploadsRequested->addUploadRequested($item);
      }
    }
    return $data;
  }

    private function _updateLeadUploadsDetailsRequested($data) {
    $leadUploadsRequestedDetails = $this->loadModel('LeadUploadsRequestedDetails');
    foreach ($data as $key => $item) {
      $exists = $leadUploadsRequestedDetails->getRequested($item['lead_ref'], [$item['owner_id']]);
      if (!empty($exists)) {
        $item['id'] = $exists[0]['id'];
      }

        if(!empty($item['id']) AND empty($item['notes'])){ // Update existing one
        $leadUploadsRequestedDetails->deleteUploadRequested($item);
      } elseif (!empty($item['id'])) { // Update existing one
        $leadUploadsRequestedDetails->updateUploadRequested($item);
      } else { // Add new item
        $data[$key]['id'] = $leadUploadsRequestedDetails->addUploadRequested($item);
      }
    }
    return $data;
  }

    private function _updateBSAccounts($data, $lead_id){
    $this->loadModel('BankStatementsAccounts');
    $lead = $this->loadModel('Leads')->getLead(['lead_id' => $lead_id]);
    foreach ($data as $key => $item) {
        if(empty($item['bsa_id']) OR empty($item['overdraft_limit'])){ // If no bsa_id or overdraft_limit, then skip this
        continue;
      } else { // Update existing one
        if ($item['is_account_level']) {
          $sendData = ['accountId' => $item['bsa_id'], 'partnerAccountId' => $lead['account_id'], 'leadId' => $lead_id, 'overdraft' => "" . $item['overdraft_limit']];
          $curl = new CurlHelper(getenv('DOMAIN_BANKFEEDS') . "/update-overdraft");
          $response = $curl->post($sendData, true, ["x-api-key" => getenv('BANK_STATEMENT_API_KEY')]);
        } else {
          $this->BankStatementsAccounts->updateBSAccount(['overdraft_limit' => $item['overdraft_limit']], ['bsa_id' => $item['bsa_id']]);
        }
      }
    }
  }

    private function _updateLeadReferences($data){
    $this->loadModel('LeadReferences');
    foreach ($data as $key => $item) {
      if (empty($item['lead_reference_id'])) { // Add new item
        $data[$key]['lead_reference_id'] = $this->LeadReferences->addLeadReference($item);
      } else { // Update existing one
        $this->LeadReferences->updateLeadReference($item);
      }
    }
    return $data;
  }

    private function _updateEntityTrust($data, $lead_id){
    $data['lead_id'] = $lead_id;
    $this->loadModel('EntityTrust');
    if (empty($data['entity_trust_id'])) // Add new item
      $data['entity_trust_id'] = $this->EntityTrust->addEntityTrust($data);
    else // Update existing one
      $this->EntityTrust->updateEntityTrust($data);
    return $data;
  }


  /* NOTE:: not using this as they are on separate places
    private function _updateLeadNotes($data){
      $this->loadModel('LeadNotes');
      foreach($data as $key=>$item){
        if(empty($item['note_id'])){ // Add new item
          $data[$key]['note_id'] = $this->LeadNotes->addNote($item);
        }else{ // Update existing one
          $this->LeadNotes->updateLeadNote($item);
        }
      }
      return $data;
    }


    private function _updateSmsCommunications($data){
      $this->loadModel('PartnerSmsHistory');
      foreach($data as $key=>$item){
        if(empty($item['sms_id'])){ // Add new item
          $data[$key]['sms_id'] = $this->PartnerSmsHistory->addPartnerSmsHistory($item);
          // Sending an SMS to Client
          $this->_sendSMS($item);
        }else{ // Update existing one
          $this->PartnerSmsHistory->updatePartnerSmsHistory($item['sms_id'], $item);
        }
      }
      return $data;
    }


    private function _sendSMS($data){
      $partner_user = $this->Auth->identify();
      $lead_owner = $this->loadModel('LeadOwners')->getLeadOwner(['lead_id'=>$data['lead_id'], 'point_of_contact'=>1]);
      if(empty($lead_owner['mobile'])) throw new \Exception("Can't find mobile for this lead.");
      $recipient = [
        'lead_id' => $data['lead_id'],
        'mobile' => $lead_owner['mobile']
      ];

      $item = [
        'recipient' => 'Client',
        'partner_id' => $partner_user['partner_id'],
        'partner_user_id' => $partner_user['partner_user_id'],
        'body' => trim($data['sms_message']),
        'notify_for_reply' => !empty($data['notify_for_reply']) ? $data['notify_for_reply'] : 0
      ];

      $ret = $this->loadModel('PartnerNotifications')->sendSMSNotification($recipient, $item);
      if(!$ret['success']) throw new \Exception($ret['message']);
    }


    private function _updateTasks($data){
      $this->loadModel('PartnerCallbacks');
      foreach($data as $key=>$item){
        unset($item['consultant']);
        unset($item['first_name']);
        unset($item['last_name']);
        unset($item['full_name']);
        unset($item['hashed_lead_id']);

        if(empty($item['callback_id'])){ // Add new item
          $data[$key]['callback_id'] = $this->PartnerCallbacks->addPartnerCallback($item);
        }else{ // Update existing one
          $this->PartnerCallbacks->updatePartnerCallbacks($item);
        }
      }
      return $data;
    }
    */


    private function _saveAnEdit($lead_id, $original, $data){
    $partner_user = $this->Auth->identify();
    $this->loadModel('PartnerLeadEdits')->saveAnEdit(
      array(
        'lead_id' => $lead_id,
        'partner_user_id' => (!empty($partner_user['partner_user_id']) ? $partner_user['partner_user_id'] : null)
      ),
      $original,
      $data,
      true
    );
  }


  // If this application is updated by client, send notification to partner:
    private function _sendNotification($data, $original){
    $this->loadModel('BackgroundJobs');
    $ref_id = json_encode(array('partner_id' => $original['partner_id'], 'lead_id' => $original['lead_id'], 'lookup_code' => 'ClientUpdatedApp'));
    if (!$this->BackgroundJobs->getBackgroundJobs(array('lead_id' => $original['lead_id'], 'job_type' => 're_send_partner_notification', 'ref_id' => $ref_id))) {
      $this->BackgroundJobs->addBackgroundJob(array(
        'lead_id'       => $original['lead_id'],
        'job_type'      => 're_send_partner_notification',
        'ref_id'        => $ref_id,
        'class_name'    => 'Partners',
        'function_name' => 're_send_partner_notification',
        'created'       => date('Y-m-d H:i:s', time()),
      ));
    }
  }


  /* =============================================================
    * MakeDecision function
    * ============================================================== */
  /* =============================================================
    * MakeDecision function
    * ============================================================== */
  public function collectAvailableProducts($lead_ref=false) {

    try {
      $this->loadModel('Leads');
      $this->loadModel('PartnerProductTypes');
      $this->loadModel('Sales');
      $this->loadModel('Lenders');

      $lendSize = new LendSize;
      $partnerIdsInWhileListOfLendSize = null; // [1, 2]; //change to null if pre-approval applies for all partners
      $partner_user = $this->Auth->identify();


      if (empty($partner_user))      throw new \Exception("Please login first.");
      if (empty($lead_ref))          throw new \Exception("Missing lead ref.");
      if ($partner_user['account_type'] === 'Applicant') throw new \Exception("You are not allow to access this.");



      $partnerId = $partner_user['partner_id'];
      $lead_id = (new LendInternalAuth)->unhashLeadId($lead_ref);

      $lead = $this->Leads->getLeadDetails($lead_id, ['get_all_owners' => true]);
      $poc_owner = $lead['lead_owner'][array_search('1', array_column($lead['lead_owner'], 'point_of_contact'))];
      if (!$lead) throw new \Exception("Could not find the lead");

      $permission_check = $this->checkPermission($lead_id, $lead['lead'], false);
      if (!$permission_check['success']) throw new \Exception($permission_check['message']);

      if ($lead['lead']['on_not_call_list']) throw new \Exception("On Do Not Call List");

      // Get partner product type details:
      if (!empty($lead['lead']['product_type_id']))
        $partner_product_type = $this->PartnerProductTypes->getPartnerProductType(['product_type_id' => $lead['lead']['product_type_id']], true);

      // Check held for review
      if ($lead['lead']['partner_status_id'] == 5) throw new \Exception("Held for review");

      // Partner honoured
      if ($lead['lead']['partner_status_id'] == 2 || $this->Leads->isLeadPartnerHonoured($lead_id))
        $confirmation = "This is a Partner Honoured Lead, are you sure that you want to send it to lenders manually?";

      $lead['abnlookup'] = $lead['lead']['organisation_name'];
      $lead['abn']       = $lead['lead']['abn'];

      $equipment_product_types = $this->loadModel('PartnerProductTypes')->getPartnerProductTypes(array('product_icon' => 'products/equipment_finance'), true);

      $isAssetFinance = false;

      foreach ($equipment_product_types as $product_type ) {
        if ($product_type['product_type_id'] == $lead['lead']['product_type_id']) {
          $isAssetFinance = true;
        }
      }

      if ($isAssetFinance) {
        $errors = $this->_leadValidationToSendLead(json_decode(json_encode($lead), true), false, true);
      } else {
        $errors = $this->_leadValidationToSendLead(json_decode(json_encode($lead), true), false);
      }

      $appendMsgDocuments = [];
      $appendMsg = [];
      $message = "";
      if (!empty($errors)) {
        Log::info("Lead {$lead_ref} - Required Fields Missing: " . json_encode($errors));
        $appendMsg = $this->_generateMessage($errors);
        $message = "Required Fields Missing";
      }

      // $documentErrors = $this->_documentValidationToSendLead($this->_getLead($lead_id), $lead_id);
      // if(!empty($documentErrors)){
      //   Log::info("Lead {$lead['lead']['lead_ref']} - Required Documents Missing: ".json_encode($documentErrors));
      //   foreach ($documentErrors as $er) {
      //        $appendMsgDocuments[] = $er;
      //   }
      //   $message = (empty($message)) ? 'Required Documents Missing' : 'Required Fields & Documents Missing';
      // }
      // if(!empty($errors) || !empty($documentErrors)) {
      //   return $this->setJsonResponse(['success' => false, 'message' => $message, 'errors' => json_encode($appendMsg), 'docErrors' => json_encode($appendMsgDocuments)]);
      // }




      if (!empty($errors)) {
        return $this->setJsonResponse(['success' => false, 'message' => $message, 'errors' => json_encode($appendMsg)]);
      }


      //throw new \Exception("Required Fields Missing". (!empty($appendMsg) ? ' ('. implode(', ', $appendMsg) .') ': ''));


      $makeDecisionV2 = new MakeDecisionV2;
      $products = $makeDecisionV2->run(['lead' => $lead['lead'], 'lead_owners' => array($poc_owner), 'bank_statements_analysis' => $lead['bank_statements_analysis']], array('lead' => true, 'lead_owners' => true, 'bank_statements_analysis' => !empty($lead['bank_statements_analysis']), 'lead_abn_lookup' => !empty($lead['abnlookup']), 'partners' => !empty($lead['lead']['partner_id']), 'lead_asset_finance' => !empty($lead['lead_asset_finance'])), null, true);
      $products = $makeDecisionV2->checkPossibleDupe($products);
      $other_products = $makeDecisionV2->checkOtherProducts($products);
      $products = $makeDecisionV2->rank($products, $lead['lender'], true);
      $products = $makeDecisionV2->lender($products, $lead);
      $other_products = $makeDecisionV2->rank($other_products, $lead['lender'], false);
      $other_products = $makeDecisionV2->lender($other_products, $lead);

      // $makeDecision = new MakeDecision;
      // $makeDecision->init(['lead'=>$lead['lead'], 'lead_owners'=>array($lead['lead_owner']), 'bank_statements_analysis'=>$lead['bank_statements_analysis']], array('lead'=>true, 'lead_owners'=>true, 'bank_statements_analysis'=>true, 'lead_abn_lookup'=>true), null, false);
      // $makeDecision->criteriaChecks();
      // $makeDecision->checkPossibleDupe();
      // $other_products = $makeDecision->checkOtherProducts();
      //
      // $products = $makeDecision->rankProductByPriority();
      //
      // $products = $makeDecision->setPartnerPreferLender($partnerId, $products);
      // $other_products = $makeDecision->rankProductByPriority($other_products);
      // $other_products = $makeDecision->setPartnerPreferLender($partnerId, $other_products);

      if (empty($partnerIdsInWhileListOfLendSize) || in_array((int)$partnerId, $partnerIdsInWhileListOfLendSize)) {
        if (!empty($lead['bank_statements_analysis']) || $lead['lead']['monthly_expenses'] >= 0) {
          $products = $lendSize->calculateLendSize($lead, $products);
          $other_products = $lendSize->calculateLendSize($lead, $other_products);
        }
      }

      // $passedProducts = array_filter($products, function($product) {
      //   return $product['pass'] === 'passed';
      // });

      $passedProducts = [];
      $failedProducts = [];
      if (!empty($products)) {
        foreach ($products as $p) {
          if ($p['pass'] === 'passed') {
            $passedProducts[] = $p;
          } else {
            $failedProducts[] = $p;
          }
        }
      }

      if (!empty($failedProducts)) $failedProducts = $this->_checkHideAnyway($failedProducts);
      $failedCriteriaButProductMatch = $makeDecisionV2->failedCriteriaButProductMatch($failedProducts);

      // Collect only one product per each lender which are already sorted by priority
      $tmpLenders = [];
      $productPerLender = [];
      if (!empty($passedProducts)) {
        foreach ($passedProducts as $product) {
          if (!in_array($product['lender_id'], $tmpLenders)) {
            $productPerLender[] = $product;
            $tmpLenders[] = $product['lender_id'];
          }
        }
      }
      $tmpLenders = []; // reset
      $otherProductPerLender = [];
      if (!empty($other_products)) {
        foreach ($other_products as $product) {
          if (!in_array($product['lender_id'], $tmpLenders)) {
            $otherProductPerLender[] = $product;
            $tmpLenders[] = $product['lender_id'];
          }
        }
      }
      $tmpLenders = []; // reset
      $failedCriteriaButProductPerLender = [];
      if (!empty($failedCriteriaButProductMatch)) {
        foreach ($failedCriteriaButProductMatch as $product) {
          if (!in_array($product['lender_id'], $tmpLenders)) {
            $failedCriteriaButProductPerLender[] = $product;
            $tmpLenders[] = $product['lender_id'];
          }
        }
      }
      unset($tmpLenders);

      // Get the lender list to which the lead has already been sent
      $alreadySentLenderIds = [];
      $saleDetails = $this->Sales->getSalesWithLenderDetails($lead_id);
      if ($saleDetails) {
        $alreadySentLenderIds = array_unique(array_column($saleDetails, 'lender_id'));
      }

      $doNotShowLenders = $this->Lenders->getListOfDoNotShowInLenderPrefs();
      $doNotShowLenderIds = array_column($doNotShowLenders, 'lender_id');

      $availableProducts = [];





      foreach ($productPerLender as $product) {

        if (!empty($product['intermediary_lender_id']) && ($partner_user['account_type'] == 'Intermediary')) {
          continue;
        }

        // Eliminate lenders with **DO_NOT_SHOW_IN_LENDER_PREFS**
        if (in_array($product['lender_id'], $doNotShowLenderIds)) continue;

        if ($product['intermediary_lender_id']) {
          $intermediary_group = $this->loadModel('IntermediaryLenders')->getInterLender(array('id' => $product['intermediary_lender_id']));
          $product['intermediary_group'] = $intermediary_group['name'];
        }

        // defiane array
        $commission_rate = [];
        if ($product['product_friendly_name']) {
          $commission_rate = $this->Lenders->getLenderCommisionRateByProduct($product['shorthand'], $product['product_friendly_name']);
        }

        $alreadySent = in_array($product['lender_id'], $alreadySentLenderIds)  ? true : false;
        $availableProducts[] = array_merge([
          'product_id' => $product['lender_product_id'],
          'lender_id' => $product['lender_id'],
          'inter_lender_id' => @$product['intermediary_lender_id'],
          'intermediary_group' => @$product['intermediary_group'],
          'email_only' => $product['email_only'],
          'lender_name' => $product['lender_name'],
          'required_accreditation' => $product['required_accreditation'],
          'lender_contact_number' => @$product['lender_contact_number'],
          'lender_contact_email' => @$product['lender_contact_email'],
          'shorthand' => $product['shorthand'],
          'lender_logo' => $product['lender_logo'],
          'already_sent' => $alreadySent,
          'duplicate' => !empty($product['duplicate']),
          'sent_more_than_config' => !empty($product['sent_more_than_config']),
          'LendSize' => @$product['LendSize'],
          'order' => !empty($product['order']) ? $product['order'] : null,
          'priority' => $product['priority'],
          'payment_daily' => $product['payment_daily'],
          'payment_weekly' => $product['payment_weekly'],
          'payment_biweekly' => $product['payment_biweekly'],
          'payment_monthly' => $product['payment_monthly'],
          'product_status' => 'passed',
          'product_name' => $product['product_name'],
          'manual_lender_leads_status' => $product['manual_lender_leads_status'],
          'product_commision_rate_type' => !empty($commission_rate) ? $commission_rate[0]['percent_type'] : null,
          'product_commision_rate' => !empty($commission_rate) ? $commission_rate[0]['percent'] : null,
          'product_commision_multiple' => count($commission_rate) > 1 ? true : false,
          'interest_type' => $product['interest_type'], // apr interest type only for product
          'apr_value' => $product['apr_value'], // apr interest type only for product need to filter
          'desc' => $product['desc'],
          'orig_fee_type' => $product['orig_fee_type'],
          'orig_fee' => $product['orig_fee'],
          'dd_fee' => $product['dd_fee'],
          'dd_freq' => $product['dd_freq'],
          'calc_method' => $product['calc_method'],
        ], $this->getLenderProductPricing($product));

      }

      $availableOtherProducts = [];
      foreach ($otherProductPerLender as $product) {

        if (!empty($product['intermediary_lender_id']) && ($partner_user['account_type'] == 'Intermediary')) {
          continue;
        }

        // Eliminate lenders with **DO_NOT_SHOW_IN_LENDER_PREFS**
        if (in_array($product['lender_id'], $doNotShowLenderIds)) continue;
        if (in_array($product['lender_id'], array_column($availableProducts, 'lender_id'))) continue;

        $alreadySent = in_array($product['lender_id'], $alreadySentLenderIds)  ? true : false;

        if ($product['intermediary_lender_id']) {
          $intermediary_group = $this->loadModel('IntermediaryLenders')->getInterLender(array('id' => $product['intermediary_lender_id']));
          $product['intermediary_group'] = $intermediary_group['name'];
        }

        // defiane array
        $commission_rate = [];
        if ($product['product_friendly_name'])
          $commission_rate = $this->Lenders->getLenderCommisionRateByProduct($product['shorthand'], $product['product_friendly_name']);

        $availableOtherProducts[] = array_merge([
          'product_id' => $product['lender_product_id'],
          'lender_id' => $product['lender_id'],
          'inter_lender_id' => $product['intermediary_lender_id'],
          'intermediary_group' => $product['intermediary_group'],
          'lender_name' => $product['lender_name'],
          'required_accreditation' => $product['required_accreditation'],
          'email_only' => $product['email_only'],
          'lender_contact_number' => @$product['lender_contact_number'],
          'lender_contact_email' => @$product['lender_contact_email'],
          'shorthand' => $product['shorthand'],
          'lender_logo' => $product['lender_logo'],
          'already_sent' => $alreadySent,
          'duplicate' => !empty($product['duplicate']),
          'sent_more_than_config' => !empty($product['sent_more_than_config']),
          'LendSize' => @$product['LendSize'],
          'order' => !empty($product['order']) ? $product['order'] : null,
          'priority' => $product['priority'],
          'payment_daily' => $product['payment_daily'],
          'payment_weekly' => $product['payment_weekly'],
          'payment_biweekly' => $product['payment_biweekly'],
          'payment_monthly' => $product['payment_monthly'],
          'product_status' => 'passed_other_product',
          'manual_lender_leads_status' => $product['manual_lender_leads_status'],
          'product_commision_rate_type' => $commission_rate[0]['percent_type'],
          'product_commision_rate' => $commission_rate[0]['percent'],
          'product_commision_multiple' => count($commission_rate) > 1 ? true : false,
          'interest_type' => $product['interest_type'], // apr interest type only for product
          'apr_value' => $product['apr_value'], // apr interest type only for product need to filter
          'desc' => $product['desc'],
          'orig_fee_type' => $product['orig_fee_type'],
          'orig_fee' => $product['orig_fee'],
          'dd_fee' => $product['dd_fee'],
          'dd_freq' => $product['dd_freq'],
          'calc_method' => $product['calc_method']
        ], $this->getLenderProductPricing($product));
      }

      $availableFailedProducts = [];









      foreach ($failedCriteriaButProductPerLender as $product) {



        if (!empty($product['intermediary_lender_id']) && ($partner_user['account_type'] == 'Intermediary')) {
          continue;
        }

        // Eliminate lenders with **DO_NOT_SHOW_IN_LENDER_PREFS**
        if (in_array($product['lender_id'], $doNotShowLenderIds)) continue;
        if (in_array($product['lender_id'], array_column($availableProducts, 'lender_id'))) continue;
        if (in_array($product['lender_id'], array_column($availableOtherProducts, 'lender_id'))) continue;

        $alreadySent = in_array($product['lender_id'], $alreadySentLenderIds)  ? true : false;

        if ($product['intermediary_lender_id']) {
          $intermediary_group = $this->loadModel('IntermediaryLenders')->getInterLender(array('id' => $product['intermediary_lender_id']));
          $product['intermediary_group'] = $intermediary_group['name'];
        }

        $commission_rate = [];
        if ($product['product_friendly_name'])
          $commission_rate = $this->Lenders->getLenderCommisionRateByProduct($product['shorthand'], $product['product_friendly_name']);

        $availableFailedProducts[] = array_merge([
          'product_id' => $product['lender_product_id'],
          'lender_id' => $product['lender_id'],
          'inter_lender_id' => $product['intermediary_lender_id'],
          'intermediary_group' => $product['intermediary_group'],
          'lender_name' => $product['lender_name'],
          'required_accreditation' => $product['required_accreditation'],
          'email_only' => $product['email_only'],
          'lender_contact_number' => @$product['lender_contact_number'],
          'lender_contact_email' => @$product['lender_contact_email'],
          'shorthand' => $product['shorthand'],
          'lender_logo' => $product['lender_logo'],
          'already_sent' => $alreadySent,
          'duplicate' => !empty($product['duplicate']),
          'sent_more_than_config' => !empty($product['sent_more_than_config']),
          'LendSize' => @$product['LendSize'],
          'order' => !empty($product['order']) ? $product['order'] : null,
          'priority' => $product['priority'],
          'payment_daily' => $product['payment_daily'],
          'payment_weekly' => $product['payment_weekly'],
          'payment_biweekly' => $product['payment_biweekly'],
          'payment_monthly' => $product['payment_monthly'],
          'product_status' => 'failed_criteria_but_product',
          'manual_lender_leads_status' => $product['manual_lender_leads_status'],
          'product_commision_rate_type' => $commission_rate[0]['percent_type'],
          'product_commision_rate' => $commission_rate[0]['percent'],
          'product_commision_multiple' => count($commission_rate) > 1 ? true : false,
          'interest_type' => $product['interest_type'], // apr interest type only for product
          'apr_value' => $product['apr_value'], // apr interest type only for product need to filter
          'desc' => $product['desc'],
          'orig_fee_type' => $product['orig_fee_type'],
          'orig_fee' => $product['orig_fee'],
          'dd_fee' => $product['dd_fee'],
          'dd_freq' => $product['dd_freq'],
          'calc_method' => $product['calc_method']
        ], $this->getLenderProductPricing($product));
      }

      $maxLenders = Configure::read('Lend.partner.max_num_lender_to_send');

      $leadDupeSales = $this->Sales->getleadDupeSales($lead['lead']['lead_id']);

      // Don't count duplicate sales
      $numOfLendersSendTo = count($saleDetails) - count($leadDupeSales);

      $no_send = $numOfLendersSendTo >= $maxLenders;
      $availableProducts = array_merge(array_merge($availableProducts, $availableOtherProducts), $availableFailedProducts);
      if (!empty($availableProducts) && (empty($partnerIdsInWhileListOfLendSize) || in_array((int)$partnerId, $partnerIdsInWhileListOfLendSize)))
        $availableProducts = $lendSize->setProductsInOrder($availableProducts, $no_send, $partnerId);

      // fetch quotes data
      $selected_proposal_lender_id = null;
      $quoteRefInfo = $this->loadModel('LeadQuotesRef')->getLeadQuoteRef(['lead_id' => $lead_id]);
      if (!empty($quoteRefInfo)) {
        $url = getenv('QUOTES_LOOKUP') . 'quotes/' . $quoteRefInfo['quote_ref'];
        $result = $this->curl($url, '', 'GET', ['x-api-key:' . getenv('QUOTES_API_KEY')]);

        // find selected proposal
        if ($result['success']) {
          $proposals = $result['data']['proposals'];
          $selected_proposal_lender_id = array();
          foreach ($proposals as $key => $proposal) {
            if ($proposal['status'] == 'Selected') {
              // find selected lender
              $proposal_mapping = $this->loadModel('LenderAssetCalcMapping')->getLenderAssetCalcMappings(['asset_calc_product_id' => $proposal['lender_product_id']]);
              if (!empty($proposal_mapping)) {
                $selected_proposal_lender_id = array_merge($selected_proposal_lender_id, array_column($proposal_mapping, 'lender_id'));
              }
            }
          }
        }
      }
      if (!empty($selected_proposal_lender_id)) {
        foreach ($availableProducts as $key => $product) {
          if (in_array($product['lender_id'], $selected_proposal_lender_id)) {
            $temp = $product;
            unset($availableProducts[$key]);
            array_unshift($availableProducts, $temp);
          }
        }
      }




      $data = [
        'leadRef' => $lead_ref,
        'partner_product_type' => !empty($partner_product_type) ? $partner_product_type : null,
        'confirmation' => !empty($confirmation) ? $confirmation : null,
        'maximumLendersNumber' => $this->Leads->convertOneDigitIntoWord($maxLenders),
        'no_send' => $no_send,
        'products' => $availableProducts,
        'selected_proposal_lender_id' => $selected_proposal_lender_id,
        'other_products' => $availableOtherProducts
      ];


      return $this->setJsonResponse(['success' => true, 'data' => $data]);

    } catch (\Exception $e) {
      if(!in_array($e->getMessage(), ['Please login first.',
        'Missing lead ref.',
        'Could not find the lead',
        'On Do Not Call List',
        'Held for review',
                                      'Required Fields Missing'])){
        Log::error($e->getMessage());
        Log::error($e->getTraceAsString());
      }
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()]);
    }
  }

  // we can move to lender matcing later BUT there are lot of dependent function inside //
  public function collectAvailableProductsAccounts($matching_query,
                                                      $account_data,
                                                      $lead_data,
                                                      $init_products = null) {

    $partner_account_meta           =  $account_data['partner_account_meta'];
    $partner_account_bs_analysise   =  $account_data['bs_analysis'];

    $entity_typecode  = $account_data['abn_lookup']['entity_type_code'];
    $industry_detail  = $account_data['partner_account_meta']['industry']['tree'];



    try{
      $this->loadModel('Leads');
      $this->loadModel('PartnerProductTypes');
      $this->loadModel('Sales');
      $this->loadModel('Lenders');

      $lendSize = new LendSize;
      $partnerIdsInWhileListOfLendSize = null;// [1, 2]; //change to null if pre-approval applies for all partners
      $partner_user = $this->Auth->identify();

      $partnerId = $partner_user['partner_id'];

      // $lead_data  = $lead_related_account_mamping['lead'];
      // $lead_other = $lead_related_account_mamping['other_related'];





      $poc_owner = $lead_data['owners_all'][array_search(1, array_column($lead_data['owners_all'], 'point_of_contact'))];

      $poc_owner['home_owner'] = empty($poc_owner['home_owner']) ? 0 : 1;

      $makeDecisionV2 = new MakeDecisionV2;


      $needed_logic_data = ['lead'=> $lead_data['lead'],
                            'lead_owners'=>array($poc_owner),
                            'lead_abn_lookup'=>$lead_data['abn_lookup'],
                            'bank_statements_analysis'=>$partner_account_bs_analysise,
                            'lead_asset_finance' =>$lead_data['assets']];

      $products = $makeDecisionV2->runLenderLogicWithAccountLevel(  $needed_logic_data,
                                                                    array('lead'=>true,
                                                                          'lead_owners'=>true,
                                                                          'bank_statements_analysis'=>!empty($partner_account_bs_analysise),
                                                                          'lead_abn_lookup'=>!empty($lead_data['abn_lookup']),
                                                                          'partners'=>!empty($lead_data['lead']['partner_id']),
                                                                          'lead_asset_finance'=>!empty($lead_data['assets'])),
                                                                    $matching_query, $init_products, true);

      $products = $makeDecisionV2->checkPossibleDupe($products);
      $products = $makeDecisionV2->rank($products,[], true);
      $products = $makeDecisionV2->lender($products,$needed_logic_data);



      if ($partner_account_meta['sales_monthly'] >= 0 ||  $partner_account_bs_analysise['avg_mto_180'] > 0 ){
        $products = $lendSize->calculateLendSizeForAccount($matching_query,$account_data,$products);
      }


      $passedProducts = [];
      $failedProducts = [];
      if(!empty($products)){
        foreach($products as $p){
          if($p['pass'] === 'passed'){
            $passedProducts[] = $p;
          }else{
            $failedProducts[] = $p;
          }
        }
      }


      if(!empty($failedProducts)) $failedProducts = $this->_checkHideAnyway($failedProducts);
      $failedCriteriaButProductMatch = $makeDecisionV2->failedCriteriaButProductMatch($failedProducts);

      // Collect only one product per each lender which are already sorted by priority
      $tmpLenders = [];
      $productPerLender = [];
      if(!empty($passedProducts)){
        foreach ($passedProducts as $product) {
          if (!in_array($product['lender_id'], $tmpLenders)) {
            $productPerLender[] = $product;
            $tmpLenders[] = $product['lender_id'];
          }
        }
      }
      $tmpLenders = []; // reset
      $failedCriteriaButProductPerLender = [];
      if(!empty($failedCriteriaButProductMatch)){
        foreach ($failedCriteriaButProductMatch as $product) {
          if (!in_array($product['lender_id'], $tmpLenders)) {
            $failedCriteriaButProductPerLender[] = $product;
            $tmpLenders[] = $product['lender_id'];
          }
        }
      }

      unset($tmpLenders);
      $inter_lender_ids = [];
      $availableProducts = [];
      foreach ($productPerLender as $product) {

        if(!empty($product['intermediary_lender_id']) && ($partner_user['account_type'] == 'Intermediary')){
          continue;
        }

        if (!empty($product['intermediary_lender_id'])) {
          if (in_array($product['intermediary_lender_id'], $inter_lender_ids)) {
            continue;
          }
          $intermediary_group = $this->loadModel('IntermediaryLenders')->getInterLender(array('id' => $product['intermediary_lender_id']));
          $product['intermediary_group'] = $intermediary_group['name'];
          $inter_lender_ids[] = $product['intermediary_lender_id'];
        }

        // defiane array
        $commission_rate= [];
        if($product['product_friendly_name'])
           $commission_rate = $this->Lenders->getLenderCommisionRateByProduct($product['shorthand'], $product['product_friendly_name']);

        $availableProducts[] = array_merge([
          'product_id' => $product['lender_product_id'],
          'lender_id' => $product['lender_id'],
          'inter_lender_id' => @$product['intermediary_lender_id'],
          'intermediary_group' => @$product['intermediary_group'],
          'email_only' => $product['email_only'],
          'lender_name' => $product['lender_name'],
          'required_accreditation' => $product['required_accreditation'],
          'lender_contact_number' => @$product['lender_contact_number'],
          'lender_contact_email' => @$product['lender_contact_email'],
          'shorthand' => $product['shorthand'],
          'lender_logo' => $product['lender_logo'],
          'duplicate' => !empty($product['duplicate']),
          'sent_more_than_config' => !empty($product['sent_more_than_config']),
          'LendSize' => @$product['LendSize'],
          'order' => !empty($product['order']) ? $product['order'] : null,
          'priority' => $product['priority'],
          'payment_daily' => $product['payment_daily'],
          'payment_weekly' => $product['payment_weekly'],
          'payment_biweekly' => $product['payment_biweekly'],
          'payment_monthly' => $product['payment_monthly'],
          'product_status' => 'passed',
          'product_name' => $product['product_name'],
          'manual_lender_leads_status' => $product['manual_lender_leads_status'],
          'product_commision_rate_type' => @$commission_rate[0]['percent_type'],
          'product_commision_rate' => @$commission_rate[0]['percent'],
          'product_commision_multiple' => count($commission_rate) > 1 ? true : false,
          'interest_type'=> $product['interest_type'], // apr interest type only for product
          'apr_value'=> $product['apr_value'], // apr interest type only for product need to filter
          'desc'=> $product['desc'],
          'orig_fee_type'=> $product['orig_fee_type'],
          'orig_fee'=> $product['orig_fee'],
          'dd_fee'=> $product['dd_fee'],
          'dd_freq'=> $product['dd_freq'],
          'calc_method'=> $product['calc_method'],
          'lvr' => $this->_findLvr($product['criteria']),
          'max_loan_amount' => $this->_findMaxLoanAmount($product['criteria']),
        ], $this->getLenderProductPricing($product));

      }


      $availableFailedProducts = [];

      foreach ($failedCriteriaButProductPerLender as $product) {



        if(!empty($product['intermediary_lender_id']) && ($partner_user['account_type'] == 'Intermediary')){
          continue;
        }

        if (!empty($availableProducts) && in_array($product['lender_id'], array_column($availableProducts, 'lender_id'))) continue;

        if (!empty($product['intermediary_lender_id'])) {
          if (in_array($product['intermediary_lender_id'], $inter_lender_ids)) {
            continue;
          }
          $intermediary_group = $this->loadModel('IntermediaryLenders')->getInterLender(array('id' => $product['intermediary_lender_id']));
          $product['intermediary_group'] = $intermediary_group['name'];
          $inter_lender_ids[] = $product['intermediary_lender_id'];
        }

        $commission_rate= [];
        if (!empty($product['product_friendly_name'])) {
          $commission_rate = $this->Lenders->getLenderCommisionRateByProduct($product['shorthand'], $product['product_friendly_name']);
        }

        $availableFailedProducts[] = array_merge([
          'product_id' => $product['lender_product_id'],
          'lender_id' => $product['lender_id'],
          'inter_lender_id' => @$product['intermediary_lender_id'],
          'intermediary_group' => @$product['intermediary_group'],
          'lender_name' => $product['lender_name'],
          'required_accreditation' => $product['required_accreditation'],
          'email_only' => $product['email_only'],
          'lender_contact_number' => @$product['lender_contact_number'],
          'lender_contact_email' => @$product['lender_contact_email'],
          'shorthand' => $product['shorthand'],
          'lender_logo' => $product['lender_logo'],
          'duplicate' => !empty($product['duplicate']),
          'sent_more_than_config' => !empty($product['sent_more_than_config']),
          'LendSize' => @$product['LendSize'],
          'order' => !empty($product['order']) ? $product['order'] : null,
          'priority' => $product['priority'],
          'payment_daily' => $product['payment_daily'],
          'payment_weekly' => $product['payment_weekly'],
          'payment_biweekly' => $product['payment_biweekly'],
          'payment_monthly' => $product['payment_monthly'],
          'product_status' => 'failed_criteria_but_product',
          'manual_lender_leads_status' => $product['manual_lender_leads_status'],
          'product_commision_rate_type' => @$commission_rate[0]['percent_type'],
          'product_commision_rate' => @$commission_rate[0]['percent'],
          'product_commision_multiple' => count($commission_rate) > 1 ? true : false,
          'interest_type'=> $product['interest_type'], // apr interest type only for product
          'apr_value'=> $product['apr_value'], // apr interest type only for product need to filter
          'desc'=> $product['desc'],
          'orig_fee_type'=> $product['orig_fee_type'],
          'orig_fee'=> $product['orig_fee'],
          'dd_fee'=> $product['dd_fee'],
          'dd_freq'=> $product['dd_freq'],
          'calc_method'=> $product['calc_method'],
          'lvr' => $this->_findLvr($product['criteria']),
          'max_loan_amount' => $this->_findMaxLoanAmount($product['criteria']),
          //'product_failed_criteria_list_all'=> $product['failed'],
          'fail_reason' => $this->getPrirotyReason($product['failed'],['entity_typecode'=>$entity_typecode,'industry_detail'=>$industry_detail ]) // this is the list of failed criteria with factor ids //
         // 'fail_reason'=>$this->getPrirotyReason($product['failed']),
        ], $this->getLenderProductPricing($product));
      }


      $availableProducts = array_merge($availableProducts, $availableFailedProducts);






      if (!empty($availableProducts) && (empty($partnerIdsInWhileListOfLendSize) || in_array((int)$partnerId, $partnerIdsInWhileListOfLendSize)))
        $availableProducts = $lendSize->setProductsInOrder($availableProducts);


      array_multisort(array_column($availableProducts, 'priority'), SORT_ASC, $availableProducts);

      $data = [
        'partner_product_type' => !empty($partner_product_type) ? $partner_product_type : null,
        'confirmation' => !empty($confirmation) ? $confirmation : null,
        'highest_loan_amount' => max(array_column($availableProducts, 'max_loan_amount')),
        'highest_lvr' => max(array_column($availableProducts, 'lvr')),
        'products' => $availableProducts
      ];

      return $data;

    }catch(\Exception $e){
      if(!in_array($e->getMessage(), ['Please login first.',
                                      'Missing lead ref.',
                                      'Could not find the lead',
                                      'On Do Not Call List',
                                      'Held for review',
                                      'Required Fields Missing'])){
        Log::error($e->getMessage());
        Log::error($e->getTraceAsString());
      }
      return $this->setJsonResponse(['success'=>false, 'message'=>$e->getMessage()]);
    }
  }


  public function getPrirotyReason($data,$reason_replace){


    $entity_typecode = !empty($reason_replace['entity_typecode']) ? $reason_replace['entity_typecode'] : 'ABN type code';
    $industry_detail = !empty($reason_replace['industry_detail']) ? $reason_replace['industry_detail'] : 'Industry name';



    foreach($data as $key=>$value) {
        $sort_data[$key] = $value['criteria']['factor_priority'];
    }
    array_multisort($sort_data, SORT_ASC, $data);


    if(strpos($data[0]['criteria']['friendly_fail_reason'], '[entity type]') !== false){
      return str_replace('[entity type]', $entity_typecode, $data[0]['criteria']['friendly_fail_reason']);
    }
    else if(strpos($data[0]['criteria']['friendly_fail_reason'], '[industry_name]') !== false){
      return str_replace('[industry_name]', $industry_detail, $data[0]['criteria']['friendly_fail_reason']);
    }
    else{
      return $data[0]['criteria']['friendly_fail_reason'];
    }

  }


    public function getLenderProductPricing($product){

    $result = [];
    $p = new ProductPricing;
    $lenderProductPricing = TableRegistry::get('LenderProductPricing');
    $lenderPricing = $lenderProductPricing->getProductPricing(['product_id' => $product['lender_product_id']]);

    for ($x = 6; $x <= 36; $x += 6) {
      $apr_info = $p->getTermRateAPR($product['lender_product_id'], $x, $lenderPricing);

      $rate          = $apr_info->rates;
      $display_apr   = $apr_info->display_apr;
      $interest_type = $apr_info->interest_type;

      $result['product_term_' . $x] = !empty($rate) ? $rate : 0;
      $result['product_term_display_' . $x] = !empty($display_apr) ? $display_apr  : 0;
      $result['product_term_interest_type_' . $x] = !empty($interest_type) ? $interest_type  : "";
    }
    return $result;
  }



    private function _leadValidationToSendLead($data, $checkContactOnly = true, $checkAllOwners = false) {
    Log::info('=========_leadValidationToSendLead() ==========');
    Log::info(json_encode($data));
    // Check validation
    $leadValidation = new LeadValidation;
    $leadValidation->check_all_owners = $checkAllOwners;
    //the required fields must be same as the ones on ``**/partners/new-affiliate-lead` page

    //fields below are always required
    $requiredFields['lead_owner'] = ['first_name', 'last_name', 'email'];

    if (!$checkContactOnly) {
      $requiredFields['lead'] = ['purpose_id', 'amount_requested', 'company_registration_date', 'sales_monthly', 'industry_id', 'organisation_name', 'product_type_id'];
      $lead_asset_finance = $this->_getLeadAssetFinance($data['lead']['lead_id']);
      if ($data['lead']['product_type_id'] == 10) {  //if Equipment finance
          if(empty($lead_asset_finance) OR empty($lead_asset_finance['contract_type'])){
          //we need to validate ABN
          if (getenv('REGION', true) === 'au') {
            $requiredFields['abnlookup'] = array();
          }
          $requiredFields['lead']['equipment_id'] = 'equipment_id';
          $requiredFields['lead']['equipment_details'] = 'equipment_details';
          //we need the eqipment fields to be filled
          switch ($data['lead']['equipment_id']) {
            case '':
              unset($data['lead']['equipment_condition']);
              break;
            default:
              $requiredFields['lead']['equipment_condition'] = 'equipment_condition';
              break;
          }
          if (!empty($data['lead']['equipment_found'])) {
            $requiredFields['lead']['equipment_source'] = 'equipment_source';
          }
        }
      }

      if (!empty($data['product_type']['sub_product']) && $data['product_type']['sub_product'] == 10) {
        array_splice($requiredFields['lead'], array_search('sales_monthly', $requiredFields['lead']), 1);
      }

      if (!empty($lead_asset_finance) && !empty($lead_asset_finance['contract_type'])) {
        $data['lead_addresses'] = $this->loadModel('LeadAddresses')->getLeadAddresses(array('lead_id' => $data['lead']['lead_id'], 'status' => 'active', 'address_type' => 'trading'));
        $data['lead_mailing_addresses'] = $this->loadModel('LeadAddresses')->getLeadAddresses(array('lead_id' => $data['lead']['lead_id'], 'status' => 'active', 'address_type' => 'mailing'));
        if (!empty($data['lead_owner'])) {
          foreach ($data['lead_owner'] as $owner_key => $owner) {
            $data['lead_owner'][$owner_key]['lead_owner_addresses'] = $this->loadModel('LeadOwnerAddress')->getLeadOwnerAddresss(array('lead_owner_id' => $owner['owner_id'], 'status' => 'active'));
            $data['lead_owner'][$owner_key]['lead_owner_employments'] = $this->loadModel('LeadOwnerEmployment')->getLeadOwnerEmployments(array('lead_owner_id' => $owner['owner_id'], 'status' => 'active'));
          }
        }
        $requiredFields['lead'][] = 'r_address';
        $requiredFields['lead'][] = 'r_suburb';
        $requiredFields['lead'][] = 'r_state';
        $requiredFields['lead'][] = 'r_postcode';

        // $requiredFields['abnlookup'] = ['entity_type_desc'];
        $requiredFields['lead_owner'][] = 'title';
        $requiredFields['lead_owner'][] = 'dob';
        $requiredFields['lead_owner'][] = 'credit_history';
        $requiredFields['lead_owner'][] = 'lead_owner_addresses';

        $requiredFields['lead_asset_finance'][] = 'equipment_id';
        $requiredFields['lead_asset_finance'][] = 'asset_description';
        $requiredFields['lead_asset_finance'][] = 'year';
        $requiredFields['lead_asset_finance'][] = 'make';
        $requiredFields['lead_asset_finance'][] = 'condition';
        $requiredFields['lead_asset_finance'][] = 'sale_type';

        if (!empty($lead_asset_finance['asset_balloon']) && (int)$lead_asset_finance['asset_balloon'] > 1) {
          $requiredFields['lead_asset_finance'][] = 'balloon_reason';
          $requiredFields['lead_asset_finance'][] = 'intention_after_loan';
          if (!empty($lead_asset_finance['balloon_reason']) && strtolower($lead_asset_finance['balloon_reason']) === 'other') {
            $requiredFields['lead_asset_finance'][] = 'balloon_reason_explanation';
          }
        }

        $requiredFields['lead_addresses'] = [
          'address',
          'suburb',
          'state',
          'postcode',
        ];

        $requiredFields['lead'][] = 'loan_term_requested_months';

        // if (!empty($data['trust']['trustee_type'])) {
        //   if ($data['trust']['trustee_type'] === 'Partnership') {
        //     $requiredFields['trustee'] = ['trustee_ACN'];
        //   } else if ($data['trust']['trustee_type'] === 'Corporate' || $data['trust']['trustee_type'] === 'Individual') {
        //     $requiredFields['trustee'] = ['trustee_type', 'trustee_ACN', 'trustee_ABN', 'trustee_first_name', 'trustee_last_name', 'trustee_name', 'trust_settor'];
        //   }
        // }
      }

      // check Property Value and Mortgage Balance
        if(!empty($data['lead_owner']['home_owner']) AND !empty($data['lead']['product_type_id']) AND ($data['lead']['product_type_id'] == '2' OR $data['lead']['product_type_id'] == '7' OR $data['product_type']['sub_product'] == '7')){
        $requiredFields['lead_owner'][] = 'estimated_value';
        $requiredFields['lead_owner'][] = 'remaining_debt';
      }
    }

    //abn or organisation_name  must not be empty, do that validation in class LeadValidation
    //      $requiredFields['abnlookup'] = array();

    //mobile or phone must not be empty
    if (!empty($data['lead_owner']['mobile']))
      $requiredFields['lead_owner'][] = 'mobile';
    else if (!empty($data['lead_owner']['phone']))
      $requiredFields['lead_owner'][] = 'phone';
    else
      $requiredFields['lead_owner'][] = 'mobile';
    $leadValidation->setRequiredFields($requiredFields);
    Log::info('====($requiredFields');
    Log::info(json_encode($requiredFields));

    $errors = $leadValidation->validate($this->loadModel('Leads')->formatLeadData($data, array('dont_toggle_phonenumbers' => true, 'check_all_owners' => $checkAllOwners))); // Check validation

    return $errors;
  }


    private function _documentValidationToSendLead($data, $leadId){
    $leadValidation = new LeadValidation;
    $data['selected_proposal'] = json_decode(json_encode($data['selected_proposal']), true);
    $data['quote'] = json_decode(json_encode($data['quote']), true);

    $uploads = $this->loadModel('PartnerLeadUploads')->getPartnerLeadUploadsWithMeta($leadId, true);
        $docErrors =  $leadValidation->validateDocumentation($this->loadModel('ProductRequiredDocs')->getRequiredDocs(),
      $data,
            $data['selected_proposal'], $uploads);
    return $docErrors;
  }

  // View lender status history
    public function viewLenderStatusHistory(){
    try {
      $partner_user = $this->Auth->identify();
      $params = $this->request->getData();
      if (empty($partner_user))      throw new \Exception("Please login first.");
      if (empty($params['lead_ref'])) throw new \Exception("Missing lead ref.");
      if ($partner_user['account_type'] === 'Applicant') throw new \Exception("You are not allow to access this.");
      $lead_id = (new LendInternalAuth)->unhashLeadId($params['lead_ref']);

      $history['history'] = $this->loadModel('LenderLeadUpdates')->getLenderStatusHistory($lead_id, $params['lender_id']);
      if (!empty($history['history'])) {
        foreach ($history['history'] as $key => $h) {
          $history['history'][$key]['color'] = $this->_decideColor($h);
          $history['history'][$key]['created'] = !empty($h['created']) ? date('jS M Y, g:ia', strtotime(str_replace('@', '', $h['created']))) : null;
          if (!empty($h['sub_history'])) {
            foreach ($h['sub_history'] as $sub_key => $sub) {
              $history['history'][$key]['sub_history'][$sub_key]['color'] = $this->_decideColor($sub);
              $history['history'][$key]['sub_history'][$sub_key]['created'] = !empty($sub['created']) ? date('jS M Y, g:ia', strtotime(str_replace('@', '', $sub['created']))) : null;
            }
          }
        }
      }
      $lender = $this->loadModel('Lenders')->getLender(['lender_id' => $params['lender_id']]);

      $this->setJsonResponse(['success' => true, 'lender' => $lender, 'history' => $history]);
    } catch (\Exception $e) {
      if (!in_array($e->getMessage(), ['Please login first.', 'Missing lead ref.'])) {
        Log::error($e->getMessage());
        Log::error($e->getTraceAsString());
      }
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()]);
    }
  }


    private function _decideColor($history){
    $color = '';
    $h = !empty($history['history']) ? $history['history'] : $history['partner_status'];
    switch ($history['type']) {
      case 1:
        switch ($h) {
          case strtolower($h) == 'sent':
          case stripos($h, 'new') !== false:
          case stripos($h, 'submitted') !== false:
            $color = 'blue';
            break;
          case stripos($h, 'funded') !== false:
          case stripos($h, 'qualified') !== false:
          case stripos($h, 'settled') !== false:
          case stripos($h, 'approved') !== false:
            $color = 'green';
            break;
          case stripos($h, 'sent') !== false:
          case stripos($h, 'signed') !== false:
          case stripos($h, 'issued') !== false:
            $color = 'purple';
            break;
          case stripos($h, 'success') !== false:
          case stripos($h, 'status') !== false:
            $color = 'purple2';
            break;
          case stripos($h, 'doesn\'t') !== false && !$simple:
          case stripos($h, 'no more') !== false && !$simple:
          case stripos($h, 'missed') !== false && !$simple:
            $color = 'orange';
            break;
          default:
            $color = 'yellow';
        }
        break;

        case 2: case 3: case 5: case 6: $color = 'red'; break;
        case 4: $color = 'green'; break;
        default: $color = 'yellow'; break;
    }
    return $color;
  }

    private function curl($url, $post, $type, $headers = []) {
    $curl = curl_init();
    $entries = ["accept: application/json", "content-type: application/json"];

    foreach ($headers as $header) {
      $entries[] = $header;
    }
    curl_setopt_array($curl, array(
      CURLOPT_URL             => $url,
      CURLOPT_RETURNTRANSFER  => true,
      CURLOPT_ENCODING        => "",
      CURLOPT_MAXREDIRS       => 10,
      CURLOPT_TIMEOUT         => 30,
      CURLOPT_HTTP_VERSION    => CURL_HTTP_VERSION_1_1,
      CURLOPT_CUSTOMREQUEST   => $type,
      CURLOPT_HTTPHEADER      => $entries
    ) + ($type === 'POST' ? array(
      CURLOPT_POST            => true,
      CURLOPT_POSTFIELDS      => json_encode($post),
    ) : array()));

    $result = curl_exec($curl);

    if ($this->isJson($result)) {
      $result = json_decode($result, true);
    }

    return $result;
  }

  private function isJson($string) {
    if (is_object(json_decode($string))) {
      return true;
    } else {
      return false;
    }
  }

  public function getDownloadedFile($info = [])
  {
    $this->Auth->identify();
    $url = 'https://znhmt5qzw5.execute-api.ap-southeast-2.amazonaws.com/production/run';

    if ($this->request->is('post')) {
      $data = $this->request->getData();
      try {
        if (empty($data))
          throw new \Exception('Missing file data');

        $result = $this->curl($url, $data, 'POST');

        if (empty($result))
          throw new \Exception('Could not reach the function temporarily');

        return $this->setJsonResponse(['url' => $result]);
      } catch (\Exception $e) {
        Log::error($e->getMessage());
        Log::error($e->getTraceAsString());
        return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()]);
      }
    } else {
      return $this->internalCall($info, $url);
    }
  }

  private function internalCall($data, $url){
    try {
      if (empty($data))
        throw new \Exception('Missing file data');

      $result = $this->curl($url, $data, 'POST');

      if (empty($result))
        throw new \Exception('Could not reach the function temporarily');

      return $result;
    } catch (\Exception $e) {
      return $e->getMessage();
    }
  }

  /**
   * Organise and validate
   * @param $data - received from quotes app
   * @return array restructured data
   * @throws \Exception
   */
    private function _organiseLeadInfo($data){
    $result = array();
    $leadFields = $this->_formatFieldData($this->loadModel('Leads')->getLeadTableFields());
    $leadOwnerFields = $this->_formatFieldData($this->loadModel('LeadOwners')->getLeadOwnerTableFields());
    $leadAssetsFields = $this->_formatFieldData($this->loadModel('LeadAssetFinance')->getLeadAssetsFinanceTableFields());
    $leadAbnFields = $this->_formatFieldData($this->loadModel('LeadAbnLookup')->getLeadAbnLookUpFields());
    $leadQuoteRefFields = $this->_formatFieldData($this->loadModel('LeadQuotesRef')->getLeadQuoteRefFields());

    if (isset($data['quote_ref'])) {
      $result['quote_ref'] = $this->_fetchValidateFields($data['quote_ref'], $leadQuoteRefFields, 'leadQuote');
    } else {
      throw new \Exception("Missing required property: quote_ref");
    }

    if (isset($data['abn_lookup'])) {
      $data['lead']['abn'] = $data['abn_lookup']['abn'];
      $data['lead']['acn'] = $data['abn_lookup']['acn'];
      $data['lead']['organisation_name'] = $data['abn_lookup']['organisation_name'];
      $data['lead']['business_name'] = $data['abn_lookup']['organisation_name'];
      $data['lead']['company_registration_date'] = $data['abn_lookup']['effective_from'];
      $data['lead']['business_type_abn'] = $data['abn_lookup']['entity_type_desc'];
      $data['lead']['trading_since'] = $data['abn_lookup']['effective_from'];
    }

    if (isset($data['lead'])) {
      $result['lead'] = $this->_fetchValidateFields($data['lead'], $leadFields, 'lead');
    } else {
      throw new \Exception("Missing required property: lead");
    }

    if (isset($data['lead_owner'])) {
      $result['lead_owners'] = $this->_fetchValidateFields($data['lead_owner'], $leadOwnerFields, 'owner');
    } else {
      throw new \Exception("Missing required property: lend_owner");
    }

    if (isset($data['abn_lookup'])) {
      $result['abn_lookup'] = $this->_fetchValidateFields($data['abn_lookup'], $leadAbnFields, 'abnLookup');
    }

    if (isset($data['asset_details'])) {
      $result['asset_details'] = $this->_fetchValidateFields($data['asset_details'], $leadAssetsFields, 'leadAssets');
    } else {
      throw new \Exception("Missing required property: asset_details");
    }

    return $result;

  }

  /**
   * @param $fields - table fields
   * @return array
   */
    private function _formatFieldData($fields){
    $result = [];
    foreach ($fields as $field) {
      $result[$field['COLUMN_NAME']] = $field['DATA_TYPE'];
    }
    return $result;
  }

  /**
   * @param $data - JSON payload
   * @param $schema - table schema
   * @param $flag - which table
   * @return array - validated data
   * @throws \Exception
   */
    private function _fetchValidateFields($data, $schema, $flag){
    $result = array();

    $stringTypes = array('varchar', 'enum');
    $numericTypes = array('int', 'decimal', 'tinyint');
    $booleanTypes = array('tinyint');
    $dateTimeTypes = array('datetime', 'date');

    if ($flag === "leadQuote") {
      if (ctype_alnum($data)) {
        $result['quote_ref'] = $data;
        return $result;
      } else {
        throw new \Exception("Invalid property value: quote_ref");
      }
    }

    $this->_validateMandatoryFields($flag, $data);

    foreach ($data as $key => $val) {
      if (array_key_exists($key, $schema) && !is_null($val) && strlen((string)$val) > 0) {
        if (in_array($schema[$key], $stringTypes)) {
          if (is_string($val)) {
            $result[$key] = $val;
          } else {
            throw new \Exception("Invalid property value: $key");
          }
                }
                else if(in_array($schema[$key], $numericTypes)){
          if (is_numeric($val)) {
            $result[$key] = $val;
          } else {
            throw new \Exception("Invalid property value: $key");
          }
                }
                else if(in_array($schema[$key], $booleanTypes)){
          if (is_bool(filter_var($val, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE))) {
            $result[$key] = $val;
          } else {
            throw new \Exception("Invalid property value: $key");
          }
                }
                else if(in_array($schema[$key], $dateTimeTypes)){
          if (strtotime($val) !== false) {
            $result[$key] = $val;
          } else {
            throw new \Exception("Invalid property value: $key");
          }
        } else {
          throw new \Exception("Invalid property value: $key");
        }

      } else if (is_null($val)) {
        $result[$key] = null;
      }
    }

    return $result;
  }

  /**
   * Validate mandatory fields
   *
   * @param $flag - table
   * @param $data - JSON payload
   * @throws \Exception
   */
    private function _validateMandatoryFields($flag, $data){
    if ($flag === 'lead') {
      if (!isset($data['product_type_id']) || !isset($data['amount_requested']) || !isset($data['loan_term_requested_months'])) {
        throw new \Exception("Mandatory field missing in: lead");
      }
    }
    if ($flag === 'owner') {
      if (!isset($data['first_name']) || !isset($data['last_name']) || !isset($data['email']) || !isset($data['mobile'])) {
        throw new \Exception("Mandatory field missing in: lead_owner");
      } else {
        if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
          throw new \Exception("Invalid email in: lead_owner");
        }
      }
    }

    if ($flag === 'abnLookup') {
      if (!isset($data['abn']))
        throw new \Exception("Mandatory field missing: abn");

      if (!isset($data['effective_from']))
        throw new \Exception("Mandatory field missing: effective_from");

      if (!isset($data['entity_type_desc']))
        throw new \Exception("Mandatory field missing: entity_type_desc");

      //if(!isset($data['gst_effective_from']))
      //throw new \Exception("Mandatory field missing: gst_effective_from");
    }

    if ($flag === 'leadAssets') {
      if (!isset($data['equipment_id']))
        throw new \Exception("Mandatory field missing: equipment_id");

      if (!isset($data['contract_type']))
        throw new \Exception("Mandatory field missing: contract_type");

      if (!isset($data['asset_purchase_price']))
        throw new \Exception("Mandatory field missing: asset_purchase_price");

      if (!isset($data['percent_or_value']))
        throw new \Exception("Mandatory field missing: percent_or_value");

      if (!isset($data['brokerage']))
        throw new \Exception("Mandatory field missing: brokerage");
      /*
            if(!isset($data['supplier_type']))
                throw new \Exception("Mandatory field missing: supplier_type");
            */
    }
  }

  /**
   * @param $data - JSON payload including quote ref and email list
   * @throws \Exception
   */
    private function _validateSendQuoteEmails($data){
    if (!isset($data['owners']))
      throw new \Exception('Missing field: emails');

    if (!isset($data['quote_ref']))
      throw new \Exception('Missing field: quote_ref');

    if (!ctype_alnum($data['quote_ref']))
      throw new \Exception("Invalid property value: quote_ref");

    if (!is_array($data['owners']))
      throw new \Exception("Invalid property value: emails");

  }

  /** Validation
   * @param $leadAssetData
   * @return bool
   */
    private function validateLeadAssetFinance($leadAssetData){
    if (isset($leadAssetData['supplier_email']) && $leadAssetData['supplier_email'] != '') {
      if (!filter_var($leadAssetData['supplier_email'], FILTER_VALIDATE_EMAIL)) {
        return false;
      }
    }

    if (isset($leadAssetData['supplier_contact_number'])) {
      if (!filter_var($leadAssetData['supplier_contact_number'], FILTER_SANITIZE_NUMBER_INT)) {
        return false;
      }
    }

    if (isset($leadAssetData['sale_type'])) {
      if (!filter_var($leadAssetData['sale_type'], FILTER_SANITIZE_STRING)) {
        return false;
      }
    }

    return true;
  }

  /* ************************************************
    Example of the input from `$data['ask_applicants']`:
    {
      ...,
      "ask_applicants": {
        "application": [owner_id1, owner_id2, ...],
        "bs": [owner_id1, owner_id2, ...],
        "consent": [owner_id1, owner_id2, ...]
        "sections": [owner_id1: ['ma', 'l'], owner_id2: ['ma', 'l'], ...]
      },
      ...
    }
    -----------------------------------------------------
    Example of the response:
    {
      "owner_id1": {
        "code": "SendToAppl",
        'sections' : "ma|l"
      },
      "owner_id2": {
        "code": "AppBSRequest"
      },
      "owner_id3": {
        "code": "SendToApplMultiTasks",
        "tasks": ["application", "bs", "consent", "privacy"]
      }
    }
    ************************************************ */
    private function _decideEmailCode($data){
    $this->loadModel('LeadOwners');
    $result = [];
    $grouped = [];

    // For file attchments:
    if (!empty($data['is_attach']) && !empty($data['ownerIds'])) {
      foreach ($data['ownerIds'] as $owner_id) {
        $result[$owner_id] = ['code' => 'AskAttach'];
      }
      return $result;
    }

    if (empty($data['ask_applicants'])) return false;
      $owner_sections = [];
    // Group it by owner_id
    foreach ($data['ask_applicants'] as $task => $owner_ids) {
        if($task == "sections"){
          foreach($owner_ids as $owner_id => $sections){
            $sectionArray = [];
            if(!empty($sections)){
              foreach ($sections as $section => $v){
                if($v){
                  $sectionArray[] = $section;
                }
              }
            }
            $owner_sections[$owner_id] = implode('|', $sectionArray);
          }
        }else{
      foreach ($owner_ids as $owner_id) {
        $grouped[$owner_id][] = $task;
      }
    }
      }

    foreach ($grouped as $owner_id => $tasks) {
      // Decide notifiation code:
      if (count($tasks) === 1) {
        switch (strtolower($tasks[0])) {
            case 'application':   $code = 'SendToAppl'; break;
            case 'bs':            $code = 'AppBSRequest'; break;
            case 'consent':       $code = 'AppConsentRequest'; break;
        }
      } else {
        $code = 'SendToApplMultiTasks';
      }
        $result[$owner_id] = ['code' => $code, 'tasks' => $tasks, 'sections' => $owner_sections[$owner_id]??""];
    }
    return $result;
  }

  /**
   * New POST API for /lead-apis/crb-trans/LEAD_REF
   */
    public function crbTrans($lead_ref=false, $signature=false){
    try {
      $this->request->allowMethod(['POST']);

      if (empty($lead_ref))  return $this->setJsonResponse(['success' => false, 'message' => 'Missing Lead Ref']);
      $lead_id = (new LendInternalAuth)->unhashLeadId($lead_ref);

      $permission_check = $this->checkPermission($lead_id);
      if (!$permission_check['success'] && getenv('LEND_ENV') > 0) return $this->setJsonResponse(['success' => false, 'message' => $permission_check['message']]);

      $data = $this->request->getData();

      if(!empty($data['owner_ref']) && empty($data['lead_owner_id'])) {
        $data['lead_owner_id'] = (new LendInternalAuth)->unhashOwnerId($data['owner_ref']);
        unset($data['owner_ref']);
      }

      if (empty($data['type'])) {
        return $this->setJsonResponse(['success' => false, 'message' => 'Missing Parameters']);
      } else {
        $all_types = $this->loadModel('PartnerCrbTrans')->getTypeEnum();
        if (!in_array($data['type'], $all_types))
          return $this->setJsonResponse(['success' => false, 'message' => 'Incorrect Parameters']);
      }

      if ($data['type'] == 'Company Enquiry' || $data['type'] == 'Company Enquiry - Enriched' || $data['type'] == 'Credit Report') {
        $data['lead_ref'] = $lead_ref;
      }

      if ($data['type'] == 'Consumer Apply' && $data['owner_ref']) {
        $hashids = new Hashids('lead_owners', 7);
        $owner_id = $hashids->decode($data['owner_ref'])[0];
        $data['lead_owner_id'] =  $owner_id;
        unset($data['owner_ref']);
      }
      // map fields
      $fields = $this->crbMapFields($data);
      if($data['type'] == 'Consumer Apply'){
        $data['document'] = $fields['document'];
        unset($fields['document']);
        unset($fields['enquiryAmount']);
      }
      if (count($fields) == 0)
        return $this->setJsonResponse(['success' => false, 'message' => 'Cannot generate the payload']);
      // set cost
      $costs = Configure::read('Lend.ReportCost') ?? [];
      $cost = $costs[$data['type']] ?? null;

      $useOwnCredential = false;
      // create instance
      if ($data['type'] == 'Credit Report'){
        $crb = new CreditorWatchService($data['type']);
      } else {
        $crb = new EquifaxService($data['type']);
        $equifaxCredentials = TableRegistry::getTableLocator()->get('PartnerExtraCredentialsEntity')
          ->getCredentialsDecrypted($this->Auth->user('partner_id'), 'equifax');
        if($equifaxCredentials && $equifaxCredentials->credentials->username && $equifaxCredentials->credentials->password){
          $cost = 0;
          $useOwnCredential = true;
        }
      }

      // expected signature
      $expected = $crb->generateSignature($fields);

      if ($signature) {
        if ($signature != $expected)
          return $this->setJsonResponse(['success' => false, 'error' => 'Invalid Signature', 'message' => 'It looks like the lead\'s details have changed since you confirmed them. Please restart to process.']);

        // if user has to have given consent, only for reports for a PERSON
        if ($data['type'] == 'Commercial Apply' || $data['type'] == 'Consumer Apply') {
          $lead_owner = $this->loadModel('LeadOwners')->getLeadOwner(array('owner_id' => $data['lead_owner_id'], 'status' => 'active'));
          if (empty($lead_owner) || $lead_owner['consent'] == null)
            return $this->setJsonResponse(['success' => false, 'message' => 'User has not given consent yet']);
        }

        // TODO: Debit their bank card / account for the transaction

        // insert into DB
        $partner_crb_tran_id = $this->loadModel('PartnerCrbTrans')->addPartnerCrbTran(['lead_id' => $lead_id, 'service' => $data['type'] == 'Credit Report' ? 'CreditorWatch' : 'Equifax', 'type' => $data['type'], 'cost' => $cost, 'use_own_credential' => $useOwnCredential]);

        $results = $crb->callService($fields, $signature);
        if (empty($results['success'])) {
          $this->loadModel('App')->postToSlack(":rotating_light: CRB Trans ({$data['type']}) error for *lead {$lead_id}*:  ```" . json_encode($results) . "``` ", "lend_errors");
        }
        // update cost
        if (!($results['success'] && !empty($results['data']) && !empty($results['data']['to_charge'])))
          $this->loadModel('PartnerCrbTrans')->updatePartnerCrbTran(['partner_crb_tran_id' => $partner_crb_tran_id, 'cost' => 0]);
        $tran = $this->loadModel('PartnerCrbTrans')->getPartnerCrbTran(['partner_crb_tran_id' => $partner_crb_tran_id]);

        $report = [];
        if ($data['type'] != 'ID Matrix') {
          $report['lead_id'] = $lead_id;
          $report['type'] = $data['type'];
        }
        $report['request_sent'] = json_encode($fields);
        $report['partner_crb_tran_id'] = $partner_crb_tran_id;

        if ($data['type'] == 'Credit Report') {
          $report['identifier_used'] = $fields['client_identifier'];
          if ($results['success']) {
            $report['status'] = 'Complete';
            $report['score'] = $results['data']['score'];
            if ($results['data']['pdfRequestId']) {
              $report['pdf_request_id'] = $results['data']['pdfRequestId'];
            }
            // $report['pdf_path'] = $results['pdf_path']; // TODO: not ready yet
          } else {
            $report['status'] = 'Error'; // maybe 'Pending' ???
          }
          $this->loadModel('PartnerCrbCwReports')->addPartnerCrbCwReport($report);
        } else {
          if ($data['type'] == 'Commercial Apply' || $data['type'] == 'ID Matrix' || $data['type'] == 'Consumer Apply')
            $report['lead_owner_id'] = $data['lead_owner_id'];
          $report['identifier_used'] = $fields['clientIdentifier'];
          if ($data['type'] != 'ID Matrix') {
            $statusMap = [
              'pending' => 'Pending',
              'in_progress' => 'Pending',
              'inprogress' => 'Pending',
              'complete' => 'Complete',
              'error' => 'Error',
              'fail' => 'Failed'
            ];
            $report['status'] = $statusMap[strtolower($results['status'])];
          } else {
            $decision = 'Error';
            $isComplete = $results['success'] && strtolower($results['status']) === 'complete';
            if ($isComplete)
              $decision = $results['data']['outcome'] ? 'Passed' : 'Failed';
            $report['decision'] = $decision;
            $report['document'] = $data['document'];
          }

          if(is_string($results['error'])){
            $resultError = json_decode($results['error'], true);
            if (json_last_error() === JSON_ERROR_NONE) {
              $results['error'] = $resultError ;
            }
          }
          

          if ($results['success']) {
            if ($data['type'] != 'ID Matrix') {
              $report['pdf_path'] = $results['data']['s3Location'];
              $report['score'] = $results['data']['score'] ?? $results['data']['vedaScore'];
              $report['comprehensive_score'] = $results['data']['comprehensiveScore'];
            }
            if ($data['type'] == 'Consumer Apply') {
              $report['score'] = $results['data']['score_nr'];
              $report['comprehensive_score'] = $results['data']['score_cr'];
              $report['one_score'] = $results['data']['one_score'];
            }
          }else{
            $report['error'] = json_encode($results['error']);
          }

          if ($data['type'] != 'ID Matrix') {
            $this->loadModel('PartnerCrbEfReports')->addPartnerCrbEfReport($report);
          } else {
            $this->loadModel('PartnerCrbIdcheck')->addPartnerCrbIdcheck($report);
          }
        }
        // generate `lead_credit_scores` data:
        $this->getTableLocator()->get('LeadCreditScoreEntity')->addCreditScore($report, @$results['data']['negativeScore']);
        if(!$results['success'] OR (!empty($report['status']) AND $report['status']=='Failed'))
          return $this->setJsonResponse([
            'success' => false, 
            'message' => !empty($results['error']) ? (!empty($results['error']['message']) ? ucfirst(strtolower($results['error']['message'])) : $results['error']) : $results['message'],
            'error_type' => !empty($results['error']['error_type']) ? $results['error']['error_type'] : null
          ]);

        return $this->setJsonResponse(['success' => true, 'result' => ['transaction' => $tran, 'data' => $report, 'result' => $results]]);

      } else {
        // check client identifier exists
        $existIdCheck = null;
        if($data['type'] == 'ID Matrix' && !empty($fields['clientIdentifier'])){
          $existIdCheck = TableRegistry::getTableLocator()->get('PartnerCrbIdcheckEntity')
                          ->find()->select(['partner_crb_idcheck_id'])
                          ->where(['identifier_used' => $fields['clientIdentifier']])
                          ->first();
          if ($existIdCheck) {
            $cost = 0;
          }
        }
        return $this->setJsonResponse(['success' => true, 'signature' => $expected, 'details_to_check' => $fields, 'cost' => $cost, 'existIdCheck'=> !empty($existIdCheck)]);
      }
    } catch (\Exception $e) {
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()]);
    }
  }

  public function createDocusignTrans($lead_ref = false, $consumer = false)
  {
    try {
      $this->request->allowMethod(['POST']);
      if (empty($lead_ref))  return $this->setJsonResponse(['success' => false, 'message' => 'Missing Lead Ref']);
      $lead_id = (new LendInternalAuth)->unhashLeadId($lead_ref);

      $data = $this->request->getData();
      $fields = $data;

      $data['template_ids'] = $this->loadModel('PartnerRequestedPrivacyForms')->getTemplateFromPayload($fields);
      $lenderData = TableRegistry::getTableLocator()
        ->get('CommPrivacyLenderListEntity')
        ->getCustomBusinessListFieldForDocusignFormatted();

      $data['extra_fields'] = !empty($data['extra_fields']) ? array_merge($data['extra_fields'], $lenderData) : $lenderData;
      if ($consumer) {
        // TEMP WHILE PETER DEVELOPING
        // YOU SHOULD GET THIS DATA PASSED IN VIA FRONT END
        $ConPartnerUserSettings = $this->loadModel('ConPartnerUserSettings')->getPartnerUserConsumerSetting(array('partner_user_id' => $data['partner_id']));
        $data['con_partner_user_settings'] = $ConPartnerUserSettings;
      }

      // $totalEnvelopeCost = $data['total_envelope_cost']; DO NOT USE PRICE FROM FRONT END
      $totalEnvelopeCost = Configure::read('Lend.pf_delivery_costs.docusign_envelope');
      unset($data['partner_id']);
      unset($data['partner_user_id']);
      unset($data['total_envelope_cost']);

      if ($this->validateTemplateIds($data['template_ids'])) {
        $sign = new SignatureService('new_envelope');

        $new_envelope = $sign->callService($data);

      } else {
        return $this->setJsonResponse(['success' => false, 'message' => 'Invalid Template Ids']);
      }

      if ($new_envelope['success']) {
        // Add records to lend.crm.partner_requested_privacy_forms
        $fields['lead_id'] = $lead_id;

        if ($consumer) {
          $results = $this->loadModel('ConCreditGuideQuote')->createConCreditGuideQuote($fields, $lead_id);
        } else {
          $results = $this->loadModel('PartnerRequestedPrivacyForms')->createPartnerRequestedPrivacyForms($fields);
        }

        $formIds = $results['ids'];

        if ($consumer) {
          $this->loadModel('ConCreditGuideQuote')->updateConCreditGuideQuoteByFormId(null, array('id' => $formIds[0]));

          $billIds = $this->loadModel('DocusignTrans')->addDocusignTrans($formIds, $fields, $new_envelope, $totalEnvelopeCost);

          return $this->setJsonResponse(['success'=>true, 'result'=> ['new_envelope' => $new_envelope['data'][0],
            'forms' => $formIds, 'bill_ids' => $billIds]]);

        } else {
          $this->updateRequestedPrivacyForm($new_envelope['data'][0]['envelopeId'], $formIds);

          // Add record to lend.crm.partner_requested_privacy_form_trans
          $billIds = $this->loadModel('PartnerRequestedPrivacyFormTrans')->addPartnerRequestedPrivacyFormTrans($formIds, $totalEnvelopeCost);

          return $this->setJsonResponse(['success'=>true, 'result'=> ['new_envelope' => $new_envelope['data'][0],
            'requested_privacy_form' => $formIds, 'bill_ids' => $billIds]]);
        }
      } else {
        return $this->setJsonResponse(['success' => false, 'message' => $new_envelope['error']]);
      }

    } catch (\Exception $e) {
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()]);
    }
  }

  /**
   * New GET & POST API's for /lead-apis/crb-idcheck/LEAD_REF
   * New POST for             /lead-apis/crb-idcheck/check-missing-fields
   */
  public function crbIdcheck($lead_ref=false){
    try {
      $this->request->allowMethod(['GET', 'POST']);

      if (empty($lead_ref)) return $this->setJsonResponse(['success' => false, 'message' => 'Missing Lead Ref']);

      if ($lead_ref === 'check-missing-fields') {
        if ($this->request->is(['POST'])) {
          $data = $this->request->getData();
          if (empty($data['document']) || empty($data['lead_owner_id']))
            return $this->setJsonResponse(['success' => false, 'message' => 'Missing Parameters']);

          // map fields
          $data['type'] = 'ID Matrix';
          $fields = $this->crbMapFields($data);
          if (count($fields) == 0)
            return $this->setJsonResponse(['success' => false, 'message' => 'Cannot generate the payload']);

          // create instance
          $crb = new EquifaxService($data['type'], $data['document']);

          // check missing fields
          $check = $crb->checkRequiredFields($fields);
          if (!$check['pass'])
            return $this->setJsonResponse(array('success' => true, 'missing_fields' => true));
          else
            return $this->setJsonResponse(array('success' => true, 'missing_fields' => false));
        }
        return $this->setJsonResponse(['success' => false, 'message' => 'Invalid Request Method.']);
      } else {
        $lead_id = (new LendInternalAuth)->unhashLeadId($lead_ref);

        $permission_check = $this->checkPermission($lead_id);
        if (!$permission_check['success']) return $this->setJsonResponse(['success' => false, 'message' => $permission_check['message']]);

        $results = [];
        if ($this->request->is(['POST'])) {
          $data = $this->request->getData();
          if (!empty($data['partner_crb_idcheck']['partner_crb_idcheck_id'])) {   // update
            $this->loadModel('PartnerCrbIdcheck')->updatePartnerCrbIdcheck($data['partner_crb_idcheck']);
            }
            else{                                                                 // insert
            $this->loadModel('PartnerCrbIdcheck')->addPartnerCrbIdcheck($data['partner_crb_idcheck']);
          }
        }
        // Should always return
        
        $results = TableRegistry::getTableLocator()->get('PartnerCrbIdcheckEntity')->find('all', 
          [
            'contain' => ['PartnerCrbTransEntity', 'LeadOwnersEntity'],
            'conditions' => [
                'PartnerCrbTransEntity.lead_id' => $lead_id, 
                'LeadOwnersEntity.lead_id' => $lead_id
            ],
          ])
          ->orderDesc('PartnerCrbIdcheckEntity.partner_crb_idcheck_id')
          ->toArray();

        return $this->setJsonResponse(['success' => true, 'data' => $results]);
      }
    } catch (\Exception $e) {
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()]);
    }
  }

  /**
   * New GET & POST API's for /lead-apis/crb-reports/LEAD_REF
   * New POST for             /lead-apis/crb-reports/check-missing-fields
   * New POST API for         /lead-apis/crb-reports/view-pdf
   */
    public function crbReports($lead_ref=false){
    try {
      $this->request->allowMethod(['GET', 'POST']);

      if (empty($lead_ref))  return $this->setJsonResponse(['success' => false, 'message' => 'Missing Parameters']);

      if ($lead_ref === 'check-missing-fields') {
        if ($this->request->is(['POST'])) {
          $data = $this->request->getData();

          if ($data['type'] == 'Consumer Apply' && $data['owner_ref']) {
            $hashids = new Hashids('lead_owners', 7);
            $owner_id = $hashids->decode($data['owner_ref'])[0];
            $data['lead_owner_id'] =  $owner_id;
          }

          if (empty($data['type']) || (empty($data['lead_ref']) && empty($data['lead_owner_id'])))
            return $this->setJsonResponse(['success' => false, 'message' => 'Missing Parameters']);

          $all_types = $this->loadModel('PartnerCrbTrans')->getTypeEnum();
          if (!in_array($data['type'], $all_types))
            return $this->setJsonResponse(['success' => false, 'message' => 'Incorrect Parameters']);

          // map fields
          $fields = $this->crbMapFields($data);
          if (count($fields) == 0)
            return $this->setJsonResponse(['success' => false, 'message' => 'Cannot generate the payload']);

          // create instance
          if ($data['type'] == 'Credit Report')
            $crb = new CreditorWatchService($data['type']);
          else
            $crb = new EquifaxService($data['type']);
          // check missing fields
          $check = $crb->checkRequiredFields($fields);
          if (!$check['pass']){
            $missing_fields = [];
            if($data['type'] == 'Consumer Apply' && $data['owner_ref']){
              $missing_fields = $crb->mapRequiredFields($check['missing_fields'], $data['owner_ref'], $fields['firstName']." ".$fields['lastName']);
            }
            return $this->setJsonResponse(array('success' => true, 'missing_fields' => true, 'fields' => $missing_fields));
          }else{
            return $this->setJsonResponse(array('success' => true, 'missing_fields' => false));
          }
        }
        return $this->setJsonResponse(['success' => false, 'message' => 'Invalid Request Method.']);
      } elseif($lead_ref === 'view-pdf') {
        if ($this->request->is(['POST'])) {
          $data = $this->request->getData();
          if (empty($data['partner_crb_ef_report_id']) && empty($data['partner_crb_cw_report_id']))
            return $this->setJsonResponse(['success' => false, 'message' => 'Missing Parameters']);

          if (!empty($data['partner_crb_ef_report_id']))
            $report = $this->loadModel('PartnerCrbEfReports')->getPartnerCrbEfReport(array('partner_crb_ef_report_id' => $data['partner_crb_ef_report_id']));
          else
            $report = $this->loadModel('PartnerCrbCwReports')->getPartnerCrbCwReport(array('partner_crb_cw_report_id' => $data['partner_crb_cw_report_id']));

          if (empty($report))  return $this->setJsonResponse(['success' => false, 'message' => 'Cannot find the report']);

          if (empty($report['pdf_path'])) { // if pdf_path not exist in db
            if (!$data['type']) $data['type'] = $report['type'];
            
            if ($data['type'] != 'Credit Report' && $data['type'] != 'ID Matrix') {
              $crb = new EquifaxService($data['type']);
              $lead = $this->loadModel('Leads')->getLead(['lead_id' => $report['lead_id']]);
              $data['lead_ref'] = $lead['lead_ref'];
              $data['lead_owner_id'] = $report['lead_owner_id'];
              $fields = $this->crbMapFields($data);

              if($data['type'] != "Consumer Apply"){
                $pdf_fields = ['clientIdentifier' => $report['identifier_used'], 'product' => (!strpos($data['type'], " - ") ? $data['type'] : substr($data['type'], 0, strpos($data['type'], " - ")))];
                $result = $crb->callService($pdf_fields, $crb->generateSignature($pdf_fields), '/get-pdf');
                if ($result['success'] && !empty($result['data']['full_path'])) {
                  $this->loadModel('PartnerCrbEfReports')->updatePartnerCrbEfReport(array('partner_crb_ef_report_id' => $data['partner_crb_ef_report_id'], 'pdf_path' => $result['data']['full_path']));
                  $url = $this->loadModel('LeadUploads')->createSignedRequest($result['data']['full_path'], '2');   // access 2 mins for now
                  return $this->setJsonResponse(array('success' => true, 'pdf_path' => $result['data']['full_path'], 'temp_access' => $url));
                }
              }else{
                $result = $crb->callService(['clientIdentifier' => $report['identifier_used']], '', '/getScoreLend');
                if ($result['success'] && !empty($result['data']['s3Location'])) {
                  $this->loadModel('PartnerCrbEfReports')->updatePartnerCrbEfReport(array('partner_crb_ef_report_id' => $data['partner_crb_ef_report_id'], 'pdf_path' => $result['data']['s3Location']));
                  $url = $this->loadModel('LeadUploads')->createSignedRequest($result['data']['s3Location'], '2');   // access 2 mins for now
                  return $this->setJsonResponse(array('success' => true, 'pdf_path' => $result['data']['s3Location'], 'temp_access' => $url));
                }
              }
            } elseif ($data['type'] === 'Credit Report') {
              if ($report['pdf_request_id']) {
                $crb = new CreditorWatchService($data['type']);
                $pdf_fields = ["pdf_request_id" => $report['pdf_request_id']];
                $result = $crb->callService($pdf_fields, $crb->generateSignature($pdf_fields), '/getPdf');

                if ($result['success'] && !empty($result['data'])) {
                  $file = $result['data'];
                  $fields = array('partner_crb_cw_report_id' => $report['partner_crb_cw_report_id'], 'pdf_path' => $file);
                  $this->loadModel('PartnerCrbCwReports')->updatePartnerCrbCwReport($fields);

                  $url = $this->loadModel('LeadUploads')->createSignedRequest($file, '2');
                  return $this->setJsonResponse(array('success' => true, 'pdf_path' => $file, 'temp_access' => $url));
                }

              }

            }
            $reportType = $report['type'] === 'Credit Report' ? $report['type'] : $report['type'] . ' report';
            $pdfMessage = 'The ' . $reportType . ' may take some time to appear, try again later.';
            return $this->setJsonResponse(['success' => false, 'message' => $pdfMessage]);
          } else {
            $url = $this->loadModel('LeadUploads')->createSignedRequest($report['pdf_path'], '2');   // access 2 mins for now
          }

          return $this->setJsonResponse(array('success' => true, 'pdf_path' => $report['pdf_path'], 'temp_access' => $url));
        }
        return $this->setJsonResponse(['success' => false, 'message' => 'Invalid Request Method.']);
      } else {
        $lead_id = (new LendInternalAuth)->unhashLeadId($lead_ref);

        $permission_check = $this->checkPermission($lead_id);
        if (!$permission_check['success'] && getenv('LEND_ENV') > 0) return $this->setJsonResponse(['success' => false, 'message' => $permission_check['message']]);

        $results = [];
        if ($this->request->is(['POST'])) {
          $data = $this->request->getData();
          if (!empty($data['partner_crb_ef_reports'])) {
            if (!empty($data['partner_crb_ef_reports']['partner_crb_ef_report_id'])) {   // update
              $this->loadModel('PartnerCrbEfReports')->updatePartnerCrbEfReport($data['partner_crb_ef_reports']);
            } else {                                                                 // insert
              $this->loadModel('PartnerCrbEfReports')->addPartnerCrbEfReport($data['partner_crb_ef_reports']);
            }
          } elseif (!empty($data['partner_crb_cw_reports'])){
            if (!empty($data['partner_crb_cw_reports']['partner_crb_cw_report_id'])) {   // update
              $this->loadModel('PartnerCrbCwReports')->updatePartnerCrbCwReport($data['partner_crb_cw_reports']);
            } else {                                                                 // insert
              $this->loadModel('PartnerCrbCwReports')->addPartnerCrbCwReport($data['partner_crb_cw_reports']);
            }
          }
        }
        // Should always return
        $ef_reports = $this->loadModel('PartnerCrbEfReports')->getPartnerCrbEfReports(array('lead_id' => $lead_id));
        $results['Equifax'] = [];
        foreach ($ef_reports as $ef_report) {
          if($ef_report['status'] == "Deleted") continue; 
          $ef_report['identifier_is_still_current'] = $this->generateCRBIdentifiers($ef_report['lead_owner_id'], $lead_ref, $data['type'] == "Consumer Apply") === $ef_report['identifier_used'];
          $ef_report['transaction'] = $this->loadModel('PartnerCrbTrans')->getPartnerCrbTran(['partner_crb_tran_id' => $ef_report['partner_crb_tran_id']]);
          $hashids = new Hashids('lead_owners', 7);
          $ef_report['owner_ref'] = $hashids->encode($ef_report['lead_owner_id']);
          array_push($results['Equifax'], $ef_report);
        }
        $cw_reports = $this->loadModel('PartnerCrbCwReports')->getPartnerCrbCwReports(array('lead_id' => $lead_id));
        $results['CreditorWatch'] = [];
        foreach ($cw_reports as $cw_report) {
          $cw_report['identifier_is_still_current'] = $this->generateCRBIdentifiers($cw_report['lead_owner_id'], $lead_ref) === $cw_report['identifier_used'];
          $cw_report['transaction'] = $this->loadModel('PartnerCrbTrans')->getPartnerCrbTran(['partner_crb_tran_id' => $cw_report['partner_crb_tran_id']]);
          array_push($results['CreditorWatch'], $cw_report);
        }

        return $this->setJsonResponse(['success' => true, 'data' => $results]);
      }
    } catch (\Exception $e) {
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()]);
    }
  }

    private function crbMapFields($data){
    $results = [];
    switch ($data['type']) {
      case 'Commercial Apply':              // Equifax
      case 'Consumer Apply':
        if (!empty($data['lead_owner_id'])) {
          $lead_owner = $this->loadModel('LeadOwners')->getLeadOwner(array('owner_id' => $data['lead_owner_id']));
          $lead_owner_addresses = $this->loadModel('LeadOwnerAddress')->getLeadOwnerAddresss(['lead_owner_id' => $data['lead_owner_id'], 'status' => 'active']);
          $lead = $this->loadModel('Leads')->getLead(['lead_id' => $lead_owner['lead_id']]);
          $lead_asset_finance = $this->loadModel('LeadAssetFinance')->getLeadAssetFinance(array('lead_id' => $lead['lead_id']));
          if (!empty($lead_owner['title'])) $results["title"] = $lead_owner['title'];
          $results["firstName"] = $lead_owner['first_name'];
          $results["lastName"] = $lead_owner['last_name'];
          if (!empty($lead_owner['middle_name'])) $results["middleName"] = $lead_owner['middle_name'];
          $genderInitial = !empty($lead_owner['gender']) ? strtoupper($lead_owner['gender'][0]) : 'U';
          $results["gender"] = in_array($genderInitial, ['M', 'F']) ? $genderInitial : 'U';
          $results["dob"] = $lead_owner['dob'];
          $results["enquiryAmount"] = $lead['amount_requested'];
          $results["clientIdentifier"] = $this->generateCRBIdentifiers($data['lead_owner_id'], $lead['lead_ref'], $data['type'] == "Consumer Apply");
          $results["currentAddress"] = [];
          // $results["previousAddress"] = [];

          if (!empty($lead_asset_finance['contract_type']) || $data['type'] == 'Consumer Apply' || $data['type'] == 'Commercial Apply') { // Use $lead_owner_addresses when asset finance
            foreach ($lead_owner_addresses as $lead_owner_address) {
              if ($lead_owner_address['date_to'] == null) {
                $results["currentAddress"]['unformatted'] = $lead_owner_address['address'] . (isset($lead_owner_address['suburb']) && !empty($lead_owner_address['suburb']) ? ', ' . $lead_owner_address['suburb'] : '') . (isset($lead_owner_address['state']) && !empty($lead_owner_address['state']) ? ', ' . $lead_owner_address['state'] : '') . (isset($lead_owner_address['postcode']) && !empty($lead_owner_address['postcode']) ? ', ' . $lead_owner_address['postcode'] : '');
              }
              // else{
              //   $address = [];
              //   $address['unformatted'] = $lead_owner_address['address'] . (isset($lead_owner_address['suburb']) && !empty($lead_owner_address['suburb']) ? ', '.$lead_owner_address['suburb'] : '') . (isset($lead_owner_address['state']) && !empty($lead_owner_address['state']) ? ', '.$lead_owner_address['state'] : '') . (isset($lead_owner_address['postcode']) && !empty($lead_owner_address['postcode']) ? ', '.$lead_owner_address['postcode'] : '');
              //   array_push($results["previousAddress"], $address);
              // }
            }
            if(!isset($results["currentAddress"]['unformatted'])){
              $results["currentAddress"]['unformatted'] = $lead_owner['address'] . (isset($lead_owner['suburb']) && !empty($lead_owner['suburb']) ? ', ' . $lead_owner['suburb'] : '') . (isset($lead_owner['state']) && !empty($lead_owner['state']) ? ', ' . $lead_owner['state'] : '') . (isset($lead_owner['postcode']) && !empty($lead_owner['postcode']) ? ', ' . $lead_owner['postcode'] : '');
            }
          } else {
            $results["currentAddress"]['unformatted'] = $lead_owner['address'] . (isset($lead_owner['suburb']) && !empty($lead_owner['suburb']) ? ', ' . $lead_owner['suburb'] : '') . (isset($lead_owner['state']) && !empty($lead_owner['state']) ? ', ' . $lead_owner['state'] : '') . (isset($lead_owner['postcode']) && !empty($lead_owner['postcode']) ? ', ' . $lead_owner['postcode'] : '');
          }

          if($data['type'] == 'Consumer Apply'){
            if ($lead_owner['driving_licence_num'] && $lead_owner['driving_licence_state']) {
              $results["driversLicense"] = $lead_owner['driving_licence_num'];
              $results["driversLicenseState"] = $lead_owner['driving_licence_state'];
              $results["driversLicenseCardNumber"] = $lead_owner['driving_licence_card_number'];
              $results["document"] = "Driving Licence";
            }elseif ($lead_owner['passport_number'] && $lead_owner['passport_country'] && $lead_owner['passport_expiry']) {
              $results["passport"] = [];
              $results["passport"]["number"] = $lead_owner['passport_number'];
              $results["passport"]["countryCode"] = $lead_owner['passport_country'];
              $results["passport"]["expiry"] = $lead_owner['passport_expiry'];
              $results["document"] = "Passport";
            }elseif ($lead_owner['medicare_number'] && $lead_owner['medicare_ref']) {
              $results["medicare"] = [];
              $results["medicare"]["cardNumber"] = $lead_owner['medicare_number'];
              $results["medicare"]["referenceNumber"] = $lead_owner['medicare_ref'];
              $results["document"] = "Medicare";
            }
          }
        }
        break;

      case 'Company Enquiry':               // Equifax
        if (!empty($data['lead_ref'])) {
          $lead = $this->loadModel('Leads')->getLead(['lead_ref' => $data['lead_ref']]);
          $results["companyName"] = $lead['organisation_name'];
          $results["companyNumber"] = $lead['acn'];
          $results["enquiryAmount"] = $lead['amount_requested'];
          $results["enriched"] = 'no';
          $results["clientIdentifier"] = $this->generateCRBIdentifiers(null, $data['lead_ref']);
        }
        break;

      case 'Company Enquiry - Enriched':    // Equifax
        if (!empty($data['lead_ref'])) {
          $lead = $this->loadModel('Leads')->getLead(['lead_ref' => $data['lead_ref']]);
          $results["companyName"] = $lead['organisation_name'];
          $results["companyNumber"] = $lead['acn'];
          $results["enquiryAmount"] = $lead['amount_requested'];
          $results["enriched"] = 'yes';
          $results["clientIdentifier"] = $this->generateCRBIdentifiers(null, $data['lead_ref']);
        }
        break;

      case 'ID Matrix':                     // Equifax
        if (!empty($data['lead_owner_id'])) {
          $lead_owner = $this->loadModel('LeadOwners')->getLeadOwner(array('owner_id' => $data['lead_owner_id']));
          $lead_owner_addresses = $this->loadModel('LeadOwnerAddress')->getLeadOwnerAddresss(['lead_owner_id' => $data['lead_owner_id'], 'status' => 'active']);
          $lead = $this->loadModel('Leads')->getLead(['lead_id' => $lead_owner['lead_id']]);
          $lead_asset_finance = $this->loadModel('LeadAssetFinance')->getLeadAssetFinance(array('lead_id' => $lead['lead_id']));

          switch ($data['document']) {
            case IDDocumentType::DrivingLicence:
              $results["driversLicense"] = $lead_owner['driving_licence_num'];
              $results["driversLicenseState"] = $lead_owner['driving_licence_state'];
              $results["driversLicenseCardNumber"] = $lead_owner['driving_licence_card_number'];
              break;
            case IDDocumentType::Passport:
              $results["passport"] = [
                "number" => $lead_owner['passport_number'],
                "countryCode" => $lead_owner['passport_country'],
                "expiry" => $lead_owner['passport_expiry'],
              ];
              break;
            case IDDocumentType::Medicare:
              // Need to combine medicare and driving license for testing purpose if not production
              if (in_array(getenv('LEND_ENV'), [0, 1])) {
                $results["driversLicense"] = $lead_owner['driving_licence_num'];
                $results["driversLicenseState"] = $lead_owner['driving_licence_state'];
                $results["driversLicenseCardNumber"] = $lead_owner['driving_licence_card_number'];
              }
              
              $results["medicare"] = [
                'cardNumber' => $lead_owner['medicare_number'],
                'referenceNumber' => $lead_owner['medicare_ref'],
                'middleNameOnCard' => $lead_owner['medicare_card_middle_name'],
                'expiryDate' => $lead_owner['medicare_card_colour'] == 'Green' ? date('Y-m', strtotime($lead_owner['medicare_expiry'])) : date('Y-m-d', strtotime($lead_owner['medicare_expiry'])),
                'color' => strtoupper(substr($lead_owner['medicare_card_colour'], 0, 1)),
              ];
              break;
          }

          $results["firstName"] = $lead_owner['first_name'];
          $results["lastName"] = $lead_owner['last_name'];
          $results["middleName"] = $lead_owner['middle_name'] ? $lead_owner['middle_name'] : null;
          $results["gender"] = $lead_owner['gender'];

          $results["dob"] = $lead_owner['dob'];
          $results["clientIdentifier"] = $this->generateCRBIdentifiers($data['lead_owner_id'], $lead['lead_ref'], false, $data['document']);
          $results['document'] = $data['document'];
          $results["currentAddress"] = [];

          foreach ($lead_owner_addresses as $lead_owner_address) {
            if ($lead_owner_address['date_to'] == null) {
              if ($lead_owner_address['address'] != null)
                $results["currentAddress"]['unformatted'] = $lead_owner_address['address'] . (isset($lead_owner_address['suburb']) && !empty($lead_owner_address['suburb']) ? ', ' . $lead_owner_address['suburb'] : '') . (isset($lead_owner_address['state']) && !empty($lead_owner_address['state']) ? ' ' . $lead_owner_address['state'] : '') . (isset($lead_owner_address['postcode']) && !empty($lead_owner_address['postcode']) ? ' ' . $lead_owner_address['postcode'] : '');
            }
          }

        }
        break;

      case 'Credit Report':                 // CreditorWatch
        if (!empty($data['lead_ref'])) {
          $lead = $this->loadModel('Leads')->getLead(['lead_ref' => $data['lead_ref']]);
          $results["companyName"] = $lead['organisation_name'];
          $results["abn"] = $lead['abn'];
          $results["client_identifier"] = $this->generateCRBIdentifiers(null, $data['lead_ref']);
          if (!empty($data['force_refresh']))  $results["force_refresh"] = true;
        }
        break;
    }
    return $results;
  }

  private function generateCRBIdentifiers($lead_owner_id, $lead_ref, $is_consumer = false, $document_type = null){
    $lead = $this->loadModel('Leads')->getLead(['lead_ref' => $lead_ref]);
    $identifiers = '';
    if ($lead_owner_id == null) {    // Company Identifiers
      if (!empty($lead['partner_id']) && !empty($lead['abn']))
        $identifiers = $lead['partner_id'] . '-' . preg_replace('/\s+/', '', $lead['abn']);
    } else {                          // Person Identifiers
      $lead_owner = $this->loadModel('LeadOwners')->getLeadOwner(array('owner_id' => $lead_owner_id));
      $address = $this->loadModel('LeadOwnerAddress')->getLeadOwnerAddresss(['lead_owner_id' => $lead_owner_id, 'status' => 'active'], ['lead_owner_address_id' => 'desc'])[0];
      if (empty($lead_owner['state']))
        $state = $address['state'];
      else
        $state = $lead_owner['state'];

      if(empty($state)) $state = $lead_owner['country'];

      if (!empty($lead['partner_id']) && !empty($lead_owner['first_name']) && !empty($lead_owner['last_name']) && !empty($lead_owner['dob']))
        $identifiers = $lead['partner_id'] 
                      . '-' . preg_replace('/\s+/', '', $lead_owner['first_name']) 
                      . '-' . preg_replace('/\s+/', '', $lead_owner['last_name']) 
                      . '-' . preg_replace('/\s+/', '', $lead_owner['dob']) 
                      . '-' . preg_replace('/\s+/', '', $state
                    );
      if ($is_consumer) {
        $identifiers .= date('-Y-m-d');
      }

      if ($document_type) {
        switch ($document_type) {
          case IDDocumentType::DrivingLicence:
            $document_number = $lead_owner['driving_licence_num'] ?? '';
            $document_expiry = $lead_owner['driving_licence_expiry'] ?? '';
            break;
          case IDDocumentType::Passport:
            $document_number = $lead_owner['passport_number'] ?? '';
            $document_expiry = $lead_owner['passport_expiry'] ?? '';
            break;
          case IDDocumentType::Medicare:
            $document_number = $lead_owner['medicare_number'] ?? '';
            $document_expiry = $lead_owner['medicare_expiry'] ?? '';
            break;
        }
  
        $document_expiry = $document_expiry ? date('Y-m-d', strtotime($document_expiry)) : '';
        $documentString = $address['address'] .'-'. $address['suburb'] .'-'. $address['state'] .'-'. $address['postcode'];
				$documentString .= $document_type .'-'. $document_number .'-'. $document_expiry;
				$hashedStr = md5(preg_replace('/\s+/', '', $documentString));
				$identifiers .= '-' . $hashedStr;
      }
    }

    $identifiers = strtolower($identifiers);

    return strlen($identifiers) > 255 ? substr($identifiers, 0, 255) : $identifiers;
  }

  public function getDocusignTrans($lead_ref = false)
  {

    try {
      if (empty($lead_ref))  return $this->setJsonResponse(['success' => false, 'message' => 'Missing Lead Ref']);
      $lead_id = (new LendInternalAuth)->unhashLeadId($lead_ref);

      $partner_requested_privacy_forms = $this->loadModel('PartnerRequestedPrivacyForms')->getPartnerRequestedPrivacyForms(['lead_id' => $lead_id]);

      return $this->setJsonResponse(['success' => true, 'partner_requested_privacy_forms' => $partner_requested_privacy_forms]);

    } catch (\Exception $e) {
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()]);
    }

  }



  public function discardRequestedPrivacyForm($form_id = false)
  {
    try {
      if (empty($form_id))  return $this->setJsonResponse(['success' => false, 'message' => 'Missing Partner Custom Privacy Form Id']);

      $result = $this->loadModel('PartnerRequestedPrivacyForms')->deletePartnerRequestedPrivacyForms(['requested_privacy_form_id' => $form_id]);
      return $this->setJsonResponse(['success' => true, 'partner_requested_privacy_form_id' => $form_id]);

    } catch (\Exception $e) {
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()]);
    }

  }

  public function getS3Link()
  {
    $data = $this->request->getData();
    try {
      $s3Link = $this->LoadModel('Partners')->createPfSignedRequest('files.lend.com.au/' . $data['path'], 'files.lend.com.au', 2);
      return $this->setJsonResponse(['success' => true, 'link' => $s3Link]);

    } catch (\Exception $e) {
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()]);
    }

  }

    public function getEnvelopeDetails($envelopeServiceId){
    if (empty($envelopeServiceId))  return $this->setJsonResponse(['success' => false, 'message' => 'Missing envelope service id']);

    try {
      $sign = new SignatureService('get_envelope');
      $envelopeData = $sign->callService($envelopeServiceId);
      return $this->setJsonResponse(['success' => true, 'envelope_data' => $envelopeData['data']]);
    } catch (\Exception $e) {
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()]);
    }
  }

  private function updateRequestedPrivacyForm($envelopeServiceId, $requestedPrivacyFormIds)
  {
    foreach ($requestedPrivacyFormIds as $id) {
      $this->loadModel('PartnerRequestedPrivacyForms')->updateRequestedPrivacyFormByFormId(
                    array('service_envelope_id' => $envelopeServiceId), array('requested_privacy_form_id' => $id)
      );
    }
  }

  public function updatePrivacyForm()
  {
    try {
      $data = $this->request->getData();
      $result =  $this->loadModel('PartnerRequestedPrivacyForms')->updateRequestedPrivacyFormByFormId(
            array('include_for_lenders' => $data['include_for_lenders']), array('requested_privacy_form_id' => $data['requested_privacy_form_id']));
      return $this->setJsonResponse(['success' => true]);
    } catch (\Exception $e) {
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()]);
    }
  }

  public function updateLeadUploads()
  {
    try {
      $data = $this->request->getData();
      $result =  $this->loadModel('LeadUploads')->updateLeadUploadedFiles(
            array('include_for_lenders' => $data['include_for_lenders']), array('upload_id' => $data['upload_id']));
      return $this->setJsonResponse(['success' => true]);
    } catch (\Exception $e) {
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()]);
    }
  }

    private function validateTemplateIds($templateIds){
        foreach ($templateIds AS $template){
      if (empty($template['template_id'])) {
        return false;
      }
    }
    return true;
  }

  public function getPartnerCustomPrivacyForms($partnerId)
  {
    try {
      $privacyForm = $this->loadModel('PartnerCustomPrivacyForms')->getForms(['partner_id' => $partnerId, 'document_title' => 'Privacy Form', 'status' => 'Ready to Use']);
      $activeLenders = $this->loadModel('Lenders')->getActiveLenderList();
      //If there is a broker privacy form then use only the Lend short privacy from else use Long version
      if (!empty($privacyForm)) {
        $lendPrivacyForm = $this->loadModel('PartnerCustomPrivacyForms')->getForms(['partner_custom_privacy_form_id' => 1, 'status' => 'Ready to Use']);
        $results = ['form' => $privacyForm, 'lend_form' => $lendPrivacyForm, 'active_lenders' => $activeLenders];
      } else {
        $lendPrivacyForm = $this->loadModel('PartnerCustomPrivacyForms')->getForms(['partner_custom_privacy_form_id' => 2, 'status' => 'Ready to Use']);
        $results = ['lend_form' => $lendPrivacyForm, 'active_lenders' => $activeLenders];
      }

      if (empty($lendPrivacyForm)) {
        return $this->setJsonResponse(['success' => false, 'message' => 'Lend privacy forms not available']);
      }

      return $this->setJsonResponse(['success' => true, 'results' => $results]);
    } catch (\Exception $e) {
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()]);
    }
  }

  // NOTE:: Check failed products have any criteria set `hide_anyway`. Remove it from the list of failed products at all so we won't show it in anywhere (e.g - failed lenders, different product types)
    private function _checkHideAnyway($failedProducts){
    foreach ($failedProducts as $key => $product) {
      foreach ($product['failed'] as $failed) {
        if (!empty($failed['criteria']['hide_anyway'])) {
          unset($failedProducts[$key]);
          continue;
        }
      }
    }
    // reset keys
    $failedProducts = array_values($failedProducts);
    return $failedProducts;
  }

  /* Function to be called from PDFGenrator Service in order to get the Broker and Lend details securely
    ** Used to Generate the PDF for RCTI
    */
    public function getRctiPayload(){
    $this->request->allowMethod(['GET']);
    $encodedLeadId = $this->request->query['encodedLeadId'];
    $partnerId = $this->request->query['partnerId'];
    $commissionId = $this->request->query['commissionId'];
    $region = $this->request->query['region'];

    $getParams = $this->request->getQueryParams();
    unset($getParams['signature']);
    if (!($this->validatePDFSignature(
      [
        'encodedLeadId' => $getParams['encodedLeadId'],
        'partnerId' => $getParams['partnerId']
      ], $this->request->query['signature']))
    ) {
      return $this->setJsonResponse(['error' => 'Invalid signature']);
    }

    $lendInfo = (strtoupper($region) === 'NZ' ? Configure::read('Lend.CompanyNz') : Configure::read('Lend.Company'));
    $data = $this->loadModel('PartnerCommissions')->getLeadBrokerDetails($encodedLeadId, $partnerId, $commissionId);
    $payload = array(
      'lendInfo' => $lendInfo,
      'data' => $data
    );

    return $this->setJsonResponse($payload);
  }

    public function collectProductPricing() {
    try {
      // Params
      $lead_ref = $_GET['lead_ref'];
      $product_ids = $_GET['product_ids'];

      // Lead
      $lead_ref = $this->request->query['lead_ref'];
      $lead_id = (new LendInternalAuth)->unhashLeadId($lead_ref);
      $lead = $this->loadModel('Leads')->getLead(['lead_id' => $lead_id]);

      // Product Id's
      $product_array = explode(',', $product_ids);

      // Lend Score
      $lend_score_pending = $this->loadModel('LendScore')->getLendScore(array('lead_id' => $lead_id));
      $lend_score = $lend_score_pending === false ? 0 : $lend_score_pending;

      // term (Number of payments)
      $numOfPayments = $lead['loan_term_requested_months'] * 4;

      $lender_pricing = (new ProductPricing)->run($product_array, $lead['loan_term_requested_months'], $numOfPayments, $lend_score, $lead['amount_requested']);
      return $this->setJsonResponse(['success' => true, 'data' => $lender_pricing]);

    } catch (\Exception $e) {
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage(), 'result' => $lender_pricing]);
    }
  }


    private function _getQuote($lead){
    // fetch quotes data
    $sel_proposal = new \stdClass;
    $quote = new \stdClass;
    if (is_array($lead['lead_quote_ref']) && !empty(is_array($lead['lead_quote_ref']['quote_ref']))) {
      $url = getenv('QUOTES_LOOKUP') . 'quotes/' . $lead['lead_quote_ref']['quote_ref'];
      $result = $this->curl($url, '', 'GET', ['x-api-key:' . getenv('QUOTES_API_KEY')]);

      // find selected proposal
      if ($result['success']) {
        $proposals = $result['data']['proposals'];
        foreach ($proposals as $key => $proposal) {
          if ($proposal['status'] == 'Selected') {
            $sel_proposal = $proposal;
            break;
          }
        }
        $quote = $result['data'];
        unset($quote['proposals']);
      }
    }

    return [$sel_proposal, $quote];
  }

    public function findEntityType(){
    try {
      $data   = $this->request->getData();
      $entityTypes = $this->LoadModel('AbnEntityTypes')->getAllEntityTypes();
      $category = '';
          foreach ($entityTypes AS $key => $val){
        if (in_array($data['entity'], $val)) {
          $category = $key;
        }
      }

      $res['success'] = true;
      $res['data'] = $category;
    } catch (\Exception $e) {
      $res['success'] = false;
      $res['error'] = $e->getMessage();
    }

    return $this->setJsonResponse($res);
  }

  public function checkLenderLevelRequiredFields()
  {
    try {
      Log::info ('----- Lender Level Required Fields Check -----');
      if (!$this->request->is('post')) {
        throw new \Exception("POST request only available.");
      }
      $data = $this->request->getData();
      Log::info (json_encode($data));
      if (empty($data['lender_id']) || empty($data['lead_ref'])) {
        throw new \Exception("Lender ID is required.");
      }

      $lead_id = (new LendInternalAuth)->unhashLeadId($data['lead_ref']);

      $lender_required_fields = TableRegistry::getTableLocator()->get('LenderRequiredFieldEntity')->find('all')
        ->where(['lender_id' => $data['lender_id']])
        ->toArray();

      if (empty($lender_required_fields)) {
        Log::info ("Lender {$data['lender_id']} doesn't have required fields");
        return $this->setJsonResponse(['success' => true]);
      }

      $requiredFields = [];
      foreach ($lender_required_fields  as $f) {
        $requiredFields[$f['table_name']][] = $f['field_name'];
      }

      $lead = $this->loadModel('Leads')->getLeadDetails($lead_id, ['get_all_owners' => true]);

      $lead_asset_finance = $this->_getLeadAssetFinance($lead_id);
      if (!empty($lead_asset_finance) && !empty($lead_asset_finance['contract_type'])) {
        $lead['lead_addresses'] = $this->loadModel('LeadAddresses')->getLeadAddresses(array('lead_id' => $lead['lead']['lead_id'], 'status' => 'active', 'address_type' => 'trading'));
        $lead['lead_mailing_addresses'] = $this->loadModel('LeadAddresses')->getLeadAddresses(array('lead_id' => $lead['lead']['lead_id'], 'status' => 'active', 'address_type' => 'mailing'));
        if (!empty($lead['lead_owner'])) {
          foreach ($lead['lead_owner'] as $owner_key => $owner) {
            $lead['lead_owner'][$owner_key]['lead_owner_addresses'] = $this->loadModel('LeadOwnerAddress')->getLeadOwnerAddresss(array('lead_owner_id' => $owner['owner_id'], 'status' => 'active'));
            $lead['lead_owner'][$owner_key]['lead_owner_employments'] = $this->loadModel('LeadOwnerEmployment')->getLeadOwnerEmployments(array('lead_owner_id' => $owner['owner_id'], 'status' => 'active'));
          }
        }
      }

      $leadValidation = new LeadValidation;
      $leadValidation->check_all_owners = true;
      $leadValidation->setRequiredFields($requiredFields);
      Log::info(json_encode($requiredFields));
      $errors = $leadValidation->validate($this->loadModel('Leads')->formatLeadData($lead, array('dont_toggle_phonenumbers' => true, 'check_all_owners' => true))); // Check validation
      if (!empty($errors)) {
        Log::info("Lead {$data['lead_ref']} - Required Fields Missing: " . json_encode($errors));
        $appendMsg = $this->_generateMessage($errors);
        return $this->setJsonResponse(['success' => false, 'message' => 'Required Fields Missing', 'missing_fields' => $appendMsg]);
      } else {
        return $this->setJsonResponse(['success' => true]);
      }
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()]);
    }
  }

  private function _generateMessage($errors)
  {
    $appendMsg = [];
    foreach ($errors as $er) {
      if (isset($er['field'], $er['error'])) {
          $er['field'] = str_replace(['organisation_name', 'purpose_id', 'industry_id'],
          ['entity_name',     'loan_purpose', 'industry'],
                                      $er['field']);
        if (!empty($er['specific'])) {
          $appendMsg[] = array($er['field'] => ucwords($er['specific']) . '\'s ' . str_replace(array('[', ']', '_'), ' ', $er['field']) . str_replace(['This value ', 'This '], ' ', $er['error']));
        } else {
          $appendMsg[] = array($er['field'] => ucwords(str_replace(array('[', ']', '_'), ' ', $er['field'])) . ' ' . str_replace(['This value ', 'This '], '', $er['error']));
        }
      }
    }
    return $appendMsg;
  }

  // $page = 1, $pagesize = 10
  public function dashboard($page = 1, $pagesize = 10)
  {
    Log::info('getUri => '.$this->request->getUri());
    Log::info('getMethod => '.$this->request->getMethod());
    Log::info('getHeaders => '.json_encode($this->request->getHeaders()));
    Log::info('input => '.json_encode($this->request->input('json_decode')));
    Log::info('getData => '.json_encode($this->request->getData()));
    Log::info('getQuery => '.json_encode($this->request->getQuery()));
    Log::info('php://input => '.json_encode(file_get_contents('php://input')));

    TableRegistry::getTableLocator()->get('App')->postToSlack("Someone request old endpoint `".$this->request->getUri()."`. Please check the logs for the details. ", "lend_errors");
    return $this->setJsonResponse(['success' => true]);
  }


  private function _findLvr($criteria) {
    try {
      foreach ($criteria  as $c) {
        if ($c['factor_id'] == 65) { // LVR
          return (float)$c['value_1'];
        }
      }
      return null;
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return null;
    }
  }

  private function _findMaxLoanAmount($criteria) {
    try {
     foreach ($criteria as $c) {
       if ($c['factor_id'] == 6) {
         if ($c['operator'] === 'between') {
          $max_loan_amount = (float)$c['value_2'];
         } else {
          $max_loan_amount = (float)$c['value_1'];
         }
         if (empty($max_loan_amount)) {
           return 0;
         } elseif ($max_loan_amount > 500000) {
           return 500000;
         } else {
           return $max_loan_amount;
         }
       }
     }
     return null;
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return null;
    }
  }

  public function syncToAccount($lead_id = null) {
    try {
      if (!$this->request->is('post') && empty($lead_id)) {
        throw new \Exception("Only POST request is allowed.");
      }
      $internalAuth = new LendInternalAuth;
      $lead_ref = $this->request->getData('lead_ref');
      $ownerRefs = $this->request->getData('owner_refs');

      $ownerIds = [];
      $ownerExpenses = [];
      if($this->request->is('post')){
        if(is_array($ownerRefs)) {
          foreach ($ownerRefs as $ownerRef) {
            if (is_array($ownerRef) && (isset($ownerRef['owner_ref']) || isset($ownerRef['owner_id']))) {
              if(isset($ownerRef['owner_id'])){
                $id = $ownerRef['owner_id'];
              }else{
                $id = $internalAuth->unhashOwnerId($ownerRef['owner_ref']);
              }
              // $id = $ownerRef['owner_ref'];//for testing
              $ownerIds[] = $id;
              if($ownerRef['sync_expenses'] && ($ownerRef['sync_expenses'] === true))
                $ownerExpenses[$id] = -1;
            }
          }
          if (count($ownerIds) === 0)
            throw new Exception('At least one owner must be provided for syncing');
        }
        else
          throw new Exception('At least one owner must be provided for syncing');
      }
      
      $lead_id = $lead_id ?: $internalAuth->unhashLeadId($lead_ref);
      $leadTable = TableRegistry::getTableLocator()->get('LeadEntity');
      $lead = $leadTable->get($lead_id, [
        'contain' => [
          'Owners' => function ($query) use ($ownerIds) {
            if (!$this->request->is('post')) {
                // Include all owners
                return $query;
            } else {
                // Only sync included owners
                return $query->where(['Owners.owner_id IN' => $ownerIds]);
            }
          },
          // 'Owners',
          'Owners.PartnerAccountPeopleEntity',
          'Owners.LeadOwnerFinancesEntity', 'Owners.OwnerAllEmployments', 'Owners.OwnerAllAddresses', 'Owners.LeadOwnerIncomeEntity', 'Owners.LeadOwnerExpenseEntity',
          'LeadAbnLookupEntity',
          'LeadReferenceEntity',
          'LeadOwnerIncomeEntity',
          'LeadOwnerIncomeEntity.ConIncomeShareEntity',
          'LeadAssetsEntity',
          'LeadAssetsEntity.ConAssetShareEntity',
          'AllAddresses',
          'LeadLiabilitiesEntity',
          'LeadLiabilitiesEntity.ConLiabilityShareEntity',
          'EntityTrustEntity',
          'LeadOwnerExpenseEntity'
        ]
      ])
      ->toArray();

      //find assets and liabilities shares for lead owners
      $consumerAssets = [];
      $consumerLiabilities = [];
      $conAssetShareTable = TableRegistry::getTableLocator()->get('ConAssetShareEntity');
      $assetShares = $conAssetShareTable->find('all')->where(['owner_id IN' => $ownerIds])->contain(['LeadAssetsEntity'])->enableHydration(false)->toArray();
      foreach($assetShares as $assetShare){
        $asset = $assetShare['asset'];
        $asset['value'] = ($asset['value'] * ($assetShare['percent'] / 100));
        $consumerAssets[$assetShare['owner_id']][] = $asset;
      }
      $conLiabilitiesShareTable = TableRegistry::getTableLocator()->get('ConLiabilityShareEntity');
      $liabilitiesShares = $conLiabilitiesShareTable->find('all')->where(['owner_id IN' => $ownerIds])->contain(['LeadLiabilitiesEntity'])->enableHydration(false)->toArray();
      foreach($liabilitiesShares as $liabilitiesShare){
        $liability = $liabilitiesShare['liability'];
        $liability['value'] = ($liability['value'] * ($liabilitiesShare['percent'] / 100));
        $consumerLiabilities[$liabilitiesShare['owner_id']][] = $liability;
      }
      
      $peopleIds = [];
      foreach($lead['owners_all'] as $i => $lo){
        $lead['owners_all'][$i]['lead_assets'] = $lo['lead_assets'] = $consumerAssets[$lead['owners_all'][$i]['owner_id']] ?? [];
        $lead['owners_all'][$i]['liabilities'] = $lo['liabilities'] = $consumerLiabilities[$lead['owners_all'][$i]['owner_id']] ?? [];

        if(isset($lo['partner_account_people']) && !is_null($lo['partner_account_people'])){
          $peopleIds[] = $lo['partner_account_people']['id'];
          if($ownerExpenses[$lo['owner_id']]){
            $lead['owners_all'][$i]['syncExpenses'] = $lo['syncExpenses'] = true;
            $ownerExpenses[$lo['owner_id']] = $lo['partner_account_people']['id'];
          } else {
            $lead['owners_all'][$i]['syncExpenses'] = $lo['syncExpenses'] = false;
          }
        }
      }
      $account_data = $leadTable->generateAccountData($lead, $this->Auth->user());
      $account_data = json_decode(json_encode($account_data), true);
      $accountTable = TableRegistry::getTableLocator()->get('PartnerAccountEntity');
      $accountPeopleTable = TableRegistry::getTableLocator()->get('PartnerAccountPeopleEntity');
      $accountLinkPeopleTable = TableRegistry::getTableLocator()->get('PartnerAccountLinkPeopleEntity');
      $associated = [
        'PartnerAccountMetaEntity',
        'LeadAssetsEntity',
        'LeadLiabilitiesEntity',
        'AllAddresses',
        'LeadAbnLookupEntity',
        'LeadReferenceEntity',
        'EntityTrustEntity',
      ];
      if(count($peopleIds)>0){
        $incomesTable = TableRegistry::getTableLocator()->get('PartnerAccountPeopleIncomesEntity');
        $expensesTable = TableRegistry::getTableLocator()->get('PartnerAccountPeopleExpensesEntity');
        $assetsTable = TableRegistry::getTableLocator()->get('PartnerAccountPeopleAssetsEntity');
        $liabilitiesTable = TableRegistry::getTableLocator()->get('PartnerAccountPeopleLiabilitiesEntity');
        $addressesTable = TableRegistry::getTableLocator()->get('LeadOwnerAddressesEntity');
        $employmentsTable = TableRegistry::getTableLocator()->get('LeadOwnerEmploymentEntity');
        $incomesTable->deleteAll(['partner_account_people_id IN ' => $peopleIds]);
        $addressesTable->deleteAll(['partner_account_people_id IN ' => $peopleIds]);
        $employmentsTable->deleteAll(['partner_account_people_id IN ' => $peopleIds]);
        if(count($ownerExpenses) > 0){
          $expensesTable->deleteAll(['partner_account_people_id IN ' => $ownerExpenses]);
        }        
        $assetsTable->updateAll(['status' => 'Deleted', 'updated'=> date('Y-m-d H:i:s')],['partner_account_people_id IN ' => $peopleIds, 'status' => 'Active']);
        $liabilitiesTable->updateAll(['status' => 'Deleted', 'updated'=> date('Y-m-d H:i:s')],['partner_account_people_id IN ' => $peopleIds, 'status' => 'Active']);
      }
      if (!empty($account_data['partner_account_people'])) {
        $partner_account_people = $account_data['partner_account_people'];
        unset($account_data['partner_account_people']);
      }
      if (!empty($lead['account_id'])) {
        $account = $accountTable->get($lead['account_id'], [
          'contain' => $associated
        ]);
        $this->_inactiveItems($account, $account_data);
        $accountTable->patchEntity($account, $account_data, [
          'associated' => $associated
        ]);
      } else {
        $account = $accountTable->newEntity($account_data, [
          'associated' => $associated
        ]);
      }
      $accountTable->save($account);
      // Sync account people:
      if (!empty($partner_account_people)) {
        // Get account with linked people
        $new_account = $accountTable->get($account->partner_account_id, [
          'contain' => ['PartnerAccountLinkPeopleEntity'],
        ]);

        $people_associated = [
          'PartnerAccountPeopleAssetsEntity', 
          'PartnerAccountPeopleLiabilitiesEntity', 
          'PartnerAccountPeopleIncomesEntity', 
          'PartnerAccountPeopleExpensesEntity', 
          'LeadOwnerAddressesEntity',
          'LeadOwnerEmploymentEntity',
        ];
        foreach ($partner_account_people as $person) {
          $is_main_point_of_contact = !empty($person['is_main_point_of_contact']);
          unset($person['is_main_point_of_contact']);
          if (empty($person['id'])) {
            $person_entity = $accountPeopleTable->newEntity($person, [
              'associated' => $people_associated,
            ]);
          } else {
            $person_entity = $accountPeopleTable->get($person['id'], [
              'contain' => $people_associated,
            ]);
            $accountPeopleTable->patchEntity($person_entity, $person, [
              'associated' => $people_associated,
            ]);
          }
          $accountPeopleTable->save($person_entity);

          // If the person is not linked to the account, create a link
          if (!empty($new_account->partner_account_link_people) && !in_array($person_entity->id, array_column($new_account->partner_account_link_people, 'people_id'))) {
            // update the other people's is_main_point_of_contact to false
            if ($is_main_point_of_contact) {
              $accountLinkPeopleTable->updateAll(['is_main_point_of_contact' => 0], ['account_id' => $account->partner_account_id]);
            }
            $account_link_people = $accountLinkPeopleTable->newEntity([
              'account_id' => $account->partner_account_id,
              'people_id' => $person_entity->id,
              'is_main_point_of_contact' => $is_main_point_of_contact,
            ]);
            $accountLinkPeopleTable->save($account_link_people);
          }
        }
      }
      
      return $this->setJsonResponse(['success' => true, 'account_ref' => $account->account_ref]);
    } catch(\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'error' => $e->getMessage()], 400);
    }
  }

  private function _inactiveItems($account, &$account_data) {
    try {
      foreach ($account->all_addresses as $item) {
        $account_data['all_addresses'][] = ['lead_address_id' => $item['lead_address_id'], 'status' => 'deleted'];
      }
      foreach ($account->liabilities as $item) {
        $account_data['liabilities'][] = ['lead_liability_id' => $item['lead_liability_id'], 'status' => 'Deleted'];
      }
      foreach ($account->assets as $item) {
        $account_data['assets'][] = ['lead_asset_id' => $item['lead_asset_id'], 'status' => 'Deleted'];
      }
      foreach ($account->references as $item) {
        $account_data['references'][] = ['lead_reference_id' => $item['lead_reference_id'], 'status' => 'inactive'];
      }
      $mainPointOfContactId = false;
      if (!empty($account->partner_account_link_people)) {
        foreach ($account->partner_account_link_people as $person) {
          if($person->is_main_point_of_contact){
            $mainPointOfContactId = $person->people_id;
          }
          $pk = array_search($person->id, array_column($account_data['partner_account_people'], 'id'));
          if ($pk === false) { //empty(0) === true
            continue;
          }
          foreach ($person->employments as $item) {
            $account_data['partner_account_people'][$pk]['employments'][] = ['lead_owner_employment_id' => $item['lead_owner_employment_id'], 'status' => 'deleted'];
          }
          foreach ($person->addresses as $item) {
            $account_data['partner_account_people'][$pk]['addresses'][] = ['lead_owner_address_id' => $item['lead_owner_address_id'], 'status' => 'deleted'];
          }
          foreach ($person->people_liabilities as $item) {
            $account_data['partner_account_people'][$pk]['people_liabilities'][] = ['lead_liability_id' => $item['lead_liability_id'], 'status' => 'Deleted'];
          }
          foreach ($person->people_assets as $item) {
            $account_data['partner_account_people'][$pk]['people_assets'][] = ['lead_asset_id' => $item['lead_asset_id'], 'status' => 'Deleted'];
          }
        }
      }
      if($mainPointOfContactId !== false){
        foreach ($account_data['partner_account_people'] as $pk => $person){
          if($account_data['partner_account_people'][$pk]['id'] != $mainPointOfContactId){
            $account_data['partner_account_people'][$pk]['is_main_point_of_contact'] = false;
          }
        }
      }
    } catch(\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }

  private function _getAssetSaleTypes() {
    $config_asset_sale_types = TableRegistry::getTableLocator()->get('ConfigAssetSaleTypeEntity')
    ->find('all')
    ->where(['active' => true, 'FIND_IN_SET("e2e", uses)'])
    ->order(['order' => 'ASC'])
    ->toArray();
    return array_values(array_column($config_asset_sale_types, 'sale_type'));
  }

  public function deleteLeadUser($lead_ref = null)
  {
      $this->request->allowMethod(['delete']);
      try {
          $lead_id = (new LendInternalAuth)->unhashLeadId($lead_ref);
          if (!$lead_id) {
              throw new \Exception("Invalid lead reference provided.");
          }
          $partner_user_leads_table = TableRegistry::getTableLocator()->get('PartnerUserLeadsEntity');
          $accessRows = $partner_user_leads_table->find()
            ->where([
              'lead_id' => $lead_id,
              'status' => 'ACCESS'
            ])
            ->all();
            if ($accessRows->count() > 0) {
              $entitiesData = [];
              $now = date('Y-m-d H:i:s');
              foreach ($accessRows as $accessRow) {
                $entitiesData[] = [
                  'partner_user_lead_id' => $accessRow->partner_user_lead_id,
                  'status' => 'REVOKED',
                  'updated'=> $now
                ];
              }
              $entitiesToUpdate = $partner_user_leads_table->patchEntities($accessRows->toArray(), $entitiesData);
              $partner_user_leads_table->saveMany($entitiesToUpdate);
            }
          $this->response = $this->response->withStatus(200);
      } catch (\Exception $e) {
          Log::error($e->getMessage());
          Log::error($e->getTraceAsString());
          $this->response = $this->setJsonResponse(['success' => false, 'error' => $e->getMessage()], 400);
      }
      return $this->response;
  }

  public function deleteLeadReferrer($lead_ref = null)
  {
      $this->request->allowMethod(['delete']);
      $lead_id = (new LendInternalAuth)->unhashLeadId($lead_ref);
  
      if (!$lead_id) {
          throw new \Exception("Invalid lead reference provided.");
      }
  
      try {
          $leadEntityTable = TableRegistry::getTableLocator()->get('LeadEntity');
          $leadEntity = $leadEntityTable->find()->where(['lead_id' => $lead_id])->first();
  
          if (!$leadEntity) {
              throw new \Exception("Lead record not found.");
          }
  
          $leadEntity->referrer_person_id = null;
  
          if (!$leadEntityTable->save($leadEntity)) {
              throw new \Exception('Error updating lead entity');
          }
  
          $this->response = $this->response->withStatus(200, 'Lead updated successfully');
      } catch (\Exception $e) {
          Log::error($e->getMessage());
          Log::error($e->getTraceAsString());
          $this->response = $this->setJsonResponse(['success' => false, 'error' => $e->getMessage()], 400);
      }
  
      return $this->response;
  }
  
  



  

}
