<?php
namespace Referrer\Controller;

use Referrer\Controller\AppController;
use Cake\Log\Log;
use Hashids\Hashids;
use Cake\Event\Event;
use Cake\ORM\TableRegistry;
use Referrer\Controller\Traits\ReferrerTokenTrait;

/**
 * ReferrerPeople Controller
 *
 *
 * @method \Referrer\Model\Entity\ReferrerPerson[]|\Cake\Datasource\ResultSetInterface paginate($object = null, array $settings = [])
 */
class ReferrerPeopleController extends AppController
{
    use ReferrerTokenTrait;

    private $user;
    private $ReferrerPeople;
    private $Referrers;
    private $id;

    public function initialize(): void
    {
        parent::initialize();
        $this->ReferrerPeople = TableRegistry::getTableLocator()->get('ReferrerPeople');
        $this->Referrers = TableRegistry::getTableLocator()->get('Referrers');
        $this->Auth->allow(['getAllReferrerPeoplesCommission']);
    }

    public function beforeFilter($event)
    {
        parent::beforeFilter($event);
        $data = $this->request->getData();
        
        if (empty($data)) {
            $data = $this->request->input("json_decode", true);
        }
        if (!empty($data['referrer_person_ref']) && !isset($data['id'])) {
            foreach ($data as $key => $value) {
                $this->request = $this->request->withData($key, $value);
            }
            $referrer_person_id = $this->ReferrerPeople->ref2id($data['referrer_person_ref']);
            $this->request = $this->request->withData("id", $referrer_person_id);
            $this->id = $referrer_person_id;
        }else if(!empty($data['id'])){
            $this->id = $data['id'];
        }
        if (getenv('LEND_ENV') == 0) {
            $this->user = $this->Auth->user();
        } else {
            $this->user = $this->Auth->identify();
        }
    }

    /**
     * Index method
     *
     * @return \Cake\Http\Response|void
     */
    public function listByReferrerRef($referrer_ref)
    {
        try {
            if(!$this->user['partner_id']){
                throw new \Exception(__('Please login first.'));
            }
            $referrer= $this->Referrers->find("all", [
                "conditions" => ["referrer_ref" => $referrer_ref],
                "contain" => ["ReferrerPeople", "LeadAbnLookupEntity"]
            ])->first();
            if($referrer->partner_id != $this->user['partner_id']){
                throw new \Exception(__('The referrer does not belong to you. Please, try again.'));
            }
            return $this->setJsonResponse($referrer->toArray()['referrer_people']);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
        }
    }

    /**
     * View method
     *
     * @param string|null $id Referrer id.
     * @return \Cake\Http\Response|void  JSON API response object.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($referrer_person_ref)
    {
        try {
            $referrerPerson = $this->ReferrerPeople->find("all", [
                "conditions" => ["referrer_person_ref" => $referrer_person_ref],
                "contain" => ["Referrers", "Referrers.LeadAbnLookupEntity"]
            ])->first();
            if($referrerPerson->referrer->partner_id != $this->user['partner_id']){
                throw new \Exception(__('The referrer does not belong to you. Please, try again.'));
            }
            unset($referrerPerson->referrer);
            return $this->setJsonResponse($referrerPerson->toArray());
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
        }
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null JSON API response object.
     */
    public function add()
    {
        try {
            if(!$this->user['partner_id']){
                throw new \Exception(__('Please login first.'));
            }
            $referrerPerson = $this->ReferrerPeople->newEntity();
            if ($this->request->is('post')) {
                $data = $this->request->getData();

                $referrer= $this->Referrers->findById($data['referrer_id'])->first();
                if($referrer->partner_id != $this->user['partner_id']){
                    throw new \Exception(__('The referrer does not belong to you. Please, try again.'));
                }
                
                $referrerPerson = $this->ReferrerPeople->patchEntity($referrerPerson, $data);
                if ($this->ReferrerPeople->save($referrerPerson)) {
                    return $this->setJsonResponse($this->ReferrerPeople->get($referrerPerson->id)->toArray());
                }
                throw new \Exception(json_encode($referrerPerson->getErrors()));
            }
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
        }
    }

    /**
     * Edit method
     *
     * @param string|null $id Referrer id.
     * @return \Cake\Http\Response|null  JSON API response object.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function edit()
    {
        try {
            $referrerPerson = $this->ReferrerPeople->get($this->id, [
                'contain' => ["Referrers"]
            ]);
            if($referrerPerson->referrer->partner_id != $this->user['partner_id']){
                throw new \Exception(__('The referrer does not belong to you. Please, try again.'));
            }
            unset($referrerPerson->referrer);
            unset($referrerPerson->created);
            if ($this->request->is(['patch', 'post', 'put'])) {
                $data = $this->request->getData();
                $referrerPerson = $this->ReferrerPeople->patchEntity($referrerPerson, $data);
                if ($this->ReferrerPeople->save($referrerPerson)) {
                    return $this->setJsonResponse($referrerPerson->toArray());
                }
                throw new \Exception(json_encode($referrerPerson->getErrors()));
            }
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
        }
    }

    public function countLeads($referrer_person_ref){
        try {
            $referrerPerson = $this->ReferrerPeople->find("all", [
                "conditions" => ["referrer_person_ref" => $referrer_person_ref],
                "contain" => ["LeadEntity", "Referrers"]
            ])->first();
            if($referrerPerson->referrer->partner_id != $this->user['partner_id']){
                throw new \Exception(__('The referrer does not belong to you. Please, try again.'));
            }
            return $this->setJsonResponse(['count' => count($referrerPerson->leads)]);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
        }
    }

    public function getAllReferrerPeople($filtered = false) {
        try {
            $referrers = $this->Referrers->find()
                ->contain([
                    'ReferrerPeople',
                    'LeadAbnLookupEntity'
                ])
                ->where([
                    'Referrers.partner_id' => $this->user['partner_id']
                ]);
            
            if ($filtered) {
                $partnerUserId = $this->user['partner_user_id'];
                $referrers->leftJoinWith('ReferrerPartnerUserAccessEntity', function ($q) use ($partnerUserId) {
                    return $q->where(['ReferrerPartnerUserAccessEntity.partner_user_id' => $partnerUserId]);
                })
                    ->andWhere(function ($exp) {
                        return $exp->or_([
                            'Referrers.all_users_can_access' => true,
                            'ReferrerPartnerUserAccessEntity.partner_user_id IS NOT' => null
                        ]);
                    });
            }

            return $this->setJsonResponse($referrers);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
        }
    }

    public function updateReferrerPeopleStatus() {

        try {
            $data = $this->request->getData();
            $referrerPerson = $this->ReferrerPeople->get($data['id']);

            $referrer_person_data = array();
            $referrer_person_data['active_status'] = $data['active_status'];
            $referrer_person_data['is_point_of_contact'] = 0;

            if ($referrerPerson) {
                if ($referrerPerson['is_point_of_contact']) {
                    $first_referrer_person_except_current = $this->ReferrerPeople->find("all", [
                        "conditions" => ["referrer_id" => $referrerPerson->referrer_id, "id !=" => $referrerPerson->id],
                        "order" => ["id" => "ASC"]
                    ])->first();
            
                    if ($first_referrer_person_except_current) {
                        $first_referrer_person_except_current->is_point_of_contact = 1;
                        $this->ReferrerPeople->save($first_referrer_person_except_current);
                    }
                }
            }
            
            if (!$referrerPerson) {
                throw new \Exception("Referrer not found");
            }
            $referrerPerson = $this->ReferrerPeople->patchEntity($referrerPerson, $referrer_person_data);
            if (!$this->ReferrerPeople->save($referrerPerson)) {
                throw new \Exception("Unable to update referrer person active status");
            }
            return $this->setJsonResponse(['success' => true, 'message' => 'Referrer person active status updated successfully.']);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
        }
    }

    public function getAllReferrerPeoplesCommission() {
        try {

            $personId = $this->getPersonId();

            if (empty($personId)) {
                throw new \InvalidArgumentException('Person ID is required', 400);
            }
            $days = $this->request->getQuery('days');
            
            try {
                $partner_commissions_table = $this->getTableLocator()->get('PartnerCommissionsEntity');
            } catch (\Exception $e) {
                throw new \RuntimeException('Failed to load Partner Commissions table', 500);
            }

            $query = $partner_commissions_table->find();
            $query->contain([
                'LeadEntity' => [
                    'fields' => ['lead_id', 'lead_ref', 'lead_type', 'referrer_person_id'],
                    'PocOwner' => [
                        'fields' => ['lead_id', 'first_name', 'last_name']
                    ]
                ],
                'SettlementReviewsEntity' => [
                    'fields' => [
                        'settlement_review_id', 
                        'commission_id',
                        'settlement_date',
                        'referrer_commission',
                        'customer_rate',
                    ]
                ],
            ])
            ->select([
                'PartnerCommissionsEntity.lead_id',
                'PartnerCommissionsEntity.commission_id',
                'PartnerCommissionsEntity.funded_amount',
                'PartnerCommissionsEntity.funded_date',
                'PartnerCommissionsEntity.is_active'
            ])
            ->where([
                'PartnerCommissionsEntity.commission <>' => 0,
                'PartnerCommissionsEntity.sale_outcome_id IS NOT' => null,
                'PartnerCommissionsEntity.is_active' => 1,
                'LeadEntity.referrer_person_id' => $personId,

            ]);
            if ($days !== null) {
                $date = new \DateTime();
                $date->modify("-{$days} days");
                $query->where([
                    'PartnerCommissionsEntity.funded_date >=' => $date->format('Y-m-d')
                ]);
            }

            try {
                $results = $query->toArray();
                if (empty($results)) {
                    return $this->setJsonResponse([
                        'data' => [],
                        'message' => $days ? "No commission records found for the last {$days} days" : 'No commission records found'
                    ], 200);
                }
                return $this->setJsonResponse([
                    'data' => $results,
                    'message' => $days ? "Commission records for the last {$days} days retrieved successfully" : 'Commission records retrieved successfully'
                ]);
            } catch (\PDOException $e) {
                Log::error('Database error in getAllReferrerPeoplesCommission: ' . $e->getMessage());
                throw new \RuntimeException('Database error occurred while fetching commission records', 500);
            }
        } catch (\InvalidArgumentException $e) {
            Log::warning($e->getMessage());
            return $this->setJsonResponse(
                ['error' => $e->getMessage()],
                $e->getCode()
            );
        } catch (\RuntimeException $e) {
            Log::error($e->getMessage());
            return $this->setJsonResponse(
                ['error' => $e->getMessage()],
                $e->getCode()
            );
        } catch (\Exception $e) {
            Log::error('Unexpected error in getAllReferrerPeoplesCommission: ' . $e->getMessage());
            Log::error($e->getTraceAsString());
            return $this->setJsonResponse(
                ['error' => 'An unexpected error occurred while processing your request'],
                500
            );
        }
    }

    
}