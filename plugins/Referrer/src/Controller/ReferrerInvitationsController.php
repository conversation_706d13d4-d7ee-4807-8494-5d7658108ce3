<?php
namespace Referrer\Controller;

use App\Lend\LendInternalAuth;
use <PERSON><PERSON><PERSON>\Controller\AppController;
use Cake\Log\Log;
use Cake\ORM\TableRegistry;
use Firebase\JWT\JWT;
use Cake\Utility\Security;

/**
 * ReferrerInvitations Controller
 */
class ReferrerInvitationsController extends AppController
{
    public function initialize() {
        parent::initialize();
        $this->Auth->allow(['accept', 'process', 'details']);
    }

    /**
     * Send an invitation
     * This is to be consumed in broker 
     */
    public function send($referrerPersonRef){
        try 
        {
            Log::info('ReferrerInvitation - Starting send process');
            Log::info('Environment: ' . getenv('LEND_ENV'));
            
            if (getenv('LEND_ENV') == 0) {
                $user = $this->Auth->user();
            } else {
                $user = $this->Auth->identify();
            }
            Log::info('User data: ' . json_encode([
                'partner_id' => $user['partner_id'] ?? null,
                'email' => $user['email'] ?? null,
                'partner_user_id' => $user['partner_user_id'] ?? null
            ]));
            if(!$user['partner_id']){
                throw new \Exception('Please login first', 403);
            }
            if (!$this->request->is('get')) {
                throw new \Exception("GET request only available.");
            }

            $referrerPersonId = LendInternalAuth::unhashReferrerPeopleId($referrerPersonRef);
            $referrerPeopleTable = TableRegistry::getTableLocator()->get('ReferrerPeople');
            $referrerPerson = $referrerPeopleTable->get($referrerPersonId);
            if (empty($referrerPerson)) {
                throw new \Exception("Invalid referrer person ref");
            }
            
            $referrerInvitationsTable = TableRegistry::getTableLocator()->get('ReferrerInvitations');
            //check if there is an existing invitation
            $invitation = $referrerInvitationsTable->find()->where(['referrer_person_id'=> $referrerPersonId, 'active'=> 1])->first();
            if(!$invitation || ($invitation['email'] !== $referrerPerson->email)){
                if($invitation){//email has changed, invalidate old invitation
                    log::error("invalidating ".$invitation['id']);
                    $inv = $referrerInvitationsTable->get($invitation['id']);
                    $inv = $referrerInvitationsTable->patchEntity($inv, ['active' => 0]);
                    $referrerInvitationsTable->save($inv);
                }
                //create new invitation
                $invitationData = [
                    'referrer_person_id'=> $referrerPersonId,
                    'email' => $referrerPerson->email,
                    'partner_user_id' => $user['partner_user_id']
                ];
                $invitation = $referrerInvitationsTable->newEntity($invitationData);
                $referrerInvitationsTable->save($invitation);
            }

            //create invitation code
            $invitationCode = $referrerInvitationsTable->createInvitationCode($invitation->id);

            $extraData = [
                'broker_full_name' => $user['name'],
                'broker_company_name' => $user['partner']['organisation_name'],
                'referrer_first_name' => $referrerPerson->first_name,
                'referrer_last_name' => $referrerPerson->last_name,
                'referrer_name' => $referrerPerson->first_name.' '.$referrerPerson->last_name,
                'from' => $user['email'],
                'to' => $referrerPerson->email,
                'invitation_code' => $invitationCode
            ];

            Log::info('Sending invitation to: ' . json_encode($extraData));
            $code = 'ReferrerInvitation';

            $notification = TableRegistry::getTableLocator()->get('PartnerNotifications')->sendPartnerNotifications($user['partner_id'], $code, false, [],$user['partner_user_id'], $extraData);
            Log::info('Notification attempt details: ' . json_encode([
                'partner_id' => $user['partner_id'],
                'code' => $code,
                'extraData' => array_merge($extraData, ['to' => $referrerPerson->email])
            ]));
            
            //TO-DO - maybe track all sending attempts and the result into another table
            if ($notification[0]['successfully_sent'] === false) {
                Log::error('Email sending failed. Notification response: ' . json_encode($notification));
                throw new \Exception("Failed to send the invitation.");
            }
            Log::info('Email sent successfully to: ' . $referrerPerson->email);
            //update invitation last_sent
            $sent = date('Y-m-d H:i:s');
            $invitation = $referrerInvitationsTable->patchEntity($invitation, ['last_sent' => $sent]);
            $referrerInvitationsTable->save($invitation);
            //create invitation email record
            $invitationEmailData = [
                'referrer_invitation_id' => $invitation->id,
                'partner_user_id' => $user['partner_user_id']
            ];
            $referrerInvitationEmailsTable = TableRegistry::getTableLocator()->get('ReferrerInvitationEmails');
            $invitationEmail = $referrerInvitationEmailsTable->newEntity($invitationEmailData);
            $referrerInvitationEmailsTable->save($invitationEmail);

            return $this->setJsonResponse(['success' => true]);
            
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
        }
    }

    /**
     * Accept invitation (for an existing user)
     */
    public function accept($referrerInvitationRef){
        try 
        {
            //get the invitation
            $invitationId = LendInternalAuth::unhashReferrerInvitationId($referrerInvitationRef);
            $referrerInvitationsTable = TableRegistry::getTableLocator()->get('ReferrerInvitations');
            $invitation =  $referrerInvitationsTable->get($invitationId);
            if (empty($invitation)) {
                throw new \Exception("Invalid invitation ref");
            }
            // if (!empty($invitation->accepted)) {
            //     throw new \Exception("This invitation has already been accepted");
            // }
            if ($invitation->active === 0) {
                throw new \Exception("This invitation is not active");
            }
            //get the account
            $referrerAccountsTable = TableRegistry::getTableLocator()->get('ReferrerAccounts');
            $account = $referrerAccountsTable->find()->where(['email'=> $invitation->email])->first();
            if(!$account){
                throw new \Exception("No existing account found for the email associated with that invitation");
            }
            $referrerPeopleTable = TableRegistry::getTableLocator()->get('ReferrerPeople');
            $referrerPerson = $referrerPeopleTable->get($invitation->referrer_person_id);
            $referrerPerson = $referrerPeopleTable->patchEntity($referrerPerson, ['referrer_account_id' => $account->id, 'joined_portal'=> date('Y-m-d H:i:s')]);
            $referrerPeopleTable->save($referrerPerson);

            $invitation = $referrerInvitationsTable->patchEntity(
                $invitation, 
                [
                    'last_opened' => date('Y-m-d H:i:s'), 
                    'accepted' => date('Y-m-d H:i:s'),
                    'referrer_account_id' => $account->id
                ]);
            $referrerInvitationsTable->save($invitation);
            return $this->setJsonResponse(['success' => true]);

        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
        }
    }

    /**
     * test function
     * TO-DO : remove this when not needed
     */
    private function getRef($id){
        return $this->setJsonResponse(['ref' => LendInternalAuth::hashReferrerInvitationId($id)]);
    }

    /**
     * Process an invitation
     */
    public function process($referrerInvitationRef){
        try
        {
            //get the invitation
            $invitationId = LendInternalAuth::unhashReferrerInvitationId($referrerInvitationRef);
            $referrerInvitationsTable = TableRegistry::getTableLocator()->get('ReferrerInvitations');
            $invitation =  $referrerInvitationsTable->get($invitationId);
            if (empty($invitation)) {
                throw new \Exception("Invalid invitation ref");
            }
            if ($invitation->active === 0) {
                throw new \Exception("This invitation is not active");
            }
            $invitation = $referrerInvitationsTable->patchEntity($invitation, ['last_opened' => date('Y-m-d H:i:s')]);
            $referrerInvitationsTable->save($invitation);

            //is there an account with this email 
            $referrerAccountsTable = TableRegistry::getTableLocator()->get('ReferrerAccounts');
            $account = $referrerAccountsTable->find()->where(['email'=> $invitation->email])->first();
            if(!$account){//create an account
                $referrerPeopleTable = TableRegistry::getTableLocator()->get('ReferrerPeople');
                $referrerPerson = $referrerPeopleTable->get($invitation->referrer_person_id);
                $accountData = [
                    'email' => $invitation->email,
                    'first_name' => $referrerPerson->first_name,
                    'last_name' => $referrerPerson->last_name,
                    'contact_number' => $referrerPerson->contact_number,
                ];
                $account = $referrerAccountsTable->newEntity($accountData);
                $referrerAccountsTable->save($account);

                $referrerPerson = $referrerPeopleTable->patchEntity($referrerPerson, ['referrer_account_id' => $account->id, 'joined_portal'=> date('Y-m-d H:i:s')]);
                $referrerPeopleTable->save($referrerPerson);

                $invitation = $referrerInvitationsTable->patchEntity($invitation, ['referrer_account_id' => $account->id, 'accepted' => date('Y-m-d H:i:s')]);
                $referrerInvitationsTable->save($invitation);
            } else {
                //create JWT token
                $token = JWT::encode([
                    'referrer_account_ref' => LendInternalAuth::hashReferrerAccountId($account['id']),
                    'exp' =>  (time() + 86400)
                  ],
                  Security::salt()
                );
                //no point doing below as the people info may be needed to possibly be selected on app(to support mutiple referrer people on one account)
                // $this->setCookieSameSite('auth_token', $token, 0, "/", "." . $_SERVER['SERVER_NAME'], getenv('LEND_ENV') > 0, true, (getenv('LEND_ENV') > 0 ? "None" : false));
                return $this->setJsonResponse(['success' => true, 'token' => $token]);
            }
            return $this->setJsonResponse(['success' => true]);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
        }
    }

    /**
     * Get invitation details
     */
    public function details($referrerInvitationRef){
        try
        {
            $contain = [
                'PartnerUserEntity' => ['fields' => ['title', 'name']],
                'ReferrerPeople' => ['fields' => [
                    'first_name',
                    'last_name',
                    'contact_number',
                    'email',
                    'is_point_of_contact',
                    'active_status',
                    'created',
                    'updated'
                ]],
                'ReferrerPeople.Referrers'=> ['fields' => ['nickname']],
                'ReferrerPeople.Referrers.PartnerEntity'=> ['fields' => [
                    'company_name',
                    'registered_name',
                    'trading_name',
                    'organisation_name',
                    'logo',
                    'brand_colour',
                ]],
            ];
            $options = ['contain' => $contain];
            $options['fields'] = [
                'email',
                'created',
                'accepted',
                'referrer_person_id'
            ];
            // $options = [];
            //get the invitation
            $invitationId = LendInternalAuth::unhashReferrerInvitationId($referrerInvitationRef);
            $referrerInvitationsTable = TableRegistry::getTableLocator()->get('ReferrerInvitations');
            $invitation =  $referrerInvitationsTable->get($invitationId, $options);
            if (empty($invitation)) {
                throw new \Exception("Invalid invitation ref");
            }
            return $this->setJsonResponse($invitation);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
        }
    }
}