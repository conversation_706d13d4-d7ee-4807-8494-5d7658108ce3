<?php

namespace Referrer\Controller;

use App\Lend\LendInternalAuth;
use Re<PERSON>rer\Controller\AppController;
use Cake\Log\Log;
use Hashids\Hashids;
use Cake\Event\Event;
use Cake\ORM\TableRegistry;

/**
 * Referrers Controller
 *
 *
 * @method \Referrer\Model\Entity\Referrer[]|\Cake\Datasource\ResultSetInterface paginate($object = null, array $settings = [])
 */
class ReferrersController extends AppController
{

    private $user;
    private $Referrers;
    private $id;

    public $paginate = [
        'limit' => 2,
        'order' => [
            'Referrers.business_name' => 'asc'
        ],
        'contain' => ['ReferrerPeople']
    ];

    public function initialize()
    {
        parent::initialize();
        $this->Referrers = TableRegistry::getTableLocator()->get('Referrers');
    }

    public function beforeFilter($event)
    {
        parent::beforeFilter($event);
        $data = $this->request->getData();
        if (empty($data)) {
            $data = $this->request->input("json_decode", true);
        }
        if ($data['referrer_ref'] && !isset($data['id'])) {
            foreach ($data as $key => $value) {
                $this->request = $this->request->withData($key, $value);
            }
            $referrer_id = $this->Referrers->ref2id($data['referrer_ref']);
            $this->request = $this->request->withData("id", $referrer_id);
            $this->id = $referrer_id;
        }
        if (getenv('LEND_ENV') == 0) {
            $this->user = $this->Auth->user();
        } else {
            $this->user = $this->Auth->identify();
        }
    }

    public function index()
    {
        try {
            $this->paginate['limit'] = $this->request->getData("limit");
            $this->paginate['page'] = $this->request->getData("page");

            $sort_by = $this->request->getData("sort_by");
            $sort_direction = $this->request->getData("sort_direction") ?? 'asc';
            $sort_direction = $sort_direction === 'desc' ? 'asc' : 'desc';

            $status = $this->request->getData("status");
            $where = ['partner_id' => $this->user['partner_id']];

            if (!empty($status)) {
                $where['Referrers.active_status'] = ($status === 'active') ? 1 : 0;
            }

            $searchString = $this->request->getData('search');

            $query = $this->Referrers->find('all')
                ->contain([
                    'ReferrerPeople',
                    'LeadAbnLookupEntity',
                    'PartnerReferrerCategoryEntity' => ['fields' => ['id', 'name']],
                ])
                ->leftJoinWith('ReferrerPeople')
                ->leftJoinWith('LeadAbnLookupEntity');

            // Handle sorting
            if (!empty($sort_by)) {
                $sortField = $sort_by;
                // Handle related model fields
                if (strpos($sort_by, '.') !== false) {
                    list($model, $field) = explode('.', $sort_by);
                    switch ($model) {
                        case 'abn_lookup':
                            $sortField = "LeadAbnLookupEntity.$field";
                            break;
                        case 'referrer_people':
                            $sortField = "ReferrerPeople.$field";
                            break;
                        default:
                            $sortField = "Referrers.$field";
                    }
                } else {
                    $sortField = "Referrers.$sort_by";
                }
                $query->order([$sortField => $sort_direction]);
            } else {
                // Default sorting
                $query->order(['Referrers.created' => 'desc']);
            }

            $query->where($where);

            if (!empty($searchString)) {
                if (is_numeric($searchString)) {
                    if (strlen($searchString) < 5) {
                        throw new \Exception("Please input more than 5 digits to search phone number.");
                    }
                    $query->where(["ReferrerPeople.contact_number LIKE" => "%$searchString%"]);
                } else {
                    if (strlen(str_replace(" ", "", $searchString)) < 3) {
                        throw new \Exception("Please input more than 3 letters to search name.");
                    }
                    if ($searchString == trim($searchString) && strpos($searchString, ' ') !== false) {
                        // if $searchString has space, check first_name and last_name separately
                        $name = explode(" ", trim($searchString));
                        $query->where([
                            'OR' => [
                                ["ReferrerPeople.first_name LIKE" => "%{$name[0]}%", "ReferrerPeople.last_name LIKE" => "%{$name[1]}%"],
                                ["ReferrerPeople.email LIKE" => "%$searchString%"],
                                ["LeadAbnLookupEntity.business_name LIKE" => "%$searchString%"],
                                ["LeadAbnLookupEntity.organisation_name LIKE" => "%$searchString%"],
                                ["Referrers.nickname LIKE" => "%$searchString%"]
                            ]
                        ]);
                    } else {
                        $query->where([
                            'OR' => [
                                ["ReferrerPeople.first_name LIKE" => "%$searchString%"],
                                ["ReferrerPeople.last_name LIKE" => "%$searchString%"],
                                ["ReferrerPeople.email LIKE" => "%$searchString%"],
                                ["LeadAbnLookupEntity.business_name LIKE" => "%$searchString%"],
                                ["LeadAbnLookupEntity.organisation_name LIKE" => "%$searchString%"],
                                ["Referrers.nickname LIKE" => "%$searchString%"]
                            ]
                        ]);
                    }
                }
            }

            if (!$this->user['account_admin']) { //limit results based on access for non-admin users
                $partnerUserId = $this->user['partner_user_id'];
                $query->leftJoinWith('ReferrerPartnerUserAccessEntity', function ($q) use ($partnerUserId) {
                    return $q->where(['ReferrerPartnerUserAccessEntity.partner_user_id' => $partnerUserId]);
                })
                    ->andWhere(function ($exp) {
                        return $exp->or_([
                            'Referrers.all_users_can_access' => true,
                            'ReferrerPartnerUserAccessEntity.partner_user_id IS NOT' => null
                        ]);
                    });
            }

            $query->group("Referrers.id");

            $paginatedResults = $this->paginate($query);
            $this->removeEntitiesJoinData($paginatedResults, 'partner_referrer_categories');

            return $this->setJsonResponse([
                'referrers' => $paginatedResults,
                'pages' => ceil($query->count() / $this->paginate['limit'])
            ]);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
        }
    }

    /**
     * Index method
     *
     * @return \Cake\Http\Response|void
     */
    public function getAllReferrers()
    {
        try {
            if (!$this->user['partner_id']) {
                throw new \Exception(__('Please login first.'));
            }
            $referrers = $this->Referrers->findByPartnerId($this->user['partner_id'])
                ->contain(['LeadAbnLookupEntity', 'PartnerReferrerCategoryEntity' => ['fields' => ['id', 'name']]]);

            if (!$this->user['account_admin']) { //limit results based on access for non-admin users
                $partnerUserId = $this->user['partner_user_id'];
                $referrers->leftJoinWith('ReferrerPartnerUserAccessEntity', function ($q) use ($partnerUserId) {
                    return $q->where(['ReferrerPartnerUserAccessEntity.partner_user_id' => $partnerUserId]);
                })
                    ->andWhere(function ($exp) {
                        return $exp->or_([
                            'Referrers.all_users_can_access' => true,
                            'ReferrerPartnerUserAccessEntity.partner_user_id IS NOT' => null
                        ]);
                    });
            }

            $this->removeEntitiesJoinData($referrers, 'partner_referrer_categories');
            return $this->setJsonResponse($referrers->toArray());
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
        }
    }

    /**
     * View method
     *
     * @param string|null $id Referrer id.
     * @return \Cake\Http\Response|void  JSON API response object.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($referrer_ref)
    {
        try {
            $referrer = $this->Referrers->find("all")
                ->contain([
                    'ReferrerPeople' => [
                        'ReferrerAccounts' => [
                            'fields' => ['id', 'email', 'first_name', 'last_name', 'password', 'contact_number', 'active']
                        ],
                        'ReferrerInvitations' => [
                            'fields' => ['id', 'email', 'last_opened', 'accepted', 'active', 'referrer_person_id']
                        ]
                    ],
                    'ReferrerRcti',
                    'ReferrerAgreements',
                    'LeadAbnLookupEntity',
                    'ReferrerAgreements.PartnerUserEntity' => ['fields' => ['PartnerUserEntity.name']],
                    'ReferrerRcti.PartnerUserEntity' => ['fields' => ['PartnerUserEntity.name']],
                    'PartnerReferrerCategoryEntity' => ['fields' => ['id', 'name']],
                    'PartnerUserEntity' => ['fields' => ['partner_user_ref', 'name']],
                ])
                ->where(["referrer_ref" => $referrer_ref])
                ->first();

            if ($referrer['all_users_can_access'] === true) { //overwrite partner users to be all active users
                $where = [
                    'partner_id' => $this->user['partner_id'],
                    'active' => 1
                ];
                $partnerUsers = TableRegistry::getTableLocator()->get('PartnerUserEntity')->find()
                    ->select(['partner_user_ref', 'name'])
                    ->where($where)
                    ->toArray();

                $referrer->partner_users = $partnerUsers;
            }
            $this->removeEntityJoinData($referrer, 'partner_referrer_categories');
            $this->removeEntityJoinData($referrer, 'partner_users');
            if ($referrer->partner_id != $this->user['partner_id']) {
                throw new \Exception(__('The referrer does not belong to you. Please, try again.'));
            }
            return $this->setJsonResponse($referrer);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
        }
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null JSON API response object.
     */
    public function add()
    {
        try {
            if (!$this->user['partner_id']) {
                throw new \Exception(__('Please login first.'));
            }
            $referrer = $this->Referrers->newEntity();
            if ($this->request->is('post')) {
                $data = $this->request->getData();
                $data['partner_id'] = $this->user['partner_id'];
                if ($data['abn_lookup']['gst_effective_to'] == "0001-01-01") {
                    $data['gst_status'] = true;
                }
                //if access not provided to all users and no users specified, give access to the current user
                if ((!isset($data['all_users_can_access']) || ($data['all_users_can_access'] == false))
                    && !isset($data['partner_users'])
                ) {
                    $data['partner_users'] = [['partner_user_id' => $this->user['partner_user_id']]];
                }
                // update all_users_can_access to false as we have a user now
                $data['all_users_can_access'] = false;
                $referrer = $this->Referrers->patchEntity($referrer, $data);
                if ($this->Referrers->save($referrer)) {
                    return $this->setJsonResponse($this->Referrers->get($referrer->id)->toArray());
                }
                throw new \Exception(__('The referrer could not be saved. Please, try again.'));
            }
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
        }
    }

    /**
     * Edit method
     *
     * @param string|null $id Referrer id.
     * @return \Cake\Http\Response|null  JSON API response object.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function edit()
    {
        try {
            if ($this->request->is(['patch', 'post', 'put'])) {
                $data = $this->request->getData();

                $referrer = $this->Referrers->get($data['id'], [
                    'contain' => []
                ]);
                if ($referrer->partner_id != $this->user['partner_id']) {
                    throw new \Exception(__('The referrer does not belong to you. Please, try again.'));
                }

                if ($data['abn_lookup']['gst_effective_to'] == "0001-01-01") {
                    $data['gst_status'] = true;
                }

                if ($data['all_users_can_access'] == 1) {
                    // clear any specific partner user access
                    // when removing all user access, you must have at least one user with access
                    $map_table = TableRegistry::getTableLocator()->get('ReferrerPartnerUserAccessEntity');
                    $partner_user_access = $map_table->find()
                        ->where(['referrer_id' => $data['id']])
                        ->first();

                    if (!$partner_user_access) {
                        $map_row = $map_table->newEntity(['referrer_id' => $data['id'], 'partner_user_id' => $this->user['partner_user_id']]);
                        $map_table->save($map_row);
                    }

                    $map_table->deleteAll(
                        [
                            'referrer_id' => $data['id'],
                            'partner_user_id !=' => $this->user['partner_user_id'],
                        ]
                    );

                    $data['partner_users'] = [['partner_user_id' => $this->user['partner_user_id']]];
                }

                $referrer = $this->Referrers->patchEntity($referrer, $data);
                if ($this->Referrers->save($referrer)) {
                    return $this->setJsonResponse($referrer->toArray());
                }
                throw new \Exception(__('The referrer could not be saved. Please, try again.'));
            }
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
        }
    }

    public function switchMainPoc()
    {
        try {
            if (!$this->user['partner_id']) {
                throw new \Exception(__('Please login first.'));
            }
            $referrer = $this->Referrers->get($this->id, [
                "contain" => ["ReferrerPeople"]
            ]);
            if ($referrer->partner_id != $this->user['partner_id']) {
                throw new \Exception(__('The referrer does not belong to you. Please, try again.'));
            }
            $data = $this->request->getData();
            $people = [];
            $referrer_person_id = TableRegistry::getTableLocator()->get('ReferrerPeople')->ref2id($data['referrer_person_ref']);
            foreach ($referrer->referrer_people as $p) {
                $tmp = ['id' => $p->id];
                if ((int)$p->id === (int)$referrer_person_id) {
                    $tmp['is_point_of_contact'] = true;
                } else {
                    $tmp['is_point_of_contact'] = false;
                }
                $people[] = $tmp;
            }

            $this->Referrers->patchEntity($referrer, ['referrer_people' => $people], [
                'associated' => ['ReferrerPeople']
            ]);
            $this->Referrers->save($referrer);

            return $this->setJsonResponse($referrer->toArray());
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
        }
    }

    public function updateReferrerActiveStatus()
    {
        try {
            $data = $this->request->getData();
            $referrer = $this->Referrers->get($data['id']);
            if (!$referrer) {
                throw new \Exception("Referrer not found");
            }
            $referrer = $this->Referrers->patchEntity($referrer, ['active_status' => $data['active_status']]);
            if (!$this->Referrers->save($referrer)) {
                throw new \Exception("Unable to update referrer active status");
            }
            return $this->setJsonResponse(['success' => true, 'message' => 'Referrer active status updated successfully.']);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
        }
    }

    public function addCategory()
    {
        try {
            if (getenv('LEND_ENV') == 0) {
                $user = $this->Auth->user();
            } else {
                $user = $this->Auth->identify();
            }
            if (!$this->user['partner_id']) {
                throw new \Exception('Please login first', 403);
            }
            if (!$this->request->is('post')) {
                throw new \Exception("POST request only available.");
            }
            $schema = ['id' => null, 'category_id' => null];
            $data = array_intersect_key($this->request->data(), $schema);
            if (!isset($data['id']) || !isset($data['category_id'])) {
                throw new \Exception("referrer id and category id must be provided.");
            }
            $referrer = $this->Referrers->get($data['id']);

            if ($referrer->partner_id !== $user['partner_id']) {
                throw new \Exception('The referrer does not belong to you. Please, try again.', 403);
            }
            $categories_table = TableRegistry::getTableLocator()->get('PartnerReferrerCategoryEntity');
            $category = $categories_table->get($data['category_id']);
            if ($category->partner_id !== $user['partner_id']) {
                throw new \Exception('You are not authroised to access that category', 403);
            }
            $map_table = TableRegistry::getTableLocator()->get('PartnerReferrerCategoryMapEntity');
            $map_row = $map_table->newEntity(['referrer_id' => $data['id'], 'partner_referrer_category_id' => $data['category_id']]);
            if ($map_table->save($map_row)) {
                //saved
            } else {
                $errors = $map_row->getErrors();
                if (isset($errors['referrer_id']['_isUnique'])) {
                    $errorMessage = $errors['referrer_id']['_isUnique'];
                    throw new \Exception($errorMessage, 409);
                }
            }
            return $this->setJsonResponse(['success' => true, 'message' => 'Category has been added to the referrer.']);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
        }
    }

    public function removeCategory()
    {
        try {
            if (getenv('LEND_ENV') == 0) {
                $user = $this->Auth->user();
            } else {
                $user = $this->Auth->identify();
            }
            if (!$this->user['partner_id']) {
                throw new \Exception('Please login first', 403);
            }
            if (!$this->request->is('post')) {
                throw new \Exception("POST request only available.");
            }
            $schema = ['id' => null, 'category_id' => null];
            $data = array_intersect_key($this->request->data(), $schema);
            if (!isset($data['id']) || !isset($data['category_id'])) {
                throw new \Exception("referrer id and category id must be provided.");
            }
            $referrer = $this->Referrers->get($data['id']);
            if ($referrer->partner_id !== $user['partner_id']) {
                throw new \Exception('The referrer does not belong to you. Please, try again.', 403);
            }
            $categories_table = TableRegistry::getTableLocator()->get('PartnerReferrerCategoryEntity');
            $category = $categories_table->get($data['category_id']); //ensure category exists

            $map_table = TableRegistry::getTableLocator()->get('PartnerReferrerCategoryMapEntity');
            $map_table->deleteAll(
                [
                    'referrer_id' => $data['id'],
                    'partner_referrer_category_id' => $data['category_id'],
                ]
            );
            return $this->setJsonResponse(['success' => true, 'message' => 'Category has been removed from the referrer.']);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
        }
    }

    public function addPartnerUser()
    {
        try {
            if (getenv('LEND_ENV') == 0) {
                $user = $this->Auth->user();
            } else {
                $user = $this->Auth->identify();
            }
            if (!$this->user['partner_id']) {
                throw new \Exception('Please login first', 403);
            }
            if (!$this->request->is('post')) {
                throw new \Exception("POST request only available.");
            }
            $schema = ['id' => null, 'partner_user_id' => null, 'partner_user_ref' => null];
            $data = array_intersect_key($this->request->data(), $schema);
            if (!isset($data['id']) || (!isset($data['partner_user_id']) && !isset($data['partner_user_ref']))) {
                throw new \Exception("referrer id and partner must be provided.");
            }
            $referrer = $this->Referrers->get($data['id']);

            if ($referrer->partner_id !== $user['partner_id']) {
                throw new \Exception('The referrer does not belong to you. Please, try again.', 403);
            }
            $partnerUserId = $data['partner_user_ref'] ? LendInternalAuth::unhashPartnerUserId($data['partner_user_ref']) : $data['partner_user_id'];
            // dump($partnerUserId);
            $partnerUserTable = TableRegistry::getTableLocator()->get('PartnerUserEntity');
            $partnerUser = $partnerUserTable->get($partnerUserId);
            if ($partnerUser->partner_id !== $user['partner_id']) {
                throw new \Exception('You are not authroised to access that user', 403);
            }
            $map_table = TableRegistry::getTableLocator()->get('ReferrerPartnerUserAccessEntity');
            $map_row = $map_table->newEntity(['referrer_id' => $data['id'], 'partner_user_id' => $partnerUserId]);
            if ($map_table->save($map_row)) {
                //saved
            } else {
                $errors = $map_row->getErrors();
                if (isset($errors['referrer_id']['_isUnique'])) {
                    $errorMessage = $errors['referrer_id']['_isUnique'];
                    throw new \Exception($errorMessage, 409);
                }
            }
            return $this->setJsonResponse(['success' => true, 'message' => 'Partner user has been added to the referrer.']);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
        }
    }

    public function removePartnerUser()
    {
        try {
            if (getenv('LEND_ENV') == 0) {
                $user = $this->Auth->user();
            } else {
                $user = $this->Auth->identify();
            }
            if (!$this->user['partner_id']) {
                throw new \Exception('Please login first', 403);
            }
            if (!$this->request->is('post')) {
                throw new \Exception("POST request only available.");
            }
            $schema = ['id' => null, 'partner_user_id' => null, 'partner_user_ref' => null];
            $data = array_intersect_key($this->request->data(), $schema);
            if (!isset($data['id']) || (!isset($data['partner_user_id']) && !isset($data['partner_user_ref']))) {
                throw new \Exception("referrer id and partner must be provided.");
            }
            $referrer = $this->Referrers->get($data['id']);
            if ($referrer->partner_id !== $user['partner_id']) {
                throw new \Exception('The referrer does not belong to you. Please, try again.', 403);
            }
            $partnerUserId = $data['partner_user_ref'] ? LendInternalAuth::unhashPartnerUserId($data['partner_user_ref']) : $data['partner_user_id'];

            $partnerUserTable = TableRegistry::getTableLocator()->get('PartnerUserEntity');
            $partnerUser = $partnerUserTable->get($partnerUserId); //ensure user exists

            $map_table = TableRegistry::getTableLocator()->get('ReferrerPartnerUserAccessEntity');
            $map_table->deleteAll(
                [
                    'referrer_id' => $data['id'],
                    'partner_user_id' => $partnerUserId,
                ]
            );
            return $this->setJsonResponse(['success' => true, 'message' => 'Partner user has been removed from the referrer.']);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
        }
    }

    /**
     * Modify the results to remove `_joinData` from entities
     */
    private function removeEntitiesJoinData(&$entities, $property)
    {
        foreach ($entities as $entity) {
            $this->removeEntityJoinData($entity, $property);
        }
    }

    /**
     * Modify the results to remove category `_joinData` for a entity
     */
    private function removeEntityJoinData(&$entity, $property)
    {
        foreach ($entity->$property as $propertyRow) {
            unset($propertyRow->_joinData);
        }
    }
}
