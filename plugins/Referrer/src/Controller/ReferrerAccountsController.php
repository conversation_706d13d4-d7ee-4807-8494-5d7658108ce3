<?php

namespace Referrer\Controller;

use App\Lend\LendInternalAuth;
use Refer<PERSON>\Controller\AppController;
use Cake\Log\Log;
use Cake\ORM\TableRegistry;
use Firebase\JWT\JWT;
use Cake\Utility\Security;
use Cake\Auth\DefaultPasswordHasher;
use App\Auth\LendAuthenticate;
use App\Lend\ReferrerHelper;
use App\Model\Entity\Referrer;

/**
 * ReferrerAccounts Controller
 */
class ReferrerAccountsController extends AppController
{
    public function initialize()
    {
        parent::initialize();
        $this->Auth->allow(['checkCredentials', 'forgotPassword', 'details', 'update', 'dashboard']);
    }

    /**
     * Check account credentials 
     */
    public function checkCredentials()
    {
        try {
            $data = $this->request->getData();
            if (!$this->request->is('post')) {
                throw new \Exception("Only POST request is available.", 405);
            }
            $apiKey = $this->request->getHeaderLine('referrers-api-key');
            if (empty($apiKey)) {
                throw new \Exception("Invalid referrers-api-key", 401);
            }
            if (is_array($apiKey)) {
                $apiKey = $apiKey[0];
            }
            if (empty($apiKey) || ($apiKey !== getenv('REFERRERS_API_KEY'))) {
                throw new \Exception("Invalid referrers-api-key", 401);
            }
            $acceptedFields = ['email', 'password'];
            $data = array_intersect_key($data, array_flip($acceptedFields));
            if (empty($data['email'])) {
                throw new \Exception("email must be provided");
            }
            if (empty($data['password'])) {
                throw new \Exception("password must be provided");
            }

            //get the account
            $referrerAccountsTable = TableRegistry::getTableLocator()->get('ReferrerAccounts');
            $account = $referrerAccountsTable->find()->where(['email' => $data['email']])->first();
            if (!$account) {
                return $this->setJsonResponse(['success' => false, 'message' => "Invalid credentials"]);
            }

            $validPassword = (new DefaultPasswordHasher)->check($data['password'], $account['password']);
            if (!$validPassword) {
                return $this->setJsonResponse(['success' => false, 'message' => "Invalid credentials"]);
            }
            $ref = LendInternalAuth::hashReferrerAccountId($account['id']);
            return $this->setJsonResponse(['success' => true, 'referrer_account_ref' => $ref]);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
        }
    }

    public function forgotPassword() {
        try {
            $data = $this->request->getData();
            $accountEmail = $data['email'];

            if (empty($accountEmail)) {
                throw new \Exception("Email must be provided", 400);
            }

            $apiKey = $this->request->getHeaderLine('referrers-api-key');
            if (empty($apiKey)) {
                throw new \Exception("Invalid referrers-api-key", 401);
            }
            if (is_array($apiKey)) {
                $apiKey = $apiKey[0];
            }
            if (empty($apiKey) || ($apiKey !== getenv('REFERRERS_API_KEY'))) {
                throw new \Exception("Invalid referrers-api-key", 401);
            }

            //get the account
            $referrerAccountsTable = TableRegistry::getTableLocator()->get('ReferrerAccounts');
            $account = $referrerAccountsTable->find()->where(['email' => $accountEmail])->first();
            if (!$account) {
                return $this->setJsonResponse(['success' => false, 'message' => "No account found with the given email"]);
            }

            $referrerPeopleTable = TableRegistry::getTableLocator()->get('ReferrerPeople');
            $referrerPeople = $referrerPeopleTable->find()->where(['referrer_account_id' => $account->id])->first();

            if (empty($referrerPeople)) {
                return $this->setJsonResponse(['success' => false, 'message' => "No referrer person found for the given account"]);
            }

            $referrers = TableRegistry::getTableLocator()->get('Referrers')->get($referrerPeople->referrer_id);

            if (empty($referrers)) {
                return $this->setJsonResponse(['success' => false, 'message' => "No referrer found for the given account"]);
            }

            $referrerInvitationsTable = TableRegistry::getTableLocator()->get('ReferrerInvitations');
            $invitation = $referrerInvitationsTable->find()->where(['referrer_person_id'=> $referrerPeople->id, 'referrer_account_id' => $account->id])->first();

            if (empty($invitation)) {
                return $this->setJsonResponse(['success' => false, 'message' => "No invitation found for the given account"]);
            }

            $partnerUsersTable = TableRegistry::getTableLocator()->get('PartnerUsers');
            $partnerUser = $partnerUsersTable->find()->where(['partner_user_id' => $invitation->partner_user_id])->first();

            if (empty($partnerUser)) {
                return $this->setJsonResponse(['success' => false, 'message' => "No partner user found for the given account"]);
            }

            $forgotPasswordCode = $referrerAccountsTable->createForgotPasswordCode($account->id);

            $extraData = [
                'referrer_first_name' => $account->first_name,
                'referrer_last_name' => $account->last_name,
                'referrer_name' => $account->first_name.' '.$account->last_name,
                'from' => $partnerUser->email,
                'to' => $account->email,
                'referrer_forgot_password_code' => $forgotPasswordCode,
            ];
            $code = 'ReferrerForgotPassword';

            $notification = TableRegistry::getTableLocator()->get('PartnerNotifications')->sendPartnerNotifications($referrers->partner_id, $code, false, [] , $invitation->partner_user_id, $extraData);

            if ($notification[0]['successfully_sent'] === false) {
                throw new \Exception("Failed to send the invitation.");
            }

            return $this->setJsonResponse(['success' => true]);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
        }
    }

    public function details()
    {
        try {
            if (!$this->request->is('get')) {
                throw new \Exception("Only GET request is available.", 405);
            }
            $accountId = $this->getAccountId();
            $contain = [
                'ReferrerPeople' => ['fields' => [
                    'referrer_account_id',
                    'referrer_person_ref',
                    'first_name',
                    'last_name',
                    'contact_number',
                    'email',
                    'is_point_of_contact',
                    'assigned_consumer_partner_user_id',
                    'assigned_commercial_partner_user_id',
                    'active_status',
                    'created',
                    'updated',
                    'admin',
                ]],
                'ReferrerPeople.Referrers' => [
                    'fields' => ['nickname','id'], // we will delete this id later //
                    'ReferrerPeople' => [
                        'fields' => ['referrer_id','referrer_person_ref','first_name', 'last_name', 'contact_number', 'email']
                    ],
                    'PartnerEntity' => ['fields' => [
                        'company_name',
                        'registered_name',
                        'trading_name',
                        'organisation_name',
                        'logo',
                        'brand_colour',
                        'affiliate_id'
                    ]],
                ],
            ];
            $options = ['contain' => $contain];
            $options['fields'] = [
                'id',
                'email',
                'first_name',
                'last_name',
                'contact_number',
                'created',
            ];
            $referrerAccountsTable = TableRegistry::getTableLocator()->get('ReferrerAccounts');
            $account = $referrerAccountsTable->get($accountId, $options);
            $account = $account->toArray();

            foreach ($account['referrer_people'] as $person) {
                if (intval($account['id']) === intval($person['referrer_account_id'])) {
                    $account['current_referrer_person_ref'] = $person['referrer_person_ref'];
                    $account['current_referrer_person_is_admin'] = $person['admin'];
                    break;
                }
            }

            foreach ($account['referrer_people'] as $i => $people) {

                $referrerHelper = new ReferrerHelper();
                $assignedConsumerBroker= $referrerHelper->getAssignedBroker($account['referrer_people'][$i], 'assigned_consumer_partner_user_ref');
                if ($assignedConsumerBroker) {
                    $account['referrer_people'][$i]['assigned_consumer_broker'] = $assignedConsumerBroker;
                }
    
                $assignedCommercialBroker = $referrerHelper->getAssignedBroker($account['referrer_people'][$i], 'assigned_commercial_partner_user_ref');
                if ($assignedCommercialBroker) {
                    $account['referrer_people'][$i]['assigned_commercial_broker'] = $assignedCommercialBroker;
                }

            }

            foreach ($account['referrer_people'] as $i => $people) {
                unset($account['referrer_people'][$i]['assigned_consumer_partner_user_id']);
                unset($account['referrer_people'][$i]['assigned_commercial_partner_user_id']);
                unset($account['referrer_people'][$i]['referrer_account_id']);
                unset($account['referrer_people'][$i]['referrer']['full_address']);
                unset($account['referrer_people'][$i]['referrer']['partner']['partner_ref']);
                
                if (isset($account['referrer_people'][$i]['referrer']['referrer_people'])) {
                    foreach ($account['referrer_people'][$i]['referrer']['referrer_people'] as $j => $refPerson) {
                        $keepFields = [
                            'full_name',
                            'referrer_person_ref'
                        ];
                        foreach ($refPerson as $field => $value) {
                            if (!in_array($field, $keepFields)) {
                                unset($account['referrer_people'][$i]['referrer']['referrer_people'][$j][$field]);
                            }
                        }
                    }
                }
            }

            return $this->setJsonResponse($account);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
        }
    }

    public function update()
    {
        try {
            if (!$this->request->is('post')) {
                throw new \Exception("Only POST request is available.", 405);
            }
            $accountId = $this->getAccountId();
            $acceptedFields = [
                'email',
                'first_name',
                'last_name',
                'contact_number',
                'password'
            ];
            $data = $this->request->getData();
            $data = array_intersect_key($data, array_flip($acceptedFields));
            //password
            if ($data['password']) {
                //do complexity checks
                if (!preg_match('/^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[!~@#$%^&*()_+\-=\[\]{};\':"\\|,.<>\/?]).{8,}$/', $data['password'])) {
                    throw new \Exception("Password doesn't meet the requirements.");
                }
                $data['password'] = (new DefaultPasswordHasher)->hash($data['password']);
            }
            $referrerAccountsTable = TableRegistry::getTableLocator()->get('ReferrerAccounts');
            $account = $referrerAccountsTable->get($accountId);
            $account = $referrerAccountsTable->patchEntity($account,  $data);
            $referrerAccountsTable->save($account);
            return $this->setJsonResponse(['success' => true, 'message' => 'Referrer account updated']);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
        }
    }

    /**
     * get Account id from JWT token sent in header
     */
    private function getAccountId()
    {
        $headers = $this->request->getHeaders();
        if (empty($headers['Token'])) {
            throw new \Exception("Token must be provided", 405);
        }
        $tkn = $headers['Token'];
        if (is_array($headers['Token'])) {
            $tkn = $headers['Token'][0];
        }
        //get info from token
        $token = $this->decodeToken($tkn);
        if (empty($token['referrer_account_ref'])) {
            throw new \Exception("referrer_account_ref missing in token");
        }
        $referrerAccountId = LendInternalAuth::unhashReferrerAccountId($token['referrer_account_ref']);
        if (empty($referrerAccountId)) {
            throw new \Exception("invalid referrer_account_ref");
        }
        return $referrerAccountId;
    }

    /**
     * Check Token expiry, decode it and get payload
     */
    private function decodeToken($token)
    {
        $result = LendAuthenticate::decode(['allowedAlgs' => ['HS256']], $token);
        $now = time();
        if (!$result['success']) {
            throw new \Exception("invalid token", 401);
        } else {
            $result['payload'] = (array)$result['payload'];
            if (empty($result['payload']['exp']) || ($result['payload']['exp'] <= $now)) {
                throw new \Exception("invalid token", 401);
            }
        }
        return $result['payload'];
    }

    private function getReferrerPeopleFromToken()
    {
        $headers = $this->request->getHeaders();
        if (empty($headers['Token'])) {
            throw new \Exception("Token must be provided", 405);
        }
        $tkn = $headers['Token'];
        if (is_array($headers['Token'])) {
            $tkn = $headers['Token'][0];
        }
        $token = $this->decodeToken($tkn);
        if (empty($token['referrer_person_ref'])) {
            throw new \Exception("referrer_person_ref missing in token");
        }
        return $token['referrer_person_ref'];
    }

    public function dashboard()
    {
        try {
            $data = $this->request->getData();
            $filter = $data['filters']['referrer_person_refs'];
            if (empty($filter)) {
                $filter[] = $this->getReferrerPeopleFromToken();
            }
            
            $referrerPeopleTable = TableRegistry::getTableLocator()->get('ReferrerPeople');
            
            $referrerPeople = $referrerPeopleTable->find()
                ->contain([
                    'ReferrerAccounts' => [
                        'fields' => ['id', 'email', 'first_name', 'last_name', 'contact_number']
                    ],
                    'Referrers' => [
                        'fields' => ['id', 'nickname'],
                        'PartnerEntity' => [
                            'fields' => [
                                'company_name',
                                'registered_name',
                                'trading_name',
                                'organisation_name',
                                'logo',
                                'brand_colour'
                            ]
                        ]
                    ],
                    'LeadEntity' => [
                        'fields' => [
                            'lead_id',
                            'lead_ref',
                            'created',
                            'referrer_person_id',
                            'created',
                            'is_archived',
                        ],
                        'conditions' => ['LeadEntity.is_archived' => 0],
                        'SettlementReviewsEntity'=> [
                            'fields' => [
                                'lead_id',
                                'commission_id',
                                'settlement_date',
                                'referrer_commission',
                                'settlement_date',
                            ]
                        ]
                    ]
                ])
                ->select([
                    'id',
                    'referrer_person_ref',
                    'first_name',
                    'last_name',
                    'contact_number',
                    'email',
                    'is_point_of_contact',
                    'active_status',
                    'created',
                    'updated'
                ])
                ->where(['ReferrerPeople.referrer_person_ref IN' => $filter])
                ->toArray();

            if (empty($referrerPeople)) {
                throw new \Exception("No data found for the given reference", 404);
            }

            // Process the data for graphs
            $today = new \DateTime();
        
            // Initialize last 7 days array with proper structure
            $last7Days = [];
            for ($i = 6; $i >= 0; $i--) {
                $date = (clone $today)->modify("-$i days");
                $dayName = $date->format('D');
                $last7Days[$dayName] = ['day' => $dayName, 'referrals' => 0];
            }

            // Initialize last 6 months arrays
            $monthlyReferrals = [];
            $monthlyEarnings = [];
            for ($i = 5; $i >= 0; $i--) {
                $date = (clone $today)->modify("-$i months");
                $monthYear = $date->format('M Y');
                $month = $date->format('M');
                $monthlyReferrals[$monthYear] = ['month' => $monthYear, 'referrals' => 0];
                $monthlyEarnings[$month] = ['month' => $month, 'earnings' => 0];
            }

            foreach ($referrerPeople as $person) {
                foreach ($person->leads as $lead) {
                    // Process last 7 days referrals
                    $leadDate = new \DateTime($lead->created);
                    $dayDiff = $today->diff($leadDate)->days;
                    if ($dayDiff <= 6) {
                        $dayName = $leadDate->format('D');
                        $last7Days[$dayName]['referrals']++;
                    }

                    // Process monthly referrals
                    $monthYear = $leadDate->format('M Y');
                    if (isset($monthlyReferrals[$monthYear])) {
                        $monthlyReferrals[$monthYear]['referrals']++;
                    }

                    // Process earnings from settlement reviews
                    if (!empty($lead->settlement_reviews)) {
                        foreach ($lead->settlement_reviews as $review) {
                            if ($review->referrer_commission) {
                                $settlementDate = $review->settlement_date ? 
                                    new \DateTime($review->settlement_date) : 
                                    new \DateTime($lead->created);
                                $month = $settlementDate->format('M');
                                if (isset($monthlyEarnings[$month])) {
                                    $monthlyEarnings[$month]['earnings'] += $review->referrer_commission;
                                }
                            }
                        }
                    }
                }
            }

            $response = [
                'last7DaysData' => array_values($last7Days),
                'monthlyData' => array_values($monthlyReferrals),
                'earningsData' => array_values($monthlyEarnings),
                //'rawData' => $referrerPeople // Include raw data if needed
            ];

            return $this->setJsonResponse($response);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return $this->setJsonResponse(
                ['error' => $e->getMessage()], 
                $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400
            );
        }
    }

    /**
     * to test token 
     * TO-DO remove this once no longer needed
     */
    private function test()
    {
        $id = 1;
        $ref = LendInternalAuth::hashReferrerAccountId($id);
        $token = JWT::encode(
            [
                'referrer_account_ref' => $ref,
                // 'exp' =>  (time() + 86400)
                'exp' => (time())
                // 'referrer_person_ref' => '4X5kXrd',
            ],
            Security::salt()
        );
        return $this->setJsonResponse(['success' => true, 'token' => $token]);

        // $result = LendAuthenticate::decode(['allowedAlgs' => ['HS256']], $token);
        // dump($result);
    }
}
