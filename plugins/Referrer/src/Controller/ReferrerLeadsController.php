<?php

namespace Referrer\Controller;

use App\Lend\LendInternalAuth;
use Referrer\Controller\AppController;
use Cake\Log\Log;
use Cake\ORM\TableRegistry;
use App\Auth\LendAuthenticate;
use App\Lend\LeadHelper;
use Cake\Datasource\ConnectionManager;
use DateTime;

/**
 * ReferrerLeadsController
 */
class ReferrerLeadsController extends AppController
{
    public function initialize()
    {
        parent::initialize();
        $this->Auth->allow(['add', 'list', 'addLeadUploads']);
    }

    /**
     * Add a lead - uses same payload as v2 API lead add endpoint
     */
    public function add()
    {
        try {
            if (!$this->request->is('post')) {
                throw new \Exception("Only POST request is available.", 405);
            }
            $personId = $this->getPersonId();
            $contain = [
                'Referrers' => ['fields' => ['id', 'partner_id']],
            ];
            $options = ['contain' => $contain];
            $options['fields'] = [
                'id',
                'referrer_id',
                'referrer_person_ref',
            ];
            $referrerPeopleTable = TableRegistry::getTableLocator()->get('ReferrerPeople');
            $person = $referrerPeopleTable->get($personId, $options);
            $partnerId = $person->referrer->partner_id;

            $data = $this->request->getData();
            $data['source'] = 'Referrers';
            $data['referrer_person_ref'] =  $person->referrer_person_ref;
            $data['partner_user_lead']['partner_user_id'] = LendInternalAuth::unhashPartnerUserId($data['partner_user_lead']['partner_user_id']);

            $leadRef = LeadHelper::addLead($data, null, $partnerId);
            $leadId = LendInternalAuth::unhashLeadId($leadRef);
            $lead_owner = $this->getTableLocator()->get('LeadOwnersEntity')->find('all')->where(['lead_id' => $leadId, 'point_of_contact' => true])->first();

            if ($leadRef) {
                $result = ['success' => true, 'lead_ref' => $leadRef];

                if (isset($data['notes'])) {
                    $lead_notes = [
                        'lead_id' => $leadId,
                        'notes' => $data['notes'],
                        'created' => date('Y-m-d H:i:s'),
                        'status' => 'Active'
                    ];

                    $leadNoteId = LeadHelper::addLeadNote($lead_notes);
                    $result['lead_note_id'] = $leadNoteId;
                }

                if ($lead_owner) {
                    $result['lead_owner_id'] = LendInternalAuth::hashOwnerId($lead_owner->owner_id);
                }

                return $this->setJsonResponse($result);
            }

            return $this->setJsonResponse(['success' => false]);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
        }
    }

    /**
     * Add lead uploads
     */
    public function addLeadUploads()
    {
        try {
            if (!$this->request->is('post')) {
                throw new \Exception("Only POST request is available.", 405);
            }
            $data = $this->request->getData();
            $personId = $this->getPersonId();
            $leadHelper = new LeadHelper();
            $leadUploadId = $leadHelper->addLeadUploads($data);

            if ($leadUploadId) {
                return $this->setJsonResponse(['success' => true, 'lead_upload_id' => $leadUploadId]);
            } else {
                return $this->setJsonResponse(['success' => false]);
            }
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
        }
    }

    /**
     * get a list of leads
     *
     * @param  int $page
     * @param  int $pagesize
     * @return void
     */
    public function list()
    {
        try {
            if (!$this->request->is('post')) {
                throw new \Exception("Only POST request is available.", 405);
            }
            $personId = $this->getPersonId();
            $schema = [
                //"referrer_person_refs" => null,
                "page" => null,
                "pagesize" => null,
                "type" => null,
                "start_date" => null,
                "end_date" => null,
                "filter_by_date" => null,
            ];

            $filter_data = array_intersect_key($this->request->getData(), $schema);
            //if not admin or not set, replace person ref in referrer_person_refs
            $referrerPeopleTable = TableRegistry::getTableLocator()->get('ReferrerPeople');
            $person = $referrerPeopleTable->get($personId);


            //leave this for now as commented out
            // foreach ($filter_data['referrer_person_refs'] as $i => $ref) {
            //     $id = LendInternalAuth::unhashReferrerPeopleId($ref);
            //     if (empty($id)) {
            //         throw new \Exception("Invalid value in referrer_person_refs");
            //     } else {
            //         $filter_data['referrer_person_refs'][$i] = $id;
            //     }
            // }

            if($person->admin){

                $refs = $referrerPeopleTable->find('list', [
                    'keyField' => 'id',
                    'valueField' => 'id'
                ])->where(['referrer_id' => $person->referrer_id])->toArray();
                
                $filter_data['referrer_person_ids'] = array_values($refs);
            }
            else {
                //if not admin, replace person ref in referrer_person_ids
                $filter_data['referrer_person_ids'] = $personId;
            }

            $where = ['ReferrerPeople.referrer_id' === $person->referrer_id];
            if (!isset($filter_data['page'])) {
                $filter_data['page'] = 1;
            }
            if (!isset($filter_data['pagesize'])) {
                $filter_data['pagesize'] = 10;
            }
            $types = [
                'open' => ["created"],
                'lost' => ["created"],
                'settled' => ["created", "settled"],
            ];
            if (!isset($filter_data['type'])) {
                $filter_data['type'] = "open";
            } elseif (!in_array($filter_data['type'], array_keys($types))) {
                throw new \Exception("Invalid type value");
            }
            if (
                isset($filter_data['filter_by_date'])
                && !in_array($filter_data['filter_by_date'], $types[$filter_data['type']])
            ) {
                throw new \Exception("Invalid filter_by_date value");
            }
            if (!isset($filter_data['filter_by_date'])) {
                $filter_data['filter_by_date'] = "created";
            }

            if (!isset($filter_data['start_date']) || !$this->isValidDate($filter_data['start_date'])) {
                throw new \Exception("Invalid start_date value" . $filter_data['start_date']);
            }

            if (!isset($filter_data['end_date']) || !$this->isValidDate($filter_data['end_date'])) {
                throw new \Exception("Invalid end_date value");
            }

            $leadEntityFields = [
                'lead_id',
                'created',
                'lead_type',
                'amount_requested',
                'product_type_id',
                'man_status_id',
                'purpose_id',
                'referrer_person_id',
                'source'
            ];
            $contain = [
                'PocOwner' => ['fields' => ['lead_id', 'first_name', 'last_name']],
                'FrmPurposeEntity' => ['fields' => ['purpose_id', 'purpose']],
                'LeadAssetFinanceEntity' => ['fields' => ['lead_id', 'finance_amount']],
                'LeadNotesEntity' => ['fields' => ['note_id', 'lead_id', 'notes', 'created'], 'conditions' => ['is_referrer' => 1, 'status']],
                'ManStatusEntity' => ['fields' => ['id', 'status_name']],
                'ManStatusHistoryEntity' => ['fields' => ['id', 'lead_id', 'created']],
                'LeadAssociatedDataEntity' => ['fields' => ['lead_id', 'max_note_id']],
                'LeadAssociatedDataEntity.LeadNotesEntity' => ['fields' => ['note_id', 'notes', 'created']],
                'ReferrerPeople' => ['fields' => ['id', 'first_name', 'last_name']],
                'SettlementReviewsEntity' => ['fields' => ['lead_id', 'settlement_date']],
            ];
            $options = ['contain' => $contain];

            $where = array_merge($where, $this->convertConditions($filter_data));
            $where[] = ['LeadEntity.is_archived' => 0]; 
            
            $leads = TableRegistry::getTableLocator()
                ->get('LeadEntity', [
                    'connection' => ConnectionManager::get('reader_db')
                ])
                ->find('all', $options)
                ->select($leadEntityFields)
                ->distinct(['LeadEntity.lead_id'])
                ->where($where);

            if ($filter_data['page']) {
                $leads->limit($filter_data['pagesize'])->offset(($filter_data['page'] - 1) * $filter_data['pagesize']);
            }
            $fullCount = $leads->count();
            $leads = $this->cleanData($leads->toArray());

            $result = ['success' => true, 'full_total' => $fullCount, 'total' => count($leads), 'leads' => $leads];
            return $this->setJsonResponse($result);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    /**
     * Clead leads data
     */
    private function cleanData($leads)
    {
        foreach ($leads as $i => $lead) {
            unset($leads[$i]['lead_id']);
            unset($leads[$i]['referrer_person_id']);
            unset($leads[$i]['man_status_id']);
            unset($leads[$i]['purpose_id']);
            foreach ($leads[$i]['settlement_reviews'] as $j => $review) {
                unset($leads[$i]['settlement_reviews'][$j]['lead_id']);
            }
            unset($leads[$i]['referrer_person']['id']);
            unset($leads[$i]['lead_associated_data']['lead_id']);
            foreach ($leads[$i]['lead_notes'] as $j => $note) {
                unset($leads[$i]['lead_notes'][$j]['lead_id']);
            }
            if (isset(($leads[$i]['lead_associated_data']))) {
                unset($leads[$i]['lead_associated_data']['lead_id']);
            }
            if (!empty($leads[$i]['man_status_histories'])) { //omly retain most recent history record
                $leads[$i]['man_status_histories'] = [
                    $leads[$i]['man_status_histories'][0]
                ];
                unset($leads[$i]['man_status_histories'][0]['id']);
                unset($leads[$i]['man_status_histories'][0]['lead_id']);
            }
            unset($leads[$i]['man_status']['id']);
            unset($leads[$i]['asset_finance']['lead_id']);
            unset($leads[$i]['purpose']['purpose_id']);
            unset($leads[$i]['owner_poc']['lead_id']);
        }
        return $leads;
    }

    /**
     * Create conditions
     */
    private function convertConditions($filterData)
    {
        $conditions = [];
        //referrer_person_refs
        $conditions[] = ['ReferrerPeople.id IN ' => $filterData['referrer_person_ids']];
        //lead conditions
        $typeConditions = [
            'open' => ['is_closed' => 0],
            'lost' => [
                'OR' => [
                        'LeadEntity.is_closed' => 1,
                        'LeadEntity.is_archived' => 1,
                    ],
                'ManStatusEntity.is_settled' => 0,
            ],
            'settled' => ['ManStatusEntity.is_settled' => 1]
        ];
        $conditions[] = $typeConditions[$filterData['type']];
        //date
        $dateFields = [
            'created' => 'LeadEntity.created',
            'settled' => 'SettlementReviewsEntity.settlement_date',
        ];
        $dateField = $dateFields[$filterData['filter_by_date']];
        $conditions[] = ["{$dateField} >=" => $filterData['start_date']];
        $conditions[] = ["{$dateField} <=" => $filterData['end_date']];
        return $conditions;
    }


    /**
     * Check if date string is in valid format
     */
    public function isValidDate($date, $format = 'Y-m-d')
    {
        $d = DateTime::createFromFormat($format, $date);
        return $d && $d->format($format) === $date;
    }

    /**
     * get Referrer person id from JWT token sent in header
     */
    private function getPersonId()
    {
        $headers = $this->request->getHeaders();
        if (empty($headers['Token'])) {
            throw new \Exception("Token must be provided", 405);
        }
        $tkn = $headers['Token'];
        if (is_array($headers['Token'])) {
            $tkn = $headers['Token'][0];
        }
        //get info from token
        $token = $this->decodeToken($tkn);
        if (!$token['referrer_account_ref']) { //unused here but must always be present
            throw new \Exception("referrer_account_ref missing in token");
        }
        $referrerAccountId = LendInternalAuth::unhashReferrerAccountId($token['referrer_account_ref']);
        if (empty($referrerAccountId)) {
            throw new \Exception("invalid referrer_account_ref");
        }
        if (!$token['referrer_person_ref']) {
            throw new \Exception("referrer_person_ref missing in token");
        }
        $referrerPersonId = LendInternalAuth::unhashReferrerPeopleId($token['referrer_person_ref']);
        if (empty($referrerPersonId)) {
            throw new \Exception("invalid referrer_person_ref");
        }
        return $referrerPersonId;
    }

    /**
     * Check Token expiry, decode it and get payload
     */
    private function decodeToken($token)
    {
        $result = LendAuthenticate::decode(['allowedAlgs' => ['HS256']], $token);
        $now = time();
        if (!$result['success']) {
            throw new \Exception("invalid token", 401);
        } else {
            $result['payload'] = (array)$result['payload'];
            if (empty($result['payload']['exp']) || ($result['payload']['exp'] <= $now)) {
                throw new \Exception("invalid token", 401);
            }
        }
        return $result['payload'];
    }
}
