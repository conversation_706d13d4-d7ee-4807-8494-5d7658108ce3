<?php
namespace Statements\Controller;

use App\Lend\LendInternalAuth;
use Cake\Core\Configure;
use Statements\Controller\AppController;

use Cake\Log\Log;
use Cake\ORM\TableRegistry;
use Hashids\Hashids;
use App\Lend\CurlHelper;

class StatementsController extends AppController
{

  // private $institutions;
  public function initialize()
  {
    parent::initialize();
    $this->Auth->allow(['getAccountDetails', 'getLead', 'getPartnerAccount', 'addLeadUploads', 'savePartnerStatementTrans', 'updateLeadBrokerflows']);
  }

  public function getAccountDetails()
  {
    try {
      if (!$this->request->is('post')) {
        throw new \Exception("POST request only available.");
      }
      $partner_account_id = $this->request->getData('partner_account_id');

      $partner_account = TableRegistry::getTableLocator()->get('PartnerAccounts')->get(['partner_account_id' => $partner_account_id], [
        'contain' => [
          'PartnerAccountMeta',
          'partnerAccountMeta.ConfigIndustries',
          'PartnerAccountPeople',
          'PartnerAccountBs',
          'LeadEntity'
        ]
      ]);

      $data = [
        'organisation_name' => null,
        'industry' => null,
        'account_ref' => null,
        'people' => [],
        'account_type' => $partner_account->account_type
      ];
      if (!empty($partner_account->abn)) {
        $data['organisation_name'] = $partner_account->meta->organisation_name ?? null;
        $data['industry'] = $partner_account->meta->industry->tree ?? null;
        $data['account_ref'] = $partner_account->account_ref ?? null;
        foreach ($partner_account->people as $person) {
          $data['people'][] = [
            'first_name' => $person['first_name'] ?? null,
            'last_name' => $person['last_name'] ?? null
          ];
        }
      }

      return $this->setJsonResponse(['success' => true, 'data' => $data]);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()]);
    }
  }

  private function _generateSignature($fields)
  {
    $sign = base64_encode(hash_hmac('sha256', json_encode($fields, JSON_UNESCAPED_SLASHES) . getenv('BANKFEEDS_SECRET'), getenv('BANKFEEDS_SECRET'))); // to base64
    return $sign;
  }

  public function getLead()
  {
    try {
      if (!$this->request->is('post')) {
        throw new \Exception("POST request only.");
      }
      if ($this->request->getData('lead_ref')) {
        $param = ['lead_ref' => $this->request->getData('lead_ref')];
      } elseif ($this->request->getData('lead_id')) {
        $param = ['lead_id' => $this->request->getData('lead_id')];
      } else {
        return $this->setJsonResponse(['success' => false, 'message' => "Wrong parameters."]);
      }
      if ($this->_generateSignature($param) != $this->request->query['signature']) {
        return $this->setJsonResponse(['error' => 'Invalid signature']);
      }
      $this->loadModel('Leads');
      $data = $this->Leads->getLead($param);
      return $this->setJsonResponse(['success' => true, 'data' => ["lead_id" => $data['lead_id'], "lead_ref" => $data['lead_ref'], "partner_id" => $data['partner_id'], "partner_account_id" => $data['account_id'], "lead_type" => $data['lead_type']]]);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()]);
    }
  }

  public function getPartnerAccount($partner_account_ref)
  {
    try {
      if ($this->_generateSignature(['partner_account_ref' => $partner_account_ref]) != $this->request->query['signature']) {
        return $this->setJsonResponse(['error' => 'Invalid signature']);
      }
      $hashids = new Hashids('partner_accounts', 7);
      $account_id = $hashids->decode($partner_account_ref)[0];
      $partner_account = TableRegistry::getTableLocator()->get('PartnerAccountEntity')->get($account_id);
      return $this->setJsonResponse(['success' => true, 'data' => ["partner_id" => (string) $partner_account->partner_id, "partner_account_id" => (string) $partner_account->partner_account_id, "account_ref" => $partner_account->account_ref, "lead_tyle" => $partner_account->account_type === 'company' ? 'commercial' : 'consumer']]);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()]);
    }
  }

  public function addLeadUploads()
  {
    try {
      if (!$this->request->is('post')) {
        throw new \Exception("POST request only.");
      }
      $this->loadModel('LeadUploads');
      $data = $this->request->getData();
      if ($this->_generateSignature($data) != $this->request->query['signature']) {
        return $this->setJsonResponse(['error' => 'Invalid signature']);
      }
      $thefile = $data['file'];
      $partner_account_id = $data['partner_account_id'];
      $lead_id = $data['lead_id'];
      if ($lead_id == "null")
        $lead_id = null;

      $this->loadModel('Leads');
      if ("json" == pathinfo($thefile['s3filename'], PATHINFO_EXTENSION)) {
        $this->LeadUploads->createRecord(
          array(
            'partner_account_id' => $partner_account_id,
            'lead_id' => $lead_id,
            'type' => 'bs', // bank statements
            'name' => $thefile['s3filename'],
            'path' => $thefile['s3path'],
            'size' => $thefile['size'],
            'description' => 'Provided by Bank Statements',
            'status' => '1',
            'created' => date('Y-m-d H:i:s')
          )
        );
      }

      $file_uploads_data_meta = [
        [
          "field_name" => "acceptable",
          "value" => "true"
        ],
        [
          "field_name" => "note",
          "value" => "Bank Statement from Illion"
        ]
      ];
      $file_uploads_data['full_path'] = $thefile['s3path'];
      $file_uploads_data['name'] = $thefile['s3filename'];
      $file_uploads_data['file_type'] = ("pdf" == pathinfo($thefile['s3filename'], PATHINFO_EXTENSION)) ? 'application/pdf' : 'text/html';
      $file_uploads_data['file_size'] = $thefile['size'];
      $file_uploads_data['uploaded_by'] = 'Client';
      $file_uploads_data['status'] = 'Active';
      if ($lead_id) {
        $this->Leads->updateLead(array('lead_id' => $lead_id, 'statements_uploaded' => '1', 'bs_doc_retrieved' => '1'));
        if (in_array(pathinfo($thefile['s3filename'], PATHINFO_EXTENSION), ['html', 'pdf'])) {
          $lead_table = TableRegistry::getTableLocator()->get('LeadEntity');
          $lead = $lead_table->get($lead_id, [
            'fields' => ['lead_type']
          ]);
          $partnerLeadUploadsTable = TableRegistry::getTableLocator()->get('PartnerLeadUploadsEntity');
          $file_uploads_data['lead_id'] = $lead_id;
          $file_uploads_data["partner_lead_uploads_meta"] = $file_uploads_data_meta;
          $file_uploads_data["partner_lead_uploads_meta"][] = [
            "field_name" => "specified",
            "value" => $lead->lead_type == "consumer" ? "Personal Bank Statement" : "Bank Statement"
          ];
          $partner_lead_upload = $partnerLeadUploadsTable->newEntity($file_uploads_data, [
            'associated' => ['PartnerLeadUploadsMetaEntity']
          ]);
          $partnerLeadUploadsTable->save($partner_lead_upload);
        }
      } else {
        if (in_array(pathinfo($thefile['s3filename'], PATHINFO_EXTENSION), ['html', 'pdf'])) {
          $partner_account_table = TableRegistry::getTableLocator()->get('PartnerAccountEntity');
          $partner_account = $partner_account_table->get($partner_account_id, [
            'fields' => ['account_type']
          ]);
          $partnerAccountUploadsTable = TableRegistry::getTableLocator()->get('PartnerAccountUploadsEntity');
          $file_uploads_data['partner_account_id'] = $partner_account_id;
          $file_uploads_data["partner_account_uploads_meta"] = $file_uploads_data_meta;
          $file_uploads_data["partner_account_uploads_meta"][] = [
            "field_name" => "specified",
            "value" => $partner_account->account_type == "consumer" ? "Personal Bank Statement" : "Bank Statement"
          ];
          if (substr($file_uploads_data['full_path'], -1) == '/') {
            $file_uploads_data['full_path'] = substr($file_uploads_data['full_path'], 0, -1);
          }
          $partner_lead_upload = $partnerAccountUploadsTable->newEntity($file_uploads_data, [
            'associated' => ['PartnerAccountUploadsMetaEntity']
          ]);
          $partnerAccountUploadsTable->save($partner_lead_upload);
        }
      }

      return $this->setJsonResponse(['success' => true]);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()]);
    }
  }

  public function updateLeadBrokerflows()
  {
    try {
      if (!$this->request->is('post')) {
        throw new \Exception("POST request only.");
      }
      $data = $this->request->getData();
      if ($this->_generateSignature($data) != $this->request->query['signature']) {
        return $this->setJsonResponse(['error' => 'Invalid signature']);
      }
      $leadBrokerflow = null;
      $leadBrokerflowsTable = TableRegistry::getTableLocator()->get('LeadBrokerflowsEntity');
      if(!empty($data['status']) && $data['status'] == "brokerflow_retrieved"){
        $leadBrokerflow = $leadBrokerflowsTable->find('all')
        ->where(['lead_id' => $data['lead_id'], "status" => 'brokerflow_submitted'])
        ->first();
      }else{
        $leadBrokerflow = $leadBrokerflowsTable->find('all')
        ->where(['lead_id' => $data['lead_id'], "status" => 'bankfeeds_submitted'])
        ->first();
      }
      
      if (!$leadBrokerflow) {
        $leadBrokerflow = $leadBrokerflowsTable->find('all')
        ->where(['lead_id' => $data['lead_id'], "bank_account_ids" => $data['bank_account_ids']])
        ->first();
        if(!$leadBrokerflow){
          $leadBrokerflowData = [
            'lead_id' => $data['lead_id'],
            'broker_flow_id' => '',
            'status' => "bankfeeds_submitted",
            'created' => date('Y-m-d H:i:s'),
            'updated' => null,
            'bank_account_ids' => $data['bank_account_ids'],
            'broker_flow_id_expired' => ''
          ];
          $leadBrokerflow = $leadBrokerflowsTable->newEntity($leadBrokerflowData);
        }
      } else {
        $leadBrokerflowPatchData = ['bank_account_ids' => $data['bank_account_ids'], 'updated' => date('Y-m-d H:i:s')];
        if (!empty($data['status'])) {
          $leadBrokerflowPatchData['status'] = $data['status'];
        }
        if (!empty($data['broker_flow_id'])) {
          $leadBrokerflowPatchData['broker_flow_id'] = $data['broker_flow_id'];
          $leadBrokerflow['broker_flow_id_expired'] = (new \DateTime(Configure::read('Lend.bs_doc_expired_days') . ' days'))->format('Y-m-d H:i:s');
        }
        $leadBrokerflowsTable->patchEntity($leadBrokerflow, $leadBrokerflowPatchData);
      }
      if (!empty($leadBrokerflow)) {
        $leadBrokerflowsTable->save($leadBrokerflow);
      }
      return $this->setJsonResponse(["success" => true, "data" => $leadBrokerflow]);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['error' => $e->getMessage()], $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400);
    }
  }

  public function savePartnerStatementTrans()
  {
    try {
      if (!$this->request->is('post')) {
        throw new \Exception("POST request only.");
      }
      $data = $this->request->getData();
      if ($this->_generateSignature($data) != $this->request->query['signature']) {
        return $this->setJsonResponse(['error' => 'Invalid signature']);
      }

      $this->loadModel('Subscription');
      $this->loadModel('PartnerStatementTrans');
      $partner_account_id = $data['partner_account_id'];
      $lead_id = $data['lead_id'];
      $service = $data['service'];
      if ($partner_account_id) {
        $partner_account = TableRegistry::getTableLocator()->get('PartnerAccounts')->find()->where(['partner_account_id' => $partner_account_id])->first();
        $partner_id = $partner_account->partner_id;
      } else if ($lead_id) {
        $this->loadModel('Leads');
        $lead = $this->Leads->getLead(['lead_id' => $lead_id]);
        $partner_id = $lead['partner_id'];
      }
      $cost = $this->Subscription->getPriceByPartnerAndService($partner_id, $service);
      if (!$cost) {
        throw new \Exception("No subscription plan found for partner: " . $partner_id);
      }
      $this->PartnerStatementTrans->addPartnerStatementTrans(
        array(
          'partner_account_id' => $partner_account_id,
          'lead_id' => $lead_id,
          'service' => $service, // bank statements
          'cost' => $cost['cost'],
          'status' => "Pending",
          'created' => date('Y-m-d H:i:s')
        )
      );
      return $this->setJsonResponse(['success' => true]);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()]);
    }
  }

  public function removeBankAccounts(){
    try {
      if (!$this->request->is('post')) {
        throw new \Exception("POST request only.");
      }
      $data = $this->request->getData();
      if(!$data['account_ref']){
        throw new \Exception("account_ref is required.");
      }
      $hashids = new Hashids('partner_accounts', 7);
      $partner_account_id = $hashids->decode($data['account_ref'])[0];
      $lead_id = null;
      if($data['lead_ref']){
        $lend_internal_auth = new LendInternalAuth;
        $lead_id = $lend_internal_auth->unhashLeadId($data['lead_ref']);  
      }
      $bankfeed_data = [
        "partner_account_id" => $partner_account_id,
        "lead_id" => $lead_id,
        "account_ids" => $data['account_ids']
      ];
      $curl = new CurlHelper(getenv('DOMAIN_BANKFEEDS') . "/remove-account");
      $response = $curl->post($bankfeed_data, true, ["x-api-key" => getenv('BANK_STATEMENT_API_KEY')]);
      return $this->setJsonResponse($response);
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()]);
    }
  }
}
