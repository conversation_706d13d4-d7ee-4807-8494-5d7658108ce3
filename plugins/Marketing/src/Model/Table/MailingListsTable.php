<?php

namespace Marketing\Model\Table;

use Cake\Log\Log;
use App\Model\Table\AppTable;
use App\Lend\LendInternalAuth;

class MailingListsTable extends AppTable
{
    private function createNewList($partnerUserId, $name, $query, $dynamic) {
        $sql = "INSERT INTO partner_emailer_mailing_lists SET partner_user_id = ?, `name` = ?, query = ? , `dynamic` = ?";
        $query = $this->DB->execute($sql, [$partnerUserId, $name, $query, $dynamic]);        
        $id = $query->lastInsertId();
        $ref = (new \App\Lend\LendInternalAuth)->hashLeadId($id);
        $sql = "UPDATE partner_emailer_mailing_lists SET ref = ? WHERE id = ?";
        $this->DB->execute($sql, [$ref, $id]);
        return $id;
    }

    private function addRecipients($recipients) {
        
        $recipientIds = [];
        foreach($recipients as $recipient) {
            //do it line by line since we need insert ids
            $sql = "INSERT INTO partner_emailer_recipients SET email = ?, first_name = ?, last_name = ?, mobile = ?, company_name = ?
                   ON DUPLICATE KEY UPDATE email = ?, id = last_insert_id(id)";
            $query = $this->DB->execute($sql, [$recipient['email'], $recipient['first_name'], $recipient['last_name'],
                                               $recipient['mobile'], $recipient['company_name'], $recipient['email']]);
            $recipientIds[] = $query->lastInsertId();
        }
        return $recipientIds;
    }

    private function addRecipientsToList($listId, $recipientIds) {


  


        //ignore if the recipient already exists in the list
        $sql = "INSERT IGNORE INTO partner_emailer_mailing_lists_recipients 
                (mailing_list_id, recipient_id) VALUES ";
       
        foreach($recipientIds as $recipientId) {
            $sql .= " ({$listId}, {$recipientId}),";
        }

        $sql = substr($sql, 0, -1);


       // echo($sql);

        //exit;
        $this->DB->execute($sql);
    }


    public function saveMailingList($list, $name, $partnerUserId, $query) {
        $listId = $this->createNewList($partnerUserId, $name, $query, 0);
        $recipientIds = $this->addRecipients($list); //how do i make them mine?
        $this->addRecipientsToList($listId, $recipientIds);
    }

    public function addToExistingMailingList($list, $ref) {
        $result = $this->getMailingListById($ref);
        $listId = $result['id'];
        $recipientIds = $this->addRecipients($list); //how do i make them mine?

        if(count($recipientIds) > 0){
            $this->addRecipientsToList($listId, $recipientIds);
        }
    }

    public function mailingListExists($mailingList)
    {
	    $params = [];
	    $params[] = $mailingList;
	    $sql = "SELECT ref, `name` FROM partner_emailer_mailing_lists AS lists WHERE archived=0 AND name = ? ";
	    $mailingLists = $this->DB->execute($sql, $params)->fetchAll('assoc');

	    $mailListNames = array_map(function($v){return $v['visible'] != 'Hidden' ? $v['name'] : '';}, $mailingLists);

	    if (in_array($mailingList, $mailListNames)) { // Update client
            return true;
        } else {
            return false;
        }
    }

    public function getMyMailingLists($partnerUserId, $search=false, $limit=false, $offset=false, $countOnly=false) {
        $extra = '';
        $params = [];
        $params[] = $partnerUserId;

        if($search) {
            $extra .= " AND lists.`name` LIKE ?";
            $params[] = "%$search%";
        }

        if(!$countOnly) {
            $extra .= " ORDER BY lists.`id` DESC";
        }

        if($limit && !$countOnly) {
            $extra .= " LIMIT $limit";
        }

        if($offset && !$countOnly) {
            $extra .= " OFFSET $offset";
        }

        $sql = "SELECT ref, `name` FROM partner_emailer_mailing_lists AS lists WHERE archived=0 AND partner_user_id=? {$extra} ";

        if($countOnly) return $this->DB->execute($sql, $params)->rowCount();

        $mailingLists = $this->DB->execute($sql, $params)->fetchAll('assoc');
        return $mailingLists;
    }

    public function getGlobalList($partnerUserId, $partnerId, $search=false, $limit=false, $offset=false, $countOnly=false)
    {
        $lead_owners_params = [$partnerId, $partnerUserId];
        $lead_owners_query = "SELECT lo.email AS email, lo.first_name AS first_name, lo.last_name AS last_name, loe.employer AS company_name
                                FROM leads l
                                JOIN partner_users pu ON pu.partner_id = l.partner_id
                                JOIN lead_owners lo ON lo.lead_id = l.lead_id
                                LEFT JOIN lead_owner_employment loe ON loe.lead_owner_id = lo.owner_id AND loe.employment_status = 'current'
                                WHERE l.partner_id = ?
                                AND pu.partner_user_id = ?
                                AND lo.email IS NOT NULL
                                AND lo.first_name IS NOT NULL
                                AND lo.last_name IS NOT NULL
                                AND lo.status = 'active'";

        if ($search) {
            $lead_owners_query = $lead_owners_query . " AND (lo.email LIKE ? OR lo.first_name LIKE ? OR lo.last_name LIKE ? OR loe.employer LIKE ?)";
            $lead_owners_params[] = "%$search%";
            $lead_owners_params[] = "%$search%";
            $lead_owners_params[] = "%$search%";
            $lead_owners_params[] = "%$search%";
        }
        $lead_owners_query =  $lead_owners_query .  " GROUP BY lo.email;";

        $lead_owners = $this->DB->execute($lead_owners_query, $lead_owners_params)->fetchAll('assoc');
        $partner_account_people_params = [$partnerId, $partnerUserId];
        $partner_account_people_query = "SELECT pap.email AS email, pap.first_name AS first_name, pap.last_name AS last_name, loe.employer AS company_name
                                FROM leads l
                                JOIN partner_users pu ON pu.partner_id = l.partner_id
                                JOIN lead_owners lo ON lo.lead_id = l.lead_id
                                JOIN partner_account_people pap ON lo.partner_account_people_id = pap.id
                                LEFT JOIN lead_owner_employment loe ON loe.partner_account_people_id = pap.id AND loe.employment_status = 'current'
                                WHERE l.partner_id = ?
                                AND pu.partner_user_id = ?
                                AND pap.email IS NOT NULL
                                AND pap.first_name IS NOT NULL
                                AND pap.last_name IS NOT NULL
                                AND pap.status = 'active'";

        if ($search) {
            $partner_account_people_query = $partner_account_people_query . " AND (pap.email LIKE ? OR pap.first_name LIKE ? OR pap.last_name LIKE ?)";
            $partner_account_people_params[] = "%$search%";
            $partner_account_people_params[] = "%$search%";
            $partner_account_people_params[] = "%$search%";
        }
        $partner_account_people_query = $partner_account_people_query . " GROUP BY pap.email;";
        $partner_account_people = $this->DB->execute($partner_account_people_query, $partner_account_people_params)->fetchAll('assoc');
        
        $totalEmails = array_merge($lead_owners, $partner_account_people);

        Log::write('debug', 'Total Emails: ' . json_encode($totalEmails));

        $recipients = [];
        foreach($totalEmails as $email) {
            if (!in_array($email['email'], array_column($recipients, 'email'))) {
                $recipients[] = $email;
            }
        }

        if ($countOnly) {
            return count($recipients);
        }

        if ($limit) {
            $recipients = array_slice($recipients, $offset, $limit);
        }

        return $recipients;
    }

    public function markUnsubScribed($listId, $recipientId){
        $sql = "UPDATE partner_emailer_mailing_lists_recipients SET subscribed = 0
                WHERE mailing_list_id = ? and recipient_id = ?";
        $this->DB->execute($sql, [$listId, $recipientId]);
    }
    
    public function getRecentMailingLists($partnerUserId)
    {
        $global = ['ref' => 'global', 'name' => 'Global Mailing List'];
        $mailingLists = $this->getMyMailingLists($partnerUserId, false, 5);
        array_unshift($mailingLists, $global);
        return $mailingLists;
    }

    private function mailingListConnectedToCampaign($ref) {
        $sql = "SELECT count(*) as `count` FROM partner_emailer_campaigns AS campaigns
                    LEFT JOIN partner_emailer_mailing_lists AS lists  ON lists.id = campaigns.mailing_list_id
                WHERE lists.ref = ?";
        $connected = $this->DB->execute($sql, [$ref])->fetch('assoc');
        return $connected['count'] ? true : false;
    }

    private function deleteMailingListRecipients($ref) {
        $id =  (new \App\Lend\LendInternalAuth)->unhashLeadId($ref);
        $sql = "DELETE FROM partner_emailer_mailing_lists_recipients WHERE mailing_list_id = ?";
        $this->DB->execute($sql, [$id]);
    }

    public function deleteMailingListRecipient($listId, $recipientId) {
        $sql = "DELETE FROM partner_emailer_mailing_lists_recipients WHERE mailing_list_id = ? AND recipient_id = ?";
        $this->DB->execute($sql, [$listId, $recipientId]);
        return [true, null];
    }

    public function deleteMailingList($ref) {
        $connected = $this->mailingListConnectedToCampaign($ref);
        if($connected) {
            //archive it - (hide form the user)
            $sql = "UPDATE partner_emailer_mailing_lists SET archived = 1 WHERE ref = ?";
            $this->DB->execute($sql, [$ref]);
            return [true, null];
        }else {
            $this->deleteMailingListRecipients($ref);//first remove recipients form the list
            $sql = "DELETE FROM partner_emailer_mailing_lists WHERE ref = ?";
            $this->DB->execute($sql, [$ref]);
            return [true, null];
        }
    }

    public function getList($ref, $search=false, $limit=false, $offset=false, $countOnly=false) {
        $extra = '';
        $params = [];
        $params[] = $ref;

        if($search) {
            $extra .= " AND (email LIKE ? OR first_name LIKE ?  OR last_name LIKE ?  OR company_name LIKE ?)";
            $params[] = "$search%";
            $params[] = "$search%";
            $params[] = "$search%";
            $params[] = "$search%";
        }

        if(!$countOnly) {
            $extra .= " ORDER BY lists.`id` DESC";
        }

        if($limit && !$countOnly) {
            $extra .= " LIMIT $limit";
        }

        if($offset && !$countOnly) {
            $extra .= " OFFSET $offset";
        }

        $sql = "SELECT email, first_name, last_name, company_name, recipient_id, mailing_list_id, subscribed FROM partner_emailer_recipients AS recipients
                    LEFT JOIN partner_emailer_mailing_lists_recipients AS listsToRecipients ON recipients.id = listsToRecipients.recipient_id
                    LEFT JOIN partner_emailer_mailing_lists AS lists ON lists.id = listsToRecipients.mailing_list_id
                 WHERE lists.ref = ? {$extra}";

        if($countOnly) return $this->DB->execute($sql, $params)->rowCount();

        $mailingLists = $this->DB->execute($sql, $params)->fetchAll('assoc');
        return $mailingLists;
    }


	public function getMailingListById($ref) {
		$sql = "SELECT id FROM partner_emailer_mailing_lists WHERE ref = ?";
		$mailingListId = $this->DB->execute($sql, [$ref])->fetch('assoc');
		return $mailingListId;
	}

    public function getCountListRecipients($mailingListParams): int
    {
        $q = $this->getSelectQuery('partner_emailer_mailing_lists', $mailingListParams);
        $list = $this->DB->execute($q['query'], $q['values'])->fetch('assoc');
        $query = 'select count(*) from partner_emailer_mailing_lists_recipients where mailing_list_id = ? and subscribed = ?';
        $count = $this->DB->execute($query, [$list['id'], 1])->fetch();

        return intval($count[0]);
    }
}
