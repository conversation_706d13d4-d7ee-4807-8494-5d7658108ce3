<?php
/**
 * Routes configuration
 *
 * In this file, you set up routes to your controllers and their actions.
 * Routes are very important mechanism that allows you to freely connect
 * different URLs to chosen controllers and their actions (functions).
 *
 * CakePHP(tm) : Rapid Development Framework (https://cakephp.org)
 * Copyright (c) Cake Software Foundation, Inc. (https://cakefoundation.org)
 *
 * Licensed under The MIT License
 * For full copyright and license information, please see the LICENSE.txt
 * Redistributions of files must retain the above copyright notice.
 *
 * @copyright     Copyright (c) Cake Software Foundation, Inc. (https://cakefoundation.org)
 * @link          https://cakephp.org CakePHP(tm) Project
 * @license       https://opensource.org/licenses/mit-license.php MIT License
 */
use Cake\Http\Middleware\CsrfProtectionMiddleware;
use Cake\Routing\RouteBuilder;
use Cake\Routing\Router;
use Cake\Routing\Route\DashedRoute;

Router::defaultRouteClass(DashedRoute::class);

Router::scope('/', function (RouteBuilder $routes) {
    $routes->registerMiddleware('csrf', new CsrfProtectionMiddleware([
        'httpOnly' => true
    ]));
    $routes->applyMiddleware('csrf');

    $routes->connect('/', ['controller' => 'Partners', 'action' => 'homePage']);
    $routes->connect('/login', ['controller' => 'PartnerUsers', 'action' => 'login']);
    $routes->connect('/partner-users/login', ['controller' => 'PartnerUsers', 'action' => 'login']);
});

Router::scope('/', function (RouteBuilder $routes) {
    $routes->connect('/forgot-password', ['controller' => 'PartnerUsers', 'action' => 'forgotPassword']);
    $routes->connect('/reset-password/:token', ['controller' => 'PartnerUsers', 'action' => 'resetPassword'])->setPatterns(['tokens' => '/^[a-zA-Z\d]+$/'])->setPass(['token']);
    $routes->connect('/activate-new-account/:token', ['controller' => 'PartnerUsers', 'action' => 'activateNewAccount'])->setPatterns(['tokens' => '/^[a-zA-Z\d]+$/'])->setPass(['token']);
    $routes->connect('/logout', ['controller' => 'PartnerUsers', 'action' => 'logout']);
    $routes->connect('/account', ['controller' => 'PartnerUsers', 'action' => 'index']);
    $routes->connect('/notifications', ['controller' => 'PartnerNotifications', 'action' => 'index']);
    $routes->connect('/lender-preferences', ['controller' => 'PartnerLenderPriority', 'action' => 'index']);
    $routes->connect('/commissions', ['controller' => 'PartnerCommissions', 'action' => 'index']);
    $routes->connect('/view-notification/*', ['controller' => 'PartnerNotifications', 'action' => 'view']);
    $routes->connect('/about-lendscore', ['controller' => 'Partners', 'action' => 'aboutLendscore']);
    $routes->connect('/bank-statement/*', ['controller' => 'BankStatementAnalysis', 'action' => 'view']);
    $routes->connect('/lender-profiles/*', ['controller' => 'LenderProfiles', 'action' => 'index']);
    $routes->connect('/clients', ['controller' => 'PartnerClients', 'action' => 'index']);
    $routes->connect('/full-search', ['controller' => 'Leads', 'action' => 'fullSearch']);
    $routes->connect('/search', ['controller' => 'Search', 'action' => 'search']);
    $routes->connect('/lend-ed', ['controller' => 'LendEd', 'action' => 'index']);
    $routes->connect('/request-callback', ['controller' => 'Leads', 'action' => 'requestCallback']);
    $routes->connect('/lead-apis/v2/validate-aid', ['controller' => 'ValidateAid', 'action' => 'validateAid', 'plugin' => 'LeadApisV2']);
    $routes->connect('/lead-apis/v2/partner-users', ['controller' => 'PartnerUsers', 'action' => 'getPartnerByAffiliate', 'plugin' => 'LeadApisV2']);
    $routes->connect('/proxy/*', array('controller' => 'proxy', 'action' => 'index'));

    // PUBLIC PAGES
    $routes->connect('/why-partner-with-lend', ['controller' => 'Partners', 'action' => 'whyPartnerWithLend']);
    $routes->connect('/pricing', ['controller' => 'Pricing', 'action' => 'index']);

    //override lender/profiles route with getData function
    $routes->connect('/lenders/get-data', ['controller' => 'Lenders', 'action' => 'getData']);

    $routes->connect('/whats-new', ['controller' => 'Accounts', 'action' => 'index']);

    // legal pages for NZ
    $routes->connect('/privacy', ['controller' => 'Terms', 'action' => 'privacy']);
    $routes->connect('/terms', ['controller' => 'Terms', 'action' => 'terms']);
    $routes->connect('/terms-of-use', ['controller' => 'Terms', 'action' => 'termsOfUse']);
    $routes->connect('/lend-partner-service-terms', ['controller' => 'Terms', 'action' => 'lendPartnerServiceTerms']);

    // REACT PAGES
    $routes->connect('/accounts/*', ['controller' => 'Accounts', 'action' => 'index']);
    $routes->connect('/account-applicants/*', ['controller' => 'Accounts', 'action' => 'index']);
    $routes->connect('/consumer-quick-quote', ['controller' => 'Accounts', 'action' => 'index']);
    $routes->connect('/lead/*', ['controller' => 'Accounts', 'action' => 'index']);
    $routes->connect('/settings/*', ['controller' => 'Accounts', 'action' => 'index']);
    $routes->connect('/calendar', ['controller' => 'Accounts', 'action' => 'index']);
    $routes->connect('/leads-call-queue', ['controller' => 'Accounts', 'action' => 'index']);
    $routes->connect('/referrers/*', ['controller' => 'Accounts', 'action' => 'index']);
    $routes->connect('/commission', ['controller' => 'Accounts', 'action' => 'index']);
    $routes->connect('/automations/*', ['controller' => 'Accounts', 'action' => 'index']);
    $routes->connect('/quick-quotes/*', ['controller' => 'Accounts', 'action' => 'index']);
    $routes->connect('/lender-guide/*', ['controller' => 'Accounts', 'action' => 'index']);
    $routes->connect('/partners/file-library', ['controller' => 'Accounts', 'action' => 'index']);
    $routes->connect('/react/*', ['controller' => 'Accounts', 'action' => 'react']);
    $routes->connect('/dashboard/*', ['controller' => 'Accounts', 'action' => 'index']);
    $routes->connect('/notification-center', ['controller' => 'Accounts', 'action' => 'index']);

    // Register scoped middleware for in scopes.
    // $routes->registerMiddleware('csrf', new CsrfProtectionMiddleware([
    //     'httpOnly' => true
    // ]));

    /**
     * Apply a middleware to the current route scope.
     * Requires middleware to be registered via `Application::routes()` with `registerMiddleware()`
     */
    // $routes->applyMiddleware('csrf');

    /**
     * Connect catchall routes for all controllers.
     *
     * Using the argument `DashedRoute`, the `fallbacks` method is a shortcut for
     *
     * ```
     * $routes->connect('/:controller', ['action' => 'index'], ['routeClass' => 'DashedRoute']);
     * $routes->connect('/:controller/:action/*', [], ['routeClass' => 'DashedRoute']);
     * ```
     *
     * Any route class can be used with this method, such as:
     * - DashedRoute
     * - InflectedRoute
     * - Route
     * - Or your own route class
     *
     * You can remove these routes once you've connected the
     * routes you want in your application.
     */
    $routes->fallbacks(DashedRoute::class);
});

/**
 * If you need a different set of middleware or none at all,
 * open new scope and define routes there.
 *
 * ```
 * Router::scope('/api', function (RouteBuilder $routes) {
 *     // No $routes->applyMiddleware() here.
 *     // Connect API actions here.
 * });
 * ```
 */