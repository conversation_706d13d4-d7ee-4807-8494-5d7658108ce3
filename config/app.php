<?php
$aws_log_group_name = getenv('LEND_ENV') == '2' ? 'brokers' : (getenv('LEND_ENV') == '1' ? 'brokers-staging' : (getenv('LEND_ENV') == '3' ? 'brokers-preprod' : ''));
return [
  /**
   * Debug Level:
   *
   * Production Mode:
   * false: No error messages, errors, or warnings shown.
   *
   * Development Mode:
   * true: Errors and warnings shown.
   */
  'debug' => filter_var(env('DEBUG', false), FILTER_VALIDATE_BOOLEAN),
  'datasource' => 'default',



  /**
   * Email delivery profiles
   * Email Profiles
   * (These are what we reference within the code, e.g. $email->send('default') )
   * The "Transport" must reference one of the configs in "EmailTransport" (below)
   */
  'Email' => [
    'default' => [
      'transport' => 'default',
      'from' => ['<EMAIL>' => 'Lend Partners'],
    ],
    'notifications' => [
      'transport' => 'notification_settings',
      'from' => ['<EMAIL>' => 'Lend Partner Notifications'],
    ],
    'reports' => [
      'transport' => 'default',
      'from' => ['<EMAIL>' => 'Lend Reports'],
    ],
    'abandoned_lead' => [
      'transport' => 'default',
      'from' => ['<EMAIL>' => 'New Lead/s'],
    ],
    'hello' => [
      'transport' => 'hello_settings',
      'from' => ['<EMAIL>' => 'Lend.com.au'],
    ],
    'mailinglist' => [
      'transport' => 'mailinglist_settings',
      'from' => ['<EMAIL>' => 'Lend.com.au'],
    ],
    'system_host' => [
      'transport' => 'system_host_settings',
      'from' => ['<EMAIL>' => 'Lend.com.au'],
    ],
    'mailtrap' => [
      'transport' => 'mailtrap',
      'from' => '<EMAIL>',
    ],
  ],


  /**
   * Email configuration.
   */
  'EmailTransport' => [
    'default' => [
      'className' => 'Smtp', // Can be "Mail", "Smtp", or "Debug"
      // The following keys are used in SMTP transports
      'host' => 'mail.lend.com.au',
      'port' => 587,
      'timeout' => 30,
      'username' => '<EMAIL>',
      'password' => 'kTEH*Vh+OqmT',
      'client' => null,
      'tls' => null,
      'url' => env('EMAIL_TRANSPORT_DEFAULT_URL', null),
    ],
    'hello_settings' => [
      'className' => 'Smtp',
      'host' => 'smtp.sendgrid.net',
      'port' => 587,
      'timeout' => 30,
      'username' => getenv('SENDGRID_HELLO_USER'),
      'password' => getenv('SENDGRID_HELLO_APIKEY'),
      'client' => null,
      'tls' => null,
      'url' => env('EMAIL_TRANSPORT_DEFAULT_URL', null),
      'for_api_use' => [
        'username' => 'lend-hello',
        'url' => 'https://api.sendgrid.com/api/mail.send.json',
        'from' => '<EMAIL>',
      ]
    ],
    'notification_settings' => [
      'className' => 'Smtp',
      'host' => 'smtp.sendgrid.net',
      'port' => 587,
      'timeout' => 30,
      'username' => getenv('SENDGRID_HELLO_USER'),
      'password' => getenv('SENDGRID_HELLO_APIKEY'),
      'client' => null,
      'tls' => null,
      'url' => env('EMAIL_TRANSPORT_DEFAULT_URL', null),
      'for_api_use' => [
        'username' => 'lend-hello',
        'url' => 'https://api.sendgrid.com/api/mail.send.json',
        'from' => '<EMAIL>',
      ]
    ],
    'mailinglist_settings' => [
      'className' => 'Smtp',
      'host' => 'smtp.sendgrid.net',
      'port' => 587,
      'timeout' => 30,
      'username' => getenv('SENDGRID_LEND_USER'),
      'password' => getenv('SENDGRID_LEND_APIKEY'),
      'client' => null,
      'tls' => null,
      'url' => env('EMAIL_TRANSPORT_DEFAULT_URL', null),
      'for_api_use' => [
        'username' => 'lend-lend',
        'url' => 'https://api.sendgrid.com/api/mail.send.json',
        'from' => '<EMAIL>',
      ]
    ],
    'consultant_settings' => [
      'className' => 'Smtp',
      'host' => 'mail.lend.com.au',
      'port' => 587,
      'timeout' => 30,
      'username' => '<EMAIL>',
      'password' => ';a_c!}[PNOJ[',
      'client' => null,
      'tls' => null,
      'url' => env('EMAIL_TRANSPORT_DEFAULT_URL', null),
    ],
    'system_host_settings' => [
      'className' => 'Mail',
      'host' => 'localhost',
      'port' => 25,
      'timeout' => 30,
      'username' => null,
      'password' => null,
      'client' => null,
      'tls' => null,
      'url' => env('EMAIL_TRANSPORT_DEFAULT_URL', null),
    ],
    'mailtrap' => [
      'className' => 'Smtp',
      'host' => 'smtp.mailtrap.io',
      'port' => 2525,
      'username' => env('MAILTRAP_USERNAME', null),
      'password' => env('MAILTRAP_PASSWORD', null),
      'for_api_use' => [
        'username' => 'lend-hello',
        'url' => 'https://api.sendgrid.com/api/mail.send.json',
        'from' => '<EMAIL>',
      ]
    ],
  ],


  /**
   * Configure basic information about the application.
   *
   * - namespace - The namespace to find app classes under.
   * - defaultLocale - The default locale for translation, formatting currencies and numbers, date and time.
   * - encoding - The encoding used for HTML + database connections.
   * - base - The base directory the app resides in. If false this
   *   will be auto detected.
   * - dir - Name of app directory.
   * - webroot - The webroot directory.
   * - wwwRoot - The file path to webroot.
   * - baseUrl - To configure CakePHP to *not* use mod_rewrite and to
   *   use CakePHP pretty URLs, remove these .htaccess
   *   files:
   *      /.htaccess
   *      /webroot/.htaccess
   *   And uncomment the baseUrl key below.
   * - fullBaseUrl - A base URL to use for absolute links. When set to false (default)
   *   CakePHP generates required value based on `HTTP_HOST` environment variable.
   *   However, you can define it manually to optimize performance or if you
   *   are concerned about people manipulating the `Host` header.
   * - imageBaseUrl - Web path to the public images directory under webroot.
   * - cssBaseUrl - Web path to the public css directory under webroot.
   * - jsBaseUrl - Web path to the public js directory under webroot.
   * - paths - Configure paths for non class based resources. Supports the
   *   `plugins`, `templates`, `locales` subkeys, which allow the definition of
   *   paths for plugins, view templates and locale files respectively.
   */
  'App' => [
    'namespace' => 'App',
    'encoding' => env('APP_ENCODING', 'UTF-8'),
    'defaultLocale' => env('APP_DEFAULT_LOCALE', 'en_US'),
    'defaultTimezone' => env('APP_DEFAULT_TIMEZONE', 'Australia/Sydney'),
    'base' => false,
    'dir' => 'src',
    'webroot' => 'webroot',
    'wwwRoot' => WWW_ROOT,
    //'baseUrl' => env('SCRIPT_NAME'),
    'fullBaseUrl' => false,
    'imageBaseUrl' => 'img/',
    'cssBaseUrl' => 'css/',
    'jsBaseUrl' => 'js/',
    'paths' => [
      'plugins' => [ROOT . DS . 'plugins' . DS],
      'templates' => [APP . 'Template' . DS],
      'locales' => [APP . 'Locale' . DS],
    ],
  ],

  /**
   * Security and encryption configuration
   *
   * - salt - A random string used in security hashing methods.
   *   The salt value is also used as the encryption key.
   *   You should treat it as extremely sensitive data.
   */
  'Security' => [
    'salt' => env('SECURITY_SALT', '5f8d132d0f76b55e215abc91a4096bb2a5b608602d4affe2d24925f65a028fcd'),
  ],

  /**
   * Apply timestamps with the last modified time to static assets (js, css, images).
   * Will append a querystring parameter containing the time the file was modified.
   * This is useful for busting browser caches.
   *
   * Set to true to apply timestamps when debug is true. Set to 'force' to always
   * enable timestamping regardless of debug value.
   */
  'Asset' => [
    'timestamp' => 'force',
    // 'cacheTime' => '+1 year'
  ],

  /**
   * Configure the cache adapters.
   */
  'Cache' => [
    'default' => [
      'className' => 'Cake\Cache\Engine\FileEngine',
      'path' => CACHE,
      'url' => env('CACHE_DEFAULT_URL', null),
    ],
    'short' => [
      'className' => 'Cake\Cache\Engine\FileEngine',
      'duration' => '+5 minutes',
      'path' => CACHE,
      'prefix' => 'short_'
    ],
    'long' => [
      'className' => 'Cake\Cache\Engine\FileEngine',
      'duration' => '+24 hours',
      'path' => CACHE,
      'prefix' => 'long_'
    ],
    'dashboard_analytics' => [
      'className' => 'Cake\Cache\Engine\FileEngine',
      'duration' => '+1 hours',
      'path' => CACHE,
      'prefix' => 'dashboard_analytics_'
    ],
    'redbook' => [
      'className' => 'Cake\Cache\Engine\FileEngine',
      'duration' => '+1 hours',
      'path' => CACHE,
      'prefix' => 'redbook_'
    ],
    'lend_config' => [
      'className' => 'Cake\Cache\Engine\FileEngine',
      'duration' => '+24 hours',
      'path' => CACHE . 'lend_config/',
      'prefix' => 'lend_config_'
    ],
    /**
     * Configure the cache used for general framework caching.
     * Translation cache files are stored with this configuration.
     * Duration will be set to '+2 minutes' in bootstrap.php when debug = true
     * If you set 'className' => 'Null' core cache will be disabled.
     */
    '_cake_core_' => [
      'className' => 'Cake\Cache\Engine\FileEngine',
      'prefix' => 'myapp_cake_core_',
      'path' => CACHE . 'persistent/',
      'serialize' => true,
      'duration' => '+1 years',
      'url' => env('CACHE_CAKECORE_URL', null),
    ],

    /**
     * Configure the cache for model and datasource caches. This cache
     * configuration is used to store schema descriptions, and table listings
     * in connections.
     * Duration will be set to '+2 minutes' in bootstrap.php when debug = true
     */
    '_cake_model_' => [
      'className' => 'Cake\Cache\Engine\FileEngine',
      'prefix' => 'myapp_cake_model_',
      'path' => CACHE . 'models/',
      'serialize' => true,
      'duration' => '+1 years',
      'url' => env('CACHE_CAKEMODEL_URL', null),
    ],

    /**
     * Configure the cache for routes. The cached routes collection is built the
     * first time the routes are processed via `config/routes.php`.
     * Duration will be set to '+2 seconds' in bootstrap.php when debug = true
     */
    '_cake_routes_' => [
      'className' => 'Cake\Cache\Engine\FileEngine',
      'prefix' => 'myapp_cake_routes_',
      'path' => CACHE,
      'serialize' => true,
      'duration' => '+1 years',
      'url' => env('CACHE_CAKEROUTES_URL', null),
    ],

    /**
     * Redis cache for queries
     */
    'queryRedis' => [
      // 'className' => 'Redis',
      'className' => 'Cake\Cache\Engine\RedisEngine',
      'host' => getenv('REDIS_SERVER'),
      'port' => getenv('REDIS_PORT'),
      'timeout' => 1,
      'duration' => '+1 hour',
      'prefix' => 'query_',
      'redisClient' => 'predis',
    ],
  ],

  /**
   * Configure the Error and Exception handlers used by your application.
   *
   * By default errors are displayed using Debugger, when debug is true and logged
   * by Cake\Log\Log when debug is false.
   *
   * In CLI environments exceptions will be printed to stderr with a backtrace.
   * In web environments an HTML page will be displayed for the exception.
   * With debug true, framework errors like Missing Controller will be displayed.
   * When debug is false, framework errors will be coerced into generic HTTP errors.
   *
   * Options:
   *
   * - `errorLevel` - int - The level of errors you are interested in capturing.
   * - `trace` - boolean - Whether or not backtraces should be included in
   *   logged errors/exceptions.
   * - `log` - boolean - Whether or not you want exceptions logged.
   * - `exceptionRenderer` - string - The class responsible for rendering
   *   uncaught exceptions. If you choose a custom class you should place
   *   the file for that class in src/Error. This class needs to implement a
   *   render method.
   * - `skipLog` - array - List of exceptions to skip for logging. Exceptions that
   *   extend one of the listed exceptions will also be skipped for logging.
   *   E.g.:
   *   `'skipLog' => ['Cake\Http\Exception\NotFoundException', 'Cake\Http\Exception\UnauthorizedException']`
   * - `extraFatalErrorMemory` - int - The number of megabytes to increase
   *   the memory limit by when a fatal error is encountered. This allows
   *   breathing room to complete logging or error handling.
   */
  'Error' => [
    'errorLevel' => E_ALL & ~E_USER_DEPRECATED,
    'exceptionRenderer' => 'Cake\Error\ExceptionRenderer',
    'skipLog' => [],
    'log' => true,
    'trace' => true,
  ],



  /**
   * Connection information used by the ORM to connect
   * to your application's datastores.
   *
   * ### Notes
   * - Drivers include Mysql Postgres Sqlite Sqlserver
   *   See vendor\cakephp\cakephp\src\Database\Driver for complete list
   * - Do not use periods in database name - it may lead to error.
   *   See https://github.com/cakephp/cakephp/issues/6471 for details.
   * - 'encoding' is recommended to be set to full UTF-8 4-Byte support.
   *   E.g set it to 'utf8mb4' in MariaDB and MySQL and 'utf8' for any
   *   other RDBMS.
   */
  'Datasources' => [
    'default' => [
      'className' => 'Cake\Database\Connection',
      'driver' => 'Cake\Database\Driver\Mysql',
      'persistent' => false,
      'host'        => !empty(getenv('SECRET_DB_HOST')) ? getenv('SECRET_DB_HOST') : (!empty(getenv('MAIN_DB_HOST')) ? getenv('MAIN_DB_HOST') : 'localhost'),
      'username'    => !empty(getenv('SECRET_DB_USER')) ? getenv('SECRET_DB_USER') : getenv('MAIN_DB_USER'),
      'password'    => !empty(getenv('SECRET_DB_PASS')) ? getenv('SECRET_DB_PASS') : getenv('MAIN_DB_PASS'),
      'database'    => getenv('MAIN_DB_NAME'),
      'unix_socket' => !empty(getenv('DB_SOCK')) ? getenv('DB_SOCK') : false,
      'encoding' => 'utf8',
      'timezone' => 'UTC',
      'flags' => [],
      'cacheMetadata' => true,
      'log' => false,
      'quoteIdentifiers' => true,
      'url' => env('DATABASE_URL', null),
    ],
    'sandbox' => [
      'className' => 'Cake\Database\Connection',
      'driver' => 'Cake\Database\Driver\Mysql',
      'persistent' => false,
      'host'        => !empty(getenv('SECRET_DB_HOST')) ? getenv('SECRET_DB_HOST') : (!empty(getenv('MAIN_DB_HOST')) ? getenv('MAIN_DB_HOST') : 'localhost'),
      'username'    => !empty(getenv('SECRET_DB_USER')) ? getenv('SECRET_DB_USER') : getenv('MAIN_DB_USER'),
      'password'    => !empty(getenv('SECRET_DB_PASS')) ? getenv('SECRET_DB_PASS') : getenv('MAIN_DB_PASS'),
      'database'    => getenv('MAIN_DB_NAME') . '_sandbox',
      'unix_socket' => !empty(getenv('DB_SOCK')) ? getenv('DB_SOCK') : false,
      'encoding' => 'utf8',
      'timezone' => 'UTC',
      'flags' => [],
      'cacheMetadata' => true,
      'log' => false,
      'quoteIdentifiers' => false,
      'url' => env('DATABASE_URL', null),
    ],
    'reader_db' => [
      'className' => 'Cake\Database\Connection',
      'driver' => 'Cake\Database\Driver\Mysql',
      'persistent' => false,
      'host'        => !empty(getenv('SECRET_READ_DB_HOST')) ? getenv('SECRET_READ_DB_HOST') : (
                          (!empty(getenv('MAIN_READER_DB_HOST')) ? getenv('MAIN_READER_DB_HOST') : (
                              !empty(getenv('MAIN_DB_HOST')) ? getenv('MAIN_DB_HOST') : 'localhost'
                            )
                          )
                        ),
      'username'    => !empty(getenv('MAIN_READER_DB_USER')) ? getenv('MAIN_READER_DB_USER') : getenv('MAIN_DB_USER'),
      'password'    => !empty(getenv('MAIN_READER_DB_PASS')) ? getenv('MAIN_READER_DB_PASS') : getenv('MAIN_DB_PASS'),
      'database'    => getenv('MAIN_DB_NAME'),
      'unix_socket' => !empty(getenv('DB_SOCK')) ? getenv('DB_SOCK') : false,
      'encoding' => 'utf8',
      'timezone' => 'UTC',
      'flags' => [],
      'cacheMetadata' => true,
      'log' => false,
      'quoteIdentifiers' => true,
      'url' => env('DATABASE_URL', null),
    ],

    // team lend DB
    'team_lend_db' => [
      'className' => 'Cake\Database\Connection',
      'driver' => 'Cake\Database\Driver\Mysql',
      'persistent' => false,
      'host'        => !empty(getenv('SECRET_DB_HOST')) ? getenv('SECRET_DB_HOST') : (!empty(getenv('TEAM_LEND_DB_HOST')) ? getenv('TEAM_LEND_DB_HOST') : 'localhost'),
      'username'    => !empty(getenv('SECRET_DB_USER')) ? getenv('SECRET_DB_USER') : getenv('TEAM_LEND_DB_USER'),
      'password'    => !empty(getenv('SECRET_DB_PASS')) ? getenv('SECRET_DB_PASS') : getenv('TEAM_LEND_DB_PASS'),
      'database'    => getenv('TEAM_LEND_DB_NAME'),
      'unix_socket' => !empty(getenv('DB_SOCK')) ? getenv('DB_SOCK') : false,
      'encoding' => 'utf8',
      'timezone' => 'UTC',
      'flags' => [],
      'cacheMetadata' => true,
      'log' => false,
      'quoteIdentifiers' => false,
      'url' => env('DATABASE_URL', null),
    ],

    /**
     * The test connection is used during the test suite.
     */
    'test' => [
      'className' => 'Cake\Database\Connection',
      'driver' => 'Cake\Database\Driver\Mysql',
      'persistent' => false,
      'host'        => 'localhost',
      'username'    => 'root',
      'password'    => 'root',
      'database'    => 'lend_crm',
      'unix_socket' => '/Applications/MAMP/tmp/mysql/mysql.sock',
      //'encoding' => 'utf8mb4',
      'timezone' => 'UTC',
      'cacheMetadata' => true,
      'quoteIdentifiers' => false,
      'log' => false,
      //'init' => ['SET GLOBAL innodb_stats_on_metadata = 0'],
      'url' => env('DATABASE_TEST_URL', null),
    ],
  ],

  /**
   * Configures logging options
   */
  'Log' => [
      'debug' => [
        'className' => (((int)getenv('LEND_ENV') >= 1) ? 'Awallef\CWL\Log\Engine\CloudwatchLog' : 'Cake\Log\Engine\FileLog'),
        'path' => LOGS,
        'file' => 'debug',
        'levels' => ['notice', 'info', 'debug'],
        'url' => env('LOG_DEBUG_URL', null),
        'scopes' => false,
        'groupName' => $aws_log_group_name,
        'streamName' => 'debugs',
        'retentionDays' => '30',
        'aws' => [
          'region' => 'ap-southeast-2',
          'version' => 'latest',
          'credentials' => [
            'key' => getenv('AWS_KEY'),
            'secret' => getenv('AWS_SEC'),
          ]
        ]
      ],
      'error' => [
        'className' => (((int)getenv('LEND_ENV') >= 1) ? 'Awallef\CWL\Log\Engine\CloudwatchLog' : 'Cake\Log\Engine\FileLog'),
        'path' => LOGS,
        'file' => 'error',
        'url' => env('LOG_ERROR_URL', null),
        'scopes' => false,
        'levels' => ['warning', 'error', 'critical', 'alert', 'emergency'],
        'groupName' => $aws_log_group_name,
        'streamName' => 'errors',
        'retentionDays' => '30',
        'aws' => [
          'region' => 'ap-southeast-2',
          'version' => 'latest',
          'credentials' => [
            'key' => getenv('AWS_KEY'),
            'secret' => getenv('AWS_SEC'),
          ]
        ]
      ],
      'queries' => [
        'className' => (((int)getenv('LEND_ENV') >= 1) ? 'Awallef\CWL\Log\Engine\CloudwatchLog' : 'Cake\Log\Engine\FileLog'),
        'path' => LOGS,
        'file' => 'queries',
        'url' => env('LOG_QUERIES_URL', null),
        'scopes' => ['queriesLog'],
        'groupName' => $aws_log_group_name,
        'streamName' => 'queries',
        'retentionDays' => '30',
        'aws' => [
          'region' => 'ap-southeast-2',
          'version' => 'latest',
          'credentials' => [
            'key' => getenv('AWS_KEY'),
            'secret' => getenv('AWS_SEC'),
          ]
        ]
      ],
      'api' => [
        'className' => (((int)getenv('LEND_ENV') >= 1) ? 'Awallef\CWL\Log\Engine\CloudwatchLog' : 'Cake\Log\Engine\FileLog'),
        'path' => LOGS,
        'file' => 'api',
        'levels' => [],
        'scopes' => ['api'],
        'groupName' => $aws_log_group_name,
        'streamName' => 'api',
        'retentionDays' => '30',
        'aws' => [
          'region' => 'ap-southeast-2',
          'version' => 'latest',
          'credentials' => [
            'key' => getenv('AWS_KEY'),
            'secret' => getenv('AWS_SEC'),
          ]
        ]
      ],
      'crmsearch' => [
        'className' => (((int)getenv('LEND_ENV') >= 1) ? 'Awallef\CWL\Log\Engine\CloudwatchLog' : 'Cake\Log\Engine\FileLog'),
        'path' => LOGS,
        'file' => 'crmsearch',
        'levels' => [],
        'scopes' => ['crmsearch'],
        'groupName' => $aws_log_group_name,
        'streamName' => 'crmsearch',
        'retentionDays' => '30',
        'aws' => [
          'region' => 'ap-southeast-2',
          'version' => 'latest',
          'credentials' => [
            'key' => getenv('AWS_KEY'),
            'secret' => getenv('AWS_SEC'),
          ]
        ]
      ],
      'bs' => [
        'className' => (((int)getenv('LEND_ENV') >= 1) ? 'Awallef\CWL\Log\Engine\CloudwatchLog' : 'Cake\Log\Engine\FileLog'),
        'path' => LOGS,
        'file' => 'bs',
        'levels' => [],
        'scopes' => ['bs'],
        'groupName' => $aws_log_group_name,
        'streamName' => 'bs',
        'retentionDays' => '30',
        'aws' => [
          'region' => 'ap-southeast-2',
          'version' => 'latest',
          'credentials' => [
            'key' => getenv('AWS_KEY'),
            'secret' => getenv('AWS_SEC'),
          ]
        ]
      ],
      'lenderlogic' => [
        'className' => (((int)getenv('LEND_ENV') >= 1) ? 'Awallef\CWL\Log\Engine\CloudwatchLog' : 'Cake\Log\Engine\FileLog'),
        'path' => LOGS,
        'file' => 'lenderlogic',
        'levels' => [],
        'scopes' => ['lenderlogic'],
        'groupName' => $aws_log_group_name,
        'streamName' => 'lenderlogic',
        'retentionDays' => '30',
        'aws' => [
          'region' => 'ap-southeast-2',
          'version' => 'latest',
          'credentials' => [
            'key' => getenv('AWS_KEY'),
            'secret' => getenv('AWS_SEC'),
          ]
        ]
      ],
      'notifications' => [
        'className' => (((int)getenv('LEND_ENV') >= 1) ? 'Awallef\CWL\Log\Engine\CloudwatchLog' : 'Cake\Log\Engine\FileLog'),
        'path' => LOGS,
        'file' => 'notifications',
        'levels' => [],
        'scopes' => ['notifications'],
        'groupName' => $aws_log_group_name,
        'streamName' => 'notifications',
        'retentionDays' => '30',
        'aws' => [
          'region' => 'ap-southeast-2',
          'version' => 'latest',
          'credentials' => [
            'key' => getenv('AWS_KEY'),
            'secret' => getenv('AWS_SEC'),
          ]
        ]
      ],
      '404s' => [
        'className' => (((int)getenv('LEND_ENV') >= 1) ? 'Awallef\CWL\Log\Engine\CloudwatchLog' : 'Cake\Log\Engine\FileLog'),
        'path' => LOGS,
        'file' => '404s',
        'levels' => [],
        'scopes' => ['404s'],
        'groupName' => $aws_log_group_name,
        'streamName' => '404s',
        'retentionDays' => '30',
        'aws' => [
          'region' => 'ap-southeast-2',
          'version' => 'latest',
          'credentials' => [
            'key' => getenv('AWS_KEY'),
            'secret' => getenv('AWS_SEC'),
          ],
        ],
      ],
    ],

  /**
   * Session configuration.
   *
   * Contains an array of settings to use for session configuration. The
   * `defaults` key is used to define a default preset to use for sessions, any
   * settings declared here will override the settings of the default config.
   *
   * ## Options
   *
   * - `cookie` - The name of the cookie to use. Defaults to 'CAKEPHP'. Avoid using `.` in cookie names,
   *   as PHP will drop sessions from cookies with `.` in the name.
   * - `cookiePath` - The url path for which session cookie is set. Maps to the
   *   `session.cookie_path` php.ini config. Defaults to base path of app.
   * - `timeout` - The time in minutes the session should be valid for.
   *    Pass 0 to disable checking timeout.
   *    Please note that php.ini's session.gc_maxlifetime must be equal to or greater
   *    than the largest Session['timeout'] in all served websites for it to have the
   *    desired effect.
   * - `defaults` - The default configuration set to use as a basis for your session.
   *    There are four built-in options: php, cake, cache, database.
   * - `handler` - Can be used to enable a custom session handler. Expects an
   *    array with at least the `engine` key, being the name of the Session engine
   *    class to use for managing the session. CakePHP bundles the `CacheSession`
   *    and `DatabaseSession` engines.
   * - `ini` - An associative array of additional ini values to set.
   *
   * The built-in `defaults` options are:
   *
   * - 'php' - Uses settings defined in your php.ini.
   * - 'cake' - Saves session files in CakePHP's /tmp directory.
   * - 'database' - Uses CakePHP's database sessions.
   * - 'cache' - Use the Cache class to save sessions.
   *
   * To define a custom session handler, save it at src/Network/Session/<name>.php.
   * Make sure the class implements PHP's `SessionHandlerInterface` and set
   * Session.handler to <name>
   *
   * To use database sessions, load the SQL file located at config/schema/sessions.sql
   */
  'Session' => [
    'defaults' => 'php',
    'timeout' => 1440, // The session will timeout after 24 hours minutes of inactivity
    'cookie' => 'LendCRM',
    'cookieTimeout' => 1440, // The session cookie will live for at most 24 hours, this does not effect session timeouts
    'checkAgent' => false,
    'autoRegenerate' => true, // causes the session expiration time to reset on each page load
  ],
];
