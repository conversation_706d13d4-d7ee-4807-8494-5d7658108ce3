<?php
$conf['Lend']=array();
//Removed, Please use config_other table app_config_version field value
// $conf['Lend']['configVersion']=1.22;// Always update after making config changes or migrations that update vue
$conf['Lend']['nameTag'] = ' | Lend Partners';
$conf['Lend']['byPassApiSignatures'] = ((string)getenv('LEND_ENV')==='0');

// SMS Service(s)
$conf['Lend']['SMS'] = array(
  'enabled' => ((string)getenv('LEND_ENV')==='2') ? true : false,
  'BurstSMS' => array(
    'api_base_url' => 'https://api.transmitsms.com',
    'sender_id' => '61417071617',
  ),
);

// Cost of Reports
$conf['Lend']['ReportCost'] = array(
  'ID Matrix' => 3.15,
  'Commercial Apply' => 14.58,
  'Consumer Apply' => 17.45,
  'Company Enquiry' => 27.31,
  'Company Enquiry - Enriched' => 49.56,
  'Credit Report' => 11.99,
);


$conf['Lend']['homeowner_types'] = array(
  '1'=>'Owns',
  '2'=>'Rents',
  '3'=>'Living with Parents',
  '4'=>'Board',
  '5'=>'Other'
);

$conf['Lend']['pf_delivery_costs'] = array(
    'sms' => 0.00,
    'envelope' => 1,
    'docusign_envelope' => 1,
);

/**
 * Upload Manager
 * https://uppy.io/docs/
 */
$conf['Lend']['UploadManager'] = array(
  'autoProceed' => true, //needs extra testing if set to false
  'restrictions' => array( //leave null for no restriction
      /*maxFileSize in bytes. NOTE: set this 1 MB higher than acutal limit.
       E.g. if Limit is 5, set to 6. Because a customer may try to upload a 5.1 MB file
       */
      'maxFileSize'=> 2097152, // bytes (Example: 6291456 is 6mb, 2097152 is 2mb)
      'maxNumberOfFiles'=> null,
      'minNumberOfFiles'=> null,
      'allowedFileTypes'=> array( //https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input/file#Limiting_accepted_file_types
          'image/*',
          '.pdf',
          '.odt',
          '.doc',
          '.docx',
          '.xls',
          '.xlsx',
          '.ppt',
          '.pptx'
      )
  )
);

// ############################## Owner Types ##############################
// * If you add more items in here, you must add them into STAR and APPLICATION System config as well.
// * File Location : /LendCRM/config/lend.php
// * File Location : /LendApplicantSystem/config/lend.php
// #####################################################################################
$conf['Lend']['lead_owner_type'] = array(
  '1'=>'Director & Shareholder',
  '2'=>'Director Only',
  '3'=>'Shareholder Only',
  '4'=>'Partner',
  '5'=>'Sole Trader',

  // No longer use:
  // '7'=>'Trustee Individual',
  // '6'=>'Applicant Only',

  // New ones
  //'8'=>'Guarantor',
  '9'=>'Chairperson',
  '10'=>'Treasurer',
  '11'=>'Secretary',
  '12'=>'Trust Trustee',
  '13'=>'Trust Beneficiary', //Trust Beneficial Owner
  '14'=>'Trust Settlor',
  '15'=>'Other',
);

$conf['Lend']['ABN_Lookup'] = array(
  'SOAP_url' => 'http://abr.business.gov.au/abrxmlsearch/ABRXMLSearch.asmx?WSDL',
);

$conf['Lend']['partner'] = array(
  'max_num_lender_to_send' => 5,
);

/*
Lend.Company contains the company, bank and invoice info
If you want to change anything within Lend.Company, please
make sure Invoice PDF still working
*/
/*------ BEGIN: Lend.Company ------*/
$conf['Lend']['Company'] = array(
  'region' => 'AU',
  'lendAddress' => array(
    'street' => 'Suite 3, Level 2, 1 Taylor Street',
    'suburb' => 'Moorabbin',
    'state'  => 'VIC 3189',
    'country' => 'Australia'
  ),
  'lendFullName' => 'Lend Capital Pty Ltd',
  'lendABN' => '**************',
  'lendBankAccount' => array(
    'accountName' => 'Lend Capital Ltd Pty',
    'accountNumber' => 'XXXX XXXX',
    'bsb' => 'XXX XXX',
    'email' => '<EMAIL>',
  ),
  'lendPhoneNumber' => '1300 668 843',
  'lendContactEmail' => '<EMAIL>',
  'lendLogo' => '/img/logos/lend.svg',
  'lendInvoiceAgreement' => '',
  // 'lendInvoiceAgreement' => 'XXX XXX XXXXX XXXXX XXX XXXXX XXXXX,  XXX XXXXX XXXXX XXX XXXXX XXXXX',
  'GST' => 0.10
);

$conf['Lend']['CompanyNz'] = array(
  'region' => 'NZ',
  'lendAddress' => array(
    'street' => 'Suite 3, Level 2, 1 Taylor Street',
    'suburb' => 'Moorabbin',
    'state'  => 'VIC 3189',
    'country' => 'Australia'
  ),
  'lendFullName' => 'Lend Capital Ltd',
  'lendABN' => '*************',
  'lendFullNameForRCTI' => 'Basketpress Limited trading as Lend',
  'LendNZBNForRCTI' => '*************',
  'lendBankAccount' => array(
    'accountName' => 'Lend Capital Pty',
    'accountNumber' => 'XXXX XXXX',
    'bsb' => 'XXX XXX',
    'email' => '<EMAIL>',
  ),
  'lendPhoneNumber' => '1300 668 843',
  'lendContactEmail' => '<EMAIL>',
  'lendLogo' => '/img/logos/lend.svg',
  'lendInvoiceAgreement' => '',
  // 'lendInvoiceAgreement' => 'XXX XXX XXXXX XXXXX XXX XXXXX XXXXX,  XXX XXXXX XXXXX XXX XXXXX XXXXX',
  'GST' => 0.15,
  'GST_Number' => '*********',
);
/*------ END: Lend.Company ------*/


$conf['Lend']['partner_honoured_lookup'] = array(
    'days' => 30,
    'scores' => array(
        'lead' => array(
          'abn' => 20,
          'acn' => 20,
          'organisation_name' => 5,
          'business_name' => 5,
        ),
        'lead_owner' => array(
          'owner_name' => 5,
          'phone' => 5,
          'mobile' => 5,
          'email' => 5,
        )
      )
);


$conf['Lend']['AWS'] = array(
  'url'           => getenv('DOMAIN_FILES').'/', // 'https://files.lend.com.au',
  'endpoint'      => 'https://lead-uploads.s3-us-west-2.amazonaws.com',
  // 'url'           => 'https://files.lend.com.au/',
  // 'url'           => 'https://s3-ap-southeast-2.amazonaws.com/lend-partner-uploads/',
  'region'        => 'ap-southeast-2',
  'Credentials'   => array(
    'lend_system' => array( // 'lend_system' is the user
      'key' => getenv('AWS_KEY'),
      'secret' => getenv('AWS_SEC'),
    ),
  ),
  'bucket' => 'files.lend.com.au', // It changed from 'lend.com.au-leaduploads' on 'us-west-2' => 'files.lend.com.au' on 'ap-southeast-2'
);


$conf['Lend']['UserNotifications'] = array(
  'pagination_limit' => 20,
);

$conf['Lend']['LenderNotes'] = array(
  'pagination_limit' => 8,
);

$conf['Lend']['LenderCallbacks'] = array(
  'pagination_limit' => 8,
);

$conf['Lend']['days_not_allow_to_reupload_bs'] = 10;

/* shorthand  => array('type' => xxx, 'number' => xxx )*/
$conf['Lend']['LenderPhones'] = array(
  'Prospa' => array(
                  'type' => 'Dedicated',
                  'number' => '1300 865 817'
                ),
  'BusinessFuel' => array(
                  'type' => 'Dedicated',
                  'number' => '0439 195 143'
                ),
  'MaxFunding' => array(
                  'type' => "Ryan's mobile",
                  'number' => '0420 570 123'
                ),
  'OnDeck' => array(
                  'type' => "Dedicated",
                  'number' => '1800 681 174'
                ),
  'Sail' => array(
                  'type' => "Warm transfer",
                  'number' => '(02) 9190 2601'
                ),
  'Cigno' => array(
                  'type' => '',
                  'number' => ''
                ),
  'Lumi' => array(
                  'type' => '',
                  'number' => '(02) 8607 5864'
                ),
  'Capify' => array(
                  'type' => 'Dedicated',
                  'number' => '1300 702 659'
                ),
  'Moula' => array(
                  'type' => '',
                  'number' => ''
                ),
  'GetCapital' => array(
                  'type' => 'Dedicated',
                  'number' => '1300 249 649'
                ),
  'BizCap' => array(
                  'type' => '',
                  'number' => '(03) 9069 5422'
                ),
  'BanjoLoans' => array(
                  'type' => 'Dedicated',
                  'number' => 'View Profile for list'
  ),
  'StriveFinancial' => array(
                  'type' => 'Dedicated',
                  'number' => '1300 478 748'
                ),
  'YourManager' => array(
                  'type' => 'Dedicated',
                  'number' => '1300 214 450'
                ),
  'InvoiceMoney' => array(
                  'type' => '',
                  'number' => '1300 811 484'
                ),
  'CashflowFinance' => array(
                  'type' => 'Dedicated',
                  'number' => '0419 952 198'
                ),
);
/* lock up leads for partner lender */
$conf['Lend']['parnter_lender_statuses_lockup'] = array(
    //final value as key
    '2' => array(
        'days' => 0,
        'message' => "Confirmation. You are closing the lead. This cannot be undone. Please ensure this is the correct final status. The lead may be sent to another lender.",
      ),
    '3' => array(
        'days' => 0,
        'message' => 'Confirmation. You are closing the lead. This cannot be undone. Please ensure this is the correct closed status.',
      ),
    '4' => array(
        'days' => 0,
        'message' => 'Confirmation. You are marking this lead as funded and commission is payable.',
      ),
);
$conf['Lend']['partner_lender_status_update'] = array(
  'alert_in_hours' => 12, //warning request for a status update
  'alert_message' => 'The status hasn’t changed for 12+ hours, if the status still hasn\'t changed, click the button above.',
);

// This is temporary authentication key for bankstatement service.
$conf['Lend']['bs_service_auth_key'] = '$2y$10$BpiBrkEE/6pcnKoo9.fufe254gqTWenO5h5SikXc3.5oP5KPLXq4a';

// partner terms edited date set up to today for now
$conf['Lend']['partner_terms_edited'] = '2023-06-27 00:00:00';
$conf['Lend']['nz_partner_terms_edited'] = '2023-01-01 00:00:00';

// File Download Portal expire time setup to 24 hours
$conf['Lend']['file_download_expire'] = 24;

// Invite New User expire time setup to 24 hours
$conf['Lend']['invite_user_expire'] = 24;

// Australia States
$conf['Lend']['AU_states'] = array('ACT','NSW','VIC','QLD','SA','WA','TAS','NT');

// Credit History
$conf['Lend']['credit_history'] = array('Good','No Credit History','Paid Defaults','Unpaid Defaults','Ex Bankrupt','Not Sure');

// Max width of a partner logo - e.g. '200px'; //MUST **NOT** INCLUDE PX!!
$conf['Lend']['max_partner_logo_width'] = '350';

$conf['Lend']['bs_slugs_logo_available'] = [
  'anz',
  'banksa',
  'bankwest',
  'bendigo',
  'bom',
  'boq',
  'cba',
  'citibank',
  'greater',
  'ing',
  'mebank',
  'nab',
  'rams',
  'stgeorge',
  'suncorp',
  'ubank',
  'westpac',
];



//Asset Finance Data Structure
$conf['Lend']['Asset_Finance_Data_Structure'] =
            [
                'mandatory_fields' =>['people'],
                'optional_fields' =>['business_credit_history','references','assets','asset_finance','liabilities'],

                'lead_owner_mandatory' =>[],
                'lead_owner_optional' =>['owner_type_other_detail', 'marital_status', 'is_guarantor', 'class_of_beneficiary', 'directorship_start_date', 'number_of_dependants', 'dependant_details', 'driving_licence_type'],

                'lead_owner_employment_mandatory' =>['employer', 'employment_type', 'date_from'],
                'lead_owner_employment_optional' =>['previous_occupation', 'date_to'],

                'lead_owner_address_mandatory' =>['living_status', 'address', 'suburb', 'state', 'postcode', 'country', 'date_from'],
                'lead_owner_address_optional' =>['living_status_other', 'landlord_name', 'landlord_contact_number', 'date_to'],

                'lead_reference_mandatory' =>['reference_type', 'company_name', 'full_name', 'email', 'contact_number', 'address', 'suburb', 'state', 'postcode', 'country'],
                'lead_reference_optional' =>['financier', 'financier_acc_num'],

                'lead_assets_mandatory' =>['asset_type', 'asset_type_name'],

                'lead_asset_finance_mandatory' =>[ 'asset_description', 'equipment', 'year', 'make', 'model', 'condition', 'reason_for_purchase'],
                'lead_asset_finance_optional' =>['reason_description', 'supplier_found', 'supplier', 'supplier_type', 'supplier_address', 'supplier_suburb', 'supplier_state', 'supplier_postcode', 'supplier_country', 'asset_purchase_price', 'asset_deposit', 'asset_tradein', 'asset_tradein_value', 'asset_tradein_debt', 'asset_balloon'],

                'lead_liabilities_mandatory' =>['liability_type', 'liability_name']
            ];

$conf['Lend']['assets_liabilities_pair'] = [
    // Individual
    '6' => '17', // Home: Home Loan
    '7' => '18', // Investment Property: Investment Property Loan
    '11' => '19', // Motor Vehicle: Motor Vehicle Loan
    '15' => '20', // Plant & Equipment: Plant & Equipment Loan
    '16' => '14', // Other: Other
    '17' => '21', // Recreational Asset: Recreational Asset Loan
    // Business
    '1' => '9', // Property: Property Loan
    '3' => '7', // Plant & Equipment: Asset Finance (single)
    '4' => '5' // Other: Other Loan
];

//Consumer lead required field
$conf['Lend']['consumer_lead_required_field'] = [
  //['field' => "con_page_status.con_cgqp_status", 'goto_field' => "amount_requested", 'text' => "Credit Guide & Quote Information", 'value' => "complete", 'condition_field' => "", 'condition_value' => "", 'route' => "../credit-guide-quote-privacy"],
  ['field' => "con_page_status.con_req_obj_status", 'goto_field' => "con_req_and_obj.amount_req", 'text' => "Requirements & Objectives Page is not Completed", 'value' => "complete", 'condition_field' => "", 'condition_value' => "", 'route' => "../requirements-objectives"],
  //['field' => "con_page_status.con_loan_details_status", 'goto_field' => "amount_requested", 'text' => "Loan Details Information", 'value' => 1, 'condition_field' => "", 'condition_value' => "", 'route' => "../loan-details"], 
  ['field' => "purpose_id", 'goto_field' => "purpose_id", 'text' => "Loan Purpose", 'value' => "notEmpty", 'condition_field' => "", 'condition_value' => "", 'route' => "../loan-details"], 
  ['field' => "amount_requested", 'goto_field' => "amount_requested", 'text' => "Loan Requested Amount", 'value' => "notEmpty", 'condition_field' => "", 'condition_value' => "", 'route' => "../loan-details"], 
  ['field' => "loan_term_requested_months", 'goto_field' => "loan_term_requested_months", 'text' => "Desired Loan Term", 'value' => "notEmpty", 'condition_field' => "", 'condition_value' => "", 'route' => "../loan-details"], 
  ['field' => "how_soon_id", 'goto_field' => "how_soon_id", 'text' => "How soon is the loan required", 'value' => "notEmpty", 'condition_field' => "", 'condition_value' => "", 'route' => "../loan-details"], 
  ['field' => "owners_all[].title", 'goto_field' => "title", 'text' => "Applicant [applicant_name]'s Title", 'value' => "notEmpty", 'condition_field' => "", 'condition_value' => "", 'route' => "../applicants?applicantId=[owner_ref]"], 
  ['field' => "owners_all[].first_name", 'goto_field' => "first_name", 'text' => "Applicant [applicant_name]'s Firstname", 'value' => "notEmpty", 'condition_field' => "", 'condition_value' => "", 'route' => "../applicants?applicantId=[owner_ref]"], 
  ['field' => "owners_all[].last_name", 'goto_field' => "last_name", 'text' => "Applicant [applicant_name]'s Lastname", 'value' => "notEmpty", 'condition_field' => "", 'condition_value' => "", 'route' => "../applicants?applicantId=[owner_ref]"], 
  ['field' => "owners_all[].mobile_or_phone", 'goto_field' => "mobile", 'text' => "Applicant [applicant_name]'s Mobile or Phone", 'value' => "notEmpty", 'condition_field' => "", 'condition_value' => "", 'route' => "../applicants?applicantId=[owner_ref]"], 
  ['field' => "owners_all[].email", 'goto_field' => "email", 'text' => "Applicant [applicant_name]'s Email", 'value' => "notEmpty", 'condition_field' => "", 'condition_value' => "", 'route' => "../applicants?applicantId=[owner_ref]"], 
  ['field' => "owners_all[].dob", 'goto_field' => "dob", 'text' => "Applicant [applicant_name]'s Date of Birth", 'value' => "notEmpty", 'condition_field' => "", 'condition_value' => "", 'route' => "../applicants?applicantId=[owner_ref]"], 
  ['field' => "owners_all[].gender", 'goto_field' => "gender", 'text' => "Applicant [applicant_name]'s Gender", 'value' => "notEmpty", 'condition_field' => "", 'condition_value' => "", 'route' => "../applicants?applicantId=[owner_ref]"], 
  ['field' => "owners_all[].marital_status", 'goto_field' => "marital_status", 'text' => "Applicant [applicant_name]'s Marital Status", 'value' => "notEmpty", 'condition_field' => "", 'condition_value' => "", 'route' => "../applicants?applicantId=[owner_ref]"], 
  ['field' => "owners_all[].residency_status", 'goto_field' => "residency_status", 'text' => "Applicant [applicant_name]'s Residency Status", 'value' => "notEmpty", 'condition_field' => "", 'condition_value' => "", 'route' => "../applicants?applicantId=[owner_ref]"], 
  ['field' => "owners_all[].credit_history", 'goto_field' => "credit_history", 'text' => "Applicant [applicant_name]'s Credit History", 'value' => "notEmpty", 'condition_field' => "", 'condition_value' => "", 'route' => "../applicants?applicantId=[owner_ref]"], 
  ['field' => "owners_all[].all_addresses", 'goto_field' => "all_addresses.0.date_from", 'text' => "Applicant [applicant_name]'s Address History (2 years)", 'value' => "", 'condition_field' => "", 'custom_function' => "validateApplicantAddressHistory", 'condition_value' => "", 'route' => "../applicants?applicantId=[owner_ref]&form=address"],
  ['field' => "owners_all[].all_addresses", 'bulk_custom_function' => 'validateAddress', 'goto_field' => "all_addresses.0.full_address", 'text' => "", 'value' => "", 'condition_field' => "", 'condition_value' => "", 'route' => "../applicants?applicantId=[owner_ref]&form=address"],
  ['field' => "owners_all[].all_employments", 'goto_field' => "all_employments.0.date_from", 'text' => "Applicant [applicant_name]'s Employment History (3 years)", 'value' => "", 'condition_field' => "", 'custom_function' => "validateApplicantEmploymentHistory", 'condition_value' => "", 'route' => "../applicants?applicantId=[owner_ref]&form=employment"],
  ['field' => "owners_all[].all_employments", 'bulk_custom_function' => 'validateEmployment', 'goto_field' => "all_employments.0.date_from", 'text' => "", 'value' => "", 'condition_field' => "", 'condition_value' => "", 'route' => "../applicants?applicantId=[owner_ref]&form=employment"],
  ['field' => "incomes[].amount", 'goto_field' => "people.0.amount", 'text' => "Applicant Income", 'value' => "notEmpty", 'condition_field' => "config_income_id", 'condition_value' => "1", 'route' => "../income-expenses"],
  //['field' => "con_credit_prop_fees[].fixed_amount | con_credit_prop_fees[].percent_of_loan", 'goto_field' => "", 'text' => "Origination fee", 'value' => "notEmpty", 'condition_field' => "config_con_fee_id", 'condition_value' => "1", 'route' => "../lenders-pricing"],
  //['field' => "con_credit_prop_fees[].fixed_amount | con_credit_prop_fees[].percent_of_loan", 'goto_field' => "", 'text' => "Commission", 'value' => "notEmpty", 'condition_field' => "config_con_fee_id", 'condition_value' => "8", 'route' => "../lenders-pricing"],
  ['field' => "asset_finance.make", 'goto_field' => "make", 'text' => "Vehicle make is required", 'value' => "notEmpty", 'filter_condition' => "purpose.is_car_loan", 'filter_condition_value' => true, 'condition_field' => "", 'condition_value' => "", 'route' => "../asset-details"], 
  ['field' => "asset_finance.model", 'goto_field' => "model", 'text' => "Vehicle model is required", 'value' => "notEmpty", 'filter_condition' => "purpose.is_car_loan", 'filter_condition_value' => true, 'condition_field' => "", 'condition_value' => "", 'route' => "../asset-details"], 
  ['field' => "asset_finance.year", 'goto_field' => "year", 'text' => "Vehicle year is required", 'value' => "notEmpty", 'filter_condition' => "purpose.is_car_loan", 'filter_condition_value' => true, 'condition_field' => "", 'condition_value' => "", 'route' => "../asset-details"], 
  ['field' => "asset_finance.sale_type", 'goto_field' => "sale_type", 'text' => "Vehicle sale type is required", 'value' => "notEmpty", 'filter_condition' => "purpose.is_car_loan", 'filter_condition_value' => true, 'condition_field' => "", 'condition_value' => "", 'route' => "../asset-details"], 
  ['field' => "asset_finance.condition", 'goto_field' => "condition", 'text' => "Vehicle condition is required", 'value' => "notEmpty", 'filter_condition' => "purpose.is_car_loan", 'filter_condition_value' => true, 'condition_field' => "", 'condition_value' => "", 'route' => "../asset-details"], 
];

//Consumer lead required field
$conf['Lend']['request_invoice'] = [
  //['field' => "con_page_status.con_cgqp_status", 'goto_field' => "amount_requested", 'text' => "Credit Guide & Quote Information", 'value' => "complete", 'condition_field' => "", 'condition_value' => "", 'route' => "../credit-guide-quote-privacy"],
  ['field' => "owner_poc.first_name", 'goto_field' => "first_name", 'text' => "Applicant Firstname", 'value' => "notEmpty", 'condition_field' => "", 'condition_value' => "", 'route' => "../applicants?applicantId=[owner_ref]"], 
  ['field' => "owner_poc.last_name", 'goto_field' => "last_name", 'text' => "Applicant Lastname", 'value' => "notEmpty", 'condition_field' => "", 'condition_value' => "", 'route' => "../applicants?applicantId=[owner_ref]"], 
  ['field' => "owner_poc.all_addresses[].full_address", 'goto_field' => "all_addresses.0.date_from", 'text' => "Applicant [applicant_name]'s Address", 'value' => "notEmpty", 'custom_function' => "",'condition_field' => "", 'condition_value' => "", 'route' => "../applicants?applicantId=[owner_ref]"], 
  ['field' => "asset_finance.supplier", 'goto_field' => "supplier", 'text' => "Supplier Name", 'value' => "notEmpty", 'condition_field' => "", 'condition_value' => "", 'route' => ""], 
  ['field' => "asset_finance.supplier_contact_name", 'goto_field' => "supplier_contact_name", 'text' => "Supplier Contact Name", 'value' => "notEmpty", 'condition_field' => "", 'condition_value' => "", 'route' => ""], 
  ['field' => "asset_finance.supplier_email", 'goto_field' => "supplier_email", 'text' => "Supplier Email", 'value' => "notEmpty", 'condition_field' => "", 'condition_value' => "", 'route' => ""], 

  ['field' => "asset_finance.make", 'goto_field' => "make", 'text' => "make", 'value' => "notEmpty", 'condition_field' => "", 'condition_value' => "", 'route' => ""], 
  ['field' => "asset_finance.year", 'goto_field' => "year", 'text' => "year", 'value' => "notEmpty", 'condition_field' => "", 'condition_value' => "", 'route' => ""], 
  ['field' => "asset_finance.model", 'goto_field' => "model", 'text' => "model", 'value' => "notEmpty", 'condition_field' => "", 'condition_value' => "", 'route' => ""],
  ['field' => "asset_finance.condition", 'goto_field' => "model", 'text' => "Vehicle Condition", 'value' => "notEmpty", 'condition_field' => "", 'condition_value' => "", 'route' => ""],
  ['field' => "asset_finance.supplier_quote_ref", 'goto_field' => "supplier_quote_ref", 'text' => "Supplier Quote ref", 'value' => "notEmpty", 'condition_field' => "", 'condition_value' => "", 'route' => ""],  

];


$conf['Lend']['request_invoice_consumer_v2'] = [
  ['field' => "owner_poc.first_name", 'goto_field' => "first_name", 'text' => "Applicant Firstname", 'value' => "notEmpty", 'condition_field' => "", 'condition_value' => "", 'route' => "../applicants?applicantId=[owner_ref]"], 
  ['field' => "owner_poc.last_name", 'goto_field' => "last_name", 'text' => "Applicant Lastname", 'value' => "notEmpty", 'condition_field' => "", 'condition_value' => "", 'route' => "../applicants?applicantId=[owner_ref]"], 
  ['field' => "owner_poc.all_addresses[].full_address", 'goto_field' => "all_addresses.0.date_from", 'text' => "Applicant [applicant_name]'s Address", 'value' => "notEmpty", 'custom_function' => "",'condition_field' => "", 'condition_value' => "", 'route' => "../applicants?applicantId=[owner_ref]"], 
  ['field' => "asset_finance.supplier", 'goto_field' => "supplier", 'text' => "Supplier Name", 'value' => "notEmpty", 'condition_field' => "", 'condition_value' => "", 'route' => ""], 
  ['field' => "asset_finance.supplier_contact_name", 'goto_field' => "supplier_contact_name", 'text' => "Supplier Contact Name", 'value' => "notEmpty", 'condition_field' => "", 'condition_value' => "", 'route' => ""], 
  ['field' => "asset_finance.supplier_email", 'goto_field' => "supplier_email", 'text' => "Supplier Email", 'value' => "notEmpty", 'condition_field' => "", 'condition_value' => "", 'route' => ""], 
];






$conf['Lend']['countries'] = [
  'AFG' => 'Afghanistan',
  'ALB' => 'Albania',
  'DZA' => 'Algeria',
  'ASM' => 'American Samoa',
  'AND' => 'Andorra',
  'AGO' => 'Angola',
  'AIA' => 'Anguilla',
  'ATA' => 'Antarctica',
  'ATG' => 'Antigua and Barbuda',
  'ARG' => 'Argentina',
  'ARM' => 'Armenia',
  'ABW' => 'Aruba',
  'AUS' => 'Australia',
  'AUT' => 'Austria',
  'AZE' => 'Azerbaijan',
  'BHS' => 'Bahamas (the)',
  'BHR' => 'Bahrain',
  'BGD' => 'Bangladesh',
  'BRB' => 'Barbados',
  'BLR' => 'Belarus',
  'BEL' => 'Belgium',
  'BLZ' => 'Belize',
  'BEN' => 'Benin',
  'BMU' => 'Bermuda',
  'BTN' => 'Bhutan',
  'BOL' => 'Bolivia (Plurinational State of)',
  'BES' => 'Bonaire, Sint Eustatius and Saba',
  'BIH' => 'Bosnia and Herzegovina',
  'BWA' => 'Botswana',
  'BVT' => 'Bouvet Island',
  'BRA' => 'Brazil',
  'IOT' => 'British Indian Ocean Territory (the)',
  'BRN' => 'Brunei Darussalam',
  'BGR' => 'Bulgaria',
  'BFA' => 'Burkina Faso',
  'BDI' => 'Burundi',
  'CPV' => 'Cabo Verde',
  'KHM' => 'Cambodia',
  'CMR' => 'Cameroon',
  'CAN' => 'Canada',
  'CYM' => 'Cayman Islands (the)',
  'CAF' => 'Central African Republic (the)',
  'TCD' => 'Chad',
  'CHL' => 'Chile',
  'CHN' => 'China',
  'CXR' => 'Christmas Island',
  'CCK' => 'Cocos (Keeling) Islands (the)',
  'COL' => 'Colombia',
  'COM' => 'Comoros (the)',
  'COD' => 'Congo (the Democratic Republic of the)',
  'COG' => 'Congo (the)',
  'COK' => 'Cook Islands (the)',
  'CRI' => 'Costa Rica',
  'HRV' => 'Croatia',
  'CUB' => 'Cuba',
  'CUW' => 'Curaçao',
  'CYP' => 'Cyprus',
  'CZE' => 'Czechia',
  'CIV' => 'Côte d\'Ivoire',
  'DNK' => 'Denmark',
  'DJI' => 'Djibouti',
  'DMA' => 'Dominica',
  'DOM' => 'Dominican Republic (the)',
  'ECU' => 'Ecuador',
  'EGY' => 'Egypt',
  'SLV' => 'El Salvador',
  'GNQ' => 'Equatorial Guinea',
  'ERI' => 'Eritrea',
  'EST' => 'Estonia',
  'SWZ' => 'Eswatini',
  'ETH' => 'Ethiopia',
  'FLK' => 'Falkland Islands (the) [Malvinas]',
  'FRO' => 'Faroe Islands (the)',
  'FJI' => 'Fiji',
  'FIN' => 'Finland',
  'FRA' => 'France',
  'GUF' => 'French Guiana',
  'PYF' => 'French Polynesia',
  'ATF' => 'French Southern Territories (the)',
  'GAB' => 'Gabon',
  'GMB' => 'Gambia (the)',
  'GEO' => 'Georgia',
  'DEU' => 'Germany',
  'GHA' => 'Ghana',
  'GIB' => 'Gibraltar',
  'GRC' => 'Greece',
  'GRL' => 'Greenland',
  'GRD' => 'Grenada',
  'GLP' => 'Guadeloupe',
  'GUM' => 'Guam',
  'GTM' => 'Guatemala',
  'GGY' => 'Guernsey',
  'GIN' => 'Guinea',
  'GNB' => 'Guinea-Bissau',
  'GUY' => 'Guyana',
  'HTI' => 'Haiti',
  'HMD' => 'Heard Island and McDonald Islands',
  'VAT' => 'Holy See (the)',
  'HND' => 'Honduras',
  'HKG' => 'Hong Kong',
  'HUN' => 'Hungary',
  'ISL' => 'Iceland',
  'IND' => 'India',
  'IDN' => 'Indonesia',
  'IRN' => 'Iran (Islamic Republic of)',
  'IRQ' => 'Iraq',
  'IRL' => 'Ireland',
  'IMN' => 'Isle of Man',
  'ISR' => 'Israel',
  'ITA' => 'Italy',
  'JAM' => 'Jamaica',
  'JPN' => 'Japan',
  'JEY' => 'Jersey',
  'JOR' => 'Jordan',
  'KAZ' => 'Kazakhstan',
  'KEN' => 'Kenya',
  'KIR' => 'Kiribati',
  'PRK' => 'Korea (the Democratic People\'s Republic of)',
  'KOR' => 'Korea (the Republic of)',
  'KWT' => 'Kuwait',
  'KGZ' => 'Kyrgyzstan',
  'LAO' => 'Lao People\'s Democratic Republic (the)',
  'LVA' => 'Latvia',
  'LBN' => 'Lebanon',
  'LSO' => 'Lesotho',
  'LBR' => 'Liberia',
  'LBY' => 'Libya',
  'LIE' => 'Liechtenstein',
  'LTU' => 'Lithuania',
  'LUX' => 'Luxembourg',
  'MAC' => 'Macao',
  'MDG' => 'Madagascar',
  'MWI' => 'Malawi',
  'MYS' => 'Malaysia',
  'MDV' => 'Maldives',
  'MLI' => 'Mali',
  'MLT' => 'Malta',
  'MHL' => 'Marshall Islands (the)',
  'MTQ' => 'Martinique',
  'MRT' => 'Mauritania',
  'MUS' => 'Mauritius',
  'MYT' => 'Mayotte',
  'MEX' => 'Mexico',
  'FSM' => 'Micronesia (Federated States of)',
  'MDA' => 'Moldova (the Republic of)',
  'MCO' => 'Monaco',
  'MNG' => 'Mongolia',
  'MNE' => 'Montenegro',
  'MSR' => 'Montserrat',
  'MAR' => 'Morocco',
  'MOZ' => 'Mozambique',
  'MMR' => 'Myanmar',
  'NAM' => 'Namibia',
  'NRU' => 'Nauru',
  'NPL' => 'Nepal',
  'NLD' => 'Netherlands (the)',
  'NCL' => 'New Caledonia',
  'NZL' => 'New Zealand',
  'NIC' => 'Nicaragua',
  'NER' => 'Niger (the)',
  'NGA' => 'Nigeria',
  'NIU' => 'Niue',
  'NFK' => 'Norfolk Island',
  'MNP' => 'Northern Mariana Islands (the)',
  'NOR' => 'Norway',
  'OMN' => 'Oman',
  'PAK' => 'Pakistan',
  'PLW' => 'Palau',
  'PSE' => 'Palestine, State of',
  'PAN' => 'Panama',
  'PNG' => 'Papua New Guinea',
  'PRY' => 'Paraguay',
  'PER' => 'Peru',
  'PHL' => 'Philippines (the)',
  'PCN' => 'Pitcairn',
  'POL' => 'Poland',
  'PRT' => 'Portugal',
  'PRI' => 'Puerto Rico',
  'QAT' => 'Qatar',
  'MKD' => 'Republic of North Macedonia',
  'ROU' => 'Romania',
  'RUS' => 'Russian Federation (the)',
  'RWA' => 'Rwanda',
  'REU' => 'Réunion',
  'BLM' => 'Saint Barthélemy',
  'SHN' => 'Saint Helena, Ascension and Tristan da Cunha',
  'KNA' => 'Saint Kitts and Nevis',
  'LCA' => 'Saint Lucia',
  'MAF' => 'Saint Martin (French part)',
  'SPM' => 'Saint Pierre and Miquelon',
  'VCT' => 'Saint Vincent and the Grenadines',
  'WSM' => 'Samoa',
  'SMR' => 'San Marino',
  'STP' => 'Sao Tome and Principe',
  'SAU' => 'Saudi Arabia',
  'SEN' => 'Senegal',
  'SRB' => 'Serbia',
  'SYC' => 'Seychelles',
  'SLE' => 'Sierra Leone',
  'SGP' => 'Singapore',
  'SXM' => 'Sint Maarten (Dutch part)',
  'SVK' => 'Slovakia',
  'SVN' => 'Slovenia',
  'SLB' => 'Solomon Islands',
  'SOM' => 'Somalia',
  'ZAF' => 'South Africa',
  'SGS' => 'South Georgia and the South Sandwich Islands',
  'SSD' => 'South Sudan',
  'ESP' => 'Spain',
  'LKA' => 'Sri Lanka',
  'SDN' => 'Sudan (the)',
  'SUR' => 'Suriname',
  'SJM' => 'Svalbard and Jan Mayen',
  'SWE' => 'Sweden',
  'CHE' => 'Switzerland',
  'SYR' => 'Syrian Arab Republic',
  'TWN' => 'Taiwan',
  'TJK' => 'Tajikistan',
  'TZA' => 'Tanzania, United Republic of',
  'THA' => 'Thailand',
  'TLS' => 'Timor-Leste',
  'TGO' => 'Togo',
  'TKL' => 'Tokelau',
  'TON' => 'Tonga',
  'TTO' => 'Trinidad and Tobago',
  'TUN' => 'Tunisia',
  'TUR' => 'Turkey',
  'TKM' => 'Turkmenistan',
  'TCA' => 'Turks and Caicos Islands (the)',
  'TUV' => 'Tuvalu',
  'UGA' => 'Uganda',
  'UKR' => 'Ukraine',
  'ARE' => 'United Arab Emirates (the)',
  'GBR' => 'United Kingdom of Great Britain and Northern Ireland (the)',
  'UMI' => 'United States Minor Outlying Islands (the)',
  'USA' => 'United States of America (the)',
  'URY' => 'Uruguay',
  'UZB' => 'Uzbekistan',
  'VUT' => 'Vanuatu',
  'VEN' => 'Venezuela (Bolivarian Republic of)',
  'VNM' => 'Viet Nam',
  'VGB' => 'Virgin Islands (British)',
  'VIR' => 'Virgin Islands (U.S.)',
  'WLF' => 'Wallis and Futuna',
  'ESH' => 'Western Sahara',
  'YEM' => 'Yemen',
  'ZMB' => 'Zambia',
  'ZWE' => 'Zimbabwe',
  'ALA' => 'Åland Islands',
];

$conf['Lend']['bs_doc_expired_days']  = 30;

$conf['Lend']['ask_applicant_pages']  = [
  'consumer' => [
    '/loan-details' => 'Loan Details',
    '/asset-details' => 'Asset Details',
    '/applicants' => 'Applicant Details',
    '/income-expenses' => 'Applicant Income & Expenses',
    '/assets-liabilities' => 'Applicant Assets & Liabilities',
    '/bank-statements' => 'Bank Statements',
  ],
  'commercial' => [
    '/business-loan-details' => 'Loan Details',
    '/business-details' => 'Business Details',
    '/asset-details' => 'Asset Details',
    '/business-assets-liabilities' => 'Business Assets & Liabilities',
    '/applicants' => 'Applicant Details',
    '/income-expenses' => 'Applicant Income & Expenses',
    '/assets-liabilities' => 'Applicant Assets & Liabilities',
    '/bank-statements' => 'Bank Statements',
  ],
  'homeloan' => [
    '/non-signature-credit-guide'=> 'Terms',
    '/property-details' => 'Property Details',
    '/loan-details' => 'Loan Details',
    '/applicants' => 'Applicant Details',
    '/income-expenses' => 'Applicant Income & Expenses',
    '/assets-liabilities' => 'Applicant Assets & Liabilities',
    '/bank-statements' => 'Bank Statements',
  ],
];

$conf['Lend']['SALARY_INCOME_CONFIG_ID']  = 1;
$conf['Lend']['SPOUSE_INCOME_CONFIG_ID']  = 7;
$conf['Lend']['RENT_EXPENSE_CONFIG_ID']  = 2;

$conf['Lend']['LEND_TRASHED_LEAD_PARTNER_ID']  = 64;
$conf['Lend']['LEND_PARTNER_IDS']  = [
  1, //Lend Direct Broker
  2, //Lend Demo
  3, //UnsecuredBusinessLoans.com.au
  4, //Lend Direct
  8, //OLD Lend SEM via MCA
  54, //Lend Capital Pty Ltd
  $conf['Lend']['LEND_TRASHED_LEAD_PARTNER_ID'], //Trashed Lead
];

$conf['Lend']['OFF_PANEL_LENDER_ID']  = 30;
$conf['Lend']['OFF_PANEL_LENDER_PRODUCT_ID']  = 330;
$conf['Lend']['NOT_IN_CALL_QUEUE_STATUS']  = [-5, -4, -3, -1];

return $conf;
?>