# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Build/Test Commands

- **Lint code**: `composer cs-check`
- **Fix code style**: `composer cs-fix`
- **Run all tests**: `composer test` or `vendor/bin/phpunit`
- **Run single test**: `vendor/bin/phpunit tests/TestCase/Path/To/FileTest.php` or `vendor/bin/phpunit --filter testMethodName`
- **Database migrations**: `vendor/bin/phinx migrate -e dev`
- **Combined check**: `composer check` (runs tests and code style check)

## Code Style Guidelines

- **Framework**: CakePHP 3.10.x - follow [CakePHP conventions](https://book.cakephp.org/3/en/intro/conventions.html)
- **Coding standard**: PSR-4 autoloading and CakePHP coding standards
- **Naming**: CamelCase for classes, camelCase for methods/properties, underscored_names for DB fields
- **Error handling**: Use CakePHP exceptions - extend from `\Cake\Core\Exception\Exception`
- **Database**: Use migrations through Phinx for schema changes
- **Documentation**: DocBlock comments for classes and methods with @param, @return tags
- **Testing**: PHPUnit for unit/integration tests with fixtures for DB interaction

## High-Level Architecture

### Core Structure
- **MVC Pattern**: Controllers in `/src/Controller/`, Models split into Entity and Table classes
- **Service Layer**: Custom business logic in `/src/Lend/` including services for email, SMS, calculations, and third-party integrations
- **Plugin Architecture**: Modular functionality in `/plugins/` directory for features like API endpoints, call queues, analytics, and lender integrations

### Authentication & Authorization
- **JWT-based Auth**: Custom JWT implementation using Firebase JWT, tokens stored in cookies
- **Multi-tiered Users**: Partner (broker), Applicant, Lender, Staff, and Intermediary user types
- **Access Control**: `AccountLevelAccess` class for permissions, controller-based authorization, feature flags per partner
- **Special Features**: Auto-login codes for email links, 2FA support, staff impersonation

### API Structure
- **Multiple API Versions**: `/plugins/Api/`, `/plugins/LeadApis/`, `/plugins/LeadApisV2/`
- **Header-based Auth**: Environment switching (sandbox/live), version headers support
- **Standardized Responses**: Uniform JSON structure with success boolean and proper HTTP status codes

### Key Design Patterns
- **Repository Pattern**: Table classes act as repositories with consistent query methods
- **Behaviors**: `GenerateLeadRefBehavior`, `TrackChangesBehavior`, `NameSanitizeBehavior`, `CustomTimestampBehavior`
- **Feature Flags**: Database-driven toggles at partner and UI levels
- **Security**: CORS handling, input validation, RSA encryption for sensitive fields, JWT tokens

### Database & Caching
- **Migrations**: Phinx for schema management (`/db/lend/migrations/`)
- **Environments**: dev, sand, stage, prod configurations
- **Read/Write Splitting**: Support for reader database connections
- **Caching**: Model and core caching with environment-specific durations