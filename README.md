# Lend Partners

## Local Environment

Instructions on how to setup the repo and all dependencies on a **local machine** for developers.

Assumes pre-existing understanding of configuring web servers (ie Apache) for basic apps.

## Dependencies

|       | Version             | Production |     |
| ----- | ------------------- | ---------- | --- |
| PHP   | 7.2.x &nbsp;x64 NTS | 7.2.28     |     |
| MySql | 5.7.x &nbsp;        | 5.7.12     |     |

###### &nbsp;&nbsp;&nbsp; As of 2020-06-01

## Installation

### **1. Repo**

1. Create `.dev_env` file in root directory of repo _(no link available)_

   Obtain this from a fellow developer.

   Adjust the file if you need to match any username/password values for your environment.

   **( If on Windows )** Ensure there are no `#` characters mid sentence, and are only used to comment out an entire line. Attemping to use a .dev_env file with `#` characters mid sentence will result in Apache failing to start.

<br>

### **2. Apache Configuration**

Production uses Beanstalk.

If using Apache for Local Environment:

1. `httpd.conf` - mod_include enabled to allow to use `Include` (`SetEnv`)

   change

   ```apache
   #LoadModule include_module modules/mod_include.so
   ```

   to

   ```apache
   LoadModule include_module modules/mod_include.so
   ```

2. `httpd-vhosts.conf` - Create vhost for the repo to include the `.dev_env` file in repo

   ```apache
   <VirtualHost *:<<_PORT_>>
       DocumentRoot "__PATH_TO_DIRECTORY__/lend-broker"
       Include __PATH_TO_DIRECTORY__/lend-broker/.dev_env
   </VirtualHost>
   ```

   The virtualhost is not actually required to serve the PHP itself, you can serve it inline or using any method, but is just used in this example in order to include the `.dev_env` file.

   Alternatively, use any method of executing `SetEnv` on the dev_env file as you see fit.

3. `httpd.conf` - Ensure you allow access to the repo directory if you have strict permissions

   Example

   ```apache
    <Directory "__PATH_TO_DIRECTORY__/lend-broker">
        Options All
        AllowOverride All
        Order allow,deny
        Allow from all
    </Directory>
   ```

<br>

### **3. PHP Configuration**

2. `php.ini` - **Soap** is required for ABN lookup during registration and is not enabled by default
   change
   ```ini
   ;extension=soap
   ```
   to
   ```ini
   extension=soap
   ```
   <br>

### **4. Database Configuration**

1. Obtain the `lend_crm` sql database file _(no link available)_ and use it to import into your local MySql server.
2. PHPMyAdmin _may_ not be the best tool to perform this import and a standalone executable program is recommended.
3. The database name is `lend_crm` but database/auth details can be found/set in `.dev_env`

### Husky not working?

Husky should be installed as part of npm install for React codebase.
If it's not running when you commit, run

```
git config core.hooksPath
```

It should return NOTHING.

If it does return something, run this command to unset:

```
git config --unset core.hookspath
```

### Build CSS locally for PHP/jquery

```
sass ./webroot/css/master.scss ./webroot/css/master.min.css --style=compressed --no-source-map
sass ./webroot/css/master-v2.scss ./webroot/css/master-v2.min.css --style=compressed --no-source-map
```

trigger update 20250226