<?php
namespace App\Controller;
use App\Lend\ChecklistHelper;


class LeadChecklistsController extends AppController {

    public function initialize() {
      parent::initialize();
    }

    public function item(){
        if (getenv('LEND_ENV') == 0) {
            $user = $this->Auth->user();
        } else {
            $user = $this->Auth->identify();
        }
        $requestData = $this->request->getData();
        $response = ['data' => ['error' => 'Method not allowed'], 'code' => 405];
        switch ($this->request->getMethod()) {
            case 'POST'://update
                $response = ChecklistHelper::updateLeadChecklistItemStatus($user['partner_id'], $requestData);
                break;
            default:
                break;
        }
        $this->setJsonResponse($response['data'], $response['code']);
    }
}