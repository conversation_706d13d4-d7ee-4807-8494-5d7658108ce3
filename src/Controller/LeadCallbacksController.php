<?php

namespace App\Controller;

use Cake\Core\Configure;
use App\Lend\LendInternalAuth;
use App\Traits\LendDateTime;
use Cake\ORM\TableRegistry;

class LeadCallbacksController extends AppController
{

    use LendDateTime;

    public function initialize()
    {
        parent::initialize();
        $this->LoadModel('Leads');
    }

    public function index()
    {
        $this->set('partnerType', $this->Auth->user('partner_type'));
        $this->set('seo', array('meta' => array('title' => 'Tasks' . $this->lendconfig['nameTag'], 'og' => array('title' => 'Tasks' . $this->lendconfig['nameTag']))));
    }

    public function addCallback()
    {
        try {
            if (!$this->request->is('post'))
            throw new \Exception("Only POST request available.");

            $data = $this->request->getData();
            $user = $this->Auth->user();
            if (empty($user)) {
                $user = $this->Auth->identify();
            }

            if (!empty($data['lead_ref'])) {
                $data['lead_id'] = (new LendInternalAuth)->unhashLeadId($data['lead_ref']);
                unset($data['lead_ref']);
            } else {
                $data['lead_id'] = null;
            }

            $leadId = $data['lead_id'];

            //check if the user has access to this lead
            if (!empty($leadId) && empty($user['access_all_leads']) && !$this->loadModel('PartnerUserLeads')->existingUserLead($leadId, $user['partner_user_id'], ['ACCESS']))
                throw new \Exception('Not allowed to access this lead');

            if (!$data['partner_user_id']) {
                $data['partner_user_id'] = $user['partner_user_id'];
            }

            $allUsers = $this->loadModel('PartnerUsers')->getPartnerUser(['partner_id' => $user['partner_id']], true);
            $userIds = [];
            if (!empty($allUsers)) {
                foreach ($allUsers as $key => $user) {
                    $userIds[] = $user['partner_user_id'];
                }
            }
            if (!in_array($data['partner_user_id'], $userIds)) {
                throw new \Exception('Not allowed to access this lead');
            }

            // $allAssignableUsers = $this->loadModel('PartnerCallbacks')->getAssignableUsers($leadId);
            // if(!array_key_exists($data['partner_user_id'], $allAssignableUsers)){
            //   throw new \Exception('Not allowed to assign to this user');
            // }

            $data['timezone'] = $data['timezone'] ?? 'UTC';

            if (!isset($data['startTime'])) {
                $hour = substr($data['scheduledTime'], 0, strpos($data['scheduledTime'], ':'));
                $hour = sprintf("%02s", $hour + (strpos($data['scheduledTime'], 'PM') && $hour < 12 ? 12 : (strpos($data['scheduledTime'], 'AM') && $hour == 12 ? -12 : 0)));
                // return $hour = between '00' -> '23'
                $data['scheduledTime'] = $hour . str_replace(' ', '', substr($data['scheduledTime'], strpos($data['scheduledTime'], ':'), 4)) . ':00';
                // return $data['scheduledTime'] = between '00:00:00' -> '23:59:00'

                $data['startTime'] = date('Y-m-d H:i:s', strtotime(str_replace('/', '-', $data['scheduledDate'] . ' ' . $data['scheduledTime'])));
            }

            $scheduled_time = $this->convertToDBValue($data['timezone'], $data['startTime']);

            // Prepare data to insert
            $post_data = array(
                'lead_id' => $leadId,
                'partner_user_id' => $data['partner_user_id'],
                'timezone' => $data['timezone'],
                'scheduled_time' => $scheduled_time,
                'end_time' => $data['endTime'] ? $this->convertToDBValue($data['timezone'], $data['endTime']) : null,
                'reason' => $data['reason'],
                'type' => $data['type'] ?? "Call",
                'email_reminder' => !empty($data['email_reminder']) ? '1' : '0',
                'status' => 0
            );
            $returnData = $post_data;
            $partner_callbacks_table = TableRegistry::getTableLocator()->get('PartnerCallbackEntity');
            $partner_callback = $partner_callbacks_table->newEntity($post_data);
            $partner_callbacks_table->save($partner_callback);
            
            $post_data['callback_id'] = $partner_callback->callback_id;
            $returnData['consultant'] = $user['name'];
            $returnData['status'] = 'scheduled';
            $returnData['scheduled_time'] = $this->convertToISO($post_data['timezone'], $post_data['scheduled_time']);
            $returnData['end_time'] = $this->convertToISO($post_data['timezone'], $post_data['end_time']);

            if ($data['lead_id']) {
                // Update lead's last_changed_date for lead sort
                $this->Leads->updateLead(['lead_id' => $data['lead_id']]);
            }

            unset($returnData['partner_user_id'], $returnData['lead_id']);

            return $this->setJsonResponse(array('success' => true, 'data' => $returnData));
        } catch (\Exception $e) {
            return $this->setJsonResponse(array('success' => false, 'message' => $e->getMessage()));
        }
    }


    public function editCallback()
    {
        // Convert non-ORM functions from addCallback. but did not change any logic//
        try {
            if (!$this->request->is('post')) {
                throw new \Exception("Only POST request available.");
            }

            $data = $this->request->getData();
            $user = $this->Auth->user();
            if (empty($user)) {
                $user = $this->Auth->identify();
            }

            if (empty($data['callback_id'])) {
                throw new \Exception("Callback ID is required.");
            }

            $callback = $this->getTableLocator()->get('PartnerCallbacks')->get($data['callback_id']);

            if (!$callback) {
                throw new \Exception("Callback not found.");
            }

            $leadId = $callback->lead_id;
            $PartnerUserLeads = $this->getTableLocator()->get('PartnerUserLeads');
            $exists = $PartnerUserLeads->find()
                ->where([
                    'partner_user_id' => $user['partner_user_id'],
                    'lead_id' => $leadId,
                    'status' => 'ACCESS'
                ])
                ->order(['partner_user_lead_id' => 'DESC'])
                ->count() > 0;

            $leadId = $callback->lead_id;
            if (!empty($leadId) && empty($user['access_all_leads']) && !$exists) {
                throw new \Exception('Not allowed to access this lead');
            }
            if (!empty($data['lead_ref'])) {
                $data['lead_id'] = (new LendInternalAuth)->unhashLeadId($data['lead_ref']);
                unset($data['lead_ref']);
            }

            $allUsers = $this->getTableLocator()->get('PartnerUsers')->find()
            ->where(['partner_id' => $user['partner_id']])
            ->toArray();

            $userIds = array_map(function ($user) {
                return $user->partner_user_id;
            }, $allUsers);

            if (!$data['partner_user_id']) {
                $data['partner_user_id'] = $user['partner_user_id'];
            }
            if (!in_array($data['partner_user_id'], $userIds)) {
                throw new \Exception('Not allowed to assign to this user');
            }

            $data['timezone'] = $data['timezone'] ?? 'UTC';

            $scheduled_time = null;
            if ($data['scheduled_time']) {
                $scheduled_time = $this->convertToDBValue($data['timezone'], $data['scheduled_time']);
            } else if ($data['startTime']) {
                $scheduled_time = $this->convertToDBValue($data['timezone'], $data['startTime']);
            }

            $end_time = null;
            if ($data['end_time']) {
                $end_time = $this->convertToDBValue($data['timezone'], $data['end_time']);
            } else if ($data['endTime']) {
                $end_time = $this->convertToDBValue($data['timezone'], $data['endTime']);
            }

            $update_data = [
                'partner_user_id' => $data['partner_user_id'],
                'timezone' => $data['timezone'],
                'scheduled_time' => $scheduled_time ?? null,
                'end_time' => $end_time ?? null,
                'reason' => $data['reason'],
                'type' => $data['type'] ?? "Call",
                'email_reminder' => !empty($data['email_reminder']) ? '1' : '0',
            ];

            $partnerCallbacksTable = $this->getTableLocator()->get('PartnerCallbackEntity');
            $callback = $partnerCallbacksTable->patchEntity($callback, $update_data);
            if (!$partnerCallbacksTable->save($callback)) {
                throw new \Exception('Failed to update callback');
            }

            // left this logic from addCallback //
            if ($callback->lead_id) {
                $this->Leads->updateLead(['lead_id' => $callback->lead_id]);
            }
            $response_data = [
                'callback_id' => $callback->callback_id,
                'consultant' => $user['name'],
                'status' => 'scheduled',
                'timezone' => $data['timezone'],
                'scheduled_time' => $this->convertToISO($data['timezone'], $scheduled_time),
                'end_time' =>  $this->convertToISO($data['timezone'], $end_time),
                'reason' => $callback->reason,
                'type' => $callback->type,
                'email_reminder' => $callback->email_reminder
            ];

            return $this->setJsonResponse(['success' => true, 'data' => $response_data]);
        } catch (\Exception $e) {
            return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    public function getLatest()
    {
        if ($hashed_lead_id = $this->request->getQuery('leadRef')) {
            $leadId = (new LendInternalAuth)->unhashLeadId($hashed_lead_id);
            $limit = Configure::read('Lend.LenderCallbacks.pagination_limit');
            $page = (int)$this->request->getQuery('page') - 1;

            $data = $this->loadModel('PartnerCallbacks')->getPartnerCallbacksWithDetails(array('pc.lead_id' => $leadId), array('pc.status' => 'asc', 'pc.scheduled_time' => 'asc'), $page, $limit);
            $showmore = (count($data) >= $limit);

            foreach ($data as $key => $d) unset($data[$key]['partner_user_id'], $data[$key]['lead_id']);
        }

        $this->setJsonResponse(array('success' => isset($leadId), 'data' => $data, 'showmore' => $showmore));
    }

    public function mark()
    {
        try {
            if (!$this->request->is('post'))
            throw new \Exception("Only POST request available.");

            $callbackId = $this->request->getData('callbackId');

            // Marking callback in db as complete/incomplete
            $this->loadModel('PartnerCallbacks');
            $callback = $this->PartnerCallbacks->getPartnerCallback(array('callback_id' => $callbackId));
            $this->PartnerCallbacks->updatePartnerCallbacks(array('callback_id' => $callbackId, 'status' => (!empty($callback['status']) ? '0' : '1'), 'marked_date' => date('Y-m-d H:i:s')));

            $this->setJsonResponse(array('success' => true, 'status' => (!empty($callback['status']) ? (strtotime($callback['scheduled_time']) < strtotime('now') ? 'overdue' : 'scheduled') : 'completed')));
        } catch (\Exception $e) {
            $this->setJsonResponse(array('success' => false, 'status' => 'incompleted', 'message' => $e->getMessage()));
        }
    }

    public function getLeadCallbacks()
    {
        try {
            if (!$this->request->is('post'))
            throw new \Exception("Only POST request available.");

            $data = $this->request->getData();
            $status = $data['status'];

            if ($status === 'completed') {
                $conditions = array('pc.status' => '1', 'pc.marked_date >' => date('Y-m-d H:i:s', strtotime('-24 hours')));
            } else {
                $conditions = array('pc.status' => '0');
            }

            $conditions['pc.partner_user_id'] = $this->Auth->user('partner_user_id');
            $leadCallbacks = $this->loadModel('PartnerCallbacks')->getPartnerCallbacksWithDetails($conditions);
            foreach ($leadCallbacks as $key => $d) unset($leadCallbacks[$key]['partner_user_id'], $leadCallbacks[$key]['lead_id']);

            $this->setJsonResponse(array('success' => true, 'data' => $leadCallbacks));
        } catch (\Exception $e) {
            $this->setJsonResponse(array('success' => false, 'message' => $e->getMessage()));
        }
    }

    public function getAllLeadCallbacks()
    {
        try {
            if (!$this->request->is('post'))
            throw new \Exception("Only POST request available.");

            $data = $this->request->getData();
            $status = $data['status'];

            if ($status === 'completed') {
                $conditions = array('pc.status' => '1', 'pc.marked_date >' => date('Y-m-d H:i:s', strtotime('-24 hours')));
            } else {
                $conditions = array('pc.status' => '0');
            }

            if ($data['startDate'] && $data['endDate']) {
                $conditions = array('pc.scheduled_time >=' => $data['startDate'], 'pc.scheduled_time <=' => date('Y-m-d H:i:s', strtotime($data['endDate']) + 86400));
            }

            $user = $this->Auth->user();
            if (empty($user)) {
                $user = $this->Auth->identify();
            }

            if (!$user['account_admin']) {
                $conditions['pc.partner_user_id'] = $user['partner_user_id'];
            } else {
                $allUsers = $this->loadModel('PartnerUsers')->getPartnerUser(['partner_id' => $user['partner_id'], 'active' => 1], true);
                $userIds = [];
                if (!empty($allUsers)) {
                    foreach ($allUsers as $key => $user) {
                        $userIds[] = $user['partner_user_id'];
                    }
                }
                $conditions['pc.partner_user_id in'] = $userIds;
            }

            $conditions['l.partner_id'] = $this->Auth->user('partner_id');
            $leadCallbacks = $this->loadModel('PartnerCallbacks')->getPartnerCallbacksWithDetails($conditions);
            foreach ($leadCallbacks as $key => $d) unset($leadCallbacks[$key]['lead_id']);

            $this->setJsonResponse(array('success' => true, 'data' => $leadCallbacks));
        } catch (\Exception $e) {
            $this->setJsonResponse(array('success' => false, 'message' => $e->getMessage()));
        }
    }


    public function getCallbackAssignableUsers($leadRef)
    {
        try {
            $leadId = (new LendInternalAuth)->unhashLeadId($leadRef);
            $user = $this->Auth->user();
            if (empty($user)) {
                $user = $this->Auth->identify();
            }
            if (!$user['account_admin'] && !$this->loadModel('PartnerUserLeads')->existingUserLead($leadId, $user['partner_user_id'], ['ACCESS'])) {
                throw new \Exception('Not allowed to access this lead');
            } else {
                $this->loadModel('PartnerCallbacks');
                $allAssignableUsers = $this->PartnerCallbacks->getAssignableUsers($leadId);
            }
            $this->setJsonResponse(array_values($allAssignableUsers));
        } catch (\Exception $e) {
            $this->setJsonResponse(['error' => $e->getMessage()]);
        }
    }
}
