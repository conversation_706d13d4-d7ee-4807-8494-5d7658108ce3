<?php

namespace App\Controller;

use App\Auth\LendAuthenticate;
use App\Model\Entity\PartnerLenderCredential;
use App\Model\Table\PartnerUsers2faTable;
use Cake\Core\Configure;
use Cake\Core\Exception\Exception;
use Cake\ORM\Query;
use Cake\Log\Log;
use Cake\Utility\Security;
use DateInterval;
use DateTime;
use Firebase\JWT\JWT;
use Cake\Mailer\Email;
use App\Lend\PartnerValidation;
use App\Lend\SignatureService;
use App\Lend\SmsHelper;
use Cake\ORM\TableRegistry;
use phpseclib\Crypt\RSA;
use App\Lend\EquifaxService;
use App\Lend\LendSignatureServiceBackendClient;

class PartnerUsersController extends AppController
{
  public function initialize()
  {
    parent::initialize();
    $this->Auth->allow([
      'login',
      'logout',
      'verifyEmail',
      'forgotPassword',
      'resetPassword',
      'activateNewAccount',
      'accregister',
      'setConsentDecStatus',
      'getExtraCredentialsApi',
      'token',
      'sendDashboardNotice',
    ]);
  }


  public function index()
  {
    $partner     = $this->LoadModel('Partners')->getPartner(array('partner_id' => $this->Auth->user('partner_id')));
    /** @var PartnerUsers2faTable $partnerUser2faTable */
    $partnerUser2faTable = $this->loadModel('PartnerUsers2fa');
    $partnerUser = $this->PartnerUsers->getPartnerUser(array('partner_user_id' => $this->Auth->user('partner_user_id')));
    if (!empty($this->request->getQuery('success'))) {
      $this->message('success', $this->request->getQuery('success'));
    } elseif (!empty($this->request->getQuery('error'))) {
      $this->message('error', $this->request->getQuery('error'));
    } elseif (empty($partnerUser['email_verified'])) {
      if (empty($this->request->getQuery('verifyEmailSent')))
        $this->message('warning', "Email Address has not been verified, click <a href='/partner-users/resend-verify-email'>here</a> to resend verification email");
      else
        $this->message('success', "An email has been sent to you, please click the link in the email to verify your email address.");
    }
    $is2faEnabled = $partnerUser2faTable->is2faEnabled($partnerUser['partner_user_id']);
    if ($is2faEnabled) {
      $pU2faRecord = $partnerUser2faTable->readRecord([
        'partner_user_id' => $partnerUser['partner_user_id'],
        'status' => 'Enabled'
      ], ['created' => 'Desc'], 1)[0];
      $this->set('pU2faRecord', $pU2faRecord);
    }

    $max_partner_logo_width = Configure::read('Lend.max_partner_logo_width');

    // Get Privacy Form
    $custom_privacy_form = $this->loadModel('PartnerCustomPrivacyForms')->getForm(['partner_id' => $this->Auth->user('partner_id'), 'status !=' => 'Deleted'], ['partner_custom_privacy_form_id' => 'DESC']);
    if ($custom_privacy_form) {
      if ($custom_privacy_form['status'] === 'Processing') {
        $createdDateTime = strtotime($custom_privacy_form['created']);
        $dateTimeNow = time();
        $interval = $dateTimeNow - $createdDateTime;
        $custom_privacy_form['interval'] = $interval / 60; //convert into minuets
      } else {
        $custom_privacy_form['interval'] = 0;
      }
    }

    $user = $this->Auth->identify();
    if ($user === false) {
      $user = $this->Auth->user();
    }

    $lenders = TableRegistry::getTableLocator()->get('LenderEntity')
      ->getActiveLenders(false)
      ->contain('LenderRequiredCredentials.PartnerLenderCredentials', function (Query $query) use ($user) {
        return $query->where([
          'partner_id' => $user['partner_id'],
          'partner_user_id' => $user['partner_user_id'],
          'key_pair' => PartnerLenderCredential::DEFAULT_KEY_PAIR,
        ]);
      })
      ->orderAsc('shorthand');

    $smsCredentials = TableRegistry::getTableLocator()->get('PartnerExtraCredentialsEntity')
      ->getCredentialsDecrypted($this->Auth->user('partner_id'), 'burstSms');

    $equifaxCredentials = TableRegistry::getTableLocator()->get('PartnerExtraCredentialsEntity')
      ->getCredentialsDecrypted($this->Auth->user('partner_id'), 'equifax');

    $credentials = TableRegistry::getTableLocator()->get('PartnerLenderCredentials')->getAllCredentialsForPartnerUserByLenderIdDecrypted($user);

    $aggregators = TableRegistry::getTableLocator()->get('AggregatorEntity')->find('all')->where(['status' => true, 'country' => getenv('REGION', true)]);

    $smtpCredentialsEntity = TableRegistry::getTableLocator()->get('PartnerExtraCredentialsEntity')
      ->getCredentialsDecrypted($user['partner_id'], 'smtp', $user['partner_user_id']);
    $smtpCredentials = $smtpCredentialsEntity ? $smtpCredentialsEntity->toArray() : null;

    $smtpVeified = TableRegistry::getTableLocator()->get('PartnerTwofaCodes')->find('all')
      ->where([
        'partner_user_id' => $this->Auth->user('partner_user_id'),
        'verification_type' => 'smtp',
      ])
      ->order(['id' => 'DESC'])
      ->first();

    $withWisrId     = [];
    $withoutWisrId  = [];

    foreach ($aggregators as $aggregator) {
      if (!empty($aggregator->wisr_partner_company_id)) {
        $withWisrId[] = $aggregator;
      } else {
        $withoutWisrId[] = $aggregator;
      }
    }


    if (!empty($partnerUser['name'])) {
      $names = explode(' ', trim($partnerUser['name']));
      $partnerUser['last_name'] = array_pop($names);
      $partnerUser['first_name'] = implode(' ', $names);
    }

    $this->set([
      'custom_privacy_form' => $custom_privacy_form,
      'is2faEnabled' => $is2faEnabled,
      'partner' => $partner,
      'max_partner_logo_width' => $max_partner_logo_width,
      'partnerUser' => $partnerUser,
      'senderId' => empty($smsCredentials) ? null : $smsCredentials->credentials->sender_id,
      'fakeKey' => empty($smsCredentials) ? null : '************',
      'equifaxUser' => empty($equifaxCredentials) ? null : $equifaxCredentials->credentials->username,
      'equifaxPassword' => empty($equifaxCredentials) ? null : str_repeat('*', strlen($equifaxCredentials->credentials->password)),
      'seo' => [
        'meta' => [
          'title' => 'Account' . $this->lendconfig['nameTag'],
          'og' => [
            'title' => 'Account' . $this->lendconfig['nameTag']
          ]
        ]
      ],
      'lenders' => $lenders,
      'credentials' => $credentials,
      'australian_aggregator_with_wisr_id' => $withWisrId,
      'australian_aggregator_without_wisr_id' => $withoutWisrId,
      'aggregators' => $aggregators,
      'smtp' => !empty($smtpCredentials['credentials']) ? (array)$smtpCredentials['credentials'] : null,
      'smtp_verified' => $smtpVeified->verified ?? false,
      'is_sent' => $smtpVeified->is_sent ?? false,
      'api_error' => $smtpVeified->api_error ?? null,
    ]);

    if (getenv('REGION', true) === 'au') {
      $this->viewBuilder()->setTemplate('index');
    } elseif (getenv('REGION', true) === 'nz') {
      $this->viewBuilder()->setTemplate('index_nz');
    }
  }

  public function disable2Fa()
  {
    $result = null;
    $recordId = $this->request->getData()['recordId'];
    /** @var PartnerUsers2faTable $partnerUser2faTable */
    $partnerUser2faTable = $this->loadModel('PartnerUsers2fa');
    try {
      $pu2faModel = $partnerUser2faTable->readRecord(['partner_user_2fa_id' => $recordId])[0];
      $pu2faModel['status'] = 'Disabled';
      $updateRecord = $partnerUser2faTable->updateRecordById($recordId, $pu2faModel);
      $result = array(
        'status' => (!empty($updateRecord) ? true : false),
        'feedback' => (!empty($updateRecord) ? '2FA disabled' : 'Due to system error we could not disable 2FA')
      );
    } catch (Exception $e) {
      $result = array(
        'status' => false,
        'feedback' => 'Due to system error we could not disable 2FA'
      );
      throw $e;
    } finally {
      return $this->setJsonResponse($result);
    }
  }
  public function get2faRecordInfo()
  {
    $result = null;
    $partnerUserid = $this->Auth->user('partner_user_id');
    $recordId = $this->request->getData()['record_id'];
    /** @var PartnerUsers2faTable $partnerUsers2FaTable */
    $partnerUsers2FaTable = $this->loadModel('PartnerUsers2fa');
    try {
      $recordModel = $partnerUsers2FaTable->readRecord(['partner_user_2fa_id' => $recordId])[0];
      $recordModel['enabled'] = date("jS M Y ( h:i A )", strtotime($recordModel['verification_code_sent']));
      unset($recordModel['partner_user_id'], $recordModel['verification_code']);
      $result = array('status' => true, 'record' => $recordModel, 'feedback' => '');
    } catch (Exception $e) {
      $result = array('status' => false, 'record' => null, 'feedback' => 'Due to server error we could not process', 'msg' => $e->getMessage());
      throw $e;
    } finally {
      return $this->setJsonResponse($result);
    }
  }

  public function updateQuoteCalcCount()
  {
    try {
      $partnerUser = $this->PartnerUsers->getPartnerUser(array('partner_user_id' => $this->Auth->user('partner_user_id')));
      $quoteCount = $partnerUser['quotes_calculated'] + 1;
      $success = $this->PartnerUsers->updatePartnerUser($this->Auth->user('partner_user_id'), array('quotes_calculated' => $quoteCount));
      return $this->setJsonResponse(array('success' => true, 'partner_user' => $success));
    } catch (\Exception $e) {
      return $this->setJsonResponse(['success' => false, 'error' => $e->getMessage()]);
    }
  }

  public function updateQuoteSelectedCount()
  {
    try {
      $partnerUser = $this->PartnerUsers->getPartnerUser(array('partner_user_id' => $this->Auth->user('partner_user_id')));
      $quoteCount = $partnerUser['quotes_selected'] + 1;
      $success = $this->PartnerUsers->updatePartnerUser($this->Auth->user('partner_user_id'), array('quotes_selected' => $quoteCount));
      return $this->setJsonResponse(array('success' => true, 'partner_user' => $success));
    } catch (\Exception $e) {
      return $this->setJsonResponse(['success' => false, 'error' => $e->getMessage()]);
    }
  }
  private function updateBillDetails($sent, $partnerId)
  {

    $partner = $this->LoadModel('Partners')->getPartner(array('partner_id' => $partnerId));

    // If there is not contact id of xero, don't update bank account detail
    if (empty($partner['contact_id']))
      return;

    $bankdetails = array(
      'partner_id' => $partnerId,
      'bank_acc_name' => $sent['partner']['bank_acc_name'],
      'bank_bsb' => $sent['partner']['bank_bsb'],
      'bank_acc_num' => $sent['partner']['bank_acc_num']
    );

    $this->LoadModel('BackgroundJobs')->addBackgroundJob(array(
      'job_type'      => 'xero_update_bankdetails',
      'ref_id'        => json_encode($bankdetails),
      'class_name'    => 'XeroUpdateDetails',
      'function_name' => 'update_bankdetails',
      'created'       => date('Y-m-d H:i:s'),
    ));
  }

  public function updateDetails()
  {
    $this->request->allowMethod(['post']);
    $sent = $this->request->getData();
    $errors = $this->validateUpdateDetails($sent);
    try {
      Log::info('---- updateDetails() START ----');
      Log::info(json_encode($errors));
      // Don't go any further if we have errors
      if (!empty($errors['errors'])) {
        throw new Exception(json_encode($errors['errors']), 422);
      }

      $sent = $errors['sent'];

      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }

      // Update partner details
      if (isset($sent['partner'])) {
        if($sent['business_address']){
          $sent['partner']['trading_address'] = $sent['business_address'];
        }
        $success = (bool)$this->LoadModel('Partners')->updatePartner($user['partner_id'], $sent['partner']);

        // Update bill details in XERO
        if ($sent['formsubmitted'] === 'bankdetails') {
          $this->updateBillDetails($sent, $user['partner_id']);
        }

        $aggregator_row = TableRegistry::getTableLocator()->get('AggregatorEntity')->find('all')
          ->where(['id' => $sent['partner']['aggregator_id']])->first();

        if (!empty($aggregator_row['wisr_partner_company_id'])) {

          $this->PartnerUsers->updateWisrPartnerCompanyId($aggregator_row['wisr_partner_company_id'], $user);
        }

        if (strtolower($aggregator_row['aggregator_name']) === 'other') {

          $slack_message = $user['name'] . " " .
            " from " . $user['company_name'] . " with partner id " .
            $user['partner_id'] . " has updated [Account >> company] with an Aggregator not in our current list. Product team - please verify the following aggregator exists and add to the Lend database" .
            " if required: " . $sent['partneruser']['main_aggregator_membership_name'];

          $channel = getenv('REGION', true) === 'au' ? 'partner_verification' : 'partner_verification_nz';
          $slack_message = ":eyes: " . $slack_message;
          $this->LoadModel('App')->postToSlack($slack_message, $channel);
        }
      }
      $lendSignUserData = [];
      // Update partner user details
      if (isset($sent['partneruser'])) {
        $changed = array();
        $sent['partneruser']['name'] = $sent['partneruser']['first_name'] . ' ' . $sent['partneruser']['last_name'];
        if($sent['partneruser']['first_name']){
          $lendSignUserData['firstName'] = $sent['partneruser']['first_name'];
        }
        if($sent['partneruser']['last_name']){
          $lendSignUserData['lastName'] = $sent['partneruser']['last_name'];
        }
        unset($sent['partneruser']['first_name'], $sent['partneruser']['last_name']);
        // Check which inputs have been updated
        if ($user['email'] != $sent['partneruser']['email']) {
          $changed[] = 'Email';
        }
        if ($user['mobile'] != $sent['partneruser']['mobile']) {
          $changed[] = 'Mobile';
        }
        if ($user['phone'] != $sent['partneruser']['phone']) {
          $changed[] = 'Phone';
        }

        if (!empty($sent['newpassword']['password'])) {
          $sent['partneruser']['password'] = $sent['newpassword']['password'];
          $changed[] = 'Password';
        }

        $success = (bool)$this->PartnerUsers->updatePartnerUser($user['partner_user_id'], $sent['partneruser']);
      }

      // Update this user's token etc:
      if ($success) {
        $user = $this->PartnerUsers->getPartnerUser(array('partner_user_id' => $user['partner_user_id']));
        unset($user['password']);

        $this->Auth->setUser($user);
        $token = JWT::encode(
          [
            'sub' => $user['email'],
            'exp' => (time() + 86400)
          ],
          Security::getSalt()
        );
        $this->setCookieSameSite('auth_token', $token, 0, "/", "." . $_SERVER['SERVER_NAME'], getenv('LEND_ENV') > 0, true, (getenv('LEND_ENV') > 0 ? "None" : false));

        //update info in lend sign
        if (!empty($user['lend_signature_user_ref'])) {
          $partnerRef = \App\Lend\LendInternalAuth::hashPartnerId($user['partner_id']);
          $lendSignClient = new LendSignatureServiceBackendClient($partnerRef, $user['partner_user_ref']);
          $lendSignUser = $lendSignClient->call('GET', '/get-user-by-custom-ref');
          if($lendSignUser['ref'] && isset($sent['partneruser'])){
            $lendSignUserData['ref'] = $lendSignUser['ref'];
            if($sent['partneruser']['email']){
              $lendSignUserData['email'] = $sent['partneruser']['email'];
            }
            if($sent['partneruser']['mobile']){
              $lendSignUserData['mobile'] = $sent['partneruser']['mobile'];
            }
            $lendSignClient->call('PUT', '/users/'.$lendSignUser['ref'], ['data' => $lendSignUserData], $lendSignUser['userGroup']['ref']);
          }
          if($lendSignUser['userGroup']['ref'] && isset($sent['partner'])){
            $lendSignUserGroupData = ['ref' => $lendSignUser['userGroup']['ref']];
            if($sent['partner']['company_name']){
              $lendSignUserGroupData['groupName'] = $sent['partner']['company_name'];
            }
            if($sent['partner']['trading_address']){
              $lendSignUserGroupData['tradingAddress'] = $sent['partner']['trading_address'];
            }
            $lendSignClient->call('PUT', '/user-groups/'.$lendSignUser['userGroup']['ref'], ['data' => $lendSignUserGroupData]);
          }
        }
        
      }
      return $this->setJsonResponse(array('success' => $success, 'changed' => $changed));
    } catch (Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      $array_errors = json_decode($e->getMessage(), true);
      if (is_array($array_errors)) {
        return $this->setJsonResponse(array('success' => false, 'error' => $array_errors), $e->getCode());
      } else {
        return $this->setJsonResponse(array('success' => false, 'error' => $e->getMessage()), $e->getCode());
      }
    }
  }

  public function updateAccreditation()
  {
    $user = $this->Auth->identify();
    if ($user === false) {
      $user = $this->Auth->user();
    }

    $this->request->allowMethod(['post']);
    $sent = $this->request->getData();
    $success = (bool)$this->PartnerUsers->updatePartnerUser($user['partner_user_id'], $sent);
    if ($success) {
      $user_details = $this->PartnerUsers->getPartnerUser(array('partner_user_id' => $user['partner_user_id']));
      unset($user_details['password']);

      $this->Auth->setUser($user_details);
      $token = JWT::encode(
        [
          'sub' => $user['email'],
          'exp' => (time() + 86400)
        ],
        Security::getSalt()
      );
      $this->setCookieSameSite('auth_token', $token, 0, "/", "." . $_SERVER['SERVER_NAME'], getenv('LEND_ENV') > 0, true, (getenv('LEND_ENV') > 0 ? "None" : false));
    }

    $this->setJsonResponse(array('success' => $success));
  }

  private function validateUpdateDetails($sent)
  {
    $user = $this->Auth->identify();
    if (empty($user)) {
      $user = $this->Auth->user();
    }

    $errors = array();
    $currentCompDetails = $this->LoadModel('Partners')->getPartner(array('partner_id' => $user['partner_id']));
    $currentDetails = $this->PartnerUsers->getPartnerUser(array('partner_user_id' => $user['partner_user_id']));

    // If new password submitted, must pass password, and confirm must match
    if (!empty($sent['newpassword']['password'])):
      if (empty($sent['newpassword']['current'])) {
        $errors[] = array('input' => 'newpassword[current]', 'message' => 'You must provide your current password');
      } elseif (!$this->PartnerUsers->checkPassword($sent['newpassword']['current'], $currentDetails['password'])) {
        $errors[] = array('input' => 'newpassword[current]', 'message' => 'You have provided an incorrect password');
      }
      if (empty($sent['newpassword']['confirm'])) {
        $errors[] = array('input' => 'newpassword[confirm]', 'message' => 'You must confirm your new password');
      } elseif ($sent['newpassword']['confirm'] !== $sent['newpassword']['password']) {
        $errors[] = array('input' => 'newpassword[confirm]', 'message' => 'Your Confirm Password does not match your New Password');
      } elseif (!empty($sent['newpassword']['password']) && !$this->PartnerUsers->isValidPass($sent['newpassword']['password'])) {
        $errors[] = array('input' => 'newpassword[password]', 'message' => 'New Password does not match Requirements');
      }
    endif;


    // Validation specific to different form types
    switch ($sent['formsubmitted']) {
      case 'contactdetails':

        // if (empty($sent['partneruser']['email']) or !filter_var($sent['partneruser']['email'], FILTER_VALIDATE_EMAIL)) {
        //   $errors[] = array('input' => 'partneruser[email]', 'message' => 'You must provide a valid email address');
        // } elseif ($sent['partneruser']['email'] !== $currentDetails['email'] and $this->PartnerUsers->getPartnerUser(array('email' => $sent['partneruser']['email']))) {
        //   $errors[] = array('input' => 'partneruser[email]', 'message' => 'Another account is already using this email address');
        // }

        if (!empty($sent['partneruser']['mobile']))
          $sent['partneruser']['mobile'] = preg_replace("/[^0-9]/", "", $sent['partneruser']['mobile']);

        if (empty($sent['partneruser']['mobile'])) {
          $errors[] = array('input' => 'partneruser[mobile]', 'message' => 'A mobile phone number is required');
        } elseif (
          getenv('REGION', true) === 'au'
          and (
            !is_numeric($sent['partneruser']['mobile'])
            or substr($sent['partneruser']['mobile'], 0, 2) !== '04'
            or strlen($sent['partneruser']['mobile']) != 10
          )
        ) {
          $errors[] = array('input' => 'partneruser[mobile]', 'message' => 'This does not look like a valid AU mobile');
        } elseif (
          getenv('REGION', true) === 'nz'
          and (
            !is_numeric($sent['partneruser']['mobile'])
            or strlen($sent['partneruser']['mobile']) > 11
          )
        ) {
          $errors[] = array('input' => 'partneruser[mobile]', 'message' => 'This does not look like a valid NZ mobile');
        }

        // Phone isn't really required, so we're not going to bother really applying any rules to it...
        if (!empty($sent['partneruser']['phone'])) {
          $sent['partneruser']['phone'] = preg_replace("/[^0-9]/", "", $sent['partneruser']['phone']);
          if (getenv('REGION', true) === 'au') {
            if (strlen($sent['partneruser']['phone']) > 11)
              $errors[] = array('input' => 'partneruser[phone]', 'message' => 'Phone Number looks too long');
            elseif (strlen($sent['partneruser']['phone']) < 10)
              $errors[] = array('input' => 'partneruser[phone]', 'message' => 'Phone Number looks too short');
          } elseif (getenv('REGION', true) === 'nz') {
            if (strlen($sent['partneruser']['phone']) !== 9)
              $errors[] = array('input' => 'partneruser[phone]', 'message' => 'Phone Number does not looks like a valid NZ landline number');
          }
        }

        if (empty($sent['partneruser']['first_name'])) {
          $errors[] = array('input' => 'partneruser[first_name]', 'message' => 'First name is required');
        }

        if (empty($sent['partneruser']['last_name'])) {
          $errors[] = array('input' => 'partneruser[last_name]', 'message' => 'Last name is required');
        }

        break;
      case 'companydetails':

        if (!empty($sent['partner']['abn'])) {
          $sent['partner']['abn'] = preg_replace("/[^0-9]/", "", $sent['partner']['abn']);
          if (getenv('REGION', true) === 'au' && strlen($sent['partner']['abn']) != 11)
            $errors[] = array('input' => 'partner[abn]', 'message' => 'Your ABN does not look valid');
          if (getenv('REGION', true) === 'nz' && strlen($sent['partner']['abn']) != 13)
            $errors[] = array('input' => 'partner[abn]', 'message' => 'Your NZBN does not look valid');
        } else {
          $errors[] = array('input' => 'partner[abn]', 'message' => 'Your ' . (getenv('REGION', true) === 'au' ? 'ABN' : 'NZBN') . ' is required');
        }

        if (empty($sent['partner']['company_name']))
          $errors[] = array('input' => 'partner[company_name]', 'message' => 'Your Company Name is required');

        if (empty($sent['partner']['address']))
          $errors[] = array('input' => 'partner[address]', 'message' => 'Your company\'s Address is required');
        if (empty($sent['partner']['suburb']))
          $errors[] = array('input' => 'partner[suburb]', 'message' => '');
        if (getenv('REGION', true) === 'au' && empty($sent['partner']['state']))
          $errors[] = array('input' => 'partner[state]', 'message' => '');
        if (getenv('REGION', true) === 'nz' && empty($sent['partner']['city']))
          $errors[] = array('input' => 'partner[city]', 'message' => '');
        if (empty($sent['partner']['postcode']))
          $errors[] = array('input' => 'partner[postcode]', 'message' => '');

        break;
      case 'branding':

        if (!empty($sent['partner']['brand_colour'])) {
          $sent['partner']['brand_colour'] = str_replace('#', '', $sent['partner']['brand_colour']);
          if (strtolower($sent['partner']['brand_colour']) === '00cf88') {
            $sent['partner']['brand_colour'] = NULL; // No need to save our default colour
          }
        }

        break;
      case 'bankdetails':

        $passwordCheckRequired = false;
        // If new bank details submitted, must pass password...
        if ($sent['partner']['bank_acc_name'] != $currentCompDetails['bank_acc_name']) {
          $passwordCheckRequired = true;
          if (empty($sent['partner']['bank_acc_name'])) {
            $errors[] = array('input' => 'partner[bank_acc_name]', 'message' => 'You must provide your account name');
          }
        }
        if ($sent['partner']['bank_bsb'] != $currentCompDetails['bank_bsb']) {
          $passwordCheckRequired = true;
          $sent['partner']['bank_bsb'] = preg_replace("/[^0-9]/", "", $sent['partner']['bank_bsb']);
          if (empty($sent['partner']['bank_bsb'])) {
            $errors[] = array('input' => 'partner[bank_bsb]', 'message' => 'BSB is required');
          } elseif (strlen($sent['partner']['bank_bsb']) != 6) {
            $errors[] = array('input' => 'partner[bank_bsb]', 'message' => 'BSB looks invalid');
          }
        }
        if ($sent['partner']['bank_acc_num'] != $currentCompDetails['bank_acc_num']) {
          $passwordCheckRequired = true;
          $sent['partner']['bank_acc_num'] = preg_replace("/[^0-9]/", "", $sent['partner']['bank_acc_num']);
          if (empty($sent['partner']['bank_acc_num'])) {
            $errors[] = array('input' => 'partner[bank_acc_num]', 'message' => 'Account Number is required');
          } elseif (strlen($sent['partner']['bank_acc_num']) < 6 && getenv('REGION', true) === 'au') {
            $errors[] = array('input' => 'partner[bank_acc_num]', 'message' => 'Account Number looks invalid');
          }
        }
        if ($passwordCheckRequired and !$this->PartnerUsers->checkPassword($sent['credentials']['password'], $currentDetails['password']))
          $errors[] = array('input' => 'credentials[password]', 'message' => 'You have provided an incorrect password');

        break;
    }

    return array('errors' => $errors, 'sent' => $sent);
  }

  /**
   * @param $vmobile in format of 04XXXXXXXX (validation must be done in ui)
   * @param $vmessage string
   */
  public function sendSms2fa()
  {
    try {
      $result = null;
      $vmobile = $this->request->getData()['mobile'];


      /** @var PartnerUsers2faTable $partnerUser2faTable */
      $partnerUser2faTable = $this->loadModel('PartnerUsers2fa');
      $smsHelper = new SmsHelper();
      $partnerUserID = $this->Auth->user('partner_user_id');
      //-#zzz-check if there is no active record

      //--create new record if txt sent
      $pu2faModel['partner_user_id'] = $this->Auth->user('partner_user_id');
      $pu2faModel['mobile'] = $vmobile;
      $pu2faModel['status'] = 'Unverified';
      $pu2faModel['verification_code'] = rand(100000, 999999);
      $pu2faModel['created'] = date('Y-m-d H:i:s');
      $vmessage = 'Here\'s your verification code to enable 2FA on your Lend Platform account: ' . $pu2faModel['verification_code'];


      if (getenv('REGION', true) === 'au') {
        //$toMobile = preg_replace(array('/ /', '/^04*./'), array('', '614'), $vmobile);
        $toMobile = preg_replace(array('/ /', '/^04/'), array('', '614'), $vmobile);
      } elseif (getenv('REGION', true) === 'nz') {
        //$toMobile = preg_replace(array('/ /', '/^02*./'), array('', '642'), $vmobile);
        $toMobile = preg_replace(array('/ /', '/^02/'), array('', '642'), $vmobile);
      }

      $smsconfig = Configure::read('Lend.SMS');
      $from = $smsconfig['BurstSMS']['sender_id'];

      $resultsms = $smsHelper->sendSms($vmessage, $toMobile, $from);
      if (strtoupper($resultsms->error->code) === 'SUCCESS') {
        $pu2faModel['verification_code_sent'] = date('Y-m-d H:i:s');
        $result = array('status' => true);
      } else {
        throw new Exception('Sorry due to system problem we could not send you text');
      }
      //$partnerNotifTable->sendSMSNotification($recipient,);
      $q = $partnerUser2faTable->createRecord($pu2faModel);
      $result['id'] = $q;
    } catch (Exception $e) {
      throw $e;
    } finally {
      return $this->setJsonResponse($result);
    }
  }

  public function enable2Fa()
  {
    $result = null;
    $user_id = $this->Auth->user('partner_user_id');
    //$verificationCode = $this->request->getData()['verification_code'];
    $requestData = $this->request->getData();
    /** @var PartnerUsers2faTable $partneruser2faTable */
    $partneruser2faTable = $this->loadModel('PartnerUsers2fa');
    try {

      $pu2faModel = $partneruser2faTable->readRecord(['partner_user_2fa_id' => $requestData['recordId']])[0];  //findByVerificationCode($verificationCode, $user_id, 'unverified')[0];
      if ($requestData['enteredCode'] != $pu2faModel['verification_code']) { //entered vfcode does not match
        $result = array('status' => false, 'feedback' => 'Verification code does not match');
        return $this->setJsonResponse($result);
      }
      $pu2faModel['status'] = 'Enabled';
      //unset($pu2faModel['partner_user_2fa_id']);
      $updateAction = $partneruser2faTable->updateRecordById($pu2faModel['partner_user_2fa_id'], $pu2faModel);

      $result = array('status' => (!empty($updateAction)), 'feedback' => (!empty($updateAction) ? '2FA activated' : 'Due to system problem we could not update record'));
    } catch (Exception $e) {
      throw $e;
    } finally {
      return $this->setJsonResponse($result);
    }
  }
  /**
   * Check if the user is locked out due to continous failed logins
   * @param mixed $partnerUserId
   * @return array|null
   */
  private function checkContinuousFailures($partnerUserId)
  {
    $lockoutAttempts = 5;
    $lockoutMins = 15;
    $timeLimit = (new DateTime())->sub(new DateInterval("PT{$lockoutMins}M"))->format('Y-m-d H:i:s');

    //Retrieve the latest lockoutAttempts records
    $partnerUserLoginsTable = TableRegistry::getTableLocator()->get('PartnerUserLogins');
    $query = $partnerUserLoginsTable->find()
      ->select(['successful_login', 'login_time'])
      ->where([
        'partner_user_id' => $partnerUserId,
        'login_time >=' => $timeLimit,
      ])
      ->order(['login_time' => 'DESC'])->distinct()
      ->limit($lockoutAttempts);
    $latestLogins = $query->toArray();
    $successfulLogins = 0;
    $failedLogins = 0;
    $lastFailedLoginTime = null;

    foreach ($latestLogins as $login) {
      if ($login->successful_login === true) {
        $successfulLogins++;
      } else {
        $failedLogins++;
        if ($lastFailedLoginTime === null) {
          $lastFailedLoginTime = $login->login_time->format('Y-m-d H:i:s');
        }
      }
    }
    // Check if the last failed login was within the last lockoutMins minutes
    $isLockedOut = false;
    $minutesToWait = 0;

    if ($failedLogins === $lockoutAttempts) {
      $lastFailedLoginTime = new DateTime($lastFailedLoginTime);
      $currentTime = new DateTime();
      $interval = $lastFailedLoginTime->diff($currentTime);

      if ($interval->i < $lockoutMins) {
        $isLockedOut = true;
        $minutesToWait = $lockoutMins - $interval->i;
      }
    }
    if ($isLockedOut) {
      return ['success' => false, 'mins' => $minutesToWait, 'msg' => 'Your account has been temporarily locked due to multiple unsuccessful login attempts. Please try again in [MINS] minutes.'];
    }
    return null;
  }
  private function _check2fa($user, $partnerUser2faTable)
  {
    //successfull creds check for 2fa
    $pu2fa = $partnerUser2faTable->readRecord([
      'partner_user_id' => $user['partner_user_id'],
      'status' => 'Enabled'
    ], ['created' => 'Desc']);
    if (sizeof($pu2fa) < 1) { //no 2fa
      $result = array('status' => false, 'feedback' => 'no 2fa', 'msg' => 'correct creds');
      return $result;
    }
    //#start-----------sms to user
    $smsHelper = new SmsHelper();
    //--update record if txt sent
    $pu2fa[0]['verification_code'] = rand(100000, 999999);
    $vmessage = 'Here\'s your verification-code to log into the Lend Platform account: ' . $pu2fa[0]['verification_code'];

    if (getenv('REGION', true) === 'au') {
      $toMobile = preg_replace(array('/ /', '/^04/'), array('', '614'), $pu2fa[0]['mobile']);
    } elseif (getenv('REGION', true) === 'nz') {
      $toMobile = preg_replace(array('/ /', '/^02/'), array('', '642'), $pu2fa[0]['mobile']);
    }

    $smsconfig = Configure::read('Lend.SMS');
    $from = $smsconfig['BurstSMS']['sender_id'];

    $resultsms = $smsHelper->sendSms($vmessage, $toMobile, $from);
    $isSmsSent = strtoupper($resultsms->error->code) === 'SUCCESS';
    if ($isSmsSent) {
      $pu2fa[0]['verification_code_sent'] = date('Y-m-d H:i:s');
      $partnerUser2faTable->updateRecordById($pu2fa[0]['partner_user_2fa_id'], $pu2fa[0]);
    } else {
      $result = array('status' => false, 'feedback' => 'failed to send text message', 'msg' => 'correct creds', 'sms_sent_status' => $isSmsSent);
      return $result;
    }
    //#end-----------sms to user
    $result = array('status' => true, 'feedback' => 'New code has been sent', 'msg' => 'correct creds', 'sms_sent_status' => $isSmsSent);
    return $result;
  }


  public function submitContactUs()
  {
    $this->request->allowMethod(['post']);

    $data = $this->request->getData();
    $data['account'] = $this->Auth->user();
    $mail = new Email('default');
    $mail->addHeaders(['X-SMTPAPI' => json_encode(array('category' => array('account_change')))]);
    $mail->template('contactus', 'default');
    $mail->emailFormat('html');
    $mail->to(['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>']);
    $mail->subject('Contact Lend - From PartnerUserID: ' . $data['account']['partner_user_id']);
    $mail->viewVars(array(
      'data' => $data,
    ));
    try {
      $success = $mail->send();
    } catch (Exception $e) {
      $success = false;
    }
    $reply = $success ? array('success' => true, 'message' => 'Message Sent!') : array('success' => false, 'message' => 'Something\'s wrong, try again.');
    return $this->setJsonResponse($reply);
  }


  public function login()
  {
    $params = $this->request->getQuery();
    $this->loadModel('PartnerUserLogins');
    /** @var PartnerUsers2faTable $partnerUser2faTable */
    $partnerUser2faTable = $this->loadModel('PartnerUsers2fa');
    $is_ajax = $this->request->is('ajax');
    if (!$this->request->is('post')) {
      unset($_COOKIE['auth_token']);
      setcookie('auth_token', '', time() - 3600, "/"); //security/PLAT-4163_Account_Lockout
    }

    if ($this->request->is('post') or !empty($_COOKIE['auth_token'])) {
      $user = $this->Auth->identify();

      $partner = $this->PartnerUsers->getPartnerUser(array('email' => $this->request->getData('email')));
      if ($partner) {
        $continuousFailures = $this->checkContinuousFailures($partner['partner_user_id']);
        if ($continuousFailures !== null)
          return $this->setJsonResponse($continuousFailures);
      }
      $clientIP = $_SERVER['REMOTE_ADDR'] ?: ($_SERVER['HTTP_X_FORWARDED_FOR'] ?: $_SERVER['HTTP_CLIENT_IP']); // client unmasked IP

      if (!$user) {
        $token_check = $this->setProperErrorMsgIfActualUserExist($_COOKIE['auth_token']);
        if (!$token_check['success'])
          $this->message('fail', $msg = $token_check['msg']);

        // Add record to PartnerUserLoginsTable
        // $partner = $this->PartnerUsers->getPartnerUser(array('email'=>$this->request->getData('email')));
        if ($partner)
          $this->PartnerUserLogins->addPartnerUserLogin(array('partner_user_id' => $partner['partner_user_id'], 'ip_address' => $clientIP, 'successful_login' => false));
      } elseif (empty($user['email_verified']) && empty($user['active'])) {
        // There are some users who are activated without email verified
        $this->message('fail', $msg = 'You have not verified your email address. Please check your inbox for the link to verify your email.');
        $this->set('login_email', $this->request->getData('email'));
      } elseif (empty($user['active'])) {
        $this->message('fail', $msg = 'Your account has not yet been activated. One of our team members will contact you shortly to activate it.');
        $this->set('login_email', $this->request->getData('email'));
      } else {
        // Check partner level's active and reason
        $partnerInfo = $this->loadModel('Partners')->getPartner(['partner_id' => $user['partner_id']]);
        $partner_feature = $this->loadModel('PartnerFeatureFlags')->getFeatureFlag(['partner_id' => $user['partner_id']]);
        if ((strtolower($partnerInfo['country']) !== strtolower(getenv('REGION', true))) && ($user['account_type'] != 'Lend Staff')) {
          $this->message('fail', $msg = 'Please make sure you are accessing the right domain');
          $this->set('login_email', $this->request->getData('email'));
        } elseif ($partnerInfo && empty($partnerInfo['active'])) {
          switch ($partnerInfo['inactive_reason']) {
            case 'ON_HOLD':
              $this->message('fail', $msg = 'Your account has been placed on hold due to inactivity. We sent you an email. Please contact us if you wish to start using your account');
              break;
            default:
              $this->message('fail', $msg = 'Your account has not been verified yet');
              break;
          }
          $this->set('login_email', $this->request->getData('email'));
        } elseif ($this->request->getQuery('action') == 'check2fa') {
          return $this->setJsonResponse($this->_check2fa($user, $partnerUser2faTable));
        } else {
          $this->Auth->setUser($user);
          $token = JWT::encode(
            [
              'sub' => $user['email'],
              'exp' => (time() + 86400)
            ],
            Security::getSalt()
          );
          $this->setCookieSameSite('auth_token', $token, 0, "/", "." . $_SERVER['SERVER_NAME'], getenv('LEND_ENV') > 0, true, (getenv('LEND_ENV') > 0 ? "None" : false));

          // reset dashboard cookies
          $this->resetDashboardCookies();
          // end reset dashboard cookies

          $appendix = '';
          if (!$this->request->is('post') && !empty($this->request->getQuery('success')))
            $appendix .= '?success=' . $this->request->getQuery('success');
          elseif (!$this->request->is('post') && !empty($this->request->getQuery('error')))
            $appendix .= '?error=' . $this->request->getQuery('error');

          // Add record to PartnerUserLoginsTable
          $this->PartnerUserLogins->addPartnerUserLogin(array('partner_user_id' => $user['partner_user_id'], 'ip_address' => $clientIP, 'successful_login' => true));


          $redirect = '';
          if (empty($user['settings_complete'])) {
            $partner_users_table = TableRegistry::getTableLocator()->get('PartnerUserEntity');
            $partner_user = $partner_users_table->get($user['partner_user_id'], [
              'contain' => ['PartnerNotificationSettingEntity'],
            ]);
            $partner_user_data = [
              'settings_complete' => true,
            ];
            if (empty($partner_user['partner_notification_settings'])) {
              $partner_user_data['partner_notification_settings'] = [];
              for ($i = 1; $i <= 10; $i++) {
                $partner_user_data['partner_notification_settings'][] = [
                  'notif_group_id' => $i,
                  'ref_type' => 'partner_user_id',
                  'ref_id' => $user['partner_user_id'],
                  'sms_on' => true,
                  'email_on' => true,
                ];
              }
            }
            $partner_users_table->patchEntity($partner_user, $partner_user_data, [
              'associated' => ['PartnerNotificationSettingEntity'],
            ]);
            $partner_users_table->save($partner_user);

            $redirect = '/notifications'; // for first visit
          } elseif (!isset($params['redirect'])) {
            switch ($this->Auth->user('account_type')) {
              case 'Lend Staff':
                $redirect = '/staff/dashboard' . $appendix;
                break;
              case 'Lender':
                $redirect = '/lender/dashboard' . $appendix;
                break;
              default:
                $termsConfig = 'Lend.partner_terms_edited';
                if (strtolower($partnerInfo['country']) === 'nz') {
                  $termsConfig = 'Lend.nz_partner_terms_edited';
                }
                if (strtotime($partnerInfo['terms_agreed_date']) < strtotime(Configure::read($termsConfig))) {
                  $redirect = '/partners/dashboard';
                } else if ($partnerInfo['status_system'] == 'manual') {
                  $redirect = "/lead/dashboard/kanban" . $appendix;
                } else {
                  $redirect = '/partners/dashboard?manage=Leads' . $appendix;
                }
                break;
            }
          } else {
            $redirect = $params['redirect'] . $appendix;
          }

          // check if it's free user
          if ($partner_feature['verified_but_no_UI_access'])
            $redirect = '/partners/add_affiliate_lead';

          if (!$is_ajax) {
            return $this->redirect($redirect);
          }
        }
      }
    }
    if ($this->request->getQuery('action') == 'verify2faCodeOnLogin' && $is_ajax) {
      $vv2fa = $this->request->getData('verification_code');
      $vemail = $this->request->getData('email');
      $result = $this->_verify2faCodeOnLogin($vv2fa, $vemail);
      return $this->setJsonResponse($result);
    } elseif ($this->request->getQuery('action') == 'check2fa' && $is_ajax) {
      return $this->setJsonResponse($this->_check2fa($user, $partnerUser2faTable));
    } elseif ($is_ajax) {
      return $this->setJsonResponse(['success' => empty($msg), 'msg' => !empty($msg) ? $msg : '', 'redirect' => !empty($redirect) ? $redirect : '']);
    }

    $this->set('user', $this->Auth->user());
    $this->set('seo', array(
      'meta' => array(
        'title' => 'Login' . $this->lendconfig['nameTag'],
        'description' => 'The perfect solution for finance brokers, lead generators and affiliate marketers. More clients get funded. You remain in complete control. Centralised dashboard & Real time reporting. Register today.',
        'og' => array(
          'title' => 'Login' . $this->lendconfig['nameTag'],
          'description' => 'The perfect solution for finance brokers, lead generators and affiliate marketers. More clients get funded. You remain in complete control. Centralised dashboard & Real time reporting. Register today.',
        ),
      )
    ));
  }

  /** check 2fa verification code on login
   * @param $verificationCode
   * @param $email
   */
  private function _verify2faCodeOnLogin($verificationCode, $email)
  {
    /** @var PartnerUsers2faTable $partnerUser2faTable */
    $partnerUser2faTable = $this->loadModel('PartnerUsers2fa');
    $partner = $this->PartnerUsers->getPartnerUser(array('email' => $email));
    $pu2fa = $partnerUser2faTable->readRecord([
      'partner_user_id' => $partner['partner_user_id'],
      'status' => 'Enabled'
    ], ['created' => 'Desc'], 1)[0];
    $vfSentTime = new \DateTime($pu2fa['verification_code_sent']);
    $now = new \DateTime('now');
    $interval = date_diff($vfSentTime, $now);
    $diffInMinutes = $interval->format('%i');
    if ($diffInMinutes > 5) { //more than 5 minutes passed
      $result = array('status' => false, 'feedback' => 'This code has expired now, please try logging in again');
    } elseif ($pu2fa['verification_code'] == $verificationCode) { //verification code entered correctly
      //empty those verification_code and verification_code_sent
      $pu2fa['verification_code'] = null;
      $pu2fa['verification_code_sent'] = null;
      $partnerUser2faTable->updateRecordById($pu2fa['partner_user_2fa_id'], $pu2fa);
      $result = array('status' => true, 'feedback' => 'ok');
    } else { //verification entered does not match with our record
      $result = array('status' => false, 'feedback' => 'Verification Code incorrect', 'msg' => 'not match with record');
    }
    return $result;
  }

  /**
   * Show the proper error message for the case where user is inactivated(active is false) but user is still on pages
   */
  protected function setProperErrorMsgIfActualUserExist($token)
  {
    if (!empty($token)) {
      $result = LendAuthenticate::decode(['allowedAlgs' => ['HS256']], $token);
      if ($result['success']) {
        $user = $this->PartnerUsers->getPartnerUser(['email' => $result['payload']->sub]);
        if ($user && empty($user['active'])) {
          // $this->message('fail', $msg = 'Your account has been inactivated.');
          // return;
          return ['success' => false, 'msg' => 'Your account has been inactivated.'];
        } elseif ($user) {
          return ['success' => true];
        }
      }
    }

    return ['success' => false, 'msg' => 'Invalid username or password'];
  }

  public function forgotPassword()
  {
    if ($this->request->is('post')) {
      $isAjax = $this->request->getData('isAjax');
      try {
        $email = $this->request->getData('email');
        if (strpos($email, '@') === false || substr_count($email, '@') > 1)
          throw new \Exception("Please enter valid email address");

        $partnerUser = $this->PartnerUsers->getPartnerUser(array('email' => $email), false);
        if (!empty($partnerUser)) {
          if (empty($partnerUser['active']))
            throw new \Exception("Your account has not been verified yet.");

          // Check if forgot_password_generated is within the last 15 minutes
          $limitMins = 15;
          if (!empty($partnerUser['token_generated'])) {
            $generatedTime = strtotime($partnerUser['token_generated']);
            $currentTime = time();
            $minutesAgo = floor(($currentTime - $generatedTime) / 60);

            if ($minutesAgo < $limitMins) { // Password reset within the last $limitMins minutes
              $remainingMinutes = $limitMins - $minutesAgo;
              switch ($minutesAgo) {
                case 0:
                  $prefix = "You have just reset your password.";
                  break;
                case 1:
                  $prefix = "You reset your password $minutesAgo minute ago.";
                  break;
                default:
                  $prefix = "You reset your password $minutesAgo minutes ago.";
                  break;
              }
              $minsSuffix = ($remainingMinutes == 1) ? "minute" : "minutes";
              // $prefix = ($minutesAgo == 0)?"You have just reset your password.":"You reset your password $minutesAgo minutes ago.";
              throw new \Exception($prefix . " Please wait for $remainingMinutes $minsSuffix before trying again.");
            }
          }

          //generate unique code and save it into ParternUsers
          $token = $this->PartnerUsers->generateForgotPassCode($email);
          if (empty($token))
            throw new \Exception('Error occurs, please try later');

          //send notification email immediately
          $this->loadModel('PartnerNotifications')->sendPartnerNotifications($partnerUser['partner_id'], 'ResetPassw', false, [], $partnerUser['partner_user_id']);
        }
        //to prevent User enumeration attack, we always send the same message
        $server_message = array('type' => 'success', 'msg' => "If an account associated with the provided email address exists, an email containing instructions on how to reset the password would be sent.");
      } catch (\Exception $e) {
        $server_message = array('type' => 'error', 'msg' => $e->getMessage());
      }
      if ($isAjax) {
        $this->setJsonResponse($server_message);
      } else {
        $this->set('server_message', $server_message);
      }
    }
    $this->set('seo', array('meta' => array('title' => 'Forgot password' . $this->lendconfig['nameTag'], 'og' => array('title' => 'Forgot Password' . $this->lendconfig['nameTag']))));
  }

  public function resetPassword($token = null)
  {
    try {
      $server_message = array();
      if (empty($token))
        throw new \Exception("Invalid url");
      $partnerUser = $this->PartnerUsers->getpartnerUser(array('token' => $token), false);
      if (empty($partnerUser['partner_user_id']) || empty($partnerUser['token_expired']))
        throw new \Exception("Expired url");

      if (!$this->request->is('post')) {
        //load the reset form
        $expiredObj = new \DateTime($partnerUser['token_expired']);
        $nowObj = new \DateTime();
        if ($expiredObj <=  $nowObj)
          throw new \Exception("Expired url");
      } else {
        //update password
        $data = $this->request->getData();
        if (empty($data['partner_user']['password']))
          throw new \Exception("Please enter New Password");

        if (empty($data['partner_user']['confirm_password']))
          throw new \Exception("Please enter Confirm Password");
        if ($data['partner_user']['password'] !== $data['partner_user']['confirm_password'])
          throw new \Exception("New Password and Confirm Password must be same");

        if (
          !empty($data['partner_user']['password']) and
          (strlen($data['partner_user']['password']) < 10 or
            strtoupper($data['partner_user']['password']) === $data['partner_user']['password'] or
            strtolower($data['partner_user']['password']) === $data['partner_user']['password'] or
            !preg_match('/\d/', $data['partner_user']['password']) or
            !preg_match('/[ !~@#$%^&*()_+\-=\[\]{};\':"\\|,.<>\/?]/', $data['partner_user']['password'])
          )
        ) {
          throw new \Exception("Password doesn't meet the requirements.");
        }
        $params = [
          'password' => $data['partner_user']['password'],
          'token' => null,
          'token_expired' => null,
        ];
        $changed = $this->PartnerUsers->updatePartnerUser($partnerUser['partner_user_id'], $params);
        if (empty($changed))
          throw new \Exception("Error occurs, please try later");

        $server_message = array('type' => 'success', 'msg' => 'Your password was updated successfully');
      }
    } catch (\Exception $e) {
      $error = $e->getMessage();
      $type = ($error == 'Expired url') ? 'warning' : 'error';

      $server_message = array('type' => $type, 'msg' => $error);
    }
    $this->set('token', $token);
    $this->set('server_message', $server_message);
  }

  public function activateNewAccount($token = null)
  {
    try {
      $server_message = array();
      if (empty($token))
        throw new \Exception("Invalid url");
      $partnerUser = $this->PartnerUsers->getpartnerUser(array('token' => $token), false);
      if (empty($partnerUser['partner_user_id']) || empty($partnerUser['token_expired']))
        throw new \Exception("Expired url");

      $this->set('user', [
        'name' => $partnerUser['name'],
        'mobile' => $partnerUser['mobile'],
        'contact_title' => $partnerUser['contact_title'],
      ]);

      if (!$this->request->is('post')) {
        //load the reset form
        $expiredObj = new \DateTime($partnerUser['token_expired']);
        $nowObj = new \DateTime();
        if ($expiredObj <=  $nowObj)
          throw new \Exception("Expired url");
      } else {
        //update password
        $data = $this->request->getData();

        Log::error($data);

        if (empty($data['partner_user']['password']))
          throw new \Exception("Please enter New Password");
        if (empty($data['partner_user']['confirm_password']))
          throw new \Exception("Please enter Confirm Password");
        if ($data['partner_user']['password'] !== $data['partner_user']['confirm_password'])
          throw new \Exception("New Password and Confirm Password must be same");

        if (
          !empty($data['partner_user']['password']) and
          (strlen($data['partner_user']['password']) < 10 or
            strtoupper($data['partner_user']['password']) === $data['partner_user']['password'] or
            strtolower($data['partner_user']['password']) === $data['partner_user']['password'] or
            !preg_match('/\d/', $data['partner_user']['password']) or
            !preg_match('/[ !~@#$%^&*()_+\-=\[\]{};\':"\\|,.<>\/?]/', $data['partner_user']['password'])
          )
        ) {
          throw new \Exception("Password doesn't meet the requirements.");
        }

        $params = [
          'password'       => $data['partner_user']['password'],
          'token'           => null,
          'token_expired'   => null,
          'email_verified'  => 1,
          'active'          => 1
        ];
        if (isset($data['partner_user']['name']))                $params['name']           = $data['partner_user']['name'];
        if (isset($data['partner_user']['mobile']))              $params['mobile']         = $data['partner_user']['mobile'];
        if (isset($data['partner_user']['contact_title']))       $params['contact_title']  = $data['partner_user']['contact_title'];

        $changed = $this->PartnerUsers->updatePartnerUser($partnerUser['partner_user_id'], $params);

        $subscriptionEnabled = $this->loadModel('PartnerFeatureFlags')->getFeatureFlag(['partner_id' => $partnerUser['partner_id']]);

        if ($subscriptionEnabled['access_to_subscription']) {
          $currentPartnerSubPlan = $this->LoadModel('Subscription')->getAllPartnerSubscriptionPlan(['partner_id' => $partnerUser['partner_id']], ['id' => 'DESC'], ['limit' => 1, 'offset' => 0]);
          if ($currentPartnerSubPlan) {
            $partnerActiveUsers = $this->PartnerUsers->getActiveUsers($partnerUser['partner_id']);
            $activeUserCount = count($partnerActiveUsers);
            $totalUsers = $currentPartnerSubPlan[0]['total_users'];
            $adminPartnerUser = $this->PartnerUsers->getAdminPartnerUser($partnerUser['partner_id']);
            $currentPlan = $this->LoadModel('Subscription')->getSubscriptionPlan(['id' => $currentPartnerSubPlan[0]['subscription_plans_id']]);
            if ($activeUserCount > $totalUsers) {
              $this->LoadModel('App')->postToSlack(":warning: Partner ID: " . $partnerUser['partner_id'] . " is currently on " . $currentPlan['name'] . " and pays for " . $totalUsers . ". They have just gone over their user cap. Please contact them on " . $adminPartnerUser['mobile'] . " or " . $adminPartnerUser['email'] . " discuss their subscription plan.", 'customer-service');
            }
          }
        }

        if (empty($changed))
          throw new \Exception("Error occurs, please try later");

        $server_message = array('type' => 'success', 'msg' => 'Your account was activated successfully');
      }
    } catch (\Exception $e) {
      $error = $e->getMessage();
      $type = ($error == 'Expired url') ? 'warning' : 'error';

      $server_message = array('type' => $type, 'msg' => $error);
    }
    $this->viewBuilder()->setLayout('empty');
    $this->set('token', $token);
    $this->set('server_message', $server_message);
  }

  public function logout()
  {
    unset($_COOKIE['auth_token']);
    setcookie('auth_token', '', time() - 3600, "/", "." . $_SERVER['SERVER_NAME']);

    $this->Cookie->delete('Dashboard.filters');
    $this->Auth->logout();
    $this->redirect('/');
  }

  public function update()
  {
    $user = $this->Auth->user();
    $data = $this->request->getData();
    if (!empty($data['sms_available_time_start']) and !empty($data['sms_available_time_end']) and strtotime($data['sms_available_time_start']) > strtotime($data['sms_available_time_end'])) {
      return $this->setJsonResponse(array('success' => false, 'error' => 'End time must be later than start time.'));
    }
    $this->PartnerUsers->updatePartnerUser($user['partner_user_id'], $data);
    $this->Auth->setUser($this->PartnerUsers->getPartnerUser(array('partner_user_id' => $user['partner_user_id']))); // Update's their "session"
    return $this->setJsonResponse(array('success' => true, 'sent' => $data));
  }

  public function verifyEmail($encodedEmail = null)
  {
    $email = base64_decode($encodedEmail);

    $response = $this->PartnerUsers->verifyPartnerUserEmail($email);

    if ($this->Auth->user()) {
      $url = $response['success']
        ? '/account?success=' . "Email Address verified successfully"
        : '/account?error=' . $response['error'];
    } else {
      $url = $response['success']
        ? '/login?success=' . "Email Address verified successfully"
        : '/login?error=' . $response['error'];
    }
    $this->redirect($url);
  }

  public function resendVerifyEmail()
  {
    $user = $this->Auth->user();
    try {
      if (!empty($user['email_verified']))
        throw new \Exception("Email Address has been verified before");

      $this->PartnerUsers->partner_notifications->sendPartnerNotifications($user['partner_id'], 'BrokerVerifyEmail', false, array(), $user['partner_user_id']);
    } catch (\Exception $e) {
      $this->message('error', $e->getMessage());
    }
    $this->redirect('/account?verifyEmailSent=true');
  }

  public function backToStaff($partnerUserId)
  {
    $originUrl = $this->request->getQuery('origin_url');
    if (empty($originUrl))
      return $this->redirect('/partners/dashboard');

    $newUser = $this->PartnerUsers->getPartnerUser(array('partner_user_id' => $partnerUserId));
    if (!$newUser)
      return $this->redirect('/partners/dashboard');

    unset($newUser['password']);

    $this->Auth->setUser($newUser);
    $token = JWT::encode(
      [
        'sub' => $newUser['email'],
        'exp' => (time() + 86400)
      ],
      Security::salt()
    );

    $this->setCookieSameSite('auth_token', $token, 0, "/", "." . $_SERVER['SERVER_NAME'], getenv('LEND_ENV') > 0, true, (getenv('LEND_ENV') > 0 ? "None" : false));

    return $this->redirect($originUrl);
  }

  public function resetDashboardCookies()
  {
    // reset date to view this month leads & reset filters
    if ($this->Cookie->read('Dashboard')) {
      if (in_array('Attempting', $this->Cookie->read('Dashboard')))
        $this->Cookie->delete('Dashboard.Leads');
      if (in_array('Pending', $this->Cookie->read('Dashboard')))
        $this->Cookie->delete('Dashboard.Pending');
      if (in_array('Attempting', $this->Cookie->read('Dashboard')))
        $this->Cookie->delete('Dashboard.Attempting');
      if (in_array('Attempting', $this->Cookie->read('Dashboard')))
        $this->Cookie->delete('Dashboard.In Progress');
      if (in_array('Attempting', $this->Cookie->read('Dashboard')))
        $this->Cookie->delete('Dashboard.Rejected');
      if (in_array('Attempting', $this->Cookie->read('Dashboard')))
        $this->Cookie->delete('Dashboard.Settled');
    }
  }

  public function callMePrompt()
  {
    $partner_user_id = $this->Auth->user('partner_user_id');
    $data = $this->request->getData();
    // Update partner_users.call_me_prompt to 1
    if (!$this->Auth->user('impersonation'))
      $this->PartnerUsers->updatePartnerUser($partner_user_id, ['call_me_prompt' => 1]);

    $this->set('dont_show_checkbox', $data['dont_show_checkbox']);
  }

  public function callMePromptAgree()
  {
    $partner_user_id = $this->Auth->user('partner_user_id');
    $this->PartnerUsers->updatePartnerUser($partner_user_id, ['call_me_prompt_agreed' => date('Y-m-d H:i:s')]);
    return $this->setJsonResponse(['success' => true]);
  }

  public function accregister()
  {

    if ($this->request->is('post')) {
      $fields = [];
      $pv = new PartnerValidation;
      $data = $this->request->getData();

      $partnerUserId = $this->Cookie->read('partner_user_id');
      $partnerUser = $this->PartnerUsers->getPartnerUser(array('partner_user_id' => $partnerUserId));
      $partner = $this->loadModel('Partners')->getPartner(array('partner_id' => $partnerUser['partner_id']));
      $businessActivities = $this->loadModel('PartnerProductTypes')->getPartnerProductTypes(array('active' => 1), false);
      //Structure Primary Business Activities
      $fields['primary_business_activities'] = $this->_formatPba($partnerUser['business_activities'], $businessActivities);
      $fields['partner_user'] = $partnerUser;
      $fields['partner'] = $partner;

      $confirmations = $pv->validateConfirmations($data['confirm_list']); // validate confirmation check boxes
      $fields = $this->_processConfirmList($data['confirm_list'], $fields); // checkbox questions

      // NOTE:: commenting out for reducing usage of Docusign for now.
      // if($confirmations){
      //     $params = $this->_formatDataForDocusign($fields);
      //     $sign = new SignatureService('new_envelope');
      //     $newEnvelope = $sign->callService($params);
      // }

      $this->PartnerUsers->updatePartnerUser((int)$partnerUser['partner_user_id'], $fields['partner_user']);

      // if($confirmations){
      //     // Notify us:
      //       $this->loadModel('Partners')->postToSlack(":heavy_exclamation_mark: *We currently have a partner who has partially completed onboarding.*\nThis partner has been sent the docusign link to complete.\n*Name is*: " . $partnerUser['name'] . ", \n*Email is*: " . $partnerUser['email'] . ", \n*Phone is*: " . $partnerUser['mobile'] . ", \n*Partner Id*: " . $partnerUser['partner_id'] . ".",
      //         (getenv('REGION', true) === 'au' ? 'partner_verification' : 'partner_verification_nz'));

      //     $this->setJsonResponse(['success'=>true, 'state'=>1]);
      // }else{
      // Notify us:
      $this->loadModel('Partners')->postToSlack(
        ":heavy_exclamation_mark: *We currently have a partner completed onboarding.*\nPlease call this client to verify their history...\n*Name is*: " . $partnerUser['name'] . ", \n*Email is*: " . $partnerUser['email'] . ", \n*Phone is*: " . $partnerUser['mobile'] . ", \n*Partner Id*: " . $partnerUser['partner_id'] . ".",
        (getenv('REGION', true) === 'au' ? 'partner_verification' : 'partner_verification_nz')
      );

      $this->setJsonResponse(['success' => true, 'state' => 2]);
      // }
    } else {
      $this->setJsonResponse(['success' => false]);
    }
  }



  public function setConsentDecStatus()
  {
    $partnerUser = $this->Auth->identify();
    $data = $this->request->getData();
    $partnerUserId = (int)$partnerUser['partner_user_id'];
    $status['show_full_app_consent'] = ($data['status'] === 'true') ? 1 : 0;

    $this->PartnerUsers->updatePartnerUser($partnerUserId, $status);
    return $this->setJsonResponse(['success' => true]);
  }

  public function updateSmsCredentials()
  {
    $data = $this->request->getData();
    if (getenv('LEND_ENV') == 0) {
      $user = $this->Auth->user();
    } else {
      $user = $this->Auth->identify();
    }
    if ($data['sms_credentials']['api_key'] === '************') {
      $existingCredentials = TableRegistry::getTableLocator()
        ->get('PartnerExtraCredentialsEntity')
        ->getCredentialsDecrypted($user['partner_id'], 'burstSms');
      if ($existingCredentials) {
        $data['sms_credentials']['api_key'] = $existingCredentials['credentials']->api_key;
        $data['sms_credentials']['secret'] = $existingCredentials['credentials']->secret;
      } else {
        unset($data['sms_credentials']['api_key']);
        unset($data['sms_credentials']['secret']);
      }
    }
    $credentialsEntity = TableRegistry::getTableLocator()
      ->get('PartnerExtraCredentialsEntity')
      ->createOrUpdateCredentials($user['partner_id'], 'burstSms', $data['sms_credentials']);

    return $this->setJsonResponse(['success' => true]);
  }

  public function updateEquifaxCredentials()
  {
    $data = $this->request->getData();
    if (getenv('LEND_ENV') == 0) {
      $user = $this->Auth->user();
    } else {
      $user = $this->Auth->identify();
    }
    $credentialsEntity = TableRegistry::getTableLocator()
      ->get('PartnerExtraCredentialsEntity')
      ->createOrUpdateCredentials($user['partner_id'], 'equifax', $data['equifax_credentials']);

    return $this->setJsonResponse(['success' => true]);
  }


  public function alertNewAggregator()
  {

    $data = $this->request->getData();
    if (getenv('LEND_ENV') == 0) {
      $user = $this->Auth->user();
    } else {
      $user = $this->Auth->identify();
    }
    $message = ":warning: parnter ID: *" . $user['partner_id'] . "* has requested that we retrieve their Wisr company-partner-id for their aggregator *" . $data['aggregator_name'] . "* Please action asap.";
    TableRegistry::getTableLocator()->get('App')->postToSlack($message, "send_errors");
    return $this->setJsonResponse(['success' => true]);
  }

  public function updateSmtpCredentials()
  {

    $data = $this->request->getData();
    if (getenv('LEND_ENV') == 0) {
      $user = $this->Auth->user();
    } else {
      $user = $this->Auth->identify();
    }

    $twofaTable = TableRegistry::getTableLocator()->get('PartnerTwofaCodes');
    $twofaEntity = $twofaTable->find('all', [
      'conditions' => [
        'partner_user_id' => $user['partner_user_id'],
        'verification_type' => 'smtp',
        'verified' => false
      ],
      'order' => ['id' => 'DESC']
    ])->first();

    $now = new \DateTime();
    if ($now > $twofaEntity->expiration_time) {
      return $this->setJsonResponse(['success' => false, 'error' => 'Verification code is incorrect']);
    }

    if ($twofaEntity->code == $data['smtp']['verification_code']) {
      $twofaEntity->verified = true;
      $twofaTable->save($twofaEntity);
      return $this->setJsonResponse(['success' => true]);
    } else {
      return $this->setJsonResponse(['success' => false, 'error' => 'Verification code is incorrect']);
    }
  }

  public function verifySmtpCredentials()
  {
    $data = $this->request->getData();
    if (getenv('LEND_ENV') == 0) {
      $user = $this->Auth->user();
    } else {
      $user = $this->Auth->identify();
    }
    $credentialsEntity = TableRegistry::getTableLocator()
      ->get('PartnerExtraCredentialsEntity')
      ->createOrUpdateCredentials($user['partner_id'], 'smtp', $data['smtp'], $user['partner_user_id']);

    $verificationCode = rand(100000, 999999);

    // insert verification code into
    $twofaTable = TableRegistry::getTableLocator()->get('PartnerTwofaCodes');
    $twofaEntity = $twofaTable->newEntity([
      'partner_user_id' => $user['partner_user_id'],
      'verification_type' => 'smtp',
      'code' => $verificationCode,
      'expiration_time' => new \DateTime('+10 minutes'),
      'verified' => false,
      'is_sent' => false,
    ]);
    $twofaTable->save($twofaEntity);

    $notification = $this->loadModel('PartnerNotifications')->sendPartnerNotifications(
      $user['partner_id'],
      'UpdateSMTP2FA',
      false,
      ['partner_user_name' => $user['name'], 'smtp_verification_code' => $verificationCode],
      $user['partner_user_id']
    );



    Log::write('debug', " notification " . json_encode($notification));
    Log::write('debug', " sent message " . $notification['successfully_sent']);

    if ($notification[0]['successfully_sent'] === false) {
      $error = '';
      if (!empty($notification[0]['lastResponse'])) {
        foreach ($notification[0]['lastResponse'] as $response) {
          if (!empty($response['message'])) {
            $error .= $response['message'] . ' ';
          }
        }
      }
      $error = trim($error) ?: 'Failed to send verification code';

      $twofaEntity->api_error = $error;
      $twofaTable->save($twofaEntity);

      return $this->setJsonResponse(['success' => false, 'error' => $error]);
    }

    $twofaEntity->is_sent = true;
    $twofaTable->save($twofaEntity);

    return $this->setJsonResponse(['success' => true]);
  }

  public function resendVerificationCode()
  {
    try {
      $user = $this->Auth->user();
      $partnerUserId = $user['partner_user_id'];
      $twofaTable = TableRegistry::getTableLocator()->get('PartnerTwofaCodes');

      $newVerificationCode = random_int(100000, 999999); // More secure random number

      $twofaEntity = $twofaTable->newEntity([
        'partner_user_id' => $partnerUserId,
        'verification_type' => 'smtp',
        'code' => $newVerificationCode,
        'expiration_time' => new \DateTime('+10 minutes'),
        'verified' => false,
        'is_sent' => false,
      ]);

      if (!$twofaTable->save($twofaEntity)) {
        throw new \Exception("Failed to save the new verification code.");
      }

      $notification = $this->loadModel('PartnerNotifications')->sendPartnerNotifications(
        $user['partner_id'],
        'UpdateSMTP2FA',
        false,
        ['partner_user_name' => $user['name'], 'smtp_verification_code' => $newVerificationCode],
        $user['partner_user_id']
      );

      if ($notification[0]['successfully_sent'] === false) {
        throw new \Exception("Failed to send the verification code.");
      } else {
        $twofaEntity->is_sent = true;
        $twofaTable->save($twofaEntity);
      }

      return $this->setJsonResponse(['success' => true, 'message' => 'Verification code sent successfully.']);
    } catch (\Exception $e) {
      return $this->setJsonResponse(['success' => false, 'error' => $e->getMessage()]);
    }
  }

  public function getExtraCredentialsApi()
  {
    try {
      if (!$this->request->is('post')) {
        return $this->setJsonResponse(['success' => false]);
      }
      $data = $this->request->getData();
      $signature = $this->request->getQuery('signature');
      if ($data['type'] == 'equifax') {
        $crb = new EquifaxService("Commercial Apply");
        $expectedSignature = $crb->generateSignature($data);
        if ($expectedSignature !== $signature) {
          throw new \Exception("Invalid Signature.");
        }
        $credentials = TableRegistry::getTableLocator()->get('PartnerExtraCredentialsEntity')
          ->getCredentialsDecrypted($data['partner_id'], $data['type']);
      } else if ($data['type'] == 'smtp') {
        $expectedSignature = base64_encode(hash_hmac('sha256', json_encode($data, JSON_UNESCAPED_SLASHES) . getenv('EMAIL_SERVICE_SECRET'), getenv('EMAIL_SERVICE_SECRET')));
        if ($expectedSignature !== $signature) {
          throw new \Exception("Invalid Signature.");
        }
        $credentials = TableRegistry::getTableLocator()->get('PartnerExtraCredentialsEntity')
          ->getCredentialsDecrypted($data['partner_user_id'], $data['type']);
      } else if ($data['type'] == 'burstSms') {
        $expectedSignature = base64_encode(hash_hmac('sha256', json_encode($data, JSON_UNESCAPED_SLASHES) . getenv('EMAIL_SERVICE_SECRET'), getenv('EMAIL_SERVICE_SECRET')));
        if ($expectedSignature !== $signature) {
          throw new \Exception("Invalid Signature.");
        }
        $credentials['credentials'] = TableRegistry::getTableLocator()->get('PartnerExtraCredentialsEntity')
          ->getCredentialsDecrypted($data['partner_user_id'], $data['type'])->allCredentialsArray();

      } else {
        throw new \Exception("Invalid Type.");
      }

//      Log::write('debug', $credentials);

      return $this->setJsonResponse(['success' => true, 'credentials' => $credentials]);
    } catch (\Throwable $th) {
      return $this->setJsonResponse(['success' => false, 'error' => $th->getMessage()]);
    }
  }

  public function token()
  {
    $data = $this->request->getData();
    $user = $this->Auth->user();
    $token = JWT::encode(
      [
        'sub' => 'Applicant',
        'exp' => (time() + 86400),
        'user' => $user,
      ],
      Security::salt()
    );
    return $this->setJsonResponse(['success' => true, 'access_token' => $token]);
  }

  public function info()
  {
    if (!$this->request->is('post')) {
      return $this->setJsonResponse(['success' => false]);
    }
    if (getenv('LEND_ENV') == 0) {
      $user = $this->Auth->user();
    } else {
      $user = $this->Auth->identify();
    }
    $data = $this->request->getData();
    if ($data['email'] != $user['email']) {
      return $this->setJsonResponse(['success' => false, 'data' => $data, 'user' => $user]);
    }
    $info = $this->PartnerUsers->getUserInfoForSignature($user);
    return $this->setJsonResponse(['success' => true, 'user' => $info]);
  }

  public function lendSignature()
  {
    $auth_token = $_COOKIE['auth_token'];
    $this->redirect(getEnv("DOMAIN_LEND_SIGNATURE")."/lend-signin/?token=${auth_token}");
  }

  public function lendSignatureOld(){
    $auth_token = $_COOKIE['auth_token'];
    $this->redirect(getEnv("DOMAIN_LEND_SIGNATURE")."/api/auth/signin?callbackUrl=%2Fmanage%2Ftemplate&lend_url=true&token=${auth_token}");
  }
  public function sendDashboardNotice()
  {
    try {
        $post = $this->request->getData();

        $signature = $this->request->getQuery('auth_sig');
        if (!$signature) {
          Log::error('No signature provided');
          return $this->setJsonResponse(['success' => false, 'message' => 'Unauthorized - No signature provided']);
        }
        $auth = new \App\Lend\LendInternalAuth();
        if (!$auth->checkSignature($signature, $post)) {
          Log::error('Invalid signature');
          return $this->setJsonResponse(['success' => false, 'message' => 'Unauthorized - Invalid signature']);
        }
        if (empty($post['notice_ref'])) {
            throw new \Exception("Notice reference is required");
        }
        $PartnerNoticesEntity = TableRegistry::getTableLocator()->get('PartnerNoticesEntity');
        $notice = $PartnerNoticesEntity->find()
            ->where(['notice_ref' => $post['notice_ref']])
            ->first();
        
        if (!$notice) {
            throw new \Exception("Notice not found with reference: " . $post['notice_ref']);
        }
        $PartnerUserEntity = TableRegistry::getTableLocator()->get('PartnerUserEntity');
    
        // Find partner users who haven't dismissed the notice
        $partnerUsers = $PartnerUserEntity
            ->find()
            ->contain(['PartnerEntity'])
            ->select([
                'partner_user_id',
                'partner_id',
                'email',
                'name',
                'partner_name' => 'PartnerEntity.company_name'
            ])
            ->where([
                'PartnerUserEntity.active' => 1,
                'PartnerEntity.active' => 1,
                'PartnerUserEntity.notification_recipient' => 1,
                'PartnerUserEntity.email_verified' => 1,
                'PartnerEntity.status_system' => 'manual'
            ])
            ->notMatching('PartnerNoticeDismissedEntity', function ($q) use ($notice) {
                return $q->where(['PartnerNoticeDismissedEntity.partner_notice_id' => $notice->partner_notice_id]);
            })
            ->toArray();

        // Set longer execution time for large batches
        set_time_limit(3600); // 1 hour

        // Process in batches with rate limiting
        $batchSize = 100; // SendGrid recommends 100 emails per batch
        $chunks = array_chunk($partnerUsers, $batchSize);
        
        $successCount = 0;
        $failureCount = 0;
        $errors = [];
        $allFailed = true;

        // After getting users but before chunking
        Log::write('debug', "Total users before chunking: " . count($partnerUsers));

        // After chunking
        $chunks = array_chunk($partnerUsers, $batchSize);
        Log::write('debug', "Created " . count($chunks) . " chunks of size " . $batchSize);

       // Before processing chunks
        foreach ($chunks as $index => $chunk) {
            Log::write('debug', "Processing chunk " . ($index + 1) . " with " . count($chunk) . " users");
            
            foreach ($chunk as $user) {
                Log::write('debug', "Processing email for user: " . $user['email']);
                try {
                    $result = $this->loadModel('PartnerNotifications')->sendPartnerNotifications(
                        $user['partner_id'],
                        'PlatformNotice',
                        false,
                        [
                            'partner_user_name' => $user['name'],
                            'platform_notice' => !empty($notice['html']) ? $notice['html'] : "",
                            'platform_title' => !empty($notice['title']) ? "<span'> Title: ". $notice['title'] . "</span>" : "",
                            'tag_category' => !empty($notice['tags']['categories']) ? "<div style='margin-bottom: 5px;'><span style='background-color: #e8f4f8; color: #2c5282; padding: 5px 10px; border-radius: 15px; font-size: 14px;'>" . implode('</span> <span style="background-color: #e8f4f8; color: #2c5282; padding: 5px 10px; border-radius: 15px; font-size: 14px;">', $notice['tags']['categories']) . "</span></div>" : "",
                        ],
                        $user['partner_user_id']
                    );
                    
                    $successCount++;
                    $allFailed = false;
                    
                    usleep(100000); // 100ms delay
                    
                } catch (\Exception $e) {
                    $failureCount++;
                    $errors[] = [
                        'user_id' => $user['partner_user_id'],
                        'error' => $e->getMessage()
                    ];
                    Log::error('Failed to send notice to user ' . $user['partner_user_id'] . ': ' . $e->getMessage());
                }
            }
            
            if ($index < count($chunks) - 1) {
                sleep(1); // 1 second delay between batches
            }
        }

        // Update notice status based on results
        $updateData = [
            'email_sent' => $allFailed ? 'FAILED' : 'SUCCESS',
            'alert_sent_at' => date('Y-m-d H:i:s')
        ];
        
        TableRegistry::getTableLocator()->get('PartnerNoticesEntity')
            ->updateAll(
                $updateData,
                ['notice_ref' => $post['notice_ref']]
            );

        return $this->setJsonResponse([
            'success' => true,
            'message' => 'Notifications processed',
            'total_processed' => count($partnerUsers),
            'successful' => $successCount,
            'failed' => $failureCount,
            'status' => $updateData['email_sent'],
            'errors' => $errors
        ]);

    } catch (\Exception $e) {
        // Update notice as failed if there's an error
        TableRegistry::getTableLocator()->get('PartnerNoticesEntity')->updateAll(
            [
                'email_sent' => 'FAILED',
                'alert_sent_at' => date('Y-m-d H:i:s')
            ],
            ['notice_ref' => $post['notice_ref']]
        );

        Log::error('Error processing notice: ' . $e->getMessage());
        return $this->setJsonResponse([
            'success' => false, 
            'message' => 'Error processing notice: ' . $e->getMessage()
        ]);
    }
  }
}
