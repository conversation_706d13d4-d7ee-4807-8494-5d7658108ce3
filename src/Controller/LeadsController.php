<?php
namespace App\Controller;

use App\Model\Table\LeadsTable;
use Cake\Core\Configure;
use Cake\Cache\Cache;
use Cake\Utility\Security;
use Cake\Http\Client;
use Cake\Log\Log;
use Cake\ORM\TableRegistry;
use App\Lend\Config;
use App\Lend\LendInternalAuth;
use App\Lend\MakeDecision;
use App\Lend\MakeDecisionV2;
use App\Lend\LendSize;
use App\Lend\LendStatus;
use App\Lend\LeadValidation;
use LeadApis\Controller\LeadApisController;
use App\Lend\LendMpdf;
use App\Lend\CheckLeadCompletion;
use Hashids\Hashids;
use Cake\Event\Event;
use Firebase\JWT\JWT;   
use Cake\Filesystem\File;
use Cake\Datasource\ConnectionManager;
use App\Lend\KanbanHelper;
use App\Lend\SocketHelper;

class LeadsController extends AppController {
    protected $config;
    protected $internal_auth;

    private $_pageHead2Array=array(
        'Leads'=>'All Leads',
        'Pending'=>'Pending Leads',
        'Attempting'=>'Attempting Leads',
        'In Progress'=>'In Progress Leads',
        'Rejected'=>'Rejected Leads',
        'Settled'=>'Settled Leads',
        'Archived'=>'Archived Leads'
    );

    public function initialize() {
      parent::initialize();
      $this->Auth->allow([
        'checkPercentage', 
        'checkPercentageBulk', 
        'checkExistsLead', 
        'saveIt', 
        'affiliateSubmission', 
        'updateLendSizeEstimate', 
        'revokeLender', 
        'fullSearch', 
        'requestCallback', 
        'generateLeadPdf', 
        'viewQuote'
      ]);
      $this->config = new Config;
      $this->internal_auth = new LendInternalAuth;
    }

    public function generateLeadPdf() {
      $post = $this->request->getData();
      LendMpdf::generatePdfFromHtml($post['html'],true,$post['filename'],$post['css']);
    }
    /*
    check the parameters if it contains start_date, end_date, range
    store in cookie where possible,
    if already exists in cookie, overwrite them and return
    */
    private function __checkDateRangeInCookie($params)
    {
      // if (isset($params['start_date'], $params['end_date'], $params['range'])) {
      //   $this->Cookie->write('Dashboard.dateRange',
      //             array('start_date' => $params['start_date'],
      //                   'end_date' => $params['end_date'],
      //                   'range' => $params['range']));
      // } elseif ($this->Cookie->check('Dashboard.dateRange')) {
      //   $params = array_merge($params,
      //               $this->Cookie->read('Dashboard.dateRange'));
      // }
      $params = $this->checkDateRangeInCookie($params); // Now using a global timeframe


      $status = !empty($params['status']) ? $params['status'] : 'Leads';
      if (isset($params['filterType']) || isset($params['filterStatuses']) || isset($params['filterLenders']) || isset($params['filterAliases'])) {
        $this->Cookie->write('Dashboard.' . $status . '.filters',
                  array('filterType' => $params['filterType'],
                        'filterStatuses' => $params['filterStatuses'],
                        'filterLenders' => $params['filterLenders'],
                        'filterAliases' => $params['filterAliases'],
                  ));
      } elseif ($this->Cookie->check('Dashboard.' . $status . '.filters')) {
        $params = array_merge($params,
                    $this->Cookie->read('Dashboard.' . $status . '.filters'));
      }
      return $params;
    }





    private function _getBulkPercentages($lead) {
        $percentages = [];
        $lead['lead_owner'] = $lead['lead_owners'];
        $ownerDetails = (new LeadApisController)->_getOwnersMoreDetails($lead);
        foreach($ownerDetails as $detail) {
          $percentages[$detail['owner_id']] = $detail['percent_complete'];
        }
        return $percentages;
    }

    public function checkPercentage($includingBS = false) {
      if(!$this->request->is('post')) return $this->setJsonResponse(array('success'=>false, 'message'=>'incorrect method'));
      ConnectionManager::alias('reader_db', 'default');
      $post = $this->request->getData();
      $bulkPercentages = [];
      $owner_ids = [];

      if(!empty($post['lead_owners'])){
        $bulkPercentages = $this->_getBulkPercentages($post);
        foreach($post['lead_owners'] as $o){
          if(!empty($o['owner_id']))
            $owner_ids[] = $o['owner_id'];
          elseif(!empty($o['owner_ref']))
            $owner_ids[] = $this->internal_auth->unhashLeadId($o['owner_ref']);
        }
        unset($post['lead_owners']);
      }

      if(!empty($post['lead']['lead_ref'])) $post['lead']['lead_id'] = $this->internal_auth->unhashLeadId($post['lead']['lead_ref']);
      //coming from bs only form

      $check_lead_completion = new CheckLeadCompletion;
      $service_result = $check_lead_completion->checkPercentage(['lead_id'=>$post['lead']['lead_id'], 'owner_ids'=>$owner_ids]);

      if (!empty($post['lead']['lead_id']) && !empty($post['lead'])) {
        //overwrite, get all details from db?
        // $post = $this->Leads->getLeadDetails($post['lead']['lead_id']);
        $percentage = !empty($includingBS)
                    ? $this->Leads->checkPercentage($post, true)
                    :$this->Leads->checkPercentage($post);
        $check_lead_completion->compare($service_result, $percentage, $bulkPercentages);
        return $this->setJsonResponse(array('success'=>true, 'percentage'=>$percentage, 'bulk_percentages'=>$bulkPercentages, 'sent'=>$post));
      }

      $data = $this->Leads->formatLeadData($post);
      $loggedUser = $this->Auth->user();

      $percentage = !empty($includingBS)
                  ? $this->Leads->checkPercentage($data, true)
                  :$this->Leads->checkPercentage($data);
      $check_lead_completion->compare($service_result, $percentage, $bulkPercentages);
      ConnectionManager::dropAlias('default');
      return $this->setJsonResponse(array('success'=>true, 'percentage'=>$percentage, 'bulk_percentages'=>$bulkPercentages, 'sent'=>$data));
    }

    public function checkPercentageBulk(){
      try{
        if(!$this->request->is('post')) throw new \Exception('incorrect method');
        $post = $this->request->getData();
        if(empty($post['lead_ids'])) throw new \Exception('Need at least one lead ID');

        $result = [];
        foreach($post['lead_ids'] as $lead_id){
          $post = $this->Leads->getLeadDetails($lead_id);
          $percentage = $this->Leads->checkPercentage($post);
          $result[] = array('lead_id'=>$lead_id, 'percentage'=>$percentage['current_percentage']);
        }

        return $this->setJsonResponse(array('success'=>true, 'result'=>$result));
      }catch(\Exception $e){
        return $this->setJsonResponse(array('success'=>false, 'message'=>$e->getMessage()));
      }
    }

    protected function _leadValidationToSendLead($data, $checkContactOnly = true)
    {
      // Check validation
      $leadValidation = new LeadValidation;
      //the required fields must be same as the ones on ``**/partners/new-affiliate-lead` page

      //fields below are always required
      $requiredFields['lead_owner'] = ['first_name', 'last_name', 'email'];

      if (!$checkContactOnly) {
        $requiredFields['lead'] = ['amount_requested', 'company_registration_date', 'sales_monthly', 'industry_id', 'organisation_name', 'product_type_id'];
        if ($data['lead']['product_type_id'] == 10) {  //if Equipment finance
          //we need to validate ABN
          $requiredFields['abnlookup'] = array();
          $requiredFields['lead']['equipment_id'] = 'equipment_id';
          $requiredFields['lead']['equipment_details'] = 'equipment_details';
          //we need the eqipment fields to be filled
          switch ($data['lead']['equipment_id']) {
            case '':
              unset($data['lead']['equipment_condition']);
              break;
            default:
              $requiredFields['lead']['equipment_condition'] = 'equipment_condition';
              break;
          }
          if(!empty($data['lead']['equipment_found'])){
            $requiredFields['lead']['equipment_source'] = 'equipment_source';
          }

        }

        // check Property Value and Mortgage Balance
        if(!empty($data['lead_owner']['home_owner']) AND !empty($data['lead']['product_type_id']) AND ($data['lead']['product_type_id'] == '2' OR $data['lead']['product_type_id'] == '7' OR $data['product_type']['sub_product'] == '7')){
          $requiredFields['lead_owner'][] = 'estimated_value';
          $requiredFields['lead_owner'][] = 'remaining_debt';
        }
      }

      //abn or organisation_name  must not be empty, do that validation in class LeadValidation
//      $requiredFields['abnlookup'] = array();

      //mobile or phone must not be empty
      if (!empty($data['lead_owner']['mobile']))
        $requiredFields['lead_owner'][] = 'mobile';
      else if (!empty($data['lead_owner']['phone']))
        $requiredFields['lead_owner'][] = 'phone';
      else
        $requiredFields['lead_owner'][] = 'mobile';
      $leadValidation->setRequiredFields($requiredFields);

      $errors = $leadValidation->validate($this->Leads->formatLeadData($data, array('dont_toggle_phonenumbers'=>true))); // Check validation

      return $errors;
    }

    public function saveIt() {
      $user = $this->Auth->identify();
      $data  = $this->request->getData();
      $partnerId = !empty($data['partner']['partner_id']) ? (int)$data['partner']['partner_id'] : null;

      if(!$this->request->is('post'))
        return $this->setJsonResponse(array('success'=>false, 'message'=>'incorrect method'));

      if(isset($data['lead']['is_abn_unknown'])) {
        $data['abnlookup'] = array_fill_keys(array_keys($data['abnlookup']), null); //make sure values are null
      }

      if(!empty($data['lead']['lead_ref']))   $data['lead']['lead_id'] = $this->internal_auth->unhashLeadId($data['lead']['lead_ref']);

      $data['lead']['partner_alias_id'] = !empty($data['alias_to_lookup']) ? $this->loadModel('PartnerAliases')->getPartnerAlias(['alias_ref'=>$data['alias_to_lookup']])['partner_alias_id'] : NULL;

      $data['lead']['call_me_first'] = !empty($data['lead']['call_me_first']) ? '1' : '0';
      $data['lead_owner']['home_owner'] = !empty($data['lead_owner']['home_owner']) ? '1' : '0';
      $data['lead']['equipment_found'] = !empty($data['lead']['equipment_found']) ? '1' : '0';

      if (!empty($partnerId) && $partnerId === 2) //demo account
        return $this->setJsonResponse(array('success'=>false, 'message'=>'Not allowed to create leads or update leads for DEMO account.', 'sent'=>$data));

      $errors = $this->_leadValidationToSendLead($data);
      if (!empty($data['lead_owner'])) {
        $leadOwnerMobile = !empty($data['lead_owner']['mobile']) ? preg_replace('/\s+/', '', $data['lead_owner']['mobile']) : '';
        if (!empty($leadOwnerMobile) && ($leadOwnerMobile == $user['phone'] || $leadOwnerMobile == $user['mobile']))
          array_push($errors, ['field'=>'owner[mobile]', 'error'=>"You've added your own phone number to the application"]);
      }

      if(!empty($errors)){
        //use different key 'errors' to indicate fields validation
        return $this->setJsonResponse(array('success'=>false, 'errors'=>$errors, 'sent'=>$data));
      }

      if(!empty($data['lead']['sub_product_type_id'])){
        $data['lead']['product_type_id'] =  $data['lead']['sub_product_type_id'];
        unset($data['lead']['sub_product_type_id']);
      }

      list($lead, $isNew, $data) = $this->_saveLead($data);

      if (empty($lead) && empty($data)) return;  //possibly it has already returned in function $this->_saveLead()
      return $this->setJsonResponse(array('success'=>true, 'data'=>$lead, 'is_new'=>$isNew, 'sent'=>$data));
    }

    protected function _saveLead($data)
    {
      $partnerId = !empty($data['partner']['partner_id']) ? (int)$data['partner']['partner_id'] : null;
      $partnerUserId = !empty($data['partner']['partner_user_id']) ? (int)$data['partner']['partner_user_id'] : null;

      //we've switched from using lead_id in the view. Make sure downstream isn't affected
      if(empty($data['lead']['lead_id']) && !empty($data['lead']['lead_ref'])){
        $data['lead']['lead_id'] = $this->Leads->leadIdFromRef($data['lead']['lead_ref']);
      }

      $data['lead']['is_abn_unknown'] = !empty($data['lead']['is_abn_unknown']) ? "1" : "0";
      $formattedData = $this->Leads->formatLeadData($data);

      //to prevent client to repeat their lead
      if (empty($partnerId) && !empty($formattedData['lead']['lead_id']) ) {
          //client editing and saving the lead,
          $currentLead = $this->Leads->getLead(array('lead_id' => $formattedData['lead']['lead_id']));
          if(empty($formattedData['lead']['product_type_id']))
            $formattedData['lead']['product_type_id'] = $currentLead['product_type_id'];

          if (!empty($currentLead['partner_id'])
        && false !== ($existingLead = $this->Leads->partnerSameOwnerLead($formattedData, $currentLead['partner_id'])) ) {
            $days = Configure::read('Lend.partner_honoured_lookup.days');
            $message = " <h3>Unable to save lead. </h3>"
                      . "<div>You have already submitted an application with those same details in the last $days days.</div>"
                      . "<div>&nbsp;<br></div>";

            return $this->setJsonResponse(array('success'=>false, 'message'=>$message, 'sent'=>$data));
          }
      }
      //check if the partner has created same lead before
      if (!empty($partnerId) AND false !== ($existingLead = $this->Leads->partnerSameOwnerLead($formattedData, $partnerId))) {
        $days = Configure::read('Lend.partner_honoured_lookup.days');
        $href = '/leads/view/' . $this->internal_auth->hashLeadId($existingLead['lead_id']);
        $message = " <h3>Unable to save lead. </h3>"
                  . "<div>You have already submitted this business within the last $days days, for the same product type. Please change the product type.</div>"
                  . "<div class='p-t-3'><a class='button' href='$href'>View Existing Lead</a></div>";

        return $this->setJsonResponse(array('success'=>false, 'message'=>$message, 'sent'=>$data));
      }

      // Update overdraft for each BS accounts
      if(!empty($data['bsa'])){
        $this->LoadModel('BankStatementsAccounts');
        if($data['lead']['overdraft_loc_limit'] == "false"){
          $data['lead']['overdraft_loc_limit'] = null;
          foreach($data['bsa'] as $bsa){
            $this->BankStatementsAccounts->updateBSAccount(['overdraft_limit'=>null], ['bsa_id'=>$bsa['bsa_id']]);
          }
        }
        else{
          $data['lead']['overdraft_loc_limit'] = 0;
          foreach($data['bsa'] as $bsa){
            $this->BankStatementsAccounts->updateBSAccount(['overdraft_limit'=>$bsa['overdraft_limit']], ['bsa_id'=>$bsa['bsa_id']]);
            $data['lead']['overdraft_loc_limit'] += floatval($bsa['overdraft_limit']);
          }
        }
      }

      // Decide new lead or exists lead
      if(empty($data['lead']['lead_id'])){

        $lead_id = $this->create($data);
        $isNew = true;

        //add new row into `partner_user_leads`
        $partnerUserLeadsTable = TableRegistry::getTableLocator()->get('PartnerUserLeadsEntity');
        $partnerUserLead = $partnerUserLeadsTable->newEntity([
          'partner_user_id' => $partnerUserId,
          'status' => 'ACCESS',
          'lead_id' => $lead_id
        ]);
        $partnerUserLeadsTable->save($partnerUserLead);

        // and we need to save a bg job
        if (!empty($data['lead']['bs_doc_id']))
          $this->LoadModel('BackgroundJobs')->addBackgroundJob(array(
                                  'lead_id'       => $lead_id,
                                  'job_type'      => 'brokerFlowDocIdRetrieval',
                                  'ref_id'        => $data['lead']['bs_doc_id'],
                                  'class_name'    => 'Bankstatements',
                                  'function_name' => 'brokerFlowDocIdRetrieval',
                                  'created'       => date('Y-m-d H:i:s', time()),
                                ));
      }else{
        $this->LoadModel('BackgroundJobs');
        $isNew = false;
        $original = $this->Leads->getLeadDetails($data['lead']['lead_id']);
        $newBsDocId = ($original['lead']['bs_doc_id']!=$data['lead']['bs_doc_id']);

        $check = $this->checkPermission($data['lead']['lead_id'], $original['lead'], true);
        if(!$check['success']) return $this->setJsonResponse($check);

        // If there's a new bs_doc_id, we need to do a few other things too...
        if ($newBsDocId) $data['lead']['bs_doc_retrieved'] = '0';

        //ignore overwriting Leads.statements_uploaded by the partner or the applicant
        if(array_key_exists('statements_uploaded', $data['lead'])) {
          unset($data['lead']['statements_uploaded']);
        }

        $data = $this->update($data);

        // and we need to save a bg job
        if ($newBsDocId) $this->BackgroundJobs->addBackgroundJob(array(
                                  'lead_id'       => $data['lead']['lead_id'],
                                  'job_type'      => 'brokerFlowDocIdRetrieval',
                                  'ref_id'        => $data['lead']['bs_doc_id'],
                                  'class_name'    => 'Bankstatements',
                                  'function_name' => 'brokerFlowDocIdRetrieval',
                                  'created'       => date('Y-m-d H:i:s', time()),
                                ));

        // If this application is updated by client, send notification to partner:
        $user = $this->Auth->identify();
        if(!$user){
          $ref_id = json_encode(array('partner_id'=>$original['lead']['partner_id'], 'lead_id'=>$data['lead']['lead_id'], 'lookup_code'=>'ClientUpdatedApp'));
          if(!$this->BackgroundJobs->getBackgroundJobs(array('lead_id'=>$data['lead']['lead_id'], 'job_type'=>'re_send_partner_notification', 'ref_id'=>$ref_id))){
            $this->BackgroundJobs->addBackgroundJob(array(
                                      'lead_id'       => $data['lead']['lead_id'],
                                      'job_type'      => 're_send_partner_notification',
                                      'ref_id'        => $ref_id,
                                      'class_name'    => 'Partners',
                                      'function_name' => 're_send_partner_notification',
                                      'created'       => date('Y-m-d H:i:s', time()),
                                    ));
          }
        }

          $this->LoadModel('PartnerLeadEdits')->saveAnEdit(
            array(
              'lead_id'=>$data['lead']['lead_id'],
              'partner_user_id'=>(!empty($user['partner_user_id'])?$user['partner_user_id']:null)
            ),
            $original,
            $data
          );


      }

      // Get ALL lead details, return lead details and $isNew, $data
      $leadid = !empty($data['lead']['lead_id']) ? $data['lead']['lead_id'] : $lead_id;
      return array($this->Leads->getLeadDetails($leadid), $isNew, $data);
    }

    public function edit($hashed_lead_id) {
      $this->newlead($hashed_lead_id);
      $this->viewBuilder()->setTemplate('newlead');
      $this->set('seo', array('meta' => array('title' => 'Edit Lead '.$hashed_lead_id.''.$this->lendconfig['nameTag'], 'og' => array('title' => 'Edit Lead '.$hashed_lead_id.''.$this->lendconfig['nameTag']))));

      // get clients
      $partnerId = $this->Auth->user('partner_id');
      $partnerUserId = $this->Auth->user('partner_user_id');
      if ($partnerId && $partnerUserId) {
        $haveClient = count($this->loadModel('PartnerClients')->getVisibleClients(array('partner_id'=>$partnerId, 'partner_user_id'=>$partnerUserId)));
        $this->set('client', $haveClient);
      }

      // generate beta link for user to jump into
      $beta_link = $this->generateBetaLink($hashed_lead_id);
      $this->set('beta_link', $beta_link);
    }

    public function newlead($hashed_lead_id=false)
    {
      if($hashed_lead_id)
        $lead_id = $this->internal_auth->unhashLeadId($hashed_lead_id);

      // Get partner Id
      $partner_id = (int)$this->Auth->user('partner_id');
      $this->set('partner_id', $partner_id);

      $topProductTypes = array();
      $subProductTypes = array();
      $partnerProductTypes = $this->loadModel('PartnerProductTypes')->getPartnerProductTypes(array('active' => 1), true);

      foreach ($partnerProductTypes as $partnerProductType) {
        if($partnerProductType['sub_product'] == 0)
          array_push($topProductTypes, $partnerProductType);
        else{
          if(array_key_exists(strval($partnerProductType['sub_product']), $subProductTypes))
            array_push($subProductTypes[strval($partnerProductType['sub_product'])], $partnerProductType);
          else
            $subProductTypes[strval($partnerProductType['sub_product'])] = array($partnerProductType);
        }
      }
      if ($partner_id) {
        $partner = $this->loadModel('Partners')->getpartner(['partner_id' => $partner_id]);
        // $showBrokerFlow = $this->LoadModel('Partners')->getPartnerShowBrokerFlow($partner_id);
        $this->set('showBrokerFlow', $partner['show_brokerflow']);
        $this->set('partnerCallMeFirst', $partner['call_me_first']);
        $this->set('partnerContactNumber', $this->getPartnerContactNumber($this->Auth->identify()));
        $this->set('partnersAliasRefs', $this->loadModel('PartnerAliases')->getPartnerAliases(['partner_id'=>$partner_id]));
        $this->set('isLendPartner', $this->checkLendPartner($partner_id));
        // $this->set('partnerProductTypes', $this->loadModel('PartnerProductTypes')->getPartnerProductTypes(array('active' => 1)));
      }

      if(!empty($lead_id)){
        // Get lead details
        $lead = $this->Leads->getLeadDetails($lead_id);

        // Check this lead is belong to this partner
        $check = $this->checkPermission($lead_id, $lead['lead']);
        if(!$check['success']) return $this->redirect('/partners/dashboard?error='.$check['message']);

        // Otherwise, set lead details & percentage:
        $current_industry = $this->config->getConfig('config_industries', array('industry_id'=>$lead['lead']['industry_id']));
        $loggedUser = $this->Auth->user();
        $percentage = empty($loggedUser)
                      ? $this->Leads->checkPercentage($lead, true)
                      : $this->Leads->checkPercentage($lead);


        $this->set('data', $lead);
        $this->set('percentage', $percentage);
        $this->set('current_industry', $current_industry);

        if (!empty($lead['abnlookup']['organisation_name']) &&
          strcasecmp($lead['lead']['organisation_name'], $lead['abnlookup']['organisation_name']))
          $this->set('manualFilledOrgName', true);

        // Check whether lead has been stopped sending
        $stopSending = $this->Leads->send_prevention->getSendPreventionWithUser(array('p.lead_id'=>$lead_id)) ? true : false;
        $this->set('stopSending', $stopSending);
      }

      $uppyMetaData = (new \App\Controller\PartnerLeadUploadsController)->getUppyMetaData($lead, $partner);
      $this->set('uppyMetaData', json_encode($uppyMetaData));

      // Set all configs
      $purpose = $this->config->getConfig('frm_purpose', ['status' => 1]);
      $frm_equipment = $this->config->getConfig('frm_equipment', ['status' => 1]);
      $loan_terms = $this->config->getConfig('frm_loan_terms', ['status' => 1]);
      $industries = $this->config->groupIndustry($this->config->getConfig('config_industries'));

      // Get average of DOB
      $avg_dob = $this->Leads->lead_owner->getAvgDob();
      $dob_start_year = date('Y', strtotime($avg_dob['dob']));
      $dob_start_month = date('m', strtotime($avg_dob['dob']));

      // Check already sent to lender && rejected or not
      if ($this->loadModel('Sales')->getSales(array('lead_id'=>$lead_id))
        && $this->loadModel('LendStatuses')->getLendStatusesOldFashion()[$lead['lead']['partner_status_id']]['groupName'] != 'Rejected'
        && $this->loadModel('LendStatuses')->getLendStatusesOldFashion()[$lead['lead']['partner_status_id']]['groupName'] != 'Pending'
        && $this->Auth->user('account_type') !== 'Lend Staff' ) {
        $this->set('readonly', 1);
      }
      //check cookie var if we need to show hint
      if (empty($_COOKIE['lend-send-to-lender-hint']) && !$this->request->getQuery('understood')) {
        $showHint = true;
      } else {
        $showHint = false;
        unset($_COOKIE['lend-send-to-lender-hint']);
        setcookie('lend-send-to-lender-hint', '1', time() + ********, '/');
      }

      // get clients
      $partnerId = $this->Auth->user('partner_id');
      $partnerUserId = $this->Auth->user('partner_user_id');
      $this->set('partner_user_id', $partnerUserId);
      if ($partnerId && $partnerUserId) {
        $haveClient = count($this->loadModel('PartnerClients')->getVisibleClients(array('partner_id'=>$partnerId, 'partner_user_id'=>$partnerUserId))) > 0;
        $this->set('have_client', $haveClient);
      }

      if(empty($lead['lead']['product_type_id']) || $lead['lead']['product_type_id'] != 10 ){
        $isEquipmentFinance = false;
      }else{
        $isEquipmentFinance = true;
      }

      $has_sub_type = false;
      if(!empty($lead['lead']['product_type_id'])){
        if(empty($this->loadModel('PartnerProductTypes')->getPartnerProductType(['product_type_id'=>$lead['lead']['product_type_id']]))){
          $has_sub_type = $this->loadModel('PartnerProductTypes')->getPartnerProductType(['product_type_id'=>$lead['lead']['product_type_id']], true)['sub_product'];
        }
      }

      $this->set('partnerProductTypes', $topProductTypes);
      $this->set('subProductTypes', $subProductTypes);
      $this->set('has_sub_type', $has_sub_type);
      $this->set('isEquipmentFinance', $isEquipmentFinance);
      $this->set('uppyConfiguration', json_encode(Configure::read('Lend.UploadManager')));
      $this->set('showHint', $showHint);
      $this->set('dob_start_year', $dob_start_year);
      $this->set('dob_start_month', $dob_start_month);
      $this->set('frm_equipment', $frm_equipment);
      $this->set('equipment_condition', ['New','Used','Other']);
      $this->set('equipment_source', $this->Leads->getAvailableEquipmentSource());
      $this->set('purpose', $purpose);
      $this->set('loan_terms', $loan_terms);
      $this->set('industries', $industries);
      $this->set('AU_states', Configure::read('Lend.AU_states'));
      $this->set('credit_history', Configure::read('Lend.credit_history'));
      $this->set('account', $this->Auth->user('account_type'));
      $this->set('lease_or_finance', $this->Leads->getLeaseOrFinance());
      $this->set('customer_type', $this->Leads->getCustomerType());
      $this->set('seo', array('meta' => array('title' => 'Add Lead'.$this->lendconfig['nameTag'], 'og' => array('title' => 'Add Lead'.$this->lendconfig['nameTag']))));
    }

    // Create new lead
    private function create($data){
      $user = $this->Auth->identify();
      $data = $this->Leads->formatLeadData($data);

      $partner_id = !empty($user) ? $user['partner_id'] : $data['partner']['partner_id'];
      // Create account level data:
      if (empty($data['lead']['account_id'])) {
        $accountTable = TableRegistry::getTableLocator()->get('PartnerAccountEntity');
        $accountPeopleTable = TableRegistry::getTableLocator()->get('PartnerAccountPeopleEntity');

        $account_data['partner_id'] = $partner_id;
        $account_data['partner_user_id'] = @$user['partner_user_id'];
        $account_data['abn'] = @$data['lead']['abn'];

        if (!empty($data['lead_owner'])) {
          $people_data = $data['lead_owner'];
          $people_data['is_main_point_of_contact'] = true;
          
          // generate conditions
          $conditions = [
            'PartnerAccountEntity.partner_id' => $partner_id,
            'PartnerAccountPeopleEntity.first_name' => $people_data['first_name'],
            'PartnerAccountPeopleEntity.last_name' => $people_data['last_name'],
            'PartnerAccountPeopleEntity.status' => 'active',
            'OR' => [
              'PartnerAccountPeopleEntity.email' => $people_data['email'],
              'PartnerAccountPeopleEntity.phone' => $people_data['phone'],
              'PartnerAccountPeopleEntity.mobile' => $people_data['mobile'],
              'PartnerAccountPeopleEntity.dob' => $people_data['dob'],
            ]
          ];
          // find matched people
          $person = $accountPeopleTable->find('all')
            ->where($conditions)
            ->contain(['PartnerAccountEntity'])
            ->order(['PartnerAccountPeopleEntity.id' => 'desc'])
            ->first();
          if (!empty($person)) {
            $people_data['id'] = $person->id;
          }

          $account_data['poc_people'] = $people_data;
        }
        $account_data['partner_account_meta'] = ['b_country' => 'AU'];

        $account = $accountTable->newEntity($account_data, [
          'associated' => ['PocPeople', 'PartnerAccountMetaEntity']
        ]);
        $accountTable->save($account);
        $data['lead']['account_id'] = $account->partner_account_id;
        $data['lead_owner']['partner_account_people_id'] = $account->poc_people->id;
      }

      // Insert into leads table
      $data['lead']['partner_id'] = $partner_id;
      $data['lead']['partner_status_id'] = 1;
      $data['lead']['status_id'] = 1;
      $data['lead']['source'] = 'Partners';
      if (!isset($data['lead']['is_abn_unknown']))$data['lead']['is_abn_unknown'] = 0;
      $lead_id = $this->Leads->addLead($data['lead']);

      // Insert into lead_owners table
      $data['lead_owner']['lead_id'] = $lead_id;
      $data['lead_owner']['point_of_contact'] = 1;
      $owner_id = $this->Leads->lead_owner->addLeadOwner($data['lead_owner']);

      // Check if same lead already exists through Lend -> in this case Partner Status wiil be 'Existing - Partner honoured'
      $this->checkExistsLead($data, $lead_id);

      // Insert into applicants table
      $this->loadModel('Applicants')->createApplicant(array('lead_id'=>$lead_id, 'email'=>$data['lead_owner']['email']));

      // Insert into lead_asset_finance table
      if(!empty($data['lead_asset_finance'])){
        $data['lead_asset_finance']['lead_id'] = $lead_id;
        $lead_asset_finance_table = TableRegistry::getTableLocator()->get('LeadAssetFinanceEntity');
        $lead_asset_finance = $lead_asset_finance_table->find('all')->where(['lead_id' => $lead_id])->first();
        if ($lead_asset_finance) {
          $lead_asset_finance_table->patchEntity($lead_asset_finance, $data['lead_asset_finance']);
        } else {
          $lead_asset_finance = $lead_asset_finance_table->newEntity($data['lead_asset_finance']);
        }
        $lead_asset_finance_table->save($lead_asset_finance);
      }

      // Insert into lead_abn_lookup table
      if(!empty($data['abnlookup']['abn'])){
        $data['abnlookup']['lead_id'] = $lead_id;
        $this->loadModel('LeadAbnLookup')->addLookup($data['abnlookup']);
      }

      // Insert into partner_lead_history
      $this->loadModel('PartnerLeadHistory')->addPartnerLeadHistory(array('partner_id'=>$data['lead']['partner_id'], 'partner_user_id'=>(!empty($this->Auth->user('partner_user_id'))?$this->Auth->user('partner_user_id'):(!empty($user['partner_user_id']) ? $user['partner_user_id'] : null)), 'lead_id'=>$lead_id, 'history_detail'=>'Lead Added'));

      return $lead_id;
    }

    // Update exists lead
    private function update($data){
      $user = $this->Auth->identify();
      if (!isset($data['lead']['is_abn_unknown'])) $data['lead']['is_abn_unknown'] = 0;

      $data = $this->Leads->formatLeadData($data);

      $this->Leads->updateLead($data['lead']);
      $this->Leads->lead_owner->updateLeadOwner($data['lead_owner']);

      $previousAbn = $this->loadModel('LeadAbnLookup')->getLeadABNDetails($data['lead']['lead_id'])['abn'];

      if($previousAbn !=  $data['abnlookup']['abn'] && !empty($data['abnlookup']['abn'])){
        $data['abnlookup']['lead_id'] = $data['lead']['lead_id'];
        $this->loadModel('LeadAbnLookup')->addLookup($data['abnlookup']);
      }

      if(!empty($data['abnlookup']['abn']) OR !empty($data['lead']['is_abn_unknown'])){
        // Mark job as complete if there is a job in AdminJobs:
        $this->loadModel('AdminJobs')->markAsComplete($data['lead']['lead_id'], 'abn_lookup', (!empty($this->Auth->user('partner_user_id'))?$this->Auth->user('partner_user_id'):(!empty($user['partner_user_id']) ? $user['partner_user_id'] : null)));
      }

      return $data;
    }






    public function revokeLender()
    {
      $leadRef = $this->request->data('lead_ref');
      $lender_id = $this->request->data('lender_id');
      $lead_id = $this->internal_auth->unhashLeadId($leadRef);
      $lead = $this->Leads->getLead(['lead_ref' => $leadRef]);
      $sale_id = $this->request->data('sale_id');

      //mark sale as revoked
      $this->loadModel('Sales')->revokeSale($sale_id);
       // Update lead to pending again
      $this->Leads->updateLead(['lead_id'=>$lead_id, 'partner_status_id'=>1]);

      //if it's an automatic lead, schedule for next lender
      if($lead['send_type'] != 'Manual') {
        $this->loadModel('BackgroundJobs')->addBackgroundJob([
          'lead_id' => $lead_id,
          'job_type' => 'send_to_lender',
          'ref_id' => json_encode(['lead_id'=>$lead_id]),
          'job_status' => 0,
          'class_name' => 'SendToLender',
          'function_name' => 'sendToLender',
          'created' => date('Y-m-d H:i:s')
        ]);
      }

      //check if lender has a field to be notified
      $lender = $this->loadModel('Lenders')->getLender(['lender_id'=>$lender_id]);

      //notify lender via email
      if(!empty($lender['lead_revoked_notice'])){
        $extraData['applicant_name'] = $lender['lender_name'];
        $extraData['lead_ref'] = $leadRef . ' ' . $lead['organisation_name'];
        $adhocData['lender_email'] = $lender['lead_revoked_notice'];
        $this->LoadModel('PartnerNotifications')->sendPartnerNotifications(null, 'LenderLeadRevoked', $lead_id, $adhocData,null, $extraData);
      }

      $this->setJsonResponse(array('success' => true));
    }

    public function saveLead() {
      //save data for lead in relevant tables
    }

    public function getLead($hashed_lead_id) {
      //get All data for a lead (all relevant tables)
      $bankfeeds_errors = Configure::read('Lend.BankFeedsErros');
      $lead_id = $this->internal_auth->unhashLeadId($hashed_lead_id);
      // Get lead details
      $lead = $this->Leads->getLeadDetails($lead_id);

      // Check permission:
      $check = $this->checkPermission($lead_id, $lead['lead']);

      if(!$check['success']){
          return $this->setJsonResponse(
            [
            'success'=>false,
            'lead'=> false,
            'message' =>$check['message']
            ]
          );
      }

      $lead['percentage'] = $this->Leads->checkPercentage($lead);
      $lead['send_prevention'] = $this->Leads->send_prevention->getSendPreventionWithUser(array('p.lead_id'=>$lead_id));

      // Get partner lead history and who added the lead
      $history = $this->loadModel('PartnerLeadHistory')->getPartnerLeadHistoryWithDetails(array('lead_id'=>$lead_id), array('history_id'=>'asc'));
      foreach ($history as $h) {
        if($h['history_detail'] == 'Lead Added'){
          $added_by = ['id' => $h['partner_user_id'], 'name' => $h['partner_user_name']];
          break;
        }
      }
      if(empty($lead['bs_accounts'])){
        $lead_activities = $this->Leads->activity->getActivities(array('lead_id'=>$lead_id), array('lead_activity_id'=>'desc'));
        $this->set('lead_activities', $lead_activities);
      }else{
        $bs_statuses = array_column($lead['bs_accounts'], 'retrieved');
        $bs_status = 'Ready to retrieve.';
        foreach($bs_statuses as $s){
          // Map BankFeeds status after retrieving:
          switch((int)$s){
            case 0: $bs_status = 'Ready to retrieve.'; break;
            case 1: $bs_status = 'Success';
              // BS Summary
              if ($bsa = $lead['bank_statements_analysis']) {
                $bs_summary = array();
                $bs_summary['avg_monthly_rev_180']     = $bsa['avg_mto_180'];
                $bs_summary['avg_monthly_rev_dep_180'] = $bsa['avg_num_mth_deps_180'];
                $bs_summary['avg_day_end_bal_180']     = $bsa['avg_day_end_bal_180'];
                $bs_summary['days_neg_180']            = $bsa['days_neg_180'];
                $bs_summary['dishonours_180']          = $bsa['days_dishonour_cnt_180'];
                //$bs_summary['cfl_detected'] = 'TBA';
              }
              break 2;
            case 2: $bs_status = 'In progress.'; break 2;
            case 5: $bs_status = 'Client uploaded an account, but it was not transaction account.'; break 2;
            default:
              if(in_array((int)$s, array_keys($bankfeeds_errors))){
                $bs_status = $bankfeeds_errors[$lead['bs_accounts'][0]['retrieved']];
              }else{
                $bs_status = 'There was an error while it\'s retrieving.';
              }
              break 2;
          }
        }
      }
      // Get previous sales history
      $prev_sales = $this->loadModel('Sales')->getSalesWithLenderDetails($lead_id, true);

      // Check there are any skipped lenders
      // NOTE:: DO NOT show skipped lenders for now until we bring proper things here: - 2nd Aug 2019
      // if($prev_sales) $prev_sales = $this->checkSkippedLenders($prev_sales, $lead);

      //if partner_status_id is 25
      if (!empty($lead['lead']['partner_status_id'])
          && $lead['lead']['partner_status_id'] == '25') {
        // No More Lenders Available, push a special element to $prev_sales array, for display in view only
        $prev_sales[] = array('lener_id' => '',
                              'lender_name' => '',
                              'shorthand' => '',
                              'status' => '-9', //add -9 fake option in view file accordingly
                              'info' => '<b>This could be due one of the following reasons:</b>
                              <ul>
<li>It\'s a duplicate lead with the remaining lenders</li>
<li>It has been sent to the maximum number of lenders</li>
<li>It doesn\'t meet the lending criteria of other lenders</li>
<li>The client is no longer interested or requested not to be contacted</li>
</ul>',
                              );
      }

      // Get Lend Score
      $lend_score = $this->loadModel('LendScore')->getLendScore(array('lead_id'=>$lead_id));
      $lend_score_config = $this->loadModel('LendScoreConfig')->getLendScoreConfig();
      $illion_pdfs = $this->LoadModel('LeadUploads')->getLatestBankStatementPDF($lead_id, true, true);
      $bs_status = (!empty($bs_status) ? $bs_status : false);

      // Config
      $industries = $this->config->groupIndustry($this->config->getConfig('config_industries'));
      $manageActiveUsers = $this->Auth->user('account_admin')
              ? $this->loadModel('PartnerUsers')->getActiveUsers($this->Auth->user('partner_id'))
              : false;

      $lead['bs_summary'] = $bs_summary;
      $lead['lead_activities'] = $lead_activities;
      $lead['history'] = $history;
      $lead['added_by'] = $added_by;
      $lead['prev_sales'] = $prev_sales;
      $lead['illion_pdfs'] = $illion_pdfs;
      $lead['bs_status'] = $bs_status;
      $lead['bs_status'] = $bs_status;
      $lead['lend_score'] = $lend_score;
      $lead['lend_score_config'] = $lend_score_config;
      $lead['manageActiveUsers'] = $manageActiveUsers;
      $this->setJsonResponse(
        [
        'success'=>true,
        'lead'=>$lead
        ]
      );
    }

    public function view($hashed_lead_id, $isQuote = false){
      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }
      if (!empty($user) AND $user['account_type']!=='Applicant') {
        $this->set('partnerId', $user['partner_id']);
      }
      $partner_feature = $this->loadModel('PartnerFeatureFlags')->getFeatureFlag(['partner_id' => $user['partner_id']]);
      if($partner_feature['REACT_COMMERCIAL_LEADS'] === '1' && $_SERVER['HTTP_REFERER'] != null && $partner_feature['access_legacy_commercial_lead'] === '0'){
        return $this->redirect('/lead/'.$hashed_lead_id.'/summary');
      }
        $this->viewBuilder()->setTemplate('lead');
        if(!$isQuote){
          $lead_id = (new LendInternalAuth)->unhashLeadId($hashed_lead_id);
          if($lead_id > 0){
            $this->set('leadRef', $hashed_lead_id);
          }else{
            return $this->redirect('/partners/dashboard?error="Invalid Lead Ref"');
          }
        }
        $this->set('uppyConfiguration', json_encode(Configure::read('Lend.UploadManager')));
    }

    /**
     * Quote page load from email link
     */
    public function viewQuote(){
        $ownerRef = $this->request->getQuery('o');
        $hashedLeadId = $this->request->getQuery('l');
        $lead_id = (new LendInternalAuth)->unhashLeadId($hashedLeadId);
        if($lead_id > 0){
          $this->set('toComplete', $hashedLeadId);
        }else{
          $this->set('toComplete', '');
        }
        
        $this->set('applicant', 'true');
        $this->set('owner_id', $ownerRef);
        $this->view($hashedLeadId, true);
    }

    public function completeApplication()
    {
      $ownerRef = $this->request->getQuery('o');
      $hashedLeadId = $this->request->getQuery('l');
      $code = $this->request->getQuery('c');
      $sections = $this->request->getQuery('s');

      $bankStatement = $this->request->getQuery('bs');

      $this->set('toComplete', $hashedLeadId);
      $this->set('applicant', 'true');
      $this->set('owner_id', $ownerRef);
      $this->set('code', $code); //what to request
      $this->set('sections', $sections); //what to request
      $this->view($hashedLeadId);
    }

    public function checkForTrans($leadRef) {
      $lead_id = (new LendInternalAuth)->unhashLeadId($leadRef);
      $crbTrans = $this->loadModel('PartnerCrbTrans')->getLeadCrbTrans($lead_id);
      $privacyTrans = $this->loadModel('PartnerRequestedPrivacyFormTrans')->getAllLeadTransactions($lead_id);
      if ( empty($crbTrans) && empty($privacyTrans) ) {
        return $this->setJsonResponse(array('trans'=>false) );
      }
      if ( !empty($crbTrans) || !empty($privacyTrans) ) {
        return $this->setJsonResponse(array('trans'=>true) );
      }
    }

    public function reassign()
    {
      $leadRef = $this->request->data('leadRef');
      $user = $this->Auth->user();
      try {
        if(empty($user['account_admin']))
          throw new \Exception('You do not have access to this resource');

        $lead = $this->Leads->getLead(['lead_ref' => $leadRef]);
        if (empty($lead))
          throw new \Exception("Lead not found");
        if ($lead['partner_id'] != $user['partner_id'])
          throw new \Exception("You do not have access to this lead");

        $partnerUserLeadsTable = TableRegistry::get('PartnerUserLeads');

        $currentUser = $partnerUserLeadsTable->getCurrentPartnerUser($lead['lead_id']);
        $manageActiveUsers = $this->loadModel('PartnerUsers')->getActiveUsers($user['partner_id']);

        $this->set('currentUser', $currentUser);
        $this->set('manageActiveUsers', $manageActiveUsers);

        //check if userRef is passed in, then re-assign the lead to another user
        if (!empty($this->request->data['userRef'])) {
          $partnerUserId = $this->internal_auth->unhashLeadId($this->request->data['userRef']);
          //check if the partnerUserId is contained in $manageActiveUsers
          if (empty($manageActiveUsers[$partnerUserId]))
            throw new \Exception('Selected user not found');

          //update partner_lead_users so that only new $partnerUserId has access to that lead
          $partnerUserLeadsTable = TableRegistry::getTableLocator()->get('PartnerUserLeadsEntity');
          $partnerUserLead = $partnerUserLeadsTable->newEntity([
            'partner_user_id' => $partnerUserId,
            'status' => 'ACCESS',
            'lead_id' => $lead['lead_id']
          ]);
          $partnerUserLeadsTable->save($partnerUserLead);

          $this->set('updated', true);
        }

      } catch (\Exception $e) {
        $error = $e->getMessage();
        $this->set('error', $error);
      }
      $this->set('leadRef', $leadRef);
      $this->viewBuilder()->setLayout('ajax');
    }

    public function listLeadStatus(){
        $result=array('success'=>false);
        /** @var LeadsTable $leadsTable */
        $leadsTable =$this->loadModel('leads');
        $user= $this->Auth->user();
        $partnerId = $user['partner_id'];
        $qsParams= $this->request->getQuery();
        $startDate=(isset($qsParams['start_date'])?$qsParams['start_date']. '00:00:00':null);
        $endDate=(isset($qsParams['end_date'])?$qsParams['end_date'].' 23:59:59':null);

        $result['list_lead_status']=$leadsTable->listLeadStatusByPartnerId($partnerId,$startDate,$endDate);
        $result['success']=true;
        return $this->setJsonResponse($result);
    }

    public function listLenders(){
      $result=array('success'=>false);
      /** @var LeadsTable $leadsTable */
      $leadsTable =$this->loadModel('leads');
      $user= $this->Auth->user();
      $partnerId = $user['partner_id'];
      $qsParams= $this->request->getQuery();
      $startDate=(isset($qsParams['start_date'])?$qsParams['start_date'].' 00:00:00':null);
      $endDate=(isset($qsParams['end_date'])?$qsParams['end_date'].' 23:59:59':null);

      $result['list_lender']=$leadsTable->listLendersByPartnerId($partnerId,$startDate,$endDate);  //listLeadStatusByPartnerId($partnerId,$startDate,$endDate);
      $result['success']=true;
      return $this->setJsonResponse($result);

  }

    public function filterWithPagReadData($csvParams = false)
    {
        if ($csvParams) {
            $qsParams = $csvParams;
        } else {
            $qsParams = $this->request->getData() + $this->request->getQuery();
        }
        $filterLeadsJson = null;
        if (isset($_COOKIE['filter']) && !empty($_COOKIE['filter'])) {
            $filterLeadsArray = json_decode($_COOKIE['filter'], true);
            $filterLeadsJson = @$filterLeadsArray[$qsParams['manage']];
        }

        if (isset($qsParams['filters'])) {//post data filters have been sent
          $filterLeadsJson['filters'] = $qsParams['filters'];
        }

        if (!isset($qsParams['order'])) {
          if (isset($_COOKIE['order_by'])) {
              $orderByValue = $_COOKIE['order_by'];
              list($orderByColumn, $orderByDirection) = explode(':', $orderByValue);
              if ($orderByColumn !== null && $orderByDirection !== null) {
                  $qsParams['order'] = array(
                      array(
                          'column' => $orderByColumn,
                          'dir' => $orderByDirection
                      )
                  );
              }
          }
        }

        $user = $this->Auth->user();
        $partnerId = $user['partner_id'];

        $result = array();
        /** @var LeadsTable $leadsTable */
        $leadsTable = $this->loadModel('leads');
        $result['draw'] = @$qsParams['draw'];

        $dbParams = array('le.partner_id' => $partnerId, 'search_value' => @$qsParams['search']['value']);
        if (strtolower($qsParams['manage']) == 'archived') {// overwrite leadsFilterJson['show_archive'] regardless whether user tick box or not
            $dbParams['le.is_archived'] = 1;
            $filterLeadsJson['show_archive'] = true;
            $filterLeadsJson['content_type'] = 'All';

            $qsParams['manage'] = 'Leads';//get all leads that are archived
            //$_COOKIE['filterLeadsJson']=json_encode($filterLeadsJson);
        }

        if (strtolower($qsParams['manage']) != 'leads') {
            $dbParams['status_id_list'] = LendStatus::getPartnerStatuses($qsParams['manage'], 'groupName');
        }
        if (isset($qsParams['start_date']) && isset($qsParams['end_date'])) {
            $dbParams['start_date'] = $qsParams['start_date'] . ' 00:00:00';
            $dbParams['end_date'] = $qsParams['end_date'] . ' 23:59:59';
        }
        if (empty($this->Auth->user('access_all_leads'))) {
            $dbParams['pul.partner_user_id'] = $this->Auth->user('partner_user_id');
        }

        $lastOriginalLeadUpdateDays = @$qsParams['lastOriginalLeadUpdateDays'];

        //read 18 statuses from lend config
        $result['partner_status_ids'] = LendStatus::getPartnerStatuses(null, 'original');
        $result['lead_status_ids'] = LendStatus::getPartnerStatuses('Leads', 'groupName');

        //will be helpful for dates's * ago function in JS, due to Sydney datetime values in live db
        $result['timezoneOffset'] = date('Y-m-d H:i:s');  //get PHP server datetime value, pass it into JS


        $intermediary_users_product_ids = [];
        $user = $this->Auth->user();
        $lenderEntityTable = TableRegistry::getTableLocator()->get('LenderEntity');
        $partnerId = $user['partner_id'];
        $isIntermediary = $user['account_type'] === 'Intermediary';
        $intermediary_stuck_lead_page = isset($lastOriginalLeadUpdateDays['selected']) && $isIntermediary;
        
        $partner_feature = $this->loadModel('PartnerFeatureFlags')->getFeatureFlag(['partner_id' => $partnerId]);
        $leadsFunction = empty($partner_feature['use_orm_lead_list']) ? 'readRecordWithPag' : 'getLeads';


        if ($intermediary_stuck_lead_page) {
          if (empty($_COOKIE['recordsTotal']) or !empty($qsParams['refreshPagination'])) {

          $result['data'] = $leadsTable->getInterMediaryLead(
            $dbParams,
            array($qsParams['order'][0]['column'] ?? 'created' => $qsParams['order'][0]['dir'] ?? 'desc'),
            array('limit' => $qsParams['length'] ?? 25, 'offset' => $qsParams['start'] ?? 0),
            true,
            ($filterLeadsJson == null ? false : $filterLeadsJson),
            $qsParams['manage'],
            false,
            $isIntermediary,
            $lastOriginalLeadUpdateDays,
            false,
            $partnerId
          );
          if (empty($_COOKIE['recordsTotal']) or !empty($qsParams['refreshPagination'])) {
              $dbParams['search_value'] = '';
              $result['recordsTotal'] = $leadsTable->getInterMediaryLead(
                $dbParams,
                false,
                false,
                true,
                ($filterLeadsJson == null ? false : $filterLeadsJson),
                $qsParams['manage'],
                true,
                $isIntermediary,
                $lastOriginalLeadUpdateDays,
                true,
                $partnerId
              )['total'][0]['count'];
              $result['recordsFiltered'] = $result['recordsTotal'];
              setcookie('recordsTotal', $result['recordsTotal'], 0, '/');
          } else {
              $result['recordsTotal'] = $_COOKIE['recordsTotal'];
              $result['recordsFiltered'] = $_COOKIE['recordsTotal'];
          }
        }
      }
        else if (!$csvParams) {
            if (@$qsParams['record'] == 'filters') {

                // $result['filters'] = $leadsTable->readRecordWithPag(
                $result['filters'] = $leadsTable->$leadsFunction(
                  $dbParams,
                  false,
                  false,
                  true,
                  [
                    'show_archive' => true,
                    'content_type' => 'all'
                  ],
                  $qsParams['manage'],
                  false,
                  $isIntermediary,
                  $lastOriginalLeadUpdateDays,
                  $partnerId 
                );
            } else {

                $dt = $leadsTable->$leadsFunction(
                  // $dt = $leadsTable->readRecordWithPag(
                  $dbParams,
                  array($qsParams['order'][0]['column'] ?? 'created' => $qsParams['order'][0]['dir'] ?? 'desc'),
                  array('limit' => $qsParams['length'] ?? 25, 'offset' => $qsParams['start'] ?? 0),
                  true,
                  ($filterLeadsJson == null ? false : $filterLeadsJson),
                  $qsParams['manage'],
                  false,
                  $isIntermediary,
                  $lastOriginalLeadUpdateDays,
                  $partnerId 
                );
                $result['data'] = $dt;
                if (empty($_COOKIE['recordsTotal']) or !empty($qsParams['refreshPagination'])) {
                    $dbParams['search_value'] = '';
                    // $result['recordsTotal'] = $leadsTable->readRecordWithPag(
                    $result['recordsTotal'] = $leadsTable->$leadsFunction(
                      $dbParams,
                      false,
                      false,
                      true,
                      ($filterLeadsJson == null ? false : $filterLeadsJson),
                      $qsParams['manage'],
                      true,
                      $isIntermediary,
                      $lastOriginalLeadUpdateDays,
                      $partnerId
                    )[0]['count'];
                    $result['recordsFiltered'] = $result['recordsTotal'];
                    setcookie('recordsTotal', $result['recordsTotal'], 0, '/');
                } else {
                    $result['recordsTotal'] = $_COOKIE['recordsTotal'];
                    $result['recordsFiltered'] = $_COOKIE['recordsTotal'];
                }
            }
            $result['params'] = $qsParams;
        }

        if ($csvParams) {
            $extraQueryOptions = [
                'csv_export',
            ];

            // Hardcode the new ORM function, so only the new function is used for CSV export and get the update.
            return $leadsTable->getLeads(
              $dbParams,
              array($csvParams['order'][0]['column'] ?? 'created' => $csvParams['order'][0]['dir'] ?? 'desc'),
              false,
              true,
              ($filterLeadsJson == null ? false : $filterLeadsJson),
              $qsParams['manage'],
              false,
              $isIntermediary,
              $lastOriginalLeadUpdateDays,
              $partnerId,
              $extraQueryOptions
            );
        } else {
            return $this->setJsonResponse($result);
        }
    }

    public function filterLeadsWithPag()
    {
        $qsmanage=$this->request->getQueryParams();
        $data['pageHead2']=(!empty($qsmanage['status'])?$this->_pageHead2Array[$qsmanage['status']].' - '.($qsmanage['range'] == 'custom'? 'Custom Range ('.date("d/m/Y", strtotime($qsmanage['start_date'])).' - '.date("d/m/Y", strtotime($qsmanage['end_date'])).')':$qsmanage['range']):'Leads');
        $data['status']=$qsmanage['status'];

        $user = $this->Auth->user();
        $data['isIntermediary'] = $user['account_type'] === 'Intermediary';

        $this->set('data',$data);
        $this->viewBuilder()->setLayout('empty_html');
    }

    private function _setLeadsCsvDataDynamic(&$data){
        $data['header']=array();
        $data['header'] = [
            'created' => 'Created Date',
            'last_changed_date' => 'Last Updated Date',
            'lead_ref' => 'Lead Ref',
            'lead_type' => 'Lead Type',
            'product_type_name' => 'Product Type',
            'purpose' => 'Purpose',
            'partner_user_name' => 'Assignee',
            'pul_granted' => 'Date Last Assigned',
            'referrer_nickname' => 'Referrer Nickname',
            'referrer' => 'Referrer Person Name',
            'referrer_contact_number' => 'Referrer Person Contact',
            'cqcq_completed_date' => 'CQCQ Completed Date',
            'cro_description' => 'R&O Description',
            'cro_loan_by_date' => 'R&O Credit Required By',
            'cro_budgeted_repayments' => 'R&O Budgeted Monthly Repay',
            'business_name' => 'Business Name',
            'organisation_name' => 'Organisation Name',
            'abn' => 'Biz ABN',
            'acn' => 'Biz ACN',
            'b_state' => 'Biz State',
            'abn_entity_type_description' => 'Biz Entity Type',
            'company_registration_date' => 'Biz Registration Date',
            'abn_gst_effective_from' => 'Biz GST From Date',
            'abn_status_active' => 'Biz GST Status',
            'industry' => 'Biz Industry',
            'current_trading_address_string' => 'Biz Trading Address',
            'biz_total_trading_addr_hist_yrs' => 'Biz Total Trading Addr Hist Yrs',
            'biz_total_trading_addr_hist_mth' => 'Biz Total Trading Addr Hist Mths',
            'current_business_address_string' => 'Biz Registered Address',
            'current_mailing_address_string' => 'Biz Mailing Address',
            'sales_monthly' => 'Biz Monthly Turnover (Verified)',
            'client_declared_sales_monthly' => 'Biz Monthly Turnover (Client Declared)',
            'monthly_expenses' => 'Biz Monthly Expenses (Verified)',
            'client_declared_monthly_expenses' => 'Biz Monthly Expenses (Client Declared)',
            'tax_outstanding_string' => 'Biz ATO Debt',
            'tax_overdue' => 'Biz ATO Plan Amt',
            'business_credit_history' => 'Biz Credit File Hist',
            'biz_total_assets' => 'Biz A&L Tot Assets',
            'biz_total_liabilities' => 'Biz A&L Tot Liabilities',
            'biz_net_position' => 'Biz A&L Net Position',
            'poc_equity' => 'POC Business Equity',
            'poc_owner_type' => 'POC Type',
            'poc_name' => 'POC Name',
            'poc_email' => 'POC Email',
            'poc_mobile' => 'POC Mobile',
            'poc_address' => 'POC Address',
            'poc_living_status' => 'POC Living Status',
            'poc_total_addr_hist_yrs' => 'POC Total Addr Hist Yrs',
            'poc_total_addr_hist_mth' => 'POC Total Addr Hist Mths',
            'poc_home_owner' => 'POC Property Owner',
            'lead_owner_dob' => 'POC Date of Birth',
            'poc_gender' => 'POC Gender',
            'poc_marital_status' => 'POC Marital Status',
            'poc_residency_status' => 'POC Residency Status',
            'poc_number_of_dependants' => 'POC Num of Dependants',
            'poc_credit_history' => 'POC Credit File Hist',
            'poc_employment_status' => 'POC Employment Status',
            'poc_occupation' => 'POC Occupation',
            'poc_employer_name' => 'POC Employer Name',
            'poc_employer_contact' => 'POC Employer Contact',
            'poc_total_employment_hist_yrs' => 'POC Total Employment Hist Yrs',
            'poc_total_employment_hist_mth' => 'POC Total Employment Hist Mths',
            'poc_total_assets' => 'A&L Tot Assets',
            'poc_total_liabilities' => 'A&L Tot Liabilities',
            'poc_net_total' => 'A&L Net Position',
//            '' => 'I&E Monthly Surplus', HEM current needs an API call for each lead.
//            '' => 'I&E Monthly HEM',
//            '' => 'I&E Adjusted Surplus',
            'is_asset_e2e' => 'Asset Finance End to End?',
            'contract_type' => 'Asset Finance Contract Type',
            'asset_type' => 'Asset Type',
            'asset_make' => 'Asset Make',
            'asset_model' => 'Asset Model',
            'asset_year' => 'Asset Year',
            'asset_age_months' => 'Asset Age',
            'asset_sale_type' => 'Asset Sale Type',
            'asset_valuation' => 'Asset Valuation (From Redbook)',
            'asset_manual_valuation' => 'Asset Valuation (Manually entered)',
            'asset_condition' => 'Asset Condition',
            'asset_ev_hybrid' => 'Asset EV Hybrid',
            'asset_purchase_price' => 'Asset Purchase Price',
            'asset_deposit' => 'Asset Finance Deposit',
            'asset_tradein_value' => 'Trade In Quote',
            'asset_outstanding_loan' => 'Outstanding Loan Payout Amt',
            'asset_insurance_addon' => 'Insurance Addon',
            'asset_other_addons' => 'Other Addons',
            'asset_balloon' => 'Balloon Amt',
            'asset_balloon_reason' => 'Balloon Reason',
            'asset_balloon_pay_method' => 'Balloon Pay Method',
            'asset_ltv' => 'Asset Finance LTV',
            'supplier' => 'Supplier Name',
            'supplier_contact_name' => 'Supplier Contact Name',
            'statements_uploaded' => 'Bank Statements Provided? (Y/N)',
            'lend_score' => 'LendScore',
            'amount_requested' => 'Lead Loan Amount (Initial Quote)',
            'lead_man_status' => 'Latest Broker Manual Status',
            'manual_status_date' => 'Manual Status Date',
            'lead_status' => 'Latest Lend System Status',
            'lender_status' => 'Latest Lender API Status',
            'submission_date' => 'Submission Date (Latest Submission)',
            'selected_lender_name' => 'Selected Lender Name',
            'selected_tier_name' => 'Selected Tier Name',
            'selected_product_name' => 'Selected Product Name',
            'selected_establishment_fees' => 'Selected Lender Establishment Fees',
            'selected_monthly_fees' => 'Selected Lender Monthly Fees',
            'selected_include_fees_requested' => 'Are Fees Selected to be Financed?',
            'selected_financed_amount' => 'Selected Financed Amount',
            'selected_commission' => 'Selected Brokerage',
            'selected_commission_type' => 'Selected Brokerage % or $',
            'selected_origination_fee' => 'Selected Origination Fee',
            'selected_term_months' => 'Selected Loan Term',
            'selected_apr' => 'Selected APR',
            'selected_base_rate' => 'Selected Base Rate',
            'selected_customer_rate' => 'Selected Customer Rate',
            'selected_repayment_amt' => 'Selected Repayment Amount',
            'selected_repayment_freq' => 'Selected Repayment Frequency',
            'funded_date' => 'Settlement Date (Latest Settlement)',
            'settled_mirrors_submission' => 'Settlement Mirrors Submission?',
            'settled_lender_name' => 'Settled Lender Name',
            'settled_tier_name' => 'Settled Tier Name',
            'settled_product_name' => 'Settled Product Name',
            'settled_lender_ref' => 'Settled Lender Contract Ref',
            'settled_loan_amount' => 'Settled Amount Final',
            'settled_brokerage_fee' => 'Settled Brokerage',
            'settled_apr' => 'Settled APR',
            'settled_base_rate' => 'Settled Base Rate',
            'settled_customer_rate' => 'Settled Customer Rate',
            'settled_total_estab_fees' => 'Settled Lender Establishment Fees Total',
            'settled_monthly_fees' => 'Settled Lender Monthly Fees',
            'settled_application_fee_included' => 'Are Fees Included in Finance at Settlement?',
            'settled_repayment_amt' => 'Settled Repayments',
            'settled_repayment_freq' => 'Settled Repayment Frequency',
            'settled_total_ballon' => 'Settled Balloon Amt',
            'settled_origination_fee' => 'Settled Origination Fee',
            'settled_vbi_income' => 'Settled Vol Bonus',
            'settled_cci_ins_comms' => 'Settled CCI Commission',
            'settled_comp_ins_comms' => 'Settled CI Commission',
            'settled_gap_ins_comms' => 'Settled Gap Commission',
            'settled_warranty_comms' => 'Settled Warranty Commission',
            'settled_other_income' => 'Settled Other Commission',
            'settled_referrer_commission' => 'Settled Referrer Commission',
            'prelim_completed_date' => 'Prelim Completed Date',
            'credit_prop_completed_date' => 'Credit Prop Completed Date',
            'last_note' => 'Last Note',
            'last_note_created' => 'Last Note Date',
            'last_task_type' => 'Last Task Type',
            'last_task_due' => 'Last Task Assignee',
            'last_task_status' => 'Last Task Due Date',
            'last_task_assignee' => 'Last Task Details',
            'campaign' => 'Campaign',
            'ga_client_id' => 'GA Client ID',
            'gclid' => 'GCLID',
            'utm_source' => 'UTM Source',
            'utm_medium' => 'UTM Medium',
            'utm_campaign' => 'UTM Campaign',
            'utm_content' => 'UTM Content',
            'utm_term' => 'UTM Term',
        ];

        foreach($data['body'] as $kb=>$vb){
            foreach ($vb as $kb2=>$vb2){
              // Check for commas, double quotes, or newlines and encapsulate the field in double quotes if any are found
              if (preg_match('/[,"\r\n]/', $data['body'][$kb][$kb2])) {
                $data['body'][$kb][$kb2] = '"' . str_replace('"', '""', $data['body'][$kb][$kb2]) . '"';
              }
              if(!isset($data['header'][$kb2])){
                unset($data['body'][$kb][$kb2]);
              }else if(in_array($kb2,array('organisation_name','purpose_other'))) { //replace with space
                $data['body'][$kb][$kb2] = preg_replace('/[ ,]+/', ' ', trim($vb2));
              }else if(in_array($kb2,array('amount_requested','funded_amount'))) { //replace with empty
                $data['body'][$kb][$kb2] = preg_replace('/[,]+/', '', trim($vb2));
              }
            }
            $data['body'][$kb]['amount_requested']=strip_tags(@$data['body'][$kb]['amount_requested']);  //str_replace(array("<span class='commissionPaid'>","</span>"),array('',''),$data['body'][$kb]['amount_requested']);
            $data['body'][$kb]['lender_status']=strip_tags(@$data['body'][$kb]['lender_status']);  //str_replace(array('<div class="elipsis">','</div>'),array('',''),$data['body'][$kb]['lender_status']);
            $data['body'][$kb]['lend_score']=strip_tags(@$data['body'][$kb]['lend_score']);
            $data['body'][$kb]['is_archived']=strip_tags(@$data['body'][$kb]['is_archived']);//  str_replace(array('<span style="color:red;">','</span>'),array('',''),$data['body'][$kb]['is_archived']);
            $data['body'][$kb]['funded_amount']=strip_tags(@$data['body'][$kb]['funded_amount']);
            $data['body'][$kb]['fund_type']=strip_tags(@$data['body'][$kb]['fund_type']);
            $data['body'][$kb]['statements_uploaded'] = !empty($data['body'][$kb]['statements_uploaded']) ? 'Retrieved' : 'Not Retrieved';
            $data['body'][$kb]['last_changed_date'] = @$data['body'][$kb]['last_changed_date'] ?: $data['body'][$kb]['created'];
        }

    }

    private function formatClosedLeadsFilters() {
      $leadsClosedFilter = json_decode($_COOKIE['leadsClosedFilter'], true);
      if (empty($leadsClosedFilter)) {
        return null;
      }

      $filters = [
        'params' => [
          'start_data' => $leadsClosedFilter['start_date'],
          'end_date' => $leadsClosedFilter['end_date'],
        ],
        'data' => [
          'filters' => []
        ],
      ];
      if (!empty($leadsClosedFilter['status'])) {
        $filters['data']['filters']['man_status_id'] = $leadsClosedFilter['status'];
        $filters['data']['filters']['partner_status_id'] = $leadsClosedFilter['status'];
      }
      if (!empty($leadsClosedFilter['customerType'])) {
        $filters['data']['filters']['lead_type'] = $leadsClosedFilter['customerType'];
      }
      if (!empty($leadsClosedFilter['assignee'])) {
        $filters['data']['filters']['assignee_id'] = $leadsClosedFilter['assignee'];
      }
      if (!empty($leadsClosedFilter['product_type'])) {
        $filters['data']['filters']['product_type_id'] = $leadsClosedFilter['product_type'];
      }
      if (!empty($leadsClosedFilter['lender'])) {
        $filters['data']['filters']['lender_name'] = $leadsClosedFilter['lender'];
      }
      if (!empty($leadsClosedFilter['referrer'])) {
        $filters['data']['filters']['referrer'] = $leadsClosedFilter['referrer'];
      }

      $filters['data']['filters']['filter_by_date'] = $leadsClosedFilter['filter_by_date'];
      $filters['data']['filters']['settled_date'] = $leadsClosedFilter['settled_date'];
      $filters['data']['filters']['is_closed'] = $leadsClosedFilter['is_closed'];
      
      return $filters;
    }

    private function formatKanbanFilters() {
      $qsParams = $this->request->getQuery();

      $leadsFilterRaw = json_decode($_COOKIE['leadsKanbanFilter'], true);
      $kanbanFilterNamesMap = [
        'status' => 'man_status_name',
        'customerType' => 'lead_type',
        'assigned' => 'assignee_id',
        'productType' => 'product_type_name',
        'lender' => 'lender_name',
        'tag' => 'tag_id',
        'referrer' => 'referrer',
      ];

      $filtersFormatted = [];
      foreach ($leadsFilterRaw as $key => $value) {
        if (isset($kanbanFilterNamesMap[$key]) && !empty($value)) {
          if ($key === 'referrer') {
            foreach ($value as $k => $v) {
              if (strpos($v, 'referrer_') === false) {
                continue;
              }
              $v = str_replace('referrer_', '', $v);
              $filtersFormatted[$kanbanFilterNamesMap[$key]][] = $v;
            }
            continue;
          }

          $filtersFormatted[$kanbanFilterNamesMap[$key]] = $value;
        }
      }
      $filtersFormatted['filter_by_date'] = 'created';
      return [
        'params' => [
          'start_date' => $qsParams['start_date'],
          'end_date' => $qsParams['end_date'],
          'range' => $qsParams['range'],
        ],
        'data' => [
          'search' => [
            'value' => '',
            'regex' => false,
          ],
          'order' => [
            [
              'column' => 'created',
              'dir' => 'desc',
            ],
          ],
          'filters' => $filtersFormatted,
        ],
      ];
    }

    public function exportLeadsCsv($type = null) {
      try {
        $data=array();// two index header and body are required
        $qsParams = $this->request->getQuery();
        $qsParams['length'] = false;// remove limit from query if there is

        $user = $this->Auth->user();
        if (empty($user)) {
          $user = $this->Auth->identify();
        }
        if (empty($user['access_lead_list_export'])) {
          throw new \Exception("You don't have permission to export leads");
        }

        $leadsFilter = json_decode($_COOKIE['leadsFilter'], true);
        if ($type === 'kanban') {
          $leadsFilter = $this->formatKanbanFilters();
        } elseif ($type ==='closed') {
          $leadsFilter = $this->formatClosedLeadsFilters();
        }

        if (!empty($leadsFilter)) {
          $flatLeadsFilter = $leadsFilter['params'] + $leadsFilter['data'];
        } else {
          $flatLeadsFilter = [];
        }

        if ($type === 'closed') {
          $flatLeadsFilter['filters']['is_closed'] = 1;
        }

        $data['body'] = $this->filterWithPagReadData($qsParams + $flatLeadsFilter);

        $this->_setLeadsCsvDataDynamic($data);

        $todayDate = (new \DateTime())->format('Ymd');

        ini_set('memory_limit','1024M');
        $this->response = $this->response->withType('csv');
        $this->response = $this->response->withDownload('lead_list_'.$todayDate. '.csv');

        $this->viewBuilder()->setLayout('ajax');
        $this->set('data', $data);
        $this->render('download_csv');
      } catch (\Exception $e) {
        Log::error($e->getMessage());
        Log::error($e->getTraceAsString());
        return $this->setJsonResponse(['success' => false, 'data' => $e->getMessage()]);
      }
    }


    /* sent leads graph on partner dashboard page */
    public function sentByMonth($howManyMonths)
    {
      $user = $this->Auth->user();
      $passInPartnerUserId = !empty($user['access_all_leads']) ? null : $user['partner_user_id'];
      list($leads, $isEmpty) = $this->Leads->sentLeadsPastXMonths(
                                      $this->Auth->user('partner_id'),
                                      $howManyMonths, $passInPartnerUserId);
      $this->setJsonResponse(array('success'=>true,
                                  'data'=>$leads,
                                  'isEmpty' => $isEmpty ? true : false) );
    }


    public function filter()
    {

      $params        = $this->request->getQueryParams();
      $showArchived = ((!empty($params['includeArchived']) AND $params['includeArchived'] == 'true') ? 1 : 0);
      unset($params['includeArchived']);//it shouldn't affect other filter

      $onlyArchived = false;
      if($params['status'] == 'Archived'){
        $onlyArchived = true;
        $params['status'] = 'Leads';
      }
      // $validStatuses = array('Leads', 'Attempting', 'In Progress', 'Rejected', 'Settled');
      $validStatuses = LendStatus::getPartnerStatuses(null, null);
      $params = $this->__checkDateRangeInCookie($params);

      $validStatuses[] = 'Leads'; //including all leads except for settled
      if (isset($params['start_date'], $params['end_date'], $params['range'])) {
        $this->Cookie->write('Dashboard.dateRange', ['start_date' => $params['start_date'], 'end_date' => $params['end_date'], 'range' => $params['range']]);
      } elseif ($this->Cookie->check('Dashboard.dateRange')) {
        $params = array_merge($params, $this->Cookie->read('Dashboard.dateRange'));
      }

      if (isset($params['status']) && $params['status'] == 'undefined' ) $params['status'] = '';
      if (!in_array($params['status'], $validStatuses) && !empty($params['status']))   die('Invalid Status Type');
      if (!$params['start_date'] OR !$params['end_date']) die('No date range provided');

      if ($params['status']) {
        $params['partner_status_id'] = LendStatus::getPartnerStatuses($params['status'], 'groupName');
      }

      $user= $this->Auth->user();
      $partnerId = $user['partner_id'];
      $passInPartnerUserId = !empty($user['access_all_leads']) ? null : $user['partner_user_id'];


      $data = $this->Leads->getLeadsTotalByPartner($partnerId, $params['start_date'], $params['end_date'], 'total', $passInPartnerUserId, $showArchived);
      if ($params['status'] == 'Settled') {
        $leads = $this->Leads->settledLeadsByPartner($partnerId, $params, 'list', $passInPartnerUserId, $showArchived);
        //Have to get the count of archived leads
        $totalArchived = count($this->Leads->settledLeadsByPartner($partnerId, $params, 'list', $passInPartnerUserId, 'countArchived'));

        list($leads, $viewFilters) = $this->Leads->getAvailableFiltersByLeads($leads, $params);
        $this->set($viewFilters);
      } else {  //other partner status than 'Settled'
        if($onlyArchived) {
            $leads = $this->Leads->sentLeadsUsingFilters($partnerId, $params, $passInPartnerUserId, 'countArchived');
        }else {
            $leads = $this->Leads->sentLeadsUsingFilters($partnerId, $params, $passInPartnerUserId, $showArchived);
        }

        $totalArchived = count($this->Leads->sentLeadsUsingFilters($partnerId, $params, $passInPartnerUserId, 'countArchived'));

        list($leads, $viewFilters) = $this->Leads->getAvailableFiltersByLeads($leads, $params);
        $this->set($viewFilters);
        foreach ($leads as $key => $lead) {
          $required = $this->Leads->checkPercentage(array('lead'=>$lead));
          $leads[$key]['percent_complete'] = $required['current_percentage'];
        }
      }
      $lend_score_config = $this->loadModel('LendScoreConfig')->getLendScoreConfig();
      $this->set('lend_score_config', $lend_score_config);

      // Get partner aliases
      $params['partner_aliases'] = $this->loadModel('PartnerAliases')->getPartnerAliases(array('partner_id'=>$partnerId));

      if($onlyArchived)
        $params['status'] = 'Archived';
      if (isset($params['status'])) {
        $this->set('openedTab', $params['status']);
      }
      if ($this->Cookie->check('Dashboard.UserDashPrefs')) {
        $columns = $this->Cookie->read('Dashboard.UserDashPrefs');
        $this->set('columns', $columns);
      }
      //get Leads status Ids
      $leadsStatusIds = LendStatus::getPartnerStatuses('Leads', 'groupName');
//print_r($leads);
//die();
      $this->set('uppyConfiguration', json_encode(Configure::read('Lend.UploadManager')));
      $this->set('data', $data); //for tiles
      $this->set('show_archived', $showArchived);
      $this->set('total_archived', $totalArchived);
      $this->set('leadsStatusIds', $leadsStatusIds);
      $this->set('leads', $leads);
      $this->set('params', $params);
    }

    /* ajax call on dashboard page */
    public function metrics()
    {
      $params = $this->__checkDateRangeInCookie($this->request->getQueryParams());

      $user = $this->Auth->user();
      $partnerId = $user['partner_id'];
      $passInPartnerUserId = !empty($user['access_all_leads']) ? null : $user['partner_user_id'];

      if (empty($partnerId)) die('log in please');
      if (!$params['start_date'] OR !$params['end_date']) die('No date range provided');

      $metrics = $this->Leads->getLeadsTotalByPartner($partnerId,
                                                      $params['start_date'],
                                                      $params['end_date'],
                                                      'metrics',
                                                    $passInPartnerUserId);

      $this->set('metrics', $metrics);
      $this->set('params', $params);
    }

    /* ajax call on dashboard page */
    public function tiles()
    {
      $params        = $this->request->getQueryParams();
      $validStatuses = LendStatus::getPartnerStatuses(null, null);

      $user= $this->Auth->user();
      $partnerId = $user['partner_id'];
      $passInPartnerUserId = !empty($user['access_all_leads']) ? null : $user['partner_user_id'];

      if (empty($partnerId)) die('log in please');
      if (!$params['start_date'] OR !$params['end_date']) die('No date range provided');

      $data = $this->Leads->getLeadsTotalByPartner($partnerId, $params['start_date'], $params['end_date'], 'total', $passInPartnerUserId);

      $openedTab = isset($params['status']) ? $params['status'] : (!empty($params['manage']) ? $params['manage'] : '');
      $this->set('openedTab', $openedTab);

      $this->set('data', $data);
      $this->set('params', $params);
    }

    public function navigationCounters() {
      unset($_COOKIE['auth_token']);
      setcookie('auth_token', '', time()-3600, "/");

      $this->Cookie->delete('Dashboard.filters');
      $this->Auth->logout();
      return $this->redirect('/');
    }

    /* global ajax call for counters on navigation */
    public function getNavigationCounters() {
      $params        = $this->request->getQueryParams();
      $validStatuses = LendStatus::getPartnerStatuses(null, null);

      $user = $this->Auth->user();
      $partnerId = $user['partner_id'];
      $passInPartnerUserId = !empty($user['access_all_leads']) ? null : $user['partner_user_id'];
      if (empty($partnerId)) die('log in please');
      if (!$params['start_date'] OR !$params['end_date']) die('No date range provided');

      $data = $this->calculateCounters($partnerId, $passInPartnerUserId, $params['start_date'], $params['end_date']);

      // $totalArchived = count($this->Leads->sentLeadsUsingFilters($partnerId, $params, $passInPartnerUserId, 'countArchived'));
      // $data['Archived'] = $totalArchived;
      $openedTab = isset($params['status']) ? $params['status'] : (!empty($params['manage']) ? $params['manage'] : '');
      $this->setJsonResponse(array('data'=>$data, 'params'=>$params, 'opened_tab'=>$openedTab));
    }

    private function calculateCounters($partnerId, $passInPartnerUserId, $start_date, $end_date){
      $result = ['Leads'=>0,'Pending'=>0,'Attempting'=>0,'In Progress'=>0,'Rejected'=>0,'Settled'=>0,'Ready to Send'=>0, 'closed'=>0];

      $dbParams = array('le.partner_id'=>$partnerId, 'search_value'=>'', 'start_date'=>date('Y-m-d 00:00:00', strtotime($start_date)), 'end_date'=>date('Y-m-d 23:59:59', strtotime($end_date)));

      if(empty($this->Auth->user('access_all_leads'))){
        $dbParams['pul.partner_user_id'] = $this->Auth->user('partner_user_id');
      }

      $leads = $this->Leads->leadStatusCounter($dbParams,"ls.group_name");

      $partner_feature = $this->loadModel('PartnerFeatureFlags')->getFeatureFlag(['partner_id' => $partnerId]);
      $leadsFunction = empty($partner_feature['use_orm_lead_list']) ? 'readRecordWithPag' : 'getLeads';
      // $funded = $this->Leads->readRecordWithPag(
      $funded = $this->Leads->$leadsFunction(
        $dbParams,false,false,true,[
        'show_archive'=>true,
        'content_type'=>'all'
      ],'settled',true);
      $dbParams['le.partner_status_id'] = 55;
      $readyTosend = $this->Leads->leadStatusCounter($dbParams);
      if($leads){
        $counts = array_column($leads, 'count','group_name');
        $result = array_merge($result, $counts);
        $result['Settled'] = $funded[0]['count']; // override funded one
        $result['total'] = array_sum($counts);
        $result['Ready to Send'] = $readyTosend[0]['count'];
      }
      return $result;
    }


    private function checkSkippedLenders($prev_sales, $lead){
      $skip_lenders=array();
      $partner_model = $this->loadModel('Partners');
      $partner = $partner_model->getPartner(array('partner_id'=>$this->Auth->user('partner_id')));
      if(isset($partner['use_lend_priority']) AND !$partner['use_lend_priority']){
        // Get partner lender preference
        if($partner_priority = $partner_model->partner_lender_priority->getPartnerLenderPriority($this->Auth->user('partner_id'))){
          // Get latest priority of lender we sent
          $latest_sale_id = max(array_column($prev_sales, 'sale_id'));
          $latest_lender_id = $prev_sales[array_search($latest_sale_id, array_column($prev_sales, 'sale_id'))]['lender_id'];
          $point_of_latest = array_search($latest_lender_id, array_column($partner_priority, 'lender_id'));
          array_splice($partner_priority, $point_of_latest+1); // Remove outrange lenders from priorty lender list

          // Find any skipped lenders
          foreach($partner_priority as $priority){
            if(!in_array($priority['lender_id'], array_column($prev_sales, 'lender_id'))){
              $skip_lenders[] = $priority;
            }else{
              $prev_sales[array_search($priority['lender_id'], array_column($prev_sales, 'lender_id'))]['priority'] = $priority['priority'];
            }
          }
        }
      }

      if(!empty($skip_lenders)){
        $md_model = new MakeDecision;
        $md_model->init($lead, array('lead'=>true, 'lead_owners'=>true, 'bank_statements_analysis'=>true, 'lead_abn_lookup'=>true), null, true);
        $md_model->criteriaChecks();

        // Get failed lowest priority product's criteria
        foreach($skip_lenders as $sl_key=>$sl){
          if($product_keys = array_keys(array_column($md_model->products, 'lender_id'), $sl['lender_id'])){
            $tmp_priority = 0;
            foreach($product_keys as $pk){
              // If there is any passed product, it means this lender or product was inactive or brand new one. So stop here.
              if($md_model->products[$pk]['pass']==='passed'){
                $skip_lenders[$sl_key]['details'] = $md_model->products[$pk];
                break;
              }
              if($md_model->products[$pk]['pass']==='failed' AND $tmp_priority < $md_model->products[$pk]['priority']){
                $tmp_priority = $md_model->products[$pk]['priority'];
                $skip_lenders[$sl_key]['details'] = $md_model->products[$pk];
              }
            }
          }
        }

        // Set details for skipped lender
        foreach($skip_lenders as $sl_key=>$sl){
          $details = array(); $rev=array();
          $did_not_meet_bs_criteria = false;
          $did_not_meet_lender_criteria = false;
          if(!empty($sl['details']['failed'])){
            foreach($sl['details']['failed'] as $criteria){
              if($criteria['criteria']['data_source']==='bank_statements_analysis' AND $did_not_meet_bs_criteria){ continue; }

              if($criteria['criteria']['data_source']==='bank_statements_analysis'){
                  $did_not_meet_bs_criteria = true;
                  $details[] = 'Did not meet bank statement criteria';
              }elseif((int)$criteria['criteria']['factor_id']===4){
                $details[] = 'Excluded industry';
              }else{
                switch($criteria['criteria']['leads_field_name']){
                  case 'months_in_business':  $details[] = 'Time in business too short'; break;
                  case 'amount_requested':    $details[] = 'Amount requested too high'; break;
                  case 'loan_term_requested': $details[] = 'Request term too long'; break;
                  case 'purpose_id':          $details[] = 'Excluded purpose'; break;
                  case 'purpose_id':          $details[] = 'Excluded purpose'; break;
                  case 'sales_monthly':       $rev[] =     'Monthly requested too high'; break;
                  // case 'sales_annual':        $rev[] =     'Annual revenue too low'; break;
                  default:
                    if(!$did_not_meet_lender_criteria){
                      $details[] = 'Did not meet lender criteria';
                      $did_not_meet_lender_criteria = true;
                    }else{
                      continue 2;
                    }
                  break;
                }
              }
            }
          }
          if(!$did_not_meet_bs_criteria){
            $details[] = 'The bank account/s provided met the lender\'s bank statements criteria';
          }

          if(!empty($rev)){
            if(count($rev)===1) $details[]=$rev[0];
            else                $details[]='Revenue too low';
          }

          if(empty($details) OR (count($details)===1 AND in_array($details[0], array('The bank account/s provided met the lender\'s bank statements criteria', 'Did not meet bank statement criteria')))){
            $details[] = 'The information provided met the lender\'s lending criteria';
          }

          // Add it to previous sales list
          $prev_sales[] = array(
            'status'      => (!empty($sl['details']['pass']) AND $sl['details']['pass']==='passed') ? -2 : -1,
            'lender_name' => $sl['details']['lender_name'],
            'shorthand'   => $sl['details']['shorthand'],
            'priority'    => $sl['priority'],
            'details'    => $details,
          );
        }

        // Sort by priority
        usort($prev_sales, function ($a, $b) {
          if ((float)$a['priority']===(float)$b['priority']) return 0;
          return ((float)$a['priority']<(float)$b['priority'])?-1:1;
        });
      }
      return $prev_sales;
    }

    public function viewLenderStatusHistory(){
      $params = $this->request->getData();
      $leadId = $this->internal_auth->unhashLeadId($params['leadRef']);

      $history['history'] = $this->loadModel('LenderLeadUpdates')->getLenderStatusHistory($leadId, $params['lenderId']);
      $history['lender'] = $params['lenderName'] ? $params['lenderName'] : null;
      $lender = $this->loadModel('Lenders')->getLender(['lender_id'=>$params['lenderId']]);

      $this->set('lender', $lender);
      $this->set('history', $history);
      $histories = $this->render('/Element/Leads/history');
    }

    public function checkExistsLead($data=false, $lead_id=false)
    {
      if(!$data AND !$lead_id){
        $data = $this->request->getData();

        if (!$this->internal_auth->checkSignature($this->request->getQuery('auth_sig'), $data))
          return $this->setJsonResponse(array('success'=>false, 'message'=>'Invalid Signature'));

        if (count($data) === 1 && !empty($data['lead_id'])) {
          $lead_id = $data['lead_id'];
          $data = $this->Leads->getLeadDetails($lead_id);
        } elseif (!empty($data['lead']['lead_id'])) {
          $lead_id = $data['lead']['lead_id'];
        }
      }

      if (empty($data) && $lead_id) {
        $data = $this->Leads->getLeadDetails($lead_id);
      }

      if(empty($data['lead']['lead_id']) && $lead_id)
        $data['lead']['lead_id'] = $lead_id;

      $result = $this->Leads->checkExistsLead($data);
      // if($exists_lead = $this->Leads->checkExistsLead($data)){
      //   $this->loadModel('PartnerHonoured')->addPartnerHonoured(array('lend_lead_id'=>$exists_lead['lead_id'], 'partner_lead_id'=>$lead_id));
      //   $this->Leads->updateLead(array('lead_id'=>$lead_id, 'partner_status_id'=>2));
      // }
      return $this->setJsonResponse(
                        array('success' => $result === true ? true : false,
                              'error' => $result !== true ? $result: '')
                            );
    }


    public function affiliateSubmission(){
      $this->request->allowMethod(['post']);

      $sent = $this->request->getData();
      // data for send to lender
      $sendto = array();

      if(isset($sent['lead']['product_sub_type_id'])){
        $sent['lead']['product_type_id'] = $sent['lead']['product_sub_type_id'];
        unset($sent['lead']['product_sub_type_id']);
      }


      // NO LONGER USED SINCE WEB AFFILITE DOESN'T EXIST, BUT LEAVING HERE FOR NOW (LET'S REMOVE APPROX DECEMBER 2019):
      if(!empty($sent['from_partner'])) unset($sent['from_partner']);

      $sent['splitNames']                = $this->splitNames($sent['lead_owner']['full_name']);
      if (!empty($sent['lead']['ref'])) {
        $sent['lead']['id'] = $this->internal_auth->unhashLeadId($sent['lead']['ref']);
        unset($sent['lead']['ref']);
      }

      $sent['lead']['lead_id']           = !empty($sent['lead']['id']) ? $sent['lead']['id'] : NULL;
      $sent['lead']['amount_requested']  = preg_replace("/[^.0-9]/", "", $sent['lead']['amount_requested']);
      $sent['lead']['company_registration_date'] = !empty($sent['lead']['company_registration_date']) ? date('Y-m-d', strtotime($sent['lead']['company_registration_date'])) : null;
      //$sent['lead']['company_registration_date']     = $sent['lead']['company_registration_date']['yyyy'].'-'.$sent['lead']['company_registration_date']['mm'].'-01';
      $sent['lead']['industry_id']       = !empty($sent['industry_child']) ? $sent['industry_child'] : (!empty($sent['industry_parent']) ? $sent['industry_parent'] : NULL);
      $sent['lead_owner']['owner_id']    = !empty($sent['lead_owner']['id']) ? $sent['lead_owner']['id'] : NULL;
      $sent['lead_owner']['mobile']      = preg_replace("/[^0-9]/", "", $sent['lead_owner']['mobile']);
      $sent['lead_owner']['first_name']  = $sent['splitNames']['first_name'];
      $sent['lead_owner']['middle_name'] = $sent['splitNames']['middle_name'];
      $sent['lead_owner']['last_name']   = $sent['splitNames']['last_name'];
      $sent['lead_owner']['state']       = $sent['lead']['b_state'];

      if($sent['lead_owner']['security_asset']=='yes' AND !empty($sent['lead_owner']['asset_value']))
        $sent['lead_owner']['security_value'] = $sent['lead_owner']['asset_value'];
      else
        $sent['lead_owner']['security_value'] = NULL;

      // Set default industry and purpose ID:
      $sent['lead']['industry_id'] = !empty($sent['lead']['industry_id']) ? $sent['lead']['industry_id'] : 189;
      $sent['lead']['purpose_id'] = !empty($sent['lead']['purpose_id']) ? $sent['lead']['purpose_id'] : 8;

      // Check validation
      $fields = array(
        'lead' => array(
          'amount_requested', 'organisation_name', 'b_state', 'customer_type','purpose_id', 'sales_monthly'
          //'amount_requested', 'company_registration_date', 'organisation_name', 'b_state', 'sales_monthly', 'customer_type'
        ),
        'lead_owner' => array(
          'first_name', 'last_name', 'mobile', 'email',
        )
      );
      if (getenv('REGION', true) === 'nz') {
        $fields['lead'] = array_filter($fields['lead'], function ($f) {
          return $f !== 'b_state';
        });
        $fields['lead'] = array_values($fields['lead']);
      }

      // Check partner alias ref
      if(!empty($_COOKIE['lend_partner_alias_ref'])){
        if($partner_alias = $this->loadModel('PartnerAliases')->getPartnerAlias(['alias_ref'=>$_COOKIE['lend_partner_alias_ref']])){
          $sent['lead']['partner_alias_id'] = $partner_alias['partner_alias_id'];
          // Remove cookie
          unset($_COOKIE['lend_partner_alias_ref']);
          setcookie('lend_partner_alias_ref', null, -1, '/');
        }
      }

      if (isset($sent['abn']) && !$sent['abn']) array_push($fields['lead'], 'abn');
      if (isset($sent['lead']['purpose_id'])) array_push($fields['lead'], 'purpose_id');
      if (isset($sent['lead']['loan_term_requested'])) array_push($fields['lead'], 'loan_term_requested');
      if (isset($sent['industry_parent']) && !$sent['industry_parent']) array_push($fields['lead'], 'industry_parent');
      if (isset($sent['industry_child']) && !$sent['industry_child']) array_push($fields['lead'], 'industry_child');
      if (isset($sent['lead']['industry_detail']) && !$sent['lead']['industry_detail']) array_push($fields['lead'], 'industry_detail');
      //if (isset($sent['lead']['tax_overdue'])) array_push($fields['lead'], 'tax_overdue');
      // if (isset($sent['lead']['product_type_id'])) array_push($fields['lead'], 'product_type_id');
      
      //if (isset($sent['lead_owner']['dob'])) array_push($fields['lead_owner'], 'dob');

      if(!isset($sent['has_customer_type']) && isset($sent['lead']['customer_type'])) {
        $sent['lead']['customer_type'] = null; //set to null anyway
      }
      if(empty($sent['has_customer_type'])){
        $fields['lead'] = array_diff($fields['lead'], ['customer_type']);
      }
      unset($sent['has_customer_type']);
      if (empty($sent['lead']['customer_type'])) {
        unset($sent['lead']['customer_type']);
      }

      $leadValidation = new LeadValidation;
      $leadValidation->setRequiredFields($fields);

      // $leadValidation->acceptAllFields();
      $errors = $leadValidation->validate( $this->Leads->formatLeadData($sent, array('dont_toggle_phonenumbers'=>true)) ); // Check validation


      // $errors = $this->LoadModel('Api.Leads')->validateAffiliateLeadSubmission($sent);

      // Any errors, return them
      if (!empty($errors)){
        // Re organise error fields
        foreach($errors as $key=>$e){
          $errors[$key]['field'] = str_replace('owner[', 'lead_owner[', $e['field']);
          if ($e['field']==='owner[first_name]') {
            $errors[] = array('field' => 'lead_owner[full_name]', 'error' => 'Your Full Name is required');
          } else if ($e['field']==='owner[last_name]') {
            $errors[] = array('field' => 'lead_owner[full_name]', 'error' => 'Your Full Name is required, this looks like just your first name');
          } else if ($e['field']==='lead[company_registration_date]'){
            $errors[] = array('field' => 'lead[company_registration_date][yyyy]', 'error' => $e['error']);
          } else if ($e['field']==='lead[abn]') {
            $errors[$key]['field'] = 'abn';
          } else if ($e['field']==='lead[industry_parent]') {
            $errors[$key]['field'] = 'industry_parent';
          } else if ($e['field']==='lead[industry_child]') {
            $errors[$key]['field'] = 'industry_child';
          }
        }

        return $this->setJsonResponse(array(
          'success' => false,
          'errors'  => $errors,
          // 'sent'    => $sent
        ));
      }

      // Alright, so let's add the lead...

      // No longer needed:
      unset($sent['lead']['id']);
      unset($sent['lead_owner']['id']);
      unset($sent['lead_owner']['full_name']);
      unset($sent['lead_owner']['security_asset']);
      unset($sent['lead_owner']['asset_value']);

      // Get the partner_id and partner_user_id for this affiliate
      $affiliateDetails = $this->LoadModel('Partners')->getAffiliatePartnerDetails($sent['affiliate']['id']);

      // Format the data and add some extra fields:
      $data = $this->Leads->formatLeadData($sent);
      $data['lead']['partner_id']        = $affiliateDetails ? $affiliateDetails['partner_id'] : NULL;
      $data['lead']['partner_status_id'] = $affiliateDetails ? 1 : NULL;
      $data['lead']['status_id']         = 1;
      $data['lead']['is_abn_unknown']    = 1; // So it still sends
      $data['lead']['source']            = 'iFrame';

      if (!empty($data['lead']['sales_monthly'])) {
        $data['lead']['client_declared_sales_monthly'] = $data['lead']['sales_monthly'];
        unset($data['lead']['sales_monthly']);
      }

      if (!empty($data['lead']['loan_term_requested'])) {
        $get_months = TableRegistry::getTableLocator()->get('FrmLoanTerms');
        $query = $get_months->find()
        ->where([
            'loan_term_id' => $data['lead']['loan_term_requested'],
            'status' => 1
        ]);
        $result = $query->first();
        if ($result) {
            $data['lead']['loan_term_requested_months']   = $result['month_mapping'];
        } 
      } else {
        if (empty($data['lead']['product_type_id'])) {
          $data['lead']['loan_term_requested_months'] = 12;
        } else {
          $partner_product_types_table = TableRegistry::getTableLocator()->get('PartnerProductTypeEntity');
          $partner_product_type = $partner_product_types_table->get($data['lead']['product_type_id']);
          if (!empty($partner_product_type)) {
            $data['lead']['loan_term_requested_months'] = $partner_product_type->default_term;
          } else {
            $data['lead']['loan_term_requested_months'] = 12;
          }
        }
      }

      // check purpose_id
      if (!empty($data['lead']['purpose_id']) && !empty($data['lead']['product_type_id'])) {
        $partner_product_types_table = TableRegistry::getTableLocator()->get('PartnerProductTypeEntity');
        $purpose_product_table = TableRegistry::getTableLocator()->get('PurposeProductEntity');
        $partner_product_type = $partner_product_types_table->get($data['lead']['product_type_id']);
        if (!empty($partner_product_type->sub_product)) {
          $product_type_id = $partner_product_type->sub_product;
        } else {
          $product_type_id = $partner_product_type->product_type_id;
        }
        $purpose_products = $purpose_product_table->find('all')->where(['purpose_id' => $data['lead']['purpose_id']])->toArray();
        if (array_search($product_type_id, array_column($purpose_products, 'product_type_id')) === false) {
          Log::info('Simple Iframe: Invalid purpose and product type so making purpose_id NULL from here.');
          $data['lead']['purpose_id'] = null;
        }
      }

      if (!$data['lead_owner']['owner_id']) {
        if (empty($data['lead']['lead_id'])) {
          unset($data['lead']['lead_id']);
        }



        $data['lead']['lead_type'] = 'commercial';
        $owner = $data['lead_owner'];

        $completeData = array_merge($data['lead'], [
            'owners_all' => [
                array_merge(
                    [
                        'point_of_contact' => 1,
                        'status' => 'active',
                    ],
                    $owner
                )
            ]
        ]);
        
        $lead_id = $this->Leads->addLead($completeData);
        $sendto['lead_id'] = $lead_id;

        if (!empty($sent['abnlookup'])) {
          $sent['abnlookup']['lead_id'] = $lead_id;
          $lead_abn_lookup_table = TableRegistry::getTableLocator()->get('LeadAbnLookupEntity');
          $lead_abn_lookup = $lead_abn_lookup_table->newEntity($sent['abnlookup']);
          $lead_abn_lookup_table->save($lead_abn_lookup);
        } elseif (!empty($sent['nzbnlookup'])) {
          $nzbn_lookup_table = TableRegistry::getTableLocator()->get('NzbnLookupEntity');
          $nzbn_lookup_entity = $nzbn_lookup_table->find('all')->where(['nzbn' => $sent['nzbnlookup']['nzbn']])->first();
          if (!empty($nzbn_lookup_entity)) {
            $nzbn_lookup_table->patchEntity($nzbn_lookup_entity, $sent['nzbnlookup']);
          } else {
            $nzbn_lookup_entity = $nzbn_lookup_table->newEntity($sent['nzbnlookup']);
          }
          $nzbn_lookup_table->save($nzbn_lookup_entity);

          $this->Leads->updateLead(['lead_id' => $lead_id, 'nzbn_id' => $nzbn_lookup_entity->id]);
        }

        // Insert into applicants table
        $this->LoadModel('Applicants')->createApplicant(array('lead_id'=>$lead_id, 'email'=>$data['lead_owner']['email']));

        // Add partner user leads
        if(!empty($sent['affiliate']['broker_assigned'])){
          if($partner_user = $this->loadModel('PartnerUsers')->getPartnerUser(['partner_user_ref'=>$sent['affiliate']['broker_assigned']])){
            $partnerUserLeadsTable = TableRegistry::getTableLocator()->get('PartnerUserLeadsEntity');
            $partnerUserLead = $partnerUserLeadsTable->newEntity([
              'partner_user_id' => $partner_user['partner_user_id'],
              'status' => 'ACCESS',
              'lead_id' => $lead_id
            ]);
            $partnerUserLeadsTable->save($partnerUserLead);
            $affiliateDetails['partner_user_id'] = $partner_user['partner_user_id'];
          }
        }

        // Set history message
        $history_message = 'Lead Added';

        // Send Client Notification
        $partnerDetail = $this->getTableLocator()->get('PartnerEntity')
            ->find()
            ->where(['partner_id' => $affiliateDetails['partner_id']])
            ->first();

        if ($affiliateDetails && ($partnerDetail->status_system !== 'manual')) {
          $this->LoadModel('PartnerNotifications')->sendPartnerNotifications($affiliateDetails['partner_id'], 'SendToWAAppl1', $lead_id);
        }

      } else if ($data['lead_owner']['email']) {
        $lead_id = $data['lead']['lead_id'];
        $sendto['lead_id'] = $lead_id;

        // Update lead table
        $this->Leads->updateLead($data['lead']);

        // Update lead_owners table
        $this->Leads->lead_owner->updateLeadOwner($data['lead_owner']);

        // Set history message
        $history_message = 'Lead Updated';
      }

      // Insert into partner_lead_history
      if ($affiliateDetails)
        $this->LoadModel('PartnerLeadHistory')->addPartnerLeadHistory(array('partner_id'=>$affiliateDetails['partner_id'], 'partner_user_id'=>$affiliateDetails['partner_user_id'], 'lead_id'=>$lead_id, 'history_detail'=>$history_message));

      $lend_internal_auth = new LendInternalAuth;
      $response = (new \Cake\Http\Client)->post(getenv('DOMAIN_CRM').'/endpoints/send-web-affiliate-lead-submission/?auth_sig='.$lend_internal_auth->createSignature($sendto), $sendto);
      if(!$response->json['success']){
        $this->Partners->postToSlack(":warning: Cannot send *Lead {$lead_id} (".($lend_internal_auth->hashLeadId($lead_id)).")* `".$data['lead']['partner_id']."` to lender. Cannot communicate with Lend Star.\n ```".$response->json['message']."```", 'lend_errors');

        // Add a background job to resend
        $sendto['partner_id'] = $data['lead']['partner_id'];
        $this->loadModel('BackgroundJobs')->addBackgroundJob([
          'lead_id'       => $lead_id,
          'job_type'      => 're_send_iframe_lead',
          'ref_id'        => json_encode($sendto),
          'class_name'    => 'Partners',
          'function_name' => 're_send_iframe_lead',
          'created'       => date('Y-m-d H:i:s'),
        ]);
      }

      // Notify us to take some possible action...
      if (!$affiliateDetails)
        $this->Partners->postToSlack(":eyes: A lead (".$lead_id.") has been submitted to the `/partners/affiliate-submission` API however there was no valid Affiliate ID.", 'broker-leads'.(in_array($data['lead']['partner_id'], array(1,3,4,8))?'-lend':''));

      $this->setJsonResponse(array(
        'success' => true,
        // 'sent'    => $sent
      ));
    }

    // removing lead from trash icon popup ok
    public function removeLeads() {
      try{
        $leadRefs        = $this->request->getData('leadRefs'); // array
        $partnerId      = $this->Auth->user('partner_id');
        $partnerUserId  = $this->Auth->user('partner_user_id');

        if (empty($leadRefs) OR empty($partnerId) OR empty($partnerUserId))
          throw new \Exception("Not allow to access this page.");

        $leads = $this->Leads->findLeadsByLeadRefs($leadRefs);
        if(empty($leads))
          throw new \Exception("Can't find any leads.");

        //check if partner is on manual status and has kanban access
        $partner = TableRegistry::getTableLocator()->get('PartnerEntity')->get($partnerId, [
          'fields' => [
            'partner_id', 'status_system'
          ],
          'contain' => [
            'PartnerFeatureFlagEntity' => ['fields' => ['partner_id', 'access_to_kanban_v2']],
          ]
        ]);
        $partnerHasKanban = (($partner->status_system === "manual") && !empty($partner->feature_flag->access_to_kanban_v2));

        $errors=[];
        foreach ($leadRefs as $leadRef) {
          $leadId = $this->internal_auth->unhashLeadId($leadRef);
          $key = array_search($leadId, array_column($leads, 'lead_id'));
          if($key===false){
            $errors[] = "Can't find a lead {$leadRef} from our system.";
            continue;
          }
          if($leads[$key]['partner_id'] != $partnerId){
            $errors[] = "You are not allow to update a lead {$leadRef}.";
            continue;
          }

          if($partnerHasKanban){
            KanbanHelper::removeLead(null, $leadRef);
            SocketHelper::kanbanRemoveLead($leadId, $leadRef, $leads[$key]['man_status_id'], $leads[$key]['product_type_id']);
          }
          $this->loadModel('PartnerLeadHistory')->addPartnerLeadHistory(array('partner_id'=>$partnerId, 'partner_user_id'=>$partnerUserId, 'lead_id'=>$leadId,'history_detail'=>'Trashed'));
          $this->Leads->updateLead(array('lead_id'=>$leadId, 'partner_id'=>Configure::read('Lend.LEND_TRASHED_LEAD_PARTNER_ID')));


          $commissionTable = TableRegistry::getTableLocator()->get('PartnerCommissionsEntity');
          $commissionTable->updateAll(
            ['partner_id' => Configure::read('Lend.LEND_TRASHED_LEAD_PARTNER_ID')],
            ['lead_id' => $leadId]
          );

          // Revoke leads
          $partnerUserLeadsTable = TableRegistry::getTableLocator()->get('PartnerUserLeadsEntity');
          $accessRows = $partnerUserLeadsTable->find()
            ->where([
              'lead_id' => $leadId,
              'status' => 'ACCESS'
            ])
            ->all();
            if ($accessRows->count() > 0) {
              $entitiesData = [];
              $now = date('Y-m-d H:i:s');
              foreach ($accessRows as $accessRow) {
                $entitiesData[] = [
                  'partner_user_lead_id' => $accessRow->partner_user_lead_id,
                  'status' => 'REVOKED',
                  'updated'=> $now
                ];
              }
              $entitiesToUpdate = $partnerUserLeadsTable->patchEntities($accessRows->toArray(), $entitiesData);
              $partnerUserLeadsTable->saveMany($entitiesToUpdate);
            }

          // Remove converted_lead_id from lender_match_requests table:
          $lenderMatchRequestsTable = TableRegistry::getTableLocator()->get('LenderMatchRequestEntity');
          $lenderMatchRequestsTable->updateAll(
            ['converted_lead_id' => null],
            ['partner_id' => $partnerId]
          );

        }

        $this->setJsonResponse(['success' => $leadRefs]);
      }catch(\Exception $e){
        $this->setJsonResponse(['success' => false, 'message'=>$e->getMessage()]);
      }
    }

    // archiving lead from trash icon popup ok
    public function archiveLeads() {
      try{
        $leadIds        = $this->request->getData('leadRefs'); // array
        $unarchive      = $this->request->getData('unarchived');
        $partnerId      = $this->Auth->user('partner_id');
        $partnerUserId  = $this->Auth->user('partner_user_id');

        if (empty($leadIds) OR empty($partnerId) OR empty($partnerUserId))
          throw new \Exception("Not allow to access this page.");

        $leads = $this->Leads->findLeadsByLeadRefs($leadIds);
        if(empty($leads))
          throw new \Exception("Can't find any leads.");

        $errors=[];
        foreach ($leadIds as $leadRef) {
          $leadId = $this->internal_auth->unhashLeadId($leadRef);
          $key = array_search($leadId, array_column($leads, 'lead_id'));
          if($key===false){
            $errors[] = "Can't find a lead {$leadRef} from our system.";
            continue;
          }
          if($leads[$key]['partner_id'] != $partnerId){
            $errors[] = "You are not allow to update a lead {$leadRef}.";
            continue;
          }
          $this->loadModel('PartnerLeadHistory')->addPartnerLeadHistory(array('partner_id'=>$partnerId, 'partner_user_id'=>$partnerUserId, 'lead_id'=>$leadId,'history_detail'=>($unarchive ? 'Unarchived' : 'Archived')));
          $this->Leads->updateLead(array('lead_id'=>$leadId, 'is_archived'=>($unarchive ? 0 : 1)));
        }

        $this->setJsonResponse(['success' => true]);
      }catch(\Exception $e){
        $this->setJsonResponse(['success' => false, 'message'=>$e->getMessage()]);
      }
    }

  /**
   * Request payload in json : {'lead_id' : xxxx, 'send_type' : 'Auto' or 'Manual'}
   */
  public function toggleSendType() {
    $sent = $this->request->getData();

    if (isset($sent['leadRef']))
      $sent['lead_id'] = $this->internal_auth->unhashLeadId($sent['leadRef']);

    if (!isset($sent['lead_id']) || !isset($sent['send_type'])) {
      return $this->setJsonResponse(['success' => false, 'message' => 'Invalid parameters', 'sent' => $sent]);
    }

    $partnerId = (int)$this->Auth->user('partner_id');
    $accountType = $this->Auth->user('account_type');
    if (!$partnerId && $accountType !== 'Lend Staff') {
      return $this->setJsonResponse(['success' => false, 'message' => 'Unauthorized Request', 'sent' => $sent]);
    }

    $lead = $this->Leads->getLead(['lead_id' => $sent['lead_id']]);
    if (((int)$lead['partner_id']) !== $partnerId && $accountType !== 'Lend Staff') {
      return $this->setJsonResponse(['success' => false, 'message' => "You have no access to this lead"]);
    } else if (!$this->Auth->user('access_all_leads')
        && !$this->loadModel('PartnerUserLeads')->existingUserLead($sent['lead_id'], $this->Auth->user('partner_user_id'), ['Access'])
        && $accountType !== 'Lend Staff'
      ) {
      return $this->setJsonResponse(['success' => false, 'message' => "You have no access to this lead"]);
    }

    $sent['send_type'] = strtolower($sent['send_type']) === 'auto' ? 'Auto' : 'Manual';

    if (!$lead) {
      return $this->setJsonResponse(['success' => false, 'message' => "Could not find the lead"]);
    }

    if ($lead['status_id'] === 17) {
      return $this->setJsonResponse(['success' => false, 'message' => "The lead has been already sent to a lender"]);
    }

    $percentage = $this->Leads->checkPercentage(['lead' => $lead]);

    // If lead percentage reaches 100%, can't to toggle to Manual because potentially that leads might be sent by 'Auto' at that moment.
    if ($sent['send_type'] === 'Manual' && $percentage['current_percentage'] === 100.0) {
      return $this->setJsonResponse(['success' => false, 'message' => "Can't toggle to 'Manual' when lead percentage reaches 100%"]);
    }

    $original = $this->Leads->getLeadDetails($sent['lead_id']);


    if (!empty($sent['product_type_id']) && !empty($lead['product_type_id']) ) {
      $this->loadModel('LeadTrackChanges')->addNewTrackChanges($sent['lead_id'], array('field' => 'product_type_id', 'before' => $lead['product_type_id'], 'after' => $sent['product_type_id'], 'who' => 'Partner'), null);
      $this->loadModel('LeadTrackChanges')->addNewTrackChanges($sent['lead_id'], array('field' => 'send_type', 'before' => $sent['send_type'] == 'Manual' ? 'Auto' : 'Manual', 'after' => $sent['send_type'], 'who' => 'Partner'), null);
      $this->Leads->updateLead(['lead_id' => $sent['lead_id'], 'send_type' => $sent['send_type'], 'product_type_id' => $sent['product_type_id']]);
    } else {
      $this->loadModel('LeadTrackChanges')->addNewTrackChanges($sent['lead_id'], array('field' => 'send_type', 'before' => $sent['send_type'] == 'Manual' ? 'Auto' : 'Manual', 'after' => $sent['send_type'], 'who' => 'Partner'), null);
      $this->Leads->updateLead(['lead_id' => $sent['lead_id'], 'send_type' => $sent['send_type']]);
    }

    unset($sent['leadRef']);
    $this->LoadModel('PartnerLeadEdits')->saveAnEdit(
      array(
        'lead_id'=>$sent['lead_id'],
        'partner_user_id'=>(!empty($this->Auth->user('partner_user_id'))?$this->Auth->user('partner_user_id'):null)
      ),
      $original,
      array('lead'=>$sent)
    );

    return $this->setJsonResponse(['success' => true]);
  }

  public function toggleField() {
    $sent = $this->request->getData();

    if (!isset($sent['leadRef']) || count($sent) == 1) {
      return $this->setJsonResponse(['success' => false, 'message' => 'Invalid parameters', 'sent' => $sent]);
    }
    $partnerId = (int)$this->Auth->user('partner_id');
    $accountType = $this->Auth->user('account_type');
    if (empty($partnerId) && $accountType !== 'Lend Staff') {
      return $this->setJsonResponse(['success' => false, 'message' => 'Unauthorized Request', 'sent' => $sent]);
    }
    if (array_key_exists('call_me_first', $sent)) {
      $sent['call_me_first'] = !empty($sent['call_me_first']) ? '1' : '0';
    }

    $sent['lead_id'] = $this->internal_auth->unhashLeadId($sent['leadRef']);
    unset($sent['leadRef']);
    $lead = $this->Leads->getLead(['lead_id' => $sent['lead_id']]);
    if (!$lead) {
      return $this->setJsonResponse(['success' => false, 'message' => "Could not find the lead"]);
    }

    if ($lead['status_id'] === 17) {
      return $this->setJsonResponse(['success' => false, 'message' => "The lead has been already sent to a lender"]);
    }

    if (((int)$lead['partner_id']) !== $partnerId && $accountType !== 'Lend Staff') {
      return $this->setJsonResponse(['success' => false, 'message' => "You have no access to this lead"]);
    } else if (!$this->Auth->user('access_all_leads')
        && !$this->loadModel('PartnerUserLeads')->existingUserLead($sent['lead_id'], $this->Auth->user('partner_user_id'), ['Access'])
        && $accountType !== 'Lend Staff') {
      return $this->setJsonResponse(['success' => false, 'message' => "You have no access to this lead"]);
    }

    $original = $this->Leads->getLeadDetails($sent['lead_id']);
    $success = $this->Leads->updateLead($sent);
    $this->LoadModel('PartnerLeadEdits')->saveAnEdit(
      array(
        'lead_id'=>$sent['lead_id'],
        'partner_user_id'=>(!empty($this->Auth->user('partner_user_id'))?$this->Auth->user('partner_user_id'):null)
      ),
      $original,
      array('lead'=>$sent)
    );

    return $success == true
          ? $this->setJsonResponse(['success' => true])
          : $this->setJsonResponse(['success' => false, 'message' => 'Error occurs, please try later']);
  }


  /**
   * Request payload in json : {'lead_id' : xxxx, 'product_id' : xxxx}
   */

  public function sendToLender() {

    $sent = $this->request->getData();

    //keep this in till staging test
    Log::write('debug', 'sendToLender: ' . json_encode($sent));

    $user = $this->Auth->user();
    if (!empty($user['partner_user_id'])) {
      $sent['partner_user_id'] = $user['partner_user_id'];
    }

    if (isset($sent['lead_ref'])) $sent['lead_id'] = $this->internal_auth->unhashLeadId($sent['lead_ref']);

    if (!isset($sent['lead_id']) || !isset($sent['product_id'])) {
      return $this->setJsonResponse(['success' => false, 'message' => 'Invalid parameters', 'sent' => $sent]);
    }

    $isRejectedOverProductAvaiable = $this->Leads->isPartnerStatus($sent['lead_id'], 'Rejected - Other Products Available', true);
    $hasProductTypeId = $this->Leads->hasProductTypeId($sent['lead_id'], $excluding = 11);



    if ($this->Auth->user('partner_id') != 2) { // if not demo account
      $accountLevelLeadAutoSend = false;
      if (!empty($sent['account_ref'])) {
        $lead = $this->Leads->getLead(array('lead_id' => $sent['lead_id']));
        if($lead['send_type'] == "Auto"){
          $accountLevelLeadAutoSend = true;
          $this->Leads->updateLead(array('lead_id' => $sent['lead_id'], 'send_type' => "Manual") );
        }
      }
      
      $authSig = (new LendInternalAuth())->createSignature($sent);
      $response = (new Client())->post(
        getenv('DOMAIN_CRM').'/api/partners/manual-send-to-lender',
        json_encode($sent),
        ['headers' => ['Content-Type' => 'application/json', 'Webhook-Signature' => $authSig], 'timeout' => 60]
      );

      // last API error for the product_id 
      // onyl for tha account level lead //
      if (!empty($sent['account_ref'])) {
        $hashids    = new Hashids('partner_accounts', 7);
        $account_id = $hashids->decode($sent['account_ref'])[0];
        if (!$response->json['success']) {
          $this->loadModel('Leads')->tagLeadAsFail(['account_id'=>$account_id,'lead_id'=>$sent['lead_id']]);
        }else{
          // moved to star - leave this comment till test in staging //
          // $this->loadModel('Leads')->deleteLeadTagAsFailed(['account_id'=>$account_id,'lead_id'=>$sent['lead_id']]);
          if($accountLevelLeadAutoSend === true){
            $this->Leads->updateLead(array('lead_id' => $sent['lead_id'], 'send_type' => "Auto") );
          }
        }
      }


      if (!$response->json['success']) {

        $display_error = $response->json['error'];

        $leadSentLogsTable = TableRegistry::get('LeadSentLogEntity');
        $leadSentLog = $leadSentLogsTable->find()
        ->where([
            'lead_id' => $sent['lead_id'],
            'product_id' => $sent['product_id'],
            'status' => 'failed'
        ])
        ->first();
        if( !empty($leadSentLog) ){
          $display_error = $leadSentLog['response'];
        }
        return $this->setJsonResponse(['success' => false, 'message' => $display_error]);
      }

      // Update `con_page_status.con_lender_pricing_status` to `submitted` if it is a consumer lead:
      $con_page_status_table = TableRegistry::getTableLocator()->get('ConPageStatusEntity');
      $con_page_status = $con_page_status_table->find('all')->where(['lead_id' => $sent['lead_id']])->first();
      if (!empty($con_page_status)) {
        $con_page_status_table->patchEntity($con_page_status, [
          'con_lender_pricing_status' => 'submitted'
        ]);
        $con_page_status_table->save($con_page_status);
      }

      $sale = $this->loadModel('Sales')->getSale(['lead_id'=>$sent['lead_id'], 'product_id'=>$sent['product_id']], ['sale_id'=>'desc']);
    }
    if ($this->Leads->isLeadPartnerHonoured($sent['lead_id'])) {
      //sent it to lenders, then remove records in partner_honoured table here.
      $this->Leads->removeFromPartnerHonouredWherePossbile($sent['lead_id']);
    }
    $return = ['success' => true, 'sale_ref' => $sale['sale_ref']];

    //check if the lead status is 'Rejected - Other Products Available' and has product_type_id set other than 'Other / Not Sure'
    //then pass additional info in return to JS
    //JS will handle the additional info
    if ($isRejectedOverProductAvaiable && $hasProductTypeId) {
      // product_type_id = 11 means 'Other / Not Sure'
      $return['change_product_type_id_to_be'] = 11;
    }
    return $this->setJsonResponse($return);
  }


  public function sendToInterMediaryLender($sent_intermedia_settings = null) {
   
     $sent['lead_id']    = $sent_intermedia_settings['original_lead_id'];
     $sent['product_id'] = $sent_intermedia_settings['product_id'];

     $user = $this->Auth->user();

     if (!empty($user['partner_user_id'])) {
       $sent['partner_user_id'] = $user['partner_user_id'];
     }

     if (!isset($sent['lead_id']) || !isset($sent['product_id'])) {
      return $this->setJsonResponse(['success' => false, 'message' => 'Invalid parameters', 'sent' => $sent]);
    }

    $isRejectedOverProductAvaiable = $this->Leads->isPartnerStatus($sent['lead_id'], 'Rejected - Other Products Available', true);
    $hasProductTypeId = $this->Leads->hasProductTypeId($sent['lead_id'], $excluding = 11);
 
    if ($this->Auth->user('partner_id') != 2) { 
      $authSig = (new LendInternalAuth())->createSignature($sent);

      $response = (new Client())->post(
        getenv('DOMAIN_CRM').'/api/partners/manual-send-to-lender',
       json_encode($sent),
       ['headers' => ['Content-Type' => 'application/json', 'Webhook-Signature' => $authSig], 'timeout' => 60]
     ); 

      if (!$response->json['success']) {
        return $this->setJsonResponse(['success' => false, 'message' => $response->json['error']]);
      }

      $sale = $this->loadModel('Sales')->getSale(['lead_id'=>$sent['lead_id'], 'product_id'=>$sent['product_id']], ['sale_id'=>'desc']);
    }

    if ($this->Leads->isLeadPartnerHonoured($sent['lead_id'])) {
      $this->Leads->removeFromPartnerHonouredWherePossbile($sent['lead_id']);
    }
     
    $return = ['success' => true, 'sale_ref' => $sale['sale_ref']];

    if ($isRejectedOverProductAvaiable && $hasProductTypeId) {
      $return['change_product_type_id_to_be'] = 11;
    }
    
    return $return;
  }

  private function getMinLoanAmount($product_id) {
    $productCriteria = $this->loadModel('LenderProduct')->getLenderProductCriteria($product_id);

		$productLowerAmount = 0;
		//Loan Amount - factor in lender_factors table
		foreach ($productCriteria as $c) {
		  if ((int)$c['factor_id'] !== 6) continue;
			if ($c['operator'] === 'between') {
				$productLowerAmount = intval($c['value_1']);
			} elseif ($c['operator'] === '>' || $c['operator'] === '>=') {
				$productLowerAmount = intval($c['value_1']);
			} elseif ($c['operator'] === '<' || $c['operator'] === '<=') {}
		}
		return $productLowerAmount;
  }

  public function exportAsPdf($lead_ref=false, $pdf_type=false, $template=false){
    if(empty($lead_ref))
      throw new \Exception("Missing lead ref.");
    if(empty($pdf_type))
      throw new \Exception("Missing pdf type.");
    if(empty($template))
      throw new \Exception("Missing template.");

    // Check this lead is belong to this partner
    $lead_id = (new LendInternalAuth)->unhashLeadId($lead_ref);

    if($pdf_type == 'export-lead')
      $payload = array(
        'lead_ref' => $lead_ref,
        // 'expires' => time() + (24 * 60 * 60); // TODO: Expire 24hrs from now
        'finance_type' => $template
      );
    else
      $payload = array(
      'lead_ref' => $lead_ref,
      // 'expires' => time() + (24 * 60 * 60); // TODO: Expire 24hrs from now
      'consent_type' => $template
    );

    // Append the logo to payload if not null
    if($logo){
      $payload['logo'] = $logo;
    }

    $combined  = json_encode($payload) . getenv('PDF_SECRET');
    $signature = base64_encode(hash_hmac('sha256', $combined, getenv('PDF_SECRET'))); // to base64

    // Construct the URL
    $url = getenv('DOMAIN_PDF').'/'.$pdf_type.'?'.http_build_query(array(
      'payload' => json_encode($payload),
      'signature' => $signature
    ));

    // and redirect to it...
    return $url;
  }

  private function extractBlaclistedLenders($lenders){
    $blacklist = [];
    if(empty($lenders)) return $blacklist;
    foreach($lenders as $lender){
      $blacklist[] = $lender['lender_id'];
    }
    return($blacklist);
  }

  public function updateLendSizeEstimate() {

    $leadId =  $this->request->getData('lead_id');
    $partner_id = $this->request->getData('partner_id');

    list($availableProducts, $availableOtherProducts) = $this->collectAvailableProducts($leadId, $partner_id);
    if(empty($this->request->getData('do_not_check_other_product')))
      $availableProducts = array_merge($availableProducts,$availableOtherProducts);
    $makeDecision = new MakeDecision;
    //add priorities based on partner id
    $availableProducts = $makeDecision->setPartnerPreferLender($partner_id, $availableProducts);
    //add priorities based on
    $lendSize = 0;
    $minLoanAmount = false;
    $productsUsed = [];//just for debug

    $blacklisted = $this->extractBlaclistedLenders(TableRegistry::get('PartnerLenderExcludes')->getPartnerLenderExcludes($partner_id));

    foreach($availableProducts as $product) {
      if(in_array($product['lender_id'],$blacklisted)) continue;
      if(!empty($product['LendSize']['loan_amount'])) {
        $product['min_loan_amount'] = $this->getMinLoanAmount($product['product_id']);
        $product['priority_lenderproduct'] = TableRegistry::get('LenderProduct')->getLenderProductPriority($product['product_id']);
        $product['priority_partnerid'] = !empty($product['priority']) ? $product['priority'] : false; //this is from preferred lenders by partner id
        unset($product['priority']);//just for cleanup. Vars may be inspected later for debug

        $productsUsed[] = $product;
        if($minLoanAmount == false || $product['min_loan_amount'] < $minLoanAmount){
          $minLoanAmount = $product['min_loan_amount'];
        }
      }
    }

    //sort first on partner priority, then on priority_lenderproduct
    usort($productsUsed, function($a, $b)
    {
      //account for empty values
      if(empty($a['priority_partnerid']) && $a['priority_partnerid'] !== 0) $a['priority_partnerid'] = 10000;
      if(empty($b['priority_partnerid']) && $b['priority_partnerid'] !== 0) $b['priority_partnerid'] = 10000;
      if(empty($a['priority_lenderproduct']) && $a['priority_lenderproduct'] !== 0) $a['priority_lenderproduct'] = 10000;
      if(empty($b['priority_lenderproduct']) && $b['priority_lenderproduct'] !== 0) $b['priority_lenderproduct'] = 10000;

      if ($a['priority_partnerid'] == $b['priority_partnerid']) {
        return $a['priority_lenderproduct'] - $b['priority_lenderproduct'];
      }
      return $a['priority_partnerid'] - $b['priority_partnerid'];
    });


    if(!empty($productsUsed)) {
      $lendSize = $productsUsed[0]['LendSize']['loan_amount'];
    }

    if($minLoanAmount == false) $minLoanAmount = 1;

    $this->Leads->updateLead(array('lead_id' => $leadId , 'lendsize_estimate' => $lendSize));   //if has available products, then update status to `Ready to Send`

    $this->setJsonResponse(['success' => true, 'lendsize_estimate' => $lendSize, 'min_loan_amount' =>$minLoanAmount]);
  }

  /**
   * Request payload in json : {'leadRef' : xxxx}
   */
  public function collectAvailableProducts($leadId=false, $pid = false) {
    $lendSize = new LendSize;

    $partnerIdsInWhileListOfLendSize = null;// [1, 2]; //change to null if pre-approval applies for all partners
    $demoPartnerId = 2; //change to null if pre-approval applies for all partners

    //using backtrace to make sure only an externaly unkown function can call me with $_GET vars
    $lendSizeEstimate = debug_backtrace()[1]['function'] == 'updateLendSizeEstimate';

    if($lendSizeEstimate){
      $sent['lead_id'] = $leadId;
      $partnerId = $pid;
    }else{
      $sent = $this->request->getData();
      $partnerId = $this->Auth->user('partner_id');
    }

    if (isset($sent['leadRef']))
      $sent['lead_id'] = $this->internal_auth->unhashLeadId($sent['leadRef']);

    if (!isset($sent['lead_id'])) {
      return $this->setJsonResponse(['success' => false, 'message' => 'Invalid parameters']);
    }

    $lead = $this->Leads->getLeadDetails($sent['lead_id']);

    if($lead['lead']['on_not_call_list'])
      return $this->setJsonResponse(['success' => false, 'message' => 'On Do Not Call List']);

    if (!$lead) {
      return $this->setJsonResponse(['success' => false, 'message' => "Could not find the lead"]);
    }
    $this->set('leadRef', $lead['lead']['lead_ref']);

    // Get partner product type details:
    if(!empty($lead['lead']['product_type_id'])){
      $partner_product_type = $this->loadModel('PartnerProductTypes')->getPartnerProductType(['product_type_id'=>$lead['lead']['product_type_id']], true);
      $this->set('partner_product_type', $partner_product_type);
    }

    // Check held for review
    if($lead['lead']['partner_status_id']==5){
      return $this->setJsonResponse(['success' => false, 'message' => "Held for review"]);
    }
    if($lead['lead']['partner_status_id']==2 || $this->Leads->isLeadPartnerHonoured($lead['lead']['lead_id'])){
      $confirmation = "This is a Partner Honoured Lead, are you sure that you want to send it to lenders manually?";
      $this->set('confirmation', $confirmation);
    }

    $lead['abnlookup'] = $lead['lead']['organisation_name'];
    $lead['abn']       = $lead['lead']['abn'];
    $errors = $this->_leadValidationToSendLead($lead, false);

    if(!empty($errors)) {
      return $this->setJsonResponse(['success'=>false, 'message'=>$errors, 'sent'=>$sent]);
    }

    $makeDecisionV2 = new MakeDecisionV2;
    $products = $makeDecisionV2->run(['lead'=>$lead['lead'], 'lead_owners'=>array($lead['lead_owner']), 'bank_statements_analysis'=>$lead['bank_statements_analysis']], array('lead'=>true, 'lead_owners'=>true, 'bank_statements_analysis'=>true, 'lead_abn_lookup'=>true, 'partners'=>!empty($lead['lead']['partner_id']), 'lead_asset_finance'=>!empty($lead['lead_asset_finance'])), null, false);
    $products = $makeDecisionV2->checkPossibleDupe($products);
    $other_products = $makeDecisionV2->checkOtherProducts($products);
    $products = $makeDecisionV2->rank($products, $lead['lender']);
    $products = $makeDecisionV2->lender($products, $lead);
    $other_products = $makeDecisionV2->rank($other_products, $lead['lender']);
    $other_products = $makeDecisionV2->lender($other_products, $lead);

    // $makeDecision = new MakeDecision;
    // $makeDecision->init(['lead'=>$lead['lead'], 'lead_owners'=>array($lead['lead_owner']), 'bank_statements_analysis'=>$lead['bank_statements_analysis']], array('lead'=>true, 'lead_owners'=>true, 'bank_statements_analysis'=>true, 'lead_abn_lookup'=>true), null, false);
    // $products_v2 = $makeDecisionV2->run(['lead'=>$lead['lead'], 'lead_owners'=>array($lead['lead_owner']), 'bank_statements_analysis'=>$lead['bank_statements_analysis']], array('lead'=>true, 'lead_owners'=>true, 'bank_statements_analysis'=>true, 'lead_abn_lookup'=>true), null, false);
    // $makeDecision->criteriaChecks();
    // $makeDecisionV2->checkResults($sent['lead_id'], $products_v2, $makeDecision->products, 'run');
    // $makeDecision->checkPossibleDupe();
    // $other_products = $makeDecision->checkOtherProducts();

    // $products = $makeDecision->rankProductByPriority();
    // $products_v2 = $makeDecisionV2->rank($products_v2, $lead['lender']);
    // $makeDecisionV2->checkResults($l['lead_id'], $products_v2, $products, 'rank');

    // $products = $makeDecision->setPartnerPreferLender($partnerId, $products);
    // $products_v2 = $makeDecisionV2->lender($products_v2, $lead);
    // $makeDecisionV2->checkResults($l['lead_id'], $products_v2, $products, 'lender');
    // $other_products = $makeDecision->rankProductByPriority($other_products);
    // $other_products = $makeDecision->setPartnerPreferLender($partnerId, $other_products);


    if (empty($partnerIdsInWhileListOfLendSize) || in_array((int)$partnerId, $partnerIdsInWhileListOfLendSize)) {
      if (!empty($lead['bank_statements_analysis']) || !empty($lead['lead']['monthly_expenses'])){
        $products = $lendSize->calculateLendSize($lead, $products);
        $other_products = $lendSize->calculateLendSize($lead, $other_products);
      }
    }
    $passedProducts = array_filter($products, function($product) {
      return $product['pass'] === 'passed';
    });

    // Collect only one product per each lender which are already sorted by priority
    $tmpLenders = [];
    $productPerLender = [];
    foreach ($passedProducts as $product) {
      if (!in_array($product['lender_id'], $tmpLenders)) {
        $productPerLender[] = $product;
        $tmpLenders[] = $product['lender_id'];
      }
    }
    $tmpLenders = []; // reset
    $otherProductPerLender = [];
    foreach ($other_products as $product) {
      if (!in_array($product['lender_id'], $tmpLenders)) {
        $otherProductPerLender[] = $product;
        $tmpLenders[] = $product['lender_id'];
      }
    }
    unset($tmpLenders);

    // Get the lender list to which the lead has already been sent
    $alreadySentLenderIds = [];
    $saleDetails = $this->LoadModel('Sales')->getSalesWithLenderDetails($sent['lead_id']);
    if ($saleDetails) {
      $alreadySentLenderIds = array_unique(array_column($saleDetails, 'lender_id'));
    }

    $doNotShowLenders = $this->LoadModel('Lenders')->getListOfDoNotShowInLenderPrefs();
    $doNotShowLenderIds = array_column($doNotShowLenders, 'lender_id');

    $availableProducts = [];
    foreach ($productPerLender as $product) {
      // Eliminate lenders with **DO_NOT_SHOW_IN_LENDER_PREFS**
      if (in_array($product['lender_id'], $doNotShowLenderIds)) continue;

      $alreadySent = in_array($product['lender_id'], $alreadySentLenderIds)  ? true : false;

      $availableProducts[] = [
        'product_id' => $product['lender_product_id'],
        'lender_id' => $product['lender_id'],
        'lender_name' => $product['lender_name'],
        'shorthand' => $product['shorthand'],
        'lender_logo' => $product['lender_logo'],
        'already_sent' => $alreadySent,
        'duplicate' => !empty($product['duplicate']),
        'sent_more_than_config' => !empty($product['sent_more_than_config']),
        'LendSize' => @$product['LendSize'],
        'order' => !empty($product['order']) ? $product['order'] : null,
        'priority' => $product['priority'],
        'payment_daily' => $product['payment_daily'],
        'payment_weekly' => $product['payment_weekly'],
        'payment_biweekly' => $product['payment_biweekly'],
        'payment_monthly' => $product['payment_monthly'],
      ];
    }


    $availableOtherProducts = [];
    foreach ($otherProductPerLender as $product) {
      // Eliminate lenders with **DO_NOT_SHOW_IN_LENDER_PREFS**
      if (in_array($product['lender_id'], $doNotShowLenderIds)) continue;
      if (in_array($product['lender_id'], array_column($availableProducts, 'lender_id'))) continue;

      $alreadySent = in_array($product['lender_id'], $alreadySentLenderIds)  ? true : false;

      $availableOtherProducts[] = [
        'product_id' => $product['lender_product_id'],
        'lender_id' => $product['lender_id'],
        'lender_name' => $product['lender_name'],
        'shorthand' => $product['shorthand'],
        'lender_logo' => $product['lender_logo'],
        'already_sent' => $alreadySent,
        'duplicate' => !empty($product['duplicate']),
        'sent_more_than_config' => !empty($product['sent_more_than_config']),
        'LendSize' => @$product['LendSize'],
        'order' => !empty($product['order']) ? $product['order'] : null,
        'priority' => $product['priority'],
        'payment_daily' => $product['payment_daily'],
        'payment_weekly' => $product['payment_weekly'],
        'payment_biweekly' => $product['payment_biweekly'],
        'payment_monthly' => $product['payment_monthly'],
      ];
    }

    $maxLenders = Configure::read('Lend.partner.max_num_lender_to_send');

    $leadDupeSales = $this->loadModel('Sales')->getleadDupeSales($lead['lead']['lead_id']);

    // Don't count duplicate sales
    $numOfLendersSendTo = count($saleDetails) - count($leadDupeSales);
    if ($numOfLendersSendTo >= $maxLenders)
      $this->set('message', "Reached the max number({$maxLenders}) of lenders to send");

    $no_send = $numOfLendersSendTo >= $maxLenders;
    if (!empty($availableProducts) && (empty($partnerIdsInWhileListOfLendSize) || in_array((int)$partnerId, $partnerIdsInWhileListOfLendSize)))
      $availableProducts = $lendSize->setProductsInOrder($availableProducts, $no_send, $partnerId);

    if (!empty($availableOtherProducts) && (empty($partnerIdsInWhileListOfLendSize) || in_array((int)$partnerId, $partnerIdsInWhileListOfLendSize)))
      $availableOtherProducts = $lendSize->setProductsInOrder($availableOtherProducts, $no_send, $partnerId);

    if($lendSizeEstimate){
      return([$availableProducts, $availableOtherProducts]);
    }else{
      $this->set('maximumLendersNumber', $this->loadModel('App')->convertOneDigitIntoWord($maxLenders));
      $this->set('no_send', $no_send);
      $this->set('products', $availableProducts);
      $this->set('other_products', $availableOtherProducts);
      $this->render((empty($partnerIdsInWhileListOfLendSize) || in_array((int)$partnerId, $partnerIdsInWhileListOfLendSize)) ? 'collect_available_products' : 'collect_available_products_old');
    }

  }

  public function getReadytosendCount($dateRange = null) {
    // var_dump($dateRange,'q'); for example: start_date=2019-09-18&end_date=2019-09-18&range=Yesterday
    $params = [];
    if (!empty($dateRange)) {
      parse_str($dateRange, $params);
    }

    $count = $this->Leads->getReadytosendCount( $this->Auth->user('partner_id'), $params );
    $this->setJsonResponse(['success' => true, 'count' => $count]);
  }

  public function stopSendingLead() {
    $leadRef        = $this->request->getData('leadRef'); // array
    $partnerId      = $this->Auth->user('partner_id');
    $partnerUserId  = $this->Auth->user('partner_user_id');
    $accountType    = $this->Auth->user('account_type');
    try {
      $error = null;
      if (empty($leadRef))
        throw new \Exception("Lead not found");

      if ((empty($partnerId) && $accountType !== 'Lend Staff') || empty($partnerUserId))
        throw new \Exception("Please log in first");

      $lead = $this->Leads->getLead(array('lead_ref' => $leadRef));
      if (((int)$lead['partner_id']) !== $partnerId && $accountType !== 'Lend Staff')
        throw new \Exception("You have no access to this lead");

      $leadId = $this->internal_auth->unhashLeadId($leadRef);
      $this->loadModel('PartnerLeadHistory')->addPartnerLeadHistory(array('partner_id'=>$partnerId, 'partner_user_id'=>$partnerUserId, 'lead_id'=>$leadId,'history_detail'=>'Lead Closed'));
      //add a record into lead_send_prevention
      $data = array();
      $data['lead_id'] = $leadId;
      $data['user_id'] = null;
      $data['partner_user_id'] = $partnerUserId;
      $data['reason']  = null;
      $data['created'] = date('Y-m-d H:i:s');
      $this->loadModel('LeadSendPrevention')->addSendPrevention($data);

      //check if the status is Rejected, In Progress, Attempting or Pending
      $lenderStatuses = $this->loadModel('LendStatuses')->getLendStatusesOldFashion();
      $lead = $this->loadModel('Leads')->getLead(['lead_id' => $leadId]);
      if (!empty($lenderStatuses[$lead['partner_status_id']]['groupName'])
        && !in_array($lenderStatuses[$lead['partner_status_id']]['groupName'], ['Attempting', 'In Progress'])) {
          //close lead directly
          $this->Leads->updateLead(array('lead_id'=>$leadId, 'partner_status_id'=>58));
      }
      // else : waiting for current lender to reject it, handled by star - status check
      $success = true;
    }catch (\Exception $e) {
      $success = false;
      $error = $e->getMessage();
    }
    $this->setJsonResponse(['success' => $success, 'error' => $error]);
  }

  public function reOpenLead(){
    $leadRef        = $this->request->getData('leadRef');
    $partnerId      = $this->Auth->user('partner_id');
    $partnerUserId  = $this->Auth->user('partner_user_id');

    try {
      $error = null;
      if (empty($leadRef))
        throw new \Exception("Lead not found");

      if (empty($partnerId) || empty($partnerUserId))
        throw new \Exception("Please log in first");

      $leadId = $this->internal_auth->unhashLeadId($leadRef);
      $lead = $this->Leads->getLeadDetails($leadId);
      if (((int)$lead['lead']['partner_id']) !== $partnerId)
        throw new \Exception("You have no access to this lead");
      // add the record into partner_lead_history
      $this->loadModel('PartnerLeadHistory')->addPartnerLeadHistory(array('partner_id'=>$partnerId, 'partner_user_id'=>$partnerUserId, 'lead_id'=>$leadId,'history_detail'=>'Lead Re-Opened'));
      //remove the record in lead_send_prevention if exist
      $this->loadModel('LeadSendPrevention')->removeSendPrevention($leadId);

      // check if lead status is 'Rejected'
      $lenderStatuses = $this->loadModel('LendStatuses')->getLendStatusById(@$lead['lead']['partner_status_id']);
      if($lenderStatuses['group_name'] === 'Rejected'){
        if($this->checkIfAvailableProductsExist($leadId, $lead))
          $this->Leads->updateLead(array('lead_id' => $leadId, 'partner_status_id' => 55));   //if has available products, then update status to `Ready to Send`
        else
          $this->Leads->updateLead(array('lead_id' => $leadId, 'partner_status_id' => 25));   // otherwise update status to `Rejected - No more lenders found`
      }

      $success = true;
    }catch (\Exception $e) {
      $success = false;
      $error = $e->getMessage();
    }
    $this->setJsonResponse(['success' => $success, 'error' => $error]);
  }

  /**
  * send to lender popup button clicked
  * partner updates their lead to new status
  **/
  public function updateLeadStatus()
  {
    try {
      $success = true;
      $message = '';

      $partnerId      = $this->Auth->user('partner_id');
      $partnerUserId  = $this->Auth->user('partner_user_id');
      if (empty($partnerId) || empty($partnerUserId))
        throw new \Exception("Please login first");

      $statusName = $this->request->getData('status');
      $leadRef = $this->request->getData('leadRef');
      if (empty($leadRef))
        throw new \Exception("Lead Ref not found");
      if (empty($statusName))
        throw new \Exception("Status not found");

      $lendStatusRow = TableRegistry::get('LendStatuses')->getLendStatusByName($statusName);
      if (empty($lendStatusRow['lend_status_id']))
        throw new \Exception("Lend Status not found");
      $partnerStatusId = $lendStatusRow['lend_status_id'];

      $leadId = $this->internal_auth->unhashLeadId($leadRef);
      if (empty($leadId))
        throw new \Exception("Lead not found");

      $lead = $this->Leads->getLeadDetails($leadId);
      if (empty($lead))
        throw new \Exception("Lead Ref not found.");

      if (empty($lead['lead']['partner_id']) || $lead['lead']['partner_id'] != $partnerId)
        throw new \Exception("No access to this lead");

      if (!$this->Leads->updatePartnerStatusId($leadId, $partnerStatusId))
        throw new \Exception("Failed to update lead status, please try later");

    } catch (\Exception $e)
    {
      $success = false;
      $message = $e->getMessage();
    }
    $this->setJsonResponse(['success' => $success, 'message' => $message]);
  }

  public function checkIfAvailableProductsExist($leadId, $lead) {
    $foundAvailableProduct = false;
    $availableProducts = [];

    $partnerId = $this->Auth->user('partner_id');

    $makeDecision = new MakeDecision;
    $makeDecision->init(['lead'=>$lead['lead'], 'lead_owners'=>array($lead['lead_owner']), 'bank_statements_analysis'=>$lead['bank_statements_analysis']], array('lead'=>true, 'lead_owners'=>true, 'bank_statements_analysis'=>true, 'lead_abn_lookup'=>true), null, false);
    $makeDecision->criteriaChecks();
    $makeDecision->checkPossibleDupe();

    $products = $makeDecision->rankProductByPriority();
    $products = $makeDecision->setPartnerPreferLender($partnerId, $products);
    $products = $makeDecision->calculateLendSize($lead, $products);

    $passedProducts = array_filter($products, function($product) {
      return $product['pass'] === 'passed';
    });

    // Collect only one product per each lender which are already sorted by priority
    $tmpLenders = [];
    $productPerLender = [];
    foreach ($passedProducts as $product) {
      if (!in_array($product['lender_id'], $tmpLenders)) {
        $productPerLender[] = $product;
        $tmpLenders[] = $product['lender_id'];
      }
    }
    unset($tmpLenders);

    // Get the lender list to which the lead has already been sent
    $alreadySentLenderIds = [];
    $saleDetails = $this->LoadModel('Sales')->getSalesWithLenderDetails($leadId);
    if ($saleDetails) {
      $alreadySentLenderIds = array_unique(array_column($saleDetails, 'lender_id'));
    }

    $doNotShowLenders = $this->LoadModel('Lenders')->getListOfDoNotShowInLenderPrefs();
    $doNotShowLenderIds = array_column($doNotShowLenders, 'lender_id');

    foreach ($productPerLender as $product) {
      // Eliminate lenders with **DO_NOT_SHOW_IN_LENDER_PREFS**
      if (in_array($product['lender_id'], $doNotShowLenderIds)) continue;

      $alreadySent = in_array($product['lender_id'], $alreadySentLenderIds)  ? true : false;

      $availableProducts[] = [
        'product_id' => $product['lender_product_id'],
        'already_sent' => $alreadySent,
        'duplicate' => !empty($product['duplicate']),
        'sent_more_than_config' => !empty($product['sent_more_than_config']),
      ];
    }

    $maxLenders = Configure::read('Lend.partner.max_num_lender_to_send');

    $leadDupeSales = $this->loadModel('Sales')->getleadDupeSales($lead['lead']['lead_id']);

    // Don't count duplicate sales
    $numOfLendersSendTo = count($saleDetails) - count($leadDupeSales);

    $no_send = $numOfLendersSendTo >= $maxLenders;
    if (!empty($availableProducts) && (empty($partnerIdsInWhileListOfLendSize) || in_array((int)$partnerId, $partnerIdsInWhileListOfLendSize)))
      $availableProducts = $makeDecision->setProductsInOrder($availableProducts, $no_send, $partnerId);

    foreach ($availableProducts as $availableProduct) {
      if ($availableProduct['already_sent']) continue;
      if (!empty($availableProduct['sent_more_than_config'])) continue;
      if (!empty($availableProduct['duplicate']) && !$no_send)  continue;

      $foundAvailableProduct = true;
      break;
    }
    return $foundAvailableProduct;

  }

  public function search() {
    $partnerId = $this->Auth->user('partner_id');

    $data               = $this->request->getData();
    $data['start_date'] = $this->Auth->user('partner_created');
    $data['end_date']   = date('Y:m:d H:i:s', time());

    $results = $this->Leads->searchLeads($partnerId, $data);

    $this->setJsonResponse(array('success'=>true, 'search_term'=>$data['search'], 'results'=>$results));
  }


  public function fullSearch($inJson = true, $data=[]){
    if(!$this->request->is('post'))
      throw new \Exception("Request type not allowed");
    if(empty($data))
      $data = $this->request->getData();

    try{
      $page  = $data['pageNumber'];
      $limit = empty($data['pageSize']) ? false : $data['pageSize'];
      $query = $data['query'];
      $user  = $this->Auth->identify();

      if(empty($user))
        throw new \Exception("Please login.");

      $partnerId           = $user['partner_id'];
      $passInPartnerUserId = !empty($user['access_all_leads']) ? null : $user['partner_user_id'];
      $showMailingListBtn  = true;

      // Prep
      $response = array(
        'success'=> true,
        'pageNumber'=>$page,
        'showMailingListBtn'=>$showMailingListBtn
      );

      $leads = [];
      if (!empty($data['totalCountCheck'])) {
          $totalResults = $this->Leads->fullSearch($partnerId, $passInPartnerUserId, $query, $limit, $page, true);
          $response['total'] = $totalResults;
      } else {
          $leads = $this->Leads->fullSearch($partnerId, $passInPartnerUserId, $query, $limit, $page);
          if($leads == false) $leads = [];
          $response['leads'] = $leads;
      }

    }catch(\Exception $e){
      $this->response->statusCode(400);
      $response = array('success'=> false, 'error'=>$e->getMessage());
    }

    if($inJson) {
      return $this->setJsonResponse($response);
    }else{
      return $response;
    }
  }

  public function requestCallback(){

        try{
            $this->request->allowMethod(['post']);
            $data = $this->request->getData();

            if(empty($data['contact_detail']) OR empty($data['ip'])){
                return $this->setJsonResponse(['success'=>false, 'message'=>'Missing parameter']);
            }
            else{
                if(empty($data['contact_detail']['name']) OR empty($data['contact_detail']['number']))
                    return $this->setJsonResponse(['success'=>false, 'message'=>'Missing parameter']);
            }
            $data['sent_from'] = getenv("DOMAIN_BRO", true);

            $header = array('Content-Type: application/json');
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, getenv("DOMAIN_APP", true).'/apply/request-callback');
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30 );
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 0);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $header);

            $result = curl_exec($ch);
            $curlReturnValue = curl_errno($ch);
            $httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

            Log::info($result);
            Log::info($curlReturnValue);
            Log::info($httpcode);

            if ('0' !== "$curlReturnValue")
                return $this->setJsonResponse(['success'=>false, 'message'=>'Unable to send the request']);

            if(!in_array($httpcode, [200, 201, 202]))
                return $this->setJsonResponse(['success'=>false, 'message'=>'Unable to send the request']);

            return $this->setJsonResponse(json_decode($result));

        }catch(\Exception $e){
            return $this->setJsonResponse(['success'=>false, 'message'=>'Unable to send the request']);
        }
    }

  private function generateBetaLink($leadRef){

    // Get current login user email
    $email = $this->Auth->user('email');
    // Add expiry date
    $expiry_date = strtotime('+ 12 Hour');
    $login = "{\"email\":\"$email\", \"expiry_date\":\"$expiry_date\", \"lead_ref\":\"$leadRef\"}";

    $build_sig = Security::salt().'#'.$login;
    $build_sig = hash('sha1', $build_sig);
    $code = $login.'#'.$build_sig;
    $code = base64_encode( $code );
    $code = str_replace('partners.lend', 'beta-partners.lend', getenv('DOMAIN_BRO', true)) . "?betalink=" . $code;

    return $code;
  }

  public function addNewPopup() {
    try {
      // $topProductTypes = array();
      // $subProductTypes = array();
      $this->loadModel('PartnerProductTypes');
      $products = $this->PartnerProductTypes->getPartnerProductTypes(array('active' => 1), true);
      $partnerProductTypes = $this->PartnerProductTypes->groupProductType($products);
      $partnerId = $this->Auth->user('partner_id');
      $partnerUserId = $this->Auth->user('partner_user_id');
      $mobile = $this->Auth->user('mobile');
      $phone = $this->Auth->user('phone');

      // foreach ($partnerProductTypes as $partnerProductType) {
      //   if($partnerProductType['sub_product'] == 0)
      //     array_push($topProductTypes, $partnerProductType);
      //   else{
      //     if(array_key_exists(strval($partnerProductType['sub_product']), $subProductTypes))
      //       array_push($subProductTypes[strval($partnerProductType['sub_product'])], $partnerProductType);
      //     else
      //       $subProductTypes[strval($partnerProductType['sub_product'])] = array($partnerProductType);
      //   }
      // }
      unset($partnerProductTypes[25]);
      $this->set('partner_id', $partnerId);
      $this->set('partner_user_id', $partnerUserId);
      $this->set('partnerProductTypes', $partnerProductTypes);
      // $this->set('subProductTypes', $subProductTypes);

      $post = $this->request->data();
      if ($this->request->is('Post') && $post != false) {
        //$post['lead']['partner_alias_id'] = !empty($post['alias_to_lookup']) ? $this->loadModel('PartnerAliases')->getPartnerAlias(['alias_ref'=>$data['alias_to_lookup']])['partner_alias_id'] : NULL;

        $post['lead']['call_me_first'] = !empty($post['lead_asset_finance']['contract_type'])
                            ? '1'
                            : (
                              !empty($post['lead']['call_me_first'])
                              ? '1'
                              : (
                                $this->loadModel('Partners')->getPartnerField($partnerId, 'call_me_first')
                                ? '1'
                                : '0'
                                )
                              );
        //other default value
        $post['lead_owner']['home_owner'] = !empty($post['lead_owner']['home_owner']) ? '1' : '0';
        $post['lead']['equipment_found'] = !empty($post['lead']['equipment_found']) ? '1' : '0';

        if($post['lead']['product_type_id'] == '23'){   // if it's Operating Lease, set purpose to Equipment
          $post['lead']['purpose_id'] = 3;
          if(!empty($post['lead_asset_finance']['contract_type'])) unset($post['lead_asset_finance']['contract_type']);
        }

        $contractType = null;
        if(!empty($post['lead_asset_finance']['contract_type'])) {
          $contractType = $post['lead_asset_finance']['contract_type'];
          $post['lead']['purpose_id'] = 3; // if it's asset finance, set purpose to Equipment
          $post['lead']['send_type'] = 'Manual';
        }
        $errors = $this->_leadValidationToSendLead($post);
        //additional check for mobile
        if(!empty($post['lead_owner']['mobile'])) {
          if(in_array(str_replace(' ', '', $post['lead_owner']['mobile']),[$mobile,$phone])){
            $errors[] = array('field' => 'lead_owner[mobile]', 'error'=>"Lead's mobile cannot be the same as the Broker's number");
          }
        }

        if(!empty($post['lead_owner']['phone'])) {
          if(in_array(str_replace(' ', '', $post['lead_owner']['phone']),[$mobile,$phone])){
            $errors[] = array('field' => 'lead_owner[phone]', 'error'=>"Lead's phone cannot be the same as the Broker's number");
          }
        }

        //assign partners.send_type to leads.send_type
        $partner = $this->loadModel('Partners')->getPartner(array('partner_id'=>$partnerId));
        if ($partner['send_type'] != null && empty($contractType))
          $post['lead']['send_type'] = ucfirst($partner['send_type']);


        $selectedProduct = $this->PartnerProductTypes->findProductById($products, $post['lead']['product_type_id']);
        $this->set('selectedProduct', $selectedProduct);

        if (!empty($errors)) {
          // Resolve sub product type
          $post['lead']['product_type_id'] = $selectedProduct['sub_product'] == 0 ? $post['lead']['product_type_id'] : $selectedProduct['sub_product'];
          $this->set('data', $post);
          $this->set('errors', $errors);
          throw new \Exception('errors');
        }
        
        list($lead, $isNew, $data) = $this->_saveLead($post);
          //redirect to new lead page
          if (!empty($lead['lead']['lead_ref'])) {
            $url = '/leads/view/'. $lead['lead']['lead_ref'];
            return $this->setJsonResponse(array('redirect'=>$url));
            die();exit;
          }
      }


    } catch (\Exception $e) {

    }
    $this->viewBuilder()->setLayout('ajax');
  
  }

  public function setLeadsTermMonths(){   
    $data = $this->request->data();

    $leads_id = (new LendInternalAuth)->unhashLeadId($data['update_lead_ref']);
    $month = $data['update_loan_term_requested_months'];
  
    $result = $this->loadModel('Leads')->setLeadsTermMonths($leads_id,$month);
    return $this->setJsonResponse(array('success'=>$result));
  }

  public function index()
  {
    $this->viewBuilder()->setLayout('accounts');
    $filePath = WWW_ROOT . '/react/asset-manifest.json';
    $file = new File($filePath);

    $manifest = json_decode($file->read())->files;
    $file->close();

    $maincss = 'main.css';
    $mainjs = 'main.js';

    $css = '/react' . $manifest->$maincss;
    $js = '/react' . $manifest->$mainjs;

    $this->set(compact('css', 'js'));
  }

  public function triggerGenerateAccount($lead_ids_csv)
  {
    try {
      $lead_ids = explode(',', $lead_ids_csv);

      foreach ($lead_ids as $lead_id) {
        TableRegistry::getTableLocator()->get('LeadEntity')->triggerGenerateAccount($lead_id);
      }
      $this->setJsonResponse(array('success' => $lead_ids));
    } catch (\Exception $e) {
      return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()]);
    }
  }
}
