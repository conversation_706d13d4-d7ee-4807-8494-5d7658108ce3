<?php


namespace App\Controller;

use App\Lend\Config;
use Cake\Core\Configure;
use Cake\Cache\Cache;
use Cake\Utility\Security;
use Firebase\JWT\JWT;
use App\Lend\EmailSender;
use App\Lend\LendInternalAuth;
use App\Lend\LendStatus;
use App\Lend\PartnerValidation;
use App\Lend\SignatureService;
use Cake\Log\Log;
use Cake\Routing\Router;
use \Aws\S3\S3Client;
use Cake\ORM\TableRegistry;

class AccreditationController extends AppController
{

    public function initialize() {
        parent::initialize();
        $this->Auth->allow([
            'uploadAccreditationForm',
            'deleteAccForm',
            'getAccForms',
            'generateAccDocumentRequest'
        ]);

        $this->config = new Config;
    }

    public function uploadAccreditationForm()
    {
        $data = $this->request->getData();

        $document = TableRegistry::getTableLocator()->get('PartnerAccreditationDocuments')->getAccreditationForm(['partner_user_id' => $data['partner_user_id'], 'type' => $data['type']]);

        // Update template_id to Lend DB:
        $result = TableRegistry::getTableLocator()->get('PartnerUsers')->uploadAccreditation($data, null);
        $accPath = explode('/', $result['s3full'], 2)[1];
        
        if(empty($document)){
            $result = TableRegistry::getTableLocator()->get('PartnerAccreditationDocuments')->addAccreditationForm($data, $result['s3path'].$result['s3filename']);
        }else{
            $result = TableRegistry::getTableLocator()->get('PartnerAccreditationDocuments')->updateAccreditationForm($document['id'], ['s3_url' => $result['s3path'].$result['s3filename']]);
        }

        $documents = TableRegistry::getTableLocator()->get('PartnerAccreditationDocuments')->getAccreditationForms(['partner_user_id' => $data['partner_user_id']]);

        $this->setJsonResponse($documents);

    }

    public function deleteAccForm(){
        $data = $this->request->getData();
        $document = TableRegistry::getTableLocator()->get('PartnerAccreditationDocuments')->getAccreditationForms(['id' => $data['formId']]);
        if($document[0]['partner_user_id'] !== $data['partner_user_id']){
            return $this->setJsonResponse(['error' => 'You are not allowed to delete this document'], 401);
        }
        $result = TableRegistry::getTableLocator()->get('PartnerAccreditationDocuments')->deleteAccreditationForm(['id' => $data['formId']]);
        $documents = TableRegistry::getTableLocator()->get('PartnerAccreditationDocuments')->getAccreditationForms(['partner_user_id' => $data['partner_user_id']]);

        $this->setJsonResponse($documents);

    }

    public function getAccForms(){
        $data = $this->request->getData();

        $result = TableRegistry::getTableLocator()->get('PartnerAccreditationDocuments')->getAccreditationForms(['partner_user_id' => $data['partner_user_id']]);
        $this->setJsonResponse($result);
    }

    public function generateAccDocumentRequest(){
        $data = $this->request->getData();
        $path = str_replace('\\', '/', $data['path']);
        $s3Link = TableRegistry::getTableLocator()->get('Partners')->createPfSignedRequest($data['bucket'] . '/' .$path, $data['bucket'], 2);

        return $this->setJsonResponse(['success'=>true, 'data'=>$s3Link]);
    }
}