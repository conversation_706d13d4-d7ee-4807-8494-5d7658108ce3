<?php

namespace App\Controller;

use Cake\Http\Client;
use App\Lend\CurlHelper;
use Cake\Core\Configure;
use App\Lend\LendInternalAuth;
use Cake\Log\Log;

class ProxyController extends AppController
{
    
    public function initialize()
    {
        parent::initialize();
        if (getenv('LEND_ENV') == 0) {
            $this->Auth->allow([
                'index',
            ]);
        }
    }
    function index($service = null)
    {
        $user = $this->Auth->user();
        if (empty($user)) {
            $user = $this->Auth->identify();
        }
        switch ($service) {
            case "email":
                $apiDomain = getenv('DOMAIN_EMAIL_SERVICE');
                $apiKey = getenv('EMAIL_SERVICE_API_KEY');
                $signatureSecret = getenv('EMAIL_SERVICE_SECRET');
                break;
            case "quote":
                $apiDomain = getenv('QUOTES_LOOKUP');
                $apiKey = getenv('QUOTES_API_KEY');
                break;
            case "bank-statements":
                $apiDomain = getenv('DOMAIN_BANKFEEDS');
                $apiKey = getenv('BANK_STATEMENT_API_KEY');
                $signatureSecret = getenv('BANKFEEDS_SECRET');
                break;
            case "bs-analysis":
                $apiDomain = getenv('DOMAIN_BSS');
                $lendconfig = Configure::read('Lend');
                $apiKey = $lendconfig['bs_service_auth_key'];
                break;
            case "match":
                $apiDomain = getenv('DOMAIN_MATCHING_ENGINE');
                $apiKey = getenv('MATCHING_ENGINE_KEY');
                break;
            case "glass":
                $apiDomain = getenv('DOMAIN_GLASS_SERVICE');
                break;
            default:
                die(json_encode(["success" => false, "error" => "Missing service name"]));
        }

        $parameters = $this->request->getAttribute('params');
        $passedArgs = $this->request->getParam('pass');
        unset($passedArgs[0]);
        $http = new Client();
        $url = $apiDomain . "/" . implode("/", $passedArgs);
        $data = $this->request->getData();
        $userAgent = $this->request->getHeaderLine('User-Agent');
        Log::info('============= Proxy START > ' . $service);
        Log::info($url);
        Log::info(json_encode($data));
        Log::info(json_encode($userAgent));

        // special handle for Matching engine
        if ($service === 'match') {
            if (stripos($url, 'get-match-results-proposed') !== false && !empty($this->request->getQuery('filter'))) {
                $filter = $this->request->getQuery('filter');
                $lead_ref = $this->request->getQuery('lead_ref');
                if (!empty($lead_ref)) {
                    $lend_internal_auth = new LendInternalAuth;
                    $lead_id = $lend_internal_auth->unhashLeadId($lead_ref);
                }
                
                $conditions = ['match_ref' => $passedArgs[2]];
                if (!empty($lead_id)) {
                    $conditions['lead_id'] = $lead_id;
                } else {
                    $conditions['lead_id IS'] = NULL;
                }
                $lender_match_requests_table = $this->getTableLocator()->get('LenderMatchRequestEntity');
                $lender_match_request = $lender_match_requests_table->find('all')->where($conditions)->first();
                if (!empty($lender_match_request)) {
                  $lender_match_requests_table->patchEntity($lender_match_request, ['filter' => $filter]);
                  $lender_match_requests_table->save($lender_match_request);
                }
            }
            if (stripos($url, 'select-unselect-result') !== false) {
                if (!empty($data['lead_ref'])) {
                    $lend_internal_auth = new LendInternalAuth;
                    $lead_id = $lend_internal_auth->unhashLeadId($data['lead_ref']);
                    unset($data['lead_ref']);

                    $con_page_status_table = $this->getTableLocator()->get('ConPageStatusEntity');
                    $con_page_status = $con_page_status_table->find('all')->where(['lead_id' => $lead_id]);
                    if (!empty($con_page_status)) {
                        $con_page_status = $con_page_status->first();
                        $patch_con_page_status = [];
                        if ($con_page_status->con_prelim_status === 'complete') {
                            $patch_con_page_status['con_prelim_status'] = 'review required';
                        }
                        if (in_array($con_page_status->con_credit_proposal_status, ['sent', 'overridden'])) {
                            $patch_con_page_status['con_credit_proposal_status'] = 'review required';
                        }
                        if (!empty($patch_con_page_status)) {
                            $con_page_status_table->patchEntity($con_page_status, $patch_con_page_status);
                            $con_page_status_table->save($con_page_status);
                        }
                    }
                }
            }
        }

        if (!empty($data['leadRef'])) {
            $lend_internal_auth = new LendInternalAuth;
            $data['leadId'] = $lend_internal_auth->unhashLeadId($data['leadRef']);
            unset($data['leadRef']);
        }
        $withSignature = $this->request->getHeader('x-with-signature');
        if (!empty($signatureSecret) && ($withSignature == true || @$withSignature[0] == true) && $this->request->is("post")) {
            $signature = CurlHelper::generateSignature($data, $signatureSecret);
            $url .= "?signature=" . $signature;
        }
        if(!empty($apiKey)){
            $header = [
                'headers' => ['x-api-key' => $apiKey]
            ];
        }
        $withAuth = $this->request->getHeader('x-with-auth');
        if (($withAuth == true || @$withAuth[0] == true)) {
            $signature = CurlHelper::generateSignature($data, $signatureSecret);
            $header['headers']['Cookie'] = 'auth_token=' . $_COOKIE['auth_token'];
        }
        Log::info('----- final url & header -----');
        Log::info($url);
        Log::info(json_encode($header));
        if ($this->request->is("get")) {
            $query = $this->request->getQueryParams();
            $response = $http->get($url, $query, $header);
        } elseif ($this->request->is("post")) {
            $header['type'] = 'json';
            $response = $http->post($url, json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES), $header);
        } elseif ($this->request->is("delete")) {
            $response = $http->delete($url, json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES), $header);
        }
        Log::info('----- response -----' . $response->getStatusCode());
        Log::info($response->getJson());
        Log::info('========== End Proxy ==========');

        return $this->setJsonResponse($response->getJson(), $response->getStatusCode());
    }
}