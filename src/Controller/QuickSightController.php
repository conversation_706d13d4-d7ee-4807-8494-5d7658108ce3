<?php
namespace App\Controller;

use Aws\QuickSight\QuickSightClient;
use Cake\Core\Configure;
use Cake\Log\Log;
use Cake\Http\Client;
use Aws\Credentials\CredentialProvider;
use Aws\Signature\SignatureV4;
use Cake\ORM\TableRegistry;

class QuickSightController extends AppController
{
    public function getEmbedUrl($userArn, $dashboardId)
    {
        $awsAccountId = '************';
        $region = 'ap-southeast-2';
        $host = "quicksight.$region.amazonaws.com";
        
        $accessKey = getenv('AWS_KEY');
        $secretKey = getenv('AWS_SEC');
        
        if (empty($accessKey) || empty($secretKey)) {
            Log::error('Missing AWS credentials');
            throw new \Exception('AWS_CREDENTIAL_ERROR');
        }

        $queryParams = [
            'creds-type' => 'QUICKSIGHT',
            'session-lifetime' => 600,
            'user-arn' => $userArn,
        ];

        $url = "https://$host/accounts/$awsAccountId/dashboards/$dashboardId/embed-url?" . http_build_query($queryParams);

        try {
            $credentials = new \Aws\Credentials\Credentials($accessKey, $secretKey);
            $signer = new SignatureV4('quicksight', $region);
            $request = new \GuzzleHttp\Psr7\Request('GET', $url, ['Host' => $host]);
            $signedRequest = $signer->signRequest($request, $credentials);

            $http = new Client();
            $response = $http->get($url, [], [
                'headers' => $signedRequest->getHeaders(),
                'timeout' => 30
            ]);

            if (!$response->isOk()) {
                Log::error('QuickSight API Error: ' . $response->getStringBody());
                throw new \Exception('QUICKSIGHT_API_ERROR');
            }

            $jsonResponse = $response->getJson();
            if (empty($jsonResponse['EmbedUrl'])) {
                Log::error('Invalid response from QuickSight API');
                throw new \Exception('HTTP_RESPONSE_ERROR');
            }

            return $jsonResponse['EmbedUrl'];
            
        } catch (\Exception $e) {
            if (strpos($e->getMessage(), '_ERROR') !== false) {
                throw $e;
            }
            Log::error('QuickSight request failed: ' . $e->getMessage());
            throw new \Exception('HTTP_RESPONSE_ERROR');
        }
    }

    public function getQuickSightReports(){
        $qsReportsTable = $this->loadModel('QsReports');
        $qsReportPermissionsTable = $this->loadModel('QsReportPermissions');

        $Auth = $this->Auth->user();
        if (empty($Auth)) {
            $Auth = $this->Auth->identify();
        }        
        $partnerId = $Auth['partner_id'] ?? null;
        $partnerUserId = $Auth['partner_user_id'] ?? null;
        $accessAllLeads = $Auth['access_all_leads'] ?? null;
        
        $level = $Auth['level'] ?? null;
        $accountAdmin = $Auth['account_admin'] ?? null;

        $partnerFeatureFlags = $this->getUserFeatureFlags($partnerId);
        
        $allReports = $qsReportsTable->find()
            ->where(['QsReports.status' => 1])
            ->order(['QsReports.order' => 'ASC'])
            ->toArray();
        
        Log::info('Total reports found: ' . count($allReports));
        
        // Debug: Check if status column exists
        if (!empty($allReports)) {
            $firstReport = $allReports[0];
            Log::info('First report properties: ' . json_encode($firstReport->toArray()));
        }
        
        $accessibleReports = [];
        
        // Check each report for access
        foreach ($allReports as $report) {
            // Check if this report has any permissions
            $hasPermissions = $qsReportPermissionsTable->find()
                ->where(['report_id' => $report->id])
                ->count() > 0;
            
            // If no permissions, skip it (no public documents)
            if (!$hasPermissions) {
                Log::info('Report ID ' . $report->id . ' has no permissions - skipping');
                continue;
            }
            
            // Report has permissions, check if user has access
            // Get all permission records for this report
            $permissionRecords = $qsReportPermissionsTable->find()
                ->where(['report_id' => $report->id])
                ->all();
            
            $hasAccess = false;
            
            // Check each permission record
            foreach ($permissionRecords as $permission) {
                $matchesThisPermission = true;                
                // If permission has partner_id, user must match it
                if (!is_null($permission->partner_id) && $permission->partner_id != $partnerId) {
                    $matchesThisPermission = false;
                }
                
                // If permission has partner_user_id, user must match it
                if (!is_null($permission->partner_user_id) && $permission->partner_user_id != $partnerUserId) {
                    $matchesThisPermission = false;
                }
                
                // If permission has feature_flag_id, user must have it enabled
                if (!is_null($permission->feature_flag_id) && !$this->hasFeatureFlag($partnerFeatureFlags, $permission->feature_flag_id)) {
                    $matchesThisPermission = false;
                }
                
                // If permission has access_all_leads requirement (not null), check against user's access_all_leads
                if (!is_null($permission->access_all_leads) && $permission->access_all_leads != $accessAllLeads) {
                    $matchesThisPermission = false;
                }
                
                // If permission has level requirement, check against user's level
                // Level 1 (admin) can access all levels, but higher levels cannot access lower level permissions
                if (!is_null($permission->level) && $level > $permission->level) {
                    $matchesThisPermission = false;
                }
                
                // If permission has account_admin requirement (not null), check against user's account_admin
                if (!is_null($permission->account_admin) && $permission->account_admin == 1 && !$accountAdmin) {
                    $matchesThisPermission = false;
                }
                
                // If this permission record matches completely, user has access
                if ($matchesThisPermission) {
                    $hasAccess = true;
                    break; // No need to check other permission records
                }
            }
            
            if ($hasAccess) {
                Log::info('Report ID ' . $report->id . ' - user has access through permissions');
                $accessibleReports[] = $report;
            } else {
                Log::info('Report ID ' . $report->id . ' - user does not have access');
            }
        }
        return $this->setJsonResponse([
            'success' => true,
            'data' => $accessibleReports,
            'total' => count($accessibleReports)
        ]);
    }

    private function getUserFeatureFlags($partnerId)
    {
        if (!$partnerId) {
            return null;
        }
        $partnerFeatureFlagsTable = $this->loadModel('PartnerFeatureFlags');
        $partnerFeatureFlags = $partnerFeatureFlagsTable->find()
            ->where(['partner_id' => $partnerId])
            ->first();
        
        return $partnerFeatureFlags;
    }

    private function hasFeatureFlag($partnerFeatureFlags, $featureFlagId)
    {
        if (!$partnerFeatureFlags || !$featureFlagId) {
            return false;
        }
        $featureFlagsTable = $this->loadModel('FeatureFlags');
        $featureFlag = $featureFlagsTable->find()
            ->where(['id' => $featureFlagId])
            ->first();

        if (!$featureFlag) {
            return false;
        }
        $flagName = $featureFlag->flag_name;
        
        if ($partnerFeatureFlags->has($flagName) && $partnerFeatureFlags->get($flagName)) {
            return true; 
        }
        return false;
    }

    public function getQuickSightIframeUrl($dashboardId){
        try {
            Log::info("getQuickSightIframeUrl method started");
            
            $Auth = $this->Auth->user();
            if (empty($Auth)) {
                $Auth = $this->Auth->identify();
            }
            
            if (empty($Auth) || empty($Auth['partner_id'])) {
                Log::error('Authentication failed: No valid auth or partner_id');
                throw new \Exception('AUTH_ERROR');
            }
            
            Log::info("Auth: " . json_encode($Auth));
            
            try {
                $partner = TableRegistry::getTableLocator()->get('Partners')->getPartner(['partner_id'=>$Auth['partner_id']]);
                if (empty($partner)) {
                    Log::error('Partner not found for partner_id: ' . $Auth['partner_id']);
                    throw new \Exception('PARTNER_ERROR');
                }
                
                $qs_username = $partner['qs_username'];
                Log::info("QS Username: " . $qs_username);
                
                if (empty($qs_username)) {
                    Log::error('No QuickSight username found for partner: ' . $Auth['partner_id']);
                    throw new \Exception('PARTNER_ERROR');
                }
            } catch (\Exception $e) {
                if ($e->getMessage() === 'AUTH_ERROR' || $e->getMessage() === 'PARTNER_ERROR') {
                    throw $e;
                }
                Log::error('Database/Partner lookup error: ' . $e->getMessage());
                throw new \Exception('PARTNER_ERROR');
            }
            
            $userArn = 'arn:aws:quicksight:ap-southeast-2:************:user/default/'.$qs_username;
            Log::info("Getting embed URL for user: $qs_username with ARN: $userArn");
            $embedUrl = $this->getEmbedUrl($userArn, $dashboardId);

            try {
                $responseData = [
                    'success' => true,
                    'data' => $embedUrl
                ];
                
                $jsonResponse = json_encode($responseData, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
                
                if ($jsonResponse === false) {
                    Log::error('JSON encoding failed for success response: ' . json_last_error_msg());
                    throw new \Exception('SYSTEM_ERROR');
                }
                
                $this->response = $this->response->withType('application/json');
                $this->response = $this->response->withStringBody($jsonResponse);
                
                return $this->response;
            } catch (\Exception $e) {
                if ($e->getMessage() === 'SYSTEM_ERROR') {
                    throw $e;
                }
                Log::error('Error building success response: ' . $e->getMessage());
                throw new \Exception('SYSTEM_ERROR');
            }
        } catch (\Exception $e) {
            Log::error('Error: ' . $e->getMessage());
            
            // Determine user-friendly message based on error type
            $userMessage = '';
            if ($e->getMessage() === 'AUTH_ERROR') {
                $userMessage = 'Sorry, there is a permissions issue with your account, please contact your BDM.';
            } elseif ($e->getMessage() === 'PARTNER_ERROR') {
                $userMessage = 'Sorry, there was an issue with the request, please try again in a few minutes, if the error persists, please contact your BDM.';
            } elseif ($e->getMessage() === 'AWS_CREDENTIAL_ERROR') {
                $userMessage = 'Sorry, there is a permissions issue with your account, please contact your BDM.';
            } elseif ($e->getMessage() === 'QUICKSIGHT_API_ERROR') {
                $userMessage = 'Sorry, there was an issue with the request, please try again in a few minutes, if the error persists, please contact your BDM.';
            } elseif ($e->getMessage() === 'HTTP_RESPONSE_ERROR') {
                $userMessage = 'Sorry, there was an issue with the request, please try again in a few minutes, if the error persists, please contact your BDM.';
            } else {
                $userMessage = 'Sorry, there was an issue with the request, please try again in a few minutes, if the error persists, please contact your BDM.';
            }
            
            return $this->response->withType('application/json')
                ->withStringBody(json_encode([
                    'success' => false,
                    'message' => $userMessage
                ], JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE));
        }
    }


}