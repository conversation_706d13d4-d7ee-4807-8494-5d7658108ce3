<?php
namespace App\Controller;

use App\Lend\Config;
use Cake\Core\Configure;
use App\Lend\LendInternalAuth;
use App\Lend\LendTCPDF;

class PartnerCommissionsController extends AppController {

    public function initialize() {
      parent::initialize();
    }

    /*
    check the parameters if it contains start_date, end_date, range
    store in cookie where possible,
    if already exists in cookie, overwrite them and return
    */
    private function __checkDateRangeInCookie($params) {

      $params = $this->checkDateRangeInCookie($params); // Now using a global timeframe

      if (isset($params['filterType']) || isset($params['filterLenders']) || isset($params['filterPartnerUser'])) {
        $this->Cookie->write('Commissions.filters',
                  array('filterType' => $params['filterType'],
                        'filterLenders' => $params['filterLenders'],
                        'filterPartnerUser' => $params['filterPartnerUser'],
                  ));
      } elseif ($this->Cookie->check('Commissions.filters')) {
        $params = array_merge($params,
                    $this->Cookie->read('Commissions.filters'));
      }
      return $params;
    }

    /*
    overwrite start_date, end_date and range in given parameter array
    */
    private function __overwriteDateRangeWherePossible($params)
    {
      if (!empty($params['commission_id'])) {
        $comId = $params['commission_id'];
        $row = $this->PartnerCommissions->getCommission(array('commission_id' => $comId));
        $dates = array(
          'start_date' => date('Y-m-d', strtotime($row['created'])),
          'end_date'   => date('Y-m-d', strtotime($row['created'])),
          'range' => 'custom',
        );
        $params = array_merge($params, $dates);
        unset($params['commission_id']);
        $this->set('commission_id', $comId);
      } elseif (!isset($params['start_date']) || !isset($params['end_date'])) {
        if(empty($params)) $params = array();
        $dates = (new Config)->getDefaultOnloadDates();
        $params = array_merge($params, $dates);
      }
      return $params;
    }
    /*
    Generate Invoice PDF
    @param: $encodedLeadId - encoded Lead Id (hashed ID/ref#)
    return LendTCPDF object - class can be found in App/Lend/LendTCPDF.php
    */
    protected function _generateInvoicePDF($partnerId, $encodedLeadId, $commissionId = null)
    {
      $pdf = new LendTCPDF();

      //get Lend Company, Bank, Invoice Info
      $lendInfo = Configure::read('Lend.Company');
      $pdf->setInvoiceLendInfo($lendInfo);

      //get Broker and Invoice info
      $data = $this->PartnerCommissions->getLeadBrokerDetails($encodedLeadId, $partnerId, $commissionId);

      $pdf->writeInvoice($data);
      return $pdf;
    }

    /*
    Partner Commissions page
    */
    public function index()
    {
      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }
      $partnerId = $user['partner_id'];

      //check date range in cookie
      $params = $this->__checkDateRangeInCookie($this->request->getQueryParams());
      //if given commission_id in $params
      $params = $this->__overwriteDateRangeWherePossible($params);
      $passInPartnerUserId = !empty($user['access_all_leads']) ? null : $user['partner_user_id'];
      if ((int)$user['level'] !== 1) {
        throw new \Exception("You don't have permission to access this page.");
      }
      
      if (!empty($params['filterPartnerUser'])) {
        $passInPartnerUserId = $params['filterPartnerUser'];
      }
      
      $commissions = $this->PartnerCommissions->getCommissionsForDateRange($partnerId, $params, $passInPartnerUserId);

      //add filters
      list($commissions, $viewFilters) = $this->PartnerCommissions->getAvailableFiltersByCommissions($commissions, $params);
      
      // Get all partner users for the filter dropdown - initial
      $this->loadModel('PartnerUsers');
      $partnerUsers = $this->PartnerUsers->getActiveUsers($partnerId, true);
      $viewFilters['partnerUsers'] = $partnerUsers;


      $uniqueCommissions = [];
      $seenCommissionIds = [];
      // we will be removing this end point functoin soon //
      foreach ($commissions as $commission) {
          if (!in_array($commission['commission_id'], $seenCommissionIds)) {
              $uniqueCommissions[] = $commission;
              $seenCommissionIds[] = $commission['commission_id'];
          }
      }

      $this->set('params', $viewFilters);
      // $this->set('params', $params);
      $this->set('commissions', $uniqueCommissions);
      $this->set('currentUserHasAccessAllLeads', !empty($user['access_all_leads']));
      $this->set('seo', array('meta' => array('title' => 'Commission Payments'.$this->lendconfig['nameTag'], 'og' => array('title' => 'Commission Payments'.$this->lendconfig['nameTag']))) );
    }

    /* ajax call from commissions page */
    public function byDateRange()
    {
      $user = $this->Auth->user();
      $partnerId = $user['partner_id'];
      $passInPartnerUserId = !empty($user['access_all_leads']) ? null : $user['partner_user_id'];
      $params = $this->__checkDateRangeInCookie($this->request->getQueryParams());
      $params = $this->__overwriteDateRangeWherePossible($params);
      
      // Check if partner user filter is set
      if (!empty($params['filterPartnerUser'])) {
        $passInPartnerUserId = $params['filterPartnerUser'];
      }

      $commissions = $this->PartnerCommissions->getCommissionsForDateRange($partnerId, $params, $passInPartnerUserId);

      //add filters
      list($commissions, $params) = $this->PartnerCommissions->getAvailableFiltersByCommissions($commissions, $params);
      
      // Get all partner users for the filter dropdown
      $this->loadModel('PartnerUsers');
      $partnerUsers = $this->PartnerUsers->getActiveUsers($partnerId, true);
      $params['partnerUsers'] = $partnerUsers;

      $this->set('params', $params);
      $this->set('commissions', $commissions);
      $this->set('currentUserHasAccessAllLeads', !empty($user['access_all_leads']));
    }

    public function getSettlementsAndCommissions($months, $includeOriginationFee = false)
    {
      $user = $this->Auth->user();
      $partnerID = $user['partner_id'];
      $passInPartnerUserId = !empty($user['access_all_leads']) ? null : $user['partner_user_id'];

      // Calculate date range
      $startDate = date('Y-m-01 00:00:00', strtotime('-' . ($months + 1) . ' months'));
      $endDate = date('Y-m-d 23:59:59', time());

      // Get commissions using getCommissionsForDateRange
      $params = [
        'start_date' => $startDate,
        'end_date' => $endDate
      ];
      $commissions = $this->PartnerCommissions->getCommissionsForDateRange($partnerID, $params, $passInPartnerUserId);

      //init variables to return
      $byMonth = array();
      $m = 0;

      // Prepare the array
      do {
        $date = date('Y-m', strtotime(date('Y-m') . ' - ' . $m . ' months'));
        $key = date('M Y', strtotime($date)); // Now includes year, e.g. "Oct 2023"
        $byMonth[$key] = array(
          'total' => array(
            'New' => 0,
            'Refinance' => 0,
            'All' => 0 
          ),
          'commission' => array(
            'New' => 0,
            'Refinance' => 0,
            'All' => 0 
          ),
          'amount' => array(
            'New' => 0,
            'Refinance' => 0,
            'All' => 0 
          )
        );
        $m++;
      } while ($m < (int)$months);


      $uniqueCommissions = [];
      $seenCommissionIds = [];
      // we will be removing this end point functoin soon //
      foreach ($commissions as &$commission) {
          if ($includeOriginationFee) {
              $commission['commission'] += $commission['origination_fee'];
          }
          if (!in_array($commission['commission_id'], $seenCommissionIds)) {
              $uniqueCommissions[] = $commission;
              $seenCommissionIds[] = $commission['commission_id'];
          }
      }


      // Populate $byMonth
      foreach ($uniqueCommissions as $commission) {
        $key = date('M Y', strtotime($commission['funded_date'])); // Now includes year
        if (!array_key_exists($key, $byMonth)) {
          continue;
        }
        if (!isset($byMonth[$key]['amount'][$commission['funded_type']])) {
          $byMonth[$key]['amount'][$commission['funded_type']] = 0;
        }
        if (!isset($byMonth[$key]['commission'][$commission['funded_type']])) {
          $byMonth[$key]['commission'][$commission['funded_type']] = 0;
        }
        if (!isset($byMonth[$key]['total'][$commission['funded_type']])) {
          $byMonth[$key]['total'][$commission['funded_type']] = 0;
        }

        $byMonth[$key]['amount'][$commission['funded_type']] += (float)$commission['funded_amount'];
        $byMonth[$key]['amount']['All'] += (float)$commission['funded_amount'];

        $byMonth[$key]['commission'][$commission['funded_type']] += (float)$commission['commission'];
        $byMonth[$key]['commission']['All'] += (float)$commission['commission'];

        $byMonth[$key]['total'][$commission['funded_type']] += 1;
        $byMonth[$key]['total']['All'] += 1;
      }

      // Handle decimal rounding issue
      foreach ($byMonth as $key => $month) {
        foreach ($month as $type => $item) {
          if ($type === 'commission' or $type === 'amount') {
            foreach ($item as $fundedType => $value) {
              $byMonth[$key][$type][$fundedType] = round($value, 2);
            }
          }
        }
      }

      $byMonth = array_reverse($byMonth);

      $this->setJsonResponse(array('success'=>true, 'data'=>$byMonth, 'isEmpty' => empty($commissions)));
    }

    /*
      invoice pdf on commissions page
    */
    public function invoicePdf($encodedLeadId = null, $encodedCommissionId = null)
    {
      $this->viewBuilder()->setLayout('ajax');
      $partnerId = $this->Auth->user('partner_id');

      // Check permission:
      $leadId = (new LendInternalAuth)->unhashLeadId($encodedLeadId);
      //use same way to encode commission_id?
      $commissionId = empty($encodedCommissionId) ? null : (new LendInternalAuth)->unhashLeadId($encodedCommissionId);
      $check = $this->checkPermission($leadId);
      
      if(!$check['success']) return $this->redirect('/partners/dashboard?error='.$check['message']);

      $payload = array(
        'encodedLeadId' => $encodedLeadId,
        'partnerId' => $partnerId,
        'commissionId' => $commissionId,
        'region' => getenv('REGION', true),
      );

      $combined  = json_encode($payload) . getenv('SIGNING_SECRET');
      $signature = base64_encode(hash_hmac('sha256', $combined, getenv('SIGNING_SECRET'))); // to base64

      $url = getenv('DOMAIN_PDF').'/export-rcti?'.http_build_query(array(
        'payload' => json_encode($payload),
        'signature' => $signature
      ));

      $this->redirect($url);
    }

    public function updateDrawdownAmount($encodedLeadId = null, $encodedCommissionId = null){
    
      $data = $this->request->getData();
      $this->PartnerCommissions->updateDrawdownAmount($data);
      $this->setJsonResponse( array('success'=>true) );
    }
    
}
