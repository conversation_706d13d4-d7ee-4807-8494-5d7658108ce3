<?php
namespace App\Controller;

use App\Lend\Config;
use Cake\Core\Configure;
use Cake\Cache\Cache;
use Cake\Utility\Security;
use Firebase\JWT\JWT;
use App\Lend\EmailSender;
use App\Lend\LendInternalAuth;
use App\Lend\LendStatus;
use App\Lend\PartnerValidation;
use App\Lend\SignatureService;
use Cake\Log\Log;
use Cake\Mailer\Email;
use Cake\ORM\TableRegistry;
use Cake\Routing\Router;
use Cake\Collection\Collection;
use App\Lend\LendSignatureServiceBackendClient;
use Cake\Datasource\Exception\RecordNotFoundException;
use App\Lend\ChecklistHelper;

class PartnersController extends AppController {

    public function initialize() {
      parent::initialize();
      $this->Auth->allow([
        'register',
        'getLenderLogos',
        'welcome',
        'verifyingAccount',
        'homePage',
        'registerValidation',
        'whyPartnerWithLend',
        'affiliates',
        'getCrossDomainData',
        'potentialPartner',
        'requestSupport',
        'doDetailsMatchPartnerAccount',
        'getLeadStatistics',
        'thankyou',
        'accreditation',
        'billing',
        'getPartnerProductTypes'
      ]);

      $this->config = new Config;
    }

    public function homePage(){
      setcookie('auth_token', '', time()-3600, "/");
      $lenderLogos = $this->LoadModel('Lenders')->getActiveLenders('shorthand asc', true);
      if (getenv('LEND_ENV') == 0) {
        $user = $this->Auth->user();
      } else {
        $user = $this->Auth->identify();
      }
      $dashboardUrl = '/partners/dashboard';
      if (!empty($user)) {
        $partner = TableRegistry::get('Partners')->getPartner(['partner_id'=>$user['partner_id']]);
        if (!empty($partner) && $partner['status_system'] == 'manual') {
          $dashboardUrl = '/lead/dashboard/kanban';
        }
      }
      $this->set('dashboardUrl', $dashboardUrl);
      $this->set('userName', empty($user) ? null : $user['name']);
      $this->set('lenderLogos', $lenderLogos);
      $this->viewBuilder()->setLayout('commonLayout'); // Case Sensitive!!!!!!!!
    }

    public function whyPartnerWithLend() {//---old home page
        if ($user = $this->Auth->identify()){
            switch($user['account_type']){
                case 'Lend Staff':  return $this->redirect('/staff/dashboard'); break;
                case 'Lender':      return $this->redirect('/lender/dashboard'); break;
                default:            return $this->redirect('/partners/dashboard'); break;
            }
        }

        $targetEmail = $this->request->getQuery('target');
        if (!empty($targetEmail) AND filter_var($targetEmail, FILTER_VALIDATE_EMAIL)){
            $this->Partners->postToSlack(":email: {$targetEmail} has just clicked the CTA link in Sam's EDM.", (getenv('REGION', true) === 'au' ? 'partner_verification' : 'partner_verification_nz'));
            $this->set('showEdmLinkPopup', true);
        }

        $lender_logos = $this->loadModel('Lenders')->getLenderLogos();
        $this->set('lender_logos', $lender_logos);

        $this->viewBuilder()->setLayout('empty');
        $this->set('seo', array(
            'meta' => array(
                'title' => 'Partner with Lend. Get More Clients Funded. (+ You\'ll Earn More)',
                'description' => 'The perfect solution for finance brokers, lead generators and affiliate marketers. More clients get funded. You remain in complete control. Centralised dashboard & Real time reporting. Register today.',
                'og' => array(
                    'title' => 'Partner with Lend. Get More Clients Funded. (+ You\'ll Earn More)',
                    'description' => 'The perfect solution for finance brokers, lead generators and affiliate marketers. More clients get funded. You remain in complete control. Centralised dashboard & Real time reporting. Register today.',
                ),
            )
        ));
        if ($this->request->params['_matchedRoute'] == '/why-partner-with-lend') {
            $this->set('seo', array('meta' => array('title' => 'Why Partner With Lend | Lend', 'og' => array('title' => 'Why Partner With Lend | Lend'))));
        }
    }

    public function getPartnerProductTypes() {
      try {
        $partnerProductTypes = $this->loadModel('PartnerProductTypes')->getPartnerProductTypes(array('active' => 1), true); 
        return $this->setJsonResponse(['success' => true, 'product_types'=>$partnerProductTypes]);
      } catch (\Exception $e) {
        return $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()]);
      }
    }

    public function registerValidation() {
      if($this->request->is('post')){
        if ($email = $this->request->getData('email'))
          die($this->duplicateEmail($email));
        else if ($abn = $this->request->getData('abn'))
          die($this->Partners->getPartner(array('abn'=>$abn)));
      }
    }

    private function __checkDateRangeInCookie($params)
    {
      // if (isset($params['start_date'], $params['end_date'], $params['range'])) {
      //   $this->Cookie->write('Dashboard.dateRange',
      //             array('start_date' => $params['start_date'],
      //                   'end_date' => $params['end_date'],
      //                   'range' => $params['range']));
      // } elseif ($this->Cookie->check('Dashboard.dateRange')) {
      //   $params = array_merge($params,
      //               $this->Cookie->read('Dashboard.dateRange'));
      // }

      $params = $this->checkDateRangeInCookie($params); // Now using a global timeframe

      $status = !empty($params['status'])
                  ? $params['status']
                  : (!empty($params['manage'])
                      ? $params['manage']
                      : 'Leads'
                    );

      if (isset($params['filterType']) || isset($params['filterStatuses'])
          || isset($params['filterLenders']) || isset($params['filterAliases'])) {
        $this->Cookie->write('Dashboard.' . $status . '.filters',
                  array('filterType' => $params['filterType'],
                        'filterStatuses' => $params['filterStatuses'],
                        'filterLenders' => $params['filterLenders'],
                        'filterAliases' => $params['filterAliases'],
                  ));
      } elseif ($this->Cookie->check('Dashboard.' . $status . '.filters')) {
        $params = array_merge($params,
                    $this->Cookie->read('Dashboard.' . $status . '.filters'));
      }
      return $params;
    }

    public function verifyingAccount(){

    }

    public function register()
    {
      if($this->request->is('post')){
        $data = $this->request->getData();
        $params = $this->request->getQueryParams();
        $chosen_plan = array_key_exists('selected_plan', $params) && array_key_exists('number_of_users', $params) && array_key_exists('bill_frequency', $params);

        $data['partner']['partner_type']  = 'Broker';
        $data['partner']['abn']           = str_replace(' ', '', $data['partner']['abn']);
        $data['partner']['bank_bsb']      = str_replace(' ', '', $data['partner']['bank_bsb']);
        $data['partner']['bank_acc_num']  = str_replace(' ', '', $data['partner']['bank_acc_num']);
        $data['partner_user']['mobile']   = str_replace(' ', '', $data['partner_user']['mobile']);
        $data['partner_user']['phone']    = str_replace(' ', '', $data['partner_user']['phone']);
        if (empty($data['partner_user']['name']) && (!empty($data['partner_user']['first_name']) || !empty($data['partner_user']['last_name']))) {
          $data['partner_user']['name'] = ucwords(trim($data['partner_user']['first_name'] . ' ' . $data['partner_user']['last_name']));
          unset($data['partner_user']['first_name'], $data['partner_user']['last_name']);
        }

        if (isset($data['partner_additional_user'])) {
          $data['partner_additional_user']['mobile']  = str_replace(' ', '', $data['partner_additional_user']['mobile']);
          $data['partner_additional_user']['phone']   = str_replace(' ', '', $data['partner_additional_user']['phone']);
        }

        $data['partner_user']['notification_recipient'] = 0; //change it to 1 once the broker verified their email address

        // Validation Check:
        $required_fields = array(
          'partner' => array(
            'company_name', 'address', 'suburb', 'postcode', 'abn'
          ),
          'partner_user' => array(
            'name', 'email', 'mobile', 'password'
          ),
        );
        if (getenv('REGION', true) === 'au') {
          $required_fields['partner'] = array_merge($required_fields['partner'], ['organisation_name', 'state']);
        } elseif (getenv('REGION', true) === 'nz') {
          $required_fields['partner'] = array_merge($required_fields['partner'], ['city']);
        }
        $pv = new PartnerValidation;
        $pv->setRequiredFields($required_fields);
        $errors = $pv->validate($data);
        // Dupe check 
        $partners_table = TableRegistry::getTableLocator()->get('PartnerEntity');
        $exists = $partners_table->find('all')->where(['abn' => $data['partner']['abn']])->first();
        if (!empty($exists)) {
          $errors[] = array('field' => '', 'error' => 'This is an exists broker.');
        }

        if(!empty($errors)){
          $this->set('data',    $data);
          $this->set('errors',  $errors);
        }else{

          // Were they referred by anybody? Track who...
          if ($referredBy = $this->_readCookie('referredBy')) {
            $data['partner']['referred_by'] = $referredBy;
          }
          $data['partner']['country'] = strtoupper(getenv('REGION', true));
          Log::write('debug', 'Partner Registration: ' . json_encode($data));

          $data['partner_user']['partner_id'] = $this->Partners->addPartner($data['partner']);
          $partner_user_id = $this->Partners->partner_users->addPartnerUser($data['partner_user']);


          // Add the accounts contact too if needed
          if (empty($data['account_contact']) OR $data['account_contact']!=='yes') {
            $data['partner_additional_user']['partner_id']              = $data['partner_user']['partner_id'];
            $data['partner_additional_user']['notification_recipient']  = '0';
            $this->Partners->partner_users->addPartnerUser($data['partner_additional_user']);
          }

          if($chosen_plan){
            $this->LoadModel('TmpPartnerBilling')->addTmpPartnerBilling(['partner_id'=> $data['partner_user']['partner_id'], 'chosen_plan'=> ucfirst($params['selected_plan']), 'bill_frequency'=> $params['bill_frequency'], 'number_of_users'=> $params['number_of_users'], 'created'=>date('Y-m-d H:i:s')]);
          }
          $feature_flags = ['partner_id'=> $data['partner_user']['partner_id']];
          if (getenv('REGION', true) === 'nz') {
            $feature_flags['access_bank_statements']  = 1;
            $feature_flags['access_to_dashboard_analytics']  = 1;
            $feature_flags['is_working_capital_pricing']  = 1;
            $feature_flags['access_iframe_form']  = 1;
            $feature_flags['access_commercial_match']  = 1;
          }
          $ui_feature_flag = TableRegistry::getTableLocator()->get('UiFeatureFlagEntity')->get(1)->toArray();
          $feature_flags['access_to_subscription']  = !empty($ui_feature_flag['ENABLE_SUBSCRIPTION']) ? 1 : 0;

          $this->LoadModel('PartnerFeatureFlags')->addFeatureFlag($feature_flags);

          $this->Cookie->write('partner_user_id', $partner_user_id);

          // Post to slack
          $this->Partners->partner_notifications->sendPartnerNotifications($data['partner_user']['partner_id'], ((getenv('REGION', true) === 'au') ? 'NewBrokerReg' : 'NewBrokerRegNz'), false, array());
          //send verify email to this new broker (not to additional accounts contact)
          $this->Partners->partner_notifications->sendPartnerNotifications($data['partner_user']['partner_id'], 'BrokerVerifyEmail', false, array(), $partner_user_id);

          if($chosen_plan && $params['selected_plan'] != 'free') {
            $this->redirect('/partners/verifying-account');
          }

          if (getenv('REGION', true) === 'nz') {
            $this->redirect('/partners/welcome');
          }

          //This new Lend Accreditation process is required for only the following broker types:
          if(in_array($data['partner']['broker_type'], ['Finance Broker','Mortgage Broker'])){
              $this->redirect('/partners/accreditation');
          }else{
            if (!empty($ui_feature_flag['ENABLE_SUBSCRIPTION'])) {
              $this->redirect('/partners/billing');
            } else {
              $this->redirect('/partners/welcome');
            }
          }

        }
      }

      $partnerProductTypes = $this->loadModel('PartnerProductTypes')->getPartnerProductTypes(array('active' => 1), true);
      $grouped = [];
      if($partnerProductTypes){
        foreach($partnerProductTypes as $productType){
          if($productType['sub_product']==0){
            if ($productType['product_type_name'] === "Other / Not Sure") {
              $grouped[$productType['product_type_id']] = [
                'product_type_id' => $productType['product_type_id'],
                'product_type_name' => "Other or Not Sure",
              ];
            }
            else if ($productType['product_type_name'] === "Combination / Multiple") {
              $grouped[$productType['product_type_id']] = [
                'product_type_id' => $productType['product_type_id'],
                'product_type_name' => "Combination or Multiple",
              ];
            } else {
              $grouped[$productType['product_type_id']] = [
                'product_type_id' => $productType['product_type_id'],
                'product_type_name' => $productType['product_type_name'],
              ];
            }
          }
        }
      }


      $aggregators = TableRegistry::getTableLocator()->get('AggregatorEntity')->find('all')->where(['status' => true, 'country' => getenv('REGION', true)]);

      if($data['partner']['aggregator_id']){
        $collection = new Collection($aggregators);
        $found = $collection->match(['id' => $data['partner']['aggregator_id']])->first();
        if(strtolower($found->aggregator_name) === 'other'){

          $slack_message = $data['partner_user']['name']. " ".
                           " from ".$data['partner']['organisation_name']." with partner id " .
                           $data['partner_user']['partner_id'] . " has signed up with an Aggregator not in our current list. Product team - please verify the following aggregator exists and add to the Lend database".
                           " if required: ".$data['partner']['main_aggregator_membership_name'];


        $channel = getenv('REGION', true) === 'au' ? 'partner_verification' : 'partner_verification_nz';
        $slack_message = ":eyes: " . $slack_message;
        $this->Partners->postToSlack($slack_message, $channel);
        } 
      }
 
      $full_name = $this->request->getQuery('fullname') ?? @$data['partner_user']['name'];
      $split_name = explode(" ", $full_name);

      $this->set('title_options', array_merge([''], $this->config->getEnumValues('partner_users', 'title')));
      $this->set('broker_type_options', array_merge([''], $this->config->getEnumValues('partners', 'broker_type')));
      $this->set('role_options', array_merge([''], $this->config->getEnumValues('partner_users', 'role')));
      $this->set('indus_assoc_member_options', $this->config->getSetValues('partner_users', 'indus_assoc_member'));
      $this->set('product_type_options', $grouped);
      $this->set('queries', $this->request->getQuery());
      $this->set('fullname', $split_name);
      $this->set('aggregators', $aggregators);
      $this->set('seo', array('meta' => array('title' => 'Get Started'.$this->lendconfig['nameTag'], 'og' => array('title' => 'Get Started'.$this->lendconfig['nameTag']))) );
      if (getenv('REGION', true) === 'au') {
        $this->viewBuilder()->setTemplate('register');
      } elseif (getenv('REGION', true) === 'nz') {
        $this->viewBuilder()->setTemplate('register_nz');
      }
    }

    public function welcome(){
      $partner_user_id = $this->Cookie->read('partner_user_id');
      if(!empty($partner_user_id)){
        $partner_user = $this->Partners->partner_users->getPartnerUser(array('partner_user_id'=>$partner_user_id));
        $confirmed = $this->hasPartnerConfirmed($partner_user['partner_id']);
        $this->set('partner_user', $partner_user);
        $this->set('confirmed', $confirmed);
      }else{
        $this->redirect('/');
      }
      $this->set('settingsToUpdate', true);
      $this->set('seo', array('meta' => array('title' => 'Welcome'.$this->lendconfig['nameTag'], 'og' => array('title' => 'Welcome'.$this->lendconfig['nameTag']))) );
    }

    public function billing() {
      $partner_user_id = $this->Cookie->read('partner_user_id');

      $partner_user = $this->Partners->partner_users->getPartnerUser(array('partner_user_id' => $partner_user_id));

      if(!empty($partner_user)){
        $partner_feature = $this->loadModel('PartnerFeatureFlags')->getFeatureFlag(array('partner_id'=>$partner_user['partner_id']));
        if (empty($partner_feature['access_to_subscription'])) {
          return $this->redirect('/partners/welcome/');
        }
        $subscriptionPrices = $this->LoadModel('Subscription')->getSubscriptionPrices(['trans_type'=>'Subscription']);
        $subscriptionPlans = $this->LoadModel('Subscription')->getAllSubscriptionPlan([]);
  
        $mergePlans = array();
        foreach ($subscriptionPlans as $plan) {
          foreach($subscriptionPrices as $price) {
            if ($plan['name'] === $price['plan_type']) {
              $plan['cost'] = $price['cost'];
              array_push($mergePlans, $plan);  
            }
          }
        }

        $this->set('partnerUser', $partner_user['partner_id']);
        $this->set('subscriptionPlans', $mergePlans);
      }else{
        $this->redirect('/partners/welcome/');
      }
    }

    public function accreditation(){
        $partner_user_id = $this->Cookie->read('partner_user_id');
        if(!empty($partner_user_id)){
            $lendconfig = Configure::read('Lend');
            $accDocTypes = $this->config->getEnumValues('partner_accreditation_documents', 'type');
            $partner_user = $this->LoadModel('PartnerUsers')->getPartnerUser(array('partner_user_id'=>$partner_user_id));
            $this->set('partnerUser', $partner_user);
            $this->set('lendConfig', $lendconfig);
            $this->set('accDocs', $accDocTypes);
        }else{
          $ui_feature_flag = TableRegistry::getTableLocator()->get('UiFeatureFlagEntity')->get(1)->toArray();
          if (!empty($ui_feature_flag['ENABLE_SUBSCRIPTION'])) {
            $this->redirect('/partners/billing/');
          } else {
            $this->redirect('/partners/welcome/');
          }
        }
    }

    public function settings(){
      $partner = $this->Partners->getPartner(array('partner_id'=>$this->Auth->user('partner_id')));
      $this->set('partner', $partner);
    }

    /**
     * @param $partnerId
     * @return bool
     */
    private function hasPartnerConfirmed($partnerId): bool
    {
        $partner_user_id = $this->Cookie->read('partner_user_id');
        $partner_user = $this->Partners->partner_users->getPartnerUser(array('partner_user_id'=>$partner_user_id));

        if($partner_user['q_investigation'] === "1" && $partner_user['q_membership'] === "1" && $partner_user['q_bankrupt'] === "1" && $partner_user['q_offence'] === "1" && $partner_user['q_otherName'] === "1"){
            return true;
        }

        return false;
    }

    private function dashboardFirstvisit() {
      $partnerID = $this->Auth->user('partner_id');
      // Lender Prefs
      $lenders   = $this->LoadModel('Lenders')->getActiveLenders();
      $account   = $this->LoadModel('Partners')->getPartner(array('partner_id'=>$partnerID));
      $preferred = $this->LoadModel('PartnerLenderPriority')->getPartnerLenderPriority($partnerID);
      $excludedLenders = $this->LoadModel('PartnerLenderExcludes')->getPartnerLenderExcludes($partnerID);
      $this->set('account', $account);
      $this->set('preferredLenders', $preferred);
      $this->set('excludedLenders', $excludedLenders);
      $this->set('lenders', $lenders);
      // Notification Prefs
      $allUsers  = $this->LoadModel('PartnerUsers')->getPartnerUser(array('partner_id'=>$partnerID), true);
      $notifs    = $this->LoadModel('PartnerNotifications')->groups->getAllGroups();

      foreach ($allUsers as $key => $allUser) {
        $allUsers[$key]['notification_settings'] = $this->PartnerNotifications->settings->getSettings(array('ref_id' => $allUser['partner_user_id'], 'ref_type' => 'partner_user_id'));
        if ($allUsers[$key]['notification_settings'] == array() || // if no notif rows found in the table
          (count($allUsers[$key]['notification_settings']) == 1 && $allUsers[$key]['notification_settings'][0]['notif_group_id'] == '9') // notif_group is set to 9 when new broker is verified
        ) {
          $this->set('allNotifEnabled', 1);
        }
      }

      $this->set('allNotifications', $notifs);
      $this->set('allUsers', $allUsers);
      // Other
      $this->set('settingsToUpdate', true);
    }

    public function dashboard(){
      if ($this->Auth->user() && $this->request->getQuery('pid')) {
        //make sure that the proper user has been updated into Auth, so here do a redirection to itself without pid.
        $this->redirect('/partners/dashboard');
      }
      $appendix = '';
      $title = 'Reports'.$this->lendconfig['nameTag'];
      if (!empty($this->request->getQuery('success')))
        $appendix .= '?success='.$this->request->getQuery('success');
      elseif (!empty($this->request->getQuery('error')))
        $appendix .= '?error='.$this->request->getQuery('error');
      if ($this->Auth->user('account_type')==='Lend Staff')
        return $this->redirect('/staff/dashboard'.$appendix);

      if (!$this->Auth->user('settings_complete')) {
        $this->dashboardFirstvisit();
        $this->viewBuilder()->setTemplate('dashboard_firstvisit');
        return;
      }

      $partnerId = $this->Auth->user('partner_id');
      $openedTab = $this->request->getQuery('manage');
      $user = $this->Auth->user();
      $params = $this->__checkDateRangeInCookie($this->request->getQueryParams());

      //dashboard page : !$openedTab or missing timeframe
      if (!$this->Partners->leads->isValidStatusName($openedTab)) {
        $passInPartnerUserId = !empty($user['access_all_leads']) ? null : $user['partner_user_id'];
        $data = $this->Partners->leads->getLeadsTotalByPartner($partnerId, $params['start_date'], $params['end_date'], 'total', $passInPartnerUserId);
        $metrics = $this->Partners->leads->getLeadsTotalByPartner($partnerId, $params['start_date'], $params['end_date'], 'metrics', $passInPartnerUserId);

        $this->set('metrics', $metrics); //metrics section
        $this->set('data', $data);  //
      } else {

        if ($this->Cookie->check('Dashboard.UserDashPrefs')) {
          $columns = $this->Cookie->read('Dashboard.UserDashPrefs');

          // NOTE:: below is tempoorary code for LendScore tab force to show. It could be removed some day. - 3rd Sep 2019
          if(!$this->Cookie->check('Dashboard.ForceToShowLendScore')){
            $this->Cookie->write('Dashboard.ForceToShowLendScore', true);
            $columns['column']['lend_score'] = 'lend_score';
            $this->Cookie->write('Dashboard.UserDashPrefs', $columns);
          }

          $this->set('columns', $columns);
        }

        if (isset($params['status']))
          $this->set('openedTab', $params['status']);

        // Get partner aliases
        $params['partner_aliases'] = $this->loadModel('PartnerAliases')->getPartnerAliases(array('partner_id'=>$partnerId));

        // NOTE:: not sure where the sets below from:
        // $this->set($viewFilters);
        // $this->set('data', $data);
        // $this->set('leads', $leads);
      }

      $this->set('params', $params);
      $this->set('openedTab', $openedTab);
      $this->set('lend_score_config', $this->loadModel('LendScoreConfig')->getLendScoreConfig());
      $this->set('seo', array('meta' => array('title' => $title, 'og' => array('title' => $title))) );

      $userCreated = new \DateTime(!empty($user['created']) ? $user['created'] : null);
      $notice = $this->loadModel('PartnerNotices')->getUseActiveAndNonDismissedNotices($user['partner_user_id'], $userCreated->format('Y-m-d H:i:s'), 'Dashboard','Active');
      $this->set('new_notice', $notice);

      $account = $this->LoadModel('Partners')->getPartner(array('partner_id'=>$partnerId));
      $termsConfig = 'Lend.partner_terms_edited';
      if (strtolower($account['country']) === 'nz') {
        $termsConfig = 'Lend.nz_partner_terms_edited';
      }
      $this->set('terms_agreed_date', $account['terms_agreed_date']);
      $this->set('partner_terms_edited', Configure::read($termsConfig));
      $this->set('partnerId', $partnerId);
      $this->set('uppyConfiguration', json_encode(Configure::read('Lend.UploadManager')));

      $partner_feature = $this->loadModel('PartnerFeatureFlags')->getFeatureFlag(array('partner_id'=>$partnerId));

      if(empty($openedTab) AND !empty($partner_feature['access_to_dashboard_analytics']))
        $this->viewBuilder()->setTemplate('vue_dashboard');
    }

    public function exportAsCsv() {
      $params            = $this->request->getQueryParams();
      $params            = $this->checkDateRangeInCookie($params); // Now using a global timeframe
      $lend_score_config = $this->loadModel('LendScoreConfig')->getLendScoreConfig();

      // So this parload works with getAvailableFiltersByLeads() we'll add a 'status' field...
      $params['status'] = strtolower($params['manage']);
      // Set filters properly
      $status = !empty($params['manage']) ? $params['manage'] : 'Leads';
      if($this->Cookie->read('Dashboard.' . $status . '.filters'))
        $params = array_merge($params,
                    $this->Cookie->read('Dashboard.' . $status . '.filters'));
      if ($params['manage'] AND $params['manage'] != 'Archived') {
        $params['partner_status_id'] = LendStatus::getPartnerStatuses($params['manage'], 'groupName');
      }

      if (!empty($params['manage']) AND $params['manage'] == 'Settled') {
        $leads = $this->Partners->leads->settledLeadsByPartner($this->Auth->user('partner_id'), $params, 'list');
      }
      elseif (!empty($params['manage']) AND $params['manage'] == 'Archived') {
        $leads = $this->Partners->leads->sentLeadsUsingFilters($this->Auth->user('partner_id'), $params, null, 'countArchived');
      }
      else {
        $leads = $this->Partners->leads->sentLeadsUsingFilters($this->Auth->user('partner_id'), $params);
      }

      list($leads, $viewFilters) = $this->Partners->leads->getAvailableFiltersByLeads($leads, $params);

      $columns = $this->request->data('columns');
      $uri = empty($params['manage']) || $params['manage'] !== 'Settled'
          ? $this->_generateOtherLeadsUriData($columns, $leads, $lend_score_config)
          : $this->_generateSettledLeadsUriData($columns, $leads, $lend_score_config, $params);

      return $this->setJsonResponse(array('success'=> true, 'uri'=>$uri));
    }

    protected function _generateSettledLeadsUriData($columns, $leads, $lend_score_config, $params)
    {
      $prefix = 'data:application/csv;charset=utf-8,';
      $uri = '';
      $colMappings = [
        'funded_date' => 'Settled Date',
        'organisation_name' => 'Business Name',
        'first_name' => 'Personal Name',
        'amount_requested' => 'Amount Req. $',
        'product_type' => 'Product',
        'shorthand' => 'Lender',
        'funded_amount' => 'Loan Amount $',
        'funded_type' => 'Type',
        'commission' => 'Commission $',
        'lend_score' => 'LendScore',
      ];
      $columnsTexts = [];
      foreach ($columns as $col) {
        $columnsTexts[] = $colMappings[$col];
      }
      if (!empty($params['partner_aliases']) && in_array('alias_company_name', $columns)) {
          $columnsTexts[] = 'Alias';
      }
      $uri = $prefix . rawurlencode(implode(',', $columnsTexts)).'%0A';

      $lines = [];
      foreach ($leads as $lead) {
        $line = [];
        if (in_array('funded_date', $columns)) {
          $line[] = !empty($lead['funded_date'])
            ? date('d/m/Y', strtotime($lead['funded_date']))
            : '-';
        }
        if (in_array('organisation_name', $columns)) {
          $line[] = str_replace(',', ' ', $lead['organisation_name']);
        }
        if (in_array('first_name', $columns)) {
          $firstName = ucwords($lead['first_name']). ' '.ucwords($lead['last_name']);
          $line[] = str_replace(',', ' ', $firstName);
          unset($firstName);
        }
        if (in_array('amount_requested', $columns)) {
          $line[] = number_format($lead['amount_requested'], 0, '', '');
        }
        if (in_array('product_type', $columns)) {
          $line[] = $lead['product_type'];
        }
        if (in_array('shorthand', $columns)) {
          $line[] = $lead['shorthand'];
        }
        if (in_array('funded_amount', $columns)) {
          $line[] = $lead['funded_amount'] <> 0
                  ? number_format($lead['funded_amount'], 0, '', '')
                  : '-';
        }
        if (in_array('funded_type', $columns)) {
          $line[] = $lead['funded_type'];
        }
        if (in_array('commission', $columns)) {
          $line[] = $lead['commission'] <> 0
                  ? number_format($lead['commission'], 0, '', '')
                  : '-';
        }
        if (in_array('lend_score', $columns)) {
          if (empty($lead['lend_score']) || round($lead['lend_score']) == 0) {
            $lendScore = '-';
          } else {
            $lendScore = ($lead['lend_score']>0 AND (int)$lead['serviceability']==0) ? 30 : round($lead['lend_score']);
            foreach ($lend_score_config['boundaries'] as $b) {
              if($b['boundaries'][0]<=$lendScore AND $b['boundaries'][1]>=$lendScore) {
                $lendScore = $lendScore . ' '. $b['title'];
              }
            }
            unset($b);
          }
          $line[] = $lendScore;
          unset($lendScore);
        }
        if (!empty($params['partner_aliases']) && in_array('alias_company_name', $columns)) {
          $line[] = !empty($lead['alias_company_name'])
                    ? $lead['alias_company_name']
                    : '-';
        }
        $lines[] = rawurlencode(implode(',', $line));
      }
      $uri .= implode('%0A', $lines);
      return $uri;
    }

    protected function _generateOtherLeadsUriData($columns, $leads, $lend_score_config)
    {
      $prefix = 'data:application/csv;charset=utf-8,';
      $uri = '';
      $colMappings = [
        'first_name' => 'Details',
        'lead_ref' => 'Lead Ref',
        'amount_requested' => 'Requested $',
        'product_type' => 'Product',
        'shorthand' => 'Lender',
        'status' => 'Status',
        'last_lender_status' => 'Lender\'s Status',
        'created' => 'Added',
        'added_by' => 'Added By',
        'last_lender_status_created' => 'Last Lender Status Updated',
        'last_note_created' => 'Last Note Updated',
        'callback_overdue_next' => 'Overdue / NextTask',
        'lend_score' => 'LendScore',
        'organisation_name' => 'Organisation Name',
        'product_type_name'=>'Product',
        'is_archived'=>'Archived',
        'lender_name'=>'Lender',
        'lender_logo'=>'Lender logo',
        'purpose_other' => 'Purpose Detail'
      ];
      $columnsTexts = [];
      foreach ($columns as $col) {
        $columnsTexts[] = $colMappings[$col];
      }
      $uri = $prefix . rawurlencode(implode(',', $columnsTexts)).'%0A';

      $lines = [];
      foreach ($leads as $lead) {
        $line = [];
        if (in_array('first_name', $columns)) {
          $firstName = ucwords($lead['first_name']). ' '.ucwords($lead['last_name']) . '@' . $lead['organisation_name'];
          $line[] = str_replace(',', ' ', $firstName);
          unset($firstName);
        }
        if (in_array('lead_ref', $columns)) {
          $line[] = $lead['hashed_lead_id'];
        }
        if (in_array('amount_requested', $columns)) {
          $line[] = number_format($lead['amount_requested'],0 ,'','');
        }
        if (in_array('product_type', $columns)) {
          $line[] = $lead['product_type'];
        }
        if (in_array('shorthand', $columns)) {
          $line[] = $lead['shorthand'];
        }
        if (in_array('status', $columns)) {
          $tmp = !empty($lead['prevention_id'])
                  ? 'Stop sending to lenders'
                  : '';
          $status = $lead['partner_status_id']==1
                    ? $lead['percent_complete'].'%'
                    : $lead['partner_status_name'];
          $line[] = !empty($tmp) ? $tmp . ' ' . $status
                          : $status;
          unset($tmp, $status);
        }
        if (in_array('last_lender_status', $columns)) {
          $line[] = ucwords(str_replace(","," ",$lead['last_lender_status']));
        }
        if (in_array('created', $columns)) {
          $line[] = date('d/m/Y H:i', strtotime($lead['created']));
        }
        if (in_array('added_by', $columns)) {
          $line[] = ucwords($lead['added_by']);
        }
        if (in_array('last_lender_status_created', $columns)) {
          $line[] = !empty($lead['last_lender_status_created'])
            ? date('d/m/Y H:i', strtotime($lead['last_lender_status_created']))
            : '-';
        }
        if (in_array('last_note_created', $columns)) {
          $line[] = !empty($lead['last_note_created'])
            ? date('d/m/Y H:i', strtotime($lead['last_note_created']))
            : '-';
        }
        if (in_array('callback_overdue_next', $columns)) {
          $line[] = !empty($lead['callback_overdue_next'])
            ? date('d/m/Y H:i', $lead['callback_overdue_next'])
            : '-';
        }
        if (in_array('lend_score', $columns)) {
          if (empty($lead['lend_score']) || round($lead['lend_score']) == 0) {
            $lendScore = '-';
          } else {
            $lendScore = ($lead['lend_score']>0 AND (int)$lead['serviceability']==0) ? 30 : round($lead['lend_score']);
            foreach ($lend_score_config['boundaries'] as $b) {
              if($b['boundaries'][0]<=$lendScore AND $b['boundaries'][1]>=$lendScore) {
                $lendScore = $lendScore . ' '. $b['title'];
              }
            }
            unset($b);
          }
          $line[] = $lendScore;
          unset($lendScore);
        }
        $lines[] = rawurlencode(implode(',', $line));
      }
      $uri .= implode('%0A', $lines);
      return $uri;
    }

  public function invalidateApiKey($environment, $apiKeyId)
  {
    $this->request->allowMethod(['GET']);
    $this->loadModel('PartnerApiKeys');

    $partner = $this->PartnerApiKeys->getPartnerByApiKey([
      'id' => $apiKeyId
    ], ($environment === 'sandbox'));

    // Check that the validated partner owns the key
    if ($partner['partner_id'] != $this->Auth->user('partner_id')) {
      return $this->setJsonResponse([
        'success' => false
      ]);
    }

    // Invalidate the api key by ID and environment
    $success = $this->PartnerApiKeys->invalidateApiKey($apiKeyId, ($environment === 'sandbox'));

    $this->setJsonResponse([
      'success' => $success
    ]);
  }

    public function generateNewApiKey($environment)
    {
      $this->request->allowMethod(['GET']);

      // Get the label or set a default (eg. '2020-01-01 Keys')
      $label = $this->request->getQuery('label', date('Y-m-d') . ' Keys');

      $this->loadModel('PartnerApiKeys');

      $environment = strtolower($environment);
      $partnerID   = $this->Auth->user('partner_id');
      $partnerUserID = $this->Auth->user('partner_user_id');

      $newCredentials = $this->PartnerApiKeys->generateNewAPICredentials(
        $partnerID, $environment, $partnerUserID, $label
      );

      $this->setJsonResponse([
          'credentials' => $newCredentials
      ]);
    }

    public function duplicateEmail($email) {
      $data = $this->Partners->partner_users->getPartnerUser(array('email'=>$email));
      if ($data) return true;
      else return false;
    }

    public function getLenderLogos() {
      $this->request->allowMethod(['GET']);
      $lenderLogos = $this->loadModel('Lenders')->getLenderLogos();
      $this->setJsonResponse(array('logos'=>$lenderLogos));

    }

    public function affiliates() {
       $this->loadModel('PartnerProductTypes');
      // Check whether partner is active
      if ($affiliate_id = $this->request->getQuery('affiliate_id')) {
          $partner = $this->Partners->getPartner(['affiliate_id' => $affiliate_id]);
          if (!$partner || empty($partner['active'])) {
            $this->set('accountInactive', true);
            $this->viewBuilder()->setLayout('empty_html');
            return;
        }
      }

      $hashed_lead_ref = $this->request->getQuery('lead_ref');
      if (!empty($hashed_lead_ref)) {
        $lead_id = LendInternalAuth::unhashLeadId($hashed_lead_ref);
        $lead = $this->loadModel('Leads')->getLeadDetails($lead_id);

        if($this->loadModel('Sales')->getSales(array('lead_id'=>$lead_id))){
          $this->set('readonly', 1);
        }
      } elseif ($hashed_lead_ref = $this->request->getQuery('lead_id')) {
        $lead_id = LendInternalAuth::unhashLeadId($hashed_lead_ref);
        $lead = $this->loadModel('Leads')->getLeadDetails($lead_id);

        if($this->loadModel('Sales')->getSales(array('lead_id'=>$lead_id))){
          $this->set('readonly', 1);
        }
      }
      $brokerAssigned = $this->request->getQuery('broker_assigned');
      if (empty($brokerAssigned) && !empty($partner['partner_id'])) {
        $brokerAssigned = $this->loadModel('PartnerUsers')->getPartnerUserRefOfPOC($partner['partner_id']);
      }

      $partnerProductTypes = $this->PartnerProductTypes->groupProductType($this->PartnerProductTypes->getPartnerProductTypes(array('active' => 1), true));
      $variables = array(
        'affiliate_id' => $this->request->getQuery('affiliate_id'),
        'affiliate'    => $this->request->getQuery('affiliate'),
        'send_type'    => $this->request->getQuery('send_type'),
        'force_send'    => $this->request->getQuery('force_send'),
        'partner_external_ref' => $this->request->getQuery('partner_external_ref'),
        'campaign'     => $this->request->getQuery('campaign'),
        'brandcolour'  => str_replace('#','',$this->request->getQuery('brandcolour', '00CF88')),
        'textcolour'   => str_replace('#','',$this->request->getQuery('textcolour', 'ffffff')),
        'questions'    => $this->request->getQuery('questions') ? $this->request->getQuery('questions') : array(),
        'height'       => $this->request->getQuery('height'),
        'sample_form'  => ($this->request->getQuery('sample')==='true'),
        'client_form'  => ($this->request->getQuery('client_form')==='true'),
        'showHeadFoot' => ($this->request->getQuery('hf')==='show'),
        'lead'         => !empty($lead['lead']) ? $lead['lead']:NULL,
        'lead_owner'   => !empty($lead['lead_owner']) ? $lead['lead_owner']:NULL,
        'states'       => array('ACT'=>'Australian Capital Territory (ACT)',
                                'NSW'=>'New South Wales (NSW)',
                                'NT'=>'Northen Territory (NT)',
                                'QLD'=>'Queensland (QLD)',
                                'SA'=>'Southern Australia (SA)',
                                'TAS'=>'Tasmania (TAS)',
                                'VIC'=>'Victoria (VIC)',
                                'WA'=>'Western Australia (WA)'),
        'broker_assigned'       => $brokerAssigned,
        'customer_type'         => $this->loadModel('Leads')->getCustomerType(),
        'residency_status'      => array('Citizen' => 'Citizen',
                                        'Permanent Resident' => 'Permanent Resident',
                                        'Visa' => 'Visa'),
        'business_credit_history' => array('Good' => 'Good',
        'No Credit History'=> 'No Credit History',
        'Paid Defaults'=> 'Paid Defaults',
        'Unpaid Defaults'=> 'Unpaid Defaults',
        'Ex Bankrupt'=> 'Ex Bankrupt',
        'Not Sure'=> 'Not Sure'),
        'partnerProductTypes' => $partnerProductTypes,
      );


      // if (in_array('LP01', $variables['questions'])) `iframe main` branch does not have this //
      $variables['purpose'] = $this->config->getCommercialOnly($this->config->getSpecialConfig('frm_purpose', ['status' => 1]), 'uses', 'set');
      if (in_array('LT01', $variables['questions']))
        $variables['loan_terms'] = $this->config->getConfig('frm_loan_terms', ['status' => 1]);
      if (in_array('IND01', $variables['questions']))
        $variables['industries'] = $this->config->groupIndustry($this->config->getConfig('config_industries'));

      if($this->request->getQuery('alias_ref'))
        setcookie('lend_partner_alias_ref', $this->request->getQuery('alias_ref'), 0, '/');

      $this->set('variables', $variables);

      if (getenv('REGION', true) === 'au') {
        $this->viewBuilder()->setTemplate('affiliates');
      } elseif (getenv('REGION', true) === 'nz') {
        $this->viewBuilder()->setTemplate('affiliates_nz');
      }

      $this->viewBuilder()->setLayout('empty_html');
    }

    public function addAffiliateLead(){
      $partnerDetails = $this->Partners->getPartner(array('partner_id'=>$this->Auth->user('partner_id')));
      $this->set('name', $this->Auth->user('name'));
      $this->set('hyperlink', getenv("DOMAIN_APP", true).'/quote?session=remove&af='.$partnerDetails['affiliate_id']);
    }


    public function leadForms() {
      $partnerDetails = $this->Partners->getPartner(array('partner_id'=>$this->Auth->user('partner_id')));
      if($this->Auth->user('account_admin')){
        $partner_users = $this->Partners->getPartnerUsers($this->Auth->user('partner_id'));
      }else{
        $partner_users = [$this->Auth->user()];
      }

      // For question radios
      $formQuestionsLoan = array(
        // loan questions 
        array( 'code' => 'LA01', 'section'=>'Loan Questions', 'parent' => null,   'label' => 'How much funding are you looking for?*',  'hint' => null, 'default' => true ),
        array( 'code' => 'LP01', 'section'=>'Loan Questions', 'parent' => null,   'label' => 'What do you want the money for?*',        'hint' => null, 'default' => true ),
        array( 'code' => 'MT01','section'=>'Loan Questions',  'parent' => null,   'label' => 'What is your average monthly turnover?', 'hint' => null, 'default' => true ),
        array( 'code' => 'LT01', 'section'=>'Loan Questions', 'parent' => null,   'label' => 'What is your desired loan term?',           'hint' => null, 'default' => false ),
        array( 'code' => 'PO01', 'section'=>'Loan Questions', 'parent' => null,   'label' => 'Are you a property owner?',           'hint' => null, 'default' => false ), // new
        array( 'code' => 'SL01', 'section'=>'Loan Questions', 'parent' => null,   'label' => 'Can you offer security for the loan?',           'hint' => null, 'default' => false ),
        array( 'code' => 'PT01', 'section'=>'Loan Questions', 'parent' => null,   'label' => 'What financial product are you after?', 'hint' => null, 'default' => false),
        array( 'code' => 'CU01', 'section'=>'Loan Questions', 'parent' => null,   'label' => 'Do you invoice customers?', 'hint' => null, 'default' => false),
      );
      // account questions 
      $formQuestionsAccount = array(
        array( 'code' => 'BN01', 'section'=>'Account Questions', 'parent' => null,   'label' => 'What is the business name?*',       'hint' => null, 'default' => true ),
        array( 'code' => 'ST01', 'section'=>'Account Questions', 'parent' => null,   'label' => 'State*',               'hint' => null, 'default' => true ),
        array( 'code' => 'IND01', 'section'=>'Account Questions', 'parent' => null,   'label' => 'What best describes your industry?',            'hint' => null, 'default' => false ),
        //array( 'code' => 'BA01', 'section'=>'Account Questions', 'parent' => null,   'label' => 'What is the principle place of business address?',            'hint' => null, 'default' => false ), // new
        array( 'code' => 'TS01', 'section'=>'Account Questions', 'parent' => null,   'label' => 'What year was the company registered?',       'hint' => null, 'default' => false ),
        array( 'code' => 'EB01', 'section'=>'Account Questions', 'parent' => null,   'label' => 'Do you own an existing business?',       'hint' => null, 'default' => false ), // new
        
        array( 'code' => 'AT01', 'section'=>'Account Questions', 'parent' => null,   'label' => 'Does the business have any ATO Debt?',            'hint' => null, 'default' => false ),
        array( 'code' => 'AM01', 'section'=>'Account Questions', 'parent' => null,   'label' => 'Average Monthly Expenses & Outgoings?',       'hint' => null, 'default' => false ), // new
        array( 'code' => 'AB01', 'section'=>'Account Questions', 'parent' => null,   'label' => (getenv('REGION', true) === 'au' ? 'ABN' : 'NZBN'), 'hint' => null, 'default' => false ),
        array( 'code' => 'AB02', 'section'=>'Account Questions', 'parent' => 'AB01', 'label' => (getenv('REGION', true) === 'au' ? 'with ABN lookup' : 'with NZBN lookup'),     'hint' => 'ABN Lookup is a free service to automatically retrieve additional information from the Australian Business Register.', 'default' => false ),
        array( 'code' => 'CH01', 'section'=>'Account Questions', 'parent' => null,   'label' => 'What is the business credit history?',       'hint' => null, 'default' => false ), // new
      );
      if (getenv('REGION', true) === 'nz') {
        $formQuestionsAccount = array_filter($formQuestionsAccount, function($item) {
          return $item['code'] !== 'ST01';
        });
        $formQuestionsAccount = array_values($formQuestionsAccount);
      }

      // applicant questions 
      $formQuestionsApplicant = array(
        array( 'code' => 'FN01', 'section'=>'Applicant Questions', 'parent' => null,   'label' => 'Full Name*',           'hint' => null, 'default' => true ),
        array( 'code' => 'EA01', 'section'=>'Applicant Questions',  'parent' => null,   'label' => 'Email Address*',       'hint' => null, 'default' => true ),
        array( 'code' => 'CN01', 'section'=>'Applicant Questions', 'parent' => null,   'label' => 'Best Contact Number*', 'hint' => null, 'default' => true ),
        array( 'code' => 'DO01',  'section'=>'Applicant Questions', 'parent' => null,   'label' => 'Date of Birth',       'hint' => null, 'default' => false ), 
        array( 'code' => 'DL01', 'section'=>'Applicant Questions',  'parent' => null,   'label' => 'Driver License Number',     'hint' => null, 'default' => false ), // new   
        array( 'code' => 'RS01', 'section'=>'Applicant Questions',  'parent' => null,   'label' => 'What is your residency status?',       'hint' => null, 'default' => false ), // new

        array( 'code' => 'WU01', 'section'=>'Applicant Questions', 'parent' => null,   'label' => 'Do you have a business Website URL?',       'hint' => null, 'default' => false ),
        array( 'code' => 'OT01',  'section'=>'Applicant Questions', 'parent' => null,   'label' => 'What is your role in the business?', 'hint' => null, 'default' => false),
        // array( 'code' => 'YT01',  'parent' => null,   'label' => 'Annual Revenue',     'hint' => null, 'default' => false ),
        // array( 'code' => 'HO01',  'parent' => null,   'label' => 'Living Situation',     'hint' => null, 'default' => false ),
        
       
      );

      $currentPartnerUser  = $this->LoadModel('PartnerUsers')->getPartnerUser(array('partner_user_id'=>$this->Auth->user('partner_user_id')), false);

      $this->set('partner_users', $partner_users);
      $this->set('formQuestionsLoan', $formQuestionsLoan);
      $this->set('formQuestionsAccount', $formQuestionsAccount);
      $this->set('formQuestionsApplicant', $formQuestionsApplicant);
      $this->set('partnerDetails', $partnerDetails);
      $this->set('seo', array('meta' => array('title' => 'Lead Forms'.$this->lendconfig['nameTag'], 'og' => array('title' => 'Lead Forms'.$this->lendconfig['nameTag']))) );
      $this->set('consentDeclaration', $currentPartnerUser['show_full_app_consent']);

      if($this->request->is('ajax')) {
        $this->set('ajax', true);
        $this->viewBuilder()->setLayout('empty_html');
      }else{
        $this->set('ajax', false);
      }
    }

    public function banners() {
      $partnerDetails = $this->Partners->getPartner(array('partner_id'=>$this->Auth->user('partner_id')));
      $this->set('partnerDetails', $partnerDetails);
      $this->set('seo', array('meta' => array('title' => 'Banners'.$this->lendconfig['nameTag'], 'og' => array('title' => 'Banners'.$this->lendconfig['nameTag']))) );
    }

    public function newAffiliateLead() {
      $affiliateId = $this->Partners->getPartner(array('partner_id'=>$this->Auth->user('partner_id')))['affiliate_id'];
      $this->set('affiliateId', $affiliateId);
    }

    public function aboutLendscore() {
      $this->set('seo', array('meta' => array('title' => 'About Lend Score'.$this->lendconfig['nameTag'], 'og' => array('title' => 'About Lend Score'.$this->lendconfig['nameTag']))) );
    }

    public function uploadPartnerLogo(){

      $data = $this->request->getData();
      $result = $this->Partners->uploadLogo($data, $data['name'], 'partner_logos');
      $logopath = explode('/', $result['s3full'], 2)[1];

      $return = array(
        'type'    => $data['type'],
        'name'    => $result['s3filename'],
        'path'    => $result['s3path'],
        'size'    => $data['files'][0]['size'],
        's3full'  => $logopath
      );

      $result = $this->Partners->updatePartner($data['partner_id'], array('logo'=>$return['s3full']));

      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }
      if (!empty($user['lend_signature_user_ref'])) {
        $partnerRef = \App\Lend\LendInternalAuth::hashPartnerId($data['partner_id']);
        $lendSignClient = new LendSignatureServiceBackendClient($partnerRef, $user['partner_user_ref']);
        $lendSignUser = $lendSignClient->call('GET', '/get-user-by-custom-ref');
        if($lendSignUser['userGroup']['ref'] && isset($return['s3full'])){
          $lendSignUserGroupData = ['ref' => $lendSignUser['userGroup']['ref']];
          $lendSignUserGroupData['logo'] = getenv('DOMAIN_FILES')."/".$return['s3full'];
          $lendSignClient->call('PUT', '/user-groups/'.$lendSignUser['userGroup']['ref'], ['data' => $lendSignUserGroupData]);
        }
      }

			$this->setJsonResponse($return);
    }

    public function updatePartner(){
      $data = $this->request->getData();
      $result = $this->Partners->updatePartner($data['partner_id'], array('logo'=>$data['logo']));
			$this->setJsonResponse($result);
    }

    // public function getPartnerLogo($s3full){
    //   $this->setJsonResponse(array('logo'=>$this->Partners->createSignedRequest($s3full)));
    // }


    // Convert partner type:
    // NOTE:: only for DEMO account for now
    // public function convertPartnerType(){
    //   $partner_id = $this->Auth->user('partner_id');
    //   if($partner_id==2){ // Only allow to demo account for now
    //     if(strtolower($this->Auth->user('partner_type'))==='web affiliate'){
    //       $params = array(
    //         'partner_type' => 'Broker',
    //         'affiliate_id' => ''
    //       );
    //     }else{
    //       $params = array(
    //         'partner_type' => 'Web Affiliate',
    //         'affiliate_id' => LendInternalAuth::hashLeadId($partner_id, 'affiliate')
    //       );
    //     }
    //     $this->Partners->updatePartner($partner_id, $params);
    //   }
    //   $this->redirect('/partners/dashboard');
    // }

    public function apiAccess()
    {
      if (!$this->Auth->user('account_admin')) {
          return $this->redirect('/partners/dashboard');
      }

      $this->loadModel('PartnerApiKeys');

      $partnerID = $this->Auth->user('partner_id');

      $partner = $this->Partners->getPartner(array('partner_id' => $partnerID));
      $sandboxPartner = $partner ? $this->Partners->getSandboxEnvironmentsPartnerAccount($partner) : '';

      $liveApiKeys = $this->PartnerApiKeys->getApiKeys(['partner_id' => $partnerID, 'invalidated is' => null]);
      $sandBoxApiKeys = $this->PartnerApiKeys->getApiKeys(['partner_id' => $sandboxPartner['partner_id'], 'invalidated is' => null], true);

      // Loop over both sets of keys, add an additional array key to keys if they are depreciated
      // and update the depreciated bool to true if any exist
      $deprecated = [];
      $liveApiKeys = array_map(function ($liveKey) use (&$deprecated) {
        $liveKey['deprecated'] = (strlen($liveKey['api_secret']) == 15);

        if ($liveKey['deprecated']) {
          $deprecated['live'] = true;
        }

        return $liveKey;

      }, $liveApiKeys);

      $sandBoxApiKeys = array_map(function ($sandboxKey) use (&$deprecated) {
        $sandboxKey['deprecated'] = (strlen($sandboxKey['api_secret']) == 15);

        if ($sandboxKey['deprecated']) {
          $deprecated['sandbox'] = true;
        }

        return $sandboxKey;

      }, $sandBoxApiKeys);

      $this->set('deprecated', $deprecated);
      $this->set('liveApiKeys', $liveApiKeys);
      $this->set('sandboxApiKeys', $sandBoxApiKeys);
      $this->set('partner', $partner);
      $this->set('sandboxPartner', $sandboxPartner);
      $this->set('seo', ['meta' => [
          'title' => 'API Access'.$this->lendconfig['nameTag'],
          'og' => [
              'title' => 'API Access'.$this->lendconfig['nameTag']
          ]
      ]]);
    }

    public function accountAdminManagement(){
      if (!$this->Auth->user('account_admin'))
        return $this->redirect('/partners/dashboard');
      $partnerID      = $this->Auth->user('partner_id');
      $partner        = $this->Partners->getPartner(array('partner_id' => $partnerID));
      $partnerUsers   = $this->Partners->getPartnerUsers($partnerID);
      $filteredUsers  = array();

      foreach ($partnerUsers as $key => $partnerUser) {
        $partnerUser['is_myself'] = ($this->Auth->user('partner_user_id') == $partnerUser['partner_user_id']);
        unset($partnerUser['partner_id']);
        unset($partnerUser['account_type']);
        unset($partnerUser['account_admin']);
        array_push($filteredUsers, $partnerUser);
      }

      $this->set('partner', $partner);
      $this->set('partnerUsers', $filteredUsers);
      $this->set('seo', array('meta' => array('title' => 'Users'.$this->lendconfig['nameTag'], 'og' => array('title' => 'Users'.$this->lendconfig['nameTag']))) );
    }

    public function inviteNewPartnerUser(){
      $this->request->allowMethod(['POST']);
      $data = $this->request->getData();
      if(!isset($data['level']) || !isset($data['email']))
        return $this->setJsonResponse(['success'=>false, 'message'=>'Invalid parameters']);

      // only account_admin = 1 has permission to invite new user
      if (!$this->Auth->user('account_admin'))
        return $this->setJsonResponse(['success'=>false, 'message'=>'You have no permission.']);

      // check if duplicate email address exist
      if($this->duplicateEmail($data['email']))
        return $this->setJsonResponse(['success'=>false, 'message'=>'Duplicate Email']);

      $params = array();
      $params['level'] = intval($data['level']);
      $params['email'] = $data['email'];
      $params['active'] = 0;
      $params['partner_id'] = $this->Auth->user('partner_id');
      $params['token'] = md5(uniqid(rand(), true));
      $params['access_all_leads'] = empty($data['access_all_leads'])? 1:0;
      // calculate expire time by adding `invite_user_expire`(24) hours to current time
      $params['token_expired'] = date('Y-m-d H:i:s', strtotime(sprintf("+%d hours", Configure::read('Lend.invite_user_expire'))));
      if ($this->Auth->user('account_type') === 'Intermediary') {
        $params['account_type'] = 'Intermediary';
      }
      $this->LoadModel('PartnerUsers')->addPartnerUser($params);


      // send an email to the new invite user
      $adhocData = array(
                    'invited_email' =>  $data['email'],
                    'invite_link'   =>  getenv('DOMAIN_BRO', true).'/activate-new-account/'.$params['token']
                  );
      $this->Partners->partner_notifications->sendPartnerNotifications($this->Auth->user('partner_id'), 'InviteUser', false, $adhocData, $this->Auth->user('partner_user_id'));

      return $this->setJsonResponse(['success'=>true, 'message'=>'Invite Sent']);
    }

    public function resendInvitePartnerUser(){
      $this->request->allowMethod(['POST']);
      $data = $this->request->getData();
      if(!isset($data['partner_user_id']))
        return $this->setJsonResponse(['success'=>false, 'message'=>'Invalid parameters']);

      $user = $this->Auth->user();
      // only account_admin = 1 has permission to invite new user
      if (!$this->Auth->user('account_admin'))
        return $this->setJsonResponse(['success'=>false, 'message'=>'You have no permission.']);

      $partner_user_id = $this->request->getData()['partner_user_id'];
      $partner_users_table = $this->getTableLocator()->get('PartnerUsers');
      $partner_user = $partner_users_table->get($partner_user_id);
      if($user['partner_id'] != $partner_user['partner_id']){
        return $this->setJsonResponse(['success'=>false, 'message'=>'You have no permission.']);
      }
      if($partner_user) {
        $params = array();   
        $params['token'] = md5(uniqid(rand(), true));
        $params['token_expired'] = date('Y-m-d H:i:s', strtotime(sprintf("+%d hours", Configure::read('Lend.invite_user_expire'))));
        $partner_user->token = $params['token'];
        $partner_user->token_expired = $params['token_expired'];
        $partner_users_table->save($partner_user);
      }
      
      // send an email to the new invite user
      $adhocData = array(
                    'invited_email' =>  $partner_user->email,
                    'invite_link'   =>  getenv('DOMAIN_BRO', true).'/activate-new-account/'.$params['token']
                  );
      $this->Partners->partner_notifications->sendPartnerNotifications($this->Auth->user('partner_id'), 'InviteUser', false, $adhocData, $this->Auth->user('partner_user_id'));

      return $this->setJsonResponse(['success'=>true, 'message'=>'Invite Sent']);
    }


    public function modifyPartnerUser(){
      $this->request->allowMethod(['POST']);
      $data = $this->request->getData();

      if(!isset($data['partner_user_id']))
        return $this->setJsonResponse(['success'=>false, 'message'=>'Invalid parameters']);

      // permission check:
      // current user must be account_admin = 1 AND the modified user must has same Partner with current user
      if (!$this->Auth->user('account_admin'))
        return $this->setJsonResponse(['success'=>false, 'message'=>'You have no permission.']);

      $partnerID      = $this->Auth->user('partner_id');
      $partnerUsers   = $this->Partners->getPartnerUsers($partnerID);

      $found = false;
      $email_changed = false;
      foreach ($partnerUsers as $key => $partnerUser) {
        if($partnerUser['partner_user_id'] == $data['partner_user_id']){
          $found = true;
          if(isset($data['email']))
            if($data['email'] != $partnerUser['email'])
              $email_changed = true;
          break;
        }
      }

      if(!$found)
        return $this->setJsonResponse(['success'=>false, 'message'=>'You have no permission.']);

      $params = array();
      if(isset($data['name']))                $params['name']           = $data['name'];
      if(isset($data['level']))               $params['level']          = $data['level'];
      if(isset($data['mobile']))              $params['mobile']         = $data['mobile'];
      if(isset($data['phone']))               $params['phone']          = $data['phone'];
      if(isset($data['contact_title']))       $params['contact_title']  = $data['contact_title'];
      if(isset($data['active']))              $params['active']         = $data['active'];
      if(!empty($data['password']))           $params['password']       = $data['password'];
      if(!empty($data['kanban_colour']))      $params['kanban_colour']  = $data['kanban_colour'];
      if(!empty($data['access_all_leads']))   $params['access_all_leads']       = 0;
      else                                    $params['access_all_leads']       = 1;
      // check if email address changed
      // if changed, need to verify email later
      if($email_changed){
        $params['email'] = $data['email'];
        $params['email_verified'] = 0;
      }

      $success = (bool)$this->Partners->partner_users->updatePartnerUser($data['partner_user_id'], $params);

      if ($success) {
        $subscriptionEnabled = $this->loadModel('PartnerFeatureFlags')->getFeatureFlag(['partner_id'=>$partnerID]);
  
        if ($subscriptionEnabled['access_to_subscription']) {
          $currentPartnerSubPlan = $this->LoadModel('Subscription')->getAllPartnerSubscriptionPlan(['partner_id'=>$partnerID], ['id'=>'DESC'], ['limit'=>1, 'offset'=>0]);
          if ($currentPartnerSubPlan) {
            $partnerActiveUsers = $this->loadModel('PartnerUsers')->getActiveUsers($partnerID);
            $activeUserCount = count($partnerActiveUsers);
            $totalUsers = $currentPartnerSubPlan[0]['total_users'];
            $adminPartnerUser = $this->loadModel('PartnerUsers')->getAdminPartnerUser($partnerID);
            $currentPlan = $this->LoadModel('Subscription')->getSubscriptionPlan(['id' => $currentPartnerSubPlan[0]['subscription_plans_id']]);
            if ($activeUserCount > $totalUsers) {
              $this->LoadModel('App')->postToSlack(":warning: Partner ID: " . $partnerID . " is currently on " . $currentPlan['name'] . " and pays for " . $totalUsers . ". They have just gone over their user cap. Please contact them on " . $adminPartnerUser['mobile'] . " or " . $adminPartnerUser['email'] . " discuss their subscription plan.", 'customer-service');
            }
          }
        }
      }

      return $this->setJsonResponse(['success'=>$success, 'message' => ($success?'The user has been successfully updated.':'There is an error when updating user, Please try again.')]);
    }

    public function revokeUserInvite() {
      try {
          // Ensure that the request is a POST request
          if (!$this->request->is('post')) {
              throw new \Exception("Only POST request is allowed.");
          }
          if (!$this->Auth->user('account_admin'))
            return $this->setJsonResponse(['success'=>false, 'message'=>'You have no permission.']);

          $user = $this->Auth->user();

          if (isset($this->request->getData()['partner_user_id'])) {
              $partner_user_id = $this->request->getData()['partner_user_id'];
              $partner_users_table = $this->getTableLocator()->get('PartnerUsers');
              $partner_user_entity = $partner_users_table->get($partner_user_id);

              if($user['partner_id'] != $partner_user_entity->partner_id){
                return $this->setJsonResponse(['success'=>false, 'message'=>'You have no permission.']);
              }
              if ($partner_users_table->delete($partner_user_entity)) {
                  $this->setJsonResponse(['success' => true, 'message' => 'User invite has been revoked']);
              } else {
                  $this->setJsonResponse(['success' => false, 'message' => 'There is an error when revoking user invite, Please try again']);
              }
          } else {
              $this->setJsonResponse(['success' => false, 'message' => 'Invalid parameters']);
          }
      } catch (RecordNotFoundException $e) {
          $this->setJsonResponse(['success' => false, 'message' => 'User invite not found']);
      } catch (\Exception $e) {
          $this->setJsonResponse(['success' => false, 'message' => $e->getMessage()]);
      }
    }
  
    
    // function to get cross domain website data
    // NEED ajax data: url = website static link, from = string/html node, to = string/html node
    public function getCrossDomainData() {
      $content = '';

      $data = $this->request->getQuery();

      if (isset($data['url']) && isset($data['from']) && isset($data['to'])) {
        $domain = parse_url($data['url']);
        if (strpos($domain['host'], ".lend.") === false) {
          return $this->setJsonResponse(['success' => false,'content' => 'Not a lend domain'], 401);
        }
        $handle = fopen($data['url'], 'r');
        $content = stream_get_contents($handle);
        fclose($handle);

        //filter content (need to be fixed if terms have div inside)
        $content = substr($content, strpos($content, $data['from'])+strlen($data['from']));
        $content = substr($content, 0, strpos($content, $data['to'])-1);

        $pos = strpos($content, '<p>&nbsp;</p>', 0);
        if ($pos !== false) {
          $logo = '<img src="/img/logos/lend.svg" alt="Lend">';
          $content = str_replace('<p>&nbsp;</p>', $logo, $content);
        }
      }

      $return = array(
        'success' => true,
        'content' => $content
      );
			$this->setJsonResponse($return);
    }

    public function potentialPartner() {
      $data = $this->request->getData();

      // Don't bother setting a response, simply return 200 straight away and continue function in background:
      $this->buffer200Status();

      if (empty($data['name']) OR empty($data['email']) OR empty($data['phone']))
        return $this->setJsonResponse(array('success'=>false));

      // Notify us:
      $this->Partners->postToSlack(":eyes: *We currently have a potential partner trying to register.*\nKeep an eye open for them finishing (so we can verify them)...\n*Name is*: ".$data['name'].", \n*Email is*: ".$data['email'].", \n*Phone is*: ".$data['phone'].".", (getenv('REGION', true) === 'au' ? 'partner_verification' : 'partner_verification_nz'));

      $this->setJsonResponse(array('success'=>true));
    }


    public function requestSupport() {
      Log::debug('request support...');
      // $query = $this->request->getQuery();
      //
      // $partnerId = LendInternalAuth::unbase62($query['partner']);
      //
      // Log::debug("$partnerId {$partnerId}");
      //
      // $partnerDetail = $this->LoadModel('Partners')->getPartner(['partner_id'=>$partnerId]);
      //
      // $content = 'who haven\'t submitted leads within 5days since they signed up. They want support';
      //
      // $this->Partners->postToSlack("Partner : {$partnerDetail['company_name']} ({$partnerId}), {$content} ", 'broker-support');
      //
      // $mail = new Email('default');
      // $mail->template('default', 'default');
      // $mail->emailFormat('html');
      // $mail->to('<EMAIL>');
      // $mail->subject("Request support from Partner : {$partnerDetail['company_name']}");
      // $mail->viewVars([
      //   'content' => "Partner : {$partnerDetail['company_name']} ({$partnerId}), {$content}"
      // ]);
      // $mail->send();
      //
      // $this->setJsonResponse(array('success'=>true));
    }

    public function dashboardColumnsSave() {
      $data = $this->request->getData();
      $this->Cookie->write('Dashboard.UserDashPrefs', $data);

      $this->setJsonResponse(array('success'=>true));
    }

    public function doDetailsMatchPartnerAccount() {
      $data   = $this->request->getData();
      $id = !empty($data['partnerId']) ? $data['partnerId'] : (!empty($data['leadRef']) ? $data['leadRef'] : false);

      if ($data['details']['mobile']) $data['details']['mobile'] = preg_replace('/\s+/','', $data['details']['mobile']);
      if ($data['details']['phone']) $data['details']['phone'] = preg_replace('/\s+/','', $data['details']['phone']);

      if (!$id) $this->setJsonResponse(array('success' => false));
      else if (!empty($data['partnerId'])) $broker = $this->loadModel('Partners')->getPartnerUsers($data['partnerId']);
      else if (!empty($data['leadRef'])) $broker = $this->loadModel('Leads')->getPartnerUsers((new LendInternalAuth)->unhashLeadId($data['leadRef']));

      foreach ($data['details'] as $key => $value)
        foreach ($broker as $b) // for partner user account > 1
          if ($b[$key] && $b[$key] == $value) {
            $same = true; break;
          }

      $this->setJsonResponse(array('success' => true, 'same' => isset($same)));
    }

    public function getLeadStatistics() {
      $data = $this->request->getData();

      if (!(new LendInternalAuth)->checkSignature($this->request->getQuery('auth_sig'), $data))
        return $this->setJsonResponse(['success'=>false, 'message'=>'Invalid Signature']);

      if (empty($data['partner_id']) || empty($data['start_date']) || empty($data['end_date']))
        return $this->setJsonResponse(['success'=>false, 'message'=>'Invalid body content']);

      $partnerId = $data['partner_id'];
      $startDate = $data['start_date'];
      $endDate = $data['end_date'];

      $user = $this->Auth->user();
      $passInPartnerUserId = !empty($user['access_all_leads']) ? null: $user['partner_user_id'];
      $totals = $this->Partners->leads->getLeadsTotalByPartner($partnerId, $startDate, $endDate, 'total', $passInPartnerUserId);
      $metrics = $this->Partners->leads->getLeadsTotalByPartner($partnerId, $startDate, $endDate, 'metrics', $passInPartnerUserId);

      return $this->setJsonResponse(['success' => true, 'totals' => $totals, 'metrics' => $metrics]);
    }

    /*
    referrals - reporting page
    */
    public function referrals()
    {
      $partnerId = $this->Auth->user('partner_id');
      list($referrals, $totals, $message) = $this->Partners->getReferralsByPartnerId($partnerId);
      if (!empty($message)) {
        $this->set('message', $message);
      }
      $lenders = $this->loadModel('Lenders')->getLenders(['status'=>1]);
      $this->set('lenders', $lenders);
      $this->set('totals', $totals);
      $this->set('referrals', $referrals);
    }

    /*
    ajax calls on referrals - reporting page
    */
    public function getReferralsChart($type = 'brokers', $months = 12)
    {
      $partnerId = $this->Auth->user('partner_id');
      if (empty($partnerId))
        return $this->setJsonResponse(['success' => false, 'error' => 'Please log in first']);
      if (strtolower($type) === 'brokers')
        $data = $this->Partners->getReferredBrokersByMonths($partnerId, $months);
      elseif (strtolower($type) === 'payments')
        $data = $this->LoadModel('PartnerVbiPayments')->getReferredPaymentsByMonths($partnerId, $months);
      else
        return $this->setJsonResponse(['success' => false, 'error' => ucwords($type). ' Chart unavailable temporarily']);

      return $this->setJsonResponse(['success' => true, 'data' => $data]);
    }

    public function agreeTerms(){
      $partnerID = $this->Auth->user('partner_id');
      $result = $this->Partners->updatePartner($partnerID, array('terms_agreed_date'=>date('Y-m-d H:i:s')));
      return $this->setJsonResponse($result);
    }

    public function uploadPrivacyForm(){
      $this->loadModel('PartnerCustomPrivacyForms');
      $data = $this->request->getData();

      try{

        //Update previous admin job status
        $previousForm = $this->PartnerCustomPrivacyForms->getForm(['partner_id' => $this->Auth->user('partner_id'), 'status' => 'Staff Review']);
        $this->invalidateAdminJob($previousForm['partner_custom_privacy_form_id']);

        // If there are any Privacy forms, update their status as `Deleted`:
        $this->PartnerCustomPrivacyForms->bulkDeletePrivacyForms($this->Auth->user('partner_id'));

        // Create init record from Lend DB:
        $partner_custom_privacy_form_id = $this->PartnerCustomPrivacyForms->createForm(['partner_id'=>$this->Auth->user('partner_id'), 'document_title'=>$data['document_title'], 'status'=>'Processing']);

        // Call signature service to initiate template:
        $sign = new SignatureService('init_template');
        $init_template = $sign->callService(['callback_url'=>getenv('DOMAIN_BRO', true).'/hooks/custom-privacy-updates/index/1']);
        if($init_template['success']){
          // Update template_id to Lend DB:
          $this->PartnerCustomPrivacyForms->updateForm($partner_custom_privacy_form_id, ['service_template_id'=>$init_template['template_id']]);
        }else{
          throw new \Exception($init_template['error']);
        }

        // Upload file to S3:
        $result = $this->Partners->uploadPrivacyForm($data, $init_template['template_id'], $this->Auth->user('partner_id'));
        $fullpath = explode('/', $result['s3full'], 2)[1];

        $this->PartnerCustomPrivacyForms->updateForm($partner_custom_privacy_form_id, ['file_name'=>$data['name'], 's3full'=>$fullpath]);

        // Return created privacy form ID:
        $data['partner_custom_privacy_form_id'] = $partner_custom_privacy_form_id;
        $data['full_path'] = $fullpath;
        return $this->setJsonResponse(['success'=>true, 'data'=>$data]);
      }catch(\Exception $e){
        Log::error($e->getMessage());
        Log::error($e->getTraceAsString());
        return $this->setJsonResponse(['success'=>false, 'error'=>$e->getMessage()]);
      }
    }

    public function generatePfSignedRequest(){
        $data = $this->request->getData();
        $bucket = 'signature-service-templates'.(getenv('LEND_ENV')=='2' ? '' : '-staging');
        $s3Link = $this->LoadModel('Partners')->createPfSignedRequest($bucket . '/' .$data['path'], $bucket, 2);

        return $this->setJsonResponse(['success'=>true, 'data'=>$s3Link]);
    }

    public function invalidateAdminJob($customFormId){
        $this->loadModel('AdminJobs');
        $adminJobs = $this->AdminJobs->getDocuSignAdminJobs(['job_type'=> 'review_docusign', 'job_status'=> 'pending']);

        foreach ($adminJobs AS $job){
            $meta = json_decode($job['meta']);
            if(!empty($meta->service_template_id) ){
                if($customFormId == (int)$meta->partner_custom_privacy_form_id){
                    $this->AdminJobs->updateAdminJob(['job_status'=> 'complete'], $job['job_id']);
                }
            }
        }
    }

    public function switchOnOffPartnerUseLendPrivacyForm(){
      try{
        $data = $this->request->getData();
        $result = $this->Partners->updatePartner($this->Auth->user('partner_id'), array('use_lend_privacy_form'=>$data['use_lend_privacy_form']));
        $custom_privacy_form = $this->loadModel('PartnerCustomPrivacyForms')->getForm(['partner_id' => $this->Auth->user('partner_id'), 'status !=' => 'Deleted'], ['partner_custom_privacy_form_id' => 'DESC']);

        $this->setJsonResponse(['success'=>true, 'data'=>$custom_privacy_form]);
      }catch(\Exception $e){
        Log::error($e->getMessage());
        Log::error($e->getTraceAsString());
        $this->setJsonResponse(['success'=>false, 'error'=>$e->getMessage()]);
      }
    }

    /**
     * This will cancel existing privacy form in processing state
     */
    public function cancelProcessingForm(){
        $data = $this->request->getData();
        try{
            $this->loadModel('PartnerCustomPrivacyForms')->updateForm($data['formId'], ['status' => 'Deleted']);
            $this->setJsonResponse(['success'=>true]);
        }catch (\Exception $e){
            $this->setJsonResponse(['success'=>false, 'error'=>$e->getMessage()]);
        }
    }

    /**
     * This will reprocess privacy form on s3
     */
    public function reProcessS3(){
        try{
            $data = $this->request->getData();
            $sign = new SignatureService('reprocess_template');
            $result = $sign->callService($data);
            $this->setJsonResponse(['success'=>true]);
        }catch (\Exception $e){
            $this->setJsonResponse(['success'=>false, 'error'=>$e->getMessage()]);
        }
    }



    public function deletePartnerCustomPrivacyForm(){
      try{
        $this->loadModel('PartnerCustomPrivacyForms');
        $data = $this->request->getData();
        $this->invalidateAdminJob($data['partner_custom_privacy_form_id']);

        $result = $this->PartnerCustomPrivacyForms->updateForm($data['partner_custom_privacy_form_id'], ['status'=>'Deleted']);
  			$this->setJsonResponse(['success'=>true]);
      }catch(\Exception $e){
        Log::error($e->getMessage());
        Log::error($e->getTraceAsString());
        $this->setJsonResponse(['success'=>false, 'error'=>$e->getMessage()]);
      }
    }

    /**
     * This is the generic thank you page
     */
    public function thankyou($pageType) {
      $this->set('pageContainerVersion', 'fluid');
      $this->set('pageType', $pageType);
    }

    /**
     * This is the sass splash screen
     */
    public function saasTransition() {
      $user = $this->Auth->user();
      $this->set('user', $user);
      if(!$user['account_admin']){
        $admin_partner_user = $this->Partners->partner_users->getPartnerUser(array('partner_id'=>$user['partner_id'], 'account_admin' => true));
        $this->set('admin_partner_user', $admin_partner_user);
      }
    }

    public function checklists(){
      if (getenv('LEND_ENV') == 0) {
        $user = $this->Auth->user();
      } else {
        $user = $this->Auth->identify();
      }
      $requestData = $this->request->getData();
      // dump($this->request->params);die();
      // dump($this->request);die();
      $response = ['data' => ['error' => 'Method not allowed'], 'code' => 405];
      switch ($this->request->getMethod()) {
        case 'POST'://add/update
          $response = ChecklistHelper::addUpdatePartnerChecklist($user['partner_id'], $requestData, isset($requestData['partner_checklist_id']));
          break;
        case 'DELETE':
          $id = filter_var($this->request->getParam('pass.0'), FILTER_VALIDATE_INT) ?: null;
          $response = ChecklistHelper::deletePartnerChecklist($user['partner_id'], $id);
          break;
        case 'GET'://list
          $active = $this->request->getQuery('active');
          $active = ($active === null) ? null : filter_var($active, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE);
          $response = ChecklistHelper::listPartnerChecklists($user['partner_id'], $active);
          break;
        default:
          break;
      }
      $this->setJsonResponse($response['data'], $response['code']);
    }

    
    public function checklistItems() {
      if (getenv('LEND_ENV') == 0) {
        $user = $this->Auth->user();
      } else {
        $user = $this->Auth->identify();
      }
      // $requestData = $this->request->getData();
      // dump($this->request->params);die();
      // dump($this->request);die();
      $response = ['data' => ['error' => 'Method not allowed'], 'code' => 405];
      switch ($this->request->getMethod()) {
        case 'DELETE':
          $id = filter_var($this->request->getParam('pass.0'), FILTER_VALIDATE_INT) ?: null;
          $response = ChecklistHelper::deletePartnerChecklistItem($user['partner_id'], $id);
          break;
        default:
          break;
      }
      $this->setJsonResponse($response['data'], $response['code']);
    }

  }