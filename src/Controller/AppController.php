<?php
/**
 * CakePHP(tm) : Rapid Development Framework (https://cakephp.org)
 * Copyright (c) Cake Software Foundation, Inc. (https://cakefoundation.org)
 *
 * Licensed under The MIT License
 * For full copyright and license information, please see the LICENSE.txt
 * Redistributions of files must retain the above copyright notice.
 *
 * @copyright Copyright (c) Cake Software Foundation, Inc. (https://cakefoundation.org)
 * @link      https://cakephp.org CakePHP(tm) Project
 * @since     0.2.9
 * @license   https://opensource.org/licenses/mit-license.php MIT License
 */
namespace App\Controller;

use App\Auth\LendAuthenticate;
use Cake\Controller\Controller;
use Cake\Event\Event;
use Cake\Core\Configure;
use App\Lend\Config;
use Cake\Log\Log;
use Cake\Utility\Security;
use Firebase\JWT\JWT;
use Cake\ORM\TableRegistry;
use App\Lend\LendInternalAuth;
use DateTime;
use Cake\Datasource\ConnectionManager;

/**
 * Application Controller
 *
 * Add your application-wide methods in the class below, your controllers
 * will inherit them.
 *
 * @link https://book.cakephp.org/3.0/en/controllers.html#the-app-controller
 */
class AppController extends Controller
{

  /**
   * Initialization hook method.
   *
   * Use this method to add common initialization code like loading components.
   *
   * e.g. `$this->loadComponent('Security');`
   *
   * @return void
   */
  public function initialize()
  {
    $valid_subdomains = explode('::', getenv('VALID_SUBDOMAINS'));
    if (isset($_SERVER['HTTP_ORIGIN']) && (in_array($_SERVER['HTTP_ORIGIN'], $valid_subdomains) || strpos($_SERVER['HTTP_ORIGIN'], ".lend.local") !== false)) {
      header("Access-Control-Allow-Origin: " . $_SERVER['HTTP_ORIGIN'] . "");
      header("Access-Control-Allow-Headers: Access-Control-Allow-Headers, Origin, Accept, X-Requested-With, Content-Type, Access-Control-Request-Method, Access-Control-Request-Headers");
    }
    parent::initialize();
    $this->loadComponent('Cookie');
    $this->loadComponent('RequestHandler', [
      'enableBeforeRedirect' => true,
    ]);

    $this->loadComponent('Auth', [
      'storage' => 'Memory',
      'authenticate' => [
        'Form' => [
          'fields' => [
            'username' => 'email',
          ],
          'scope' => [ /* 'PartnerUsers.active'=>1 */],
        ],
        'Lend' => [
          'userModel' => 'PartnerUsers',
          'fields' => [
            'username' => 'email',
          ],
          'scope' => [ /* 'PartnerUsers.active'=>1 */],
          'parameter' => 'auth_token',
          'queryDatasource' => true,
        ]
      ],
      'authorize' => array('Controller'),
      'checkAuthIn' => 'Controller.initialize',
    ]);

    $this->Auth->setConfig('authenticate', [
      'Form' => [
        'userModel' => 'PartnerUsers',
        'fields' => [
          'username' => 'email',
        ],
        'scope' => [ /* 'PartnerUsers.active'=>1 */],
      ]
    ]);
    $this->Auth->setConfig('unauthorizedRedirect', false);
    $this->Auth->setConfig('loginAction', '/login');
    $this->Auth->constructAuthenticate();

    if (getenv('LEND_ENV') == 0 && getenv('LOCAL_ACCOUNT_PARTNER_ID') && empty($this->Auth->user()) && empty($this->Auth->identify())) {
      $newUser = TableRegistry::getTableLocator()->get('PartnerUserEntity')->find('all', [
        'conditions' => ['partner_id' => getenv('LOCAL_ACCOUNT_PARTNER_ID'), 'account_admin' => true]
      ])
        ->first()
        ->toArray();
      unset($newUser['password']);
      $this->Auth->setUser($newUser);
      $token = JWT::encode(
        [
          'sub' => $newUser['email'],
          'exp' => (time() + 86400)
        ],
        Security::getSalt()
      );
      setcookie('auth_token', $token, 0, "/", ".partner.lend.local");
    }

    if ($this->request->getHeader('aid')) {
      $ref = $this->checkAutoLoginCode($this->request->getHeader('aid')[0]);
      $user = [
        'partner_id' => $ref['ref'], // partner_id
        'account_type' => 'Applicant',
        'level' => -1,
        'partner_user_id' => @$ref['partnerUserId'],
      ];
      $this->Auth->setUser($user);
      $user = $this->Auth->user();
      $token = JWT::encode([
          'sub' => 'Applicant',
          'exp' =>  0,
          'user' => $user,
          ],
          Security::getSalt());
          $this->setCookieSameSite('auth_token', $token, 0, "/", "." . $_SERVER['SERVER_NAME'], getenv('LEND_ENV') > 0, true, (getenv('LEND_ENV') > 0 ? "None" : false));
    }

    // $this->loadComponent('Flash');

    /*
     * Enable the following component for recommended CakePHP security settings.
     * see https://book.cakephp.org/3.0/en/controllers/components/security.html
     */
    //$this->loadComponent('Security');
  }

  public function beforeRedirect($event, $url, $response)
  {
    $hostWhitelist = [
      getenv('DOMAIN_PDF'),
      getenv('REACT_URL'),
      getenv('NZ_REACT_URL'),
    ];
    $domainWhitelisted = false;
    foreach ($hostWhitelist as $h) {
      if (strpos($url, $h) !== false) {
        $domainWhitelisted = true;
      }
    }
    $parsedUrl = !empty($url) ? parse_url($url) : null;
    if (
      getenv('LEND_ENV') > 0
      && !$domainWhitelisted
      && isset($parsedUrl['host'])
      && $parsedUrl['host'] !== $_SERVER['HTTP_HOST']
      && strpos($parsedUrl['host'], "lend.com.au") === false
      && strpos($parsedUrl['host'], "lendsign.com") === false
      && strpos($parsedUrl['host'], "s3.ap-southeast-2.amazonaws.com") === false
    ) {
      header("Location: /");
      die();
    }
    if (strpos($_SERVER['HTTP_ACCEPT'], "application/json") !== false && strpos($url, "/login") !== false) {
      header('HTTP/1.1 401 Unauthorized');
      die(json_encode(['success' => false, 'error' => "login required", 'message' => "redirect URL: " . $url]));
    }
  }

  public function isAuthorized($user)
  {
    // $controller = $this->request->params['controller'];
    // $action = $this->request->params['action'];

    // // free user access permission checks
    // $partner = $this->loadModel('Partners')->getPartner(array('partner_id'=>$user['partner_id']));
    // $free_user = ($partner['TEMP_verified_but_no_UI_access']) ? true : false;
    // $free_user_redirect = false;
    // if($free_user){
    //   if($controller == 'Partners' && $action == 'addAffiliateLead')
    //     $free_user_redirect = false;
    //   elseif($controller == 'PartnerUsers' && in_array($action, ['index', 'updateDetails', 'sendSms2fa', 'enable2Fa']))
    //     $free_user_redirect = false;
    //   elseif($controller == 'PartnerNotifications' && $action == 'poll')
    //     $free_user_redirect = false;
    //   else
    //     $free_user_redirect = true;
    // }

    // if($free_user_redirect)
    //   $this->redirect(array('controller' => 'Partners', 'action' => 'addAffiliateLead'));

    return true;
  }

  /** Process questions from last section of the registration wizard
   * @param $params
   * @return array
   */
  protected function _processConfirmList($params, &$fields)
  {

    //$newArray = array();

    if (in_array('qInvestigation', $params)) {
      $fields['partner_user']['q_investigation'] = 1;
    } else {
      $fields['partner_user']['q_investigation'] = 0;
    }

    if (in_array('qMembership', $params)) {
      $fields['partner_user']['q_membership'] = 1;
    } else {
      $fields['partner_user']['q_membership'] = 0;
    }

    if (in_array('qBankrupt', $params)) {
      $fields['partner_user']['q_bankrupt'] = 1;
    } else {
      $fields['partner_user']['q_bankrupt'] = 0;
    }

    if (in_array('qOffence', $params)) {
      $fields['partner_user']['q_offence'] = 1;
    } else {
      $fields['partner_user']['q_offence'] = 0;
    }

    if (in_array('qOtherName', $params)) {
      $fields['partner_user']['q_otherName'] = 1;
    } else {
      $fields['partner_user']['q_otherName'] = 0;
    }


    // Set To processing state
    $fields['partner_user']['is_accredited'] = 'Processing';
    return $fields;
  }

  protected function _formatDataForDocusign($fields)
  {
    $result = [];

    $result['via_sms'] = false;
    $result['via_email'] = true;
    $result['description'] = "Broker Accreditation request from Lend Capital Pty Ltd";
    $result['business']['business_name'] = ($fields['partner']['company_name']) ? $fields['partner']['company_name'] : '';
    $result['business']['abn_acn'] = ($fields['partner']['abn']) ? $fields['partner']['abn'] : '';
    $result['business']['business_address'] = ($fields['partner']['address']) ?
      $fields['partner']['address'] . ', ' . $fields['partner']['suburb'] . ' ' . $fields['partner']['state'] . ' ' . $fields['partner']['postcode'] : '';
    $result['business']['contact_number'] = ($fields['partner_user']['mobile']) ? $fields['partner_user']['mobile'] : '';
    $result['business']['active_lenders'] = "";

    unset(
      $fields['partner']['bank_acc_name'],
      $fields['partner']['bank_bsb'],
      $fields['partner']['bank_acc_num']
      );

    $split_name = explode(" ", $fields['partner_user']['name']);

    $result['template_ids'][] = array("template_id" => "3"); // Prod Id
    $result['recipients'][] = array(
      "first_name" => $split_name[0],
      "last_name" => $split_name[1],
      "middle_name" => "",
      "email" => $fields['partner_user']['email'],
      "mobile" => $fields['partner_user']['mobile']
    );

    $result['callback_url'] = getenv('DOMAIN_BRO', true) . '/hooks/custom-privacy-updates/envelope';
    $result['email'] = array(
      "email_subject" => "Please Sign attached Broker Accreditation from Lend Capital Pty Ltd",
      "email_blurb" => "Lend Capital Pty Ltd sent you a document to review and sign. \n\nKind Regards, \n\nLend Team"
    );

    unset($fields['partner_user']['password'], $fields['password_confirm']);
    $result['meta_data']['raw_registration_data'] = json_encode($fields);

    return $result;
  }

  /** format business activities for docusign fields
   * @param $partnerActivities
   * @param $businessActivities
   * @return string
   */
  protected function _formatPba($partnerActivities, $businessActivities): string
  {

    $selectedActivities = explode(",", $partnerActivities);
    $result = [];

    foreach ($businessActivities as $activity) {
      if (in_array($activity['product_type_id'], $selectedActivities)) {
        $result[] = $activity['product_type_name'];
      }
    }

    return implode(",", $result);
  }

  protected function _writeCookie($name, $value)
  {
    return $this->Cookie->write($name, $value);
  }

  protected function _readCookie($name)
  {
    return $this->Cookie->check($name) ? $this->Cookie->read($name) : null;
  }

  protected function _deleteCookie($name)
  {
    if ($this->Cookie->check($name))
      $this->Cookie->delete($name);
    return true;
  }

  public function beforeFilter(Event $event)
  {
    if (getenv('LEND_ENV') == 0) {
      $user = $this->Auth->user();
    } else {
      $user = $this->Auth->identify();
    }
    if ($user)
      $user['names'] = $this->splitNames(@$user['name']);

    // check if it's free user
    if ($user and !empty($user['partner_id'])) {
      $partner = $this->loadModel('Partners')->getPartner(array('partner_id' => $user['partner_id']));
      $partner_feature = $this->loadModel('PartnerFeatureFlags')->getFeatureFlag(array('partner_id' => $user['partner_id']));
      $this->set('partner_feature', $partner_feature);
      $this->set('free_user', !empty($partner_feature['verified_but_no_UI_access']) ? true : false);
      $this->set('isPremiumUser', empty($partner_feature['verified_but_no_UI_access']) && !empty($partner_feature['access_to_asset_form']));
      $this->set('access_to_billing', !empty($partner_feature['access_to_billing']) ? true : false);
      $this->set('access_to_dashboard_analytics', !empty($partner_feature) && array_key_exists('access_to_dashboard_analytics', $partner_feature) && $partner_feature['access_to_dashboard_analytics']);

      $conPartnerUserSettings = TableRegistry::getTableLocator()
        ->get('ConPartnerUserSettingsEntity')
        ->find('all')
        ->where(['partner_user_id' => $user['partner_user_id']])
        ->first();

      $user['consumer_partner_user_settings_complete'] = $conPartnerUserSettings['status'] === 'complete';


      $aggregator_id =  $partner['aggregator_id'];
      if($aggregator_id){
        $user['aggregator_name'] = TableRegistry::getTableLocator()->get('AggregatorEntity')->find('all')
      ->where(['id' => $aggregator_id])->first()->aggregator_name;
      }else{
        $user['aggregator_name'] = '';
      }

      $user['access_to_billing'] = !empty($partner_feature['access_to_billing']);
      $this->set('user', $user);
      $this->set('access_to_account', !empty($partner_feature) && array_key_exists('access_to_account', $partner_feature) && $partner_feature['access_to_account']);
      $this->set('UiFeatureFlag', TableRegistry::getTableLocator()->get('UiFeatureFlag')->find('all')->first());
      $this->set('access_to_asset_form', !empty($partner_feature) && array_key_exists('access_to_asset_form', $partner_feature) && $partner_feature['access_to_asset_form']);
      $access_to_kanban = $partner['status_system'] === "manual";
      $this->set('access_to_kanban', $access_to_kanban);
      $access_to_call_queue = @$partner_feature['enable_call_queue'];
      $this->set('access_to_call_queue', $access_to_call_queue);
      $access_to_react_commissions = @$partner_feature['access_to_new_commission'];
      $this->set('access_to_react_commissions', $access_to_react_commissions);
      $access_to_consumer_leads = @$partner_feature['access_to_consumer_leads'];
      $this->set('access_to_consumer_leads', $access_to_consumer_leads);
      $access_to_lead_consumer_v2 = @$partner_feature['access_to_lead_consumer_v2'];
      $this->set('access_to_lead_consumer_v2', $access_to_lead_consumer_v2);
      $access_to_automation = @$partner_feature['access_to_automation'];
      $this->set('access_to_automation', $access_to_automation);
      $access_to_referrer = @$partner_feature['access_to_referrer'];
      $this->set('access_to_referrer', $access_to_referrer);
      $REACT_COMMERCIAL_LEADS = @$partner_feature['REACT_COMMERCIAL_LEADS'];
      $this->set('REACT_COMMERCIAL_LEADS', $REACT_COMMERCIAL_LEADS);
      $access_to_tick_and_flick = @$partner_feature['access_to_tick_and_flick'];
      $this->set('access_to_tick_and_flick', $access_to_tick_and_flick);
      $access_to_react_quick_quote = @$partner_feature['access_to_react_quick_quote'];
      $this->set('access_to_react_quick_quote', $access_to_react_quick_quote);
      $access_legacy_commercial_lead = @$partner_feature['access_legacy_commercial_lead'];
      $this->set('access_legacy_commercial_lead', $access_legacy_commercial_lead);

      $access_home_loan = @$partner_feature['access_home_loan'];
      $this->set('access_home_loan', $access_home_loan);

      $access_broker_dash_react = @$partner_feature['access_broker_dash_react'];
      $this->set('access_broker_dash_react', $access_broker_dash_react);

      /**
       * Seeking a time to rebuild above settings, 
       * Please use $partner_feature for any new permission
       **/
      $this->set('partner_feature', $partner_feature);


    }
    // NOTE:: Applicants only can access specific pages:
    if ($user and $user['account_type'] === 'Applicant') {
      $applicants_allow = [
        'Applicants' => ['index', 'update', 'updateIncomeAndExpenses', 'updateAssetAndLiability', 'switchPoc', 'updateAskApplicantPageStatus', 'deleteOwnerEmployment', 'deleteOwnerAddress'],
        'Leads' => ['completeApplication', 'viewQuote', 'checkPercentage', 'getRequiredFieldsData', 'add', 'view', 'viewPartial', 'updateLoanDetails', 'updateLeadProperty', 'updateLeadAsset', 'getBrokerDetails', 'askApplicantEmail', 'updateApplicantResponse', 'finaliseClientChanges', 'getBs', 'updateBusinessDetail', 'getPartnerByLeadRef', 'deleteAddress', 'getLeadOwners', 'checkLeadOwnerHistory', 'getSentLenders', 'viewLenderStatusHistory', 'getLeadOwnerForRetargetMarketing', 'createLeadFromTargetApp'],
        'PartnerUserInfo' => ['getPartnerUserInfo', 'getLeadAssignedUser'],
        'LeadApis' => '*',
        'BankStatements' => '*',
        'PartnerLeadUploads' => ['getS3SignedUploadRequest', 'PrivacyConsentPdf', 'PrivacyForms', 'LendForms'],
        'Partners' => ['thankyou', 'homePage'],
        'Config' => ['uiFeatureFlags'],
        'Configs' => '*',
        'Proxy' => '*',
        'ValidateAid' => ['validateAid'],
        'PartnerUsers' => ['getPartnerByAffiliate'],
        'Hem' => ['getHem'],
        'Documentations' => ['getS3SignedUploadRequest', 'updateUploads', 'viewImage'],
        'Account' => ['view', 'updateTrusteeDetails', 'updateAddresses', 'deleteAddress', 'updateFinances', 'search'],
        'Api' => ['run', ],
        'SendToLender' => ['run'],
        'MatchingEngine' => ['runProfile', 'run'],
        'LeadApisV2' => ['createLeadFromTargetApp', 'getLeadOwnerForRetargetMarketing'],
      ];
      if (!empty($user['newFlow'])) { //new react based flow - used for consumer
        // echo "reached new flow check";
        $applicants_allow = [
          'ValidateAid' => ['validateAid'],
          'PartnerUsers' => ['getPartnerByAffiliate'],
          'Leads' => ['view', 'viewPartial', 'add', 'updateLoanDetails', 'updateLeadProperty', 'updateLeadAsset', 'getBrokerDetails', 'askApplicantEmail', 'updateApplicantResponse', 'finaliseClientChanges', 'getBs', 'updateBusinessDetail', 'getPartnerByLeadRef', 'deleteAddress', 'getRequiredFieldsData', 'getLeadOwners', 'checkLeadOwnerHistory', 'getLeadOwnerForRetargetMarketing', 'createLeadFromTargetApp'],
          'PartnerUserInfo' => ['getPartnerUserInfo', 'getLeadAssignedUser'],
          'Configs' => '*',
          //need to review this
          'Applicants' => ['update', 'updateIncomeAndExpenses', 'updateAssetAndLiability', 'switchPoc', 'updateAskApplicantPageStatus', 'deleteOwnerEmployment', 'deleteOwnerAddress'],
          'Hem' => ['getHem'],
          'Documentations' => ['getS3SignedUploadRequest', 'updateUploads', 'viewImage'],
          'BankStatements' => ['institutions', 'initCustomer', 'mfaCustomer', 'customerAccounts', 'selectedAccounts'],
          'Account' => ['view', 'updateTrusteeDetails', 'updateAddresses', 'deleteAddress', 'updateFinances', 'search'],
          'Proxy' => '*',
          'Config' => ['uiFeatureFlags'],
          'Configs' => ['index'],
          'LeadApis' => '*',
          'Api' => ['run', ],
          'AdhocSignature' => ['getLeadAskApplicantSignatureDocs'],
          'NonSignatureCreditGuide' => ['previewUrl', 'updateNonSignatureConsent'],
          'LeadApisV2' => ['createLeadFromTargetApp', 'getLeadOwnerForRetargetMarketing'],
        ];
      }
      $action = $this->request->getParam('action');
      $controller = $this->request->getParam('controller');
      if (empty($applicants_allow[$controller]) or ($applicants_allow[$controller] !== '*' and !in_array($action, $applicants_allow[$controller]))) {
        // Debug logging for staging
        if (getenv('LEND_ENV') > 0) {
          Log::error("Applicant access denied - Controller: $controller, Action: $action");
          Log::error("Server name: " . $_SERVER['SERVER_NAME']);
          Log::error("User data: " . json_encode($user));
        }
        unset($_COOKIE['auth_token']);
        setcookie('auth_token', '', time() - 3600, "/", "." . $_SERVER['SERVER_NAME']);
        $this->Auth->logout();
        return $this->redirect('/');
      }

    }

    $currentAction = $this->request->getParam('action');

    // Check for applicants auto login:
    if ($this->request->query('aid') && ($currentAction !== "validateAid")) {
      $this->Auth->logout();
      $result = $this->_checkApplicantLogin($this->request->query('aid'));
      if ($currentAction == "viewQuote") {
        return $this->redirect(array('controller' => 'Leads', 'action' => 'viewQuote', 'o' => $result['o'], 'l' => $result['l'], 'q' => $result['q']));
      } else {
        return $this->redirect(array('controller' => 'Leads', 'action' => 'completeApplication', 'o' => $result['o'], 'l' => $result['l'], 'c' => $result['c'], 's' => $result['s']));
      }
    }


    // Quick check to see if there is a special code in the url to auto-log them in
    if ($this->request->query('betalink')) {
      $code = base64_decode($this->request->query('betalink')); // Grab the special auto login code
      $lead_ref = $this->processAutoLogin($code);
      if (!empty($lead_ref)) {
        return $this->redirect('/leads/view/' . $lead_ref);
      } else {
        $this->Flash->error('That link has now expired.');
      }
    } elseif ($this->request->query('crblink')) {
      $code = base64_decode($this->request->query('crblink')); // Grab the special auto login code
      $lead_ref = $this->processAutoLogin($code);
      if (!empty($lead_ref)) {
        // TODO: needs to update frame to Credit Reports Section(does not exist yet)
        return $this->redirect('/leads/view/' . $lead_ref . '?frame=Summary');
      } else {
        $this->Flash->error('That link has now expired.');
      }
    }

    $access = new \App\Lend\AccountLevelAccess;
    if (!$access->isTotalAccessRevoked($user)) {
      $access->setThisUser($user);
      $access->setThisRequest($this->request);
      if (!$access->canUserAccessControllerAction())
        $this->redirect('/');
    }
    ;
    //layout message
    if (!empty($this->request->getQuery('success')))
      $this->message('success', $this->request->getQuery('success'));
    elseif (!empty($this->request->getQuery('error')))
      $this->message('error', $this->request->getQuery('error'));
    $lendconfig = Configure::read('Lend');
    $lendStatusesTable = $this->loadModel('LendStatuses');
    $lendconfig['lender_statuses'] = $lendStatusesTable->getLendStatusesOldFashion();
    $lendconfig['partner_lender_statuses'] = $lendStatusesTable->getPortalStatusesOldFashion();

    // Injecting services based on ENV and feature flags
    $lendconfig['PartnerServices'] = array(
      'Quote' => !empty(getenv('QUOTES_LOOKUP')) ? true : false,
      'Equifax' => !empty(getenv('DOMAIN_EQF')) and (!empty($partner) and $partner_feature['access_to_id_ver']) ? true : false,
      'CreditorWatch' => !empty(getenv('DOMAIN_EQF')) and !empty(getenv('DOMAIN_CRW')) and (!empty($partner) and $partner_feature['access_to_crb']) ? true : false,
      'PrivacyForms' => (!empty($partner) and $partner_feature['access_to_privacy']) ? true : false
    );

    $this->lendconfig = Configure::read('Lend');
    $this->set('user', (isset($user['account_type']) && $user['account_type'] === 'Applicant') ? false : $user);
    $this->set('lendconfig', $lendconfig);

    //write to Cookie as well
    $partnersTable = TableRegistry::get('Partners');
    if (!empty($user['partner_id']) and $partnersTable->getSpecialPartnerIds($user['partner_id'])) {
      $lendPortalSwitching = $partnersTable->getSpecialAccounts();
      $this->_writeCookie('LendPortalSwitching', $lendPortalSwitching);
      $this->set('lendPortalSwitching', $lendPortalSwitching);
    } else
      $this->_deleteCookie('LendPortalSwitching');

    // If the URL contains a Referrer param (rb, "Referred By", in DB: string, 10 length), save who referred them:
    if (!empty($this->request->getQuery('rc')) and strlen($this->request->getQuery('rc')) <= 10)
      $this->_writeCookie('referredBy', $this->request->getQuery('rc'));

    // Set a timeframe for the navigation
    $params = ($this->request->getParam('start_date') and $this->request->getParam('end_date') and $this->request->getParam('range'))
      ? $this->request->getQueryParams() : $this->checkDateRangeInCookie($this->request->getQueryParams());
    //set default timeframe if not a date
    if (!TableRegistry::get('Leads')->isValidDatesRange($params))
      $params = array_merge($params, (new Config)->getDefaultOnloadDates());

    $this->set('navTimeRange', $params);

    // Check any new partner releases
    $newRelease = false;
    $latest_release = TableRegistry::get('PartnerReleases')->getPartnerRelease(['status' => 'Active'], ['release_id' => 'DESC'], 1);
    if (empty($user['viewed_whats_new'])) {
      $newRelease = true;
    } elseif ($latest_release) {
      if (strtotime($latest_release['created']) > strtotime($user['viewed_whats_new'])) {
        $newRelease = true;
      }
    }
    $this->set('newRelease', $newRelease);
  }

  /**
   *
   * Overwrite the CakePHP beforeRender method
   * @param \Cake\Event\Event $event An Event instance
   * @return void
   */
  public function beforeRender(Event $event)
  {
    //do not cached in web browsers
    $this->response = $this->response->withHeader('Expires', 'Sun, 01 Jan 2018 00:00:00 GMT')
      ->withHeader('Cache-Control', 'no-store, no-cache, must-revalidate, max-age=0, post-check=0, pre-check=0')
      ->withHeader('Pragma', 'no-cache');
    $action = $this->request->getParam('action');
    $controller = $this->request->getParam('controller');

    //splash screen for 1st august SAAS transition
    $user = !empty($this->Auth) ? $this->Auth->user() : null;
    if ($user && !empty($user['partner_id'])) {
      $hasCardOnFile = $this->_sassCheckHasCardOnFile();
      $this->set('hasCardOnFile', $hasCardOnFile);
      if (!$hasCardOnFile) {
        if (
          !(($controller == "Billing" && $action == "manage")
            || ($controller == "Partners" && $action == "saasTransition")
            || ($controller == "Partners" && $action == "homePage")
            || ($controller == "PartnerUsers" && $action == "logout")
            || ($controller == "PartnerUsers" && $action == "login")
          )
        ) {
          if (stripos($this->request->getHeader('Accept')[0], 'application/json') !== false) {
            return $this->setJsonResponse(['success' => false, 'error' => 'No Billing Card'], 402);
          } else {
            $this->redirect('partners/saas-transition');
          }
        }
      }
    }
    $this->set('cakeController', $controller);
    $this->set('cakeAction', $action);
    $this->set('isMobile', $this->request->is('mobile'));
  }


  protected function checkDateRangeInCookie($params = array())
  {
    if (isset($params['start_date'], $params['end_date'], $params['range'])) {
      $this->Cookie->write(
        'Navigation.dateRange',
        array(
          'start_date' => $params['start_date'],
          'end_date' => $params['end_date'],
          'range' => $params['range']
        )
      );
    } elseif ($this->Cookie->check('Navigation.dateRange')) {
      $currentDateRange = $this->Cookie->read('Navigation.dateRange');
      if ($currentDateRange['range'] == "All Time") {
        $today = date('Y-m-d', time());
        $dateRange90Day = array(
          'start_date' => date('Y-m-d', strtotime($today . ' -89 day')),
          'end_date' => $today,
          'range' => "Last 90 Days"
        );
        $this->Cookie->write('Navigation.dateRange', $dateRange90Day);
      }
      $params = array_merge($params, $this->Cookie->read('Navigation.dateRange'));
    }
    return $params;
  }


  protected function setJsonResponse($data, $code = 200)
  {
    $this->loadComponent('RequestHandler');
    $this->RequestHandler->renderAs($this, 'json');
    $this->response = $this->response
      ->withType('application/json')
      ->withStatus($code);
    $this->set(['data' => $data, '_serialize' => 'data']);
  }

  public function message($type = 'success', $msg = '')
  {
    $this->set('server_message', array('type' => $type, 'msg' => $msg));
  }

  public function splitNames($name)
  {
    if (empty($name))
      return [];
    $split_name = array_values(array_filter(explode(" ", $name)));
    $first_name = $split_name[0];
    $middle_name = (count($split_name) > 2) ? implode(" ", array_slice($split_name, 1, -1)) : '';
    $last_name = (count($split_name) > 1) ? end($split_name) : NULL;
    return array(
      'name' => $name,
      'name_array' => $split_name,
      'first_name' => ucwords($first_name),
      'middle_name' => ucwords($middle_name),
      'last_name' => ucwords($last_name)
    );
  }

  // Date Diff
  public function getDateDiff($date1, $date2, $format = 'Y-m-d')
  {
    $date_1 = new \DateTime(Date($format, strtotime($date1)));
    $date_2 = new \DateTime(Date($format, strtotime($date2)));
    return $date_1->diff($date_2);
  }

  /*
   * This little-gem of a function uses the little-known about ob_* functions...
   * The Output Buffer functions allows us to return a status code 200 to a
   * request *instantly*, but, continue executing the code of the controller
   * without having to use some sort of queue system.
   * e.g. near the top of a controller: $this->buffer200Status();
   * Client gets a response and we can continue working...
   */
  protected function buffer200Status()
  {
    ignore_user_abort(true); // Even if a user quits the ajax connection, will continue to execute
    ob_start(); // Output buffer
    $serverProtocol = filter_input(INPUT_SERVER, 'SERVER_PROTOCOL', FILTER_SANITIZE_STRING);
    header($serverProtocol . ' 200 OK'); // This is the key / magic for client-side
    header('Content-Encoding: none'); // Disable compression (in case content length is compressed).
    header('Content-Length: ' . ob_get_length());
    header('Connection: close'); // Close the connection.
    ob_end_flush(); // Flush this round of Output Buffering
    // ob_flush(); // Flush all Output Buffering
    flush();
  }

  public function checkPermission($lead_id, $lead = false, $client_access = false, $is_lender_portal = false)
  {
    $user = $this->Auth->user();
    if (empty($user)) {
      $user = $this->Auth->identify();
    }

    if (!$lead) {
      $lead = $this->loadModel('Leads')->getLead(array('lead_id' => $lead_id));
    }

    if (empty($lead)) {
      return array('success' => false, 'message' => 'Cannot find a lead.');
    }
    $lead = json_decode(json_encode($lead), true);

    //if user is AID autheticated and has lead_id on user, this must match requested lead_id
    if ($user['account_type'] === 'Applicant') {
      if (
        (isset($user['lead_id']) && ($user['lead_id'] != $lead_id)) //has lead_id on user, this must match requested lead_id
        || (isset($user['partner_id']) && ($user['partner_id'] != $lead['partner_id']))
      ) { //has partner_id on user, this must match requested lead's partner_id
        return array('success' => false, 'message' => 'Not allowed to access.' . "|" . $user['partner_id'] . "|" . $lead['partner_id'] . "|" . $user['lead_id'] . "|" . $lead_id, 'code' => 401);
      }
    }

    if (
      $lead['source'] === 'APP' ||
      $lead['source'] === 'APP-Consumer' ||
      $lead['source'] === 'APP-iFrame' &&
      $lead['partner_id'] == $user['partner_id']
    ) {
      return array('success' => true);
    }

    if ($client_access and $user['account_type'] === 'Applicant')
      return array('success' => true);

    if ($is_lender_portal)
      return array('success' => true);

    if ($user['account_type'] == 'Intermediary') {
      return array('success' => true);
    }

    if ($user['account_type'] === 'Lender') {
        $lenderLead = TableRegistry::getTableLocator()->get('PartnerLenderLeads')->getLenderLeadDetails($user['partner_id'], $lead_id);
        if(empty($lenderLead['PartnerLenderLead'])) {
            return ['success' => false];
        }
        return ['success' => true];
    }

    if ($lead['partner_id'] != $user['partner_id'] and $user['account_type'] != 'Lend Staff')
      return array('success' => false, 'message' => 'Not allowed to access.');
    elseif (empty($user['access_all_leads']) && !$this->loadModel('PartnerUserLeads')->existingUserLead($lead_id, $user['partner_user_id'], ['ACCESS']))
      return array('success' => false, 'message' => 'Not allowed to access');

    return array('success' => true);
  }


  public function checkLendPartner($partnerId)
  {
    return in_array($partnerId, array(1, 2, 3, 4, 8));
  }

  public function getPartnerContactNumber($partnerUser)
  {
    return !empty($partnerUser['mobile'])
      ? $partnerUser['mobile']
      : (!empty($partnerUser['phone'])
        ? $partnerUser['phone']
        : 'Not Provided');
  }

  private function processAutoLogin($code)
  {
    $codearr = explode('#', $code); // Splits the user id from the signature sent

    //Check expiry date
    $json = json_decode($codearr[0]);
    if (intval($json->expiry_date) > time()) {
      $expected_sig = Security::salt() . '#' . $codearr[0]; // Build the signature we are expecting
      $expected_sig = hash('sha1', $expected_sig); // Build the signature we are expecting
      // Check if the signature sent matches the one we should be expecting
      if ($codearr[1] === $expected_sig) {
        $user = $this->loadModel('PartnerUsers')->getPartnerUser(['partner_user_id' => $json->partner_user_id]);
        if ($user) {
          if ($lead = $this->loadModel('Leads')->getLead(array('lead_ref' => $json->lead_ref))) {
            unset($user['password']);
            $this->Auth->setUser($user);
            $user = $this->Auth->user();
            $user['names'] = $this->splitNames($user['name']);
            $token = JWT::encode(
              [
                'sub' => $user['email'],
                'exp' => (time() + 86400)
              ],
              Security::salt()
            );
            $this->setCookieSameSite('auth_token', $token, 0, "/", "." . $_SERVER['SERVER_NAME'], getenv('LEND_ENV') > 0, true, (getenv('LEND_ENV') > 0 ? "None" : false));
            return $json->lead_ref;
          }
        }
      }
    } else {
      return false;
    }
  }

  private function _checkApplicantLogin($aid)
  {
    $code = base64_decode($aid); // Grab the special auto login code
    $codearr = explode('#', $code); // Splits the user id from the signature sent

    //Check expiry date
    $lead_cred = explode('l3nd', $codearr[0]);
    if (empty($lead_cred) and count($lead_cred) !== 2 and intval($lead_cred[1]) < time())
      die('This link has now expired.');

    $expected_sig = Security::getSalt() . '#' . $codearr[0]; // Build the signature we are expecting
    $expected_sig = hash('sha1', $expected_sig); // Build the signature we are expecting

    // Check if the signature sent matches the one we should be expecting
    if ($codearr[1] !== $expected_sig)
      die('Unexpected token.');

    $additionalCode = !empty($codearr[2]) ? $codearr[2] : null;
    $quoteRef = !empty($codearr[3]) ? $codearr[3] : null;
    $leadid = (new LendInternalAuth)->unhashLeadId($lead_cred[0]);

    if (empty($leadid))
      die("Can't find a lead.");
    $sections = '';
    if (isset($additionalCode)) {
      parse_str($additionalCode, $parseCode);
      $applicant = @$parseCode['applicant'];
      $first_name = @$parseCode['first_name'];
      $last_name = @$parseCode['last_name'];
      $sections = @$parseCode['sections'];
    }
    if (isset($quoteRef)) {
      extract(parse_ini_string($quoteRef));
    }

    $ownerRef = !empty($applicant) ? (new LendInternalAuth)->unhashLeadId($applicant) : '';
    $leadRef = (new LendInternalAuth)->hashLeadId($leadid);
    $code = $this->request->getQuery('code') ? $this->request->getQuery('code') : '';

    // If it's applicant login and its name has been changed, NOT allowed to login.
    if (!empty($ownerRef)) {
      $name = $this->loadModel('LeadOwners')->getOwnerIdName($ownerRef);
      if (@$first_name != $name['first_name'] or @$last_name != $name['last_name']) {
        die('This link has now expired.');
      }
    }

    $user = [
      'lead_id' => $leadid,
      'lead_ref' => $leadRef,
      'owner_id' => $ownerRef,
      'owner_ref' => @$applicant,
      'account_type' => 'Applicant',
      'level' => -1,
    ];
    $this->Auth->setUser($user);
    $user = $this->Auth->user();
    $token = JWT::encode(
      [
        'sub' => 'Applicant',
        'exp' => (time() + 86400),
        'user' => $user,
      ],
      Security::salt()
    );
    $this->setCookieSameSite('auth_token', $token, 0, "/", "." . $_SERVER['SERVER_NAME'], getenv('LEND_ENV') > 0, true, (getenv('LEND_ENV') > 0 ? "None" : false));
    return array('o' => $ownerRef, 'l' => $leadRef, 'c' => $code, 'q' => $quoteRef, 's' => $sections);
  }


  /*
  Partner IS NOT IN
    Partner Type in (Web Affiliate, Mortgage Broker)
    or
    is_affilate = 1
    or
    is_lend =1
    or
    is_test = 1
    AND
    No active credit card on file

    AND
    Date > 31st Jul 2023 
  */
  private function _sassCheckHasCardOnFile()
  {
    // return true;
    $partner = $this->LoadModel('Partners')->getPartner(array('partner_id' => $this->Auth->user('partner_id')));
    if ($partner['partner_type'] == 'Lender') {
      return true;
    }
    if (in_array($partner['broker_type'], ['Mortgage Broker', 'Affiliate / Lead Gen'])) {
      return true;
    }
    if ($partner['is_affiliate'] == '1' || $partner['is_lend'] == '1' || $partner['is_test_acc'] == '1' || strtolower($partner['country']) === 'nz') {
      return true;
    }
    $billing = $this->LoadModel('TmpPartnerBilling')->getTmpPartnerBilling(['partner_id' => $partner['partner_id']]);
    if (!empty($billing)) {
      return true;
    }
    return false;
  }

  public function checkAutoLoginCode($aid) {
    $code = base64_decode($aid); // Grab the special auto login code
    $codearr = explode('#', $code); // Splits the user id from the signature sent

    //Check expiry date
    $cred = explode('l3nd', $codearr[0]);
    if(!empty($cred) AND count($cred)!==2 AND intval($cred[1]) < time())
      die('This link has now expired.');

    $expected_sig = Security::getSalt().'#'.$codearr[0]; // Build the signature we are expecting
    $expected_sig = hash('sha1', $expected_sig); // Build the signature we are expecting

    // Check if the signature sent matches the one we should be expecting
    if ( $codearr[1] !== $expected_sig )
      die('Unexpected token.');

    if ($cred[2] === 'Applicants') {
      $ref = (new LendInternalAuth)->unhashLeadId($cred[0]);
    } else {
      $ref = $cred[0];
    }
    return [
      'ref' => $ref,
      'source' => @$cred[2],
      'page' => @$cred[3],
      'partnerUserId' => @$cred[4]
    ];
  }
  public function checkPartnerAccountBelongsToUser($accountRef=null, $userPartnerId=null, $accountPartnerId=null, $autoLogin = false) {
    $this->loadModel('PartnerAccounts');

    if (empty($userPartnerId)) {
      $user = $this->Auth->user();
      if (empty($user)) {
        $user = $this->Auth->identify();
      }
      if (empty($user) && $autoLogin) {
        $ref = $this->checkAutoLoginCode($this->request->query('code'));
        if (!empty($ref['ref'])) {
          return true;
        }
      }else if($user['partner_id']){
        $userPartnerId = $user['partner_id'];
      }else if($user['newFlow'] && $user['account_type'] == "Applicant" && $user['lead_id']){
        $lead = TableRegistry::getTableLocator()->get('LeadEntity')->get($user['lead_id'])->toArray();
        $userPartnerId = $lead['partner_id'];
      }
    }

    if (empty($accountPartnerId) && !empty($accountRef)) {
      $accountPartnerId = $this->PartnerAccounts->getPartnerIdByAccountRef($accountRef);
    }

    if(empty($userPartnerId) || empty($accountPartnerId) || $userPartnerId !== $accountPartnerId){
      return false;
    }

    return true;
  }

  public function setCookieSameSite(
    string $name,
    string $value,
    int $expire,
    string $path,
    string $domain,
    bool $secure,
    bool $httponly,
    $samesite = 'None'
  ): void {
    if (PHP_VERSION_ID < 70300) {
      setcookie($name, $value, $expire, $path . ($samesite === false ? '' : '; samesite=' . $samesite), $domain, $secure, $httponly);
      return;
    }
    $options = [
      'expires' => $expire,
      'path' => $path,
      'domain' => $domain,
      'secure' => $secure,
      'httponly' => $httponly,
    ];
    if ($samesite !== false) {
      $options['samesite'] = $samesite;
    }
    setcookie($name, $value, $options);
  }

  protected function filterContentByLeadAccess($content) {
    $user = $this->Auth->user();
    if (empty($user)) {
      $user = $this->Auth->identify();
    }
    if (empty($user)) {
      return [];
    }
    // Access rules: if account_admin, all for partner_id.
    if ($user['account_admin'] === true) {
      $leadsWithAccess = TableRegistry::getTableLocator()
        ->get('LeadEntity', [
          'connection' => ConnectionManager::get('reader_db')
        ])
        ->find()
        ->where(['partner_id' => $user['partner_id']])
        ->toArray();
    }
    // Else: all from partner_user_leads.
    // Else: check lead.user_id
    else {
      $leadsWithAccess = TableRegistry::getTableLocator()
        ->get('LeadEntity', [
          'connection' => ConnectionManager::get('reader_db')
        ])
        ->find()
        ->contain(['PartnerUserLeadsEntity'])
        ->where([
          'PartnerUserLeadsEntity.partner_user_id' => $user['partner_user_id'],
          'PartnerUserLeadsEntity.status <> "REVOKED"',
        ])
        ->toArray();

      $leadsWithAccessByUserId = TableRegistry::getTableLocator()
        ->get('LeadEntity', [
          'connection' => ConnectionManager::get('reader_db')
        ])
        ->find()
        ->where(['user_id' => $user['partner_user_id']])
        ->toArray();

      $leadsWithAccess = array_merge($leadsWithAccess, $leadsWithAccessByUserId);
    }

    $leadIds = [];
    foreach ($leadsWithAccess as $lead) {
      $leadIds[] = $lead->lead_id;
    }
    foreach ($content as $key => $value) {
      if (!in_array($value['lead_id'], $leadIds)) {
        unset($content[$key]);
      }
    }
    return array_values($content);
  }

  /**
   * Get From date based on duration and 'to' date
   * @param mixed $years
   * @param mixed $months
   * @param mixed $toValue
   * @return mixed
   */
  protected function getDurationBasedFromDate($years = 0, $months = 0, $toValue = null){
    try {
      if (empty($years)) {
        $years = 0;
      }
      if (empty($months)) {
        $months = 0;
      }
      $toDate = $toValue ? new DateTime($toValue) : new DateTime(); //Use current date and time if $toValue is not provided
      // Subtract years and months from the date
      $toDate->modify("-$years years");
      $toDate = new DateTime($toDate->format('Y-m-01 00:00:00'));
      $toDate->modify("-$months months");
      return $toDate->format('Y-m-01 00:00:00');
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
    }
  }

  /**
   * Get duration in months and years based on from and to dates
   * @param mixed $from
   * @param mixed $to
   * @return array
   */
  protected function getDuration($from, $to = null){
    try {
      $toDate = !empty($to) ? new DateTime($to) : new DateTime();
      $fromDate = new DateTime($from);
      $interval = $toDate->diff($fromDate);
      $durationYears = $interval->y;
      $durationMonths = $interval->m;
      // Adjust months if day of 'to' is less than day of 'from'
      if ($toDate->format('j') < $fromDate->format('j')) {
        $durationMonths--;
        // Adjust years and months if months became negative
        if ($durationMonths < 0) {
          $durationYears--;
          $durationMonths += 12;
        }
      }
      return ['years' => $durationYears, 'months' => $durationMonths];
    }
    catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return ['years' => 0, 'months' => 0, 'error'=> $e->getMessage()];
    }
  }

  /**
   * Readjust address/employment from and to dates based on order starting from most recent record
   * Used when an address/employment is deleted and from/to dates need to be readjusted
   * @param mixed $table
   * @param mixed $column
   * @param mixed $value
   * @return void
   */
  protected function readjustAddressEmploymentDurations($table, $column, $value){
    try {
      if (!in_array($table, ['LeadOwnerAddressesEntity', 'LeadAddressEntity', 'LeadOwnerEmploymentEntity']))
        return;
      $addressTable = TableRegistry::getTableLocator()->get($table);
      $idCol = $addressTable->getPrimaryKey();
      $addresses = $addressTable->find('all')->where([$column => $value, 'status' => 'active'])->order([$idCol => 'ASC'])->toArray();//get most recent record first
      $updateData = [];
      $toDate = null;
      foreach($addresses as $address){
        $duration = $this->getDuration($address['date_from'], $address['date_to']);
        $fromDate = $this->getDurationBasedFromDate($duration['years'], $duration['months'], $toDate);//calculate from
        $data = [];
        $data[$idCol] = $address[$idCol];
        $data['date_from'] = $fromDate;
        $data['date_to'] = $toDate;
        $updateData[] = $data;
        $toDate = $fromDate;//for next row
      }
      $addressesUpdated = $addressTable->patchEntities($addresses, $updateData);
      $addressTable->saveMany($addressesUpdated);
    }
    catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
    }
  }

  protected function __validateUpload($file)
  {
    // whitelist of valid file mime types:
    $validExtensions = [
      'application/json',
      'application/msword',
      'application/pdf',
      'application/postscript',
      'application/vnd.ms-outlook',
      'image/bmp',
      'image/gif',
      'image/heic',
      'image/heif',
      'image/jpeg',
      'image/png',
      'image/svg+xml',
      'image/tiff',
      'image/webp',
      'image/x-adobe-dng',
      'message/rfc822',
      'text/plain',
    ];
    $fileMimeType = mime_content_type($file['tmp_name']);

    if (!in_array($fileMimeType, $validExtensions)) {
      // special check for mime started with 'application/vnd.ms-excel' and 'application/vnd.openxmlformats-officedocument'
      if (strpos($fileMimeType, 'application/vnd.ms-excel') === 0 || strpos($fileMimeType, 'application/vnd.openxmlformats-officedocument') === 0) {
        return true;
      }
      return false;
    }

    return true;
  }

  protected function __validatePdfUpload($file)
  {
    // Check if file is a valid PDF by checking the MIME type
    $fileMimeType = mime_content_type($file['tmp_name']);
    if ($fileMimeType !== 'application/pdf') {
      return $file['tmp_name'];
    }

    // Check the file extension
    $fileExtension = pathinfo($file['name'], PATHINFO_EXTENSION);
    if (strtolower($fileExtension) !== 'pdf') {
      return $file['tmp_name'];
    }

    try {
      // upload file to temporary folder
      $uploadDir = ROOT . '/tmp/pdfs';
      if (!is_dir($uploadDir)) {
          mkdir($uploadDir, 0777, true); // Create the directory if it doesn't exist
      }
      $uploadedFilePath = $uploadDir . '/' . basename($file['name']);
      $sanitizedFilePath = $uploadDir . '/sanitized_' . basename($file['name']);
        
      // Move the uploaded file to the server
      if (!copy($file['tmp_name'], $uploadedFilePath)) {
        throw new \Exception('Failed to copy uploaded file');
      }

      // Get the raw content of the PDF (including objects and streams)
      $rawContent = file_get_contents($uploadedFilePath);

      // Search for potential JavaScript-related entries
      $jsPatterns = ['\/JavaScript', '\/JS', '\/OpenAction'];
      $js_detected = false;
      foreach ($jsPatterns as $pattern) {
        if (preg_match('/' . $pattern . '/', $rawContent)) {
          $js_detected = true;
          break;
        }
      }
      if ($js_detected) {
        $this->_sanitizePdf($uploadedFilePath, $sanitizedFilePath);
        unlink($uploadedFilePath);
        return $sanitizedFilePath;
      } else {
        return $uploadedFilePath;
      }
    } catch (\Exception $e) {
      unlink($uploadedFilePath);
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }

  private function _sanitizePdf($inputFile, $outputFile) {
    $gsPath = getenv('LEND_ENV') == '0' ? '/usr/local/bin/gs' : 'gs';
    $command = $gsPath . " -o " . escapeshellarg($outputFile) . " -sDEVICE=pdfwrite -dPDFSETTINGS=/prepress " . escapeshellarg($inputFile);
    exec($command, $output, $returnVar);
    Log::error($output);
    Log::error($returnVar);

    if ($returnVar !== 0) {
        throw new \Exception("Failed to sanitize PDF: " . implode("\n", $output));
    }
    return $outputFile;
  }

}