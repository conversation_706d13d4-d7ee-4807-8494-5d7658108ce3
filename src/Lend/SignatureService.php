<?php
namespace App\Lend;

use Cake\ORM\Table;
use App\Model\Table\AppTable;
use Cake\Core\Configure;
use Cake\Log\Log;

class SignatureService extends AppTable {

  private $service_configs = [
    'get_envelope' => [
      'method' => 'get',
      'url' => '/envelopes'
    ],
    'get_multiple_envelopes' => [
      'method' => 'get',
      'url' => '/get-multiple-envelopes'
    ],
    'get_envelope_by_docusign_ref' => [
      'method' => 'get',
      'url' => '/envelope-by-docusign-ref'
    ],
    'new_envelope' => [
      'method' => 'post',
      'url' => '/envelopes'
    ],
    'init_template' => [
      'method' => 'post',
      'url' => '/init-template'
    ],
    'reprocess_template' => [
      'method' => 'post',
      'url' => '/process-form'
    ]
  ];
  private $service_type = null;

	function __construct($service_type = 'get_envelope') {
		parent::__construct(array());
    if($this->_validateServiceType($service_type))
      $this->service_type = $service_type;
	}

  public function switchServiceType($service_type){
    if($this->_validateServiceType($service_type))
      $this->service_type = $service_type;
  }

	public function callService($fields=null){
    switch($this->service_configs[$this->service_type]['method']){
      case 'get':   return $this->_callGetRequest($fields); break;
      case 'post':  return $this->_callPostRequest($fields); break;
    }
  }


  private function _validateServiceType($service_type){
    if(empty($this->service_configs[$service_type]))
      throw new \Exception("Can't find service type.");
    return true;
  }

    private function _generateSignature($fields)
    {
        return base64_encode(hash_hmac('sha256', json_encode($fields, JSON_UNESCAPED_SLASHES | JSON_HEX_APOS) . getenv('SIGNATURE_SERVICE_SECRET'), getenv('SIGNATURE_SERVICE_SECRET'))); // to base64
    }

    private function _callPostRequest($fields)
    {
      \Cake\Log\Log::info('START request Signature Service - '. $this->service_type);
      \Cake\Log\Log::info(json_encode($fields));
        $signature = $this->_generateSignature($fields);
        $curl = curl_init();
        $entries = ["accept: application/json", "content-type: application/json"];

        curl_setopt_array($curl, array(
            CURLOPT_URL => getenv('DOMAIN_SIGNATURE_SERVICE') . $this->service_configs[$this->service_type]['url'] . '?signature=' . $signature,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_HTTPHEADER => $entries,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($fields, JSON_UNESCAPED_SLASHES | JSON_HEX_APOS)
        ));

        $result = curl_exec($curl);
        if (!$result) {
          $result = [];
          $result['error'] = curl_error($curl);
        }
        \Cake\Log\Log::info(json_encode($result));

        if ($this->_isJson($result)) {
            $result = json_decode($result, true);
        }

        return $result;
    }

	private function _callGetRequest($data){
		$curl = curl_init();
		$entries = ["accept: application/json", "content-type: application/json"];
        $id = (!empty($data)) ? "/$data" : "";

		curl_setopt_array($curl, array(
			CURLOPT_URL             => getenv('DOMAIN_SIGNATURE_SERVICE') . $this->service_configs[$this->service_type]['url'] . $id,
			CURLOPT_RETURNTRANSFER  => true,
			CURLOPT_ENCODING        => "",
			CURLOPT_MAXREDIRS       => 10,
			CURLOPT_TIMEOUT         => 30,
			CURLOPT_HTTP_VERSION    => CURL_HTTP_VERSION_1_1,
			CURLOPT_CUSTOMREQUEST   => 'GET',
			CURLOPT_HTTPHEADER      => $entries,
			CURLOPT_POST            => false,
		));

		$result = curl_exec($curl);

		if($this->_isJson($result)) {
			$result = json_decode($result, true);
		}

		return $result;
	}

	private function _isJson($string) {
    if (is_array($string)) {
      return false;
    }
		if (is_object(json_decode($string))) {
			return true;
		}else{
			return false;
		}
	}


}
