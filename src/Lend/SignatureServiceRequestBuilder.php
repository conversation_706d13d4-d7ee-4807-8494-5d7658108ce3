<?php

namespace App\Lend;

use App\Enums\LendSignTemplateUse;
use Cake\ORM\TableRegistry;
use Cake\Datasource\ConnectionManager;
use Cake\Http\Client;
use Cake\Core\Configure;
use Exception;

class SignatureServiceRequestBuilder
{

    protected $leadRef;
    protected $applicantRef;
    protected $templates;
    protected $lead;
    protected $user;
    protected $requestBody;
    protected $leadPartner;
    protected $leadPartnerUser;
    protected $leadOwners;
    protected $activeLendersString;
    protected $conPartnerUserSettings;

    public function __construct($leadRef = null, $user, $applicantRef = null)
    {
        $this->leadRef = $leadRef;
        $this->applicantRef = $applicantRef;
        $this->user = $user;
        $this->requestBody = [];
    }

    protected function getLead()
    {
        if (!$this->leadRef) {
            return null;
        }
        if (!empty($this->lead)) {
            return $this->lead;
        }
        $leads = TableRegistry::getTableLocator()
            ->get('LeadEntity')
            ->find()
            ->where(['lead_ref' => $this->leadRef])
            ->toArray();
        if (empty($leads)) {
            throw new \Exception('Lead not found');
        }
        $this->lead = array_shift($leads);
        return $this->lead;
    }

    protected function getLeadCurrentAddress()
    {
        $leadAddress = TableRegistry::getTableLocator()
            ->get('LeadAddressEntity')
            ->find()
            ->where(['lead_id' => $this->lead->lead_id, 'date_to IS NULL'])
            ->first();
        return $leadAddress ? $leadAddress->full_address : '';
    }

    protected function getAccountCurrentAddress()
    {
        $leadAddress = TableRegistry::getTableLocator()
            ->get('PartnerAccountEntity')
            ->find()
            ->where(['lead_id' => $this->lead->lead_id, 'date_to IS NULL'])
            ->first();
        return $leadAddress ? $leadAddress->full_address : '';
    }

    protected function getLeadPartner()
    {
        if (!empty($this->leadPartner)) {
            return $this->leadPartner;
        }

        $leadPartners = TableRegistry::getTableLocator()
            ->get('PartnerEntity')
            ->find()
            ->where(['partner_id' => $this->getLead()->partner_id])
            ->toArray();

        if (empty($leadPartners)) {
            throw new \Exception('Lead partner not found');
        }

        $this->leadPartner = array_shift($leadPartners);
        return $this->leadPartner;
    }

    protected function getLeadOwners()
    {
        $lead = $this->getLead();
        if (!empty($this->leadOwners)) {
            return $this->leadOwners;
        }
        $leadOwners = TableRegistry::getTableLocator()
            ->get('LeadOwnersEntity')
            ->find()
            ->contain(['CurrentAddress', 'CurrentEmployment'])
            ->where(['LeadOwnersEntity.lead_id' => $lead->lead_id])
            ->toArray();

        $this->leadOwners = $leadOwners;
        return $this->leadOwners;
    }

    protected function getLeadPartnerUser()
    {
        if (!empty($this->leadPartnerUser)) {
            return $this->leadPartnerUser;
        }
        $leadPartnerUsers = TableRegistry::getTableLocator()
            ->get('PartnerUserEntity')
            ->find()
            ->where(['partner_user_id' => $this->user['partner_user_id']])
            ->toArray();

        if (empty($leadPartnerUsers)) {
            throw new \Exception('Lead partner user not found');
        }
        $this->leadPartnerUser = array_shift($leadPartnerUsers);
        return $this->leadPartnerUser;
    }

    public function getPartnerAccountAndPeople()
    {
        if (empty($this->applicantRef)) {
            return null;
        }
        $applicantId = LendInternalAuth::unhashPartnerAccountPeopleId($this->applicantRef);
        $applicant = TableRegistry::getTableLocator()
            ->get('PartnerAccountPeopleEntity')
            ->find()
            ->where(['id' => $applicantId])
            ->first();
        $partnerAccount = TableRegistry::getTableLocator()
            ->get('PartnerAccountEntity')
            ->find()
            ->contain(['CurrentTradingAddress', 'PartnerAccountMetaEntity'])
            ->where(['PartnerAccountEntity.partner_account_id' => $applicant->partner_account_id])
            ->first();
        $partnerUserId = $partnerAccount->partner_user_id;
        $partnerUser = TableRegistry::getTableLocator()
            ->get('PartnerUserEntity')
            ->find()
            ->where(['partner_user_id' => $partnerUserId])
            ->first();
        $partnerUser = $this->getLeadPartnerUser();
        $partner = TableRegistry::getTableLocator()
            ->get('PartnerEntity')
            ->find()
            ->where(['partner_id' => $partnerAccount->partner_id])
            ->first();
        return [
            "applicant" => $applicant,
            "partnerAccount" => $partnerAccount,
            "partnerUser" => $partnerUser,
            "partner" => $partner
        ];
    }

    protected function getActiveLendersString()
    {
        if (!empty($this->activeLendersString)) {
            return $this->activeLendersString;
        }

        $activeLenders = TableRegistry::getTableLocator()->get('Lenders')->getActiveLenderList();
        $activeLendersString = "";
        if (!empty($activeLenders)) {
            $activeLendersString .= $activeLenders[0]['lender_name'];
            for ($i = 1; $i < sizeof($activeLenders); $i++) {
                $activeLendersString .= ', ' . $activeLenders[$i]['lender_name'];
            }
        }
        $this->activeLendersString = $activeLendersString;
        return $this->activeLendersString;
    }

    public function wordWrapToAddresses()
    {
        $this->requestBody['extra_fields']['External ACL Address']
            = wordwrap(@$this->requestBody['extra_fields']['External ACL Address'], 45, "\r\n");
        $this->requestBody['con_partner_user_settings']['address']
            = wordwrap(@$this->requestBody['con_partner_user_settings']['address'], 45, "\r\n");
        return $this;
    }

    public function setCallback($callback)
    {
        $this->requestBody['callback_url'] = $callback;
        return $this;
    }

    public function setDescription($description)
    {
        $this->requestBody['description'] = $description;
        return $this;
    }

    public function setCommon()
    {
        $this->requestBody['via_sms'] = false;
        $this->requestBody['via_email'] = true;
        return $this;
    }

    public function buildEmail()
    {
        $partnerUser = $this->getLeadPartnerUser();
        $partner = $this->getLeadPartner();
        $companyName = $partnerUser->company_name ?? $partner->company_name;
        $this->requestBody['email'] = [
            'partner_email' => $partnerUser->email,
            'partner_name' => $companyName,
            'email_subject' => "Please Sign Attached Documents from {$companyName}",
            'email_blurb' => "{$companyName} sent you a document to review and sign. \n\nKind Regards, \n\n{$companyName}"
        ];
        return $this;
    }

    public function buildApplicantEmail()
    {
        $partnerAccountAndPeople = $this->getPartnerAccountAndPeople();
        $partner = $partnerAccountAndPeople['partner'];
        $partnerUser = $partnerAccountAndPeople['partnerUser'];
        $companyName = $partnerUser->company_name ?? $partner->company_name;
        $this->requestBody['email'] = [
            'partner_email' => $partnerUser->email,
            'partner_name' => $companyName,
            'email_subject' => "Please Sign Attached Documents from {$companyName}",
            'email_blurb' => "{$companyName} sent you a document to review and sign. \n\nKind Regards, \n\n{$companyName}"
        ];
        return $this;
    }

    public function buildMeta($ownerIds = [], $nonApplicantRefs = [])
    {
        if (empty($ownerIds)) {
            $owners = $this->getLeadOwners();
            $ownerIds = [];
            foreach ($owners as $owner) {
                $ownerIds[] = $owner->owner_id;
            }
        }

        $this->requestBody['meta_data'] = [
            'lead_ref' => $this->leadRef,
            'owner_ids' => json_encode($ownerIds),
            'non_applicant_refs' => json_encode($nonApplicantRefs),
        ];
        return $this;
    }

    public function buildApplicantMeta($applicantRef)
    {
        $this->requestBody['meta_data'] = [
            'applicant_ref' => $applicantRef,
        ];
        return $this;
    }

    public function buildBusiness()
    {
        if (!empty($this->requestBody['business'])) {
            return $this;
        }
        $partner = $this->getLeadPartner();
        $partnerUser = $this->getLeadPartnerUser();
        $this->requestBody['business'] = [
            'business_name' => $partner->company_name,
            'abn_acn' => $partner->abn,
            'business_address' => "{$partner->address} {$partner->suburb} {$partner->state} {$partner->postcode}",
            'contact_number' => $partnerUser->mobile ?? $partnerUser->phone ?? "",
            'active_lenders' => $this->getActiveLendersString(),
            'broker_name' => $partnerUser->name,
        ];

        return $this;
    }

    public function buildRecipients($filterOwnerIds = [])
    {
        if (!empty($this->requestBody['recipients'])) {
            return $this;
        }
        $leadOwners = $this->getLeadOwners();
        $recipients = [];
        foreach ($leadOwners as $owner) {
            if (!empty($filterOwnerIds) && !in_array($owner->owner_id, $filterOwnerIds)) {
                continue;
            }

            $ref = LendInternalAuth::hashOwnerId($owner->owner_id);
            $recipient = [
                'ref' => $ref,
                'first_name' => $owner->first_name,
                'middle_name' => $owner->middle_name,
                'last_name' => $owner->last_name,
                'email' => $owner->email,
                'mobile' => $owner->mobile ?? $owner->phone,
                'driving_licence' => $owner->driving_licence_num,
                'address' => "",
            ];
            if (!empty($owner->current_address)) {
                $recipient['address'] = "{$owner->current_address->address} {$owner->current_address->suburb} {$owner->current_address->state} {$owner->current_address->postcode}";
            }
            $recipients[] = $recipient;
        }
        $this->requestBody['recipients'] = $recipients;

        return $this;
    }

    public function buildLead()
    {
        if (empty($this->lead)) {
            $this->getLead();
        }

        $leadAddress = $this->getLeadCurrentAddress();
        $this->requestBody['payload'] = array_merge($this->requestBody['payload'] ?? [], [
            "Business_Name" => $this->lead->business_name,
            "Business_Entity_Name" => $this->lead->organisation_name,
            "Business_ABN" => $this->lead->abn,
            "Business_ACN" => $this->lead->acn,
            "Business_Trading_Addr" => $leadAddress,
            "Loan_Amt_Requested" => $this->lead->amount_requested,
            "Monthly_Revenue_Verified" => $this->lead->sales_monthly,
            "Monthly_Revenue_Declared" => $this->lead->client_declared_sales_monthly,
            "Date" => date('d/m/Y'),
        ]);
        return $this;
    }

    public function buildAccount()
    {
        $partnerAccountAndPeople = $this->getPartnerAccountAndPeople();
        $partnerAccount = $partnerAccountAndPeople['partnerAccount'];
        $this->requestBody['payload'] = array_merge($this->requestBody['payload'] ?? [], [
            "Business_Name" => $partnerAccount->partner_account_meta->business_name,
            "Business_Entity_Name" => $partnerAccount->partner_account_meta->organisation_name,
            "Business_ABN" => $partnerAccount->abn,
            "Business_ACN" => $partnerAccount->partner_account_meta->acn,
            "Business_Trading_Addr" => $partnerAccount->current_trading_addresses->full_address,
            "Monthly_Revenue" => $partnerAccount->partner_account_meta->sales_monthly,
            "Date" => date('d/m/Y'),
        ]);
        return $this;
    }

    public function buildRecipientsNew($filterOwnerIds = [])
    {
        if (!empty($this->requestBody['recipients'])) {
            return $this;
        }
        $leadOwners = $this->getLeadOwners();
        $recipients = [];
        foreach ($leadOwners as $owner) {
            if (!empty($filterOwnerIds) && !in_array($owner->owner_id, $filterOwnerIds)) {
                continue;
            }

            $applicantType = 'Applicant';
            if ($owner->is_guarantor && $owner->con_spouse) {
                $applicantType = 'Guarantor & Spouse';
            } elseif ($owner->is_guarantor) {
                $applicantType = 'Guarantor';
            } elseif ($owner->con_spouse && !$owner->point_of_contact) {
                $applicantType = 'Spouse';
            }

            $ref = LendInternalAuth::hashOwnerId($owner->owner_id);

            $recipient = [
                'ref' => $ref,
                'Recipient_Type' => $applicantType,
                'Recipient_First_Name' => $owner->first_name,
                'Recipient_Middle_Name' => $owner->middle_name,
                'Recipient_Last_Name' => $owner->last_name,
                'Recipient_Full_Name' => $owner->full_name,
                'Recipient_Email' => $owner->email,
                'Recipient_Phone' => $owner->mobile ?? $owner->phone,
                'email' => $owner->email,
                'mobile' => $owner->mobile ?? $owner->phone,
                'first_name' => $owner->first_name,
                'middle_name' => $owner->middle_name,
                'last_name' => $owner->last_name,
                'driving_licence' => $owner->driving_licence_num,
                "Recipient_State" => $owner->state,
                "Recipient_DOB" => empty($owner->dob) ? '' : date("d/m/Y", strtotime($owner->dob)),
                'Recipient_Driving_Lic_Num' => $owner->driving_licence_num,
                'Recipient_Driving_Expiry' => empty($owner->driving_licence_expirydate) ? '' : date("d/m/Y", strtotime($owner->driving_licence_expiry)),
                "Recipient_Marital_Status" => $owner->marital_status,
                "Recipient_Insurance_Consent_Yes_No" => "",
                'Recipient_Addr' => "",
                'Recipient_Driving_Lic_Card_Num' => $owner->driving_licence_card_number,
                'Recipient_Driving_Lic_State' => $owner->driving_licence_state,
                'Recipient_Passport_Num' => $owner->passport_number,
                'Recipient_Passport_Expiry' => empty($owner->passport_expiry) ? '' : date("d/m/Y", strtotime($owner->passport_expiry)),
                'Recipient_Passport_Country' => $owner->passport_country,
                'Recipient_Medicare_Num' => $owner->medicare_number,
                'Recipient_Medicare_Ref' => $owner->medicare_ref,
                'Recipient_Medicare_Expiry' => empty($owner->medicare_expiry) ? '' : date("d/m/Y", strtotime($owner->medicare_expiry)),
                'Recipient_Medicare_Card_Colour' => $owner->medicare_card_colour,
            ];
            if (!empty($owner->current_employment)) {
                foreach ($owner->current_employment as $key => $employment) {
                    $employment_duration_years_and_months = TableRegistry::getTableLocator()
                        ->get('LeadEntity')->formatDateDifference($employment->date_from, $employment->date_to);
                    $recipient['Recipient_Current_Employ_Type_' . ($key + 1)] = $employment->employment_type;
                    $recipient['Recipient_Current_Employ_Occupation_' . ($key + 1)] = $employment->previous_occupation;
                    $recipient['Recipient_Current_Employ_Employer_' . ($key + 1)] = $employment->employer;
                    $recipient['Recipient_Current_Employ_Duration_Year_And_Mths_' . ($key + 1)] = $employment_duration_years_and_months;
                    $recipient['Recipient_Current_Employ_Duration_Year_' . ($key + 1)] = explode(" ", $employment_duration_years_and_months)[0];
                    $recipient['Recipient_Current_Employ_Duration_Mths_' . ($key + 1)] = explode(" ", $employment_duration_years_and_months)[2];
                }
            }
            if (!empty($owner->current_address)) {
                $recipient['Recipient_Addr'] = $owner->current_address->full_address;
                $recipient['address'] =  $recipient['Recipient_Addr'];
            }
            $recipients[] = $recipient;
        }
        $this->requestBody['recipients'] = $recipients;

        return $this;
    }

    public function buildApplicantRecipientsNew($applicantIds = [])
    {
        if (!empty($this->requestBody['recipients'])) {
            return $this;
        }
        $applicants = TableRegistry::getTableLocator()
            ->get('PartnerAccountPeopleEntity')
            ->find()
            ->where(['id IN' => $applicantIds])
            ->toArray();
        $recipients = [];
        foreach ($applicants as $applicant) {
            if (!empty($applicantIds) && !in_array($applicant->id, $applicantIds)) {
                continue;
            }

            $ref = LendInternalAuth::hashPartnerAccountPeopleId($applicant->id);
            $recipient = [
                'ref' => $ref,
                'Recipient_Type' => 'Applicant',
                'Recipient_First_Name' => $applicant->first_name,
                'Recipient_Middle_Name' => $applicant->middle_name,
                'Recipient_Last_Name' => $applicant->last_name,
                'Recipient_Full_Name' => $applicant->full_name,
                'Recipient_Email' => $applicant->email,
                'Recipient_Phone' => $applicant->mobile ?? $applicant->phone,
                'email' => $applicant->email,
                'mobile' => $applicant->mobile ?? $applicant->phone,
                'first_name' => $applicant->first_name,
                'middle_name' => $applicant->middle_name,
                'last_name' => $applicant->last_name,
                'driving_licence' => $applicant->driving_licence_num,
                "Recipient_State" => $applicant->state,
                "Recipient_DOB" => date("d/m/Y", strtotime($applicant->dob)),
                'Recipient_Driving_Lic_Num' => $applicant->driving_licence_num,
                'Recipient_Driving_Expiry' => date("d/m/Y", strtotime($applicant->driving_licence_expiry)),
                "Recipient_Marital_Status" => $applicant->marital_status,
                "Recipient_Insurance_Consent_Yes_No" => "",
                'Recipient_Addr' => "",
            ];
            if (!empty($applicant->current_address)) {
                $recipient['Recipient_Addr'] = "{$applicant->current_address->address} {$applicant->current_address->suburb} {$applicant->current_address->state} {$applicant->current_address->postcode}";
                $recipient['address'] =  $recipient['Recipient_Addr'];
            }
            $recipients[] = $recipient;
        }
        $this->requestBody['recipients'] = $recipients;

        return $this;
    }

    public function buildPartnerAndUser()
    {
        $partner = $this->getLeadPartner();
        $partnerUser = $this->getLeadPartnerUser();
        $this->requestBody['payload'] = array_merge($this->requestBody['payload'] ?? [], [
            'Partner_Company_Name' => $partner->company_name,
            'Partner_ABN' => $partner->abn,
            'Partner_Entity_Name' => $partner->organisation_name,
            'Partner_Entity_Name_&_ABN' => "{$partner->organisation_name} (ABN {$partner->abn})",
            'Partner_Addr' => "{$partner->address} {$partner->suburb} {$partner->state} {$partner->postcode}",
            'contact_number' => $partnerUser->mobile ?? $partnerUser->phone ?? "",
            'Partner_User_Name' => $partnerUser->name,
            'Partner_User_Phone' => $partnerUser->mobile ?? $partnerUser->phone ?? "",
            'Partner_User_Email' => $partnerUser->email,
        ]);

        return $this;
    }

    public function buildApplicantPartnerAndUser()
    {
        $partnerAccountAndPeople = $this->getPartnerAccountAndPeople();
        $partner = $partnerAccountAndPeople['partner'];
        $partnerUser = $partnerAccountAndPeople['partnerUser'];
        $this->requestBody['payload'] = array_merge($this->requestBody['payload'] ?? [], [
            'Partner_Company_Name' => $partner->company_name,
            'Partner_ABN' => $partner->abn,
            'Partner_Entity_Name' => $partner->organisation_name,
            'Partner_Entity_Name_&_ABN' => "{$partner->organisation_name} (ABN {$partner->abn})",
            'Partner_Addr' => "{$partner->address} {$partner->suburb} {$partner->state} {$partner->postcode}",
            'contact_number' => $partnerUser->mobile ?? $partnerUser->phone ?? "",
            'Partner_User_Name' => $partnerUser->name,
            'Partner_User_Phone' => $partnerUser->mobile ?? $partnerUser->phone ?? "",
            'Partner_User_Email' => $partnerUser->email,
        ]);
        return $this;
    }

    public function buildTemplates()
    {
        $templates = TableRegistry::getTableLocator()
            ->get('PartnerCustomPrivacyFormsEntity')
            ->find()
            ->where(['document_title IN' => $this->templates])
            ->toArray();

        $this->requestBody['template_ids'] = [];
        if (!empty($templates)) {
            foreach ($templates as $template) {
                $this->requestBody['template_ids'][] = ['template_id' => $template['service_template_id']];
            }
        }
        return $this;
    }

    public function buildConPartnerUserSettings($conPartnerUserSettings = null)
    {
        if (empty($conPartnerUserSettings)) {
            $conPartnerUserSettings = $this->getPartnerUserSettings();
        }

        if (!empty($conPartnerUserSettings)) {
            $this->requestBody['con_partner_user_settings'] = $conPartnerUserSettings;

            if (!empty($conPartnerUserSettings['cg_top_lenders'])) {
                $this->requestBody['con_partner_user_settings']['cg_top_lenders_string']
                    = implode(', ', array_column($conPartnerUserSettings['cg_top_lenders'], 'name'));
            }
        }
        return $this;
    }

    public function buildFeesTables()
    {
        $conPartnerUserFeeDefaults = TableRegistry::getTableLocator()
            ->get('ConPartnerUserFeeDefaultEntity')
            ->find()
            ->where(['partner_user_id' => $this->user['partner_user_id'], 'active' => 1])
            ->contain(['ConfigConFeeEntity'])
            ->all();

        $brokerFeesCount = 1;
        $thirdPartyFeesCount = 1;
        if (empty($this->requestBody['extra_fields'])) {
            $this->requestBody['extra_fields'] = [];
        }
        foreach ($conPartnerUserFeeDefaults as $feeDefault) {
            if ($feeDefault['config_con_fee']['group'] === 'broker' && $brokerFeesCount < 4) {
                $this->requestBody['extra_fields']['Broker Fee Name ' . $brokerFeesCount]
                    = $feeDefault['display_fee_name'];
                $this->requestBody['extra_fields']['Broker Fee Explanation ' . $brokerFeesCount]
                    = $feeDefault['explanation'];
                $this->requestBody['extra_fields']['Broker Fee Amount ' . $brokerFeesCount]
                    = $feeDefault['display_amount_string'];
                $brokerFeesCount++;
            }
            if ($feeDefault['config_con_fee']['group'] === 'third party' && $thirdPartyFeesCount < 4) {
                $this->requestBody['extra_fields']['Third Party Fee Name ' . $thirdPartyFeesCount]
                    = $feeDefault['display_fee_name'];
                $this->requestBody['extra_fields']['Third Party Fee Explanation ' . $thirdPartyFeesCount]
                    = $feeDefault['explanation'];
                $this->requestBody['extra_fields']['Third Party Fee Amount ' . $thirdPartyFeesCount]
                    = $feeDefault['display_amount_string'];
                $thirdPartyFeesCount++;
            }
        }
        return $this;
    }

    public function buildFees()
    {
        $conPartnerUserFeeDefaults = TableRegistry::getTableLocator()
            ->get('ConPartnerUserFeeDefaultEntity')
            ->find()
            ->where(['partner_user_id' => $this->user['partner_user_id'], 'active' => 1])
            ->contain(['ConfigConFeeEntity'])
            ->all();

        if (empty($this->requestBody['payload'])) {
            $this->requestBody['payload'] = [];
        }
        $this->requestBody['payload']['Broker_Fee'] = [];
        foreach ($conPartnerUserFeeDefaults as $feeDefault) {
            if ($feeDefault['config_con_fee']['group'] === 'broker') {
                $brokerFee = [
                    "Name" => $feeDefault['display_fee_name'],
                    "Explanation" => $feeDefault['explanation'],
                    "Amount" => $feeDefault['display_amount_string'],
                ];
                $this->requestBody['payload']['Broker_Fee'][] = $brokerFee;
            }
            if ($feeDefault['config_con_fee']['group'] === 'third party') {
                $thirdPartyFee = [
                    "Name" => $feeDefault['display_fee_name'],
                    "Explanation" => $feeDefault['explanation'],
                    "Amount" => $feeDefault['display_amount_string'],
                ];
                $this->requestBody['payload']['Third_Party_Fee'][] = $thirdPartyFee;
            }
        }
        $this->requestBody['payload']['CS_Broker_Fee'] = $this->requestBody['payload']['Broker_Fee'];
        $this->requestBody['payload']['CS_Third_Party_Fee'] = $this->requestBody['payload']['Third_Party_Fee'];
        return $this;
    }

    public function buildCreditProposalFees()
    {
        $conCreditProposalFees = TableRegistry::getTableLocator()
            ->get('ConCreditPropFeeEntity')
            ->find()
            ->where(['lead_id' => $this->lead->lead_id])
            ->contain(['ConfigConFeeEntity'])
            ->all();

        if (empty($this->requestBody['payload'])) {
            $this->requestBody['payload'] = [];
        }
        //array fees
        $this->requestBody['payload']['CP_Broker_Fees'] = [];
        $this->requestBody['payload']['CP_Third_Party_Fees'] = [];
        $this->requestBody['payload']['CP_Lender_Fees'] = [];
        $this->requestBody['payload']['CP_Commissions_Fees'] = [];
        $this->requestBody['payload']['CP_Referral_Fees'] = [];
        //single fees
        $this->requestBody['payload']['CP_Orig_Fee'] = '';
        $this->requestBody['payload']['CP_Lender_Estab_Fee'] = '';
        $this->requestBody['payload']['CP_Lender_Val_Fee'] = '';
        $this->requestBody['payload']['CP_Lender_Sett_Fee'] = '';
        $this->requestBody['payload']['CP_Lender_Risk_Fee'] = '';
        $this->requestBody['payload']['CP_Lender_Other_Fee'] = '';
        $this->requestBody['payload']['CP_Lender_Other_Fee_Explanation'] = '';
        $this->requestBody['payload']['CP_Total_Lender_Fees'] = '';
        $this->requestBody['payload']['CP_Commission_Fee'] = '';
        $this->requestBody['payload']['CP_Third_Party_Fee'] = '';
        $this->requestBody['payload']['CP_Third_Party_Fee_Explanation'] = '';
        $this->requestBody['payload']['CP_Referral_Fee'] = '';
        foreach ($conCreditProposalFees as $fee) {
            //array fees
            if ($fee['config_con_fee']['group'] === 'broker') {
                $brokerFee = [
                    "Name" => $fee['display_fee_name'],
                    "Explanation" => $fee['explanation'],
                    "Amount" => $fee['display_amount_string'],
                ];
                $this->requestBody['payload']['CP_Broker_Fees'][] = $brokerFee;
            }
            if ($fee['config_con_fee']['group'] === 'third party') {
                $thirdPartyFee = [
                    "Name" => $fee['display_fee_name'],
                    "Explanation" => $fee['explanation'],
                    "Amount" => $fee['display_amount_string'],
                ];
                $this->requestBody['payload']['CP_Third_Party_Fees'][] = $thirdPartyFee;
            }
            if ($fee['config_con_fee']['group'] === 'lender') {
                $lenderFee = [
                    "Name" => $fee['display_fee_name'],
                    "Explanation" => $fee['explanation'],
                    "Amount" => $fee['display_amount_string'],
                ];
                $this->requestBody['payload']['CP_Lender_Fees'][] = $lenderFee;
            }
            if ($fee['config_con_fee']['group'] === 'commissions') {
                $commissionsFee = [
                    "Name" => $fee['display_fee_name'],
                    "Paid_By" => $fee['paid_by'],
                    "Paid_To" => $fee['paid_to'],
                    "Explanation" => $fee['explanation'],
                    "Amount" => $fee['display_amount_string'],
                ];
                $this->requestBody['payload']['CP_Commissions_Fees'][] = $commissionsFee;
            }
            if ($fee['config_con_fee']['group'] === 'referral') {
                $referralFee = [
                    "Name" => $fee['display_fee_name'],
                    "Paid_By" => $fee['paid_by'],
                    "Paid_To" => $fee['paid_to'],
                    "Explanation" => $fee['explanation'],
                    "Amount" => $fee['display_amount_string'],
                ];
                $this->requestBody['payload']['CP_Referral_Fees'][] = $referralFee;
            }
            //single fees
            if ($fee['config_con_fee']['type'] === 'Origination Fee' && empty($this->requestBody['payload']['CP_Orig_Fee'])) {
                $this->requestBody['payload']['CP_Orig_Fee'] = $fee['display_amount_string'];
            }
            if ($fee['config_con_fee']['type'] === 'Establishment Fee' && empty($this->requestBody['payload']['CP_Lender_Estab_Fee'])) {
                $this->requestBody['payload']['CP_Lender_Estab_Fee'] = $fee['display_amount_string'];
            }
            if ($fee['config_con_fee']['type'] === 'Valuation Fee' && empty($this->requestBody['payload']['CP_Lender_Val_Fee'])) {
                $this->requestBody['payload']['CP_Lender_Val_Fee'] = $fee['display_amount_string'];
            }
            if ($fee['config_con_fee']['type'] === 'Settlement Fee' && empty($this->requestBody['payload']['CP_Lender_Sett_Fee'])) {
                $this->requestBody['payload']['CP_Lender_Sett_Fee'] = $fee['display_amount_string'];
            }
            if ($fee['config_con_fee']['type'] === 'Risk Fee' && empty($this->requestBody['payload']['CP_Lender_Risk_Fee'])) {
                $this->requestBody['payload']['CP_Lender_Risk_Fee'] = $fee['display_amount_string'];
            }
            if ($fee['config_con_fee']['type'] === 'Other Fee to Lender' && empty($this->requestBody['payload']['CP_Lender_Other_Fee'])) {
                $this->requestBody['payload']['CP_Lender_Other_Fee'] = $fee['display_amount_string'];
                $this->requestBody['payload']['CP_Lender_Other_Fee_Explanation'] = $fee['explanation'];
            }
            if ($fee['config_con_fee']['type'] === 'Third Party Fee' && empty($this->requestBody['payload']['CP_Third_Party_Fee'])) {
                $this->requestBody['payload']['CP_Third_Party_Fee'] = $fee['display_amount_string'];
                $this->requestBody['payload']['CP_Third_Party_Fee_Explanation'] = $fee['explanation'];
            }
            if ($fee['config_con_fee']['type'] === 'Referral Fee' && empty($this->requestBody['payload']['CP_Referral_Fee'])) {
                $this->requestBody['payload']['CP_Referral_Fee'] = $fee['display_amount_string'];
            }
        }
        return $this;
    }

    public function addExternalEntityFields()
    {
        $c = $this->getPartnerUserSettings();
        if (!empty($c) && !empty($c['external_acl'])) {
            $this->requestBody['extra_fields']['External ACL Name'] = $c['external_acl_name'];
            $this->requestBody['extra_fields']['External ACL'] = $c['external_acl'];
            $this->requestBody['extra_fields']['External ABN'] = $c['external_abn'];
            $this->requestBody['extra_fields']['External ACL Name & ABN']
                = "{$c['external_acl_name']} (ABN {$c['external_abn']})";
            $this->requestBody['extra_fields']['External ACL Address'] = $c['external_acl_address'];
            $this->requestBody['extra_fields']['External ACL Contact Number'] = $c['external_acl_contact_num'];
            $this->requestBody['extra_fields']['External ACL Email'] = $c['external_acl_email'];
            $this->requestBody['extra_fields']['Consumer Description']
                = "{$c['entity_name']} (ABN {$c['abn']}), as an authorised credit representative #{$c['acr']} of"
                . "{$c['external_acl_name']} (ABN {$c['external_abn']}), ACL #{$c['external_acl']} will collect, store, disclose and use personal "
                . "information and credit information that you provide to us in accordance with the terms of this Privacy "
                . "Consent and our Privacy Policy. You may obtain a copy of our Privacy Policy by contacting us on "
                . "{$c['external_acl_contact_num']} and email {$c['external_acl_email']}. \n\nBy signing this document, "
                . "you agree we can collect, hold, use and exchange personal and credit information about you for these "
                . "stated purposes. The Privacy Policy also contains information on how you can complain about a breach of "
                . "the privacy laws, how you can access or request to correct your personal and credit information that we "
                . "hold about you and how to have that information amended.";
        }
        return $this;
    }

    public function addPayloadFields()
    {
        if (empty($this->requestBody['payload'])) {
            $this->requestBody['payload'] = [];
        }
        $c = $this->getPartnerUserSettings();
        if (!empty($c) && !empty($c['external_acl'])) {
            $this->requestBody['payload']['External_ACL_Name'] = $c['external_acl_name'];
            $this->requestBody['payload']['External_ACL'] = $c['external_acl'];
            $this->requestBody['payload']['External_ABN'] = $c['external_abn'];
            $this->requestBody['payload']['External_ACL_Name_&_ABN']
                = "{$c['external_acl_name']} (ABN {$c['external_abn']})";
            $this->requestBody['payload']['External_ACL_Address'] = $c['external_acl_address'];
            $this->requestBody['payload']['External_ACL_Contact Number'] = $c['external_acl_contact_num'];
            $this->requestBody['payload']['External_ACL_Email'] = $c['external_acl_email'];
            $this->requestBody['payload']['Consumer_Description']
                = "{$c['entity_name']} (ABN {$c['abn']}), as an authorised credit representative #{$c['acr']} of"
                . "{$c['external_acl_name']} (ABN {$c['external_abn']}), ACL #{$c['external_acl']} will collect, store, disclose and use personal "
                . "information and credit information that you provide to us in accordance with the terms of this Privacy "
                . "Consent and our Privacy Policy. You may obtain a copy of our Privacy Policy by contacting us on "
                . "{$c['external_acl_contact_num']} and email {$c['external_acl_email']}. \n\nBy signing this document, "
                . "you agree we can collect, hold, use and exchange personal and credit information about you for these "
                . "stated purposes. The Privacy Policy also contains information on how you can complain about a breach of "
                . "the privacy laws, how you can access or request to correct your personal and credit information that we "
                . "hold about you and how to have that information amended.";
        }
        return $this;
    }
    /*
  "Referrer_Company_Name": "JKL Pty Ltd",
  "Referrer_Nickname": "JKL",
  "Referrer_Person_Name": "John Black"
  */
    public function buildReferrer()
    {
        if ($this->lead->referrer_person_id) {
            $referrerPeopleTable = TableRegistry::getTableLocator()->get('ReferrerPeople');
            $referrerPerson = $referrerPeopleTable->find("all", [
                "conditions" => ["ReferrerPeople.id" => $this->lead->referrer_person_id],
                "contain" => ["Referrers", "Referrers.LeadAbnLookupEntity"]
            ])->first();
            $this->requestBody['payload']['Referrer_Company_Name'] = $referrerPerson->referrer->abn_lookup->organisation_name;
            $this->requestBody['payload']['Referrer_Nickname'] = $referrerPerson->referrer->nickname;
            $this->requestBody['payload']['Referrer_Person_Name'] = $referrerPerson->full_name;
        } else {
            $this->requestBody['payload']['Referrer_Company_Name'] = '';
            $this->requestBody['payload']['Referrer_Nickname'] = '';
            $this->requestBody['payload']['Referrer_Person_Name'] = '';
        }
        return $this;
    }

    public function buildCreditProposal() {
        if($this->lead->lead_id){
            $conPrelim = TableRegistry::getTableLocator()
            ->get('ConPrelimEntity')
            ->find()
            ->where(['lead_id' => $this->lead->lead_id])
            ->order(['created' => 'DESC'])
            ->first();
            if($conPrelim){
                $this->requestBody['payload']['CP_Credit_Provider'] = $this->requestBody['payload']['Selected_Lender'];
                $this->requestBody['payload']['CP_Finance_Amt'] = "$".($conPrelim->finance_amount_manual ?? $this->requestBody['payload']['Selected_Finance_Amt'] ?? 0);
                $this->requestBody['payload']['CP_Term'] = $conPrelim->loan_term_manual ?? $this->requestBody['payload']['Selected_Term_Months'] ?? 0;
                if ($conPrelim->monthly_repayments_manual > 0) {
                    $this->requestBody['payload']['CP_Repay_Amt'] = "$".$conPrelim->monthly_repayments_manual;
                    $this->requestBody['payload']['CP_Repay_Frequency'] = "Monthly";
                } else {
                    $this->requestBody['payload']['CP_Repay_Amt'] = "$".($this->requestBody['payload']['Selected_Repayment_Amt'] ?? 0);
                    $this->requestBody['payload']['CP_Repay_Frequency'] = $this->requestBody['payload']['Selected_Repayment_Frequency'];
                }
                $this->requestBody['payload']['CP_Interest_Rate'] = $conPrelim->interest_rate_manual ?? $this->requestBody['payload']['Selected_Rate_After_Discount'] ?? 0;
            }
        }
        return $this;
    }

    public function addConsumerLendersTableData()
    {
        $lenderListEntities = TableRegistry::getTableLocator()
            ->get('ConPrivacyLenderListEntity')
            ->find('all')
            ->order(['credit_provider' => 'ASC'])
            ->toArray();

        $lenderNames = '';
        $lenderAbnAcn = '';
        $lenderWebsite = '';

        foreach ($lenderListEntities as $entity) {
            $lenderNames .= $entity['credit_provider'] . "\n";
            $lenderAbnAcn .= $entity['abn_or_acn'] . "\n";
            $lenderWebsite .= $entity['website_address'] . "\n";
        }

        $this->requestBody['extra_fields']['Lender Names'] = $lenderNames;
        $this->requestBody['extra_fields']['Lender ABN ACN'] = $lenderAbnAcn;
        $this->requestBody['extra_fields']['Lender Website'] = $lenderWebsite;

        return $this;
    }

    public function addLendersTableData()
    {
        $lenderListEntities = TableRegistry::getTableLocator()
            ->get('ConPrivacyLenderListEntity')
            ->find('all')
            ->toArray();

        foreach ($lenderListEntities as $entity) {
            $lenderRecord = [
                'Name' => $entity['credit_provider'],
                'ABN_ACN' => $entity['abn_or_acn'],
                'Website' => $entity['website_address'],
            ];
            $this->requestBody['payload']['Lenders'][] = $lenderRecord;
        }

        return $this;
    }

    public function addCommercialLendersData()
    {
        $lenderData = TableRegistry::getTableLocator()
            ->get('CommPrivacyLenderListEntity')
            ->getCustomBusinessListFieldForDocusignFormatted();

        $this->requestBody['extra_fields'] = !empty($this->requestBody['extra_fields']) ? array_merge($this->requestBody['extra_fields'], $lenderData) : $lenderData;

        return $this;
    }

    public function addTopLenders()
    {
        $conPartnerUserSettings = $this->getPartnerUserSettings();
        if (!empty($conPartnerUserSettings) && !empty($conPartnerUserSettings['cg_top_lenders'])) {
            $topLenderNames = array_column($conPartnerUserSettings['cg_top_lenders'], 'name');
            $topLenderNames = array_filter($topLenderNames, function ($name) {
                return !empty($name);
            });
            $this->requestBody['extra_fields']['Top Lenders'] = implode(', ', $topLenderNames);
        }
        return $this;
    }

    public function addTemplate($documentTitle)
    {
        $documentTemplates = TableRegistry::getTableLocator()
            ->get('PartnerCustomPrivacyFormsEntity')
            ->find()
            ->where(['document_title' => $documentTitle])
            ->toArray();

        if (empty($this->requestBody['template_ids'])) {
            $this->requestBody['template_ids'] = [];
        }

        if (!empty($documentTemplates)) {
            $this->requestBody['template_ids'][] = ['template_id' => $documentTemplates[0]['service_template_id']];
        }

        return $this;
    }

    public function addFormIdsToTemplates()
    {
        foreach ($this->requestBody['template_ids'] as $key => $template) {
            $documentTemplate = TableRegistry::getTableLocator()
                ->get('PartnerCustomPrivacyFormsEntity')
                ->find()
                ->where([
                    'service_template_id' => $template['template_id'],
                    'status' => 'Ready to Use',
                ])
                ->first();
            if (!empty($documentTemplate)) {
                $this->requestBody['template_ids'][$key]['form_id'] = $documentTemplate->partner_custom_privacy_form_id;
            }
        }
        return $this;
    }

    protected function addPrivacyFormTemplate($customPrivacyFormTitle, $lendPrivacyFormTitle, $miniPrivacyFormTitle)
    {
        $leadPartnerId = $this->getLeadPartnerId();
        $partner = $this->getLeadPartner();
        $customTemplates = TableRegistry::getTableLocator()
            ->get('PartnerCustomPrivacyFormsEntity')
            ->find()
            ->where([
                'partner_id' => $leadPartnerId,
                'document_title' => $customPrivacyFormTitle,
                'status' => 'Ready to Use'
            ])
            ->toArray();

        if (empty($this->requestBody['template_ids'])) {
            $this->requestBody['template_ids'] = [];
        }
        if (!empty($customTemplates) && !$partner['use_lend_privacy_form']) {
            $this->requestBody['template_ids'][] = ['template_id' => $customTemplates[0]['service_template_id']];
            if ($customTemplates[0]['lend_addendum'] == true) {
                $this->addTemplate($miniPrivacyFormTitle);
            }
        } else {
            $this->addTemplate($lendPrivacyFormTitle);
        }

        return $this;
    }

    public function addConsumerPrivacyFormTemplate()
    {
        $conPartnerUserSettings = TableRegistry::getTableLocator()
            ->get('ConPartnerUserSettingsEntity')
            ->find()
            ->where(['partner_user_id' => $this->user['partner_user_id']])
            ->first();
        if (!empty($conPartnerUserSettings) && !empty($conPartnerUserSettings['acr'])) {
            $this->addPrivacyFormTemplate(
                'Consumer Privacy Form',
                'Consumer Full Privacy Form with ACR',
                'Consumer Short Privacy Form'
            );
        } else {
            $this->addPrivacyFormTemplate(
                'Consumer Privacy Form',
                'Consumer Full Privacy Form',
                'Consumer Short Privacy Form'
            );
        }
        return $this;
    }

    public function addCreditQuoteTemplate()
    {
        $conPartnerUserSettings = $this->getPartnerUserSettings();
        $templateName = 'CreditQuote';
        if (!empty($conPartnerUserSettings) && !$conPartnerUserSettings['is_bid']) {
            if (empty($conPartnerUserSettings['acr'])) {
                $templateName = 'CreditQuoteNoRepresentative';
            }
        }
        if (!empty($conPartnerUserSettings) && $conPartnerUserSettings['is_bid']) {
            $templateName = 'CreditQuoteBid';
            if (empty($conPartnerUserSettings['acr'])) {
                $templateName = 'CreditQuoteNoRepresentativeBid';
            }
        }
        $this->addTemplate($templateName);
        return $this;
    }

    public function addCommercialPrivacyFormTemplate()
    {
        $this->addPrivacyFormTemplate(
            'Privacy Form',
            'Full Privacy Form',
            'Short Privacy Form'
        );
        return $this;
    }

    public function getRequestBody()
    {
        return $this->requestBody;
    }

    public function getLeadId()
    {
        $lead = $this->getLead();
        return $lead->lead_id;
    }

    public function getLeadPartnerId()
    {
        $leadPartner = $this->getLeadPartner();
        return $leadPartner->partner_id;
    }

    public function getLeadPartnerUserId()
    {
        $leadPartnerUser = $this->getLeadPartnerUser();
        return $leadPartnerUser->partner_user_id;
    }

    public function getPartnerUserSettings()
    {
        if (!empty($this->conPartnerUserSettings)) {
            return $this->conPartnerUserSettings;
        }
        if ($this->leadRef) {
            $lead = $this->getLead();
        }
        $conPartnerUserSettings = TableRegistry::getTableLocator()
            ->get('ConPartnerUserSettingsEntity')
            ->find()
            ->where(['partner_user_id' => $this->user['partner_user_id']])
            ->first();
        $this->conPartnerUserSettings = $conPartnerUserSettings;
        return $conPartnerUserSettings;
    }

    protected function getPrimaryBusinessActivity($partnerUser)
    {
        $selectedActivities = explode(",", $partnerUser['business_activities']);

        $businessActivities = TableRegistry::getTableLocator()
            ->get('PartnerProductTypes')
            ->getPartnerProductTypes(array('active' => 1), false);

        $result = [];

        foreach ($businessActivities as $activity) {
            if (in_array($activity['product_type_id'], $selectedActivities)) {
                $result[] = $activity['product_type_name'];
            }
        }

        return implode(", ", $result);
    }

    protected function signatureServiceVariableMap($requestOwnerIds = [])
    {
        $con_partner_user_settings = $this->getPartnerUserSettings();
        $user = $this->getLeadPartnerUser();
        $partner = $this->getLeadPartner();
        if (empty($this->requestBody['business'])) {
            $this->buildBusiness();
        }
        $business = $this->requestBody['business'];
        if (empty($this->requestBody['recipients'])) {
            $this->buildRecipients($requestOwnerIds);
        }
        // TODO DocuSign handle envelope per person, so when sent to multiple recipients, person should be different each time.
        $person = $this->requestBody['recipients'][0];
        $variables = [];
        $variables['Consumer_Entity_Name_&_ABN'] = "{$con_partner_user_settings['entity_name']} (ABN {$con_partner_user_settings['abn']})";
        $variables['Consumer_Entity_Name'] = $con_partner_user_settings['entity_name'];
        $variables['Consumer_Address'] = $con_partner_user_settings['address'];
        $variables['Consumer_Email'] = $con_partner_user_settings['email'];
        $variables['Consumer_Phone_Number'] = $con_partner_user_settings['phone'];
        $variables['Consumer_ACL'] = $con_partner_user_settings['acl'];
        $variables['Consumer_ACR'] = $con_partner_user_settings['acr'];
        $variables['Consumer_Upfront'] = $con_partner_user_settings['cg_upfront_comms_range'];
        $variables['Consumer_Trail'] = $con_partner_user_settings['cg_trail_comms_range'];
        $variables['Consumer_Volume'] = $con_partner_user_settings['cg_vb_comms_range'];
        $variables['Complaints_Phone'] = $con_partner_user_settings['cg_complain_phone'];
        $variables['Complaints_Email'] = $con_partner_user_settings['cg_complain_email'];
        $variables['Complaints_Address'] = $con_partner_user_settings['cg_complaint_address'];
        $variables['AFCA_Member_Number'] = $con_partner_user_settings['cd_afca_member_num'];
        $variables['Driving_Licence'] = $person['driving_licence'];
        $variables['Recipient_Address'] = $person['address'];
        $variables['Business_Name'] = $business['business_name'];
        $variables['Consumer_Broker_Name'] = $business['broker_name'];
        $variables['Abn_Acn'] = $business['abn_acn'];
        $variables['Business_Address'] = $business['address'];
        $variables['Business_Contact_Number'] = $business['contact'];
        $variables['Custom_Business_List_old'] = $business['credit_provider_list'];
        $variables['1_Accreditation_Title'] = $user['title'];
        $variables['1_Accreditation_Name'] = $user['name'];
        $variables['1_Accreditation_DOB'] = $user['dob'];
        $variables['1_Accreditation_Role'] = $user['role'];
        $variables['1_Accreditation_BusinessNature'] = $user['business_nature'];
        $variables['1_Accreditation_WebAffiliate'] = ($user['web_affiliate'] === 0) ? 'No' : 'Yes';
        $variables['1_Accreditation_BusinessActivity'] = $this->getPrimaryBusinessActivity($user);
        $variables['1_Accreditation_Email'] = $user['email'];
        $variables['1_Accreditation_Mobile'] = $user['mobile'];
        $variables['1_Accreditation_IAM'] = $user['indus_assoc_member'];
        $variables['1_Accreditation_FBAA'] = (isset(json_decode($user['indus_assoc_number'], true)['FBAA'])) ? json_decode($user['indus_assoc_number'], true)['FBAA'] : '';
        $variables['1_Accreditation_MFAA'] = (isset(json_decode($user['indus_assoc_number'], true)['MFAA'])) ? json_decode($user['indus_assoc_number'], true)['MFAA'] : '';
        $variables['1_Accreditation_CAFBA'] = (isset(json_decode($user['indus_assoc_number'], true)['CAFBA'])) ? json_decode($user['indus_assoc_number'], true)['CAFBA'] : '';
        $variables['1_Accreditation_IAName'] = ($user['indus_assoc_name'] !== '') ? $user['indus_assoc_name'] : '';
        $variables['1_Accreditation_IANumber'] = (isset(json_decode($user['indus_assoc_number'], true)['Other'])) ? json_decode($user['indus_assoc_number'], true)['Other'] : '';
        $variables['1_Accreditation_TradingAddress'] = $partner['trading_address'];
        $variables['1_Accreditation_PostalAddress'] = $partner['postal_address'];

        //new variables
        $variables['CS_Broker_ABN'] = $con_partner_user_settings['abn'];
        $variables['CS_Broker_Business_Name'] = $con_partner_user_settings['bus_name'];
        $variables['CS_Broker_Entity_Name'] = $con_partner_user_settings['entity_name'];
        $variables['CS_Broker_Entity_Name_&ABN'] = "{$con_partner_user_settings['entity_name']} (ABN {$con_partner_user_settings['abn']})";
        $variables['CS_Broker_Email'] = $con_partner_user_settings['email'];
        $variables['CS_Broker_Phone'] = $con_partner_user_settings['phone'];
        $variables['CS_Broker_Addr'] = $con_partner_user_settings['address'];
        $variables['CS_Broker_ACR'] = $con_partner_user_settings['acr'];
        $variables['CS_Broker_ACL'] = $con_partner_user_settings['acl'];
        $variables['CS_ACRLicensee_ACL'] = $con_partner_user_settings['external_acl'];
        $variables['CS_ACRLicensee_Name'] = $con_partner_user_settings['external_acl_name'];
        $variables['CS_ACRLicensee_ABN'] = $con_partner_user_settings['external_abn'];
        $variables['CS_ACRLicensee_Name&ABN'] = "{$con_partner_user_settings['external_acl_name']} (ABN {$con_partner_user_settings['external_abn']})";
        $variables['CS_ACRLicensee_Addr'] = $con_partner_user_settings['external_acl_address'];
        $variables['CS_ACRLicensee_Email'] = $con_partner_user_settings['external_acl_email'];
        $variables['CS_ACRLicensee_Phone'] = $con_partner_user_settings['external_acl_contact_num'];
        $variables['CS_Broker_AFCA_Member_Num'] = $con_partner_user_settings['cd_afca_member_num'];
        $variables['CS_Broker_AFCA_Complaints_Phone'] = $con_partner_user_settings['cg_complain_phone'];
        $variables['CS_Broker_AFCA_Complaints_Email'] = $con_partner_user_settings['cg_complain_email'];
        $variables['CS_Broker_AFCA_Complaints_Addr'] = $con_partner_user_settings['cg_complaint_address'];
        $variables['CS_Broker_Fees_Upfront'] = $con_partner_user_settings['cg_upfront_comms_range'];
        $variables['CS_Broker_Fees_Trail'] = $con_partner_user_settings['cg_trail_comms_range'];
        $variables['CS_Broker_Fees_VBI'] = $con_partner_user_settings['cg_vb_comms_range'];
        $variables['CS_Broker_6_Lenders'] = $this->requestBody['extra_fields']['Top Lenders'];
        $variables['CS_Broker_Is_Bid'] = $con_partner_user_settings['is_bid'] ? "Yes" : "No";
        $variables['CS_Broker_ACR_Or_ACL'] = $con_partner_user_settings['acr'] ? 'ACR' : 'ACL';

        return $variables;
    }

    public function formatForLendSignatureService($requestOwnerIds = [])
    {
        if (empty($this->requestBody['payload'])) {
            $this->requestBody['payload'] = [];
        }
        if (isset($this->requestBody['extra_fields'])) {
            $this->requestBody['payload'] = array_merge($this->requestBody['payload'], $this->requestBody['extra_fields']);
        }
        $this->requestBody['payload'] = array_merge($this->requestBody['payload'], $this->signatureServiceVariableMap($requestOwnerIds));
        return $this;
    }

    public function addVia($params)
    {
        $formattedVia = [];
        foreach ($params['via'] as $via) {
            $formattedVia[$via['ref']] = $via;
        }
        $this->requestBody['via'] = $formattedVia;
        return $this;
    }

    public function setFlowVariationOf($baseFlowName)
    {
        $flowName = $baseFlowName;
        if ($baseFlowName == LendSignTemplateUse::CreditGuideAndQuote) {
            $conPartnerUserSettings = $this->getPartnerUserSettings();
            if (!empty($conPartnerUserSettings) && !$conPartnerUserSettings['is_bid']) {
                if (empty($conPartnerUserSettings['acr'])) {
                    $flowName = $baseFlowName . 'NoRepresentative';
                }
            }
            if (!empty($conPartnerUserSettings) && $conPartnerUserSettings['is_bid']) {
                $flowName = $baseFlowName . 'Bid';
                if (empty($conPartnerUserSettings['acr'])) {
                    $flowName = $baseFlowName . 'NoRepresentativeBid';
                }
            }
        }
        $this->requestBody['flow'] = $flowName;
        return $this;
    }

    public function buildOrgLevelLead()
    {
        $lend_internal_auth = new LendInternalAuth;
        $lead_id = $lend_internal_auth->unhashLeadId($this->leadRef);
        TableRegistry::getTableLocator()->remove('LeadEntity');
        $lead_table = TableRegistry::getTableLocator()->get('LeadEntity', [
            'connection' => ConnectionManager::get('reader_db')
        ]);
        $associated = [
            'Owners' => [
                'CurrentEmployment',
                'CurrentAddress',
            ],
            'PocOwner',
            'LeadAssetFinanceEntity',
            'LeadAbnLookupEntity',
            'LeadPricingEntity',
            'NzbnLookupEntity',
            'CurrentLenderStatus',
            'PartnerCommissionEntity' => [
                'conditions' => ['PartnerCommissionEntity.is_active' => 1],
            ],
            'LenderMatchRequestEntity',
            'SettlementReviewsEntity' => [
                'PartnerCommissionEntity' => [
                    'conditions' => ['PartnerCommissionEntity.is_active' => 1],
                ],
                'conditions' => ['SettlementReviewsEntity.is_active' => 1],
                'sort' => ['SettlementReviewsEntity.settlement_review_id' => 'DESC'],
            ],
            'LeadAssociatedDataEntity' => [
                'LeadNotesEntity',
            ],
            'PartnerUserLeadsEntity' => [
                'PartnerUserEntity',
            ],
            'ManStatusEntity' => [
                'ManStatusGroupEntity',
            ],
            'ReferrerPeople',
            'LendScoreEntity',
            'LeadOwnerIncomeEntity' => [
                'LeadOwnersEntity',
                'ConfigIncomeEntity',
                'ConIncomeShareEntity',
            ],
            'LeadOwnerExpenseEntity' => [
                'ConfigExpenseEntity',
                'LeadOwnerExpensesBreakdownEntity',
            ],
            'LeadAssetsEntity' => [
                'ConAssetShareEntity',
                'ConfigAssetTypeEntity',
            ],
            'LeadLiabilitiesEntity' => [
                'LeadAssetsEntity',
                'ConLiabilityShareEntity',
                'ConfigLiabilityEntity',
            ],
        ];
        $lead = $lead_table->get($lead_id, [
            'contain' => $associated
        ]);

        $formattedLead = [];
        //lead details
        $formattedLead['Lead_Ref'] = $lead->lead_ref;
        $formattedLead['Lead_Type'] = $lead->lead_type;
        $formattedLead['Product_Type'] = $lead->product_type;
        $formattedLead['Created_Date'] = $lead->created->format('d/m/Y');
        $formattedLead['Last_Updated_Date'] = $lead->last_changed_date ? $lead->last_changed_date->format('d/m/Y') : '';
        $formattedLead['Last_Note_Date'] = $lead->lead_associated_data->note->created ? $lead->lead_associated_data->note->created->format('d/m/Y') : '';
        $formattedLead['Last_Note'] = $lead->lead_associated_data->note->notes;
        $formattedLead['Main_Applicant_Full_Name'] = $lead->owner_poc->full_name;
        $formattedLead['Main_Applicant_Bank_Acc_Name'] = $lead->owner_poc->direct_debit_acc_holder_name;
        $formattedLead['Main_Applicant_Bank_BSB'] = $lead->owner_poc->direct_debit_bsb;
        $formattedLead['Main_Applicant_Bank_Number'] = $lead->owner_poc->direct_debit_acc_number;
        $formattedLead['Campaign'] = $lead->campaign;
        $formattedLead['Current_Assignee'] = $lead->partner_user_lead->partner_user->name;
        $formattedLead['Last_Assigned_Date'] = $lead->partner_user_lead->created ? $lead->partner_user_lead->created->format('d/m/Y') : '';
        $formattedLead['Current_Workflow_Stage'] = $lead->man_status->man_status_group->group_name;
        $formattedLead['Current_Workflow_Status'] = $lead->man_status->status_name;
        $formattedLead['Current_Lender_Status'] = $lead->lender_status->combined_status_string;
        $formattedLead['Referrer_Person_Name'] = $lead->referrer_person->full_name;
        $formattedLead['Referrer_Contact_Number'] = $lead->referrer_person->contact_number;
        $formattedLead['Bank_Statements_Yes_No'] = $lead->statements_uploaded ? 'Yes' : 'No';
        $formattedLead['LendScore'] = $lead->lend_score->lend_score;

        //incomes, expenses, assets, liabilities
        $incomes = [];
        $incomeSchema = [
            'label' => '',
            'description' => '',
            'shared_amount' => 0,
            'net' => 0,
        ];
        $incomeTotal = 0;
        $incomeNetTotal = 0;
        $pocOwnerName = $lead['owner_poc']['first_name'];

        foreach ($lead['incomes'] as $incomeRaw) {
            $income = $incomeRaw->toArray();
            $income['label'] = "{$pocOwnerName}'s " . $incomeRaw['config_income']['income_type'];
            if ($income['config_income']['id'] === Configure::read('Lend.SALARY_INCOME_CONFIG_ID')) {
                $income['label'] = '<b>' . $incomeRaw['owner']['first_name'] . '\'s</b> ' . $incomeRaw['config_income']['income_type'];
            }
            if ($income['config_income']['id'] === Configure::read('Lend.SPOUSE_INCOME_CONFIG_ID')) {
                $income['label'] = '<b>' . $incomeRaw['owner']['first_name'] . '\'s</b> ' . $incomeRaw['config_income']['income_type'];
            }

            if ($incomeRaw['net']) {
                $incomeNetTotal += $income['shared_amount'];
            } else {
                $incomeTotal += $income['shared_amount'];
            }
            $income['description'] = $incomeRaw['config_income']['description'];
            $income['net'] = $incomeRaw['net'] ? 'Yes' : 'No';

            $incomes[] = array_intersect_key($income, $incomeSchema);
        }

        $expenses = [];
        $expensesTotal = 0;
        $lessSharedExpenses = 0;
        $totalRent = 0;
        foreach ($lead['expenses'] as $expenseRaw) {
            $expense = $expenseRaw->toArray();
            $expenseType = $expenseRaw['config_expense']['expenses_type'];
            $expense['amount_calculated'] = $expense['amount'] ?? 0;
            if ($expense['shared'] === true) {
                $expense['amount_calculated'] = $expense['shared_amount'];
                $lessSharedExpenses += $expense['amount'] * $expense['shared_percentage'] / 100;
            }
            if (empty($expenses[$expenseType])) {
                $expenses[$expenseType] = $expense;
            }
            if ($expenseRaw['config_expense']['id'] === Configure::read('Lend.RENT_EXPENSE_CONFIG_ID')) {
                $totalRent += $expense['amount_calculated'];
            } else {
                $expenses[$expenseType]['amount_calculated'] += $expense['amount_calculated'];
            }
            $expensesTotal += $expense['amount'];
        }
        foreach ($expenses as $key => $expense) {
            $expenses[$key] = [
                'expenses_type' => $expense['config_expense']['expenses_type'],
                'description' => $expense['config_expense']['description'],
                'amount' => $expense['amount_calculated'],
            ];
        }
        $expenses = array_values($expenses);

        $ownerNamesById = [];
        foreach ($lead['owners_all'] as $owner) {
            $ownerNamesById[$owner['owner_id']] = $owner['full_name'];
        }

        $businessAssets = [];
        $applicantAssets = [];
        $businessAssetsTotalValue = 0;
        $applicantAssetsTotalValue = 0;
        foreach ($lead['assets'] as $assetRaw) {
            $asset = $assetRaw->toArray();
            $totalApplicantsShare = 100;
            $assetOwnershipArray = [$pocOwnerName . ' - 100%'];
            if (!empty($asset['shared'])) {
                $totalApplicantsShare = 0;
                $assetOwnershipArray = [];
                foreach ($asset['shared'] as $share) {
                    $assetOwnershipArray[] = $ownerNamesById[$share['owner_id']] . ' - ' . $share['percent'] . '%';
                    $totalApplicantsShare += $share['percent'];
                }
                if ($totalApplicantsShare < 100) {
                    $assetOwnershipArray[] = 'Non-Applicants - ' . (100 - $totalApplicantsShare) . '%';
                }
            }
            $asset['ownership'] = implode('<br>', $assetOwnershipArray);
            $assetObj = [
                'asset_name' => $asset['asset_type']['asset_type_name'],
                'value' => $asset['value'],
                'ownership' => $asset['ownership'],
            ];
            if ($asset['asset_type']['asset_type'] === "business") {
                $businessAssets[] = $assetObj;
                $businessAssetsTotalValue += $assetRaw['value'] * $totalApplicantsShare / 100;
            } else {
                $applicantAssets[] = $assetObj;
                $applicantAssetsTotalValue += $assetRaw['value'] * $totalApplicantsShare / 100;
            }
        }

        $businessLiabilities = [];
        $businessLiabilitiesTotalLimit = 0;
        $businessLiabilitiesTotalMonthlyRepayment = 0;
        $businessLiabilitiesTotalOwing = 0;
        $applicantLiabilities = [];
        $applicantLiabilitiesTotalLimit = 0;
        $applicantLiabilitiesTotalMonthlyRepayment = 0;
        $applicantLiabilitiesTotalOwing = 0;
        foreach ($lead['liabilities'] as $liabilityRaw) {
            $totalApplicantsShare = 100;
            $ownershipArray = [$pocOwnerName . ' - 100%'];
            if (!empty($liabilityRaw['shared'])) {
                $totalApplicantsShare = 0;
                $ownershipArray = [];
                foreach ($liabilityRaw['shared'] as $share) {
                    $ownershipArray[] = $ownerNamesById[$share['owner_id']] . ' - ' . $share['percent'] . '%';
                    $totalApplicantsShare += $share['percent'];
                }
                if ($totalApplicantsShare < 100) {
                    $ownershipArray[] = 'Non-Applicants - ' . (100 - $totalApplicantsShare) . '%';
                }
            }

            $liabilityObj = [
                'limit' => $liabilityRaw['limit'] ?? 0,
                'repayment_pm' => $liabilityRaw['repayment_pm'] ?? 0,
                'loan_balance' => $liabilityRaw['loan_balance'] ?? 0,
                'liability_name' => $liabilityRaw['liability']['liability_name'],
                'ownership' => implode('<br>', $ownershipArray),
            ];
            if ($liabilityRaw['liability']['liability_type'] === "business") {
                $businessLiabilities[] = $liabilityObj;
                $businessLiabilitiesTotalLimit += $liabilityRaw['limit'];
                $businessLiabilitiesTotalMonthlyRepayment += $liabilityRaw['repayment_pm'] * $totalApplicantsShare / 100;
                $businessLiabilitiesTotalOwing += $liabilityRaw['loan_balance'];
            } else {
                $applicantLiabilities[] = $liabilityObj;
                $applicantLiabilitiesTotalLimit += $liabilityRaw['limit'];
                $applicantLiabilitiesTotalMonthlyRepayment += $liabilityRaw['repayment_pm'] * $totalApplicantsShare / 100;
                $applicantLiabilitiesTotalOwing += $liabilityRaw['loan_balance'];
            }
        }

        $formattedLead['Incomes_Monthly'] = $incomes;
        $formattedLead['Expenses_Monthly'] = $expenses;
        $formattedLead['Business_Assets'] = $businessAssets;
        $formattedLead['Business_Liabilities'] = $businessLiabilities;
        $formattedLead['Applicant_Assets'] = $applicantAssets;
        $formattedLead['Applicant_Liabilities'] = $applicantLiabilities;

        $formattedLead['Monthly_Income_Gross'] = $incomeTotal;
        $formattedLead['Monthly_Income_Net'] = $incomeNetTotal;
        $formattedLead['Monthly_Expenses'] = $expensesTotal;
        $formattedLead['Monthly_Expenses_Less_Shared'] = $lessSharedExpenses;
        $formattedLead['Monthly_Expenses_Total'] = $expensesTotal - $lessSharedExpenses;
        $formattedLead['Monthly_Expenses_Total_Less_Rent'] = $expensesTotal - $lessSharedExpenses - $totalRent;
        $formattedLead['Total_Business_Assets_Value'] = $businessAssetsTotalValue;
        $formattedLead['Total_Business_Liabilities_Value'] = $businessLiabilitiesTotalLimit;
        $formattedLead['Business_AL_Net_Position'] = $businessAssetsTotalValue - $businessLiabilitiesTotalLimit;
        $formattedLead['Monthly_Business_Liabilities_Payments'] = $businessLiabilitiesTotalMonthlyRepayment;
        $formattedLead['Total_Business_Liabilities_Owing'] = $businessLiabilitiesTotalOwing;
        $formattedLead['Total_Applicant_Assets_Value'] = $applicantAssetsTotalValue;
        $formattedLead['Total_Applicant_Liabilities_Value'] = $applicantLiabilitiesTotalLimit;
        $formattedLead['Applicant_AL_Net_Position'] = $applicantAssetsTotalValue - $applicantLiabilitiesTotalLimit;
        $formattedLead['Monthly_Applicant_Liabilities_Payments'] = $applicantLiabilitiesTotalMonthlyRepayment;
        $formattedLead['Total_Applicant_Liabilities_Owing'] = $applicantLiabilitiesTotalOwing;



        //Selected Match
        $selected_match = null;
        if (!empty($lead->lender_match_requests)) {
            // $lead->lender_match_requests[0]->match_ref
            $http = new Client();
            $header = [
                'type' => 'json',
                'headers' => ['x-api-key' => getenv('MATCHING_ENGINE_KEY')]
            ];
            $response = $http->get(getenv('DOMAIN_MATCHING_ENGINE') . "/get-match-results-selected/" . $lead->lender_match_requests[0]->match_ref, [], $header);
            $status = $response->getStatusCode();
            $match_result = $response->getJson();
            if ($status != '200') {
                throw new Exception($match_result['error']['message']);
            }

            $selected_match = json_decode(json_encode($match_result['data']['matches'][0]), FALSE);;
        }
        $formattedLead['Selected_Lender'] = $selected_match->lenders->lender_name;
        $formattedLead['Selected_Tier'] = $selected_match->lender_tiers->tier_name;
        $formattedLead['Selected_Product'] = $selected_match->lender_products->product_name;
        $Selected_Estab_Fees = 0;
        $Selected_Monthly_Fees = 0;
        if (!empty($selected_match->lender_match_result_fees)) {
            foreach ($selected_match->lender_match_result_fees as $fee) {
                if ($fee->category == 'Establishment') {
                    $Selected_Estab_Fees += $fee->fee;
                }
            }
            foreach ($selected_match->lender_match_result_fees as $fee) {
                if ($fee->category == 'Ongoing') {
                    $Selected_Monthly_Fees += $fee->fee;
                }
            }
        }
        $formattedLead['Selected_Estab_Fees'] = $Selected_Estab_Fees;
        $formattedLead['Selected_Monthly_Fees'] = $Selected_Monthly_Fees;
        $formattedLead['Selected_Origination_Fee'] = $selected_match->origination_fee;
        $formattedLead['Selected_Finance_Fees_Yes_No'] = $selected_match->loan_amt_inc_fees ? 'Yes' : 'No';
        $formattedLead['Selected_Finance_Amt'] = $selected_match->financed_amount;
        $formattedLead['Selected_Commission'] = $selected_match->commission;
        $formattedLead['Selected_Term_Months'] = $selected_match->term_months;
        $formattedLead['Selected_Working_Cap_APR'] = $selected_match->apr ? $selected_match->apr : 'N/A';
        $formattedLead['Selected_Customer_Rate'] = $selected_match->customer_rate;
        $formattedLead['Selected_Rate_After_Discount'] = $selected_match->adjusted_rate;
        $formattedLead['Selected_Repayment_Amt'] = $selected_match->repayment_amt;
        switch ($selected_match->repayment_freq) {
            case 'week':
                $formattedLead['Selected_Repayment_Frequency'] = 'Weekly';
                break;
            case 'biweek':
                $formattedLead['Selected_Repayment_Frequency'] = 'Fortnightly';
                break;
            case 'month':
                $formattedLead['Selected_Repayment_Frequency'] = 'Monthly';
                break;
            default:
                $formattedLead['Selected_Repayment_Frequency'] = $selected_match->repayment_freq;
                break;
        }
        $formattedLead['Selected_Loan_Amount'] = $selected_match->loan_amt;
        $formattedLead['Submission_Date'] = $selected_match->created;

        //Settlement
        $settlement = $lead->settlement_reviews[0];
        $formattedLead['Settled_Lender'] = $settlement->lender_name;
        $formattedLead['Settled_Tier'] = $settlement->tier_name;
        $formattedLead['Settled_Product'] = $settlement->settled_product_name;
        $formattedLead['Settled_Estab_Fees'] = $settlement->total_estab_fees;
        $formattedLead['Settled_Monthly_Fees'] = $settlement->monthly_fees;
        $formattedLead['Settled_Origination_Fee'] = $settlement->origination_fee;
        $formattedLead['Settled_Finance_Fees_Yes_No'] = $settlement->application_fee_included ? 'Yes' : 'No';
        $formattedLead['Settled_Finance_Amt'] = $settlement->loan_amount;
        $formattedLead['Settled_Term_Months'] = $settlement->loan_term;
        $formattedLead['Settled_Working_Cap_APR'] = $settlement->apr ? $settlement->apr : 'N/A';
        $formattedLead['Settled_Customer_Rate'] = $settlement->customer_rate;
        $formattedLead['Settled_Repayment_Amt'] = $settlement->repayment_amt;
        $formattedLead['Settled_Repayment_Frequency'] = $settlement->repayment_freq;
        $formattedLead['Settled_Date'] = $settlement->settlement_date ? $settlement->settlement_date->format('d/m/Y') : '';
        $formattedLead['Settled_Contract_Ref'] = $settlement->contract_ref;
        $formattedLead['Settled_CCI_Product'] = $settlement->cci_ins_product;
        $formattedLead['Settled_CCI_Premium'] = $settlement->cci_ins_prem;
        $formattedLead['Settled_Comp_Product'] = $settlement->comp_ins_product;
        $formattedLead['Settled_Comp_Premium'] = $settlement->comp_ins_prem;
        $formattedLead['Settled_Gap_Product'] = $settlement->gap_ins_product ? $settlement->gap_ins_product : 'N/A';
        $formattedLead['Settled_Gap_Premium'] = $settlement->gap_ins_prem ? $settlement->gap_ins_prem : 'N/A';
        $formattedLead['Settled_Warranty_Product'] = $settlement->warranty_product;
        $formattedLead['Settled_Warranty_Premium'] = $settlement->warranty_prem;
        $formattedLead['Settled_Commission'] = $settlement->partner_commissions->commission;
        $formattedLead['Settled_Referrer_Commission'] = $settlement->referrer_commission;
        $formattedLead['Settled_CCI_Commission'] = $settlement->cci_ins_comms;
        $formattedLead['Settled_Comp_Commission'] = $settlement->comp_ins_comms;
        $formattedLead['Settled_Gap_Commission'] = $settlement->gap_ins_comms;
        $formattedLead['Settled_Warranty_Commission'] = $settlement->warranty_comms;
        $formattedLead['Settled_Other_Commission'] = $settlement->other_income;
        $formattedLead['Settled_VBI_Commission'] = $settlement->vbi_income;


        //Asset Finance
        $asset_finance = $lead->asset_finance;
        $formattedLead['AF_E2E_Yes_No'] = $lead->pricing->application_type === 'e2e' ? 'Yes' : 'No';
        $formattedLead['AF_Contract_Type'] = $asset_finance->contract_type;
        $formattedLead['AF_Description'] = $asset_finance->asset_description ? $asset_finance->asset_description : 'N/A';
        $formattedLead['AF_Make'] = $asset_finance->make;
        $formattedLead['AF_Model'] = $asset_finance->model;
        $formattedLead['AF_Year'] = $asset_finance->year;
        $formattedLead['AF_Age_Months'] = $asset_finance->asset_age_months;
        $formattedLead['AF_Condition'] = $asset_finance->condition;
        $formattedLead['AF_Sale_Type'] = $asset_finance->sale_type;
        $formattedLead['AF_Purchase_Amt'] = $asset_finance->asset_purchase_price;
        $formattedLead['AF_Value'] = $asset_finance->finance_amount;
        $formattedLead['AF_Value_Source'] = $asset_finance->valuation_type;
        $formattedLead['AF_Deposit_Amt'] = $asset_finance->asset_deposit;
        $formattedLead['AF_Tradein_Amt'] = $asset_finance->asset_tradein;
        $formattedLead['AF_Insurance_Addon_Amt'] = $asset_finance->insurance_addon;
        $formattedLead['AF_Other_Addons_Amt'] = $asset_finance->other_addons;
        $formattedLead['AF_Ballon_Amt'] = $asset_finance->asset_balloon;
        $formattedLead['AF_Finance_Amt'] = $asset_finance->finance_amount;
        $formattedLead['AF_LTV'] = $asset_finance->ltv;
        $formattedLead['AF_VIN'] = $asset_finance->vin;
        $formattedLead['AF_Odometer'] = $asset_finance->odometer;
        $formattedLead['AF_Eng_Number'] = $asset_finance->engine;
        $formattedLead['AF_Reg_Number'] = $asset_finance->rego;
        $formattedLead['AF_Colour'] = $asset_finance->colour;
        $formattedLead['AF_Origin_State'] = $asset_finance->rego_state;
        $formattedLead['AF_Supplier_Name'] = $asset_finance->supplier;
        $formattedLead['AF_Supplier_Contact_Name'] = $asset_finance->supplier_contact_name;
        $formattedLead['AF_Supplier_Contact_Number'] = $asset_finance->supplier_contact_number;
        $formattedLead['AF_Supplier_Contact_Email'] = $asset_finance->supplier_email;
        $formattedLead['AF_Supplier_Quote_Ref'] = $asset_finance->supplier_quote_ref;
        if (empty($this->requestBody['payload'])) {
            $this->requestBody['payload'] = [];
        }
        $this->requestBody['payload'] = array_merge($formattedLead, $this->requestBody['payload']);
        return $this;
    }

    public function buildNonApplicantRecipients($nonApplicantRecipients = [])
    {
        if (!empty($this->requestBody['recipients'])) {
            $recipients = $this->requestBody['recipients'];
        } else {
            $recipients = [];
        }

        foreach ($nonApplicantRecipients as $ref => $recipient) {
            $recipients[] = [
                'ref' => $recipient['ref'],
                'Recipient_Type' => 'Non-Applicant',
                'Recipient_First_Name' => explode(' ', $recipient['name'])[0] ?? '',
                'Recipient_Middle_Name' => '',
                'Recipient_Last_Name' => explode(' ', $recipient['name'])[1] ?? '',
                'Recipient_Full_Name' => $recipient['name'],
                'Recipient_Email' => $recipient['email'],
                'Recipient_Phone' => $recipient['mobile'],
                'email' => $recipient['email'],
                'mobile' => $recipient['mobile'],
                'first_name' => explode(' ', $recipient['name'])[0] ?? '',
                'middle_name' => '',
                'last_name' => explode(' ', $recipient['name'])[1] ?? '',
                'Recipient_Addr' => $recipient['address'] ?? '',
                'address' => $recipient['address'] ?? '',
                'is_non_applicant' => true,
                'non_applicant_ref' => $recipient['ref']
            ];
        }

        $this->requestBody['recipients'] = $recipients;
        return $this;
    }
}
