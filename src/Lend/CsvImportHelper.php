<?php

namespace App\Lend;

use Cake\Core\Configure;
use Cake\Datasource\ConnectionManager;
use Cake\Log\Log;
use Cake\ORM\TableRegistry;

class CsvImportHelper
{
    protected $settings;
    protected $partnerUser;
    protected $fieldMap;
    protected $columnNames;
    protected $foundFields;
    protected $notFoundFields;
    protected $entitySavingOrder;
    protected $entityForeignKeys;
    protected $entityDefaultValues;
    protected $entityFieldValidation;
    protected $entityFieldFormat;
    protected $entityRequiredFields;
    protected $entityDuplicationCheck;
    protected $entityAfterSaveCallbacks;
    protected $csvFileContent;
    protected $csvData;
    protected $header;

    protected $lineIndex;
    protected $savedEntities;
    protected $errors;
    protected $errorFields;
    protected $enumOptions;
    protected $dateFields;
    protected $warnings;
    protected $warningFields;
    protected $exceptionCount;


    public function __construct($settings, $csvFileContent)
    {
        $this->settings = $settings;
        $this->partnerUser = $settings['partnerUser'];
        $this->fieldMap = $settings['fieldMap'];
        $this->columnNames = $settings['columnNames'];
        $this->entitySavingOrder = $settings['entitySavingOrder'];
        $this->entityForeignKeys = $settings['entityForeignKeys'];
        $this->entityDefaultValues = $settings['entityDefaultValues'];
        $this->entityFieldValidation = $settings['entityFieldValidation'];
        $this->entityFieldFormat = $settings['entityFieldFormat'];
        $this->entityRequiredFields = $settings['entityRequiredFields'];
        $this->entityDuplicationCheck = $settings['entityDuplicationCheck'];
        $this->entityAfterSaveCallbacks = $settings['entityAfterSaveCallbacks'];

        $this->csvFileContent = $csvFileContent;
        $this->savedEntities = [];
        $this->errorFields = [];
        $this->errors = [];
        $this->warningFields = [];
        $this->warnings = [];
        $this->enumOptions = [];

        $this->exceptionCount = 0;
    }

    protected function setCsvData($csvData)
    {
        $this->csvData = $csvData;
    }

    protected function implodeWithQuotes(array $array)
    {
        return implode(',', array_map(function ($v) {
            return '"' . $v . '"';
        }, $array));
    }

    protected function getCsvValue($csvData, $entityName, $field, $entityIndex = false)
    {
        if ($entityIndex !== false) {
            $csvDataIndex = array_search($entityName . '.' . $entityIndex . '.' . $field, $this->foundFields);
        } else {
            $csvDataIndex = array_search($entityName . '.' . $field, $this->foundFields);
        }
        return $csvData[$csvDataIndex];
    }
    protected function getCsvIndex($entityName, $field, $entityIndex = false)
    {
        if ($entityIndex !== false) {
            return array_search($entityName . '.' . $entityIndex . '.' . $field, $this->foundFields);
        } else {
            return array_search($entityName . '.' . $field, $this->foundFields);
        }
    }

    protected function setCsvValue(&$csvData, $newValue, $entityName, $field, $entityIndex = false)
    {
        $csvDataIndex = $this->getCsvIndex($entityName, $field, $entityIndex);
        $csvData[$csvDataIndex] = $newValue;
    }

    protected function appendCsvValue(&$csvData, $newValue, $entityName, $field, $entityIndex = false)
    {
        $csvData[$this->getCsvIndex($entityName, $field, $entityIndex)] = $this->getCsvValue($csvData, $entityName, $field) . $newValue;
    }

    protected function setForeighKey($entityName, &$entityData)
    {
        $entityForeignKeys = [];
        if (!empty($this->entityForeignKeys[$entityName])) {
            $foreignKey = explode('.', $this->entityForeignKeys[$entityName]);
            $entityForeignKeys[] = $foreignKey[2];
            if (!empty($this->savedEntities[$foreignKey[0]])) {
                $entityData[$foreignKey[2]] = $this->savedEntities[$foreignKey[0]][$foreignKey[1]];
            }
        }
        return $entityForeignKeys;
    }

    protected function setDefaultValues($entityName, &$entityData)
    {
        if (!empty($this->entityDefaultValues[$entityName])) {
            foreach ($this->entityDefaultValues[$entityName] as $key => $value) {
                $entityData[$key] = $value;
            }
        }
    }

    protected function formatFields(&$csvData, $entityName, &$entityData, $entityIndex = false)
    {
      if (!empty($this->entityFieldFormat[$entityName])) {
        foreach ($this->entityFieldFormat[$entityName] as $field => $format) {
          $method = $format[1];
          $entityData[$field] = $format[0]::$method($entityData);
          if ($entityIndex !== false) {
            $csvDataIndex = array_search($entityName . '.' . $entityIndex . '.' . $field, $this->foundFields);
            if ($csvDataIndex !== false) {
              $csvData[$csvDataIndex] = $entityData[$field];
            }
          }
          else {
            $csvDataIndex = array_search($entityName . '.' . $field, $this->foundFields);
            if ($csvDataIndex !== false) {
              $csvData[$csvDataIndex] = $entityData[$field];
            }
          }
        }
      }
    }

    protected function validateEntity($entityName, &$entityData, $entityIndex = false)
    {

        if (!empty($this->entityFieldValidation[$entityName])) {
            foreach ($this->entityFieldValidation[$entityName] as $field => $validation) {
                $methodName = $validation[1];
                if (!$validation[0]::$methodName($entityData[$field])) {
                    if (!empty($this->entityRequiredFields[$entityName])
                        && in_array($field, $this->entityRequiredFields[$entityName])
                    ) {
                        if ($entityIndex !== false) {
                            $this->errors[$this->lineIndex][] = "Line: $this->lineIndex, validation failed in field: $field of entity: $entityName index: $entityIndex";
                        } else {
                            $this->errors[$this->lineIndex][] = "Line: $this->lineIndex, validation failed in field: $field of entity: $entityName";
                        }
                        $this->errorFields[$this->lineIndex][] = $field;
                    } else {
                        if ($entityIndex !== false) {
                            $this->warnings[$this->lineIndex][] = "Line: $this->lineIndex, validation failed in field: $field of entity: $entityName index: $entityIndex";
                        } else {
                            $this->warnings[$this->lineIndex][] = "Line: $this->lineIndex, validation failed in field: $field of entity: $entityName";
                        }
                        $this->warningFields[$this->lineIndex][] = $field;
                    }
                    unset($entityData[$field]);
                }
            }
        }
    }

    protected function checkMissingFields($entityName, $entityData, $entityForeignKeys, &$csvData, $entityIndex = false)
    {
        $missingFieldsCount = 0;
        $presentFieldsCount = 0;
        $missingFieldErrors = [];
        $missingFields = [];
        if (!empty($this->entityRequiredFields[$entityName])) {
            foreach ($this->entityRequiredFields[$entityName] as $field) {
                if (empty($entityData[$field]) || $entityData[$field] === 'REQUIRED') {
                    $missingFieldsCount++;
                    if ($entityIndex !== false) {
                        $missingFieldErrors[] = "Line: $this->lineIndex, missing field: $field of entity: $entityName index: $entityIndex";
                        if (!in_array($field, $entityForeignKeys)) {
                            $csvDataIndex = array_search($entityName . '.' . $entityIndex . '.' . $field, $this->foundFields);
                            if ($csvDataIndex !== false) {
                                $csvData[$csvDataIndex] = 'REQUIRED';
                            }
                            if (!in_array($field, $this->errorFields[$this->lineIndex])) {
                                $this->errorFields[$this->lineIndex][] = $field;
                            }
                        }
                    } else {
                        $missingFieldErrors[] = "Line: $this->lineIndex, missing field: $field of entity: $entityName";

                        if (!in_array($field, $entityForeignKeys)) {
                            $csvDataIndex = array_search($entityName . '.' . $field, $this->foundFields);
                            // If index is false the column is missing completely.
                            if ($csvDataIndex !== false) {
                              $csvData[$csvDataIndex] = 'REQUIRED';
                            }

                            if (!in_array($field, $this->errorFields[$this->lineIndex])) {
                                $this->errorFields[$this->lineIndex][] = $field;
                            }
                        }
                    }
                } else {
                    $presentFieldsCount++;
                }
            }
            if ($presentFieldsCount > 0 && $missingFieldsCount > 0) {
                $this->errors[$this->lineIndex] = array_merge($this->errors[$this->lineIndex], $missingFieldErrors);
                $this->errorFields[$this->lineIndex] = array_merge($this->errorFields[$this->lineIndex], $missingFields);
            }
        }
        return $missingFieldsCount;
    }

    protected function readEnumsFromDb()
    {
        //Record all enum options per saved entity.
        foreach ($this->entitySavingOrder as $entityName) {
            $model = TableRegistry::getTableLocator()->get($entityName);
            $sql = "SHOW COLUMNS FROM `{$model->getTable()}`";
            $db = ConnectionManager::get(Configure::read('datasource'));
            $fieldData = $db->execute($sql)->fetchAll('assoc');

            if (!empty($fieldData)) {
                foreach ($fieldData as $field) {
                    $isEnum = strpos($field['Type'], 'enum');
                    $isDate = strpos($field['Type'], 'date');
                    if ($isEnum !== false) {
                        $patterns = array('enum(', ')', '\'');
                        $enumData = str_replace($patterns, '', $field['Type']);
                        if (!empty($enumData)) {
                            $this->enumOptions[$entityName][$field['Field']] = explode(',', $enumData);
                        }
                    }
                    if ($isDate !== false) {
                        $this->dateFields[$entityName][] = $field['Field'];
                    }
                }
            }
        }
    }

    protected function processEnums(&$csvData, $entityName, &$entityData, $entityIndex = false)
    {
        $incorrectEnumsCount = 0;

        if (!empty($this->enumOptions[$entityName])) {
            foreach ($this->enumOptions[$entityName] as $field => $options) {
                if (!empty($entityData[$field]) && !in_array($entityData[$field], $options)) {
                    $tempValue = $entityData[$field];
                    $tempValue = ucfirst($tempValue);
                    if (in_array($tempValue, $options)) {
                        $entityData[$field] = $tempValue;
                        continue;
                    }
                    $tempValue = str_replace('.', '', $tempValue);
                    if (in_array($tempValue, $options)) {
                        $entityData[$field] = $tempValue;
                        continue;
                    }
                    $entityData[$field] = null;
                    $incorrectEnumsCount++;
                    if ($entityIndex !== false) {
                        $this->warnings[$this->lineIndex][] = "Line: $this->lineIndex, incorrect enum value in field: $field of entity: $entityName index: $entityIndex";
                    } else {
                        $this->warnings[$this->lineIndex][] = "Line: $this->lineIndex, incorrect enum value in field: $field of entity: $entityName";
                    }

                    $this->appendCsvValue($csvData, '*', $entityName, $field, $entityIndex);

                    $this->warningFields[$this->lineIndex][] = $field;
                    unset($entityData[$field]);
                }
            }
        }

        return $incorrectEnumsCount;
    }

    protected function processDates(&$csvData, $entityName, &$entityData, $entityIndex = false)
    {
        if (!empty($this->dateFields[$entityName])) {
            foreach ($this->dateFields[$entityName] as $field) {
                if (!empty($entityData[$field])) {
                    $datetime = \DateTime::createFromFormat("d/m/Y", $entityData[$field]);
                    if ($datetime !== false) {
                        $entityData[$field] = $datetime->format('Y-m-d');
                    } else {
                        $warningText = "Line: $this->lineIndex, incorrect date format: $field of entity: $entityName";
                        if ($entityIndex) {
                            $warningText .= "index: $entityIndex";
                        }
                        $this->warnings[$this->lineIndex][] = $warningText;
                        $entityData[$field] = null;
                        $this->setCsvValue($csvData, 'FORMAT ERROR', $entityName, $field, $entityIndex);
                    }
                }
            }
        }
    }

    protected function parseHeader($headerLine) {
      $headerFields = str_getcsv($headerLine);
      $notFoundFields = $headerFields;
      $foundIndexes = [];
      $foundFields = [];
      $flatAliases = [];
      foreach ($this->fieldMap as $field => $aliases) {
        foreach ($aliases as $alias) {
          $flatAliases[$alias] = $field;
        }
      }
      foreach ($headerFields as $index => $columnName) {
        if (isset($flatAliases[$columnName])) {
          $foundIndexes[] = $index;
          $foundFields[] = $flatAliases[$columnName];
          unset($notFoundFields[$index]);
        }
        else {
          $foundFields[] = 'skip';
        }
      }
      foreach ($foundIndexes as $index) {
        unset($notFoundFields[$index]);
      }
      $this->foundFields = $foundFields;
      $this->notFoundFields = $notFoundFields;
    }

    protected function handleEntityImport(&$csvData, $entityName, &$entityData, $entityIndex = false)
    {
        $table = TableRegistry::getTableLocator()->get($entityName);

        $this->setDefaultValues($entityName, $entityData);
        $this->formatFields($csvData, $entityName, $entityData, $entityIndex);

        $entityForeignKeys = $this->setForeighKey($entityName, $entityData);

        $this->validateEntity($entityName, $entityData, $entityIndex);

        $missingFieldsCount = $this->checkMissingFields($entityName, $entityData, $entityForeignKeys, $csvData, $entityIndex);

        $this->processEnums($csvData, $entityName, $entityData, $entityIndex);
        $this->processDates($csvData, $entityName, $entityData, $entityIndex);

        if ($missingFieldsCount === 0) {
            $duplicate = false;
            if (!empty($this->entityDuplicationCheck[$entityName])) {
                $duplicateCheck = $this->entityDuplicationCheck[$entityName];
                $duplicateCheckMethod = $duplicateCheck[1];
                $duplicate = $duplicateCheck[0]::$duplicateCheckMethod($entityData);
            }
            $entity = null;
            if ($duplicate) {
                $entity = $duplicate;
                foreach ($entityData as $field => $value) {
                    if ($field !== 'is_imported') {
                          $entity->set($field, $value);
                    }
                }
                if ($entityName === 'PartnerAccountPeopleEntity') {
                    array_unshift($this->warningFields[$this->lineIndex], 'DUPLICATE');
                }
                $table->save($entity);
            } else {
                $entity = $table->newEntity($entityData);
                $table->save($entity);
            }
            if (!empty($this->entityAfterSaveCallbacks[$entityName])) {
                $afterSaveCallback = $this->entityAfterSaveCallbacks[$entityName];
                $afterSaveCallbackMethod = $afterSaveCallback[1];
                $saveContext = [
                  'partnerUser' => $this->partnerUser,
                  'entities' => $this->savedEntities,
                ];
                $afterSaveCallback[0]::$afterSaveCallbackMethod($entity, $saveContext);
            }

            if ($entityIndex !== false) {
                $this->savedEntities[$entityName][$entityIndex] = $entity;
            } else {
                $this->savedEntities[$entityName] = $entity;
            }
        }
    }

    protected function handleEntityImportException(\Exception $e, $entityName, $entityIndex = null)
    {
        $errorMessage = "Line: $this->lineIndex, exception importing entity: $entityName";
        if ($entityIndex !== null) {
            $errorMessage .= " index: $entityIndex";
        }

        $this->errors[$this->lineIndex][] = $errorMessage;

        if (!in_array('EXCEPTION', $this->errorFields[$this->lineIndex])) {
            $this->errorFields[$this->lineIndex][] = 'EXCEPTION';
        }

        if ($e->getMessage() !== '') {
            $this->errors[$this->lineIndex][] = "Exception message: " . $e->getMessage();
        }

        Log::error('CSV Import Exception: ' . $e->getMessage() . '. trace: ' . $e->getTraceAsString());

        $this->exceptionCount++;
    }

    public function import()
    {
        if (!empty($this->csvFileContent)) {
            $this->readEnumsFromDb();

            $csvLines = preg_split("/\r\n|\n|\r/", $this->csvFileContent);

            $firstLine = array_shift($csvLines);
            $this->parseHeader($firstLine);
            $isErrorReport = false;
            // Need to support both formats of error reports.
            if (strpos($firstLine, ',Errors,Warnings')) {
                $isErrorReport = true;
                $firstLine = str_replace(',Errors,Warnings', '', $firstLine);
            }
            if (strpos($firstLine, ',"Errors","Warnings"')) {
                $isErrorReport = true;
                $firstLine = str_replace(',"Errors","Warnings"', '', $firstLine);
            }

            $errorLines = [];
            $successCount = 0;
            $errorCount = 0;
            $warningOnlyCount = 0;
            $duplicateCount = 0;
            $this->lineIndex = 1;
            foreach ($csvLines as $csvLine) {
                if ($csvLine === '') {
                    continue;
                }
                // Reset saved entities for each line.
                $this->savedEntities = [];
                $csvData = str_getcsv($csvLine);
                if ($isErrorReport) {
                    unset($csvData[count($csvData) - 1]);
                    unset($csvData[count($csvData) - 1]);
                }

                $entityValues = [];

                foreach ($csvData as $index => $value) {
                    if ($value === 'NULL') {
                        continue;
                    }
                    // Remove quotes from values.
                    $value = str_replace('"', '', $value);
                    $mapValue = $this->foundFields[$index];
                    $mapValueArray = explode('.', $mapValue);
                    if ($mapValueArray[0] === 'skip') {
                        continue;
                    }

                    if (count($mapValueArray) === 2) {
                        if ($mapValueArray[1] === 'partner_user_ref') {
                            $partnerUserId = LendInternalAuth::unhashPartnerUserId($value);
                            if (!empty($partnerUserId)) {
                                $entityValues[$mapValueArray[0]]['partner_user_id'] = $partnerUserId;
                            }
                            if (empty($partnerUserId)) {
//                                $this->errors[$this->lineIndex][] = "Line: $this->lineIndex, invalid partner_user_ref";
//                                $this->errorFields[$this->lineIndex][] = 'partner_user_ref';
//                                $this->setCsvValue($csvData, 'INVALID', $mapValueArray[0], 'partner_user_ref');
                            }
                        } else {
                            if (!empty($value)) {
                                $entityValues[$mapValueArray[0]][$mapValueArray[1]] = $value;
                            }
                        }
                    }
                    if (count($mapValueArray) === 3) {
                        if (!empty($value)) {
                            $entityValues[$mapValueArray[0]][$mapValueArray[1]][$mapValueArray[2]] = $value;
                        }
                    }
                }

                foreach ($this->entitySavingOrder as $entityName) {
                    $entityData = $entityValues[$entityName];
                    if (empty($entityData)) {
                        continue;
                    }
                    if (!empty($entityValues[$entityName][0]) && is_array($entityValues[$entityName][0])) {
                        foreach ($entityValues[$entityName] as $entityIndex => $subEntityData) {
                            try {
                                $this->handleEntityImport($csvData, $entityName, $subEntityData, $entityIndex);
                            } catch (\Exception $e) {
                                $this->handleEntityImportException($e, $entityName, $entityIndex);
                            }
                        }

                        continue;
                    }

                    try {
                        $this->handleEntityImport($csvData, $entityName, $entityData);
                    } catch (\Exception $e) {
                        $this->handleEntityImportException($e, $entityName);
                    }
                }

                if (!empty($this->errorFields[$this->lineIndex]) || !empty($this->warningFields[$this->lineIndex])) {
                    $newErrorLine = $this->implodeWithQuotes($csvData);
                    if (!empty($this->errorFields[$this->lineIndex])) {
                        $newErrorLine .= ',"' . implode(' ', $this->errorFields[$this->lineIndex]) . '"';
                        $errorCount++;
                    } else {
                        $newErrorLine .= ',""';
                    }
                    if (!empty($this->warningFields[$this->lineIndex])) {
                        if (in_array('DUPLICATE', $this->warningFields[$this->lineIndex])) {
                          $duplicateCount++;
                        } elseif (empty($this->errorFields[$this->lineIndex])) {
                            $warningOnlyCount++;
                        }
                        $newErrorLine .= ',"' . implode(' ', $this->warningFields[$this->lineIndex]) . '"';
                    } else {
                        $newErrorLine .= ',""';
                    }
                    $errorLines[] = $newErrorLine;
                } else {
                    $successCount++;
                }

                $this->lineIndex++;
            }
        }

        if (!empty($errorLines)) {
            $firstLine .= ',Errors,Warnings';
            $firstLine = str_replace(array("\r", "\n"), '', $firstLine);
            array_unshift($errorLines, $firstLine);
        }
        $errorCsv = implode("\n", $errorLines);

        if ($this->exceptionCount > 0) {
          TableRegistry::getTableLocator()->get('App')->postToSlack("CSV Import Exception. Search CloudWatch 'CSV Import Exception' for details", "lend_errors");
        }

        return [
            'success' => empty($errorLines),
            'errorCsv' => $errorCsv,
            'errorCsvLines' => $errorLines,
            'successCount' => $successCount,
            'errorCount' => $errorCount,
            'warningsOnlyCount' => $warningOnlyCount,
            'duplicateCount' => $duplicateCount,
            'notFoundFields' => $this->notFoundFields,
            'exceptionCount' => $this->exceptionCount,
        ];
    }
}
