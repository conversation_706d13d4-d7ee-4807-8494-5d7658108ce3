<?php
namespace App\Lend;

use App\Model\Table\AppTable;
use Cake\Log\Log;
use Cake\ORM\TableRegistry;
use Cake\Core\Configure;
use Cake\Chronos\Chronos;
use DateTime;

/* ============================================================
* How to use
* $lv = new LeadValidation;
* $lv->setRequiredFields($required_fields); // Set required fields if necessary
* $errors = $lv->validate($data); // Check validation
============================================================ */
class LeadValidation extends AppTable {
  private $requiredFields=array();
  private $acceptedFields=array();
  public $check_all_owners = false;

  function __construct() {
    parent::__construct(array());
  }

  /* ============================================================
  *Required Fields setting like following example:
    $required_fields = array(
      'lead' => array(
        'amount_requested', 'purpose_id', 'industry_id', 'organisation_name', 'company_registration_date'
      ),
      'lead_owner' => array(
        'first_name', 'last_name', 'email', 'mobile'
      ),
    );
  ============================================================ */
  public function setRequiredFields($fields){
    $this->requiredFields = $fields;
  }

  public function setAcceptedFields($fields) {
    $this->acceptedFields = $fields;
  }

  public function validate($data, $fromPartnerAPI = false) {
    $errors         = array();

    if(!empty($this->acceptedFields)):
      foreach ($data as $table => $fields):
        if (!is_array($fields)) continue;
        $apiKey = $table;
        if ($table==='lead_owner') $apiKey = 'owner';
        if (($table === 'lead_owner' AND $this->check_all_owners) ||
          in_array($table, ['lead_owner_employments', 'lead_owner_incomes', 'lead_owner_addresses', 'lead_notes'])
        ) {
          foreach ($data[$table] as $item) {
            foreach ($item as $field => $value) {
              if (!in_array($field, $this->acceptedFields[$table]) AND !$this->errorAlreadyProvided($apiKey.'['.$field.']', $errors)) {
                $errors[] = array('table' => $apiKey, 'field' => '['.$field.']', 'error'=>'This is not an accepted field.');
                $errorsProvided[] = $apiKey.'['.$field.']';
              }
            }
          }
        } else {
          foreach ($fields as $field=>$value):
            if (!in_array($field, $this->acceptedFields[$table]) AND !$this->errorAlreadyProvided($apiKey.'['.$field.']', $errors)) {
              $errors[] = array('table' => $apiKey, 'field' => '['.$field.']', 'error'=>'This is not an accepted field.');
              $errorsProvided[] = $apiKey.'['.$field.']';
            }
          endforeach;
        }
      endforeach;
    endif;

    if ($this->check_all_owners) {
      $apiKey = 'owner';
      foreach ($data['lead_owner'] as $key => $owner) {
        // Email is valid
        if (!empty($owner['email']) AND !filter_var($owner['email'], FILTER_VALIDATE_EMAIL) ) {
          if (!$this->errorAlreadyProvided('owner[email]', $errors))
            $errors[] = array('table' => $apiKey, 'field' => '[email]', 'error'=>' Email is not a valid email', 'specific'=>$owner['first_name']);
          $errorsProvided[] = 'owner[email]';
        }

        // Mobile length is 10
        if (getenv('REGION', true) === 'au') {
          if (!empty($owner['mobile']) AND strlen($owner['mobile'])!=10) {
            if (!$this->errorAlreadyProvided('owner[mobile]', $errors))
              $errors[] = array('table' => $apiKey, 'field' => '[mobile]', 'error'=>' Mobile is not an Australian 10-character format', 'specific'=>$owner['first_name']);
            $errorsProvided[] = 'owner[mobile]';
          }
        } elseif (getenv('REGION', true) === 'nz') {
          if (!empty($owner['mobile']) AND strlen($owner['mobile']) > 11) {
            if (!$this->errorAlreadyProvided('owner[mobile]', $errors))
              $errors[] = array('table' => $apiKey, 'field' => '[mobile]', 'error'=>' Mobile is not a valid New Zealand format', 'specific'=>$owner['first_name']);
            $errorsProvided[] = 'owner[mobile]';
          }
        }

        if (getenv('REGION', true) === 'au') {
          // Phone length is 10
          if (!empty($owner['phone']) AND strlen($owner['phone'])!=10) {
            if (!$this->errorAlreadyProvided('owner[phone]', $errors))
              $errors[] = array('table' => $apiKey, 'field' => '[phone]', 'error'=>' Phone is not an Australian 10-character format', 'specific'=>$owner['first_name']);
            $errorsProvided[] = 'owner[phone]';
          }
        } elseif (getenv('REGION', true) === 'nz') {
          // Phone length is 9
          if (!empty($owner['phone']) AND strlen($owner['phone']) != 9) {
            if (!$this->errorAlreadyProvided('owner[phone]', $errors))
              $errors[] = array('table' => $apiKey, 'field' => '[phone]', 'error'=>' Phone is not a valid New Zealand format', 'specific'=>$owner['first_name']);
            $errorsProvided[] = 'owner[phone]';
          }
        }

        // DOB is not valid date format
        if (!empty($owner['dob']) AND !$this->isValidYYYYMMDD($owner['dob']) ) {
          if (!$this->errorAlreadyProvided('owner[dob]', $errors))
            $errors[] = array('table' => $apiKey, 'field' => '[dob]', 'error'=>' DOB date is not the accepted format (YYYY-MM-DD)', 'specific'=>$owner['first_name']);
          $errorsProvided[] = 'owner[dob]';
        }

        //driving_licence_expiry is not valid date format
        if (!empty($owner['driving_licence_expiry']) AND !$this->isValidYYYYMMDD($owner['driving_licence_expiry']) ) {
          if (!$this->errorAlreadyProvided('owner[driving_licence_expiry]', $errors))
            $errors[] = array('table' => $apiKey, 'field' => '[driving_licence_expiry]', 'error'=>' Driving Licence Expiry date is not the accepted format (YYYY-MM-DD)', 'specific'=>$owner['first_name']);
          $errorsProvided[] = 'owner[driving_licence_expiry]';
        }

        //driving_licence_state must in the ENUM
        if(!empty($owner['driving_licence_state']) AND !in_array($owner['driving_licence_state'], Configure::read('Lend.AU_states'))){
          if (!$this->errorAlreadyProvided('owner[driving_licence_state]', $errors))
            $errors[] = array('table' => $apiKey, 'field' => '[driving_licence_state]', 'error'=>' Driving Licence State must be in Australia', 'specific'=>$owner['first_name']);
          $errorsProvided[] = 'owner[driving_licence_state]';
        }

        //credit_history must in the ENUM
        if(!empty($owner['credit_history']) AND !in_array($owner['credit_history'], Configure::read('Lend.credit_history'))){
          if (!$this->errorAlreadyProvided('owner[credit_history]', $errors))
            $errors[] = array('table' => $apiKey, 'field' => '[credit_history]', 'error'=>' Invalid Credit History option provided', 'specific'=>$owner['first_name']);
          $errorsProvided[] = 'owner[credit_history]';
        }

        //lead_owner state must in the ENUM
        if(!empty($owner['state']) AND !in_array($owner['state'], Configure::read('Lend.AU_states'))){
          if (!$this->errorAlreadyProvided('owner[state]', $errors))
            $errors[] = array('table' => $apiKey, 'field' => '[state]', 'error'=>' Personal Address State must be in Australia', 'specific'=>$owner['first_name']);
          $errorsProvided[] = 'owner[state]';
        }
      }
    } else {
      // Email is valid
      $apiKey = 'owner';
      if (!empty($data['lead_owner']['email']) AND !filter_var($data['lead_owner']['email'], FILTER_VALIDATE_EMAIL) ) {
        if (!$this->errorAlreadyProvided('owner[email]', $errors))
          $errors[] = array('table' => $apiKey, 'field' => '[email]', 'error'=>'Email is not a valid email');
        $errorsProvided[] = 'owner[email]';
      }

      // Check Mobile
      if (getenv('REGION', true) === 'au') {
        // Mobile length is 10
        if (!empty($data['lead_owner']['mobile']) AND strlen($data['lead_owner']['mobile'])!=10) {
          if (!$this->errorAlreadyProvided('owner[mobile]', $errors))
            $errors[] = array('table' => $apiKey, 'field' => '[mobile]', 'error'=>'Mobile is not an Australian 10-character format');
          $errorsProvided[] = 'owner[mobile]';
        }
      } elseif (getenv('REGION', true) === 'nz') {
        // Mobile length is 11
        if (!empty($data['lead_owner']['mobile']) AND strlen($data['lead_owner']['mobile']) > 11) {
          if (!$this->errorAlreadyProvided('owner[mobile]', $errors))
            $errors[] = array('table' => $apiKey, 'field' => '[mobile]', 'error'=>'Mobile is not a valid New Zealand format');
          $errorsProvided[] = 'owner[mobile]';
        }
      }
      // Check Phone
      if (getenv('REGION', true) === 'au') {
        // Phone length is 10
        if (!empty($data['lead_owner']['phone']) AND strlen($data['lead_owner']['phone'])!=10) {
          if (!$this->errorAlreadyProvided('owner[phone]', $errors))
            $errors[] = array('table' => $apiKey, 'field' => '[phone]', 'error'=>'Phone is not an Australian 10-character format');
          $errorsProvided[] = 'owner[phone]';
        }
      } elseif (getenv('REGION', true) === 'nz') {
        // Phone length is 9
        if (!empty($data['lead_owner']['phone']) AND strlen($data['lead_owner']['phone']) != 9) {
          if (!$this->errorAlreadyProvided('owner[phone]', $errors))
            $errors[] = array('table' => $apiKey, 'field' => '[phone]', 'error'=>'Phone is not a valid New Zealand format');
          $errorsProvided[] = 'owner[phone]';
        }
      }

      // DOB is not valid date format
      if (!empty($data['lead_owner']['dob']) AND !$this->isValidYYYYMMDD($data['lead_owner']['dob']) ) {
        if (!$this->errorAlreadyProvided('owner[dob]', $errors))
          $errors[] = array('table' => $apiKey, 'field' => '[dob]', 'error'=>'DOB date is not the accepted format (YYYY-MM-DD)');
        $errorsProvided[] = 'owner[dob]';
      }

      //driving_licence_expiry is not valid date format
      if (!empty($data['lead_owner']['driving_licence_expiry']) AND !$this->isValidYYYYMMDD($data['lead_owner']['driving_licence_expiry']) ) {
        if (!$this->errorAlreadyProvided('owner[driving_licence_expiry]', $errors))
          $errors[] = array('table' => $apiKey, 'field' => '[driving_licence_expiry]', 'error'=>'Driving Licence Expiry date is not the accepted format (YYYY-MM-DD)');
        $errorsProvided[] = 'owner[driving_licence_expiry]';
      }

      //driving_licence_state must in the ENUM
      if(!empty($data['lead_owner']['driving_licence_state']) AND !in_array($data['lead_owner']['driving_licence_state'], Configure::read('Lend.AU_states'))){
        if (!$this->errorAlreadyProvided('owner[driving_licence_state]', $errors))
          $errors[] = array('table' => $apiKey, 'field' => '[driving_licence_state]', 'error'=>'Driving Licence State must be in Australia');
        $errorsProvided[] = 'owner[driving_licence_state]';
      }

      //credit_history must in the ENUM
      if(!empty($data['lead_owner']['credit_history']) AND !in_array($data['lead_owner']['credit_history'], Configure::read('Lend.credit_history'))){
        if (!$this->errorAlreadyProvided('owner[credit_history]', $errors))
          $errors[] = array('table' => $apiKey, 'field' => '[credit_history]', 'error'=>'Invalid Credit History option provided');
        $errorsProvided[] = 'owner[credit_history]';
      }

      //lead_owner state must in the ENUM
      if(!empty($data['lead_owner']['state']) AND !in_array($data['lead_owner']['state'], Configure::read('Lend.AU_states'))){
        if (!$this->errorAlreadyProvided('owner[state]', $errors))
          $errors[] = array('table' => $apiKey, 'field' => '[state]', 'error'=>'Personal Address State must be in Australia');
        $errorsProvided[] = 'owner[state]';
      }
    }

    // Amount requested more than 0
    if (!empty($data['lead']['amount_requested']) AND (float)$data['lead']['amount_requested'] < 1 ) {
      if (!$this->errorAlreadyProvided('lead[amount_requested]', $errors))
        $errors[] = array('table' => 'lead', 'field' => '[amount_requested]', 'error'=>'Loan Amount Requested must be more than 0');
      $errorsProvided[] = 'lead[amount_requested]';
    }

    // Sales monthly must be more than 0
    if (array_key_exists('sales_monthly', $data['lead']) AND (float)$data['lead']['sales_monthly'] < 1 AND in_array('sales_monthly', $this->requiredFields['lead'])) {
      if (!$this->errorAlreadyProvided('lead[sales_monthly]', $errors))
        $errors[] = array('table' => 'lead', 'field' => '[sales_monthly]', 'error'=>'Must be more than 0');
      $errorsProvided[] = 'lead[sales_monthly]';
    }

    // force_send must be 0 or 1
    if (array_key_exists('force_send', $data['lead']) AND !in_array($data['lead']['force_send'], ['0','1'])  ) {
      if (!$this->errorAlreadyProvided('lead[force_send]', $errors))
        $errors[] = array('table' => 'lead', 'field' => '[force_send]', 'error'=>'Force Send must be 0 or 1');
      $errorsProvided[] = 'lead[force_send]';
    }

    // send_type must be Auto or Manual
    if (array_key_exists('send_type', $data['lead']) AND !in_array($data['lead']['send_type'], ['Auto','Manual'])  ) {
      if (!$this->errorAlreadyProvided('lead[send_type]', $errors))
        $errors[] = array('table' => 'lead', 'field' => '[send_type]', 'error'=>'Send Type must be Auto or Manual');
      $errorsProvided[] = 'lead[send_type]';
    }

    // Trading since not valid date format
    // If trading since is split up
    if (!empty($data['lead']['company_registration_date']) AND is_array($data['lead']['company_registration_date'])){
      if(!empty($data['lead']['company_registration_date']['yyyy'])){
        $data['lead']['company_registration_date'] = (!empty($data['lead']['company_registration_date']['yyyy'])?$data['lead']['company_registration_date']['yyyy']:'0000').'-'.
                                          (!empty($data['lead']['company_registration_date']['mm'])?$data['lead']['company_registration_date']['mm']:'00').'-'.
                                          (!empty($data['lead']['company_registration_date']['dd'])?$data['lead']['company_registration_date']['dd']:'01');
      }else{
        unset($data['lead']['company_registration_date']);
      }
    }
    if (!empty($data['lead']['company_registration_date']) AND !$this->isValidYYYYMMDD($data['lead']['company_registration_date']) ) {
      if (!$this->errorAlreadyProvided('lead[company_registration_date]', $errors))
        //$errors[] = array('table' => 'lead', 'field' => '[company_registration_date]', 'error'=>'Company Registration Date is not the accepted format (YYYY-MM-DD)');
        $errors[] = array('table' => 'lead', 'field' => '[company_registration_date]', 'error'=>'This is required');

      $errorsProvided[] = 'lead[company_registration_date]';
    }

    //who_to_contact field from API
    if (!empty($data['lead']['who_to_contact']) AND !in_array(ucwords($data['lead']['who_to_contact']), ['Broker', 'Client'])) {
      if (!$this->errorAlreadyProvided('lead[who_to_contact]', $errors))
        $errors[] = array('table' => 'lead', 'field' => '[who_to_contact]', 'error'=>'Who To Contact must be either Broker or Client');
      $errorsProvided[] = 'lead[who_to_contact]';
    }

    //lead b_state must in the ENUM
    if(!empty($data['lead']['b_state']) AND !in_array($data['lead']['b_state'], Configure::read('Lend.AU_states'))){
      if (!$this->errorAlreadyProvided('lead[b_state]', $errors))
        $errors[] = array('table' => 'lead', 'field' => '[b_state]', 'error'=>'Business Address State must be in Australia');
      $errorsProvided[] = 'lead[b_state]';
    }

    // Fields that need to be numeric
    $numericFields = array(
      'lead_owner'=>array('postcode','owner_type','equity'),
      'lead'=>array('abn','amount_requested','purpose_id','industry_id','b_postcode','sales_monthly', 'overdraft_loc_limit')
    );
    foreach ($numericFields as $table => $fields):
      $apiKey = $table;
      if ($table==='lead_owner') $apiKey = 'owner';
      if ($table === 'lead_owner' AND $this->check_all_owners) {
        foreach ($data['lead_owner'] as $key => $owner) {
          foreach ($fields as $field):
            if (!empty($owner[$field])
                      && !is_numeric($owner[$field])
                      &&  !$this->errorAlreadyProvided($apiKey.'['.$field.']', $errors)) {
              $errors[] = array('table' => $apiKey, 'field' => $apiKey.'['.$field.']',
                      'error'=>'This value is invalid. It should be a numeric value.', 'specific'=>$owner['first_name']);
            }
          endforeach;
        }
      } else {
        foreach ($fields as $field):
          if (!empty($data[$table][$field]) && $field == 'abn'
                && !$this->errorAlreadyProvided($apiKey.'['.$field.']', $errors)) {
              //ABN validation
              if (!$this->validABN($data[$table][$field])) {
                $errors[] = array('table' => $apiKey, 'field' => $apiKey.'['. (getenv('REGION', true) === 'nz' ? 'nzbn' : 'abn') .']',
                      'error'=>'This value is invalid');
              }
          } elseif (!empty($data[$table][$field])
                    && !is_numeric($data[$table][$field])
                    &&  !$this->errorAlreadyProvided($apiKey.'['.$field.']', $errors)) {
            $errors[] = array('table' => $apiKey, 'field' => $apiKey.'['.$field.']',
                    'error'=>'This value is invalid. It should be a numeric value.');
          }
        endforeach;
      }
    endforeach;

    if(!empty($this->requiredFields)) {
      foreach ($this->requiredFields as $table => $fields) {
        $apiKey = $table;

          if ($table === 'lead_owner') {
            $apiKey = 'owner';
          } elseif ($table === 'abnlookup') {
            $apiKey = 'Business Details';
          } elseif ($table === 'lead_asset_finance') {
            $apiKey = 'Asset Finance';
          }
          if ($table === 'abnlookup' && ( (empty($data['abnlookup']['abn'])
            && empty($data['lead']['abn'])
            && empty($data['lead']['is_abn_unknown']))
            || (!empty($data['lead']['is_abn_unknown'])
            && empty($data['lead']['organisation_name']))))
          {
            $errors[] = array('table' => $apiKey, 'field' => $apiKey, 'error'=>$data['lead']['abn'].'This is required');
          }

          if ($table === 'lead_owner' AND $this->check_all_owners) {
            foreach ($fields as $field) {
              foreach ($data['lead_owner'] as $key => $owner) {
                if (empty( $owner[$field] ) AND !$this->errorAlreadyProvided('['.$field.']', $errors)) {
                  Log::error($owner);
                  $custom_field = $field;
                  if ($field === 'lead_owner_addresses') {
                    $custom_field = 'address';
                  } elseif ($field === 'lead_owner_employments') {
                    $custom_field = 'employment_history';
                  }
                  $errors[] = array('table' => $apiKey, 'field' => '['.$custom_field.']', 'error'=>'This is required', 'specific'=>$owner['first_name']);
                } elseif ($field === 'lead_owner_addresses') {
                  $check_fields = [
                    'address',
                    'suburb',
                    'state',
                    'postcode',
                  ];
                  $total_months = 0;
                  foreach ($owner[$field] as $address) {
                    Log::error($address);
                    foreach ($check_fields as $check_field) {
                      if (empty($address[$check_field])) {
                        $errors[] = array('table' => $apiKey, 'field' => '['.$check_field.']', 'error' => 'This is required', 'specific'=>$owner['first_name']);
                      }
                    }
                    // Special check:
                    if (empty($address['date_from'])) {
                      $errors[] = array('table' => $apiKey, 'field' => 'residential_period', 'error' => 'This is required', 'specific'=>$owner['first_name']);
                    } else {
                      $date_from = (empty($address['date_from']) ? Chronos::now() : Chronos::createFromFormat('Y-m-d', date('Y-m-d', strtotime($address['date_from']))));
                      $date_to = (empty($address['date_to']) ? Chronos::now() : Chronos::createFromFormat('Y-m-d', date('Y-m-d', strtotime($address['date_to']))));
                      $total_months += $date_from->diffInMonths($date_to);
                    }
                  }
                  if ($total_months < 36) {
                    $errors[] = array('table' => $apiKey, 'field' => 'residential_period', 'error' => 'This should be more than 3 years', 'specific'=>$owner['first_name']);
                    break;
                  }
                } elseif ($field === 'lead_owner_employments') {
                  $check_fields = [
                    'previous_occupation',
                    'employer',
                    'employment_type',
                    'previous_contact_phone',
                  ];
                  $total_months = 0;
                  foreach ($owner[$field] as $employment) {
                    foreach ($check_fields as $check_field) {
                      if (empty($employment[$check_field])) {
                        $errors[] = array('table' => $apiKey, 'field' => '['.str_replace('previous_', 'employment_', $check_field).']', 'error' => 'This is required', 'specific'=>$owner['first_name']);
                      }
                    }
                    // Special check:
                    if (empty($employment['date_from'])) {
                      $errors[] = array('table' => $apiKey, 'field' => 'employment_period', 'error' => 'This is required', 'specific'=>$owner['first_name']);
                    } else {
                      $date_from = (empty($employment['date_from']) ? Chronos::now() : Chronos::createFromFormat('Y-m-d', date('Y-m-d', strtotime($employment['date_from']))));
                      $date_to = (empty($employment['date_to']) ? Chronos::now() : Chronos::createFromFormat('Y-m-d', date('Y-m-d', strtotime($employment['date_to']))));
                      $total_months += $date_from->diffInMonths($date_to);
                    }
                  }
                  if ($total_months < 36) {
                    $errors[] = array('table' => $apiKey, 'field' => 'employment_period', 'error' => 'This should be more than 3 years', 'specific'=>$owner['first_name']);
                    break;
                  }
                }
              }
            }
          } else {
            foreach ($fields as $field) {
              if ($table === 'abnlookup') {
                if ($field === 'entity_type_desc' && empty($data['abnlookup']['entity_type_desc'])) {
                  $errors[] = array('table' => $apiKey, 'field' => '['.$field.']', 'error'=>'This is required');
                }
              }
              if ($table === 'lead' && $field === 'company_registration_date'
                  && (empty($data[$table][$field]))
                  && !$this->errorAlreadyProvided('['.$field.'][yyyy]', $errors)) {
                $errors[] = array('table' => $apiKey, 'field' => '['.$field.'][yyyy]', 'error'=>'This is required');
              } elseif (in_array($table, ['lead_addresses'])) {
                if (empty($data[$table])) {
                  $errors[] = array('table' => $apiKey, 'field' => 'Trading Address History', 'error' => 'This is required');
                  break;
                } else {
                  $addr_months = 0;
                  foreach($data[$table] as $address) {
                    if (empty($address[$field])) {
                      $errors[] = array('table' => $apiKey, 'field' => 'Trading Address History', 'error' => 'This is required');
                      break 2;
                    }
                    if (empty($address['date_from'])) {
                      $errors[] = array('table' => $apiKey, 'field' => 'Trading Address History', 'error' => 'This is required');
                      break 2;
                    } else {
                      $date_from = (empty($address['date_from']) ? Chronos::now() : Chronos::createFromFormat('Y-m-d', date('Y-m-d', strtotime($address['date_from']))));
                      $date_to = (empty($address['date_to']) ? Chronos::now() : Chronos::createFromFormat('Y-m-d', date('Y-m-d', strtotime($address['date_to']))));
                      $addr_months += $date_from->diffInMonths($date_to);
                    }
                  }
                  // if ($addr_months < 36) {
                  //   $errors[] = array('table' => $apiKey, 'field' => 'Trading Address History', 'error' => 'This should be more than 3 years');
                  //   break;
                  // }
                }
              } elseif (in_array($table, ['lead_mailing_addresses'])) {
                if (empty($data[$table])) {
                  $errors[] = array('table' => $apiKey, 'field' => 'Mailing Address', 'error' => 'This is required');
                  break;
                } else {
                  $addr_months = 0;
                  foreach($data[$table] as $address) {
                    if (empty($address[$field])) {
                      $errors[] = array('table' => $apiKey, 'field' => 'Mailing Address', 'error' => 'This is required');
                      break 2;
                    }
                  }
                }
              } else {
                if ($table === 'lead_owner') {
                  $poc_owner = $data['lead_owner'][array_search('1', array_column($data['lead_owner'], 'point_of_contact'))];
                  if (!empty($poc_owner)) { // IF POC Exists then the lead is made and $data['lead_owner'] will always be an array of objects
                    if (!$poc_owner[$field] AND !$this->errorAlreadyProvided('['.$field.']', $errors)) {
                      $errors[] = array('table' => $apiKey, 'field' => '['.$field.']', 'error'=>'This is required');
                      // Log::error(array('table' => $apiKey, 'field' => '['.$field.']', 'error'=>'This is required'));
                    }  
                  } else { // IF no POC its a brand new lead being made
                    if (!$data['lead_owner'][$field] AND !$this->errorAlreadyProvided('['.$field.']', $errors)) {
                      $errors[] = array('table' => $apiKey, 'field' => '['.$field.']', 'error'=>'This is required');
                      // Log::error(array('table' => $apiKey, 'field' => '['.$field.']', 'error'=>'This is required'));
                    }  
                  }
                }
                if ($table === 'lead') {
                  if (!$data['lead'][$field]  AND !$this->errorAlreadyProvided('['.$field.']', $errors)) {
                    if ($field === 'b_address') {
                      $errors[] = array('table' => $apiKey, 'field' => 'Trading Address History', 'error'=>'is required');
                    } elseif (in_array($field, ['r_address','r_suburb','r_state','r_postcode']) AND !$this->errorAlreadyProvided('Registered Address', $errors)) {
                      $errors[] = array('table' => $apiKey, 'field' => 'Registered Address', 'error'=>'is required');
                    } else {
                      $errors[] = array('table' => $apiKey, 'field' => '['.$field.']', 'error'=>'This is required');
                    }
                  }
                }
              }
            }
          }
      }
    }

    $futureDatesAreInvalid = array(
      'lead_owner'=>array('dob'),
      'lead'=>array('company_registration_date')
    );
    foreach ($futureDatesAreInvalid as $table => $fields) {
      $apiKey = $table;
      if ($table==='lead_owner') $apiKey = 'owner';
      if ($table === 'lead_owner' AND $this->check_all_owners) {
        foreach ($data['lead_owner'] as $key => $owner) {
          foreach ($fields as $field):
            if (!empty($owner[$field]) AND (strtotime($owner[$field]) > strtotime(date('Y-m-d', time())))) {
              if (!$this->errorAlreadyProvided($apiKey.'['.$field.']', $errors))
                $errors[] = array('table' => $apiKey, 'field' => '['.$field.']', 'error'=>'Invalid Date - The date is in the future.', 'specific'=>$owner['first_name']);
              $errorsProvided[] = $apiKey.'['.$field.']';
            }
          endforeach;
        }
      } else {
        foreach ($fields as $field):
          if (!empty($data[$table][$field]) AND (strtotime($data[$table][$field]) > strtotime(date('Y-m-d', time())))) {
            if (!$this->errorAlreadyProvided($apiKey.'['.$field.']', $errors))
              $errors[] = array('table' => $apiKey, 'field' => '['.$field.']', 'error'=>'Invalid Date - The date is in the future.');
            $errorsProvided[] = $apiKey.'['.$field.']';
          }
        endforeach;
      }
    }

    // Check if some of the ID's provided are valid too:

    if (!empty($data['lead']['industry_id']) AND !$this->isAnAcceptedId('industry_id', $data['lead']['industry_id'])) {
      if (!$this->errorAlreadyProvided('lead[industry_id]', $errors))
        $errors[] = array('table' => 'lead', 'field' => '[industry_id]', 'error'=>'Industry ID provided is not an accepted ID');
      $errorsProvided[] = 'lead[industry_id]';
    }

    if (!empty($data['lead']['purpose_id']) AND !$this->isAnAcceptedId('purpose_id', $data['lead']['purpose_id'])) {
      if (!$this->errorAlreadyProvided('lead[purpose_id]', $errors))
        $errors[] = array('table' => 'lead', 'field' => '[purpose_id]', 'error'=>'Purpose ID provided is not an accepted ID');
      $errorsProvided[] = 'lead[purpose_id]';
    }

    if (!$fromPartnerAPI AND !empty($data['lead']['product_type_id']) AND !empty($this->requiredFields['lead']) AND in_array('product_type_id', $this->requiredFields['lead'])) {
      list($success, $field, $error) = $this->loanTypeCheck($data);
      if (!$success){
        if(!$this->errorAlreadyProvided($field, $errors))
          $errors[] = array('table' => 'lead', 'field' => $field, 'error'=>$error);
        $errorsProvided[] = $field;
      }
    }

    if (!empty($data['lead']['loan_term_requested']) AND !$this->isAnAcceptedId('loan_term_id', $data['lead']['loan_term_requested'])) {
      if (!$this->errorAlreadyProvided('lead[loan_term_requested]', $errors))
        $errors[] = array('table' => 'lead', 'field' => '[loan_term_requested]', 'error'=>'Loan Term provided is not an accepted ID');
      $errorsProvided[] = 'lead[loan_term_requested]';
    }

    // Check Owner Details - extra validation for API (1 owner only)
    if (!empty($data['lead_owner']) && !$this->check_all_owners) {
      $ownerErrors = $this->validateOwnerDetail($data['lead_owner']);
      $this->pushClassicErrorsToAPIErrorList($ownerErrors, 'lead_owner', $errors, $errorsProvided);
    }

    // Check Home Loan Details if present
    if (!empty($data['lead_home_loan_detail'])) {
      $homeDetailErrors = $this->validateHomeLoanDetail($data['lead_home_loan_detail']);
      $this->pushClassicErrorsToAPIErrorList($homeDetailErrors, 'lead_home_loan_detail', $errors, $errorsProvided);
    }

    // Check Owner Addresses if present
    if (!empty($data['lead_owner_addresses'])) {
      foreach ($data['lead_owner_addresses'] as $key => $address) {
        $ownerAddressErrors = $this->validateOwnerAddress($address);
        $this->pushClassicErrorsToAPIErrorList($ownerAddressErrors, 'lead_owner_addresses['.$key.']', $errors, $errorsProvided);
      }
    }

    // Check Home Loan Property Details if present
    if (!empty($data['lead_home_loan_property'])) {
      $propertyDetailErrors = $this->validateHomeLoanProperty($data['lead_home_loan_property']);
      $this->pushClassicErrorsToAPIErrorList($propertyDetailErrors, 'lead_home_loan_property', $errors, $errorsProvided);
    }

    return $errors;
  }
  
  /**
   * Takes an array of errors where the key is the field name and the value is the error message,
   * and pushes it to the API error list format.
   *
   * @param array $newErrors The array of errors to be pushed into the API error format
   * @param string $tableName The table name to be specified in the API error list
   * @param array &$existErrors The existing API error list
   * @param array &$errorsProvided The list of errors that have already been provided
   */
  protected function pushClassicErrorsToAPIErrorList($newErrors, $tableName, &$existErrors, &$errorsProvided)
  {
    if (empty($newErrors)) return;
    foreach ($newErrors as $field => $msg) {
      $errorKey = $tableName . '[' . $field . ']';
      if (!in_array($errorKey, $errorsProvided)) {
        $existErrors[] = array('table' => $tableName, 'field' => '[' . $field . ']', 'error' => $msg);
        $errorsProvided[] = $errorKey;
      }
    }
  }

  
  /**
   * Validates the owner details to ensure that the provided data is acceptable.
   *
   * @param array $data The data to be validated containing details of the owner.
   * @return array An array of errors found during validation, if any.
   *
   * This function performs the following checks:
   * - Enum Fields: Validates fields such as title, gender, marital status, driving licence type, 
   *   credit history, security type, and residency status against predefined values.
   * - Unsigned Numbers: Ensures that fields like retire age and amount requested contain 
   *   non-negative numbers.
   * - Set Fields: Validates fields that should contain valid IDs for retired pay off options.
   * - Country-Specific Validation: Validates the state field
   **/
  protected function validateOwnerDetail($data)
  {
    $errors = [];
    if (!$data) return $errors;

    // settings for fields
    $enumFields = [
      'title' => ['Mrs', 'Miss', 'Mr', 'Ms'],
      'gender' => ['Female', 'Male'],
      'marital_status' => ['Single', 'Married', 'De facto'],
      'driving_licence_type' => ['Full', 'Provisional', 'Learner', 'Heavy Vehicle', 'None', 'Overseas'],
      'credit_history' => Configure::read('Lend.credit_history'),
      'security_type' => ['Property (Residential)', 'Property (Commercial)', 'Vehicle', 'Equipment', 'Other'],
      'residency_status' => ['Citizen', 'Permanent Resident', 'Visa'],
    ];
    $unsignNumberFields = ['retire_age', 'amount_requested'];

    $setFields = [
      'retired_pay_off_options' => TableRegistry::getTableLocator()->get('HomeLoanPayOffOptions')->find('all')->select(['id'])->extract('id')->toArray(),
    ];

    // Check State Data if the country is AU
    if ($data['country'] === 'AU') {
      $enumFields['state'] = Configure::read('Lend.AU_states');
    }

    // Check enum fields
    $this->validateEnumFields($enumFields, $data, $errors);

    // Check unsigned numbers
    $this->validateUnsignedNumberFields($unsignNumberFields, $data, $errors);

    // Check set fields
    $this->validateSetDataFields($setFields, $data, $errors);
    return $errors;
  }

  protected function validateOwnerAddress($data) 
  {
    $errors = [];
    if (!$data) return $errors;

    // settings for fields
    $enumFields = [
      'address_type' => ['residential','mailing','settlement_post'],
      'living_status' => ['Owned Outright','Mortgaged','Renting','Boarding','Living with parents','Other']
    ];

    // Check State Data if the country is AU
    if($data['country'] === 'AU') {
      $enumFields['state'] = Configure::read('Lend.AU_states');
    }

    // Check enum fields
    $this->validateEnumFields($enumFields, $data, $errors);

    return $errors;
  }

  /**
   * Validate Home Loan details to ensure that the provided data is acceptable.
   *
   * @param array $data The data to be validated, containing details of the home loan.
   * @return array An array of errors found during validation, if any.
   *
   * This function performs the following checks:
   * - Enum Fields: Validates fields that should contain predefined values such as property found status, 
   *   property use, property type, development type, and rent frequency.
   * - Unsigned Numbers: Ensures that fields like loan amount, deposit, LVR, and loan term contain 
   *   non-negative numbers.
   * - Set Fields: Validates fields that should contain valid IDs for refinance reasons and loan features.
   */
  protected function validateHomeLoanDetail($data)
  {
    $errors = [];
    if (!$data) return $errors;

    // settings for fields
    $enumFields = [
      'property_found' => ['Yes', 'Still looking'],
      'property_use' => ['Live In', 'Investment'],
      'property_type' => ['Detached House', 'Semi-detached House', 'Terrace', 'Townhouse', 'Villa', 'Apartment/Unit', 'Vacant Land', 'Other'],
      'development_type' => ['Established', 'New', 'Off The Plan', 'House & Land Package', 'Owner Builder'],
      'rent_frequency' => ['Weekly', 'Fortnightly', 'Monthly'],
      'loan_term_year' => [5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30]
    ];
    $unsignNumberFields = ['loan_amount', 'deposit', 'lvr', 'loan_term_year'];
    $setFields = [
      'refinance_reasons' => TableRegistry::getTableLocator()->get('HomeLoanRefinanceReasons')->find('all')->select(['id'])->extract('id')->toArray(),
      'loan_features' => TableRegistry::getTableLocator()->get('HomeLoanFeatures')->find('all')->select(['id'])->extract('id')->toArray(),
    ];


    // Check enum fields
    $this->validateEnumFields($enumFields, $data, $errors);

    // Check unsigned numbers
    $this->validateUnsignedNumberFields($unsignNumberFields, $data, $errors);

    // Check ID Array or Set Fields
    $this->validateSetDataFields($setFields, $data, $errors);

    return $errors;
  }

  
  /**
   * Validate Home loan property, Makesure the provided data is acceptable
   * @param array $data The data to be validated
   * @return array The errors found
   */
  protected function validateHomeLoanProperty($data)
  {
    $errors = [];
    if (!$data) return $errors;

    // Check enum fields
    $enumFields = [
      'repayment_type' => ['Principle & Interest', 'Interest Only', 'Unsure'],
      'rate_type' => ['Variable', 'Fixed', 'Split', 'Unsure'],
      'repayment_frequency' => ['Monthly', 'Fortnightly', 'Weekly', 'Unsure'],
    ];
    $unsignNumberFields = ['estimated_value'];

    // Check State Data if the country is AU
    if($data['country'] === 'AU'){
      $enumFields['state'] = Configure::read('Lend.AU_states');
    }

    $this->validateEnumFields($enumFields, $data, $errors);

    // Check unsigned numbers
    $this->validateUnsignedNumberFields($unsignNumberFields, $data, $errors);

    return $errors;
  }

  
  /**
   * Validates the length of fields in the provided data against specified settings.
   *
   * @param array $fieldSettings An associative array where keys are field names and values are arrays with 'min' and 'max' length constraints.
   * @param array $data The data to be validated, with field names as keys.
   * @param array &$errors A reference to an array where error messages will be stored if validation fails.
   * @return array An empty array if validation passes, otherwise adds error messages to the $errors array.
   */
  protected function validateFieldsLength ($fieldSettings, $data, &$errors)
  {
    if ($fieldSettings || $data) return  [];
    foreach ($fieldSettings as $key => $length) {
      if (strlen($data[$key]) > $length['max']) {
        $errors[$key] = 'Field length should not exceed ' . $length . ' characters';
      } elseif (strlen($data[$key]) < $length['min']) {
        $errors[$key] = 'Field length should not be less than ' . $length . ' characters';
      }
    }
  }

  /**
   * Validates the values of the fields in the provided data against specified settings.
   *
   * @param array $fieldSettings An associative array where keys are field names and values are arrays with accepted values.
   * @param array $data The data to be validated, with field names as keys.
   * @param array &$errors A reference to an array where error messages will be stored if validation fails.
   * @return array An empty array if validation passes, otherwise adds error messages to the $errors array.
   */
  protected function validateSetDataFields ($fieldSettings, $data, &$errors)
  {
    if (!$fieldSettings || !$data) return  [];

    foreach ($fieldSettings as $key => $values) {
      $lowerCaseValues = array_map('strtolower', $values);
      if (!empty($data[$key])) {
        foreach ($data[$key] as $item) {
          // Check if the value is valid, using lower case to avoid case sensitivity
          if(!in_array(strtolower($item), $lowerCaseValues)){
            $errors[$key] = "`$key` contains invalid value. Accepted values are: `" . implode('`, `', $values) . '`';
          }
        }
      }
    }
  }


  /**
   * Validates the values of the fields in the provided data against specified settings.
   *
   * @param array $fieldSettings An associative array where keys are field names and values are arrays with accepted values.
   * @param array $data The data to be validated, with field names as keys.
   * @param array &$errors A reference to an array where error messages will be stored if validation fails.
   * @return array An empty array if validation passes, otherwise adds error messages to the $errors array.
   */
  protected function validateEnumFields($fieldSettings, $data, &$errors)
  {
    if (!$fieldSettings || !$data) return [];

    foreach ($fieldSettings as $key => $values) {
      $lowerCaseValues = array_map('strtolower', $values);
      // Check if the value is valid, using lower case to avoid case sensitivity
      if (!empty($data[$key]) && !in_array(strtolower($data[$key]), $lowerCaseValues)) {
        $errors[$key] = "Invalid Value for `$key`. Accepted values are: `" . implode('`, `', $values) . '`';
      }
    }
  }

  
  /**
   * Validates the values of the fields in the provided data against specified settings.
   *
   * @param array $fieldSettings An array of field names.
   * @param array $data The data to be validated, with field names as keys.
   * @param array &$errors A reference to an array where error messages will be stored if validation fails.
   * @return array An empty array if validation passes, otherwise adds error messages to the $errors array.
   */
  protected function validateUnsignedNumberFields($fieldSettings, $data, &$errors)
  {
    if (!$fieldSettings || !$data) return  [];
    foreach ($fieldSettings as $key) {
      if (!empty($data[$key]) && ($data[$key] < 0 || !is_numeric($data[$key]))) {
        $errors[$key] = "`$key` should be a non-negative number";
      }
    }
  }


  function validateDocumentation($requiredDocs, $lead, $selProposal, $uploads){


      $productType = !empty($lead['quote']['product_type_id']) ? $lead['quote']['product_type_id'] : $lead['lead']['product_type_id'];
      $leadBusinessType = (!empty($lead['lead']['business_type_abn'])) ? $lead['lead']['business_type_abn'] : null;
      if(!empty($selProposal)) {
          $proposalProduct = (!empty($selProposal['lender_product_id'])) ? $selProposal['lender_product_id'] : null;
      }else{
          $proposalProduct = null;
      }

      foreach ($requiredDocs AS $key => $doc){
          $count  = 0;
          if((int)($productType) === (int)($doc['product_type_id']) &&
              $this->checkEntityType($leadBusinessType, json_decode($doc['entity_type'], true)) &&
              $this->checkAssetProduct($proposalProduct, $doc['assset_calc_product_id'])){
              $docList = json_decode($doc['required_docs'], true);
              $required = $docList;

              foreach ($uploads as $upload) {
                  foreach ($upload['meta'] as $meta) {
                      if(isset($meta['field_name']) && $meta['field_name'] === 'specified'){
                          if(array_search($meta['value'], $required) !== false){
                              $count ++;
                              $id = array_search($meta['value'], $required);
                              if ($id > -1) {
                                  unset($required[$id]);
                              }
                          }
                      }
                  }

              }
              return $required;
          }
          else {
              continue;
          }
      }

  }

  private function checkAssetProduct($proposalProduct, $assetCalcProductId){
      if(empty($proposalProduct) && empty($assetCalcProductId))
          return true;

      if((int)$proposalProduct === (int)$assetCalcProductId && ((int)$assetCalcProductId !== 0))
          return true;

      return false;
  }

  private function checkEntityType($leadBusinessType, $docEntityType): bool
  {

      if(empty($docEntityType))
          return true;

      foreach ($docEntityType AS $key => $entity){
          if($entity === $leadBusinessType){
              return true;
          }
      }
      return false;
  }

  private function loanTypeCheck($data){
    $max_amount = 99999999;
    $max_invoice = 127;
    $this->leadsRepository = TableRegistry::get('Leads');
    $product_type_id = $data['lead']['product_type_id'];

    if($product_type_id == '2'){
      if($this->check_all_owners){
        foreach ($data['lead_owner'] as $key => $owner) {
          if(empty($owner['security_type']))
            return array(false,'owner[security_type]', 'Security Type is not provided');
          elseif(!in_array($owner['security_type'], $this->leadsRepository->getEnums('lead_owners', 'security_type')))
            return array(false,'owner[security_type]', 'Security Type provided is not valid');
          if($owner['security_type'] == 'Other'){
            if(empty($owner['security_type_detail']))
              return array(false,'owner[security_type_detail]', 'Security Type Details is not provided');
          }
          if(empty($owner['estimated_value']))
            return array(false,'owner[estimated_value]', 'Estimated Value is not provided');
          elseif(intval($owner['estimated_value']) > $max_amount)
            return array(false,'owner[estimated_value]', 'Estimated Value Maximum amount is $99,999,999');
          if(empty($owner['remaining_debt']))
            return array(false,'owner[remaining_debt]', 'Remaining Debt is not provided');
          elseif(intval($owner['remaining_debt']) > $max_amount)
            return array(false,'owner[remaining_debt]', 'Remaining Debt Maximum amount is $99,999,999');
        }
      }else{
        if(empty($data['lead_owner'][0]['security_type']))
          return array(false,'owner[security_type]', 'Security Type is not provided');
        elseif(!in_array($data['lead_owner'][0]['security_type'], $this->leadsRepository->getEnums('lead_owners', 'security_type')))
          return array(false,'owner[security_type]', 'Security Type provided is not valid');
        if($data['lead_owner'][0]['security_type'] == 'Other'){
          if(empty($data['lead_owner'][0]['security_type_detail']))
            return array(false,'owner[security_type_detail]', 'Security Type Details is not provided');
        }
        if(empty($data['lead_owner'][0]['estimated_value']))
          return array(false,'owner[estimated_value]', 'Estimated Value is not provided');
        elseif(intval($data['lead_owner'][0]['estimated_value']) > $max_amount)
          return array(false,'owner[estimated_value]', 'Estimated Value Maximum amount is $99,999,999');
        if(empty($data['lead_owner'][0]['remaining_debt']))
          return array(false,'owner[remaining_debt]', 'Remaining Debt is not provided');
        elseif(intval($data['lead_owner'][0]['remaining_debt']) > $max_amount)
          return array(false,'owner[remaining_debt]', 'Remaining Debt Maximum amount is $99,999,999');
      }
    }
    elseif($product_type_id == '6'){
      if(empty($data['lead']['smsf_property_type']))
        return array(false,'lead[smsf_property_type]', 'SMSF Property Type is not provided');
      elseif(!in_array($data['lead']['smsf_property_type'], $this->leadsRepository->getEnums('leads', 'smsf_property_type')))
        return array(false,'lead[smsf_property_type]', 'SMSF Property Type provided is not valid');
      if(empty($data['lead']['smsf_property_postcode']))
        return array(false,'lead[smsf_property_postcode]', 'SMSF Property Postcode is not provided');
      elseif(strlen((string)$data['lead']['smsf_property_postcode']) != 4)
        return array(false,'lead[smsf_property_postcode]', 'SMSF Property Postcode provided is not valid');
      if(empty($data['lead']['smsf_property_balance']))
        return array(false,'lead[smsf_property_balance]', 'SMSF Property Balance is not provided');
      elseif(intval($data['lead']['smsf_property_balance']) > $max_amount)
        return array(false,'lead[smsf_property_balance]', 'SMSF Property Balance Maximum amount is $99,999,999');
    }
    elseif($product_type_id == '15' OR $product_type_id == '16' OR $product_type_id == '17' OR $product_type_id == '18'){
      if($this->check_all_owners){
        foreach ($data['lead_owner'] as $key => $owner) {
          if(empty($owner['security_type']))
            return array(false,'owner[security_type]', 'Security Type is not provided');
          elseif(!in_array($owner['security_type'], $this->leadsRepository->getEnums('lead_owners', 'security_type')))
            return array(false,'owner[security_type]', 'Security Type provided is not valid');
          if($owner['security_type'] == 'Other'){
            if(empty($owner['security_type_detail']))
              return array(false,'owner[security_type_detail]', 'Security Type Details is not provided');
          }
          if(empty($owner['estimated_value']))
            return array(false,'owner[estimated_value]', 'Estimated Value is not provided');
          elseif(intval($owner['estimated_value']) > $max_amount)
            return array(false,'owner[estimated_value]', 'Estimated Value Maximum amount is $99,999,999');
          if(empty($owner['remaining_debt']))
            return array(false,'owner[remaining_debt]', 'Remaining Debt is not provided');
          elseif(intval($owner['remaining_debt']) > $max_amount)
            return array(false,'owner[remaining_debt]', 'Remaining Debt Maximum amount is $99,999,999');
        }
      }else{
        if(empty($data['lead_owner'][0]['security_type']))
          return array(false,'owner[security_type]', 'Security Type is not provided');
        elseif(!in_array($data['lead_owner'][0]['security_type'], $this->leadsRepository->getEnums('lead_owners', 'security_type')))
          return array(false,'owner[security_type]', 'Security Type provided is not valid');
        if($data['lead_owner'][0]['security_type'] == 'Other'){
          if(empty($data['lead_owner'][0]['security_type_detail']))
            return array(false,'owner[security_type_detail]', 'Security Type Details is not provided');
        }
        if(empty($data['lead_owner'][0]['estimated_value']))
          return array(false,'owner[estimated_value]', 'Estimated Value is not provided');
        elseif(intval($data['lead_owner'][0]['estimated_value']) > $max_amount)
          return array(false,'owner[estimated_value]', 'Estimated Value Maximum amount is $99,999,999');
        if(empty($data['lead_owner'][0]['remaining_debt']))
          return array(false,'owner[remaining_debt]', 'Remaining Debt is not provided');
        elseif(intval($data['lead_owner'][0]['remaining_debt']) > $max_amount)
          return array(false,'owner[remaining_debt]', 'Remaining Debt Maximum amount is $99,999,999');
      }
      if(empty($data['lead']['private_lending_purpose_id']))
        return array(false,'lead[private_lending_purpose_id]', 'Private Lending Purpose is not provided');
      elseif(!$this->isAnAcceptedId('private_lending_purpose_id', $data['lead']['private_lending_purpose_id']))
        return array(false,'lead[private_lending_purpose_id]', 'Private Lending Purpose provided is not valid');
    }
    elseif($product_type_id == '8'){
      if(empty($data['lead']['invoice_amount_outstanding']))
        return array(false,'lead[invoice_amount_outstanding]', 'Invoice Amount Outstanding is not provided');
      elseif(intval($data['lead']['invoice_amount_outstanding']) > $max_amount)
        return array(false,'lead[invoice_amount_outstanding]', 'Invoice Amount Outstanding Maximum amount is $99,999,999');
      if(empty($data['lead']['number_of_invoices']))
        return array(false,'lead[number_of_invoices]', 'Number of Invoices is not provided');
      elseif(intval($data['lead']['number_of_invoices']) > $max_invoice)
        return array(false,'lead[number_of_invoices]', 'Number of Invoices Maximum amount is 127');
      if(empty($data['lead']['books_package_is']))
        return array(false,'lead[books_package_is]', 'Books Package is not provided');
      elseif(!$this->isAnAcceptedId('books_package_id', $data['lead']['books_package_is']))
        return array(false,'lead[books_package_is]', 'Books Package provided is not valid');
      if((string)$data['lead']['books_package_is'] == '4'){
        if(empty($data['lead']['books_package_detail']))
          return array(false,'lead[books_package_detail]', 'Books Package Details is not provided');
      }
    }
    elseif($product_type_id == '10'){
      if(empty($data['lead']['equipment_id']))
        return array(false,'lead[equipment_id]', 'Equipment ID is not provided');
      elseif(!$this->isAnAcceptedId('equipment_id', $data['lead']['equipment_id']))
        return array(false,'lead[equipment_id]', 'Equipment ID provided is not valid');
      if((string)$data['lead']['equipment_id'] == '7'){
        if(empty($data['lead']['equipment_details']))
          return array(false,'lead[equipment_details]', 'Equipment Details is not provided');
      }
      if(!isset($data['lead']['equipment_found']))
        return array(false,'lead[equipment_found]', 'Equipment Found is not provided');
      if($data['lead']['equipment_found']){
        if(empty($data['lead']['equipment_source']))
          return array(false,'lead[equipment_source]', 'Equipment Source is not provided');
        elseif(!in_array($data['lead']['equipment_source'], $this->leadsRepository->getEnums('leads', 'equipment_source')))
          return array(false,'lead[equipment_source]', 'Equipment Source provided is not valid');
      }
    }
    return array(true,null,null);
  }


  private function isAnAcceptedId($idType, $idGiven) {
    switch ($idType) {
      case 'industry_id':  $ids = $this->DB->execute('SELECT industry_id FROM config_industries'); break;
      case 'purpose_id':   $ids = $this->DB->execute('SELECT purpose_id FROM frm_purpose WHERE status = 1'); break;
      case 'loan_term_id': $ids = $this->DB->execute('SELECT loan_term_id FROM frm_loan_terms WHERE status = 1'); break;
      case 'private_lending_purpose_id': $ids = $this->DB->execute('SELECT purpose_id FROM frm_private_lending_purpose WHERE status = 1');$idType='purpose_id'; break;
      case 'books_package_id': $ids = $this->DB->execute('SELECT books_package_id FROM frm_books_package WHERE STATUS = 1'); break;
      case 'equipment_id': $ids = $this->DB->execute('SELECT equipment_id FROM frm_equipment WHERE STATUS = 1'); break;
    }

    if (empty($ids)) return true;

    $validIds = array();
    foreach ($ids as $id) $validIds[] = (string)$id[$idType];

    return in_array((string)$idGiven, $validIds);
  }


  private function isValidYYYYMMDD($date) {
    $format = 'Y-m-d';
    $dateString = date($format, strtotime($date));
    $dateTime = DateTime::createFromFormat($format, $dateString);
    return $dateTime && $dateTime->format($format) === $dateString;
  }

  private function validABN($abn)
  {
    $valid = false;
    $abn = preg_replace('/[^\d]/', '', $abn); //remove all other characters except digits
    
    $bn_digit = 11;
    if (getenv('REGION', true) === 'nz') {
    $bn_digit = 13;
    }

    if (strlen($abn) === $bn_digit) {
      //see rules @https://abr.business.gov.au/Help/AbnFormat
      if (getenv('REGION', true) === 'au') {
        $sum = 0;
        $weightingFactors = [10, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19];
        foreach($weightingFactors as $pos => $weighting) {
          $digit = $abn[$pos] - ($pos ? 0 : 1); // first digit needs to minus 1, otherwise keep same as it as
          $sum += $digit * $weighting;
        }
        $valid = ($sum > 0 && ($sum % 89 == 0)) ? true : false;
      } elseif (getenv('REGION', true) === 'nz') {
        $valid = true;
      }
    }
    return $valid;
  }

  private function errorAlreadyProvided($field, $errors) {
    foreach ($errors as $error)
      if ($field===$error['field'])
        return true;
    return false;
  }

  public function onlyShowTheseFields($payload, $toRemove=array()) {
    foreach ($payload as $table => $fields):
      foreach ($fields as $field => $value):
        if (!in_array($field, $toRemove[$table]))
          unset($payload[$table][$field]);
      endforeach;
    endforeach;
    return $payload;
  }

  public function validateAssetFinance($data, $header){

    $checks = Configure::read('Lend.Asset_Finance_Data_Structure');
    $this->leadsRepository = TableRegistry::get('Leads');

    // Step 1: check header: asset_finance_details
    if(!array_key_exists($header, $data)){
      $error =  $header." - This field is required";
      return array(false,$error);
    }
    else if(count($data) > 1){
      $error = array_keys($data)[1]." - This field is unnecessary";
      return array(false,$error);
    }

    // Step 2: check mandatory fields
    foreach ($checks['mandatory_fields'] as $key => $value) {
      if(!array_key_exists($value, $data[$header])){
        $error =  $value." - This field is required";
        return array(false,$error);
      }
      if($value == 'people'){
        if(!is_array($data[$header][$value])){
          $error = $value." - Invalid field type";
          return array(false,$error);
        }
        foreach ($data[$header][$value] as $key1 => $value1) {
          foreach ($checks['lead_owner_mandatory'] as $key2 => $value2) {
            if(!array_key_exists($value2, $value1)){
              $error =  $value2." - This field is required";
              return array(false,$error);
            }
          }
          foreach ($checks['lead_owner_optional'] as $key2 => $value2) {
            if(array_key_exists($value2, $value1)){
              if($value2 == 'owner_type_other_detail'){
                if(!is_string($value1[$value2]) OR strlen($value1[$value2]) > 255){
                  $error = $value2." - Invalid field type";
                  return array(false,$error);
                }
              }
              if($value2 == 'marital_status'){
                if(!in_array($value1[$value2], $this->leadsRepository->getEnums('lead_owners', 'marital_status'))){
                  $error = $value2." - Invalid field type";
                  return array(false,$error);
                }
              }
              if($value2 == 'is_guarantor'){
                if(!is_numeric($value1[$value2]) OR strlen((string)$value1[$value2]) > 1){
                  $error = $value2." - Invalid field type";
                  return array(false,$error);
                }
              }
              if($value2 == 'class_of_beneficiary'){
                if(!is_string($value1[$value2]) OR strlen($value1[$value2]) > 255){
                  $error = $value2." - Invalid field type";
                  return array(false,$error);
                }
              }
              if($value2 == 'directorship_start_date'){
                if(!$this->leadsRepository->isValidTimeStamp($value1[$value2])){
                  $error = $value2." - Invalid field type";
                  return array(false,$error);
                }
              }
              if($value2 == 'number_of_dependants'){
                if(!is_string($value1[$value2]) AND !is_numeric($value1[$value2])){
                  $error = $value2." - Invalid field type";
                  return array(false,$error);
                }
              }
              if($value2 == 'dependant_details'){
                if(!is_string($value1[$value2])){$error = $value2." - Invalid field type";
                  return array(false,$error);
                }
              }
              if($value2 == 'driving_licence_type'){
                if(!in_array($value1[$value2], $this->leadsRepository->getEnums('lead_owners', 'driving_licence_type'))){
                  $error = $value2." - Invalid field type";
                  return array(false,$error);
                }
              }
            }
          }
          if(array_key_exists('employment', $value1 )){
            foreach ($value1['employment'] as $key2 => $value2) {
              foreach ($checks['lead_owner_employment_mandatory'] as $key3 => $value3) {
                if(!array_key_exists($value3, $value2)){
                  $error =  $value3." - This field is required";
                  return array(false,$error);
                }
                if($value3 == 'employer'){
                  if(!is_string($value2[$value3]) OR strlen($value2[$value3]) > 255){
                    $error = $value3." - Invalid field type";
                    return array(false,$error);
                  }
                }
                if($value3 == 'employment_type'){
                  if(!in_array($value2[$value3], $this->leadsRepository->getEnums('lead_owner_employment', 'employment_type'))){
                    $error = $value3." - Invalid field type";
                    return array(false,$error);
                  }
                }
                if($value3 == 'date_from'){
                  if(!$this->leadsRepository->isValidTimeStamp($value2[$value3])){
                    $error = "Invalid ".$value3." parameter, should be a unix timestamp";
                    return array(false,$error);
                  }
                }
              }
              foreach ($checks['lead_owner_employment_optional'] as $key3 => $value3) {
                if(array_key_exists($value3, $value2)){
                  if($value3 == 'previous_occupation'){
                    if(!is_string($value2[$value3]) OR strlen($value2[$value3]) > 255){
                      $error = $value3." - Invalid field type";
                      return array(false,$error);
                    }
                  }
                  if($value3 == 'date_to'){
                    if(!$this->leadsRepository->isValidTimeStamp($value2[$value3])){
                      $error = "Invalid ".$value3." parameter, should be a unix timestamp";
                      return array(false,$error);
                    }
                  }
                }
              }
            }
          }
          if(array_key_exists('addresses', $value1)){
            foreach ($value1['addresses'] as $key2 => $value2) {
              foreach ($checks['lead_owner_address_mandatory'] as $key3 => $value3) {
                if(!array_key_exists($value3, $value2)){
                  $error =  $value3." - This field is required";
                  return array(false,$error);
                }
                if($value3 == 'living_status'){
                  if(!in_array($value2[$value3], $this->leadsRepository->getEnums('lead_owner_addresses', 'living_status'))){
                    $error = $value3." - Invalid field type";
                    return array(false,$error);
                  }
                }
                if($value3 == 'address'){
                  if(!is_string($value2[$value3]) OR strlen($value2[$value3]) > 100){
                    $error = $value3." - Invalid field type";
                    return array(false,$error);
                  }
                }
                if($value3 == 'suburb'){
                  if(!is_string($value2[$value3]) OR strlen($value2[$value3]) > 50){
                    $error = $value3." - Invalid field type";
                    return array(false,$error);
                  }
                }
                if($value3 == 'state'){
                  if(!in_array($value2[$value3], $this->leadsRepository->getEnums('lead_owner_addresses', 'state'))){
                    $error = $value3." - Invalid field type";
                    return array(false,$error);
                  }
                }
                if($value3 == 'postcode'){
                  if(!is_string($value2[$value3]) OR strlen($value2[$value3]) > 4){
                    $error = $value3." - Invalid field type";
                    return array(false,$error);
                  }
                }
                if($value3 == 'country'){
                  if(!is_string($value2[$value3]) OR strlen($value2[$value3]) > 2){
                    $error = $value3." - Invalid field type";
                    return array(false,$error);
                  }
                }
                if($value3 == 'date_from'){
                  if(!$this->leadsRepository->isValidTimeStamp($value2[$value3])){
                    $error = "Invalid ".$value3." parameter, should be a unix timestamp";
                    return array(false,$error);
                  }
                }
              }
              foreach ($checks['lead_owner_address_optional'] as $key3 => $value3) {
                if(array_key_exists($value3, $value2)){
                  if($value3 == 'living_status_other'){
                    if(!is_string($value2[$value3]) OR strlen($value2[$value3]) > 255){
                      $error = $value3." - Invalid field type";
                      return array(false,$error);
                    }
                  }
                  if($value3 == 'landlord_name'){
                    if(!is_string($value2[$value3]) OR strlen($value2[$value3]) > 255){
                      $error = $value3." - Invalid field type";
                      return array(false,$error);
                    }
                  }
                  if($value3 == 'landlord_contact_number'){
                    if(!is_string($value2[$value3]) AND !is_numeric($value2[$value3]) OR strlen((string)$value2[$value3]) > 10){
                      $error = $value3." - Invalid field type";
                      return array(false,$error);
                    }
                  }
                  if($value3 == 'date_to'){
                    if(!$this->leadsRepository->isValidTimeStamp($value2[$value3])){
                      $error = "Invalid ".$value3." parameter, should be a unix timestamp";
                      return array(false,$error);
                    }
                  }
                }
              }
            }
          }
        }
      }
      unset($data[$header][$value]);
    }

    // Step 3: check optional fields
    foreach ($checks['optional_fields'] as $key => $value) {
      if($value == 'business_credit_history'){
        if(array_key_exists($value, $data[$header])){
          if(!in_array($data[$header][$value], $this->leadsRepository->getEnums('leads', 'business_credit_history'))){
            $error = $value." - Invalid field type";
            return array(false,$error);
          }
        }
      }
      else{
        if(array_key_exists($value, $data[$header])){
          if($value == 'asset_finance'){
            foreach ($checks['lead_asset_finance_mandatory'] as $key1 => $value1) {
              if(!array_key_exists($value1, $data[$header][$value])){
                $error =  $value1." - This field is required";
                return array(false,$error);
              }
              if($value1 == 'equipment'){
                if(!is_array($this->leadsRepository->getequipmentID($data[$header][$value][$value1]))){
                  $error = $value1." - Invalid field type";
                  return array(false,$error);
                }
              }
              if($value1 == 'condition' OR $value1 == 'reason_for_purchase'){
                if(!in_array($data[$header][$value][$value1], $this->leadsRepository->getEnums('lead_asset_finance', $value1))){
                  $error = $value1." - Invalid field type";
                  return array(false,$error);
                }
              }
              if($value1 == 'asset_description'){
                if(!is_string($data[$header][$value][$value1]) OR strlen($data[$header][$value][$value1]) > 255){
                  $error = $value1." - Invalid field type";
                  return array(false,$error);
                }
              }
              if($value1 == 'year'){
                if(!is_string($data[$header][$value][$value1]) OR strlen($data[$header][$value][$value1]) > 4){
                  $error = $value1." - Invalid field type";
                  return array(false,$error);
                }
              }
              if($value1 == 'make' OR $value1 == 'model'){
                if(!is_string($data[$header][$value][$value1]) OR strlen($data[$header][$value][$value1]) > 100){
                  $error = $value1." - Invalid field type";
                  return array(false,$error);
                }
              }
            }
            foreach ($checks['lead_asset_finance_optional'] as $key1 => $value1) {
              if(array_key_exists($value1, $data[$header][$value])){
                if($value1 == 'reason_description' OR $value1 == 'supplier' OR $value1 == 'supplier_address'){
                  if(!is_string($data[$header][$value][$value1]) OR strlen($data[$header][$value][$value1]) > 255){
                    $error = $value1." - Invalid field type";
                    return array(false,$error);
                  }
                }
                if($value1 == 'supplier_suburb'){
                  if(!is_string($data[$header][$value][$value1]) OR strlen($data[$header][$value][$value1]) > 50){
                    $error = $value1." - Invalid field type";
                    return array(false,$error);
                  }
                }
                if($value1 == 'supplier_postcode'){
                  if(!is_string($data[$header][$value][$value1]) OR strlen($data[$header][$value][$value1]) > 4){
                    $error = $value1." - Invalid field type";
                    return array(false,$error);
                  }
                }
                if($value1 == 'supplier_country'){
                  if(!is_string($data[$header][$value][$value1]) OR strlen($data[$header][$value][$value1]) > 2){
                    $error = $value1." - Invalid field type";
                    return array(false,$error);
                  }
                }
                if($value1 == 'supplier_state' OR $value1 == 'supplier_type'){
                  if(!in_array($data[$header][$value][$value1], $this->leadsRepository->getEnums('lead_asset_finance', $value1))){
                    $error = $value1." - Invalid field type";
                    return array(false,$error);
                  }
                }
                if($value1 == 'supplier_found'){
                  if(!is_numeric($data[$header][$value][$value1]) OR strlen((string)$data[$header][$value][$value1]) > 1){
                    $error = $value1." - Invalid field type";
                    return array(false,$error);
                  }
                }
                if($value1 == 'asset_purchase_price' OR $value1 == 'asset_deposit' OR $value1 == 'asset_tradein' OR $value1 == 'asset_tradein_value' OR $value1 == 'asset_tradein_debt' OR $value1 == 'asset_balloon'){
                  if(!is_string($data[$header][$value][$value1]) AND !is_numeric($data[$header][$value][$value1])){
                    $error = $value1." - Invalid field type";
                    return array(false,$error);
                  }
                }
              }
            }
          }
          else{
            if(!is_array($data[$header][$value])){
              $error = $value." - Invalid field type";
              return array(false,$error);
            }
            if($value == 'references'){
              foreach ($data[$header][$value] as $key1 => $value1) {
                foreach ($checks['lead_reference_mandatory'] as $key2 => $value2) {
                  if(!array_key_exists($value2, $value1)){
                    $error =  $value2." - This field is required";
                    return array(false,$error);
                  }
                  if($value2 == 'reference_type' OR $value2 == 'state'){
                    if(!in_array($value1[$value2], $this->leadsRepository->getEnums('lead_references', $value2))){
                      $error = $value2." - Invalid field type";
                      return array(false,$error);
                    }
                  }
                  if($value2 == 'company_name' OR $value2 == 'email' OR $value2 == 'address'){
                    if(!is_string($value1[$value2]) OR strlen($value1[$value2]) > 255){
                      $error = $value2." - Invalid field type";
                      return array(false,$error);
                    }
                  }
                  if($value2 == 'full_name'){
                    if(!is_string($value1[$value2]) OR strlen($value1[$value2]) > 100){
                      $error = $value2." - Invalid field type";
                      return array(false,$error);
                    }
                  }
                  if($value2 == 'contact_number'){
                    if(!is_string($value1[$value2]) OR strlen($value1[$value2]) > 10){
                      $error = $value2." - Invalid field type";
                      return array(false,$error);
                    }
                  }
                  if($value2 == 'suburb'){
                    if(!is_string($value1[$value2]) OR strlen($value1[$value2]) > 50){
                      $error = $value2." - Invalid field type";
                      return array(false,$error);
                    }
                  }
                  if($value2 == 'postcode'){
                    if(!is_string($value1[$value2]) OR strlen($value1[$value2]) > 4){
                      $error = $value2." - Invalid field type";
                      return array(false,$error);
                    }
                  }
                  if($value2 == 'country'){
                    if(!is_string($value1[$value2]) OR strlen($value1[$value2]) > 2){
                      $error = $value2." - Invalid field type";
                      return array(false,$error);
                    }
                  }
                }
                foreach ($checks['lead_reference_optional'] as $key2 => $value2) {
                  if(array_key_exists($value2, $value1)){
                    if($value2 == 'financier' OR $value2 == 'financier_acc_num'){
                      if(!is_string($value1[$value2]) OR strlen($value1[$value2]) > 50){
                        $error = $value2." - Invalid field type";
                        return array(false,$error);
                      }
                    }
                  }
                }
              }
            }
            if($value == 'assets'){
              foreach ($data[$header][$value] as $key1 => $value1) {
                foreach ($checks['lead_assets_mandatory'] as $key2 => $value2) {
                  $asset_type_id = false;
                  $sub_mandatory = [];
                  $sub_optional = [];
                  if(!array_key_exists($value2, $value1)){
                    $error =  $value2." - This field is required";
                    return array(false,$error);
                  }
                  if($value2 == 'asset_type'){
                    if(!in_array($value1[$value2], $this->leadsRepository->getEnums('config_asset_types', $value2))){
                      $error = $value2." - Invalid field type";
                      return array(false,$error);
                    }
                  }
                  if($value2 == 'asset_type_name'){
                    $asset_type_id = $this->leadsRepository->getAssetTypeID($value1['asset_type'], $value1[$value2]);
                    if(!is_array($asset_type_id)){
                      $error = $value2." - Invalid field type";
                      return array(false,$error);
                    }
                  }
                  if(is_array($asset_type_id)){
                    switch ($asset_type_id['asset_type_id']) {
                      case 1: //Property
                      array_push($sub_mandatory, 'value', 'finance_outstanding');
                      array_push($sub_optional, 'description');
                      break;
                      case 2: //Savings
                      array_push($sub_mandatory, 'value');
                      array_push($sub_optional, 'description');
                      break;
                      case 3: //Plant & Equipment
                      array_push($sub_mandatory, 'value', 'description', 'finance_outstanding');
                      break;
                      case 4: //Other
                      array_push($sub_mandatory, 'value', 'description');
                      break;
                      case 5: //Debtors
                      array_push($sub_mandatory, 'debtors_money_owed');
                      array_push($sub_optional, 'description');
                      break;
                      case 6: //Home
                      array_push($sub_mandatory, 'value', 'finance_outstanding');
                      array_push($sub_optional, 'description');
                      break;
                      case 7: //Investment Property
                      array_push($sub_mandatory, 'value', 'finance_outstanding');
                      array_push($sub_optional, 'description');
                      break;
                      case 8: //Savings
                      array_push($sub_mandatory, 'value');
                      array_push($sub_optional, 'description');
                      break;
                      case 9: //Cash
                      array_push($sub_mandatory, 'value');
                      array_push($sub_optional, 'description');
                      break;
                      case 10: //Term Deposit
                      array_push($sub_mandatory, 'value');
                      array_push($sub_optional, 'description');
                      break;
                      case 11: //Motor Vehicle
                      array_push($sub_mandatory, 'value', 'description', 'finance_outstanding');
                      break;
                      case 12: //Home Contents
                      array_push($sub_mandatory, 'value');
                      array_push($sub_optional, 'description');
                      break;
                      case 13: //Super
                      array_push($sub_mandatory, 'value');
                      array_push($sub_optional, 'description');
                      break;
                      case 14: //Shares
                      array_push($sub_mandatory, 'value');
                      array_push($sub_optional, 'description');
                      break;
                      case 15: //Plant & Equipment
                      array_push($sub_mandatory, 'value', 'description', 'finance_outstanding');
                      break;
                      case 16: //Other
                      array_push($sub_mandatory, 'value', 'description', 'finance_outstanding');
                      break;
                      case 17: //Recreational Asset
                      array_push($sub_mandatory, 'value', 'description', 'finance_outstanding');
                      break;
                    }
                  }
                }
                foreach ($sub_mandatory as $key3 => $value3) {
                  if(!array_key_exists($value3, $value1)){
                    $error =  $value3." - This field is required";
                    return array(false,$error);
                  }
                  if($value3 == 'finance_outstanding'){
                    if(!is_numeric($value1[$value3]) OR strlen((string)$value1[$value3]) > 1){
                      $error = $value3." - Invalid field type";
                      return array(false,$error);
                    }
                  }
                  if($value3 == 'debtors_money_owed'){
                    if(!is_string($value1[$value3]) AND !is_numeric($value1[$value3])){
                      $error = $value3." - Invalid field type";
                      return array(false,$error);
                    }
                  }
                  if($value3 == 'description'){
                    if(!is_string($value1[$value3]) OR strlen($value1[$value3]) > 255){
                      $error = $value3." - Invalid field type";
                      return array(false,$error);
                    }
                  }
                  if($value3 == 'value'){
                    if(!is_string($value1[$value3]) AND !is_numeric($value1[$value3])){
                      $error = $value3." - Invalid field type";
                      return array(false,$error);
                    }
                  }
                }
                foreach ($sub_optional as $key3 => $value3) {
                  if(array_key_exists($value3, $value1)){
                    if($value3 == 'description'){
                      if(!is_string($value1[$value3]) OR strlen($value1[$value3]) > 255){
                        $error = $value3." - Invalid field type";
                        return array(false,$error);
                      }
                    }
                  }
                }
              }
            }
            if($value == 'liabilities'){
              foreach ($data[$header][$value] as $key1 => $value1) {
                foreach ($checks['lead_liabilities_mandatory'] as $key2 => $value2) {
                  $liability_id = false;
                  $sub_mandatory = [];
                  $sub_optional = [];
                  if(!array_key_exists($value2, $value1)){
                    $error =  $value2." - This field is required";
                    return array(false,$error);
                  }
                  if($value2 == 'liability_type'){
                    if(!in_array($value1[$value2], $this->leadsRepository->getEnums('config_liabilities', $value2))){
                      $error = $value2." - Invalid field type";
                      return array(false,$error);
                    }
                  }
                  if($value2 == 'liability_name'){
                    $liability_id = $this->leadsRepository->getLiabilityID($value1['liability_type'], $value1[$value2]);
                    if(!is_array($liability_id)){
                      $error = $value2." - Invalid field type";
                      return array(false,$error);
                    }
                  }
                  if(is_array($liability_id)){
                    switch ($liability_id['liability_id']) {
                      case 1: //ATO Debt
                      array_push($sub_mandatory, 'loan_balance', 'ato_payplan_arranged', 'repayment_pm');
                      break;
                      case 2: //Loan
                      array_push($sub_mandatory, 'loan_balance', 'financier', 'repayment_pm', 'description');
                      break;
                      case 3: //OD/LOC
                      array_push($sub_mandatory, 'financier', 'limit', 'used');
                      break;
                      case 4: //Business CC
                      array_push($sub_mandatory, 'financier', 'limit', 'used');
                      break;
                      case 5: //Other
                      array_push($sub_mandatory, 'description', 'loan_balance', 'repayment_pm');
                      break;
                      case 6: //Creditors
                      array_push($sub_mandatory, 'loan_balance');
                      break;
                      case 7: //AssetFinance (single)
                      array_push($sub_mandatory, 'description', 'financier', 'loan_balance', 'asset_fi_to_pay_out', 'asset_fi_amount', 'asset_fi_commenced', 'asset_fi_term');
                      array_push($sub_optional, 'asset_fi_balloon');
                      break;
                      case 8: //AssetFinance (multiple)
                      array_push($sub_mandatory, 'financier', 'loan_balance', 'asset_fi_to_pay_out', 'asset_fi_amount', 'asset_fi_commenced', 'asset_fi_term', 'asset_fi_num_of_assets');
                      array_push($sub_optional, 'description', 'asset_fi_balloon');
                      break;
                      case 9: //Property Loan
                      array_push($sub_mandatory, 'description', 'loan_balance', 'financier', 'repayment_pm');
                      break;
                      case 10: //Credit Card
                      array_push($sub_mandatory, 'financier', 'limit', 'used', 'repayment_pm');
                      break;
                      case 11: //Personal Loan
                      array_push($sub_mandatory, 'description', 'financier', 'loan_balance', 'repayment_pm');
                      break;
                      case 12: //Other Loan
                      array_push($sub_mandatory, 'description', 'financier', 'loan_balance', 'repayment_pm');
                      break;
                      case 13: //Overdraft
                      array_push($sub_mandatory, 'financier', 'limit', 'used', 'repayment_pm');
                      break;
                      case 14: //Other
                      array_push($sub_mandatory, 'description', 'financier', 'loan_balance', 'repayment_pm');
                      break;
                      case 15: //Child Support
                      array_push($sub_mandatory, 'repayment_pm');
                      break;
                      case 16: //Tax Debt
                      array_push($sub_mandatory, 'description', 'financier', 'loan_balance', 'repayment_pm');
                      break;
                      case 17: //Home Loan
                      array_push($sub_mandatory, 'description', 'financier', 'loan_balance', 'repayment_pm');
                      break;
                      case 18: //Investment Property Loan
                      array_push($sub_mandatory, 'description', 'financier', 'loan_balance', 'repayment_pm');
                      case 19: //Motor Vehicle Loan
                      array_push($sub_mandatory, 'description', 'financier', 'loan_balance', 'repayment_pm', 'asset_fi_to_pay_out');
                      case 20: //Plant & Equipment Loan
                      array_push($sub_mandatory, 'description', 'financier', 'loan_balance', 'repayment_pm', 'asset_fi_to_pay_out');
                      case 21: //Recreational Asset Loan
                      array_push($sub_mandatory, 'financier', 'loan_balance', 'repayment_pm');
                      break;
                    }
                  }
                }
                foreach ($sub_mandatory as $key3 => $value3) {
                  if($value3 == 'description'){
                    if(!is_string($value1[$value3]) OR strlen($value1[$value3]) > 255){
                      $error = $value3." - Invalid field type";
                      return array(false,$error);
                    }
                  }
                  if($value3 == 'financier'){
                    if(!is_string($value1[$value3]) OR strlen($value1[$value3]) > 50){
                      $error = $value3." - Invalid field type";
                      return array(false,$error);
                    }
                  }
                  if($value3 == 'limit' OR $value3 == 'used' OR $value3 == 'loan_balance' OR $value3 == 'financier' OR $value3 == 'repayment_pm' OR $value3 == 'asset_fi_amount' OR $value3 == 'asset_fi_term' OR $value3 == 'asset_fi_balloon' OR $value3 == 'asset_fi_balance' OR $value3 == 'asset_fi_num_of_assets'){
                    if(!is_string($value1[$value3]) AND !is_numeric($value1[$value3])){
                      $error = $value3." - Invalid field type";
                      return array(false,$error);
                    }
                  }
                  if($value3 == 'asset_fi_to_pay_out' OR $value3 == 'ato_payplan_arranged'){
                    if(!is_numeric($value1[$value3]) OR strlen((string)$value1[$value3]) > 1){
                      $error = $value3." - Invalid field type";
                      return array(false,$error);
                    }
                  }
                  if($value3 == 'asset_fi_commenced'){
                    if(!$this->leadsRepository->isValidTimeStamp($value1[$value3])){
                      $error = "Invalid ".$value3." parameter, should be a unix timestamp";
                      return array(false,$error);
                    }
                  }
                }
                foreach ($sub_optional as $key3 => $value3) {
                  if(array_key_exists($value3, $value1)){
                    if($value3 == 'description'){
                      if(!is_string($value1[$value3]) OR strlen($value1[$value3]) > 255){
                        $error = $value3." - Invalid field type";
                        return array(false,$error);
                      }
                    }
                    if($value3 == 'asset_fi_balloon'){
                      if(!is_string($value1[$value3]) AND !is_numeric($value1[$value3])){
                        $error = $value3." - Invalid field type";
                        return array(false,$error);
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
      unset($data[$header][$value]);
    }

    // Step 4: check unnecessary fields exist
    if(count((array)($data[$header])) > 0){
      $error = (array_keys((array)($data[$header])))[0]." - Invalid field name";
      return array(false,$error);
    }

    return array(true,null);
  }

}