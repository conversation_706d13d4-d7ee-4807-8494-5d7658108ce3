<?php

namespace App\Lend;

use Exception;
use Cake\ORM\TableRegistry;
use Cake\Datasource\ConnectionManager;
use Cake\Database\Expression\QueryExpression;
use Cake\Database\Expression\TupleComparison;
use Cake\Database\Query;
use Cake\Log\Log;
use Cake\Core\Configure;

class KanbanHelper
{

    public static function moveLeadDataUpdated($leadRef, $oldManStatusId, $newManstatusId, $oldProductId, $newProductId, $noSocketEmits = false)
    {
        $lead_table = TableRegistry::getTableLocator()->get('LeadEntity');
        $lead = $lead_table->find()
            ->select(['lead_id', 'partner_id'])
            ->where(['lead_ref' => $leadRef])
            ->first();
        if (!$lead) {
            throw new Exception('Unable to find lead');
        }
        $manStatusGroupChanged = false;
        $swimlaneChanged = false;
        $manStatusGroupId = null;
        $manStatusTable = TableRegistry::getTableLocator()->get('ManStatusEntity');
        if ($newManstatusId) {
            $newManStatus = $manStatusTable->get($newManstatusId);
            $manStatusGroupId = $newManStatus->man_status_group_id;
            if (!$oldManStatusId)
                $manStatusGroupChanged = true;
            else {
                $oldManStatus = $manStatusTable->get($oldManStatusId);
                if ($oldManStatus->man_status_group_id !== $manStatusGroupId)
                    $manStatusGroupChanged = true;
            }
        }

        if ($newProductId) {
            if (!$oldProductId)
                $swimlaneChanged = true;
            else {
                $oldSwimlaneId = self::getSwimlaneId($lead->partner_id, $oldProductId);
                $newSwimlaneId = self::getSwimlaneId($lead->partner_id, $newProductId);
                if ($oldSwimlaneId !== $newSwimlaneId)
                    $swimlaneChanged = true;
            }
        }

        if (($manStatusGroupChanged === true) || ($swimlaneChanged === true)) {
            self::removeLead(null, $leadRef);
            self::addLead(null, $leadRef, null, null, $manStatusGroupId);
            //Remove and then re-add lead
            $oldManStatusGroupId = null;
            if($oldManStatusId){
                $oldManStatus = $manStatusTable->get($oldManStatusId);
                $oldManStatusGroupId = $oldManStatus->man_status_group_id;
            }
            
            $extraData = [
                'lead_ref' => $leadRef,
                'from_group_id' => $oldManStatusGroupId,
                'from_product_type_id' => $oldProductId
            ];
            if(!$noSocketEmits){
                SocketHelper::kanbanAddLead($lead->lead_id, null, 'lead_add_remove', $extraData);
            }
        }
        else{
            $filterValuesChanged = [];
            if($oldManStatusId !== $newManstatusId){
                $filterValuesChanged['status'] = ['old' => $oldManStatusId, 'new'=> $newManstatusId];
            }
            if($oldProductId !== $newProductId){
                $filterValuesChanged['product_type'] = ['old' => $oldProductId, 'new'=> $newProductId];
            }
            if(!$noSocketEmits){
                SocketHelper::kanbanUpdateLead($lead->lead_id, $filterValuesChanged);
            }
        }
    }

    /**
     * Move a lead
     */
    public static function moveLead($partnerUserId, $leadRef, $location = null, $relativeLeadRef = null, $manStatusGroupId = null, $addOtherUsers = false)
    {
        self::removeLead($addOtherUsers ? null : $partnerUserId, $leadRef);
        self::addLead($partnerUserId, $leadRef, $location, $relativeLeadRef, $manStatusGroupId, $addOtherUsers);
    }

    /**
     * Add a lead
     * If a partner user is not specified, this will add lead to top of the boards all users which have access to the kead
     * If a partner user is specified and location is not specified, it will only add to that users board (at top)
     * If a partner user is specified and location is specified, it will only add to that users board(at specified location)
     *
     * if $addOtherUsers is true and $partnerUserId is not null, lead will be added to top of other users boards
     */
    public static function addLead($partnerUserId = null, $leadRef, $location = null, $relativeLeadRef = null, $manStatusGroupId = null, $addOtherUsers = false)
    {
        try {
            $lead_table = TableRegistry::getTableLocator()->get('LeadEntity');
            $options = [];
            if (!$manStatusGroupId) {
                $contain = [
                    'ManStatusEntity' => ['fields' => ['man_status_group_id']]
                ];
                $options = ['contain' => $contain];
            }
            $lead = $lead_table->find('all', $options)->where(['LeadEntity.lead_ref' => $leadRef]);
            $lead->enableHydration(false);
            $lead = $lead->first();
            if(!in_array($lead['call_queue_status'], Configure::read('Lend.NOT_IN_CALL_QUEUE_STATUS'))){//value is invalid - ignore
                return;
            }
            $partner_user_table = TableRegistry::getTableLocator()->get('PartnerUsers');
            $partnerUsers = ($partnerUserId && !$addOtherUsers) ? [$partnerUserId] : SocketHelper::getLeadUsers($lead['lead_id'], true);
            $partnerUserEntity = $partner_user_table->get($partnerUsers[0]);
            $partnerId = $partnerUserEntity->partner_id;

            if (!$manStatusGroupId) {
                if ($lead['man_status'] && $lead['man_status']['man_status_group_id']) {
                    $manStatusGroupId = $lead['man_status']['man_status_group_id'];
                } else {
                    throw new Exception('Cannot determine man_status_group_id for lead ' . $leadRef);
                }
            }
            // dump($lead);
            // dump($lead['product_type_id']);
            //find the swimlane based on lead product_type
            $swimlaneId = self::getSwimlaneId($partnerId, $lead['product_type_id']);

            // dump($swimlane['kanban_swimlane_id']);die();
            //get the position Entity if it exists and update or create a new one
            $kanbanLeadPositionTable = TableRegistry::getTableLocator()->get('KanbanLeadPositionsEntity');
            $kanbanLeadPositionMapTable = TableRegistry::getTableLocator()->get('KanbanLeadPositionsMapEntity');

            foreach ($partnerUsers as $partnerUser) {
                $position = $kanbanLeadPositionTable->find()
                    ->where([
                        'partner_user_id' => $partnerUser,
                        'man_status_group_id' => $manStatusGroupId,
                        'kanban_swimlane_id' => $swimlaneId
                    ])->first();
                if ($position) {
                    $leads = $position->leads;//array
                    if(!in_array($leadRef, $leads)){//only add if not present
                        if (
                            (!$relativeLeadRef || !in_array($location, ['above', 'below']))// if no valid relative lead ref or position has been provided, add to top
                            || ($partnerUserId && $addOtherUsers && ($partnerUser !== $partnerUserId))//other users have to be added and this is other user
                        ) {
                            array_unshift($leads, $leadRef);
                        } else {
                            $referenceIndex = array_search($relativeLeadRef, $leads);
                            if ($referenceIndex === false) {//can't find the relative lead
                                array_unshift($leads, $leadRef);
                            } else {
                                array_splice($leads, $referenceIndex + (($location === 'above') ? 0 : 1), 0, $leadRef);
                            }
                        }
                        $position = $kanbanLeadPositionTable->patchEntity($position, ['leads' => $leads]);
                    }
                } else {
                    $position = $kanbanLeadPositionTable->newEntity([
                        'partner_user_id' => $partnerUser,
                        'man_status_group_id' => $manStatusGroupId,
                        'kanban_swimlane_id' => $swimlaneId,
                        'leads' => [$leadRef]
                    ]);
                }
                $kanbanLeadPositionTable->save($position);
                //duplicate check - unsure how it's happening, but strangely it is
                $existing = $kanbanLeadPositionMapTable->exists([
                    'kanban_lead_positions_id' => $position->id,
                    'lead_ref' => $leadRef
                ]);
                if (!$existing) {
                    $positionMap = $kanbanLeadPositionMapTable->newEntity([
                        'kanban_lead_positions_id' => $position->id,
                        'lead_ref' => $leadRef
                    ]);
                    $kanbanLeadPositionMapTable->save($positionMap);
                }
            }
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
        }
    }

    /**
     * remove a lead
     * if no $partnerUserId is provided, will delete leadRef from everywhere
     */
    public static function removeLead($partnerUserId = null, $leadRef, $retainAccessAllLeadsUsers = false)
    {
        $contain = ['KanbanLeadPositionsEntity'];
        if($retainAccessAllLeadsUsers){//don't remove if the oartner user specified has access to all leads
            if($partnerUserId){
                $partnerUserTable = TableRegistry::getTableLocator()->get('PartnerUserEntity');
                $partnerUser = $partnerUserTable->get($partnerUserId, ['fields' => ['partner_user_id', 'access_all_leads']]);
                if(!empty($partnerUser['access_all_leads'])){
                    return;
                }
            }
            else{
                $contain['KanbanLeadPositionsEntity.PartnerUserEntity'] = ['fields' => ['partner_user_id', 'access_all_leads']];
            }
        }

        $kanbanLeadPositionMapTable = TableRegistry::getTableLocator()->get('KanbanLeadPositionsMapEntity');
        $kanbanLeadPositionTable = TableRegistry::getTableLocator()->get('KanbanLeadPositionsEntity');
        $results = $kanbanLeadPositionMapTable->find()
            ->contain($contain)
            ->where(['KanbanLeadPositionsMapEntity.lead_ref' => $leadRef]);
        if ($partnerUserId) {
            $results->matching('KanbanLeadPositionsEntity', function ($q) use ($partnerUserId) {
                return $q->where(['KanbanLeadPositionsEntity.partner_user_id' => $partnerUserId]);
            });
        }
        else if($retainAccessAllLeadsUsers)//delete only for users who have access to all leads
        {
            $results->matching('KanbanLeadPositionsEntity.PartnerUserEntity', function ($q) {
                return $q->where(['PartnerUserEntity.access_all_leads' => 0]);
            });
        }

        $results = $results->all();
        foreach ($results as $result) {
            $position = $result['kanban_lead_position'];
            $leadsRemoved = array_filter($position->leads, function ($item) use ($leadRef) {
                return $item !== $leadRef;
            });
            $leadsRemoved = array_values($leadsRemoved);
            $position = $kanbanLeadPositionTable->patchEntity($position, ['leads' => $leadsRemoved]);
            $kanbanLeadPositionTable->save($position);
            $kanbanLeadPositionMapTable->delete($result);
        }
    }

    /**
     * Get position data for Kanban board
     */
    static public function getPositionData($partnerUserId, $partnerId, $leadsWhitelist = null, $manStatusGroupId = null, $swimlaneId = null, $combineSwimlanes = false)
    {
        $swimlaneNames = [];
        $swimlaneMap = [];
        $kanbanLeadPositionTable = TableRegistry::getTableLocator()->get('KanbanLeadPositionsEntity');
        $manStatusTable = TableRegistry::getTableLocator()->get('ManStatusEntity');
        $options = [];
        $contain = [
            'KanbanSwimlanesEntity' => ['fields' => ['id', 'name', 'order']],
            'ManStatusGroupEntity' => ['fields' => ['id', 'group_name', 'color', 'order']]
        ];
        $options = ['contain' => $contain];
        $kanbanRows = $kanbanLeadPositionTable->find('all', $options)
            ->where(['KanbanLeadPositionsEntity.partner_user_id' => $partnerUserId])
            ->order(['KanbanSwimlanesEntity.order', 'ManStatusGroupEntity.order']);
        if ($manStatusGroupId) {
            $kanbanRows->where(['KanbanLeadPositionsEntity.man_status_group_id' => $manStatusGroupId]);
        }
        if ($swimlaneId) {
            $kanbanRows->where(['KanbanLeadPositionsEntity.kanban_swimlane_id' => $swimlaneId]);
        }
        // $allLeadRefs = [];//for testing
        $allSwimlaneKey = "*";
        $swimlaneMap[$allSwimlaneKey] = [
            'groupIds' => [],
            'groupNames' => [],
            'groupMap' => [],
            'groupStatuses' => []
        ];
        //add all the man status groups and their statuses (PLAT-5848)
        $manStatusGroups = TableRegistry::getTableLocator()->get('ManStatusGroupEntity')
            ->find('all', ['contain' => [
                'ManStatusEntity' => ['fields' => ['id','man_status_group_id', 'order', 'status_name']],
            ]])
            ->select(['id', 'order', 'group_name', 'active'])
            ->order(['order'])
            ->where([
                'partner_id' => $partnerId,
                'ManStatusGroupEntity.active' => true,
            ]);
        $groups = [];
        foreach($manStatusGroups as $group){
            if(!is_array($group['man_status']) || count($group['man_status']) === 0){
                continue;
            }
            $groups['groupIds'][] = $group['id'];
            $groups['groupNames'][$group['id']] = $group['group_name'];
            // $groups['groupMap'][$group['id']] = [];
            $statuses = [];
            foreach($group['man_status'] as $status){
                $statuses[$status['order']] = [
                    'id' => $status['id'],
                    'order' => $status['order'],
                    'status_name' => $status['status_name'],
                ];
            }
            ksort($statuses);
            $groups['groupStatuses'][$group['id']] = array_values($statuses);
        }
        $swimlaneMap[$allSwimlaneKey] = $groups;

        foreach ($kanbanRows as $row) {
            Log::info('HERE4 $row: ' . json_encode($row));
            $swimlaneId = (string)$row['kanban_swimlane']['id'];
            if(!$swimlaneId || $combineSwimlanes){
                $swimlaneId = "_";
            }
            $swimlaneNames[$swimlaneId] = $row['kanban_swimlane']['name'];
            if (!isset($swimlaneMap[$swimlaneId])) {
                $swimlaneMap[$swimlaneId] = [
                    'groupIds' => [],
                    'groupNames' => [],
                    'groupMap' => [],
                    'groupStatuses' => []
                ];
            }
            $manStatusGroupId = (string)$row['man_status_group']['id'];
            if(!$swimlaneMap[$swimlaneId]['groupIds'] || !in_array($manStatusGroupId, $swimlaneMap[$swimlaneId]['groupIds'])){
                $swimlaneMap[$swimlaneId]['groupIds'][] = $manStatusGroupId;
            }
            // if(!$swimlaneMap[$allSwimlaneKey]['groupIds'] || !in_array($manStatusGroupId, $swimlaneMap[$allSwimlaneKey]['groupIds'])){
            //     $swimlaneMap[$allSwimlaneKey]['groupIds'][] = $manStatusGroupId;
            // }
            $swimlaneMap[$swimlaneId]['groupNames'][$manStatusGroupId] = $row['man_status_group']['group_name'];
            // $swimlaneMap[$allSwimlaneKey]['groupNames'][$manStatusGroupId] = $row['man_status_group']['group_name'];
            // $allLeadRefs = array_merge($allLeadRefs, array_values(array_intersect($row['leads'], $leadsWhitelist)));//for testing
            $leadRefs = $leadsWhitelist ? array_values(array_intersect($row['leads'], $leadsWhitelist)) : [];
            if($swimlaneId === "_"){
                $swimlaneMap[$swimlaneId]['groupMap'][$manStatusGroupId] = array_merge($swimlaneMap[$swimlaneId]['groupMap'][$manStatusGroupId] ?? [], $leadRefs);
            }
            else{
                $swimlaneMap[$swimlaneId]['groupMap'][$manStatusGroupId] = $leadRefs;
            }
            // $swimlaneMap[$swimlaneId]['groupMap'][$manStatusGroupId] = array_merge($swimlaneMap[$swimlaneId]['groupMap'][$manStatusGroupId] ?? [], $leadRefs);

            // $swimlaneMap[$allSwimlaneKey]['groupMap'][$manStatusGroupId] = array_merge($swimlaneMap[$allSwimlaneKey]['groupMap'][$manStatusGroupId] ?? [], $leadRefs);
            // if(!$swimlaneMap[$allSwimlaneKey]['groupStatuses'][$manStatusGroupId]){
            //     $groupStatuses = $manStatusTable->find('all', ['fields' => ['id', 'order', 'status_name']])
            // ->where(['active' => true, 'man_status_group_id' => $manStatusGroupId, 'partner_id' => $partnerId])->toArray();
            //     $swimlaneMap[$allSwimlaneKey]['groupStatuses'][$manStatusGroupId] = $groupStatuses;
            // }
            $swimlaneMap[$swimlaneId]['groupStatuses'][$manStatusGroupId] = $swimlaneMap[$allSwimlaneKey]['groupStatuses'][$manStatusGroupId];
        }
        return [
            'swimlaneIds' => array_keys($swimlaneNames),
            'swimlaneNames' => $swimlaneNames,
            'swimlaneMap' => $swimlaneMap,
            // 'allRefs'=> $allLeadRefs,//for testing
        ];
    }

    /**
     * find the swimlane based on lead product_type
     */
    //TO-DO need to redo this so it prechecks and get swinlanes (the easier way)
    static public function getSwimlaneId($partnerId, $productTypeId)
    {
        $swimlaneProductTypeTable = TableRegistry::getTableLocator()->get('KanbanSwimlaneProductTypesEntity');
        $swimlane = $swimlaneProductTypeTable->find()
            ->select(['kanban_swimlane_id'])
            ->where(['product_type_id' => $productTypeId])
            ->contain(['KanbanSwimlanesEntity'])
            ->matching('KanbanSwimlanesEntity', function ($q) use ($partnerId) {
                return $q->where(function (QueryExpression $exp) use ($partnerId) {
                    return $exp->or_([
                        'KanbanSwimlanesEntity.partner_id' => $partnerId,
                        'KanbanSwimlanesEntity.partner_id IS' => null
                    ]);
                });
            })
            ->order(['KanbanSwimlanesEntity.partner_id' => 'DESC']) // Prioritize rows with specified partner_id first
            ->first();
        return $swimlane['kanban_swimlane_id'];
    }


    static public function getSwimlaneProducts($swimlaneId)
    { //Need to test
        $swimlaneProductTypeTable = TableRegistry::getTableLocator()->get('KanbanSwimlaneProductTypesEntity');
        $qry = $swimlaneProductTypeTable->find()
            ->select(['product_type_id'])
            ->where(['kanban_swimlane_id' => $swimlaneId]);
        $productTypeIds = $qry->all()->toArray();
        return $productTypeIds;
    }

    /**
     * Get kanban lead data for position data creation
     */
    static private function getKanbanLeads($partnerUserId)
    {
        $partnerUserTable = TableRegistry::getTableLocator()->get('PartnerUserEntity');
        $kanbanSwimlanesTable = TableRegistry::getTableLocator()->get('KanbanSwimlanesEntity');
        $leadsTable = TableRegistry::getTableLocator()->get('LeadEntity');

        $partnerUser = $partnerUserTable->get($partnerUserId);
        $partnerId = $partnerUser->partner_id;
        $swimlaneCount = $kanbanSwimlanesTable->find()->where(['partner_id' => $partnerId])->count();
        $swimlaneConditions = ['KanbanSwimlanesEntity.partner_id IS' => null];
        if ($swimlaneCount > 0) {
            $swimlaneConditions = [
                'OR' => [
                    'KanbanSwimlanesEntity.id IS' => null,// no swimlane
                    'KanbanSwimlanesEntity.partner_id' => $partnerId //partner swimlane
                ]
            ];
        }
        // $hasFullAccess = $partnerUser->access_all_leads == 1 || $partnerUser->account_admin == 1;
        $hasFullAccess = $partnerUser->access_all_leads == 1;
        // $hasFullAccess = false;//for testing

        $query = $leadsTable->find()
            ->select([
                'LeadEntity.lead_id',
                'LeadEntity.lead_ref',
                'LeadEntity.product_type_id',
                'ManStatusEntity.man_status_group_id',
                'swimlane_id' => 'KanbanSwimlanesEntity.id'
            ])
            ->leftJoin(
                ['ManStatusEntity' => 'man_statuses'],
                [
                    'ManStatusEntity.id = LeadEntity.man_status_id',
                    'ManStatusEntity.active' => 1
                ]
            )
            ->leftJoin(
                ['PartnerProductTypeEntity' => 'partner_product_types'],
                [
                    'PartnerProductTypeEntity.product_type_id = LeadEntity.product_type_id'
                ]
            )
            ->leftJoin(
                ['KanbanSwimlaneProductTypesEntity' => 'kanban_swimlane_product_types'],
                [
                    'KanbanSwimlaneProductTypesEntity.product_type_id = PartnerProductTypeEntity.product_type_id'
                ]
            )
            ->leftJoin(
                ['KanbanSwimlanesEntity' => 'kanban_swimlanes'],
                [
                    'KanbanSwimlaneProductTypesEntity.kanban_swimlane_id = KanbanSwimlanesEntity.id'
                ]
            )
            ->where($swimlaneConditions)
            ->where(['LeadEntity.partner_id' => $partnerId, 'LeadEntity.man_status_id IS NOT NULL'])
            ->order(['LeadEntity.created' => 'DESC']);
        if (!$hasFullAccess) {
            $query->matching('PartnerUserLeadsEntity', function ($q) use ($partnerUserId) {
                return $q->where([
                    'PartnerUserLeadsEntity.partner_user_id' => $partnerUserId,
                    'PartnerUserLeadsEntity.status' => 'ACCESS'
                ]);
            });
        }
        // return $query->sql();
        $results = $query->all();
        return $results;
    }

    /**
     * Generate and save position data for a user
     */
    static public function createPositionData($partnerUserId)
    {
        if (!self::canPartnerUserAccessKanban($partnerUserId)) {
            return false;
        }

        $kanbanLeadPositionTable = TableRegistry::getTableLocator()->get('KanbanLeadPositionsEntity');
        $kanbanLeadPositionMapTable = TableRegistry::getTableLocator()->get('KanbanLeadPositionsMapEntity');

        //Wipe data for user
        $kanbanLeadPositionTable->deleteAll(['partner_user_id' => $partnerUserId]);

        $data = self::getKanbanLeads($partnerUserId);

        $kanbanLeadPositions = [];
        foreach ($data as $leadRecord) {
            $kanbanLeadPositions[$leadRecord['swimlane_id'] ?? 'NULL'][$leadRecord['ManStatusEntity']['man_status_group_id']][] = $leadRecord['lead_ref'];
        }
        $kanbanLeadPositionsData = [];
        $kanbanLeadPositionsMapData = [];

        foreach ($kanbanLeadPositions as $swimlaneId => $groupsData) {
            foreach ($groupsData as $manStatusGroupId => $leads) {
                $kanbanLeadPositionsData[] = [
                    'partner_user_id' => $partnerUserId,
                    'kanban_swimlane_id' => $swimlaneId,
                    'man_status_group_id' => $manStatusGroupId,
                    'leads' => $leads
                ];
            }
        }
        $kanbanLeadPositionsRows = $kanbanLeadPositionTable->newEntities($kanbanLeadPositionsData);
        $kanbanLeadPositionTable->saveMany($kanbanLeadPositionsRows);
        foreach ($kanbanLeadPositionsRows as $row) {
            foreach ($row->leads as $leadRef) {
                $kanbanLeadPositionsMapData[] = [
                    'kanban_lead_positions_id' => $row->id,
                    'lead_ref' => $leadRef,
                ];
            }
        }
        $kanbanLeadPositionMapRows = $kanbanLeadPositionMapTable->newEntities($kanbanLeadPositionsMapData);
        $kanbanLeadPositionMapTable->saveMany($kanbanLeadPositionMapRows);
        return true;
    }

    static public function manualStatusChangeGroup($manStatusId)
    {
        try {

            $manStatusTable = TableRegistry::getTableLocator()->get('ManStatusEntity');
            $manStatus = $manStatusTable->get($manStatusId);
            $manStatusGroupId = $manStatus->man_status_group_id;
            $partnerId = $manStatus->partner_id;
            if (!self::canPartnerAccessKanban($partnerId)) {
                return false;
            }
            //find all leads in this status, along with their kanban_swimlane_id (based on product_id)
            $kanbanSwimlanesTable = TableRegistry::getTableLocator()->get('KanbanSwimlanesEntity');
            $kanbanLeadPositionTable = TableRegistry::getTableLocator()->get('KanbanLeadPositionsEntity');
            $kanbanLeadPositionMapTable = TableRegistry::getTableLocator()->get('KanbanLeadPositionsMapEntity');

            $swimlaneCount = $kanbanSwimlanesTable->find()->where(['partner_id' => $partnerId])->count();
            $swimlaneConditions = ['KanbanSwimlanesEntity.partner_id IS' => null];
            if ($swimlaneCount > 0) {
                $swimlaneConditions = [
                    'OR' => [
                        'KanbanSwimlanesEntity.id IS' => null,// no swimlane
                        'KanbanSwimlanesEntity.partner_id' => $partnerId //partner swimlane
                    ]
                ];
            }

            $leadsTable = TableRegistry::getTableLocator()->get('LeadEntity');
            $query = $leadsTable->find()
                ->select([
                    'LeadEntity.lead_id',
                    'LeadEntity.lead_ref',
                    'LeadEntity.product_type_id',
                    'swimlane_id' => 'KanbanSwimlanesEntity.id'
                ])
                ->leftJoin(
                    ['PartnerProductTypeEntity' => 'partner_product_types'],
                    [
                        'PartnerProductTypeEntity.product_type_id = LeadEntity.product_type_id'
                    ]
                )
                ->leftJoin(
                    ['KanbanSwimlaneProductTypesEntity' => 'kanban_swimlane_product_types'],
                    [
                        'KanbanSwimlaneProductTypesEntity.product_type_id = PartnerProductTypeEntity.product_type_id'
                    ]
                )
                ->leftJoin(
                    ['KanbanSwimlanesEntity' => 'kanban_swimlanes'],
                    [
                        'KanbanSwimlaneProductTypesEntity.kanban_swimlane_id = KanbanSwimlanesEntity.id'
                    ]
                )
                ->where($swimlaneConditions)
                ->where([
                    'LeadEntity.partner_id' => $partnerId,
                    'LeadEntity.man_status_id' => $manStatusId
                ])
                ->order(['LeadEntity.created' => 'DESC']);

            $leads = $query->all()->toArray();

            // dump($leads);
            $leadsRefs = array_column($leads, 'lead_ref');

            //these lead need to be removed from the old kanban_lead_positions rows and add to new ones
            //find which users have these leads on their boards, so we can add it to new kanban_lead_positions rows
            $oldPositionRows = $kanbanLeadPositionTable->find()
                ->select([
                    'position_id' => 'KanbanLeadPositionsEntity.id',
                    'KanbanLeadPositionsMapEntity.lead_ref',
                    'partner_user_id', 'kanban_swimlane_id',
                    'leads',
                ])
                ->distinct()
                ->join([
                    'table' => 'kanban_lead_positions_map',
                    'alias' => 'KanbanLeadPositionsMapEntity',
                    'type' => 'INNER',
                    'conditions' => [
                        'KanbanLeadPositionsMapEntity.kanban_lead_positions_id = KanbanLeadPositionsEntity.id',
                        'KanbanLeadPositionsMapEntity.lead_ref IN' => $leadsRefs
                    ]
                ])
                ->join([
                    'table' => 'leads',
                    'alias' => 'LeadEntity',
                    'type' => 'INNER',
                    'conditions' => 'LeadEntity.lead_ref = KanbanLeadPositionsMapEntity.lead_ref'
                ])
                ->order(['LeadEntity.created' => 'ASC'])
                // ->sql()
                ->disableHydration()
                ->toArray();
            // dump($oldPositionRows->sql());
            // dump($oldPositionRows->toArray()); die();
            // dump("OldRows");
            // print_r($oldPositionRows);
            // dump($oldPositionRows);
            // die();
            // $partnerUserIds =  array_unique(array_column($oldPositionRows, 'partner_user_id'));
            $retainColumns = array_flip(['partner_user_id', 'kanban_swimlane_id']);
            $uniqueItems = [];
            $uniqueItemsWithRefs = [];
            foreach ($oldPositionRows as $item) {
                // Extract the columns of interest
                $filtered = array_intersect_key($item, $retainColumns);
                // Serialize the filtered array to use as a unique identifier
                $key = serialize($filtered);
                $filtered['man_status_group_id'] = $manStatusGroupId;
                // Add only unique items to the result
                if (!isset($uniqueItems[$key])) {
                    $uniqueItems[$key] = $filtered;
                    $uniqueItemsWithRefs[$key] = $filtered;
                }
                $uniqueItemsWithRefs[$key]['lead_refs'][] = $item['KanbanLeadPositionsMapEntity']['lead_ref'];
            }
            // Convert the unique associative array to a regular indexed array
            $newArray = array_values($uniqueItems);
            // $newArrayWithRefs = array_values($uniqueItemsWithRefs);
            // dump($uniqueItemsWithRefs);//die();
            //remove lead_refs from old position rows
            $kanbanLeadPositionTempData = [];
            foreach ($oldPositionRows as $oldPositionRow) {
                $kanbanLeadPositionTempData[$oldPositionRow['position_id']]['lead_refs'][] = $oldPositionRow['lead_ref'];
                $kanbanLeadPositionTempData[$oldPositionRow['position_id']]['leads'] = $oldPositionRow['leads'];
            }
            $kanbanLeadPositionData = [];
            foreach ($kanbanLeadPositionTempData as $positionId => $swimlane) {
                $refs = $swimlane['lead_refs'];
                $kanbanLeadPositionData[$positionId] = [
                    'id' => $positionId,
                    'leads' => array_filter($swimlane['leads'], function ($item) use ($refs) {
                        return !in_array($item, $refs);
                    })
                ];
            }
            // dump($kanbanLeadPositionData);
            $removalRows = $kanbanLeadPositionTable->find()->where(['id IN' => array_keys($kanbanLeadPositionData)]);
            $removalRows = $kanbanLeadPositionTable->patchEntities($removalRows, $kanbanLeadPositionData);
            $kanbanLeadPositionTable->saveMany($removalRows);
            $kanbanLeadPositionMapTable->deleteAll(['lead_ref IN' => $leadsRefs]);

            //Add leads to new positions rows
            //run a tuple(kinda) search to find rows
            $conditions = [];
            foreach ($newArray as $tuple) {
                $conditions[] = [
                    'AND' => [
                        'KanbanLeadPositionsEntity.partner_user_id' => $tuple['partner_user_id'],
                        'KanbanLeadPositionsEntity.kanban_swimlane_id' => $tuple['kanban_swimlane_id'],
                        'KanbanLeadPositionsEntity.man_status_group_id' => $tuple['man_status_group_id']
                    ]
                ];
            }
            $results = $kanbanLeadPositionTable->find()
                ->select(['id', 'partner_user_id', 'kanban_swimlane_id', 'man_status_group_id', 'leads'])
                ->where(['OR' => $conditions])->disableHydration();
            // dump($results->sql());    
            $results = $results->toArray();
            // dump($results);//die();    


            $newPositionData = [];
            foreach ($results as $newRow) {
                // dump(array_intersect_key($newRow, $retainColumns));
                $key = serialize(array_intersect_key($newRow, $retainColumns));
                // dump($key);
                $leadsData = $newRow['leads'];
                if (isset($uniqueItemsWithRefs[$key])) {//must be found
                    $uniqueItemsWithRefs[$key]['found'] = true;
                    $leadsData = array_merge($uniqueItemsWithRefs[$key]['lead_refs'], $leadsData);
                }
                $newPositionData[$newRow['id']] = ['id' => $newRow['id'], 'leads' => $leadsData];
            }
            // dump($newPositionData);//die();   

            $newPositionRows = array_filter($uniqueItemsWithRefs, function ($subArray) {
                return !isset($subArray['found']);
            });

            // dump($newPositionRows);
            // die();  

            $additionRows = $kanbanLeadPositionTable->find()->where(['id IN' => array_keys($newPositionData)]);
            $additionRows = $kanbanLeadPositionTable->patchEntities($additionRows, $newPositionData);
            $kanbanLeadPositionTable->saveMany($additionRows);
            if (count($newPositionRows) > 0) {
                $newPositionRows = array_map(function ($subArray) {
                    $subArray['leads'] = array_reverse($subArray['lead_refs']);
                    unset($subArray['lead_refs']);
                    return $subArray;
                }, $newPositionRows);
                $newRows = $kanbanLeadPositionTable->newEntities($newPositionRows);
                $kanbanLeadPositionTable->saveMany($newRows);
            }
            return true;
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return false;
        }
    }

    //can implement these after
    static public function productChangeSwimlane()
    {
        //find all leads with this product_type_id, along with their kanban_swimlane_id (based on product_type_id)
        //remove these leads from any kanban_lead_positions rows (find them using kanban_lead_positions_map)
        //while doing the above store the partner_user_ids (so we don't have to figure out which users to remap to)
        //add to top of new group + swimlane array
    }

    static public function turnOffSwimlanes($partnerUserId)
    {

    }

    static public function turnOnSwimlanes($partnerUserId)
    {
        self::createPositionData($partnerUserId);
    }

    static private function canPartnerAccessKanban($partnerId)
    {

        $featureFlafTable = TableRegistry::getTableLocator()->get('PartnerFeatureFlagEntity');
        $access = $featureFlafTable->find()->where(['partner_id' => $partnerId, 'access_to_kanban_react' => 1])->count();
        return $access > 0;
    }

    static private function canPartnerUserAccessKanban($partnerUserId)
    {
        $partnerUserTable = TableRegistry::getTableLocator()->get('PartnerUserEntity');
        $res = $partnerUserTable->find()
            ->select(['pff.access_to_kanban_react'])
            ->join([
                'table' => 'partner_feature_flags',
                'alias' => 'pff',
                'type' => 'INNER',
                'conditions' => 'pff.partner_id = PartnerUserEntity.partner_id',
            ])
            ->where(['PartnerUserEntity.partner_user_id' => $partnerUserId])->first();
        if($res){
            $res = $res->toArray();
        }
        if (!$res || !$res['pff'] || !$res['pff']['access_to_kanban_react'] || ($res['pff']['access_to_kanban_react'] != 1)) {
            return false;
        }
        return true;
    }

}