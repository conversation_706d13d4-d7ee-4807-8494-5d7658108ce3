<?php
namespace App\Lend;

use App\Enums\IDDocumentType;
use App\Model\Table\AppTable;

class EquifaxService extends AppTable {

	private $type = array(
						'Commercial Apply' 				=> ['mandatory_fields' => ['firstName', 'lastName', 'enquiryAmount', 'currentAddress'=>['unformatted']], 'url'=>'/commercial-apply'],
						'Consumer Apply' 				=> ['mandatory_fields' => ['firstName', 'lastName', 'dob', 'currentAddress'=>['unformatted'], 'gender', 'document'], 'url'=>'/getScoreLend'],
						'Company Enquiry' 				=> ['mandatory_fields' => ['companyNumber', 'enquiryAmount'], 'url'=>'/company-enquiry'],
						'Company Enquiry - Enriched' 	=> ['mandatory_fields' => ['companyNumber', 'enquiryAmount'], 'url'=>'/company-enquiry'],
						'ID Matrix' 					=> ['mandatory_fields' => ['firstName', 'lastName', 'currentAddress'=>['unformatted']], 'url'=>'/id-matrix'],
						);
	private $additional_fields = ['Driving Licence'=> ['driversLicense','driversLicenseState'], 'Medicare'=>['medicare'=>['cardNumber','referenceNumber']], 'Passport'=>['passport'=>['number','countryCode','expiry']]];
	private $requried_fields_map = [
		'firstName' => [
			'name' => "first_name",
			'message' => "[applicant_name]'s Firstname",
			'route' => "../applicants?applicantId=[owner_ref]"
		], 
		'lastName' => [
			'name' => "last_name",
			'message' => "[applicant_name]'s Lastname",
			'route' => "../applicants?applicantId=[owner_ref]"
		], 
		'dob' => [
			'name' => "dob",
			'message' => "[applicant_name]'s Date of Birth",
			'route' => "../applicants?applicantId=[owner_ref]"
		], 
		'unformatted' => [
			'name' => "all_addresses.0.full_address",
			'message' => "[applicant_name]'s Address",
			'route' => "../applicants?applicantId=[owner_ref]&form=address"
		],
		'gender' => [
			'name' => "gender",
			'message' => "[applicant_name]'s Gender",
			'route' => "../applicants?applicantId=[owner_ref]"
		], 
		'document' => [
			'name' => "driving_licence_num",
			'message' => "[applicant_name]'s Identification (Driving Licence, Passport or Medicare)",
			'route' => "../applicants?applicantId=[owner_ref]&form=identification"
		], 
	];

	private $active_type;
	private $type_name;

	function __construct($init_type, $document=false) {
		parent::__construct(array());
		$this->type_name = $init_type;
		$this->active_type = $this->type[$init_type];
		if($init_type == 'ID Matrix' && $document){
			foreach ($this->additional_fields[$document] as $key => $value) {
				if($document == 'Driving Licence')
					array_push($this->active_type['mandatory_fields'], $value);
				else
					$this->active_type['mandatory_fields'][$key] = $value;
			}
		}
	}

	public function checkRequiredFields($fields, $mandatory_fields=false){
		$pass = true;
		$missing_fields = array();
		if(!$mandatory_fields)	$mandatory_fields = $this->active_type['mandatory_fields'];
		foreach($mandatory_fields as $key=>$mf){
			if(is_array($mf)){
				$result = $this->checkRequiredFields($fields[$key], $mf);
				if(!$result['pass']){
					$pass = false;
					$missing_fields = array_merge($missing_fields, $result['missing_fields']);
				}
			}elseif(empty($fields[$mf])){
				$pass = false;
				$missing_fields[] = $mf;
			}
		}

		return array('pass'=>$pass, 'missing_fields'=>$missing_fields);
	}

	public function mapRequiredFields($missing_fields, $owner_ref, $owner_name){
		$fields_map = [];
		foreach($missing_fields as $key=>$mf){
			$field = $this->requried_fields_map[$mf];
			$field['message'] = str_replace("[applicant_name]", $owner_name, $field['message']);
			$field['route'] = str_replace("[owner_ref]", $owner_ref, $field['route']);
			$fields_map[] = $field;
		}

		return $fields_map;
	}

	public function generateSignature($fields){
		return base64_encode(hash_hmac('sha256', json_encode($fields, JSON_UNESCAPED_SLASHES). getenv('EQUIFAX_SECRET'), getenv('EQUIFAX_SECRET'))); // to base64
	}

	public function callService($fields, $signature, $url = false){
		$curl = curl_init();
		$entries = ["accept: application/json", "content-type: application/json", "X-Signature:".$signature];

		curl_setopt_array($curl, array(
			CURLOPT_URL             => ($this->type_name == "Consumer Apply" ? getenv('DOMAIN_EQF_CONSUMER') : getenv('DOMAIN_EQF')) . (empty($url) ? $this->active_type['url'] : $url),
			CURLOPT_RETURNTRANSFER  => true,
			CURLOPT_ENCODING        => "",
			CURLOPT_MAXREDIRS       => 10,
			CURLOPT_TIMEOUT         => 60,
			CURLOPT_HTTP_VERSION    => CURL_HTTP_VERSION_1_1,
			CURLOPT_CUSTOMREQUEST   => 'POST',
			CURLOPT_HTTPHEADER      => $entries,
			CURLOPT_POST            => true,
			CURLOPT_POSTFIELDS      => json_encode($fields)
		));

		$result = curl_exec($curl);

		if(curl_errno($curl) == 28)	// time out
			return ['success' => false, 'status'=>'error', 'message' => 'Our systems are experiencing a momentary issue, try requesting the report again soon.'];

		if($this->isJson($result)) {
			$result = json_decode($result, true);
		}

		return $result;
	}

	private function isJson($string) {
		if (is_object(json_decode($string))) {
			return true;
		}else{
			return false;
		}
	}

	public function generateCRBIdentifiers($partnerID, $acn, $owner, $document_type, $useEFConsumerService = false)
	{
		$identifiers = '';
		if (empty($owner)) {    // Company Identifiers
			if (!empty($partnerID) && !empty($acn))
				$identifiers = $partnerID . '-' . preg_replace('/\s+/', '', $acn);
		} else {
			// Applicant Identifiers
			$address = $owner['current_address'];
			if (empty($owner['state']))
				$state = $owner['current_address']['state'] ?? '';
			else
				$state = $owner['state'];

			if (empty($state)) $state = $owner['country'];

			$dob = is_object($owner['dob']) ? $owner['dob']->format('Y-m-d') : ($owner['dob'] ? date('Y-m-d', strtotime($owner['dob'])) : '');

			if (!empty($partnerID) && !empty($owner['first_name']) && !empty($owner['last_name']) && !empty($owner['dob'])) {
				$identifiers = $partnerID 
					. '-' . preg_replace('/\s+/', '', $owner['first_name']) 
					. '-' . preg_replace('/\s+/', '', $owner['last_name']) 
					. '-' . preg_replace('/\s+/', '', $dob) 
					. '-' . preg_replace('/\s+/', '', $state);
			}

			if ($useEFConsumerService) {
				$identifiers .= date('-Y-m-d');
			}

			if ($document_type) {
				switch ($document_type) {
					case IDDocumentType::DrivingLicence:
						$document_number = $owner['driving_licence_num'] ?? '';
						$document_expiry = $owner['driving_licence_expiry'] ?? '';
						break;
					case IDDocumentType::Passport:
						$document_number = $owner['passport_number'] ?? '';
						$document_expiry = $owner['passport_expiry'] ?? '';
						break;
					case IDDocumentType::Medicare:
						$document_number = $owner['medicare_number'] ?? '';
						$document_expiry = $owner['medicare_expiry'] ?? '';
						break;
				}
	
				$document_expiry = $document_expiry ? date('Y-m-d', strtotime($document_expiry)) : '';
				$documentString = $address['address'] .'-'. $address['suburb'] .'-'. $address['state'] .'-'. $address['postcode'];
				$documentString .= $document_type .'-'. $document_number .'-'. $document_expiry;
				$hashedStr = md5(preg_replace('/\s+/', '', $documentString));
				$identifiers .= '-' . $hashedStr;
			}
		}

		$identifiers = strtolower($identifiers);

		return strlen($identifiers) > 255 ? substr($identifiers, 0, 255) : $identifiers;
	}

	public function fillIdCheckInfo($partnerID, $acn, $owner, $useEFConsumerService = false)
	{
		$owner['id_verification_result'] = null;
		$owner['id_verification_doc'] = null;

		if ($owner['latest_id_matrix']) {
			$identifiers = $this->generateCRBIdentifiers($partnerID, $acn, $owner, $owner['latest_id_matrix']['document'], $useEFConsumerService);
			if(strpos($owner['latest_id_matrix']['identifier_used'], $identifiers) !== false){
				$owner['id_verification_doc'] = $owner['latest_id_matrix']['document'];
				$owner['id_verification_result'] = $owner['latest_id_matrix']['decision'];
			}
			unset($owner['latest_id_matrix']);
		}

		return $owner;
	}
}
