<?php

namespace App\Lend;

use Exception;
use <PERSON>ake\ORM\TableRegistry;
use Cake\Datasource\ConnectionManager;
use Cake\Database\Expression\QueryExpression;
use Cake\Log\Log;
use <PERSON><PERSON><PERSON>\Controller\LeadsController;
use Cake\Datasource\Exception\RecordNotFoundException;

/**
 * Helper Class for Partner and Lead checklists
 */
class ChecklistHelper
{
    /**
     * Add/Update partner checklists
     */
    public static function addUpdatePartnerChecklist($partnerId, $requestData, $update = false)
    {
        try {
            $schema = [
                "partner_checklist_id" => "pk",
                "checklist_name" => true,
                "checklist_description" => null,
                "is_active" => "warn",
                "partner_checklist_product_types" => [
                    "partner_checklist_product_type_id" => "pk",
                    "product_type_id" => true
                ],
                "checklist_items" => [
                    "partner_checklist_item_id" => "pk",
                    "item_name" => true,
                    "item_description" => null,
                    "order" => true,
                    "is_active" => "warn",
                ]
            ];
            $data = array_intersect_key($requestData, $schema);
            // dump($data);
            foreach ($schema as $key => $fields) {
                if (isset($data[$key])) {
                    if (is_array($fields)) {
                        if (is_array($data[$key])) {
                            foreach ($data[$key] as &$item) {
                                $item = array_intersect_key($item, $fields);
                                foreach ($fields as $field => $required) {
                                    $path = $key . '.' . $field;
                                    if (!isset($item[$field])) {
                                        if (!$update && ($required === true)) {
                                            throw new \Exception("Field " . $path . " is required");
                                        }
                                    } else if ($update && ($required === "warn")) {
                                        throw new \Exception("Field " . $path . " cannot be modified");
                                    }
                                }
                            }
                        } else {
                            throw new \Exception("Field " . $key . " is not in correct format");
                        }
                    } else if ($update && ($fields === "warn")) {
                        throw new \Exception("Field " . $key . " cannot be modified");
                    }
                } else {
                    if (!$update && ($fields === true)) {
                        throw new \Exception("Field " . $key . " is required");
                    }
                }
            }
            $data['partner_id'] = $partnerId;
            $partnerChecklistsTable = TableRegistry::getTableLocator()->get('PartnerChecklistEntity');
            $associatedTables = ['PartnerChecklistItemEntity', 'PartnerChecklistProductTypeEntity'];
            $productTypeIds = array_column($data['partner_checklist_product_types'] ?? [], 'product_type_id');
            if ($update) {
                $checklist = $partnerChecklistsTable
                    ->get(
                        (int) $data['partner_checklist_id'],
                        [
                            'contain' => $associatedTables,
                            'conditions' => [
                                'partner_id' => $partnerId
                            ]
                        ]
                    );
                //ensure existing items eing edited belong to this checklist
                $updateChecklistItemIds = array_column($data['checklist_items'] ?? [], 'partner_checklist_item_id');
                $existingChecklistItemIds = array_column($checklist->checklist_items, 'partner_checklist_item_id');
                $invalidChecklistItemIds = array_diff($updateChecklistItemIds,  $existingChecklistItemIds);
                if (count($invalidChecklistItemIds) > 0) {
                    throw new Exception("These partner_checklist_item_id(s) don't belong to this checklist: " . implode(", ", $invalidChecklistItemIds));
                }

                //create array of existing items with their id's so we can access without looping multiple times
                $existingChecklistItems = [];
                foreach($checklist->checklist_items as $itm){
                    $existingChecklistItems[$itm->partner_checklist_item_id] = $itm;
                }

                //add updated timestamp to checklist items
                foreach($data['checklist_items'] as &$item){
                    if(isset($item['partner_checklist_item_id']) && isset($existingChecklistItems[$item['partner_checklist_item_id']])){
                        foreach($item as $f => $v){
                            if ($f === 'partner_checklist_item_id') {
                                continue; // Skip comparing the ID
                            }
                            if($v !== $existingChecklistItems[$item['partner_checklist_item_id']]->$f){
                                $item['updated'] = (new \DateTime())->format('Y-m-d H:i:s');
                                break;
                            }
                        }
                    }
                }

                // $existingProductTypeIds = array_column($checklist->partner_checklist_product_types ?? [], 'product_type_id');
                $existingProductTypeIds = [];
                $existingProductTypeRows = [];
                foreach($checklist->partner_checklist_product_types as $p){
                    $existingProductTypeIds[] = $p->product_type_id;
                    $existingProductTypeRows[$p->partner_checklist_product_type_id] = [
                        'partner_checklist_product_type_id' => $p->partner_checklist_product_type_id,
                        'partner_checklist_id' => $p->partner_checklist_id,
                        'product_type_id' => $p->product_type_id,
                    ];
                }
                //overwrite any exisiting rows coming through
                foreach($data['partner_checklist_product_types'] as $i => $pt){
                    if(isset($pt['partner_checklist_product_type_id'])){
                        if(isset($existingProductTypeRows[$pt['partner_checklist_product_type_id']])){
                            $data['partner_checklist_product_types'][$i] = $existingProductTypeRows[$pt['partner_checklist_product_type_id']];
                        }
                        else{//invalid row, delete it
                            unset($data['partner_checklist_product_types'][$i]);
                        }
                    }
                }
                $added = array_values(array_diff($productTypeIds, $existingProductTypeIds));
                $removed = array_values(array_diff($existingProductTypeIds, $productTypeIds));
                $retained = array_values(array_intersect($existingProductTypeIds, $productTypeIds));
                $originalCheckListItemIds = array_column($checklist->checklist_items, 'partner_checklist_item_id');

                $deleteConditions = ['partner_checklist_id' => $checklist->partner_checklist_id];
                if (count($retained) > 0) {
                    $deleteConditions['product_type_id NOT IN'] = $retained;
                }
                //remove the product type mappings for this checklist (except the ones being retained)
                $partnerChecklistProductTypeTable = TableRegistry::getTableLocator()->get('PartnerChecklistProductTypeEntity');
                $partnerChecklistProductTypeTable->deleteAll($deleteConditions);

                $partnerChecklistsTable->patchEntity($checklist, $data, ['associated' => $associatedTables]);
                $partnerChecklistsTable->save($checklist);
                if (count($added) > 0) { //create lead checklists for newly mapped product type ids
                    $jsonData = [
                        'partner_checklist_id' => $checklist->partner_checklist_id,
                        'data' => $added
                    ];
                    self::createBackgroundJob('add_lead_checklists', 'addLeadChecklists', null, json_encode($jsonData));
                }
                if (count($removed) > 0) { //remove lead checklists for removed product type ids
                    $jsonData = [
                        'partner_checklist_id' => $checklist->partner_checklist_id,
                        'data' => $removed
                    ];
                    self::createBackgroundJob('deactivate_lead_checklists', 'deactivateLeadChecklists', null, json_encode($jsonData));
                }
                $checklist = $partnerChecklistsTable->get($checklist->partner_checklist_id, ['contain' => $associatedTables]);
                $newCheckListItemIds = array_column($checklist->checklist_items, 'partner_checklist_item_id');
                $addedCheckListItemIds = array_values(array_diff($newCheckListItemIds, $originalCheckListItemIds));
                if ((count($addedCheckListItemIds) > 0) && (count($retained) > 0)) { //create new lead checklists items for existing lead checklists
                    $jsonData = [
                        'partner_checklist_id' => $checklist->partner_checklist_id,
                        'data' => [
                            'checklist_item_ids' => $addedCheckListItemIds,
                            'product_type_ids' => $retained
                        ]
                    ];
                    self::createBackgroundJob('add_new_checklist_items', 'addNewChecklistItems', null, json_encode($jsonData));
                }
            } else {
                $checklist = $partnerChecklistsTable->newEntity(
                    $data,
                    ['associated' => $associatedTables]
                );
                $partnerChecklistsTable->save($checklist);
                if (count($productTypeIds) > 0) { //create lead checklists
                    $jsonData = [
                        'partner_checklist_id' => $checklist->partner_checklist_id,
                        'data' => $productTypeIds
                    ];
                    self::createBackgroundJob('add_lead_checklists', 'addLeadChecklists', null, json_encode($jsonData));
                }
                $checklist = $partnerChecklistsTable->get($checklist->partner_checklist_id, ['contain' => $associatedTables]);
            }
            return ['data' => ['success' => true, 'data' => $checklist], 'code' => 200];
        } catch (RecordNotFoundException $e) {
            return ['data' => ['error' => 'Invalid partner_checklist_id'], 'code' => 400];
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return ['data' => ['error' => $e->getMessage()], 'code' => ($e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400)];
        }
    }

    /**
     * Delete(soft delete) a partner check;ist, also adds background job to delete any associated lead checklists
     */
    public static function deletePartnerChecklist($partnerId, $checklistId)
    {
        try {
            if (!$checklistId) {
                throw new Exception("Invalid or missing partner_checklist_id");
            }
            $partnerChecklistsTable = TableRegistry::getTableLocator()->get('PartnerChecklistEntity');
            $associatedTables = [
                'PartnerChecklistItemEntity' => ['fields' => ['partner_checklist_item_id', 'partner_checklist_id', 'is_active']],
            ];
            $checklist = $partnerChecklistsTable
                ->get(
                    (int) $checklistId,
                    [
                        'fields' => ['partner_checklist_id', 'is_active'],
                        'contain' => $associatedTables,
                        'conditions' => [
                            'partner_id' => $partnerId
                        ]
                    ]
                );
            if (!$checklist) {
                throw new Exception("Invalid or missing partner_checklist_id");
            }

            $updateData = [
                'is_active' => false,
                'checklist_items' => []
            ];

            foreach ($checklist->checklist_items as $item) {
                $updateData['checklist_items'][] = [
                    'partner_checklist_item_id' => $item->partner_checklist_item_id,
                    'is_active' => false
                ];
            }
            $partnerChecklistsTable->patchEntity($checklist, $updateData, ['associated' => $associatedTables]);
            $partnerChecklistsTable->save($checklist);
            $jsonData = [
                'partner_checklist_id' => $checklistId
            ];
            self::createBackgroundJob('deactivate_lead_checklists', 'deactivateLeadChecklists', null, json_encode($jsonData));
            return ['data' => ['success' => true], 'code' => 200];
        } catch (RecordNotFoundException $e) {
            return ['data' => ['error' => 'Invalid partner_checklist_id'], 'code' => 400];
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return ['data' => ['error' => $e->getMessage()], 'code' => ($e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400)];
        }
    }

    public static function listPartnerChecklists($partnerId, $active = null)
    {
        try {
            $partnerChecklistsTable = TableRegistry::getTableLocator()->get('PartnerChecklistEntity');
            $conditions = ['partner_id' => $partnerId];
            if (!is_null($active)) {
                $conditions['is_active'] = $active;
            }
            $checklists = $partnerChecklistsTable
                ->find(
                    'all',
                    [
                        'order' => ['PartnerChecklistEntity.created' => 'ASC'],
                        'contain' => [
                            'PartnerChecklistItemEntity' => [
                                'sort' => ['PartnerChecklistItemEntity.order' => 'ASC'],
                                'conditions' => [
                                    'PartnerChecklistItemEntity.is_active' => 1
                                ]
                                ],
                            'PartnerChecklistProductTypeEntity.PartnerProductTypeEntity' => [
                                'fields' => ['product_type_id', 'product_type_name']
                            ]
                        ],
                        'conditions' => $conditions
                    ]
                );
            return ['data' => ['success' => true, 'data' => $checklists], 'code' => 200];
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return ['data' => ['error' => $e->getMessage()], 'code' => ($e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400)];
        }
    }

    /**
     * Adds background job(s) for rechecking checklist status
     * This does a single insert for all rows rather than use multiple entities as it could be thousands of leads
     */
    private static function createBackgroundJob($jobType, $functionName, $leadId, $refId = null)
    {
        $bg_jobs_table = TableRegistry::getTableLocator()->get('BackgroundJobEntity');
        $bg_job = $bg_jobs_table->newEntity([
            'lead_id' => $leadId,
            'job_type' => $jobType,
            'ref_id' => $refId,
            'job_status' => 0,
            'class_name' => 'Checklists',
            'function_name' => $functionName,
        ]);
        $bg_jobs_table->save($bg_job);
    }

    /**
     * Reset the lead checklists for a lead
     * 
     * Adds any checklists applicable to the lead
     * Deactivates existing lead checklists not applicable to the lead
     * Removes/Adds lead checklist items to any retained checklists 
     * based on the parent partner checklist config
     */
    public static function resetLeadChecklists($leadId) {
        try{
            $leadsTable = TableRegistry::getTableLocator()->get('LeadEntity');
            $leadChecklistTable = TableRegistry::getTableLocator()->get('LeadChecklistEntity');
            $partnerChecklistsTable = TableRegistry::getTableLocator()->get('PartnerChecklistEntity');
            $partnerChecklistItemsTable = TableRegistry::getTableLocator()->get('PartnerChecklistItemEntity');
            $partnerChecklistProductTypeTable = TableRegistry::getTableLocator()->get('PartnerChecklistProductTypeEntity');

            //get product type
            $lead = $leadsTable->get($leadId, ['fields' => ['lead_id', 'partner_id', 'product_type_id']]);
            // dump($lead);
            //get partner checklists for the product type
            $checklists = $partnerChecklistProductTypeTable->find()
                ->select([
                    'PartnerChecklistProductTypeEntity.product_type_id',
                    'PartnerChecklistProductTypeEntity.partner_checklist_id',
                    'PartnerChecklistEntity.partner_checklist_id',
                    'PartnerChecklistEntity.partner_id',
                    'PartnerChecklistItemEntity.partner_checklist_item_id',
                    'PartnerChecklistItemEntity.partner_checklist_id',
                ])
                ->join([
                    'PartnerChecklistEntity' => [
                        'table' => 'partner_checklists',
                        'type' => 'INNER',
                        'conditions' => [
                            'PartnerChecklistEntity.partner_checklist_id = PartnerChecklistProductTypeEntity.partner_checklist_id',
                            'PartnerChecklistEntity.partner_id' => $lead->partner_id,
                            'PartnerChecklistEntity.is_active' => 1,
                        ]
                    ],
                    'PartnerChecklistItemEntity' => [
                        'table' => 'partner_checklist_items',
                        'type' => 'INNER',
                        'conditions' => [
                            'PartnerChecklistItemEntity.partner_checklist_id = PartnerChecklistEntity.partner_checklist_id',
                            'PartnerChecklistItemEntity.is_active' => 1,
                        ]
                    ]
                ])
                ->where([
                    'PartnerChecklistProductTypeEntity.product_type_id' => $lead->product_type_id,
                ])
                ->enableHydration(false)
                ->distinct()
                ->toArray();
            $checklistItemsMap = [];
            foreach ($checklists as $r) {
                $checklistItemsMap[$r['partner_checklist_id']][] = $r['PartnerChecklistItemEntity']['partner_checklist_item_id'];
            }
            $partnerChecklistIds = array_keys($checklistItemsMap);

            //find the ones that don't exist for the lead and create them
            //find existing lead checklists
            
                $existingLeadChecklists = $leadChecklistTable
                ->find()
                ->contain([
                    'LeadChecklistItemEntity'
                ])
                ->where([
                    'LeadChecklistEntity.lead_id' => $leadId,
                    'LeadChecklistEntity.is_active' => 1,
                ])
                ->toArray();
            
            

                // dump($existingLeadChecklists);die();
            $existingPartnerChecklistIds = array_column($existingLeadChecklists, 'partner_checklist_id');

            // dump($existingPartnerChecklistIds);
            if(count($existingPartnerChecklistIds) > 0){
                //update checklist items from partner checklists for any existing checklists
                $leadChecklistIds = [];
                $leadChecklistData = [];
                $leadChecklistItemIds = [];
                $newLeadChecklistItemData = [];
                foreach($existingLeadChecklists as $leadChecklist){
                    if(!in_array($leadChecklist['partner_checklist_id'], $partnerChecklistIds)){//no longer required, deactivate
                        $leadChecklistIds[] = $leadChecklist['lead_checklist_id'];
                        $leadChecklistData[] = [
                            'lead_checklist_id' => $leadChecklist['lead_checklist_id'],
                            'is_active' => 0
                        ];
                        continue;
                    }

                    //find partner_checklist_item_ids that exist in lead checklist items
                    $itemIds = array_column($leadChecklist['checklist_items'], 'partner_checklist_item_id', 'lead_checklist_item_id');
                    $missingItemIds = array_diff($checklistItemsMap[$leadChecklist['partner_checklist_id']], $itemIds);
                    if(count($missingItemIds) > 0){
                        foreach($missingItemIds as $i){
                            $newLeadChecklistItemData[] = [
                                'lead_id' => $leadId,
                                'lead_checklist_id' => $leadChecklist['lead_checklist_id'],
                                'partner_checklist_item_id' => $i,
                            ];
                        }
                    }
                    $extraItems = array_diff($itemIds, $checklistItemsMap[$leadChecklist['partner_checklist_id']]);
                    $leadChecklistItemIds = array_keys($extraItems);
                }

                //deactivate lead checklists that don't match product type id
                if(count($leadChecklistIds) > 0){
                    $leadChecklistEntities = $leadChecklistTable->find('all')->where(['lead_checklist_id IN' => $leadChecklistIds]);
                    $leadChecklistEntities = $leadChecklistTable->patchEntities($leadChecklistEntities, $leadChecklistData);
                    $leadChecklistTable->saveMany($leadChecklistEntities);
                }

                //delete inactive partner check list items thatv are still in lead checklist
                if(count($leadChecklistItemIds) > 0){
                    $partnerChecklistItemsTable->deleteAll(['lead_checklist_item_id IN' => $leadChecklistItemIds]);
                }

                //create missing items for existing checklists
                if(count($newLeadChecklistItemData) > 0){
                    $leadChecklistItemEntities = $partnerChecklistItemsTable->newEntities($newLeadChecklistItemData);
                    $partnerChecklistItemsTable->saveMany($leadChecklistItemEntities);
                }
            }

            //create new lead checklists based on product type
            $newPartnerChecklistIds = array_diff($partnerChecklistIds, $existingPartnerChecklistIds);
            // dump($newPartnerChecklistIds);
            if(count($newPartnerChecklistIds) > 0){
                $newChecklists = $partnerChecklistsTable->find()
                    ->where([
                        'PartnerChecklistEntity.partner_checklist_id IN' => $newPartnerChecklistIds
                    ])
                    ->contain([
                        'PartnerChecklistItemEntity' => function ($q) {
                            return $q->where([
                                'PartnerChecklistItemEntity.is_active' => 1
                            ]);
                        }
                    ]);
                $leadChecklistsData = [];
                foreach($newChecklists as $checklist){
                    $leadChecklist = [
                        'lead_id' => $leadId,
                        'partner_checklist_id' => $checklist->partner_checklist_id,
                        'checklist_items' => []
                    ];
                    foreach ($checklist->checklist_items as $item) {
                        $leadChecklist['checklist_items'][] = [
                            'partner_checklist_item_id' => $item->partner_checklist_item_id,
                        ];
                    }
                    $leadChecklistsData[] = $leadChecklist;
                }
                if(count($leadChecklistsData) > 0){
                    $leadChecklists = $leadChecklistTable->newEntities($leadChecklistsData);
                    $leadChecklistTable->saveMany($leadChecklists);
                }
            }

            self::updateLeadStatus([$leadId]);
        }
        catch(Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return ['data' => ['error' => $e->getMessage()], 'code' => ($e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400)];

        }
    }

    public static function deletePartnerChecklistItem($partnerId, $checklistItemId)
    {
        try {
            if (!$checklistItemId) {
                throw new Exception("Invalid or missing partner_checklist_item_id");
            }
            $partnerChecklistItemTable = TableRegistry::getTableLocator()->get('PartnerChecklistItemEntity');
            $associatedTables = [
                'PartnerChecklistEntity' => ['fields' => ['partner_checklist_id', 'partner_id']],
            ];
            $checklistItem = $partnerChecklistItemTable
                ->get(
                    (int) $checklistItemId,
                    [
                        'fields' => ['partner_checklist_item_id', 'partner_checklist_id'],
                        'contain' => $associatedTables
                    ]
                );
            if ($checklistItem->partner_checklist->partner_id !== $partnerId) {
                throw new Exception("Invalid or missing partner_checklist_id");
            }

            $partnerChecklistItemTable->patchEntity($checklistItem, ['is_active' => false]);
            $partnerChecklistItemTable->save($checklistItem);
            //need to disable all lead_checklist_items which belog to this checklist items and recheck the status for the lead
            $jsonData = [
                'checklist_item_id' => $checklistItemId
            ];
            self::createBackgroundJob('deactivate_lead_checklist_items', 'deleteLeadChecklistItems', null, json_encode($jsonData));
            return ['data' => ['success' => true], 'code' => 200];
        } catch (RecordNotFoundException $e) {
            return ['data' => ['error' => 'Invalid partner_checklist_item_id'], 'code' => 400];
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return ['data' => ['error' => $e->getMessage()], 'code' => ($e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400)];
        }
    }

    public static function updateLeadChecklistItemStatus($partnerId, $requestData)
    {
        try {
            $schema = [
                'lead_checklist_item_id' => null,
                'is_complete' => null,
            ];
            $requestData = array_intersect_key($requestData, $schema);

            if (
                !isset($requestData['lead_checklist_item_id']) 
                    || !is_numeric($requestData['lead_checklist_item_id'])
                ) {
                throw new Exception("Invalid or missing lead_checklist_item_id");
            }
            if (
                !isset($requestData['is_complete']) 
                    || !is_bool($requestData['is_complete'])
                ) {
                throw new Exception("Invalid or missing is_complete");
            }
            $leadChecklistItemTable = TableRegistry::getTableLocator()->get('LeadChecklistItemEntity');
            $associatedTables = [
                'LeadChecklistEntity' => ['fields' => ['lead_checklist_id', 'lead_id']],
                'LeadChecklistEntity.LeadEntity' => ['fields' => ['lead_id', 'partner_id']],
            ];

            $checklistItem = $leadChecklistItemTable
                ->get(
                    (int) $requestData['lead_checklist_item_id'],
                    [
                        'fields' => ['lead_checklist_item_id', 'lead_checklist_id'],
                        'contain' => $associatedTables
                    ]
                );
            if ($checklistItem->lead_checklist->lead->partner_id !== $partnerId) {
                throw new Exception("Invalid or missing lead_checklist_item_id");
            }
            $data = ['is_complete' => $requestData['is_complete'], 'updated' => date('Y-m-d H:i:s')];
            
            $leadChecklistItemTable->patchEntity($checklistItem, $data);
            $leadChecklistItemTable->save($checklistItem);
            self::updateLeadStatus([$checklistItem->lead_checklist->lead_id]);
            return ['data' => ['success' => true, 'data' => $checklistItem], 'code' => 200];
        } catch (RecordNotFoundException $e) {
            return ['data' => ['error' => 'Invalid partner_checklist_item_id'], 'code' => 400];
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return ['data' => ['error' => $e->getMessage()], 'code' => ($e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 400)];
        }
    }

    /**
     * Recalculate and update checklist_status for checklists
     * linked to the leads and overall lead checklist_status 
     */
    public static function updateLeadStatus($leadIds)
    {
        if (!empty($leadIds)) {
            $leadAssociatedDataTable = TableRegistry::getTableLocator()->get('LeadAssociatedData');
            $leadChecklistTable =TableRegistry::getTableLocator()->get('LeadChecklistEntity');

            $batchSize = 500;
            $chunks = array_chunk($leadIds, $batchSize);
            foreach ($chunks as $chunk) {
                $checklistStatuses = $leadChecklistTable
                    ->find()
                    ->select([
                        'LeadChecklistEntity.lead_checklist_id',
                        'LeadChecklistEntity.lead_id',
                        'LeadChecklistEntity.is_active',
                    ])
                    ->contain(['LeadChecklistItemEntity'])
                    ->where([
                        'LeadChecklistEntity.is_active' => true,
                        'LeadChecklistEntity.lead_id IN' => $chunk
                    ])
                    ->toArray();
                if (empty($checklistStatuses)) {
                    //no checklists found for any leads in chunk, means all leads statuses for the chunk have to be set to defaults
                    $leadAssociatedDataTable->query()
                        ->update()
                        ->set(['checklist_status' => 0])
                        ->where(['lead_id IN' => $chunk])
                        ->execute();
                    continue;
                }
                $leadStatusMap = [];
                foreach ($checklistStatuses as $leadChecklist) {
                    $items = $leadChecklist->checklist_items;
                    $completedCount = 0;
                    foreach ($items as $item) {
                        if ($item->is_complete) {
                            $completedCount++;
                        }
                    }
                    if ($completedCount === 0) {
                        $checklistStatus = 0; // Not Started
                    } elseif ($completedCount < count($items)) {
                        $checklistStatus = 1; // In Progress
                    } else {
                        $checklistStatus = 2; // Completed
                    }
                    $leadStatusMap[$leadChecklist->lead_id][$leadChecklist->lead_checklist_id] = $checklistStatus;
                }
                $caseStmtLead = "CASE lead_id ";
                $caseStmt = "CASE lead_checklist_id ";
                $checklistIds = [];

                foreach ($leadStatusMap as $leadId => $statuses) {
                    $notStartedCount = 0;
                    $completeCount  = 0;
                    foreach ($statuses as $leadChecklistId => $status) {
                        if ($status === 1) {
                            $notStartedCount++;
                        } else if ($status === 3) {
                            $completeCount++;
                        }
                        $caseStmt .= "WHEN {$leadChecklistId} THEN {$status} ";
                        $checklistIds[] = $leadChecklistId;
                    }
                    if (count($statuses) === $notStartedCount) {
                        $caseStmtLead .= "WHEN {$leadId} THEN 1 ";
                    } else if (count($statuses) === $completeCount) {
                        $caseStmtLead .= "WHEN {$leadId} THEN 3 ";
                    } else {
                        $caseStmtLead .= "WHEN {$leadId} THEN 2 ";
                    }
                }
                $caseStmtLead .= "ELSE 0 END";
                $caseStmt .= "ELSE 1 END";
                $leadChecklistTable->query()
                    ->update()
                    ->set(['status' => $leadChecklistTable->query()->newExpr($caseStmt)])
                    ->where(['lead_checklist_id IN' => $checklistIds])
                    ->execute();
                $leadAssociatedDataTable->query()
                    ->update()
                    ->set(['checklist_status' => $leadChecklistTable->query()->newExpr($caseStmtLead)])
                    ->where(['lead_id IN' => $chunk])
                    ->execute();
            }
        }
    }
}
