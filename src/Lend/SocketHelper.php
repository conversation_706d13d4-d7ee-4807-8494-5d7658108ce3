<?php
namespace App\Lend;

use Exception;
use Cake\ORM\TableRegistry;
use Cake\Datasource\ConnectionManager;
use Cake\Database\Expression\QueryExpression;
use Cake\Log\Log;
use <PERSON><PERSON><PERSON>\Controller\LeadsController;
use Cake\Core\Configure;
use App\Lend\LendInternalAuth;

class SocketHelper{
    /**
	 * Send a message to socket server (to broadcast to sockets)
	 */
	public static function sendMessage($userIds, $topic, $message){
		try{
            if(empty(getenv('SEND_SOCKET_REQUESTS')) || (!in_array(getenv('SEND_SOCKET_REQUESTS'), ['1', 'true']))){
                return;
            }
			$payload = [
				'userIds' => $userIds,
				'topic' => $topic,
				'message' => $message
			];
			$ch = curl_init();
			curl_setopt_array($ch, [
				CURLOPT_URL => getenv('DOMAIN_SOCKET') . '/broadcast',
				CURLOPT_RETURNTRANSFER => true,
				CURLOPT_ENCODING => "",
				CURLOPT_MAXREDIRS => 10,
				CURLOPT_TIMEOUT => 60,
				CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
				CURLOPT_CUSTOMREQUEST => "POST",
				CURLOPT_POSTFIELDS => json_encode($payload),
				CURLOPT_HTTPHEADER => [
					"Content-Type: application/json",
					"x-api-key:".getenv('SOCKET_API_KEY'),
				],
			]);
			//execute post
			$result = curl_exec($ch);
			$curlReturnValue = curl_errno($ch);
			$httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
			$err = curl_error($ch);
			curl_close($ch);
		
			if ('0' !== "$curlReturnValue") {
				throw new \Exception("Failed to communicate with server: " . $err);
			} else {
				if($httpcode != '200'){
					if($result){
						$decodedResult = json_decode($result, true);
						if($decodedResult['error']){
							throw new \Exception($decodedResult['error']);
						}
						else{
							throw new \Exception('Http Error Code: ' . $httpcode);
						}
					}
				}
			}
		}
		catch (Exception $ex){
            TableRegistry::getTableLocator()->get('App')->postToSlack(
                ":warning: :no_bell: Unable to send message to socket server :no_bell: :warning: \n"
				. "Exception: `".$ex->getMessage()."` \n"
                . "Payload: ```".json_encode($payload)."``` \n",
             "lend_errors");
		}
	}

    /**
     * Return an array of user ids of users who can see a lead
     */
    public static function getLeadUsers($leadId, $includeAccessUsers = false){
        $lead_table = TableRegistry::getTableLocator()->get('LeadEntity');
        // $lead = $lead_table->get($leadId, ['contain'=>['PartnerUserLeadsEntity']]);
        $lead = $lead_table->get($leadId);
        $partnerId = $lead->partner_id;

        $partnerUsersTable = TableRegistry::getTableLocator()->get('PartnerUserEntity');
        $query = $partnerUsersTable->find()
            ->select(['PartnerUserEntity.partner_user_id'])
            ->where(['PartnerUserEntity.partner_id' => $partnerId]);
        if($includeAccessUsers === true){
            $query->leftJoinWith('PartnerUserLeadsEntity', function ($q) use ($leadId) {
                return $q->where([
                    'PartnerUserLeadsEntity.lead_id' => $leadId,
                    'PartnerUserLeadsEntity.status' => 'ACCESS',
                ]);
            })
            ->where(function ($exp, $q) {
                return $exp->or_([
                    'PartnerUserEntity.access_all_leads' => 1,
                    'PartnerUserLeadsEntity.partner_user_lead_id IS NOT NULL'
                ]);
            });
        }
        else{
            $query->where(['PartnerUserEntity.access_all_leads' => 1]);
        }
        // log::error($query->sql());
        $userIds = $query->toList();
        $userIds = array_column($userIds, 'partner_user_id');
        return array_filter($userIds, function ($id) {
            return $id !== null;
        });
    }

    public static function kanbanAddLead($leadId, $partnerUserIds = null, $type = null, $extraData = null){
        //Add lead to Kanban boards
        $options = [
            'fields' => ['man_status_id', 'product_type_id', 'call_queue_status'],
            'contain' => [
                'ManStatusEntity'=> ['fields' => ['id', 'man_status_group_id']],
                'ManStatusEntity.ManStatusGroupEntity'=> ['fields' => ['id']],
            ]
        ];
        $lead = TableRegistry::getTableLocator()->get('LeadEntity')->get($leadId, $options);
        // KanbanHelper::addLead(null, $lead->lead_ref);
        if(!in_array($lead->call_queue_status, Configure::read('Lend.NOT_IN_CALL_QUEUE_STATUS'))){//value is invalid - ignore
            return;
        }
        $msg = [
            'type'=> ($type ??'lead_add'),
            'to_group_id' => $lead->man_status->man_status_group->id,
            'product_type_id'=> $lead->product_type_id
        ];
        if($extraData){
            $msg['extra_data'] = $extraData;
        }
        self::sendLead($leadId, $partnerUserIds, true, $msg);
    }

    /** Need to emit addlead with special event type so that either it can load entire board again 
     * or it can load the specific man_status_group_id and swimlane data for those filters (will be lighter)
    */
    public static function kanbanUpdateLead($leadId, $filterValuesChanged = []){
        if(count($filterValuesChanged) > 0){//filter values have changed
            $leadTable = TableRegistry::getTableLocator()->get('LeadEntity');
            $lead = $leadTable->get($leadId, ['fields' => ['lead_ref', 'man_status_id', 'product_type_id']]);
            $extraData = [
                'lead_ref' =>  $lead->lead_ref,
                'from_group_id' => $lead->man_status_id,
                'from_product_type_id' => $lead->product_type_id
            ];
            self::kanbanAddLead($leadId, null, 'lead_add_filter_update', $extraData);
        }
        else{
            self::sendLead($leadId, null, true, ['type'=> 'lead_update']);
        }
    }

    public static function kanbanUpdateLeadAmountRequested($leadId, $values){
        self::kanbanAddLead($leadId, null, 'lead_update_amount_requested', $values);
    }

    public static function invalidateBoards($partnerId, $partnerUserIds = null){
        if(!KanbanHelper::partnerHasKanbanV2Access($partnerId)){
            return;
        }
        if(!$partnerUserIds){
            $partnerUsersTable = TableRegistry::getTableLocator()->get('PartnerUserEntity');
            $query = $partnerUsersTable->find()
                ->select(['PartnerUserEntity.partner_user_id'])
                ->where(['PartnerUserEntity.partner_id' => $partnerId]);
            $partnerUserIds = $query->all()->extract('partner_user_id')->toList();
        }
        if(count($partnerUserIds) > 0){
            $msg = [
                'type'=> 'invalidate_kanban',
                'data' => [] 
            ];
            self::sendMessage($partnerUserIds, 'kanban', $msg);
        }
    }

    public static function kanbanMoveLeadTop($leadId, $excludeSocketId = null, $partnerUserId = null, $oldManStatusGroupId){
        self::kanbanMoveLead('lead_move_top', $leadId, $excludeSocketId, $partnerUserId, $oldManStatusGroupId);
    }

    public static function kanbanMoveLeadSpecificPosition($leadId, $excludeSocketId = null, $partnerUserId = null, $oldManStatusGroupId, $relativeLeadRef, $relativeLeadLocation){
        self::kanbanMoveLead('lead_move_specific', $leadId, $excludeSocketId, $partnerUserId, $oldManStatusGroupId, $relativeLeadRef, $relativeLeadLocation);
    }

    private function kanbanMoveLead($moveType, $leadId, $excludeSocketId = null, $partnerUserId = null, $oldManStatusGroupId, $relativeLeadRef = null, $relativeLocation = null){
        $options = [
            'fields' => ['lead_ref', 'man_status_id', 'product_type_id'],
            'contain' => [
                'ManStatusEntity'=> ['fields' => ['id', 'man_status_group_id']],
                'ManStatusEntity.ManStatusGroupEntity'=> ['fields' => ['id']],
            ]
        ];
        $lead = TableRegistry::getTableLocator()->get('LeadEntity')->get($leadId, $options);
        $msg = [
            'type'=> $moveType,
            'lead_ref' => $lead->lead_ref,
            'from_group_id' => $oldManStatusGroupId,
            'to_group_id' => $lead->man_status->man_status_group->id,
            'product_type_id'=> $lead->product_type_id
        ];
        if($relativeLeadRef){
            $msg['relative_lead_ref'] = $relativeLeadRef;
            $relativeLeadId = (new LendInternalAuth)->unhashLeadId($relativeLeadRef);
            $relativeLeadData =  self::getLeadData($relativeLeadId);
            $msg['relative_lead_filters'] = $relativeLeadData['filters'];
        }
        if($relativeLocation){
            $msg['relative_location'] = $relativeLocation;
        }
        if($excludeSocketId){
            $msg['exclude'] = $excludeSocketId;
        }
        self::sendLead($leadId, $partnerUserId?[$partnerUserId]:null, true, $msg);
    }

    public static function kanbanRemoveLead($leadId, $leadRef, $oldManStatusId, $oldProductTypeId, $userIds = null, $filterDataUpdate = false){
        if($oldManStatusId){//cannot remove unless this is not null
            $manStatusTable = TableRegistry::getTableLocator()->get('ManStatusEntity');
            $oldManStatus = $manStatusTable->get($oldManStatusId);
            $leadData = self::getLeadData($leadId);
            $msg = [
                'type'=> (($filterDataUpdate == true) ? 'lead_remove_filter_update':'lead_remove'),
                'data' => [
                    'lead_ref' => $leadRef,
                    'lead' => $leadData['lead'],
                    'from_group_id' => $oldManStatus->man_status_group_id,
                    'product_type_id' => $oldProductTypeId
                ] 
            ];
            Log::debug("kanbanRemoveLead msg");
            Log::debug($msg);
            $r = self::sendMessage($userIds??self::getLeadUsers($leadId, true), 'kanban', $msg);
        }
    }

    private static function sendLead($leadId, $partnerUserIds = null, $includeAccessUsers = false, $messageData = []){
        $message = ['type' => $messageData['type']];
        unset($messageData['type']);
        $message['data'] = $messageData;
        $leadData = self::getLeadData($leadId);
        $message['data']['lead'] = $leadData['lead'];
        $message['data']['lead_filter'] = $leadData['filters'];
        self::sendMessage($partnerUserIds??self::getLeadUsers($leadId, $includeAccessUsers), 'kanban', $message);
    }

    /**
     * Create lead data array in the format expected by UI Apps front end
     */
    public static function getLeadData($leadId){
        $fields = [
            'lead' => [
                'lead_ref',
                'lead_type',
                'organisation_name',
                'amount_requested',
                'last_changed_date',
                'status' => 'man_status.status_name',
                'status_id' => 'man_status_id',
                'status_group_id' => 'man_status.man_status_group_id',
                'status_group_colour' => 'man_status.man_status_group.color',
                // 'is_off_panel',//custom logic - added in post processing
                'lender' => 'lead_associated_data.lender_name',
                'lender_logo' => 'lead_associated_data.lender.lender_logo',
                'poc_first_name' => 'owner_poc.first_name',
                'poc_last_name' => 'owner_poc.last_name',
                'poc_mobile' => 'owner_poc.mobile',
                'poc_phone' => 'owner_poc.phone',
                'poc_avatar_image_url' => 'owner_poc.avatar_image_url',
                'assignee' => 'partner_user_lead.user.name',
                'assignee_kanban_colour' => 'partner_user_lead.user.kanban_colour',
                'assignee_user_id' => 'partner_user_lead.partner_user_id',
                'latest_note' => 'lead_associated_data.note.notes',
                'latest_note_created' => 'lead_associated_data.note.created',
                'tag_colour' => 'tag.color',
                'tag' => 'tag.tag',
                'product_type_name' =>  'partner_product_type.product_type_name',
                'swimlane_id' => 'partner_product_type.kanban_swimlane_product_types.0.kanban_swimlane_id'

            ],
            'filters' => [
                'created',
                'last_changed_date',
                'status' => 'man_status_id',
                'customer_type' => 'lead_type',
                'assignee' => 'partner_user_lead.partner_user_id',
                'product_type' => 'product_type_id',
                'lender' => 'lead_associated_data.lender_name',
                'tag_id',
                'referrer' => 'referrer_person_id',
            ]
        ];
        
        $leadTable = TableRegistry::getTableLocator()->get('LeadEntity');
        $options = [
            'fields' => [
                'lead_id',
                'lead_ref',
                'organisation_name',
                'amount_requested',
                'last_changed_date',
                'created',
                'man_status_id',
                'lead_type',
                'product_type_id',
                'tag_id',
                'referrer_person_id',
            ],
            'contain' => [
                'ManStatusEntity' => ['fields' => ['id', 'status_name', 'man_status_group_id']],
                'ManStatusEntity.ManStatusGroupEntity' => ['fields' => ['ManStatusGroupEntity.id','color']],
                'LeadAssociatedDataEntity' => ['fields' => ['lead_id', 'lender_name', 'max_lender_id', 'max_note_id']],
                'LeadAssociatedDataEntity.LenderEntity' => ['fields' => ['lender_id', 'lender_logo']],
                'LeadAssociatedDataEntity.LeadNotesEntity' => ['fields' => ['note_id', 'notes', 'created']],
                'PocOwner' => ['fields' => ['lead_id', 'first_name', 'last_name', 'mobile', 'phone', 'avatar_image_url']],
                'PartnerUserLeadsEntity' => ['fields' => ['lead_id', 'partner_user_id']],
                'PartnerUserLeadsEntity.PartnerUserEntity' => ['fields' => ['partner_user_id', 'name', 'kanban_colour']],
                'PartnerTagEntity'=> ['fields' => ['id', 'tag', 'color']],
                'PartnerProductTypeEntity'=> ['fields' => ['product_type_id', 'product_type_name']],
                'PartnerProductTypeEntity.KanbanSwimlaneProductTypesEntity'=> ['fields' => ['product_type_id', 'kanban_swimlane_id']],
            ]

        ];
        $lead = $leadTable->get($leadId, $options);
        $data = ['lead' => [], 'filters' => []];
        foreach ($fields as $type => $fieldList) {
            foreach ($fieldList as $alias => $path) {
                if (is_numeric($alias)) {
                    $alias = $path;
                }

                $segments = explode('.', $path);
                $value = $lead;
                foreach ($segments as $segment) {
                    // $value = $value->$segment ?? null;
                    if (is_array($value) && is_numeric($segment)) {
                        $segment = (int)$segment; // Convert to integer index
                        $value = $value[$segment] ?? null;
                    } elseif (is_object($value)) {
                        $value = $value->$segment ?? null;
                    } else {
                        $value = null;
                    }
                    if ($value === null) {
                        break;
                    }
                }
                $data[$type][$alias] = $value;
            }
        }
        $data['filters']['is_off_panel'] = (($data['filters']['lender_id'] ?? null) === 30);
        $data['lead']['is_off_panel'] = $data['filters']['is_off_panel'];
        //get funded date(max)
        $partnerCommissionsTable = TableRegistry::getTableLocator()->get('PartnerCommissions');
        $maxCommission = $partnerCommissionsTable
            ->find()
            ->where(['lead_id' => $leadId, 'is_active'=> true])
            ->order(['commission_id' => 'DESC'])
            ->select(['funded_date'])
            ->first();
        $data['filters']['funded_date'] = $maxCommission ? $maxCommission->funded_date : null;

        unset($data['filters']['lender_id']);
        return $data;
    }
}