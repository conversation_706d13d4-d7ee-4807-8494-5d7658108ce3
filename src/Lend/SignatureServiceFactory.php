<?php

namespace App\Lend;

use App\Enums\LendSignTemplateUse;
use Cake\Datasource\ModelAwareTrait;
use Hashids\Hashids;

class SignatureServiceFactory
{

    use ModelAwareTrait;

    public $useLendSignatureService;
    protected $partnerUser;
    protected $templateUse;

    public function __construct($partnerUser, $templateUse)
    {
        $this->useLendSignatureService = false;
        $this->partnerUser = $partnerUser;
        $this->templateUse = $templateUse;
        $partnerFlags = $this->loadModel('PartnerFeatureFlags')->getFeatureFlag(['partner_id' => $partnerUser['partner_id']]);
        if ($partnerFlags['use_lend_signature_service']) {
            $this->useLendSignatureService = true;
        }
    }

    protected function callDocuSignService($params)
    {
        switch ($this->templateUse) {
            case LendSignTemplateUse::CreditGuideAndQuote:
                $leadRef = $params['leadRef'];
                $requestOwnerIds = $params['requestOwnerIds'];

                $requestBuilder = new SignatureServiceRequestBuilder($leadRef, $this->partnerUser);
                $requestBody = $requestBuilder
                ->setCommon()
                ->setCallback(getenv('DOMAIN_BRO').'/lead-apis/v2/track-recipient/credit-guide')
                ->setDescription('CreditQuote for leadRef ' . $leadRef)
                ->buildBusiness()
                ->addConsumerPrivacyFormTemplate()
                ->addCreditQuoteTemplate()
                ->buildRecipients($requestOwnerIds)
                ->buildEmail()
                ->buildMeta($requestOwnerIds)
                ->buildConPartnerUserSettings()
                ->buildFeesTables()
                ->addExternalEntityFields()
                ->addConsumerLendersTableData()
                ->addTopLenders()
                ->wordWrapToAddresses()
                ->getRequestBody();

                $sign = new SignatureService('new_envelope');
                return $sign->callService($requestBody);
            default:
                throw new \Exception('Invalid template use');
        }
    }

    protected function convertPartnerUserIdsToRefs($requestOwnerIds)
    {
        $requestOwnerRefs = [];
        foreach ($requestOwnerIds as $id) {
            $requestOwnerRefs[] = LendInternalAuth::hashOwnerId($id);
        }
        return $requestOwnerRefs;
    }

    public function getSignatureTemplatePayload($params)
    {
        if (!empty($params['requestOwnerIds'])) {
            $params['requestOwnerRefs'] = $this->convertPartnerUserIdsToRefs($params['requestOwnerIds']);
        }
        switch ($this->templateUse) {
            case LendSignTemplateUse::CreditGuideAndQuote:
                $leadRef = $params['leadRef'];
                $requestOwnerIds = $params['requestOwnerIds'];
                $requestBuilder = new SignatureServiceRequestBuilder($leadRef, $this->partnerUser);
                $data = $requestBuilder
                    ->setFlowVariationOf(LendSignTemplateUse::CreditGuideAndQuote)
                    ->setCallback(getenv('DOMAIN_BRO') . '/lead-apis/v2/track-recipient/credit-guide')
                    ->setDescription('CreditQuote for leadRef ' . $leadRef)
                    ->buildBusiness()
                    ->buildLead()
                    ->buildPartnerAndUser()
                    ->buildRecipientsNew($requestOwnerIds)
                    ->buildOrgLevelLead()
                    ->addVia($params)
                    ->buildEmail()
                    ->buildMeta($requestOwnerIds)
                    ->buildConPartnerUserSettings()
                    ->buildFees()
                    ->addPayloadFields()
                    ->wordWrapToAddresses()
                    ->formatForLendSignatureService($requestOwnerIds)
                    ->getRequestBody();

                $data['meta_data']['type'] = 'lead';

                return $data;
            case LendSignTemplateUse::CommercialPrivacyForms:
                $leadRef = $params['leadRef'];
                $requestOwnerIds = $params['requestOwnerIds'];
                $requestBuilder = new SignatureServiceRequestBuilder($leadRef, $this->partnerUser);
                $data = $requestBuilder
                    ->setCommon()
                    ->setCallback(getenv('DOMAIN_BRO') . '/lead-apis/v2/track-recipient/privacy-form')
                    ->setDescription('Privacy Form request from ' . $this->partnerUser['partner_user_id'] . ' LeadRef ' . $leadRef)
                    ->buildLead()
                    ->buildPartnerAndUser()
                    ->buildRecipientsNew($requestOwnerIds)
                    ->buildOrgLevelLead()
                    ->addVia($params)
                    ->buildEmail()
                    ->buildMeta($requestOwnerIds)
                    ->getRequestBody();
                $data['flow'] = LendSignTemplateUse::CommercialPrivacyForms;
                $data['meta_data']['type'] = 'lead';

                return $data;
            case LendSignTemplateUse::AdhocLead:
                $leadRef = $params['ref'];
                $requestOwnerIds = $params['requestOwnerIds'];
                $nonApplicantRefs = [];
                foreach ($params['nonApplicantRecipients'] as $ref => $recipient) {
                    $nonApplicantRefs[] = $ref;
                }
                $requestBuilder = new SignatureServiceRequestBuilder($leadRef, $this->partnerUser);
                $data = $requestBuilder
                    ->setCallback(getenv('DOMAIN_BRO') . '/lead-apis/v2/track-recipient/adhoc')
                    ->setDescription('Signature Requested for lead ' . $leadRef)
                    ->buildBusiness()
                    ->buildLead()
                    ->buildPartnerAndUser()
                    ->buildRecipientsNew($requestOwnerIds)
                    ->buildOrgLevelLead()
                    ->buildNonApplicantRecipients($params['nonApplicantRecipients'] ?? [])
                    ->addVia($params)
                    ->buildEmail()
                    ->buildMeta($requestOwnerIds, $nonApplicantRefs)
                    ->buildConPartnerUserSettings()
                    ->buildFees()
                    ->addPayloadFields()
                    ->addLendersTableData()
                    ->addTopLenders()
                    ->wordWrapToAddresses()
                    ->formatForLendSignatureService($requestOwnerIds)
                    ->getRequestBody();
                $data['flow'] = LendSignTemplateUse::AdhocLead;
                $data['meta_data']['flow'] = LendSignTemplateUse::AdhocLead;
                $data['meta_data']['type'] = 'lead';
                $data['templateRef'] = $params['templateRef'];
                return $data;
            case LendSignTemplateUse::AdhocApplicant:
                $applicantRef = $params['ref'];
                $requestOwnerIds = $params['requestOwnerIds'];
                $requestBuilder = new SignatureServiceRequestBuilder(null, $this->partnerUser, $applicantRef);
                $data = $requestBuilder
                    ->setCallback(getenv('DOMAIN_BRO') . '/lead-apis/v2/track-recipient/adhoc')
                    ->setDescription('Signature Requested for applicant ' . $applicantRef)
                    ->buildAccount()
                    ->buildApplicantPartnerAndUser()
                    ->buildApplicantRecipientsNew($requestOwnerIds)
                    ->addVia($params)
                    ->buildApplicantEmail()
                    ->buildApplicantMeta($applicantRef)
                    ->getRequestBody();
                $data['flow'] = LendSignTemplateUse::AdhocApplicant;
                $data['meta_data']['flow'] = LendSignTemplateUse::AdhocApplicant;
                $data['meta_data']['type'] = 'applicant';
                $data['templateRef'] = $params['templateRef'];
                return $data;
            case LendSignTemplateUse::CreditProposal:
                $leadRef = $params['leadRef'];
                $requestOwnerIds = $params['requestOwnerIds'];
                $requestBuilder = new SignatureServiceRequestBuilder($leadRef, $this->partnerUser);
                $data = $requestBuilder
                    ->setCallback(getenv('DOMAIN_BRO') . '/lead-apis/v2/credit-proposal/send-by-lend-signature')
                    ->setDescription('Credit Proposal for leadRef ' . $leadRef)
                    ->buildBusiness()
                    ->buildLead()
                    ->buildPartnerAndUser()
                    ->buildRecipientsNew($requestOwnerIds)
                    ->buildOrgLevelLead()
                    ->buildEmail()
                    ->buildMeta($requestOwnerIds)
                    ->buildConPartnerUserSettings()
                    ->buildCreditProposal()
                    ->buildCreditProposalFees()
                    ->buildReferrer()
                    ->wordWrapToAddresses()
                    ->formatForLendSignatureService($requestOwnerIds)
                    ->getRequestBody();
                $data['flow'] = LendSignTemplateUse::CreditProposal;
                $data['meta_data']['flow'] = LendSignTemplateUse::CreditProposal;
                return $data;
            case LendSignTemplateUse::NonSignatureCreditGuide:
                $leadRef = $params['leadRef'];
                $requestOwnerIds = $params['requestOwnerIds'];
                $requestBuilder = new SignatureServiceRequestBuilder($leadRef, $this->partnerUser);
                $data = $requestBuilder
                    ->setFlowVariationOf(LendSignTemplateUse::NonSignatureCreditGuide)
                    ->setCallback(null)
                    ->setDescription('Non Signature Credit Guide for leadRef ' . $leadRef)
                    ->buildBusiness()
                    ->buildLead()
                    ->buildOrgLevelLead()
                    ->buildPartnerAndUser()
                    ->buildRecipientsNew($requestOwnerIds)
                    ->addVia($params)
                    ->buildEmail()
                    ->buildMeta($requestOwnerIds)
                    ->buildConPartnerUserSettings()
                    ->buildFees()
                    ->addPayloadFields()
                    ->wordWrapToAddresses()
                    ->formatForLendSignatureService($requestOwnerIds)
                    ->getRequestBody();

                $data['meta_data']['type'] = 'lead';
                return $data;
            default:
                throw new \Exception('Invalid template use');
        }
    }

    public function generatePreview($payload)
    {
        $partner = $this->loadModel('Partners')->getPartner(['partner_id' => $this->partnerUser['partner_id']]);
        $partnerRef = LendInternalAuth::hashPartnerId($partner['partner_id']);
        $userRef = $this->partnerUser['partner_user_ref'];
        $sign = new LendSignatureServiceBackendClient($partnerRef, $userRef);
        $sign->checkCreateLendSignatureUser($this->partnerUser);
        $preview = $sign->callWithSignature('POST', '/generate-pdf', $payload);
        $signatures = $sign->generateSignature($preview['ref']);
        return ['ref' => $preview['ref'], 'pdf' => $preview['pdf'] ?? '', 'signatures' => $signatures];
    }

    public function callLendSignatureService($params)
    {
        if (!empty($params['requestOwnerIds'])) {
            $params['requestOwnerRefs'] = $this->convertPartnerUserIdsToRefs($params['requestOwnerIds']);
        }
        $partner = $this->loadModel('Partners')->getPartner(['partner_id' => $this->partnerUser['partner_id']]);
        $partnerRef = LendInternalAuth::hashPartnerId($partner['partner_id']);
        $userRef = $this->partnerUser['partner_user_ref'];
        $sign = new LendSignatureServiceBackendClient($partnerRef, $userRef);
        $sign->checkCreateLendSignatureUser($this->partnerUser);
        $data = $this->getSignatureTemplatePayload($params);
        $requestParams = [];
        $requestParams['flow'] = $data['flow'];
        $requestParams['dataJson'] = json_encode($data);
        return $sign->callWithSignature('POST', '/start-flow', $requestParams);
    }

    public function callService($params)
    {
        if ($this->useLendSignatureService) {
            return $this->callLendSignatureService($params);
        }
        return $this->callDocuSignService($params);
    }
}
