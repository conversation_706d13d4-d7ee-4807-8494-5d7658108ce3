<?php

namespace App\Lend;
use Cake\ORM\TableRegistry;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Client;
use Cake\Log\Log;

class LendSignatureServiceBackendClient
{
    protected $apiKey;
    protected $partnerRef;
    protected $userRef;
    protected $httpClient;

    public function __construct($partnerRef, $userRef = null, $httpClient = null)
    {
        $this->partnerRef = $partnerRef;
        $this->userRef = $userRef;
        $this->apiKey = getenv('LEND_SIGNATURE_API_KEY');
        if (empty($httpClient)) {
            $this->httpClient = new Client();
        }
    }

    public function checkCreateLendSignatureUser($user)
    {
        if (!$user['lend_signature_user_ref']) {
            $data = TableRegistry::getTableLocator()->get('PartnerUsers')->getUserInfoForSignature($user);
            $signUser = $this->call('POST', '/users', ['data' => $data]);
            if ($signUser) {
                TableRegistry::getTableLocator()->get('PartnerUsers')->updatePartnerUser($user['partner_user_id'], ['lend_signature_user_ref' => $signUser['ref']]);
            }
        }
    }

    public function call($method, $endpoint, $params = null, $userGroupRef = null)
    {
        if (empty(getenv('DOMAIN_LEND_SIGNATURE_API')) && getenv('LEND_ENV') == '0') {
            return false;
        }
        $headers = [
            'Content-Type' => 'application/json',
            'x-user-group-ref' => $this->partnerRef,
            'x-api-key' => $this->apiKey,
        ];
        if (!empty($this->userRef)) {
            $headers['x-user-ref'] = $this->userRef;
        }

        if (!empty($userGroupRef)) {
            $headers['x-user-group-ref'] = $userGroupRef;
        }

        if (!empty($params)) {
          $request = new Request(
            $method,
            getenv('DOMAIN_LEND_SIGNATURE_API') . $endpoint,
            $headers,
            json_encode($params)
          );
        }
        else {
          $request = new Request(
            $method,
            getenv('DOMAIN_LEND_SIGNATURE_API') . $endpoint,
            $headers
          );
        }

        $response = $this->httpClient->send($request);

        $responseBodyRaw = $response->getBody()->getContents();
        $responseBody = json_decode($responseBodyRaw, true);
        if (!empty($responseBody['status']) && $responseBody['status'] === 'success') {
            return $responseBody['data'];
        }
        return false;
    }

    public static function generateSignature($params, $signatureFields = null)
    {
        $secret = getenv('LEND_SIGNATURE_API_SECRET');

        $paramsForSignature = $params;
        if (!empty($signatureFields)) {
            $paramsForSignature = [];
            foreach ($signatureFields as $field) {
                $paramsForSignature[$field] = $params[$field];
            }
        }
        if (!is_array($paramsForSignature)) {
            $stringToSign = $paramsForSignature;
        } else {
            $stringToSign = json_encode($paramsForSignature, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        }
        $stringToSign .= $secret;

        return base64_encode(hash_hmac('sha256', $stringToSign, $secret));
    }

    public static function checkSignature($params, $signature, $signatureFields = null)
    {
        if (isset($params['signature'])) {
            unset($params['signature']);
        }
        return $signature === self::generateSignature($params, $signatureFields);
    }

    public function callWithSignature($method, $endpoint, $params, $signatureFields = null)
    {
        $params['signature'] = $this->generateSignature($params, $signatureFields);
        if (!empty($signatureFields)) {
            $params['signature_fields'] = $signatureFields;
        }
        return $this->call($method, $endpoint, $params);
    }

    public static function shortenUrl($url, $expire = null)
    {
        $client = new Client();
        $headers = [
            'Content-Type' => 'application/json',
            'x-api-key' => getenv('LEND_SIGNATURE_API_KEY')
        ];
        $payload = [
            'link' => $url,
            'expire' => $expire ? $expire : date('Y-m-d H:i:s', strtotime('+5 day'))
        ];
        $response = $client->post(getenv('DOMAIN_LEND_SIGNATURE_API') . '/shorten', [
            'headers' => $headers,
            'json' => $payload
        ]);
        $data = json_decode($response->getBody()->getContents(), true);
        if ($data['status'] !== 'success') {
            Log::error("generate short link failed: payload: " . json_encode($payload) . " response: " . json_encode($data));
            return '';
        }
        return $data['data']['shortLink'];
    }
}
