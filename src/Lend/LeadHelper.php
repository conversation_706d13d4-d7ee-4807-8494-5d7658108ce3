<?php

namespace App\Lend;

use Cake\ORM\TableRegistry;
use Cake\Log\Log;
use Hashids\Hashids;
use Exception;

class LeadHelper
{

    /**
     * Add a lead
     */
    public static function addLead($lead_data, $user = null, $partnerId = null)
    {
        if (empty($user) && empty($partnerId)) {
            throw new Exception("User or partner must be provided");
        }
        $partners_table = TableRegistry::getTableLocator()->get('PartnerEntity');
        $partner = $partners_table->get($user ? $user['partner_id'] : $partnerId);

        if ($lead_data['lead_type'] == "consumer" && !isset($lead_data['product_type_id'])) {
            $lead_data['product_type_id'] = 25;
        }
        $lead_data['partner_id'] = $partner['partner_id'];
        $lead_data['source'] = isset($lead_data['source']) ? $lead_data['source'] : 'Partners';
        
        $lead_data['status_id'] = 1;
        $lead_data['partner_status_id'] = 1;
        $lead_data['call_me_first'] = (isset($lead_data['call_me_first']) ? $lead_data['call_me_first'] : $partner->call_me_first);
        $lead_data['purpose_id'] = (!empty($lead_data['purpose_id']) ? intval($lead_data['purpose_id']) : null);
        $lead_data['equipment_id'] = (!empty($lead_data['equipment_id']) ? intval($lead_data['equipment_id']) : null);

        $lead_data['send_type'] = !empty($lead_data['send_type'])
            ? $lead_data['send_type']
            : ($partner['send_type'] === 'auto' ? 'Auto' : 'Manual');

        $lead_data['force_send'] = (!empty($lead_data['force_send']) ? $lead_data['force_send'] : false);

        if (!empty($lead_data['abn_lookup'])) {
            $lead_data['abn'] = $lead_data['abn_lookup']['abn'] ?? null;
            $lead_data['acn'] = $lead_data['abn_lookup']['acn'] ?? null;
            $lead_data['business_type_abn'] = $lead_data['abn_lookup']['entity_type_code'] ?? null;
            $lead_data['r_state'] = $lead_data['abn_lookup']['state'] ?? null;
            $lead_data['organisation_name'] = $lead_data['abn_lookup']['organisation_name'] ?? null;
            $lead_data['business_name'] = $lead_data['abn_lookup']['business_name'] ?? null;
            $lead_data['company_registration_date'] = $lead_data['abn_lookup']['effective_from'] ?? null;
        }

        if (!empty($lead_data['nzbn_lookup'])) {
            $lead_data['nzbn_lookup']['original_payload'] = json_decode($lead_data['nzbn_lookup']['original_payload'], true);

            $nzbn_lookup_table = TableRegistry::getTableLocator()->get('NzbnLookupEntity');
            $nzbn_lookup = $nzbn_lookup_table->find('all')->where(['nzbn' => $lead_data['nzbn_lookup']['nzbn']])->first();
            if (!empty($nzbn_lookup)) {
                $nzbn_lookup_table->patchEntity($nzbn_lookup, $lead_data['nzbn_lookup']);
            } else {
                $nzbn_lookup = $nzbn_lookup_table->newEntity($lead_data['nzbn_lookup']);
            }
            $nzbn_lookup_table->save($nzbn_lookup);

            $lead_data['nzbn_id'] = $nzbn_lookup->id;
            $lead_data['abn'] = $lead_data['nzbn_lookup']['nzbn'];
            $lead_data['organisation_name'] = $lead_data['nzbn_lookup']['entity_name'];
            $lead_data['business_name'] = $lead_data['nzbn_lookup']['entity_name'];
            $lead_data['business_type_abn'] = $lead_data['nzbn_lookup']['entity_type_description'];
            $lead_data['company_registration_date_from_abnlookup'] = $lead_data['nzbn_lookup']['registration_date'];
            $lead_data['company_registration_date'] = $lead_data['nzbn_lookup']['registration_date'];
            unset($lead_data['nzbn_lookup']);
        }

        //start commercial react payload//
        if (!empty($lead_data['lead_owners'])) {
            $lead_data['owners_all'][0] = $lead_data['lead_owners'];
            unset($lead_data['lead_owners']);
        }
        //end commercial react payload//
        if (!empty($lead_data['owners_all'])) {
            $poc_owner_key = 0;
            foreach ($lead_data['owners_all'] as $key => $owner) {
                if (!empty($owner['point_of_contact'])) {
                    $poc_owner_key = $key;
                    break;
                }
            }
            $lead_data['owners_all'][$poc_owner_key]['point_of_contact'] = true;
        }

        if ($user && !empty($user['partner_user_id'])) {
            $lead_data['partner_user_lead'] = [
                'partner_user_id' => $user['partner_user_id'],
                'status' => 'ACCESS'
            ];
        }

        if (!empty($lead_data['broker_assigned'])) {
            $partner_user_ref = $lead_data['broker_assigned'];
            unset($lead_data['broker_assigned']);
            $partner_user = TableRegistry::getTableLocator()->get('PartnerUsers')->getPartnerUser(['partner_user_ref' => $partner_user_ref]);
            if ($partner_user) {
                $lead_data['partner_user_lead'] = [
                    'partner_user_id' => $partner_user['partner_user_id'],
                    'status' => 'ACCESS'
                ];
                $lead_data['partner_user_id'] = $partner_user['partner_user_id'];
            };
        }

        if (!empty($lead_data['from_ticknflick'])) {
            $lead_data['is_tick_and_flick'] = true;
        }

        // Set default term if it's empty:
        if (empty($lead_data['loan_term_requested_months'])) {
            if (empty($lead_data['product_type_id'])) {
                $lead_data['loan_term_requested_months'] = 12;
                $lead_data['loan_term_requested'] = 1;
            } else {
                $partner_product_types_table = TableRegistry::getTableLocator()->get('PartnerProductTypeEntity');
                $partner_product_type = $partner_product_types_table->get($lead_data['product_type_id']);
              if (!empty($partner_product_type)) {
                $lead_data['loan_term_requested_months'] = $partner_product_type->default_term;
                $default_term_years = $partner_product_type->default_term / 12;
                $lead_data['loan_term_requested'] = $default_term_years;
              } else {
                $lead_data['loan_term_requested_months'] = 12;
                $lead_data['loan_term_requested'] = 1;
              }
            }
        }

        unset($lead_data['lead_associated_data']);

        if (!empty($lead_data['asset_finance']['contract_type'])) {
            $lead_data['pricing']['application_type'] = 'e2e';
            $lead_data['pricing']['contract_type'] = strtolower($lead_data['asset_finance']['contract_type']) === 'low doc' ? 'low' : 'full';
        }

        //add referrer info and assign to nominated user (if present)
        if (isset($lead_data['referrer_person_ref'])) {
            $referrerPeopleTable = TableRegistry::getTableLocator()->get('ReferrerPeople');
            $person = $referrerPeopleTable->find()->where(['referrer_person_ref' => $lead_data['referrer_person_ref']])->first();
            if (!$person) {
                throw new \Exception("Invalid referrer_person_ref");
            }
            $lead_data['referrer_person_id'] = $person['id'];
            //if referrer_person_ref is present and source is APP-Consumer, then set source to Referrer_V2_iFrame
            if($lead_data['source'] == 'APP-Consumer'){
                $lead_data['source'] = 'Referrer_V2_iFrame';
            }
            $referrerSources = ['referrers', 'Referrer_V2_iFrame'];
            if ($lead_data['source'] && in_array(strtolower($lead_data['source']), $referrerSources)) {
                if (($lead_data['lead_type'] == "consumer") && !empty($person['assigned_consumer_partner_user_id'])) {
                    $lead_data['partner_user_lead'] = [
                        'partner_user_id' => $person['assigned_consumer_partner_user_id'],
                        'status' => 'ACCESS'
                    ];
                    //for man_status_history
                    $lead_data['partner_user_id'] = $person['assigned_consumer_partner_user_id'];
                } elseif (!empty($person['assigned_commercial_partner_user_id'])) {
                    $lead_data['partner_user_lead'] = [
                        'partner_user_id' => $person['assigned_commercial_partner_user_id'],
                        'status' => 'ACCESS'
                    ];
                    $lead_data['partner_user_id'] = $person['assigned_consumer_partner_user_id'];
                }
            }
        }

        $lead = TableRegistry::getTableLocator()->get('LeadEntity')->createLead($lead_data);

        if (!empty($lead_data['quote_ref'])) {
            $lender_match_requests_table = TableRegistry::getTableLocator()->get('LenderMatchRequestEntity');
            $quote = $lender_match_requests_table->find("all")->where(["match_ref" => $lead_data['quote_ref']])->first();
            if (!$quote) {
                throw new \Exception("Quote not found", 404);
            }
            $lender_match_requests_table->patchEntity($quote, ['converted_lead_id' => $lead->lead_id]);
            $lender_match_requests_table->save($quote);
        }

        return $lead->lead_ref;
    }

    public static function addLeadNote($lead_note_data)
    {
        $lead_notes_table = TableRegistry::getTableLocator()->get('LeadNotesEntity');
        $lead_note = $lead_notes_table->newEntity($lead_note_data);
        $lead_notes_table->save($lead_note);

        return $lead_note->note_id;
    }

    public function addLeadUploads($data)
    {
        if (empty($data['lead_ref'] || empty($data['owner_ref']))) {
            throw new \Exception("Missing required fields");
        }

        if (!empty($data['lead_ref'])) {
            $lead_id = (new LendInternalAuth)->unhashLeadId($data['lead_ref']);
        }
        if (!empty($data['owner_ref'])) {
            $hashids = new Hashids('lead_owners', 7);
            $owner_id = $hashids->decode($data['owner_ref'])[0];
        }

        $data['partner_lead_uploads']['lead_id'] = $lead_id;
        $data['partner_lead_uploads']['owner_id'] = @$owner_id;

        if ((string)(getenv('LEND_ENV') == '0' || (string)getenv('LEND_ENV') == '1') && $data['partner_lead_uploads']['full_path']) {
            $path = 'DevTeam/' . $data['partner_lead_uploads']['full_path'];
            $data['partner_lead_uploads']['full_path'] = $path;
          }

        $partnerLeadUploadsTable = TableRegistry::getTableLocator()->get('PartnerLeadUploadsEntity');
        $partner_lead_upload = $partnerLeadUploadsTable->newEntity($data['partner_lead_uploads'], [
            'associated' => ['PartnerLeadUploadsMetaEntity']
        ]);
        $partnerLeadUploadsTable->save($partner_lead_upload);

        return $partner_lead_upload->partner_lead_upload_id;
    }
}
