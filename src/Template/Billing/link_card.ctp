<form name="lend-billing" class="card-link-form">
  <input id="lend-billing-email" type="hidden" value="<?= $partner_user['email'] ?>" />
  <input id="lend-billing-phone" type="hidden" value="<?= $partner_user['phone'] ?? $partner_user['mobile'] ?>" />
  <div class="row-grid">
    <div class="col-grid">
      <div class="field-group">
        <label for="lend-billing-name">Name on Card</label>
        <input id="lend-billing-name" type="text" placeholder="Jane Doe" required="" autocomplete="name" class="fullwidth mb-0" />
      </div>
    </div>
  </div>
  <div class="row-grid mb-5 pb-3">
    <div class="col-grid">
      <label for="lend-billing-card">Card Details</label>
      <div id="lend-billing-card"></div>
    </div>
  </div>
  <button type="submit" disabled="disabled" class="btn btn-green">Link Card</button>
</form>
<script>
  var $stripe, elements;
  (function stripeInit() {
    try {
      $stripe = window.Stripe('<?= getenv('STRIPE_API_KEY') ?>');
      elements = $stripe.elements();
      var cardElement = elements.create('card', {
        iconStyle: 'solid',
        hidePostalCode: true,
        style: {
          base: {
            iconColor: '#ddd',
            color: '#4e4e4e',
            fontFamily: '"Gordita", "Helvetica Neue", Helvetica, Arial, sans-serif',
            fontSmoothing: 'antialiased',
            "::placeholder": {
              fontSize: '13px',
              color: '#ddd'
            }
          },
          invalid: {
            iconColor: '#DE1C1C',
            color: '#DE1C1C',
          },
        },
      });
      cardElement.mount('#lend-billing-card')
      bindFormEvents(cardElement)
    } catch (error) {
      console.log("catching stripe");
      if (window.Stripe) return;
      // Load StripeJs
      var script = document.createElement('script')
      script.src = 'https://js.stripe.com/v3/'
      script.onload = stripeInit;
      document.head.append(script);
    }
  }());

  var cardEvent;

  function useState() {
    var form = document.querySelector('form[name="lend-billing"]')
    var btn = form.querySelector('button[type="submit"]')
    var name = form.querySelector('input#lend-billing-name')
    var email = form.querySelector('input#lend-billing-email')
    var phone = form.querySelector('input#lend-billing-phone')
    return {
      $stripe: $stripe,
      cardEvent: cardEvent || {},
      elements: elements,
      form: form,
      btn: btn,
      name: name
    }
  }

  function resolveSubmitBtnState(event) {
    var state = useState()
    var name = state.name
    var cardEvent = state.cardEvent;
    var cardValid = cardEvent.complete && !cardEvent.error
    var isValid = cardValid && name.value

    if (isValid) state.btn.removeAttribute('disabled')
    else state.btn.setAttribute('disabled', true)
  }


  function showLinkCardMessageResponse(opts) {
    window.popup.loading(false)
    var type = opts.type || 'blue';
    var texts = opts.message || 'Something went wrong, please try again later.'
    var messageDom = function() {
      return '<div class="mt-0 alert ' + type + '">' +
        texts +
        '</div>' +
        '<div style="text-align:right" class="m-t-1">' +
        '<button onclick="window.location.reload();" class="button">OK</button>' +
        '</div>';
    }

    var popupTitle = opts.type === 'error' ? 'Error' : 'Success';
    window.popup.show({
      messageHtml: messageDom(),
      contentClass: 'bigger_quarter',
      tabTitleHtml: '<p class="m-b-0 popup_title"><b class="title">' + popupTitle + '</b></p>',
    });

  }

  function createStripeToken(payload) {
    var state = useState();

    // Use Stripe.js to create a token. We only need to pass the card Element
    // from the Element group in order to create a token. We can also pass
    // in the additional customer data we collected in our form.
    var card = state.elements.getElement('card')
    window.popup.loading(true)
    state.$stripe.createToken(card, payload).then(function(result) {
      if (result.token) {
        // We received a token, do stuff. 
        return window.ajaxManager.addReq({
          type: 'POST',
          url: '/billing/create-customer/',
          dataType: 'json',
          data: result.token,
          success: function(response) {
            // Do stuff
            if (window.location.pathname === "/partners/billing" && response.success) {
              window.popup.close();
              $(".creditCardDetails").text(response.data.card.brand + " (ending " + response.data.card.last4 + ")");
              $(".continueReg").removeClass("disabled");
              $(".creditCardDiv").show();
            } else if (response.success) {
              window.location.reload()
            } else {
              showLinkCardMessageResponse({
                type: 'error',
                message: response.message.message ?? 'Cannot link the card.'
              })
            }
          },
          error: function(error) {
            showLinkCardMessageResponse({
              type: error,
              message: error.message
            })
          }
        });
      }
      showLinkCardMessageResponse({
        type: 'error',
        message: (result.error || {}).message
      })
    });
  }

  function bindFormEvents(card) {
    var state = useState();
    state.name.addEventListener('change', function() {
      resolveSubmitBtnState()
    })
    card.on('change', function(event) {
      cardEvent = event
      resolveSubmitBtnState()
    })

    state.form.addEventListener('submit', function(e) {
      e.preventDefault();

      // Gather additional customer data we may have collected in our form.
      var name = state.name.value;

      // Email & Phone is not necessary for stripe
      // https://stripe.com/docs/js/tokens_sources/create_token?type=cardElement#stripe_create_token-data
      var additionalData = {
        name: name ? name.value : undefined
      };

      createStripeToken(additionalData)
    });
  }
</script>

<style>
  .card-link-form {
    margin: 0
  }

  .card-link-form [type="submit"] {
    width: 100%;
    font-size: 14px;
    padding-top: 3px;
    margin-bottom: 0;
  }

  #lend-billing-card {
    height: 38px;
    padding: 6px 10px;
    background-color: #fff;
    border: 1px solid #D1D1D1;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  #lend-billing-card:focus {
    border: 1px solid #33C3F0;
    outline: 0;
  }
</style>