<div id="dt_trans_table_filter" class="is-flex">

    <div class="viewing position-relative mr-3">
        <span style="line-height: 1.5;">
            <select class="inline-date-filter" onchange="applyTransFilter('time')" id="transactionsDateRange">
              <?php foreach ($timeTypes as $id => $type) { ?>
                  <option value="<?php echo $id ?>" <?php echo in_array(trim($type), $timeFiltersChecked) ? 'selected' : null ?>><?php echo $type; ?></option>
              <?php } ?>
            </select>
        </span>

        <div id="customRange" class="float-right m-x-auto-xs"
            <?php echo (in_array(trim('Custom Range'), $timeFiltersChecked)) ? null: 'style="display:none;"'?>>
            <div class="input-group input-daterange column filter-dates filter-trans">
                <input type="date" class="form-control date-range date-range-right no-datepicker" name="search[filters][custom][start_date]" id="customStartDate" value="<?php echo (!empty($customRange)) ? date('Y-m-d', strtotime($customRange->start_date)) : ''?>">
                <div class="input-group-addon"> - </div>
                <input type="date" class="form-control date-range date-range-left no-datepicker" name="search[filters][custom][end_date]" id="customEndDate" value="<?php echo (!empty($customRange)) ? date('Y-m-d', strtotime($customRange->end_date)) : ''?>">
            </div>
            <button onclick="applyTransFilter('time')" id="customRangeButton">↻</button>
        </div>
    </div>

    <div id="dash_FiltersIcon" class="filtersPsuedoInput filtersApplied billingTransItem button grey mr-3">
        Filters
        <span id="filterscount" class="badge"><?= (empty($filtersChecked)) ? 0 : (count($filtersChecked)) ?></span>
        <span class="arrow-down darkgrey"></span>

        <div id="trans_filter_list" class="filterList text-left" style="display: none;">
            <a class="closeFilterBox u-pull-right">×</a>
            <ul class="noliststyle">
                <li>
                    <label class="filterTitle">Report Type</label>
                    <div class="filterContent" id="filterStatusDiv">
                        <?php foreach ($types as $id => $type): ?>
                            <label class="inline customCheckbox" for="searchFilterStatus<?php echo $id ?>">
                                <input
                                        type="checkbox" id="searchFilterStatus<?php echo $id ?>"
                                        name="search[filter][status][]"
                                        value="<?php echo $id ?>" <?php echo in_array(trim($type), $filtersChecked) ? 'checked' : null ?>>
                                <label for="searchFilterStatus<?php echo $id ?>"><?php echo $type ?></label>

                                <span class="checkmark"></span></label>
                        <?php endforeach; ?>
                    </div>

                </li>
                <li class="float-right">
                    <button type="button" onclick="clearTransFilterButton('item')">Clear</button>
                    <button type="button" onclick="applyTransFilter('item')">Apply</button>
                </li>
            </ul>
        </div>
    </div>
    <div class="">
        <span style="line-height: 1.5;">
            <select class="inline-date-filter" onchange="gstToggle()" id="gstToggle">
              <option value="ex">ex GST</option>
              <option value="incl">incl GST</option>
            </select>
        </span>
    </div>
</div>


<script>
    function applyTransFilter(type) {
        let tab = "transaction-tab";
        let timeFilters = '';
        let itemFilters = '';
        let customRange = [];
        let filterTransJsonOld = JSON.parse(cookieGet('filter_billing'));
        let filterTransJson = {};
        filterTransJson[tab] = {};
        let dateRange = $('#transactionsDateRange').find(":selected").text();

        if(type === 'item'){
            filterTransJson[tab]['item_type'] = [];
            filterTransJson[tab]['date_type'] = (filterTransJsonOld[tab]) ? filterTransJsonOld[tab]['date_type'] : [];

        }else{
            filterTransJson[tab]['item_type'] = (filterTransJsonOld[tab]) ? filterTransJsonOld[tab]['item_type'] : [];
            filterTransJson[tab]['date_type'] = [];
        }

        $('#filterStatusDiv input').each(function () {
            if ($(this).is(':checked')) {
                if(type === 'item')
                    filterTransJson[tab].item_type.push($(this).val());
                itemFilters = (itemFilters) ? itemFilters + ',' + $(this).closest('label').text().trim() : $(this).closest('label').text().trim();
            }
        });

        if(dateRange === 'Custom Range'){
            if($('.date-range-right').val() === '' && $('.date-range-left').val() === ''){
                var date = new Date();
                var firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
                $('.date-range-right').val(formatDateTime(firstDay.toLocaleDateString("en-us", {timeZone: "Australia/Sydney"}).substring(0,10)));
                $('.date-range-left').val(formatDateTime(date.toLocaleDateString("en-us", {timeZone: "Australia/Sydney"}).substring(0,10)));
                return $('#customRange').show();
            }
            customRange = {"start_date": $('.date-range-right').val(), "end_date": $('.date-range-left').val()};
        }else{
            $('#customRange').hide();
        }
        if(type === 'time')
            filterTransJson[tab].date_type.push(dateRange);
        timeFilters = dateRange;


        $('.billingTransItem').removeClass('triggered');
        $('#date_filter_list').slideUp(200);

        cookieSet('filter_billing', JSON.stringify(filterTransJson), 1);
        billingTransFilter(itemFilters, timeFilters, customRange);
    }

    function clearTransFilterButton(type) {
        let filterTransJson = {};
        let tab = "transaction-tab";
        filterTransJson[tab] = {};

        if(type === 'item'){
            filterTransJson[tab]['item_type'] = [];

            $('#filterStatusDiv input').each(function () {
                if ($(this).is(':checked')) {
                    $(this).prop('checked', false);
                }
            });
        }else{
            filterTransJson[tab]['date_type'] = [];

            $('#timeFilterStatusDiv input').each(function () {
                if ($(this).val() != 0) {
                    $(this).prop('checked', false);
                }else{
                    $(this).prop('checked', true);
                    filterTransJson[tab]['date_type'] = ['0'];
                }

            });
        }

        cookieSet('filter_billing', JSON.stringify(filterTransJson), 1);

    }

    function formatDateTime(date){
        const dateParts = date.split("/");
        return dateParts[2] + '-' + ("0" + dateParts[0]).slice(-2) + '-' + ("0" + dateParts[1]).slice(-2);
    }

    function gstToggle(){
        if($("#gstToggle").val() == "ex"){
            $(".ex-gst").show();
            $(".incl-gst").hide();
        }else{
            $(".ex-gst").hide();
            $(".incl-gst").show();
        }
        transcriontDatatable.columns.adjust().draw();
    }

    gstToggle();

    // Fix for jQuery UI interfering with native HTML5 date inputs
    $(document).ready(function() {
        $('.no-datepicker').each(function() {
            if ($(this).hasClass('hasDatepicker')) {
                $(this).datepicker('destroy').removeClass('hasDatepicker');
            }
            this.type = 'date';
            $(this).css({
                'position': 'relative',
                'z-index': '1000',
                'pointer-events': 'auto'
            });
        });
        $('.no-datepicker').on('click focus', function(e) {
            if (this.type === 'date') {
                this.showPicker && this.showPicker();
            }
        });
    });

</script>
<style>
    #dt_trans_table_filter .button .badge {
        height: 18px;
        line-height: 1.4;
        font-size: 10px;
        font-weight: 600;
        padding: 3px 2px;
        margin: 0 2px;
        position: relative;
        transform: translateY(-1px);
        color: gray;
        background: #e3e3e3;
    }

    .button .badge {
        height: 18px;
        line-height: 1.4;
        font-size: 10px;
        font-weight: 600;
        padding: 3px 2px;
        margin: 0 2px;
        position: relative;
        transform: translateY(-1px);
        color: gray;
        background: #e3e3e3;

    }

    .date-range{
        font-size: 11px !important;
        position: relative;
    }

    .date-range-left{
        border-radius: 0 4px 4px 0 !important;
    }

    .date-range-right{
        border-radius: 4px 0 0 4px !important;
    }

    .inline-date-filter{
        appearance: none;
        border: 1px solid #c8c8c8 !important;
        background: #fff url(/img/greydownarrow.png) no-repeat right 10px center;
        background-size: 8px auto;
        height: 32px;
        padding: 0 30px 0 10px !important;
        font-size: .9em;
        border-radius: 4px;
        box-shadow: none;
        box-sizing: border-box;
        text-transform: none;
        max-width: 225px;
    }

    #customRange{
        position: absolute;
        top: 32px;
        border-top: none;
        border-bottom-left-radius: 4px;
        border-bottom-right-radius: 4px;
        margin: 2px 0 0 -5px;
        padding: 0 35px 2px 5px;
        color: #3c3c3c;
        max-width: 290px;
        z-index: 1000;
    }



    #customRangeButton{
        background: #00cf88;
        width: 30px;
        color: #fff;
        height: 30px;
        padding: 0 5px;
        border: #00bb7b;
        position: absolute;
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
        font-size: 1.3em;
        font-weight: normal;
        line-height: 33px;
        margin: 0;
    }

    .filter-trans{
        min-width: 195px;
    }

    #transactions_length{
        margin-bottom: 30px;
    }

    @media only screen and (max-width: 600px) {
        #transactions_length{
            width: 100% !important;
        }

        .inline-date-filter{
            max-width: 120px !important;
        }
    }

</style>