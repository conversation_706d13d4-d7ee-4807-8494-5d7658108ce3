<?php
if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on')
    $url = "https://";
else
    $url = "http://";

$url .= $_SERVER['HTTP_HOST'];
$url .= $_SERVER['REQUEST_URI'];

$parameter = basename($_SERVER['REQUEST_URI']);

$domain = getenv('DOMAIN_BRO', true) . '/partners/dashboard';

$btn_visible = true;


if (strpos($url, '/account')) {
    // $pageTitle =   "Account"; 
} elseif (strpos($url, '/leads/complete-application')) {
    $btn_visible = false;
} else {
}
?>

<?php if ($cakeController === 'LenderLeadUpdates') { ?>
    <header class="header bg-lend">
        <div class="container">
            <a id='mainNav_Logo'
                href="/"><?php echo $this->Html->image('partners_logo.svg', ['alt' => 'Lend Platform', 'class' => 'logo']) ?></a>
        </div>
    </header>
    <nav class="nav">
        <div class="container">
            <ul class="mainDesktopNav">
                <li><a id='subNav_AllLeads' href="/lender-lead-updates/all-leads">All Leads</a></li>
                <li><a id='subNav_Logout' href="/lender-lead-updates/logout">Logout</a></li>
            </ul>
        </div>
    </nav>


<?php } elseif (in_array($cakeAction, ['thankyou'])) { ?>
    <header class="header bg-lend">
        <div class="container">
            <a id='mainNav_Logo'
                href="/"><?php echo $this->Html->image('partners_logo.svg', ['alt' => 'Lend Platform', 'class' => 'logo']) ?></a>
        </div>
    </header>
<?php } elseif (!$user) { ?>
    <!-- No Nav -->
<?php } elseif ($cakeController === 'PartnerUsers' and in_array($cakeAction, ['login', 'resetPassword', 'forgotPassword'])) { ?>
    <!-- No Nav -->
<?php } elseif ($free_user) { ?>
    <?= $this->Element('header_basic') ?>
<?php } elseif ($cakeController === 'Partners' and $cakeAction === 'register') { ?>

    <div class="greyRegisterSteps">
        <div class="container">
            <div class="steps m-b-0 row text-dark">
                <div class="step step-1 onethird column text-center">
                    <div class="circle grey border-green"></div>
                    <span class="text-green">Your Details</span>
                </div>
                <div class="step step-2 onethird column text-center">
                    <div class="circle grey<?php if ($cakeAction === 'welcome') echo ' border-green'; ?>"></div>
                    <span <?php if ($cakeAction === 'welcome') echo 'class="text-green"'; ?>>Your Business</span>
                </div>
                <div class="step step-3 onethird column text-center">
                    <div class="circle grey"></div>
                    <span>Finished</span>
                </div>
                <div class="line-container position-absolute">
                    <div class="line"></div>
                    <div class="line border-green"
                        style="<?php if ($cakeAction === 'welcome') echo 'width:50%';
                                else echo 'width:0%'; ?>"></div>
                </div>
            </div>
        </div>
    </div>
<?php } else { ?>

    <nav class="dashboardSidebar <?= strpos($url, '/leads/view/') ? 'close' : ''; ?>"
        style="<?= strpos($url, '/view-notification') || strpos($url, '/partners/accreditation') ? 'display: none;' : "" ?>">
        <header>


            <div class="image-text">
                <span class="image">
                    <a href=<?= $access_to_kanban ? '/lead/dashboard/kanban' : '/partners/dashboard?manage=Leads' ?>>
                        <img src="/img/logos/lend_logo_blue.svg" /></a>
                </span>
            </div>
            <?php if (!empty($btn_visible)) { ?>
                <div id="subNav_OpenCreate" class="buttons-container">
                    <button class="addNewLeadButton createButton">
                        <div class="button-inner">
                            <span class="left-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-plus" width="20"
                                    height="20" viewBox="0 0 24 24" stroke="#FFF" stroke-width="2" fill="none"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <line x1="12" y1="5" x2="12" y2="19"></line>
                                    <line x1="5" y1="12" x2="19" y2="12"></line>
                                </svg>
                            </span>
                            <span class="button-label">
                                Create
                            </span>
                        </div>
                    </button>
                </div>
            <?php } ?>
        </header>

        <div class="dashboardMenuBar">
            <div class="menu">
                <?php if ($access_to_call_queue && $user['access_to_call_queue']): ?>
                    <li class="nav-links">
                        <a href="/leads-call-queue" class="nav-callqueue-link">
                            <i class='bx bx-tabler-phone icon'>
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-phone" width="20"
                                    height="20" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" fill="none"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path
                                        d="M5 4h4l2 5l-2.5 1.5a11 11 0 0 0 5 5l1.5 -2.5l5 2v4a2 2 0 0 1 -2 2a16 16 0 0 1 -15 -15a2 2 0 0 1 2 -2">
                                    </path>
                                </svg>
                            </i>
                            <span class="text nav-text">Call Queue</span>
                            <div class="nav-tooltip nav-tooltip-callqueue">
                                <div class="menu-arrow-tooltip"></div>
                                Call Queue
                            </div>
                            <span id="call_queue" class="">
                                <b></b>
                            </span>
                        </a>
                    </li>
                <?php endif ?>

                <?php if ($access_to_react_quick_quote): ?>
                    <li class="nav-links">
                        <a href="/quick-quotes" class="nav-callqueue-link">
                            <i class='bx bx-tabler-zoom-money icon'>
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-zoom-money"
                                    width="20" height="20" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"
                                    fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                                    <path d="M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0" />
                                    <path d="M21 21l-6 -6" />
                                    <path d="M12 7h-2.5a1.5 1.5 0 0 0 0 3h1a1.5 1.5 0 0 1 0 3h-2.5" />
                                    <path d="M10 13v1m0 -8v1" />
                                </svg>
                            </i>
                            <span class="text nav-text">Quick Quotes</span>
                            <div class="nav-tooltip nav-tooltip-quick-quotes">
                                <div class="menu-arrow-tooltip"></div>
                                Quick Quotes
                            </div>
                            <span id="quick_quotes" class="">
                                <b></b>
                            </span>
                        </a>
                    </li>
                <?php endif ?>

                <?php if ($access_to_account): ?>
                    <li class="nav-links">
                        <a href="/accounts" class="nav-accounts-link">
                            <i class='bx bx-home-alt icon'>
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-building-bank"
                                    width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke=" #000000" fill="none"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                                    <line x1="3" y1="21" x2="21" y2="21" />
                                    <line x1="3" y1="10" x2="21" y2="10" />
                                    <polyline points="5 6 12 3 19 6" />
                                    <line x1="4" y1="10" x2="4" y2="21" />
                                    <line x1="20" y1="10" x2="20" y2="21" />
                                    <line x1="8" y1="14" x2="8" y2="17" />
                                    <line x1="12" y1="14" x2="12" y2="17" />
                                    <line x1="16" y1="14" x2="16" y2="17" />
                                </svg>
                            </i>
                            <span class="text nav-text">Accounts</span>
                            <div class="nav-tooltip nav-tooltip-accounts">
                                <div class="menu-arrow-tooltip"></div>
                                Accounts
                            </div>
                        </a>
                    </li>

                    <li class="nav-links">
                        <a href="/account-applicants" class="nav-applicants-link">
                            <i class='bx bx-bar-chart-alt-2 icon'>
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-friends" width="20"
                                    height="20" viewBox="0 0 24 24" stroke-width="2" stroke=" #000000" fill="none"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                                    <circle cx="7" cy="5" r="2" />
                                    <path d="M5 22v-5l-1 -1v-4a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v4l-1 1v5" />
                                    <circle cx="17" cy="5" r="2" />
                                    <path d="M15 22v-4h-2l2 -6a1 1 0 0 1 1 -1h2a1 1 0 0 1 1 1l2 6h-2v4" />
                                </svg>
                            </i>
                            <span class="text nav-text">Applicants</span>
                            <div class="nav-tooltip nav-tooltip-applicants">
                                <div class="menu-arrow-tooltip"></div>
                                Applicants
                            </div>
                        </a>
                    </li>
                <?php endif; ?>
                <?php if ($access_to_kanban): ?>
                    <li>
                        <a class="nav-links nav-leads-link" href="/lead">
                            <i class='bx bx-bell icon'>
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-news" width="20"
                                    height="20" viewBox="0 0 24 24" stroke-width="2" stroke="#000000" fill="none"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path
                                        d="M16 6h3a1 1 0 0 1 1 1v11a2 2 0 0 1 -4 0v-13a1 1 0 0 0 -1 -1h-10a1 1 0 0 0 -1 1v12a3 3 0 0 0 3 3h11">
                                    </path>
                                    <line x1="8" y1="8" x2="12" y2="8"></line>
                                    <line x1="8" y1="12" x2="12" y2="12"></line>
                                    <line x1="8" y1="16" x2="12" y2="16"></line>
                                </svg>
                            </i>
                            <span class="text nav-text">
                                Leads
                            </span>
                            <div class="nav-tooltip nav-tooltip-leads">
                                <div class="menu-arrow-tooltip"></div>
                                Leads
                            </div>
                            <span class="text nav-text dropdown-btn" style="margin-left:auto;">
                                <svg xmlns="http://www.w3.org/2000/svg"
                                    class="arrow icon icon-tabler icon-tabler-chevron-down <?= strpos($url, '/lead/') ? 'dropdownContainer dashboard-rotated-down' : '' ?>"
                                    width="14" height="14" viewBox="0 0 24 24" stroke-width="2" stroke="#000000" fill="none"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <polyline points="9 6 15 12 9 18"></polyline>
                                </svg>
                            </span>
                        </a>
                    </li>
                <?php else: ?>
                    <li>
                        <a class="nav-links nav-leads-link" href="/partners/dashboard?manage=Leads">
                            <i class='bx bx-bell icon'>
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-news" width="20"
                                    height="20" viewBox="0 0 24 24" stroke-width="2" stroke="#000000" fill="none"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path
                                        d="M16 6h3a1 1 0 0 1 1 1v11a2 2 0 0 1 -4 0v-13a1 1 0 0 0 -1 -1h-10a1 1 0 0 0 -1 1v12a3 3 0 0 0 3 3h11">
                                    </path>
                                    <line x1="8" y1="8" x2="12" y2="8"></line>
                                    <line x1="8" y1="12" x2="12" y2="12"></line>
                                    <line x1="8" y1="16" x2="12" y2="16"></line>
                                </svg>
                            </i>
                            <span class="text nav-text">
                                Leads
                            </span>
                            <div class="nav-tooltip nav-tooltip-leads">
                                <div class="menu-arrow-tooltip"></div>
                                Leads
                            </div>
                            <span class="text nav-text dropdown-btn" style="margin-left:auto;">
                                <svg xmlns="http://www.w3.org/2000/svg"
                                    class="arrow icon icon-tabler icon-tabler-chevron-down <?= strpos($url, '/lead/') ? 'dropdownContainer dashboard-rotated-down' : '' ?>"
                                    width="14" height="14" viewBox="0 0 24 24" stroke-width="2" stroke="#000000" fill="none"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <polyline points="9 6 15 12 9 18"></polyline>
                                </svg>
                            </span>
                        </a>
                    </li>
                <?php endif; ?>
                <div class="dropdownContainer"
                    style="<?= strpos($url, '/partners/dashboard?manage') ? '' : 'display: none;' ?>">
                    <ul style="font-family: 'Inter', sans-serif;">
                        <?php echo $this->Element('dashboardnav', array('menuVersion' => 'desktop')) ?>
                    </ul>
                </div>
                <?php
                if ($user['level'] == 1) {
                    if ($access_to_react_commissions): ?>
                        <li class="nav-links commission-link">
                            <a href="/commission"
                                class="nav-commissions-link <?= strpos($url, '/commission') ? 'dashboard-active' : '' ?>">
                                <i class='bx bx-home-alt icon'>
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-currency-dollar"
                                        width="20" height="20" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"
                                        fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                        <path d="M16.7 8a3 3 0 0 0 -2.7 -2h-4a3 3 0 0 0 0 6h4a3 3 0 0 1 0 6h-4a3 3 0 0 1 -2.7 -2">
                                        </path>
                                        <path d="M12 3v3m0 12v3"></path>
                                    </svg>
                                </i>
                                <span
                                    class="text nav-text <?= strpos($url, '/commission') ? 'activeText' : '' ?>">Commissions</span>
                                <div class="nav-tooltip nav-tooltip-commissions">
                                    <div class="menu-arrow-tooltip"></div>
                                    Commissions
                                </div>
                            </a>
                        </li>

                    <?php else: ?>
                        <li class="nav-links commission-link">
                            <a href="/commissions"
                                class="nav-commissions-link <?= strpos($url, '/commissions') ? 'dashboard-active' : '' ?>">
                                <i class='bx bx-home-alt icon'>
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-currency-dollar"
                                        width="20" height="20" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"
                                        fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                        <path d="M16.7 8a3 3 0 0 0 -2.7 -2h-4a3 3 0 0 0 0 6h4a3 3 0 0 1 0 6h-4a3 3 0 0 1 -2.7 -2">
                                        </path>
                                        <path d="M12 3v3m0 12v3"></path>
                                    </svg>
                                </i>
                                <span
                                    class="text nav-text <?= strpos($url, '/commissions') ? 'activeText' : '' ?>">Commissions</span>
                                <div class="nav-tooltip nav-tooltip-commissions">
                                    <div class="menu-arrow-tooltip"></div>
                                    Commissions
                                </div>
                            </a>
                        </li>
                <?php endif;
                } ?>
                <?php if ($access_to_referrer): ?>
                    <li class="nav-links">
                        <a href="/referrers" class="nav-referrer-link">
                            <i class='bx bx-home-alt icon'>
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-building-bank"
                                    width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke=" #000000" fill="none"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M8 7a4 4 0 1 0 8 0a4 4 0 0 0 -8 0"></path>
                                    <path d="M6 21v-2a4 4 0 0 1 4 -4h3"></path>
                                    <path d="M16 22l5 -5"></path>
                                    <path d="M21 21.5v-4.5h-4.5"></path>
                                </svg>
                            </i>
                            <span class="text nav-text">Referrers</span>
                            <div class="nav-tooltip nav-tooltip-referrer">
                                <div class="menu-arrow-tooltip"></div>
                                Referrers
                            </div>
                        </a>
                    </li>
                <?php endif; ?>
                <?php if ($partner_feature['access_marketing'] ?? false): ?>
                    <li class="nav-links">
                        <a href="/marketing/emails"
                            class="nav-marketing-link <?= strpos($url, '/marketing/emails') ? 'dashboard-active' : '' ?>">
                            <i class='bx bx-home-alt icon'>
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-world" width="20"
                                    height="20" viewBox="0 0 24 24" stroke-width="2"
                                    stroke="<?= strpos($url, '/marketing/emails') ? '#5048E5' : '#2c3e50' ?>" fill="none"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <circle cx="12" cy="12" r="9"></circle>
                                    <line x1="3.6" y1="9" x2="20.4" y2="9"></line>
                                    <line x1="3.6" y1="15" x2="20.4" y2="15"></line>
                                    <path d="M11.5 3a17 17 0 0 0 0 18"></path>
                                    <path d="M12.5 3a17 17 0 0 1 0 18"></path>
                                </svg>
                            </i>
                            <span
                                class="text nav-text <?= strpos($url, '/marketing/emails') ? 'activeText' : '' ?>">Marketing</span>
                            <div class="nav-tooltip nav-tooltip-marketing">
                                <div class="menu-arrow-tooltip"></div>
                                Marketing
                            </div>
                        </a>
                    </li>
                <?php endif; ?>
                <?php if (!empty($access_broker_dash_react)): ?>
                <li class="nav-links">
                    <a href="/dashboard" class="nav-dashboard-link <?= $this->request->getPath() === '/dashboard' ? 'activeText' : '' ?>">
                        <i class='bx bx-home-alt icon'>
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-device-analytics"
                                width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor"
                                fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                <rect x="3" y="4" width="18" height="12" rx="1"></rect>
                                <line x1="7" y1="20" x2="17" y2="20"></line>
                                <line x1="9" y1="16" x2="9" y2="20"></line>
                                <line x1="15" y1="16" x2="15" y2="20"></line>
                                <path d="M8 12l3 -3l2 2l3 -3"></path>
                            </svg>
                        </i>
                        <span class="text nav-text">Dashboard</span>
                    </a>
                </li>
                <?php endif; ?>
                <?php if ($user['status_system'] != "manual") { ?>
                <li class="nav-links">
                    <a href="<?= $domain ?>"
                        class="nav-dashboard-link <?= strpos($url, '/partners/dashboard') && !(strpos($url, '/partners/dashboard?manage')) ? 'dashboard-active' : '' ?>">
                        <i class='bx bx-home-alt icon'>
                            <svg xmlns=" http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-device-analytics"
                                width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor"
                                fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                <rect x="3" y="4" width="18" height="12" rx="1"></rect>
                                <line x1="7" y1="20" x2="17" y2="20"></line>
                                <line x1="9" y1="16" x2="9" y2="20"></line>
                                <line x1="15" y1="16" x2="15" y2="20"></line>
                                <path d="M8 12l3 -3l2 2l3 -3"></path>
                            </svg>
                        </i>
                        <span class="text nav-text <?= $url === $domain ? 'activeText' : '' ?>">Reports</span>
                        <div class="nav-tooltip nav-tooltip-dashboard">
                            <div class="menu-arrow-tooltip"></div>
                            Reports
                        </div>
                    </a>
                </li>
                <?php } ?>
                <?php if ($access_to_automation): ?>
                    <li class="nav-links">
                        <a href="/automations" class="nav-automations-link">
                            <i class='bx bx-tabler-phone icon'>
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
                                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                    class="tabler-icon tabler-icon-bolt">
                                    <path d="M13 3l0 7l6 0l-8 11l0 -7l-6 0l8 -11"></path>
                                </svg>
                            </i>
                            <span class="text nav-text">Automations</span>
                            <div class="nav-tooltip nav-tooltip-automations">
                                <div class="menu-arrow-tooltip"></div>
                                Automations
                            </div>
                            <span class="text nav-text dropdown-btn" style="margin-left:auto;">
                                <svg xmlns="http://www.w3.org/2000/svg"
                                    class="arrow icon icon-tabler icon-tabler-chevron-down <?= strpos($url, '/lead/') ? 'dropdownContainer dashboard-rotated-down' : '' ?>"
                                    width="14" height="14" viewBox="0 0 24 24" stroke-width="2" stroke="#000000" fill="none"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <polyline points="9 6 15 12 9 18"></polyline>
                                </svg>
                            </span>
                        </a>
                    </li>
                <?php endif; ?>


            </div>

            <div class="bottom-content dashboardSeparator">
                <li class="">
                    <a href="/calendar" class="nav-tasks-link <?= strpos($url, '/calendar') ? 'dashboard-active' : '' ?>">
                        <i class='bx bx-log-out icon'>
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-checklist"
                                width="20" height="20" viewBox="0 0 24 24" stroke-width="2"
                                stroke="<?= strpos($url, '/calendar') ? '#5048E5' : '#2c3e50' ?>" fill="none"
                                stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                <path d="M9.615 20h-2.615a2 2 0 0 1 -2 -2v-12a2 2 0 0 1 2 -2h8a2 2 0 0 1 2 2v8"></path>
                                <path d="M14 19l2 2l4 -4"></path>
                                <path d="M9 8h4"></path>
                                <path d="M9 12h2"></path>
                            </svg>
                        </i>
                        <span class="text nav-text <?= strpos($url, '/calendar') ? 'activeText' : '' ?>">Calendar</span>
                        <div class="nav-tooltip nav-tooltip-tasks">
                            <div class="menu-arrow-tooltip"></div>
                            Calendar
                        </div>
                        <span id="tasks" class="">
                            <b></b>
                        </span>
                    </a>
                </li>
                <li class="">
                    <a id='mainNav_Notifications' onclick="toggleNotificationsPanel(event)"
                        class="navIcon notifs relative nav-notifications-link">
                        <i class='bx bx-log-out icon'>
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-bell" width="20"
                                height="20" viewBox="0 0 24 24" stroke-width="2" stroke=" #000000" fill="none"
                                stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                <path
                                    d="M10 5a2 2 0 0 1 4 0a7 7 0 0 1 4 6v3a4 4 0 0 0 2 3h-16a4 4 0 0 0 2 -3v-3a7 7 0 0 1 4 -6">
                                </path>
                                <path d="M9 17v1a3 3 0 0 0 6 0v-1"></path>
                            </svg>
                        </i>
                        <span class="text nav-text">Notifications</span>
                        <div class="nav-tooltip nav-tooltip-notifications">
                            <div class="menu-arrow-tooltip"></div>
                            Notifications
                        </div>
                        <span id="alert_tab" class="">
                            <b></b>
                        </span>
                    </a>
                </li>
                <li class="">
                    <a class="openManage nav-manage-link">
                        <i class='bx bx-log-out icon'>
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-settings" width="20"
                                height="20" viewBox="0 0 24 24" stroke-width="2" stroke=" #000000" fill="none"
                                stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                <path
                                    d="M10.325 4.317c.426 -1.756 2.924 -1.756 3.35 0a1.724 1.724 0 0 0 2.573 1.066c1.543 -.94 3.31 .826 2.37 2.37a1.724 1.724 0 0 0 1.065 2.572c1.756 .426 1.756 2.924 0 3.35a1.724 1.724 0 0 0 -1.066 2.573c.94 1.543 -.826 3.31 -2.37 2.37a1.724 1.724 0 0 0 -2.572 1.065c-.426 1.756 -2.924 1.756 -3.35 0a1.724 1.724 0 0 0 -2.573 -1.066c-1.543 .94 -3.31 -.826 -2.37 -2.37a1.724 1.724 0 0 0 -1.065 -2.572c-1.756 -.426 -1.756 -2.924 0 -3.35a1.724 1.724 0 0 0 1.066 -2.573c-.94 -1.543 .826 -3.31 2.37 -2.37c1 .608 2.296 .07 2.572 -1.065z">
                                </path>
                                <circle cx="12" cy="12" r="3"></circle>
                            </svg>
                        </i>
                        <span class="text nav-text">Manage</span>
                        <div class="nav-tooltip nav-tooltip-manage">
                            <div class="menu-arrow-tooltip"></div>
                            Manage
                        </div>
                    </a>
                </li>
                <li class="navIcon menu relative clickableItem">
                    <a class="openProfile nav-profile-link">
                        <div class="userImageDiv">
                            <?php if (empty($user['broker_image'])):  ?>
                                <p class="userInitials">
                                    <?= strtoupper(substr($user['names']['first_name'], 0, 1) . (!empty($user['names']['last_name']) ? substr($user['names']['last_name'], 0, 1) : '')) ?>
                                </p>
                            <?php else: ?>
                                <img class="userImage" src="<?= $user['broker_image']; ?>">
                            <?php endif; ?>
                        </div>

                        <span class="text nav-text" style="">
                            <div class="userCompany">

                                <?= $user['name'] ?>

                            </div>
                            <div class="userEmail">
                                <?= $user['email'] ?>

                            </div>
                        </span>
                        <div class="nav-tooltip nav-tooltip-profile">
                            <div class="menu-arrow-tooltip"></div>
                            Account
                        </div>

                        <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-chevron-right"
                            width="18" height="18" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none"
                            stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                            <polyline points="9 6 15 12 9 18"></polyline>
                        </svg>
                    </a>
                </li>
            </div>
        </div>
    </nav>
    <div class="dropdownMenu hidden-menu">
        <ul class="noliststyle subnav" id="dashboardMenu" data-test="<?= isset($test_var) ? $test_var : "" ?>"
            style="font-family: 'Inter', sans-serif;">
            <?php if (empty($settingsToUpdate) || !$settingsToUpdate): ?>
                <?= $this->Element('header_menulist_default') ?>
            <?php endif ?>
        </ul>
        <div class="menu-arrow"></div>
    </div>

    <div class="dropdownManageMenu hidden-menu">
        <ul class="noliststyle subnav" style="font-family: 'Inter', sans-serif;">
            <li class="dropdownTitle">Account</li>
            <?php if ($this->Lend->shallWeShowThisElement('lendersLink', $user)) { ?>
                <li>
                    <a id='mainMenu_Preferences' href="/lender-preferences">
                        <i>
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-settings" width="14"
                                height="14" viewBox="0 0 24 24" stroke-width="2" stroke=" #000000" fill="none"
                                stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                <path
                                    d="M10.325 4.317c.426 -1.756 2.924 -1.756 3.35 0a1.724 1.724 0 0 0 2.573 1.066c1.543 -.94 3.31 .826 2.37 2.37a1.724 1.724 0 0 0 1.065 2.572c1.756 .426 1.756 2.924 0 3.35a1.724 1.724 0 0 0 -1.066 2.573c.94 1.543 -.826 3.31 -2.37 2.37a1.724 1.724 0 0 0 -2.572 1.065c-.426 1.756 -2.924 1.756 -3.35 0a1.724 1.724 0 0 0 -2.573 -1.066c-1.543 .94 -3.31 -.826 -2.37 -2.37a1.724 1.724 0 0 0 -1.065 -2.572c-1.756 -.426 -1.756 -2.924 0 -3.35a1.724 1.724 0 0 0 1.066 -2.573c-.94 -1.543 .826 -3.31 2.37 -2.37c1 .608 2.296 .07 2.572 -1.065z">
                                </path>
                                <circle cx="12" cy="12" r="3"></circle>
                            </svg>
                        </i>
                        <span class="text nav-text">Preferences</span>
                    </a>
                </li>
            <?php } ?>
            <?php if ($access_to_consumer_leads): ?>
                <li>
                    <a id='mainMenu_consumer_settings' href="/settings/consumer">
                        <i>
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-settings" width="14"
                                height="14" viewBox="0 0 24 24" stroke-width="2" stroke=" #000000" fill="none"
                                stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                <path
                                    d="M10.325 4.317c.426 -1.756 2.924 -1.756 3.35 0a1.724 1.724 0 0 0 2.573 1.066c1.543 -.94 3.31 .826 2.37 2.37a1.724 1.724 0 0 0 1.065 2.572c1.756 .426 1.756 2.924 0 3.35a1.724 1.724 0 0 0 -1.066 2.573c.94 1.543 -.826 3.31 -2.37 2.37a1.724 1.724 0 0 0 -2.572 1.065c-.426 1.756 -2.924 1.756 -3.35 0a1.724 1.724 0 0 0 -2.573 -1.066c-1.543 .94 -3.31 -.826 -2.37 -2.37a1.724 1.724 0 0 0 -1.065 -2.572c-1.756 -.426 -1.756 -2.924 0 -3.35a1.724 1.724 0 0 0 1.066 -2.573c-.94 -1.543 .826 -3.31 2.37 -2.37c1 .608 2.296 .07 2.572 -1.065z">
                                </path>
                                <circle cx="12" cy="12" r="3"></circle>
                            </svg>
                        </i>
                        <span class="text nav-text">Consumer Settings</span>
                    </a>
                </li>
            <?php endif; ?>
            <?php if ($access_to_billing) { ?>
                <li>
                    <a id="mainMenu_ManageBilling" href="/billing/manage">
                        <i>
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-credit-card" width="14"
                                height="14" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" fill="none"
                                stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                <rect x="3" y="5" width="18" height="14" rx="3"></rect>
                                <line x1="3" y1="10" x2="21" y2="10"></line>
                                <line x1="7" y1="15" x2="7.01" y2="15"></line>
                                <line x1="11" y1="15" x2="13" y2="15"></line>
                            </svg>
                        </i>
                        <span class="text nav-text">Manage Billing</span>
                    </a>
                </li>
            <?php } ?>
            <li>
                <a id="mainMenu_Notifications" href="/notifications">
                    <i>
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-notification" width="14"
                            height="14" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" fill="none"
                            stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                            <path d="M10 6h-3a2 2 0 0 0 -2 2v9a2 2 0 0 0 2 2h9a2 2 0 0 0 2 -2v-3"></path>
                            <circle cx="17" cy="7" r="3"></circle>
                        </svg>
                    </i>
                    <span class="text nav-text">Notifications</span>
                </a>
            </li>
            <li>
                <a id='mainMenu_LeadForms' href="/partners/file-library">
                    <i>
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-folder" width="14" height="14" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                            <path d="M5 4h4l3 3h7a2 2 0 0 1 2 2v8a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2v-11a2 2 0 0 1 2 -2" />
                        </svg>
                    </i>
                    <span class="text nav-text">File Library</span>
                </a>
            </li>
            <?php if ($user['partner_type'] != 'Lender' && !empty($partner_feature['access_iframe_form'])) { ?>
                <?php if (!empty($partner_feature['access_to_react_full_app'])) { ?>
                <li>
                    <a id='mainMenu_LeadForms' href="/settings/lead-forms">
                        <i>
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-article" width="14"
                                height="14" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" fill="none"
                                stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                <rect x="3" y="4" width="18" height="16" rx="2"></rect>
                                <path d="M7 8h10"></path>
                                <path d="M7 12h10"></path>
                                <path d="M7 16h10"></path>
                            </svg>
                        </i>
                        <span class="text nav-text">Lead Forms</span>
                    </a>
                </li>
                <?php } else { ?>
                    <li>
                    <a id='mainMenu_LeadForms' href="/partners/lead-forms">
                        <i>
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-article" width="14"
                                height="14" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" fill="none"
                                stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                <rect x="3" y="4" width="18" height="16" rx="2"></rect>
                                <path d="M7 8h10"></path>
                                <path d="M7 12h10"></path>
                                <path d="M7 16h10"></path>
                            </svg>
                        </i>
                        <span class="text nav-text">Lead Forms</span>
                    </a>
                </li>
                <?php } ?>
                <li>
                    <a id='mainMenu_Banners' href="/partners/banners">
                        <i>
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-layout-dashboard"
                                width="14" height="14" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" fill="none"
                                stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                <path d="M4 4h6v8h-6z"></path>
                                <path d="M4 16h6v4h-6z"></path>
                                <path d="M14 12h6v8h-6z"></path>
                                <path d="M14 4h6v4h-6z"></path>
                            </svg>
                        </i>
                        <span class="text nav-text">Banners</span>
                    </a>
                </li>
                <?php if ($user['status_system'] == "manual") { ?>
                    <li>
                        <a id='mainMenu_Workflow' href="/settings/workflow">
                            <i>
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-status-change"
                                    width="14" height="14" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                                    <path d="M6 18m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0" />
                                    <path d="M18 18m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0" />
                                    <path d="M6 12v-2a6 6 0 1 1 12 0v2" />
                                    <path d="M15 9l3 3l3 -3" />
                                </svg>
                            </i>
                            <span class="text nav-text">Workflow Settings</span>
                        </a>
                    </li>
                <?php } ?>
                <?php if ($user['account_admin'] && !empty($partner_feature['LEAD_CHECKLISTS'])) { ?>
                  <li>
                    <a id='mainMenu_checklists' href="/settings/checklists">
                      <i>
                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="tabler-icon tabler-icon-list-check "><path d="M3.5 5.5l1.5 1.5l2.5 -2.5"></path><path d="M3.5 11.5l1.5 1.5l2.5 -2.5"></path><path d="M3.5 17.5l1.5 1.5l2.5 -2.5"></path><path d="M11 6l9 0"></path><path d="M11 12l9 0"></path><path d="M11 18l9 0"></path></svg>
                      </i>
                      <span class="text nav-text">Manage checklists</span>
                    </a>
                  </li>
                <?php } ?>
                <li>
                  <a id='mainMenu_archived' href=<?= $access_to_kanban ? '/lead/dashboard/archived' : '/partners/dashboard?manage=Archived' ?>>
                    <i>
                      <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="tabler-icon tabler-icon-archive "><path d="M3 4m0 2a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v0a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2z"></path><path d="M5 8v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-10"></path><path d="M10 12l4 0"></path></svg>
                    </i>
                    <span class="text nav-text">Archived Leads</span>
                  </a>
                </li>
            <?php } ?>
            <div class="divider"></div>
            <?php if ($user['account_admin']) { ?>
                <?php if (!empty($partner_feature['access_partner_api'])) { ?>
                    <li>
                        <a id='mainMenu_ApiAccess' href="/partners/api-access">
                            <i>
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-api" width="14"
                                    height="14" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" fill="none"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M4 13h5"></path>
                                    <path d="M12 16v-8h3a2 2 0 0 1 2 2v1a2 2 0 0 1 -2 2h-3"></path>
                                    <path d="M20 8v8"></path>
                                    <path d="M9 16v-5.5a2.5 2.5 0 0 0 -5 0v5.5"></path>
                                </svg>
                            </i>
                            <span class="text nav-text">API Access</span>
                        </a>
                    </li>
                <?php } ?>
                <li>
                    <a id='mainMenu_Users' href="/partners/account-admin-management">
                        <i>
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-users" width="14"
                                height="14" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" fill="none"
                                stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                <circle cx="9" cy="7" r="4"></circle>
                                <path d="M3 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2"></path>
                                <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                                <path d="M21 21v-2a4 4 0 0 0 -3 -3.85"></path>
                            </svg>
                        </i>
                        <span class="text nav-text">Users</span>
                    </a>
                </li>

            <?php } ?>
        </ul>
        <div class="menu-arrow"></div>
    </div>

    <div class="createMenu hidden-menu" style="width: 250px;">
        <ul class="noliststyle subnav" style="font-family: 'Inter', sans-serif;">
            <?php if ($access_to_account): ?>
                <?php if ($access_to_consumer_leads): ?>
                    <li>
                        <div class="label">Commercial</div>
                    </li>
                <?php endif; ?>
                <li>
                    <a class='createMenu_Account' href="/accounts?create=account">
                        <i>
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-building-bank"
                                width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke=" #000000" fill="none"
                                stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                                <line x1="3" y1="21" x2="21" y2="21" />
                                <line x1="3" y1="10" x2="21" y2="10" />
                                <polyline points="5 6 12 3 19 6" />
                                <line x1="4" y1="10" x2="4" y2="21" />
                                <line x1="20" y1="10" x2="20" y2="21" />
                                <line x1="8" y1="14" x2="8" y2="17" />
                                <line x1="12" y1="14" x2="12" y2="17" />
                                <line x1="16" y1="14" x2="16" y2="17" />
                            </svg>
                        </i>
                        <span class="text nav-text">Account</span>
                    </a>
                </li>
                <li>
                    <a class='createMenu_Applicant' href="/account-applicants?create=applicant">
                        <i>
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-user" width="20"
                                height="20" viewBox="0 0 24 24" viewBox="0 0 24 24" stroke-width="2" stroke=" #000000"
                                fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                                <circle cx="12" cy="7" r="4" />
                                <path d="M6 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2" />
                            </svg>
                        </i>
                        <span class="text nav-text">Applicant</span>
                    </a>
                </li>
            <?php endif; ?>

            <?php if ($access_legacy_commercial_lead): ?>
                <li>
                    <a class='createMenu_Lead' id='subNav_AddLead'>
                        <i>
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-link" width="20"
                                height="20" viewBox="0 0 24 24" viewBox="0 0 24 24" stroke-width="2" stroke=" #000000"
                                fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                                <path d="M10 14a3.5 3.5 0 0 0 5 0l4 -4a3.5 3.5 0 0 0 -5 -5l-.5 .5" />
                                <path d="M14 10a3.5 3.5 0 0 0 -5 0l-4 4a3.5 3.5 0 0 0 5 5l.5 -.5" />
                            </svg>
                        </i>
                        <span class="text nav-text">Lead<?php if ($REACT_COMMERCIAL_LEADS): ?> - Legacy<?php endif; ?></span>
                    </a>
                </li>
            <?php endif; ?>

            <?php if ($REACT_COMMERCIAL_LEADS): ?>
                <li>
                    <a class='createMenu_LeadBeta' href="/accounts?create=commercial_lead_react">
                        <i>
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-link" width="20"
                                height="20" viewBox="0 0 24 24" viewBox="0 0 24 24" stroke-width="2" stroke=" #000000"
                                fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                                <path d="M10 14a3.5 3.5 0 0 0 5 0l4 -4a3.5 3.5 0 0 0 -5 -5l-.5 .5" />
                                <path d="M14 10a3.5 3.5 0 0 0 -5 0l-4 4a3.5 3.5 0 0 0 5 5l.5 -.5" />
                            </svg>
                        </i>
                        <span class="text nav-text"> Lead </span>
                    </a>
                </li>
            <?php endif; ?>
            <?php if ($access_to_asset_form): ?>
                <li>
                    <a class='createMenu_AssetQuote' id="subNav_AddQuote" onclick="startQuickQuote();">
                        <i>
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-receipt" width="20"
                                height="20" viewBox="0 0 24 24" viewBox="0 0 24 24" stroke-width="2" stroke=" #000000"
                                fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke=" none" d="M0 0h24v24H0z" fill="none"></path>
                                <path
                                    d="M5 21v-16a2 2 0 0 1 2 -2h10a2 2 0 0 1 2 2v16l-3 -2l-2 2l-2 -2l-2 2l-2 -2l-3 2m4 -14h6m-6 4h6m-2 4h2">
                                </path>
                            </svg>
                        </i>
                        <span class="text nav-text">Asset Quote</span>
                    </a>
                </li>
            <?php endif; ?>
            <?php if ($access_to_tick_and_flick): ?>
                <li>
                    <a class='createMenu_LeadBeta' href="/accounts?create=consumer_lead_tick_and_flick_commercial">
                        <i>
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-link" width="20"
                                height="20" viewBox="0 0 24 24" viewBox="0 0 24 24" stroke-width="2" stroke=" #000000"
                                fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                                <path d="M10 14a3.5 3.5 0 0 0 5 0l4 -4a3.5 3.5 0 0 0 -5 -5l-.5 .5" />
                                <path d="M14 10a3.5 3.5 0 0 0 -5 0l-4 4a3.5 3.5 0 0 0 5 5l.5 -.5" />
                            </svg>
                        </i>
                        <span class="text nav-text">Commercial Assisted App</span>
                    </a>
                </li>
            <?php endif; ?>

            <?php if ($access_to_consumer_leads): ?>
                <li>
                    <div class="label">Consumer</div>
                </li>
                <?php if ($partner_feature['access_to_consumer_quote_v1'] ?? false): ?>
                <li>
                    <a class='createMenu_ConsumerQuickQuote' id="subNav_ConsumerQuickQuote" href="/consumer-quick-quote">
                        <i>
                            <svg xmlns=" http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-receipt" width="20"
                                height="20" viewBox="0 0 24 24" viewBox="0 0 24 24" stroke-width="2" stroke=" #000000"
                                fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke=" none" d="M0 0h24v24H0z" fill="none"></path>
                                <path
                                    d="M5 21v-16a2 2 0 0 1 2 -2h10a2 2 0 0 1 2 2v16l-3 -2l-2 2l-2 -2l-2 2l-2 -2l-3 2m4 -14h6m-6 4h6m-2 4h2">
                                </path>
                            </svg>
                        </i>
                        <span class="text nav-text">Quick Quote</span>
                    </a>
                </li>
                <?php endif; ?>
                <li>
                    <a class='createMenu_ConsumerLead' id="subNav_ConsumerLead" href="/accounts?create=consumer_lead">
                        <i>
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-link" width="20"
                                height="20" viewBox="0 0 24 24" viewBox="0 0 24 24" stroke-width="2" stroke=" #000000"
                                fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                                <path d="M10 14a3.5 3.5 0 0 0 5 0l4 -4a3.5 3.5 0 0 0 -5 -5l-.5 .5" />
                                <path d="M14 10a3.5 3.5 0 0 0 -5 0l-4 4a3.5 3.5 0 0 0 5 5l.5 -.5" />
                            </svg>
                        </i>
                        <span class="text nav-text">Lead</span>
                    </a>
                </li>
            <?php endif; ?>
            <?php if ($access_to_tick_and_flick): ?>
                <li>
                    <a class='createMenu_LeadBeta' href="/accounts?create=consumer_lead_tick_and_flick">
                        <i>
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-link" width="20"
                                height="20" viewBox="0 0 24 24" viewBox="0 0 24 24" stroke-width="2" stroke=" #000000"
                                fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                                <path d="M10 14a3.5 3.5 0 0 0 5 0l4 -4a3.5 3.5 0 0 0 -5 -5l-.5 .5" />
                                <path d="M14 10a3.5 3.5 0 0 0 -5 0l-4 4a3.5 3.5 0 0 0 5 5l.5 -.5" />
                            </svg>
                        </i>
                        <span class="text nav-text">Consumer Assisted App</span>
                    </a>
                </li>
            <?php endif; ?>
            <?php if ($access_home_loan): ?>
                <li>
                    <div class="label">Home Loan</div>
                </li>
                <a class='createMenu_ConsumerLead' id="subNav_HomeLoanLead" href="/accounts?create=homeloan_lead">
                        <i>
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-link" width="20"
                                height="20" viewBox="0 0 24 24" viewBox="0 0 24 24" stroke-width="2" stroke=" #000000"
                                fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                                <path d="M10 14a3.5 3.5 0 0 0 5 0l4 -4a3.5 3.5 0 0 0 -5 -5l-.5 .5" />
                                <path d="M14 10a3.5 3.5 0 0 0 -5 0l-4 4a3.5 3.5 0 0 0 5 5l.5 -.5" />
                            </svg>
                        </i>
                        <span class="text nav-text">Lead</span>
                    </a>
            <?php endif;?>
        </ul>
    </div>

<?php } ?>

<style>
    :root {
        font-family: 'Inter', sans-serif;
    }

    @supports (font-variation-settings: normal) {
        :root {
            font-family: 'Inter var', sans-serif;
        }
    }

    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        font-family: 'Inter', sans-serif;
    }

    :root {
        /* ===== Colors ===== */
        --body-color: #F9FAFB;
        --dashboardSidebar-color: #FFF;
        --primary-color: #695CFE;
        --primary-color-light: #F6F5FF;
        --toggle-color: #DDD;
        --text-color: black;

        /* ====== Transition ====== */
        --tran-03: all 0.2s ease;
        --tran-03: all 0.3s ease;
        --tran-04: all 0.3s ease;
        --tran-05: all 0.3s ease;
    }

    body {
        min-height: 100vh;
        background-color: var(--body-color);

    }

    ::selection {
        background-color: var(--primary-color);
        color: #fff;
    }

    /* ===== dashboardSidebar ===== */
    .dashboardSidebar {
        display: block;
        position: fixed;
        top: 0;
        left: 0;
        height: 100svh;
        width: 300px;
        padding: 0;
        background: var(--dashboardSidebar-color);

        z-index: 100;
        border: none;
        border-right: 1px solid #e5e7eb;
        transition: none;
    }

    .dashboardSidebar.close {
        width: 60px;
        padding: 0px;
    }

    /* ===== Reusable code - Here ===== */
    .dashboardSidebar li {
        height: 50px;
        list-style: none;
        display: flex;
        align-items: center;
        font-size: 14px;
    }

    .dashboardSidebar li a {
        padding: 10px 12px;
        margin: 0;
    }

    .dashboardSidebar header .image,
    .dashboardSidebar .icon {
        min-width: 20px;
        /* border-radius: 6px; */
    }

    .dashboardSidebar .icon {

        margin-right: 6px;
        display: -webkit-box;
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        -webkit-justify-content: center;
        justify-content: center;
        -webkit-align-items: center;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        -webkit-align-self: center;
        -ms-flex-item-align: center;
        align-self: center;
        margin-left: 4px;
    }

    .dashboardSidebar .text,
    .dashboardSidebar .icon {
        color: #000000;
    }

    .dashboardSidebar .text {
        font-size: 14px;
        font-weight: 400;
        white-space: nowrap;
        opacity: 1;
        font-family: "Inter";
    }

    .dashboardSidebar.close .text {
        opacity: 0;
    }

    /* =========================== */

    .dashboardSidebar header {
        position: relative;
        display: flex;
        align-items: center;
        width: 100%;
        height: 70px;
        border-bottom: 1px solid rgb(229, 231, 235);
        gap: 10px;
    }

    .dashboardSidebar header .image-text {
        padding: 0 12px 2px;
    }

    .dashboardSidebar header .logo-text {
        display: flex;
        flex-direction: column;
    }

    .dashboardSidebar header {
        margin: 0;
        padding: 0px 10px;
    }

    header .image-text .name {
        margin-top: 2px;
        font-size: 14px;
        font-weight: 600;
    }

    header .image-text .profession {
        font-size: 14px;
        margin-top: -2px;
        display: block;
    }

    .dashboardSidebar header .image {
        height: 24px;
        width: 24px;
        display: block;
    }

    .dashboardSidebar header .buttons-container {
        margin-left: auto;
    }

    .dashboardSidebar header .image img {
        object-fit: cover;
        width: 100%;
        height: 100%;
        display: block;
    }

    .dashboardSidebar header .expandToggle {
        position: absolute;
        top: 50%;
        right: -25px;
        transform: translateY(-50%) rotate(180deg);
        height: 25px;
        width: 25px;
        background-color: var(--primary-color);
        color: var(--dashboardSidebar-color);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        cursor: pointer;
    }


    .dashboardSidebar.close .expandToggle {
        transform: translateY(-50%) rotate(0deg);
    }

    .dashboardSidebar li.search-box {
        /* border-radius: 6px; */
        background-color: var(--primary-color-light);
        cursor: pointer;
    }

    .dashboardSidebar li.search-box input {
        height: 100%;
        width: 100%;
        outline: none;
        border: none;
        background-color: var(--primary-color-light);
        color: var(--text-color);
        /* border-radius: 6px; */
        font-size: 14px;
        font-weight: 500;
    }

    .dashboardSidebar li a {
        list-style: none;
        height: 100%;
        background-color: transparent;
        display: flex;
        align-items: center;
        height: 100%;
        width: 100%;
        /* border-radius: 6px; */
        text-decoration: none;
    }

    .dashboardSidebar .bottom-content li.navIcon.menu {
        height: unset;
        padding: 0px;
    }

    .dashboardSidebar .bottom-content li.navIcon.menu .openProfile {
        padding: 10px;
        gap: 16px;
        flex: 1 1 0%;
        width: 100%;
    }

    .dashboardSidebar .bottom-content li.navIcon.menu .openProfile .text.nav-text {
        padding: 0px;
        margin: 0;
        line-height: 1.55;
    }

    .createMenu .subnav li .label {
        font-family: Inter;
        -webkit-tap-highlight-color: transparent;
        line-height: 1.55;
        text-decoration: none;
        color: rgb(75, 85, 99);
        font-weight: 500;
        font-size: 12px;
        padding: 5px 12px;
        cursor: default;
    }

    .createMenu .beta {
        margin-left: 8px;
        -webkit-tap-highlight-color: transparent;
        font-family: Inter;
        font-size: 11px;
        height: 20px;
        line-height: calc(1.25rem - 0.125rem);
        -webkit-text-decoration: none;
        text-decoration: none;
        padding: 0 calc(1rem / 1.5);
        box-sizing: border-box;
        display: -webkit-inline-box;
        display: -webkit-inline-flex;
        display: -ms-inline-flexbox;
        display: inline-flex;
        -webkit-align-items: center;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        -webkit-justify-content: center;
        justify-content: center;
        width: auto;
        text-transform: uppercase;
        border-radius: 2rem;
        font-weight: 700;
        letter-spacing: 0.015625rem;
        cursor: inherit;
        text-overflow: ellipsis;
        overflow: hidden;
        background: linear-gradient(120deg, #12b886 0%, #82c91e 100%);
        color: #fff;
        border: 0rem solid transparent;
    }

    .dashboardSidebar .bottom-content li.navIcon.menu .openProfile svg {
        margin-left: auto;
        margin-right: 0;
    }

    .dashboardSidebar li a:hover {
        background-color: #F9FAFB;
    }

    .dashboardSidebar li a:hover .icon,
    .dashboardSidebar li a:hover .text {
        color: black;
    }


    .dashboardSidebar .dashboardMenuBar {
        height: calc(100svh - 70px);
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        overflow-y: scroll;
        padding: 10px;
    }

    .dashboardSidebar.close .dashboardMenuBar {
        padding: 0;
        overflow: hidden;
    }

    .dashboardSidebar.close .dashboardMenuBar .nav-text {
        display: hidden;
    }

    .dashboardMenuBar .menu {
        display: flex;
        flex-direction: column;
    }

    .dashboardMenuBar::-webkit-scrollbar {
        display: none;
    }

    .dashboardSidebar .dashboardMenuBar .mode {
        /* border-radius: 6px; */
        background-color: var(--primary-color-light);
        position: relative;
    }

    .dashboardMenuBar .mode .sun-moon {
        height: 50px;
        width: 60px;
    }

    .mode .sun-moon i {
        position: absolute;
    }

    .mode .sun-moon i.sun {
        opacity: 0;
    }

    .dashboardMenuBar .bottom-content {
        position: sticky;
        bottom: 0;
        left: 0;
        background: #ffffff;
        display: flex;
        flex-direction: column;
    }

    .dashboardMenuBar .bottom-content .toggle-switch {
        position: absolute;
        right: 0;
        height: 100%;
        min-width: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        /* border-radius: 6px; */
        cursor: pointer;
    }

    .home {
        position: absolute;
        top: 0;
        top: 0;
        left: 250px;
        height: 100vh;
        width: calc(100% - 250px);
        background-color: var(--body-color);
    }

    .home .text {
        font-size: 14px;
        font-weight: 500;
        color: var(--text-color);
        padding: 12px 60px;
    }

    .dashboardSidebar.close~.home {
        left: 78px;
        height: 100vh;
        width: calc(100% - 78px);
    }

    .dashboardSeparator {
        border-top: 1px solid #e5e7eb;
        padding-top: 12px;
    }

    .activeText {
        color: #5048E5 !important;
    }

    .dropdownContainer {}

    .dropdownContainer ul {
        height: unset;
        width: 100%;
    }

    .dropdownContainer ul li {
        margin-left: 30px;
        border-left: rgb(209, 213, 219) solid 1px;
        height: unset;
    }

    .dropdownContainer ul li a.dashboard-active {
        font-weight: 600;
        color: rgb(80, 72, 229);
    }

    .show-dropdown {
        position: absolute;
        left: 100%;
        top: -10px;
        margin-top: 0;
        padding: 10px 20px;
        border-radius: 0 6px 6px 0;
        opacity: 0;
        display: block;
        pointer-events: none;
        transition: 0s;
    }

    .sub-menu {
        position: absolute;
        left: 100%;
        top: -10px;
        margin-top: 0;
        padding: 10px 20px;
        border-radius: 0 6px 6px 0;
        opacity: 0;
        display: block;
        pointer-events: none;
        transition: 0s;
    }

    .dashboard-tooltip {
        position: relative;
        display: inline-block;
    }

    .dashboard-tooltip .tooltiptext {
        visibility: hidden;
        width: 120px;
        background-color: black;
        color: #fff;
        text-align: center;
        border-radius: 6px;
        padding: 5px 0;

        /* Position the tooltip */
        position: absolute;
        z-index: 1;
    }

    .dashboard-tooltip:hover .tooltiptext {
        visibility: visible;
    }

    .nav-tooltip {
        font-family: 'Inter', sans-serif;
        max-width: 100px;
        height: 35px;
        padding: 7px;
        border: 1px solid #ccc;
        box-shadow: 0 2px 4px rgb(46 46 46 / 20%);
        -webkit-box-shadow: 0 2px 4px rgb(46 46 46 / 20%);
        border-radius: 5px;
        -webkit-border-radius: 5px;
        background: black;
        color: white;
        position: fixed;
        text-align: left;
        display: none;
        align-items: center;
        z-index: 100;
    }

    .close .buttons-container {
        display: hidden;
    }
</style>