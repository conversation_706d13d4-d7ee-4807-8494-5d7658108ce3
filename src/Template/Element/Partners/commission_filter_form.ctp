
  <div class="row m-b-1 p-t-3 p-t-0-md m-b-0-xs">
    <div class="six columns m-b-2-xs">
      <h1 class="m-b-0 p-y-0">Commission Details</h1>
      <div class="inline cirleTag purple"><span></span>Refinance</div>
      <div class="inline cirleTag orange"><span></span>New</div>
    </div>
    <div class="six columns text-right text-center-xs m-l-0">
      <div class="display-inline-block-xs">
        <div class="viewing">

          <div class="commissionFilterContent">
            <div id="comm_FilterIcon" class="filtersPsuedoInput filtersApplied commissionFilterApplied text-center">
              <span class="filtersCount">
                <?php 
                  $filterCount = 0;
                  if (!empty($params['filterType'])) $filterCount++;
                  if (!empty($params['filterLendersChecked'])) $filterCount += count($params['filterLendersChecked']);
                  if (!empty($params['filterPartnerUser'])) $filterCount++;
                  echo $filterCount;
                ?>
              </span>
              Filter <span class="arrow-down darkgrey"></span>
              <div class="filterList text-left" style="display:none">
                <a class="closeFilterBox u-pull-right">&times;</a>
                <ul class="noliststyle">
                  <li>
                    <label class="filterTitle">Type</label>
                    <div class="filterContent">
                      <label class="inline middle lendRadios"><input type="radio" name="search[filters][type]" value="All" <?php echo empty($params['filterType']) || !in_array($params['filterType'], array('New', 'Refinance')) ? 'checked':null; ?> /> <span class="label-body small-padding-right">Any</span></label>
                      <br><label class="inline middle lendRadios"><input type="radio" name="search[filters][type]" value="New" <?php echo !empty($params['filterType']) && $params['filterType'] === 'New' ? 'checked' : null; ?> /> <span class="label-body">New <?php echo $this->Lend->displayCounterNumber(@$params['leadsCounter'], 'Type.New')?></span></label>
                      <br><label class="inline middle lendRadios"><input type="radio" name="search[filters][type]" value="Refinance" <?php echo !empty($params['filterType']) && $params['filterType'] === 'Refinance' ? 'checked' : null; ?> /> <span class="label-body small-padding-right">Refinance <?php echo $this->Lend->displayCounterNumber(@$params['leadsCounter'], 'Type.Refinance')?></span></label>
                    </div>
                  </li>
                  <li <?php echo empty($params['filterLenders']) || count($params['filterLenders'])<2? 'style="display:none"': null?>>
                    <label class="filterTitle">Lender</label>
                    <div class="filterContent" id="filterLenderDiv">
                      <?php if(!empty($params['filterLenders'])): ?>
                        <?php foreach ($params['filterLenders'] as $fId => $fn): ?>
                          <label class="inline customCheckbox" for="searchFilterLender<?php echo $fId; ?>"><input type="checkbox" id="searchFilterLender<?php echo $fId; ?>" name="search[filter][lender][]" value="<?php echo $fId ?>" <?php echo in_array($fId, $params['filterLendersChecked'])? 'checked': null?>> <?php echo $fn ?> <?php echo $this->Lend->displayCounterNumber(@$params['leadsCounter'], 'Lender.'.$fId)?><span class="checkmark"></span></label>
                        <?php endforeach; ?>
                      <?php endif; ?>
                    </div>
                  </li>
                  <li <?php echo (empty($params['partnerUsers']) || count($params['partnerUsers'])<2 || empty($currentUserHasAccessAllLeads))? 'style="display:none"': null?>>
                    <label class="filterTitle">Partner User</label>
                    <div class="filterContent" id="filterPartnerUserDiv">
                      <select name="search[filters][partnerUser]" class="form-control">
                        <option value="">All Users</option>
                        <?php if(!empty($params['partnerUsers'])): ?>
                          <?php foreach ($params['partnerUsers'] as $userId => $userName): ?>
                            <option value="<?php echo $userId ?>" <?php echo (!empty($params['filterPartnerUser']) && $params['filterPartnerUser'] == $userId) ? 'selected' : ''; ?>><?php echo h($userName) ?></option>
                          <?php endforeach; ?>
                        <?php endif; ?>
                      </select>
                    </div>
                  </li>
                  <li class="float-right">
                    <button class="clearFilterButton">Clear</button>
                    <button id="filterButton">Apply</button>
                  </li>
                </ul>
              </div>
            </div>
          </div>

        </div>
    </div>
  </div>

  </div>
