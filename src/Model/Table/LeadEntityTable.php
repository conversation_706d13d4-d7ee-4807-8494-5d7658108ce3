<?php

namespace App\Model\Table;

use App\Model\Entity\Lead;
use Cake\ORM\Query;
use Cake\ORM\Table;
use Cake\Log\Log;
use Cake\Datasource\EntityInterface;
use Cake\ORM\TableRegistry;
use App\Auth\LendAuthenticate;
use App\Enums\LendSignTemplateUse;
use App\Lend\CurlHelper;
use App\Lend\LendInternalAuth;
use LeadApisV2\Controller\HemController;
use App\Lend\KanbanHelper;
use App\Lend\SocketHelper;
use Cake\Database\Schema\TableSchema;
use Cake\I18n\Time;

class LeadEntityTable extends Table
{
  protected $_table = 'leads';

  protected $_primaryKey = 'lead_id';

  protected $_entityClass = Lead::class;

  public function initialize(array $config)
  {
    parent::initialize($config);

    $this->addBehavior('TrackChanges');
    $this->addBehavior('CustomTimestamp');
    // $this->addBehavior('GenerateLeadRef');//done in aftersave now

    $this->belongsTo('PartnerAccounts')
      ->setForeignKey('account_id')
      ->setProperty('partner_account');

    $this->belongsTo('PartnerProductTypeEntity')
      ->setForeignKey('product_type_id')
      ->setProperty('partner_product_type');

    $this->belongsTo('NzbnLookupEntity')
      ->setForeignKey('nzbn_id')
      ->setProperty('nzbn_lookup');

    $this->hasMany('Owners', ['className' => 'LeadOwnersEntity'])
      ->setForeignKey('lead_id')
      ->setConditions(['Owners.status' => 'active'])
      ->setSort(['Owners.point_of_contact' => 'desc'])
      ->setProperty('owners_all');

    $this->hasMany('OtherOwners', ['className' => 'LeadOwnersEntity'])
      ->setForeignKey('lead_id')
      ->setConditions(['OtherOwners.point_of_contact' => false, 'OtherOwners.status' => 'active'])
      ->setProperty('owners_other');

    $this->hasOne('PocOwner', ['className' => 'LeadOwnersEntity'])
      ->setConditions(['PocOwner.point_of_contact' => true, 'PocOwner.status' => 'active'])
      ->setForeignKey('lead_id')
      ->setProperty('owner_poc');

    $this->hasOne('InactiveOwner', ['className' => 'LeadOwnersEntity'])
      ->setConditions(['InactiveOwner.status' => 'inactive'])
      ->setForeignKey('lead_id')
      ->setProperty('inactive_owner');

    $this->belongsTo('PartnerAccountEntity')
      ->setForeignKey('account_id')
      ->setProperty('partner_account');

    $this->hasOne('LeadAssetFinanceEntity')
      ->setForeignKey('lead_id')
      ->setProperty('asset_finance');

    $this->hasMany('LeadReferenceEntity')
      ->setConditions(['LeadReferenceEntity.status' => 'active'])
      ->setForeignKey('lead_id')
      ->setProperty('references');

    $this->hasMany('LeadAssetsEntity')
      ->setConditions(['LeadAssetsEntity.status' => 'Active'])
      ->setForeignKey('lead_id')
      ->setProperty('assets');

    $this->hasOne('ReferenceAccountant', ['className' => 'LeadReferenceEntity'])
      ->setConditions(['ReferenceAccountant.reference_type' => 'Accountant', 'ReferenceAccountant.status' => 'active'])
      ->setForeignKey('lead_id')
      ->setProperty('reference_accountant');

    $this->hasMany('AllAddresses', ['className' => 'LeadAddressEntity'])
      ->setConditions(['AllAddresses.status' => 'active'])
      ->setForeignKey('lead_id')
      ->setProperty('all_addresses');

      $this->hasOne('AddressCurrent', ['className' => 'LeadAddressEntity'])
      ->setConditions(['AddressCurrent.date_to IS NULL'])
      ->setForeignKey('lead_id')
      ->setProperty('current_address');

    $this->hasMany('AddressesPrevious', ['className' => 'LeadAddressEntity'])
      ->setConditions(['AddressesPrevious.date_to IS NOT NULL'])
      ->setForeignKey('lead_id')
      ->setProperty('former_addresses');

    $this->hasMany('LeadCreditScoreEntity')
      ->setForeignKey('lead_id')
      ->setProperty('lead_credit_scores');

    $this->hasMany('LeadLiabilitiesEntity')
      ->setConditions(['LeadLiabilitiesEntity.status' => 'Active'])
      ->setForeignKey('lead_id')
      ->setProperty('liabilities');

    $this->hasMany('SaleEntity')
      ->setForeignKey('lead_id')
      ->setProperty('sales');

    $this->hasMany('ManStatusHistoryEntity')
      ->setForeignKey('lead_id')
      ->setProperty('man_status_histories')
      ->setSort(['ManStatusHistoryEntity.created' => 'DESC']);


    $this->hasOne('LeadQuoteRefEntity')
      ->setForeignKey('lead_id')
      ->setProperty('lead_quote_ref');

    $this->hasMany('LeadPreferredLenderEntity')
      ->setForeignKey('lead_id')
      ->setProperty('lead_preferred_lender');

    $this->hasMany('LeadExcludedLenderEntity')
      ->setForeignKey('lead_id')
      ->setProperty('lead_excluded_lender');

    $this->belongsTo('LendStatusEntity')
      ->setForeignKey('partner_status_id')
      ->setProperty('lend_status');

    $this->belongsTo('ManStatusEntity')
      ->setForeignKey('man_status_id')
      ->setProperty('man_status');

    $this->belongsTo('PartnerEntity')
      ->setForeignKey('partner_id')
      ->setProperty('partner');

    $this->belongsTo('FrmPurposeEntity')
      ->setForeignKey('purpose_id')
      ->setProperty('purpose');

    $this->belongsTo('CurrentLenderStatus', ['className' => 'LenderLeadUpdateEntity'])
      ->setForeignKey('lender_status_id')
      ->setProperty('lender_status');

    $this->belongsTo('PartnerTagEntity')
      ->setForeignKey('tag_id')
      ->setProperty('tag');

    $this->hasOne('LeadAbnLookupEntity', [
      'className' => 'LeadAbnLookupEntity',
      'foreignKey' => 'lead_id',
      'propertyName' => 'abn_lookup',
      'strategy' => 'select',
      'sort' => ['LeadAbnLookupEntity.abn_id' => 'DESC'],
      'conditions' => function ($exp, $query) {
        $query->leftJoin(
          ['LeadEntity' => 'leads'],
          [
            'LeadEntity.abn = LeadAbnLookupEntity.abn'
          ]
        );
        return $exp->add(['LeadEntity.lead_id = LeadAbnLookupEntity.lead_id']);
      }
    ]);

    $this->hasOne('EntityTrustEntity')
      ->setForeignKey('lead_id')
      ->setProperty('entity_trust');

    $this->hasOne('ConPageStatusEntity')
      ->setForeignKey('lead_id')
      ->setProperty('con_page_status');

    $this->hasMany('ConPrelimEntity')
      ->setForeignKey('lead_id')
      ->setSort(['ConPrelimEntity.created' => 'desc'])
      ->setProperty('con_preliminary');

    $this->hasMany('ConReqAndObjEntity')
      ->setForeignKey('lead_id')
      ->setSort(['ConReqAndObjEntity.created' => 'desc'])
      ->setProperty('con_req_and_obj');

    $this->hasMany('ConCreditPropFeeEntity')
      ->setForeignKey('lead_id')
      ->setProperty('con_credit_prop_fees');

    $this->hasMany('ConRequirementEntity')
      ->setForeignKey('lead_id')
      ->setProperty('con_requirements');

    $this->hasOne('LeadPricingEntity')
      ->setForeignKey('lead_id')
      ->setProperty('pricing');

    $this->hasMany('LeadLenderMatchEntity')
      ->setConditions(['LeadLenderMatchEntity.version IS NULL'])
      ->setForeignKey('lead_id')
      ->setProperty('lender_matches');

    $this->hasOne('SelectedLenderMatch', ['className' => 'LeadLenderMatchEntity'])
      ->setConditions(['SelectedLenderMatch.version IS NULL', 'SelectedLenderMatch.status' => 'Selected'])
      ->setForeignKey('lead_id')
      ->setProperty('selected_lender_match');

    $this->hasMany('PartnerLeadUploadsEntity')
      ->setForeignKey('lead_id')
      ->setConditions(['status' => 'Active'])
      ->setProperty('uploads');

    $this->hasMany('LeadOwnerIncomeEntity')
      ->setForeignKey('lead_id')
      ->setProperty('incomes');

    $this->hasMany('LeadOwnerExpenseEntity')
      ->setForeignKey('lead_id')
      ->setProperty('expenses');

    $this->hasOne('PartnerUserLeadsEntity')
      ->setConditions(['PartnerUserLeadsEntity.status' => 'ACCESS'])
      ->setForeignKey('lead_id')
      ->setProperty('partner_user_lead');

    $this->hasMany('AllPartnerUserLeadsEntity', ['className' => 'PartnerUserLeadsEntity'])
      ->setForeignKey('lead_id')
      ->setProperty('all_partner_user_leads');


    $this->hasMany('LeadStatusHistoryEntity')
      ->setForeignKey('lead_id')
      ->setProperty('status_history');

      $this->hasMany('PartnerLeadHistoryEntity')
      ->setForeignKey('lead_id')
      ->setProperty('partner_lead_history')
      ->setConditions([
          'PartnerLeadHistoryEntity.history_detail IN' => [
            'Lead Unarchived',
            'Lead Archived',
            'Lead Closed',
            'Lead Open',
            'Lead Assigned',
            'Lead Revoked',
            'Trashed'
          ]
      ]);

    $this->hasOne('HistoryAdded', [
      'className' => 'PartnerLeadHistoryEntity',
      'foreignKey' => 'lead_id',
      'conditions' => [
        'HistoryAdded.partner_id = LeadEntity.partner_id',
        'HistoryAdded.partner_user_id IS NOT NULL',
        'HistoryAdded.history_detail' => 'Lead Added'
      ],
      'propertyName' => 'history_added'
    ]);


    //callback way if the above does not work
    // $this->hasOne('HistoryAdded', [
    //   'className' => 'LeadStatusHistoryEntity',
    //   'foreignKey' => 'lead_id',
    //   'conditions' => function ($exp, $query) {
    //     return $exp
    //       ->equalFields('HistoryAdded.partner_id', $this->aliasField('partner_id'))
    //       ->isNotNull('HistoryAdded.partner_user_id')
    //       ->eq('HistoryAdded.history_detail', 'Lead Added');
    //   },
    //   'propertyName' => 'history_added'
    // ]);

    $this->hasMany('PartnerCallbackEntity')
      ->setForeignKey('lead_id')
      ->setProperty('tasks');

    $this->hasMany('LeadStatusHistoryEntity')
      ->setForeignKey('lead_id')
      ->setProperty('status_history');

    $this->hasMany('LeadNotesEntity')
      ->setForeignKey('lead_id')
      ->setProperty('lead_notes');

    $this->hasMany('PartnerCommissionEntity')
      ->setForeignKey('lead_id')
      ->setProperty('partner_commissions');

    $this->hasOne('LatestCommission', [
      'className' => 'PartnerCommissionEntity',
      'foreignKey' => 'lead_id',
      'propertyName' => 'latest_commission',
      'strategy' => 'select',
      'sort' => ['LatestCommission.funded_date' => 'DESC'],
      'conditions' => function ($exp, $query) {
        return $exp->add(['LatestCommission.is_active' => 1]);
      }
    ]);

    $this->hasMany('LenderMatchRequestEntity')
      ->setForeignKey('lead_id')
      ->setProperty('lender_match_requests')
      ->setConditions(['LenderMatchRequestEntity.match_ref IS NOT null'])
      ->setSort(['LenderMatchRequestEntity.id' => 'desc']);

    $this->hasMany('SettlementReviewsEntity')
      ->setForeignKey('lead_id')
      ->setProperty('settlement_reviews');

    $this->hasOne('LatestNote', [
      'className' => 'LeadNotesEntity',
      'foreignKey' => 'lead_id',
      'propertyName' => 'latest_note',
      'strategy' => 'select',
      // 'sort' => ['LeadNotesEntity.created' => 'DESC'],//gets most recent by default
      'conditions' => function ($exp, $query) {
        $query->leftJoin(
          ['LeadEntity' => 'leads'],
          [
            'LatestNote.lead_id = LeadEntity.lead_id'
          ]
        );
        return [];
        // return $exp->add(['LeadEntity.lead_id = LeadAbnLookupEntity.lead_id']);//no conditions
      }
    ]);

    //Leaving this here is case next_task details is required
    //This is based on existing rules where we show the oldest open task(not considering scheduled time)
    /*
    $this->hasOne('NextTask', [
    'className' => 'PartnerCallbackEntity',
    'foreignKey' => 'lead_id',
    'propertyName' => 'next_task',
    'strategy' => 'select',
    'conditions' => function ($exp, $query) {
    $query->innerjoin(
    [
    'NxtTask' => $query
    ->getConnection()
    ->newQuery()
    ->select(['lead_id', 'callback' => $query->func()->min('callback_id')])
    ->from('partner_callbacks')
    ->where('status != 0')
    ->group('lead_id')
    ],
    [
    'NextTask.lead_id = NxtTask.lead_id',
    'NextTask.callback_id = NxtTask.callback'
    ]
    );
    return [];
    }
    ]);
    */

    // $this->hasOne('SalesCurrent', [
    //   'className' => 'SaleEntity',
    //   'foreignKey' => 'lead_id',
    //   'propertyName' => 'sales_current',
    //   'strategy' => 'select',
    //   'conditions' => function ($exp, $query) {
    //     $query->innerjoin(
    //       [
    //         'SlsCurrent' => $query
    //           ->getConnection()
    //           ->newQuery()
    //           ->select(['lead_id', 'sale_id' => $query->func()->max('sale_id')])
    //           ->from('sales')
    //           ->where('status != 0')
    //           ->group('lead_id')
    //       ],
    //       [
    //         'SalesCurrent.lead_id = SlsCurrent.lead_id',
    //         'SalesCurrent.sale_id = SlsCurrent.sale_id'
    //       ]
    //     );
    //     return [];
    //     // return $exp->add(['LeadEntity.lead_id = LeadAbnLookupEntity.lead_id']);//no conditions
    //   }
    // ]);

    $this->hasOne('LendScoreEntity') //need to check if there can only be one, if not need to get this separately
      ->setForeignKey('lead_id')
      ->setProperty('lend_score');

    $this->belongsTo('LenderLeadUpdateEntity')
      ->setForeignKey('lender_status_id')
      ->setProperty('lender_status_ent');

    $this->hasMany('DueTasks', ['className' => 'PartnerCallbackEntity', 'sort' => ['DueTasks.callback_id' => 'ASC']])
      ->setConditions(['DueTasks.status != 0'])
      ->setForeignKey('lead_id')
      ->setProperty('due_tasks');

    $this->belongsTo('PartnerCommission', ['className' => 'PartnerCommissionsEntity'])
      ->setForeignKey('latest_commission_id')
      ->setProperty('partner_commission');

    // HasMany for Lender Lead Updates
    $this->hasMany('LatestLenderStatuses', ['className' => 'LenderLeadUpdateEntity', 'sort' => ['LatestLenderStatuses.created' => 'DESC']])
      ->setForeignKey('lead_id')
      ->setProperty('latest_lender_statuses');

    $this->hasMany('LeadCallAttemptEntity')
      ->setSort(['LeadCallAttemptEntity.created' => 'desc'])
      ->setForeignKey('lead_id')
      ->setProperty('lead_call_attempts');

    $this->belongsTo('FrmHowSoonEntity')
      ->setForeignKey('how_soon_id')
      ->setProperty('how_soon');

    $this->hasOne('LeadAssociatedDataEntity')
      ->setForeignKey('lead_id')
      ->setProperty('lead_associated_data');

    $this->hasOne('IntermediaryLenderMappingEntity')
      ->setForeignKey('new_lead_id')
      ->setProperty('intermediary_lender_mapping');

    $this->hasMany('LeadNotificationPartnerUsersEntity')
      ->setForeignKey('lead_id')
      ->setProperty('lead_notification_partner_users');

    $this->hasOne('SentToIntermediary', ['className' => 'IntermediaryLenderMappingEntity'])
      ->setForeignKey('original_lead_id')
      ->setProperty('sent_to_intermediary');

    $this->belongsTo('ReferrerPeople')
    ->setForeignKey('referrer_person_id')
    ->setProperty('referrer_person');

    $this->belongsTo('IntermediaryOriginalPartner', ['className' => 'PartnerEntity'])
    ->setForeignKey('intermediary_original_partner_id')
    ->setProperty('intermediary_original_partner');

    $this->hasMany('PartnerRequestedPrivacyForm', [
      'className' => 'LendSignatureRequestEntity',
      'sort' => ['PartnerRequestedPrivacyForm.created' => 'desc'],
      'strategy' => 'select',
    ])
      ->setConditions(['PartnerRequestedPrivacyForm.template_use_shortname' => LendSignTemplateUse::CommercialPrivacyForms])
      ->setForeignKey('lead_id')
      ->setProperty('partner_requested_privacy_forms');

    $this->hasOne('LatestLeadSignatureRequestEntity', [
        'className' => 'LendSignatureRequestEntity',
        'sort' => ['LatestLeadSignatureRequestEntity.signed_time' => 'DESC'],
        'conditions' => function ($exp, $query) {
            $query->limit(1);
            return $exp;
        },
    ])
    ->setForeignKey('lead_id')
    ->setProperty('latest_lead_signature_request');


      $this->hasMany('PartnerAccountLinkPeopleEntity', [
        'foreignKey' => 'account_id',
        'bindingKey' => 'account_id',
      'propertyName' => 'partner_account_link_people'
    ]);
    $this->hasOne('LeadMarketingEntity')
    ->setForeignKey('lead_id')
    ->setProperty('lead_marketing');

    $this->hasOne('LeadHomeLoanDetail')
    ->setForeignKey('lead_id')
    ->setProperty('lead_home_loan_details');

    $this->hasOne('LeadHomeLoanProperty')
    ->setForeignKey('lead_id')
    ->setProperty('lead_home_loan_property');

    $this->hasOne('LeadHomeLoanCompliance', ['className' => 'LeadHomeLoanCompliances'])
    ->setForeignKey('lead_id')
    ->setProperty('lead_home_loan_compliance');

    $this->hasMany('LendSignatureRequestEntity', ['sort' => ['LendSignatureRequestEntity.sent_time' => 'DESC']])
      ->setForeignKey('lead_id')
      ->setProperty('lend_signature_requests');
  }

  public function _initializeSchema(TableSchema $schema): TableSchema
  {
    $schema->setColumnType('prev_business_name', 'boolean');
    return parent::_initializeSchema($schema);
  }

  //query this dupe function to check ABN, applicant full name, phone, email and financial product ,NOT closed NOT archived, Created in last 30 days
  public function checkDuplicate($entity)
  {
    if (!$entity->duplicated_lead_ref) {
      $poc_owner = null;
      if (!empty($entity->owners_all)) {
        foreach ($entity->owners_all as $owner) {
          if (!empty($owner->point_of_contact)) {
            $poc_owner = $owner;
            break;
          }
        }
      }

      if ($poc_owner) {
        $existingLead = $this->find()
          ->where([
            'partner_id' => $entity->partner_id,
            'created >' => date('Y-m-d', strtotime('-14 days')),
            "PocOwner.first_name" => $poc_owner->first_name,
            "PocOwner.last_name" => $poc_owner->last_name,
            "PocOwner.mobile" => $poc_owner->mobile,
            "PocOwner.email" => $poc_owner->email,
            'product_type_id IS' => $entity->product_type_id,
            'is_closed !=' => 1,
            'is_archived !=' => 1
          ])
          ->contain(['PocOwner'])
          ->first();
        if ($existingLead) {
          $entity->duplicated_lead_ref = $existingLead->lead_ref;
        }
      }
    }
    return $entity;
  }

  public function beforeSave($event, EntityInterface $entity, $options)
  {
    $options['isNew'] = false;
    //save old entities to be tracked for afterSave
    $options['beforeSaveData']['lead_associated_data'] = $entity->lead_associated_data ? (clone $entity->lead_associated_data) : null;
    $options['beforeSaveData']['owner_poc'] = $entity->owner_poc ? (clone $entity->owner_poc) : null;
    $options['beforeSaveData']['partner_user_lead'] = $entity->partner_user_lead ? (clone $entity->partner_user_lead) : null;

    $entity->prev_business_name = !empty($entity->prev_business_name) ? true : false;
    if (!empty($_COOKIE['auth_token'])) {
      $result = LendAuthenticate::decode(['allowedAlgs' => ['HS256']], $_COOKIE['auth_token']);
      if ($result['success']) {
        $user = TableRegistry::getTableLocator()->get('PartnerUserEntity')->find('all')
          ->where(['email' => $result['payload']->sub])
          ->first();
      }
    }
    //count years_in_business
    if (empty($entity->years_in_business) && !empty($entity->company_registration_date) && strtotime($entity->company_registration_date) > 0) {
      $entity->years_in_business = date('Y', strtotime($entity->created)) - date('Y', strtotime($entity->company_registration_date));
    }

    if ($entity->product_type_id == 27 && !in_array($entity->purpose_id, [41, 42])) {
      $entity->purpose_id = 41;
    }

    // Check for Transfer Lead Rules
    if ($entity->isNew() && $entity->partner_id) {
      $partner_transfer_lead_rules_table = TableRegistry::getTableLocator()->get('PartnerTransferLeadRuleEntity');
      $partner_transfer_lead_rule = $partner_transfer_lead_rules_table->find('all')->where(['partner_id_from' => $entity->partner_id, 'active' => true])->toArray();
      if (!empty($partner_transfer_lead_rule)) {
        $rules = $partner_transfer_lead_rule[0]->rules;
        $formatted_payload = $this->formatPayload($entity);
        // make lead_owners to POC owner:
        $formatted_payload['lead_owners'] = array_filter($formatted_payload['lead_owners'], function ($owner) {
          return $owner['point_of_contact'];
        })[0];
        if ($this->_checkRules($formatted_payload, $rules)) {
          $entity->partner_id = $partner_transfer_lead_rule[0]->partner_id_to;
          // Turn off force send:
          $entity->force_send = false;
          $entity->send_type = 'Manual';
        }
      }
    }

    // set default value for product_type_id of Consumer Lead
    if (!empty($entity->lead_type) && $entity->lead_type === 'consumer' && !$entity->product_type_id) {
      if (in_array(@$entity->purpose_id, [20, 21, 22, 23])) {
        $entity->product_type_id = 25;
      } elseif (in_array(@$entity->purpose_id, [41, 42])) {
        $entity->product_type_id = 27;
      } else {
        $entity->product_type_id = 26;
      }
      $entity->setDirty('product_type_id', true);
    }

    if (!empty($entity->lead_type) && $entity->lead_type === 'commercial' && !$entity->product_type_id) {
      if (in_array(@$entity->purpose_id, [3,14])) { //3: Vehicles or Transport, 14: Machinery or Equipment
        $entity->product_type_id = 20; //Chattel Mortgage
      }  else {
        $entity->product_type_id = 1; //Unsecured Loan
      }
      $entity->setDirty('product_type_id', true);
    }

    // get dirty
    $dirty = $this->_getDirtyAll($entity);

    // Handle lead status changes (closed/open and archived/unarchived)
    if (!$entity->isNew() && !empty($user)) {
      // Handle is_closed status changes
      if (isset($dirty['is_closed'])) {
        $newValue = $entity->is_closed;
        $status = ($newValue == 1) ? 'Closed' : 'Open';

        // Record in LeadsClosedHistoryEntity (legacy table)
        $leadsClosedHistoryTable = TableRegistry::getTableLocator()->get('LeadsClosedHistoryEntity');
        $leadsClosedHistoryEntity = $leadsClosedHistoryTable->newEntity([
            'partner_user_id' => $user->partner_user_id,
            'lead_id' => $entity->lead_id,
            'status' => strtoupper($status),
            'updated' => date('Y-m-d H:i:s')
        ]);
        $leadsClosedHistoryTable->save($leadsClosedHistoryEntity);

        // Record in PartnerLeadHistoryEntityTable
        $this->_recordLeadStatusChange($entity, $user, $status);

        if ($newValue == 1) {
            $entity->lead_closed_time = date('Y-m-d H:i:s');
        }
      }

      // Handle is_archived status changes
      if (isset($dirty['is_archived'])) {
        $newValue = $entity->is_archived;
        $status = ($newValue == 1) ? 'Archived' : 'Unarchived';

        // Record in PartnerLeadHistoryEntityTable
        $this->_recordLeadStatusChange($entity, $user, $status);
      }
    }


    if (!empty($dirty['asset_finance']['equipment_id'])) {
      $frm_equipment_table = TableRegistry::getTableLocator()->get('FrmEquipmentEntity');
      $equipment = $frm_equipment_table->get($entity->asset_finance->equipment_id);
      if (!empty($equipment->parent_id)) {
        $entity->sub_equipment_id = $equipment->equipment_id;
        $entity->equipment_id = $equipment->parent_id;
      } elseif (!empty($equipment->equipment_id)) {
        $entity->sub_equipment_id = null;
        $entity->equipment_id = $equipment->equipment_id;
      }
    }

    if (!empty($entity->asset_finance->sale_type)) {
      $entity->equipment_source = $entity->asset_finance->sale_type;
    }

    if (array_key_exists('purpose_id', $entity->toArray()) && array_key_exists('equipment_id', $entity->toArray())) {
      $entity = $this->__checkPurposeEquipment($entity);
    }
    $entity = $this->__checkAccountLevel($entity);

    if ($entity->isNew()) {
      $entity = $this->checkDuplicate($entity);
      $options['isNew'] = true;

      // get partner detail:
      if (!empty($entity->partner_id)) {
        $partner_table = TableRegistry::getTableLocator()->get('PartnerEntity');
        $partner = $partner_table->get($entity->partner_id, [
          'contain' => [
            'PartnerFeatureFlagEntity',
            'ManStatusEntity',
          ]
        ]);
      }
      // call queue status check:
      if (
        !empty($partner->feature_flag->enable_call_queue) &&
        (
          empty($partner->not_allowed_source_in_queue) ||
          empty($entity->source) ||
          !in_array($entity->source, array_map(function ($item) {
            return trim($item);
          }, explode(',', $partner->not_allowed_source_in_queue)))
        ) &&
        (
          empty($partner->block_intermediary_original_partner_ids) ||
          empty($entity->intermediary_original_partner_id) ||
          !in_array($entity->intermediary_original_partner_id, array_map(function ($item) {
            return trim($item);
          }, explode(',', $partner->block_intermediary_original_partner_ids)))
        )
      ) {
        $entity->call_queue_status = 0;
      }else{
        $entity->call_queue_status = -1;
      }
      // sales_monthly:
      if (empty($entity->sales_monthly) && !empty($entity->client_declared_sales_monthly)) {
        $entity->sales_monthly = $entity->client_declared_sales_monthly;
      }

      // manual status
      if (!empty($partner)) {
        if ($partner->status_system === 'manual') {
          foreach ($partner->man_statuses as $status) {
            if ($status->is_new_lead) {
              $entity->man_status_id = $status->id;
              break;
            }
          }
          $entity->send_type = 'Manual';
        } elseif (empty($entity->send_type)) {
          $entity->send_type = 'Auto';
        }
      }
      // default set for Consumer lead:
      if (!empty($entity->lead_type) && $entity->lead_type === 'consumer') {
        if (empty($entity->how_soon_id)) {
          $entity->how_soon_id = 4;
        }

        if (!empty($user) && $user->partner_id === $entity->partner_id) {
          $partner_user_id = $user->partner_user_id;
        } else {
          // NOTE:: for sending to intermediary, we decided to use admin of intermediary lender's default fee.
          //        This might not a proper logic but it should be enough for now.
          $partner_user = TableRegistry::getTableLocator()->get('PartnerUserEntity')->find('all')->where(['partner_id' => $entity->partner_id, 'account_admin' => true])->first();
          $partner_user_id = $partner_user->partner_user_id;
        }

        if (!empty($partner_user_id)) {
          // get partner user fee default
          $partner_user_fee_default_table = TableRegistry::getTableLocator()->get('ConPartnerUserFeeDefaultEntity');
          $con_credit_prop_fees = $partner_user_fee_default_table->find('all')
            ->where(['partner_user_id' => $partner_user_id, 'active' => true])
            ->toArray();
          $con_credit_prop_fees = json_decode(json_encode($con_credit_prop_fees), true);
          $con_credit_prop_fees = array_map(function ($item) {
            unset($item['id']);
            unset($item['created']);
            unset($item['updated']);
            return $item;
          }, $con_credit_prop_fees);
        }

        $this->patchEntity($entity, [
          'con_page_status' => [
            "con_cgqp_status" => "incomplete",
            "con_req_obj_status" => "incomplete",
            "con_loan_details_status" => false,
            "con_asset_details_status" => false,
            "con_applicants_status" => false,
            "con_finances_status" => false,
            "con_assets_liabilities_status" => false,
            "con_lender_pricing_status" => "incomplete",
            "con_references_status" => false,
            "con_prelim_status" => "incomplete",
            "con_credit_proposal_status" => "not sent"
          ],
          'con_credit_prop_fees' => @$con_credit_prop_fees,
        ], ['associated' => ['ConPageStatusEntity', 'ConCreditPropFeeEntity']]);
      }
    }
    $entity->last_changed_date = date('Y-m-d H:i:s');

    $entity = $this->__checkAccountLevel($entity);

    if ($entity->isNew()) {
      $entity->set('is_just_created', true);
    }

    return $entity;
  }

  public function afterSave($event, EntityInterface $entity, $options)
  {
    if (empty($entity->lead_ref)) {
      $lead_ref = (new LendInternalAuth)->hashLeadId($entity->lead_id);
      $this->updateAll(
        ['lead_ref' => $lead_ref],
        ['lead_id' => $entity->lead_id]
      );
      $entity->lead_ref = $lead_ref;
    }

    if (!empty($_COOKIE['auth_token'])) {
      $result = LendAuthenticate::decode(['allowedAlgs' => ['HS256']], $_COOKIE['auth_token']);
      if ($result['success']) {
        $user = TableRegistry::getTableLocator()->get('PartnerUserEntity')->find('all')
          ->where(['email' => $result['payload']->sub])
          ->first();
      }
    }
    if (!empty($user) && $user->partner_id === $entity->partner_id) {
      $this->_updateConPageStatus($entity);
    }
    $this->_updateStatusHistory($entity);
    // $this->_updateLeadAssociatedData($entity);//moved back to trigger
    $this->_addBackgroundJob($entity);
    $entity->set('is_just_created', false);

    $partnerId = $entity->partner_id ??$this->get($entity->lead_id, ['fields' => ['partner_id']])->partner_id;

    //check if partner is on manual status
    $partner = TableRegistry::getTableLocator()->get('PartnerEntity')->get($partnerId, [
      'fields' => [
          'partner_id', 'status_system'
      ],
      'contain' => [
          'PartnerFeatureFlagEntity' => ['fields' => ['partner_id', 'access_to_kanban_v2']],
      ]
    ]);

    if(($partner->status_system === "manual") && !empty($partner->feature_flag->access_to_kanban_v2)){
      //notify about new lead via socket server
      $validCallQueueStatuses = [-1, -3, -4];//only leads is these stauses are shown on the kanban board
      $isNew = $options->offsetExists('isNew') ? $options->offsetGet('isNew') : false;
      $existing = $this->get($entity->get('lead_id'), ['fields' => ['lead_id', 'man_status_id', 'product_type_id']]);
      $dirtyFields = $entity->extractOriginalChanged($entity->getVisible());
      $oldManStatusId = isset($dirtyFields['man_status_id'])?$dirtyFields['man_status_id']:$existing->man_status_id;
      $oldProductId = isset($dirtyFields['product_type_id'])?$dirtyFields['product_type_id']:$existing->product_type_id;
      $kanbanChanges = (isset($dirtyFields['man_status_id']) || isset($dirtyFields['product_type_id']));
      if($entity->man_status_id
        && $entity->_noKanbanChanges//don't enforce all kanban changes
        && !$isNew
        && $kanbanChanges//there are kanban changes that force changes
        ){
          KanbanHelper::moveLeadDataUpdated($entity->lead_ref,$oldManStatusId, $existing->man_status_id, $oldProductId, $existing->product_type_id, true);
      }
      if($entity->man_status_id && !$entity->_noKanbanChanges){
        if ($isNew === true) {
          if(in_array($entity->call_queue_status, $validCallQueueStatuses)){//value is valid
            KanbanHelper::addLead(null, $entity->lead_ref);
            SocketHelper::kanbanAddLead($entity->lead_id);
          }
        }
        else{
          $originalFields = ['man_status_id', 'product_type_id', 'partner_id'];
          $originalValues = $entity->extractOriginal($originalFields);
          $missingValueFields = array_diff($originalFields, array_keys($originalValues));
          if(!empty($missingValueFields)){
            $entityFresh = $this->get($entity->lead_id, ['fields' => $missingValueFields]);
            foreach($missingValueFields as $f){
              $originalValues[$f] = $entityFresh->$f;
            }
          }

          if($kanbanChanges === true){
            KanbanHelper::moveLeadDataUpdated($entity->lead_ref,$oldManStatusId, $existing->man_status_id, $oldProductId, $existing->product_type_id);
          }
          else{
            //check if we need to notify Kanban board of lead data changes
            $leadUpdate = false;//track if lead_update needs to be sent
            $filterValuesChanged = [];
            $fieldsToCheck = [
              'leads' => [
                'lead'=> [//lead update to be done if one of these changes
                  'organisation_name',
                  'last_changed_date',
                  'lead_type',
                  'tag_id',
                  'amount_requested'
                ],
                'filters'=> [
                  'last_changed_date',
                  'status' => 'man_status_id',
                  'customer_type' => 'lead_type',
                  'tag_id',
                  'referrer' =>'referrer_person_id',
                ]
              ],
              'other' => [
                'owner_poc' => [
                  'lead'=> [
                    'first_name',
                    'last_name',
                    'mobile',
                    'phone',
                    'avatar_image_url',
                  ]
                ],
                'lead_associated_data' => [
                  'lead' => [
                    'lender_name',
                    'max_note_id',
                  ],
                  'filters' => [
                    'lender' => 'lender_name'
                  ],
                ],
                'partner_user_lead' => [
                  'lead' => [
                    'partner_user_id',
                  ],
                  'filters' => [
                    'assignee' => 'partner_user_id',
                  ],
                ]
              ]
            ];
            $trackedFields = [];
            $changedFields = [];
            foreach($fieldsToCheck['leads'] as $type => $fields){
              foreach ($fields as $k => $field){
                $trackedFields[] = $field;
                $alias = is_int($k) ? $field : $k;
                if(array_key_exists($field, $dirtyFields)){
                  $changedFields[] = $field;
                  $leadUpdate = true;
                  if($type == 'filters'){
                    $filterValuesChanged[$alias] = [
                      'old'=>  ($dirtyFields[$field] instanceof Time)?$dirtyFields[$field]->format('Y-m-d H:i:s'): $dirtyFields[$field],
                      'new'=> $entity->$field,
                    ];
                  }
                }
              }
            }
            foreach($fieldsToCheck['other'] as $entityName => $vals){
              $relatedEntity = $options['beforeSaveData'][$entityName];
              if($relatedEntity){
                //we can only see dirty field names for related entities, not their old values as these are not tracked by cake
                $dirtyFieldsEntity = $relatedEntity->getDirty();
                foreach($vals as $type => $fields){
                  foreach ($fields as $k => $field){
                    $trackedFields[] = $field;
                    $alias = is_int($k) ? $field : $k;
                    if(in_array($field, $dirtyFieldsEntity)){
                      $changedFields[] = $field;
                      $leadUpdate = true;
                      if($type === 'filters'){
                        $filterValuesChanged[$alias] = [
                          'new'=> $relatedEntity->$field,
                        ];
                      }
                    }
                  }
                }
              }
            }
            /**
             * Man status
             */
            if($leadUpdate === true){
              $trackedFields = array_unique($trackedFields);
              $changedFields = array_unique($changedFields);
              if(count(array_intersect(['amount_requested', 'last_changed_date'], $changedFields)) === 2){//amount_requested is being changed
                SocketHelper::kanbanUpdateLeadAmountRequested($entity->lead_id, ['old' => $dirtyFields['amount_requested'], 'new' => $entity->amount_requested]);
                if(count($changedFields) !== 2){//more fields are being updated
                  SocketHelper::kanbanUpdateLead($entity->lead_id, $filterValuesChanged);
                }
              } else if (
                !array_key_exists('partner_id', $dirtyFields)
                && $entity->is_closed != true
                && $entity->is_archived != true
              ) {
                SocketHelper::kanbanUpdateLead($entity->lead_id, $filterValuesChanged);
              }
            }
          }

          if(array_key_exists('partner_id', $dirtyFields)) {//Trash lead
            //remove from old partner
            KanbanHelper::removeLead(null, $entity->lead_ref);
            SocketHelper::kanbanRemoveLead($entity->lead_id, $entity->lead_ref, $originalValues['man_status_id'], $originalValues['product_type_id']);
            // KanbanHelper::addLead(null, $entity->lead_ref);
            // SocketHelper::kanbanAddLead($entity->lead_id);
          }
          if(count(array_intersect(['is_closed', 'is_archived'], array_keys($dirtyFields))) > 0){
            //remove lead from any boards in case it has been archived or closed
            if(($entity->is_closed == true) || ($entity->is_archived == true)){
              KanbanHelper::removeLead(null, $entity->lead_ref);
              SocketHelper::kanbanRemoveLead($entity->lead_id, $entity->lead_ref, $originalValues['man_status_id'], $originalValues['product_type_id']);
            }
            else{//is_closed or is_archived (or both) are now false
              if(in_array($entity->call_queue_status, $validCallQueueStatuses)){//value is valid
                KanbanHelper::addLead(null, $entity->lead_ref);
                SocketHelper::kanbanAddLead($entity->lead_id);
              }
            }
          }
          if(array_key_exists('call_queue_status', $dirtyFields)) {
            if(in_array($dirtyFields['call_queue_status'], $validCallQueueStatuses)){//new value is valid
              if(!in_array($originalValues['call_queue_status'], $validCallQueueStatuses)){//old value is invalid
                KanbanHelper::addLead(null, $entity->lead_ref);
                SocketHelper::kanbanAddLead($entity->lead_id);
              }
            }
            else{//new value is invalid
              if(in_array($originalValues['call_queue_status'], $validCallQueueStatuses)){//old value is valid - remove from boards
                KanbanHelper::removeLead(null, $entity->lead_ref);
                SocketHelper::kanbanRemoveLead($entity->lead_id, $entity->lead_ref, $originalValues['man_status_id'], $originalValues['product_type_id']);
              }
            }
          }
        }
      }
    }
  }

  public function checkUnassignedLeads($partner_id)
  {
    $q = $this->find()
      // ->contain(['PartnerUserLeadsEntity'])
      ->leftJoin(
        ['PartnerUserLeadsEntity' => 'partner_user_leads'],
        ['LeadEntity.lead_id = PartnerUserLeadsEntity.lead_id']
      )
      ->where([
        'LeadEntity.partner_id' => $partner_id,
        'PartnerUserLeadsEntity.lead_id IS' => null
      ]);

    return $q;
  }

  public function generateAccountData($lead, $user = false)
  {
    try {
      $account = $this->_mapAccount($lead, $user);
      $account['partner_account_meta'] = $this->_mapAccountMeta($lead);
      $account['partner_account_people'] = !empty($lead['owners_all']) ? $this->mapAccountPeople($lead) : [];
      $account['assets'] = !empty($lead['assets']) ? $this->_mapAssets($lead['assets']) : [];
      $account['liabilities'] = !empty($lead['liabilities']) ? $this->_mapLiabilities($lead['liabilities']) : [];
      $account['references'] = !empty($lead['references']) ? $this->_mapReferences($lead['references']) : [];
      $account['all_addresses'] = !empty($lead['all_addresses']) ? $this->_mapAddresses($lead['all_addresses'], $lead) : [];
      $account['abn_lookup'] = !empty($lead['abn_lookup']) ? $this->_mapAbn($lead['abn_lookup']) : [];
      $account['nzbn_lookup'] = !empty($lead['nzbn_lookup']) ? $this->_mapNzbn($lead['nzbn_lookup']) : [];
      $account['entity_trust'] = !empty($lead['entity_trust']) ? $this->_mapEntityTrust($lead['entity_trust']) : [];

      return $account;
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }

  public function getApplicationData($leadRef)
  {
    $leadId = LendInternalAuth::unhashLeadId($leadRef);

    $where = ['LeadEntity.lead_ref' => $leadRef];
    $lead = $this
      ->find('all')
      ->where($where)
      ->contain([
        'CurrentLenderStatus.LenderProductEntity.LenderEntity',
        'LendStatusEntity',
        'PartnerProductTypeEntity',
        'Owners',
        'FrmHowSoonEntity',
        'FrmPurposeEntity',
        'FrmPurposeEntity.ParentPurpose',
        'LeadAssetFinanceEntity',
        'LeadAssetFinanceEntity.FrmEquipmentEntity',
        'Owners.OwnerAllAddresses',
        'Owners.OwnerAllEmployments',
        'LeadOwnerIncomeEntity',
        'LeadOwnerIncomeEntity.ConIncomeShareEntity',
        'LeadOwnerIncomeEntity.ConfigIncomeEntity',
        'LeadOwnerExpenseEntity',
        'LeadOwnerExpenseEntity.ConfigExpenseEntity',
        'LeadOwnerExpenseEntity.ConfigExpenseEntity.ConfigExpensesBreakdownEntity.LenderEntity',
        'LeadAssetsEntity',
        'LeadAssetsEntity.ConAssetShareEntity',
        'LeadAssetsEntity.ConfigAssetTypeEntity',
        'LeadLiabilitiesEntity',
        'LeadLiabilitiesEntity.ConfigLiabilityEntity',
        'LeadLiabilitiesEntity.ConLiabilityShareEntity',
        'SelectedLenderMatch',
        'SelectedLenderMatch.LenderProductEntity',
        'SelectedLenderMatch.LenderProductEntity.LenderEntity',
        'ConCreditPropFeeEntity',
        'ConCreditPropFeeEntity.ConfigConFeeEntity',
        'PocOwner',
        'PartnerEntity',
        'LeadPricingEntity',
      ])
      ->first();

    $leadArray = $lead->toArray();

    $ownerNamesById = [];

    $totalIncomeGross = 0;
    $totalIncomeNet = 0;
    foreach ($leadArray['owners_all'] as &$owner) {
      $totalIncomeGross += $owner['monthly_gross_income'];
      $totalIncomeNet += $owner['monthly_net_income'];
      $ownerNamesById[$owner['owner_id']] = "{$owner['first_name']} {$owner['last_name']}";
      $owner['dob'] = date('d/m/Y', strtotime($owner['dob']));

      $owner['dependant_age'] = [];
      $ownerDependantYobs = explode(',', $owner['dependant_details']);
      foreach ($ownerDependantYobs as $dependantYob) {
        if (empty($dependantYob)) {
          continue;
        }
        $currentYear = date('Y');
        $age = $currentYear - $dependantYob;
        $owner['dependant_age'][] = $age;
      }

      if (!empty($owner['all_addresses'])) {
        foreach ($owner['all_addresses'] as &$address) {
          $address['time_at_address'] = $this->formatDateDifference($address['date_from'], $address['date_to']);
          $fromYear = date('Y', strtotime($address['date_from']));
          $fromMonth = date('m', strtotime($address['date_from']));
          if (empty($address['date_to'])) {
            $address['period_of_inhabitance'] = "{$fromMonth}/{$fromYear} - Present";
            continue;
          }
          $toYear = date('Y', strtotime($address['date_to']));
          $toMonth = date('m', strtotime($address['date_to']));
          $address['period_of_inhabitance'] = "{$fromMonth}/{$fromYear} - {$toMonth}/{$toYear}";
        }
      }
      if (!empty($owner['all_employments'])) {
        foreach ($owner['all_employments'] as &$employment) {

          $occupations_table = TableRegistry::getTableLocator()->get('Occupation');
          if (intval($employment['previous_occupation']) > 0) {
            $occupation = $occupations_table->find('all')->where([
              'Occupation.occupations_id' => $employment['previous_occupation'],
            ])->first();
            if (!empty($occupation)) {
              $employment['previous_occupation'] = $occupation->occupations_name;
            }
          }
          $employment['time_at_employment'] = $this->formatDateDifference($employment['date_from'], $employment['date_to']);

          $fromYear = date('Y', strtotime($employment['date_from']));
          $fromMonth = date('m', strtotime($employment['date_from']));
          if (empty($employment['date_to'])) {
            $employment['tenure'] = "{$fromMonth}/{$fromYear} - Present";
            continue;
          }
          $toYear = date('Y', strtotime($employment['date_to']));
          $toMonth = date('m', strtotime($employment['date_to']));
          $employment['tenure'] = "{$fromMonth}/{$fromYear} - {$toMonth}/{$toYear}";
        }
      }
    }

    $leadArray['total_income_gross'] = $totalIncomeGross;
    $leadArray['total_income_net'] = $totalIncomeNet;

    foreach ($leadArray['incomes'] as &$income) {
      $income['ownership'] = [];
      if ($income['config_income']['shared'] === false && !empty($income['owner_id'])) {
        $ownerName = $ownerNamesById[$income['owner_id']];
        $income['ownership'] = ["{$ownerName} 100%"];
      } else {
        foreach ($income['shared'] as $shared) {
          $ownerName = $ownerNamesById[$shared['owner_id']];
          $income['ownership'][] = "{$ownerName} {$shared['percent']}%";
        }
      }
    }

    $monthlyExpenses = 0;
    $lessSharedExpenses = 0;
    foreach ($leadArray['expenses'] as &$expense) {
      $breakdownLenders = [];
      if (!empty($expense['config_expense']['breakdown'])) {
        foreach ($expense['config_expense']['breakdown'] as &$breakdown) {
          $breakdownLenders[$breakdown['lender']['lender_name']] = $breakdown['lender']['lender_name'];
        }
      }
      $expense['breakdown_lenders'] = empty($breakdownLenders)
        ? '-' : implode(', ', $breakdownLenders);

      $monthlyExpenses += $expense['amount'];
      if ($expense['shared'] === true) {
        $lessSharedExpenses += $expense['amount'] * $expense['shared_percentage'] / 100;
      }
    }
    $totalMonthlyExpenses = $monthlyExpenses - $lessSharedExpenses;
    $leadArray['monthly_expenses'] = $monthlyExpenses;
    $leadArray['less_shared_expenses'] = $lessSharedExpenses;
    $leadArray['total_monthly_expenses'] = $totalMonthlyExpenses;
    $leadArray['hem_data'] = HemController::getHemData($leadRef);

    $pocOwnerName = $lead['owner_poc']['full_name'];
    $assetsTotalValue = 0;
    foreach ($leadArray['assets'] as &$asset) {
      $totalApplicantsShare = 100;
      $assetOwnershipArray = [$pocOwnerName . ' - 100%'];
      if (!empty($asset['shared'])) {
        $totalApplicantsShare = 0;
        $assetOwnershipArray = [];
        foreach ($asset['shared'] as $share) {
          $assetOwnershipArray[] = $ownerNamesById[$share['owner_id']] . ' - ' . $share['percent'] . '%';
          $totalApplicantsShare += $share['percent'];
        }
        if ($totalApplicantsShare < 100) {
          $assetOwnershipArray[] = 'Non-Applicants - ' . (100 - $totalApplicantsShare) . '%';
        }
      }
      $asset['ownership'] = implode('<br>', $assetOwnershipArray);
      $assetsTotalValue += $asset['value'] * $totalApplicantsShare / 100;
    }
    $leadArray['assets_total_value'] = $assetsTotalValue;


    $liabilitiesTotalValue = 0;
    $liabilitiesTotalLimit = 0;
    $liabilitiesTotalRepayment = 0;
    $liabilitiesTotalBalance = 0;
    foreach ($leadArray['liabilities'] as &$liability) {
      $totalApplicantsShare = 100;
      $liabilityOwnershipArray = [$pocOwnerName . ' - 100%'];
      if (!empty($liability['shared'])) {
        $totalApplicantsShare = 0;
        $liabilityOwnershipArray = [];
        foreach ($liability['shared'] as $share) {
          $liabilityOwnershipArray[] = $ownerNamesById[$share['owner_id']] . ' - ' . $share['percent'] . '%';
          $totalApplicantsShare += $share['percent'];
        }
        if ($totalApplicantsShare < 100) {
          $liabilityOwnershipArray[] = 'Non-Applicants - ' . (100 - $totalApplicantsShare) . '%';
        }
      }
      $liability['ownership'] = implode('<br>', $liabilityOwnershipArray);
      $liabilitiesTotalValue += $liability['value'] * $totalApplicantsShare / 100;
      $liabilitiesTotalLimit += $liability['limit'];
      $liabilitiesTotalRepayment += $liability['repayment_pm'];
      $liabilitiesTotalBalance += $liability['loan_balance'];
    }
    $leadArray['liabilities_total_value'] = $liabilitiesTotalValue;
    $leadArray['liabilities_total_limit'] = $liabilitiesTotalLimit;
    $leadArray['liabilities_total_repayment'] = $liabilitiesTotalRepayment;
    $leadArray['liabilities_total_balance'] = $liabilitiesTotalBalance;

    $brokerFeesCount = 0;
    $thirdPartyFeesCount = 0;
    $lenderFeesCount = 0;
    $brokerFees = [];
    $lenderFees = [];
    $thirdPartyFees = [];
    foreach ($leadArray['con_credit_prop_fees'] as $fee) {
      if ($fee['config_con_fee']['group'] === 'broker') {
        $brokerFees[$brokerFeesCount]['fee_name'] = $fee['display_fee_name'];
        $brokerFees[$brokerFeesCount]['explanation'] = $fee['explanation'];
        $brokerFees[$brokerFeesCount]['amount'] = $fee['display_amount_string'];
        $brokerFeesCount++;
      }
      if ($fee['config_con_fee']['group'] === 'third party') {
        $thirdPartyFees[$thirdPartyFeesCount]['fee_name'] = $fee['display_fee_name'];
        $thirdPartyFees[$thirdPartyFeesCount]['explanation'] = $fee['explanation'];
        $thirdPartyFees[$thirdPartyFeesCount]['amount'] = $fee['display_amount_string'];
        $thirdPartyFeesCount++;
      }
      if ($fee['config_con_fee']['group'] === 'lender') {
        $lenderFees[$lenderFeesCount]['fee_name'] = $fee['display_fee_name'];
        $lenderFees[$lenderFeesCount]['explanation'] = $fee['explanation'];
        $lenderFees[$lenderFeesCount]['amount'] = $fee['display_amount_string'];
        $lenderFeesCount++;
      }
    }
    $leadArray['third_party_fees'] = $thirdPartyFees;
    $leadArray['broker_fees'] = $brokerFees;
    $leadArray['lender_fees'] = $lenderFees;
    $leadArray['loan_name'] = $leadArray['purpose']['purpose'] . " Loan";
    if ($leadArray['purpose']['purpose'] === 'Personal Loan') {
      $leadArray['loan_name'] = 'Personal Loan';
    }
    if (!empty($leadArray['purpose']['parent'])) {
      $leadArray['loan_name'] = 'Personal Loan';
    }
    $leadArray['purpose_is_personal'] = $leadArray['purpose']['purpose'] === 'Personal Loan' || !empty($leadArray['purpose']['parent']);

    if (empty($leadArray['purpose'])) {
      $leadArray['purpose'] = [
        'purpose' => '',
      ];
      $leadArray['purpose_is_personal'] = true;
    }
    if (!empty($leadArray['purpose_other'])) {
      $leadArray['purpose'] = [
        'purpose' => $leadArray['purpose_other'],
      ];
    }

    return $leadArray;
  }

  /**
   * Calculate difference between 2 dates as 'X years Y months'
   * Defaults $toDate to current date if null
   */
  public function formatDateDifference($fromDate, $toDate = null) {
    $toDate = $toDate ?? date('Y-m-d');
    $from = new \DateTime($fromDate);
    $to = new \DateTime($toDate);
    $interval = $from->diff($to);
    return $interval->y . ' years ' . $interval->m . ' months';
  }

  private function _mapAccount($lead, $user)
  {
    try {
      if (empty($lead['partner_id'])) {
        throw new \Exception("partner_id is required");
      }

      $account = [
        'partner_id' => $lead['partner_id'],
        'additional_info' => $lead['additional_info'],
        'partner_user_id' => @$user['partner_user_id']
      ];
      if ($lead['lead_type'] !== 'consumer')
        $account['abn'] = @$lead['abn'];
      return $account;
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }

  private function _mapAccountMeta($lead)
  {
    try {
      $partner_account_meta = [];
      $convert_fields = [
        "phone" => "business_phone",
        // *
        "mobile" => "business_phone_other",
        // *
        "email" => "email_business",
        // *
        "organisation_name" => "organisation_name",
        "business_name" => "business_name",
        "trading_since" => "trading_since",
        "registration_date" => "company_registration_date",
        // *
        "acn" => "acn",
        "business_type" => "business_type",
        "business_type_abn" => "business_type_abn",
        "trustee_type" => "trustee_type",
        "trustee_names" => "trustee_names",
        "industry_id" => "industry_id",
        "industry_detail" => "industry_detail",
        "monthly_expenses" => "monthly_expenses",
        "sales_monthly" => "sales_monthly",
        "tax_outstanding" => "tax_outstanding",
        "tax_overdue" => "tax_overdue",
        "tax_outstanding_arrangement" => "tax_outstanding_arrangement",
        "books_package_id" => "books_package_is",
        // *
        "books_package_detail" => "books_package_detail",
        "premise_type_id" => "premise_type_id",
        "premise_other_detail" => "premise_other_detail",
        "premise_own" => "premise_own",
        "premise_time_in_months" => "premise_time_in_months",
        "b_address" => "b_address",
        "b_suburb" => "b_suburb",
        "b_state" => "b_state",
        "b_postcode" => "b_postcode",
        "b_country" => "b_country",
        "r_address" => "r_address",
        "r_suburb" => "r_suburb",
        "r_state" => "r_state",
        "r_postcode" => "r_postcode",
        "r_country" => "r_country",
        "post_address" => "post_address",
        "post_suburb" => "post_suburb",
        "post_state" => "post_state",
        "post_postcode" => "post_postcode",
        "post_country" => "post_country",
        "global_not_call_id" => "global_not_call_id",
        "on_not_call_list" => "on_not_call_list",
        "invoice_amount_outstanding" => "invoice_amount_outstanding",
        "number_of_invoices" => "number_of_invoices",
        "invoice_lvr" => "invoice_lvr",
        "security_status" => "security_status",
        "show_security_other" => "show_security_other",
        "trading_period_select" => "trading_period_select",
        "prev_business_name" => "prev_business_name",
        "business_credit_history" => "business_credit_history",
        "customer_type" => "customer_type",
        "bank_statements" => "bank_statements",
        "ato_portal" => "ato_portal",
        "bas_statements" => "bas_statements",
        "prev_asset_finance" => "prev_asset_finance",
        "comp_financial_statements" => "comp_financial_statements",
        "personal_business_tax_returns" => "personal_business_tax_returns",
        "pl_balance_sheet" => "pl_balance_sheet",
      ];

      foreach ($convert_fields as $account_field => $lead_field) {
        if (isset($lead[$lead_field])) {
          $partner_account_meta[$account_field] = $lead[$lead_field];
        }
      }

      unset($partner_account_meta['partner_account_id']);

      return $partner_account_meta;
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }

  public function mapAccountPeople($lead, $owners = null)
  {
    try {
      if (!$owners)
        $owners = $lead['owners_all'];
      $people = [];
      $convert_fields = [
        'id' => 'partner_account_people_id',
        'is_main_point_of_contact' => 'point_of_contact'
      ];
      $unset_fields = ['lead_id', 'owner_id', 'created', 'updated', 'partner_account_people', 'status'];
      foreach ($owners as $o) {
        $ownerId = $o['owner_id'];
        foreach ($convert_fields as $people_field => $owner_field) {
          $o[$people_field] = $o[$owner_field];
          unset($o[$owner_field]);
        }
        foreach ($unset_fields as $f) {
          unset($o[$f]);
        }
        $o['dob'] = !empty($o['dob']) ? date('Y-m-d', strtotime($o['dob'])) : null;
        //map to income and expenses
        if($lead['lead_type'] === 'commercial'){
            $o['people_incomes'] = !empty($lead['incomes']) ? $this->_mapIncomes($lead['incomes'], $ownerId) : (!empty($o['finances']) ? $this->_mapIncomesFinances($o['finances']) : []);
            if($o['syncExpenses'] === true){
              $o['people_expenses'] = !empty($lead['expenses']) ? $this->_mapExpenses($lead['expenses']) : [];
            }
        }
        else{//consumer

          $o['additional_info'] = $lead['additional_info'];
          $o['people_incomes'] = !empty($lead['incomes']) ? $this->_mapIncomes($lead['incomes'], $ownerId) : [];
          if($o['syncExpenses'] === true)
            $o['people_expenses'] = !empty($lead['expenses']) ? $this->_mapExpenses($lead['expenses']) : [];
        }
        $o['employments'] = !empty($o['all_employments']) ? $this->_mapEmployments($o['all_employments']) : [];
        $o['addresses'] = !empty($o['all_addresses']) ? $this->_mapPeopleAddresses($o['all_addresses']) : [];
        $o['people_assets'] = !empty($lead['assets']) ? $this->_mapAssets($lead['assets'], true, $ownerId) : [];
        $o['people_liabilities'] = !empty($lead['liabilities']) ? $this->_mapLiabilities($lead['liabilities'], true, $ownerId) : [];
        if (!$o['id']) {
          $orWhere = [];
          if (!empty($o['dob'])) {
            $orWhere[] = ['PartnerAccountPeopleEntity.dob' => date('Y-m-d', strtotime($o['dob']))];
          }
          if (!empty($o['mobile'])) {
            $orWhere[] = ['PartnerAccountPeopleEntity.mobile' => $o['mobile']];
          }
          if (!empty($o['phone'])) {
            $orWhere[] = ['PartnerAccountPeopleEntity.phone' => $o['phone']];
          }
          if (!empty($o['email'])) {
            $orWhere[] = ['PartnerAccountPeopleEntity.email' => $o['email']];
          }
          $accountPeopleTable = TableRegistry::getTableLocator()->get('PartnerAccountPeopleEntity');
          $exists = $accountPeopleTable->find('all')
            ->where(['PartnerAccountPeopleEntity.first_name' => $o['first_name']])
            ->where(['PartnerAccountPeopleEntity.last_name' => $o['last_name']])
            ->where(['PartnerAccountPeopleEntity.status' => 'active'])
            ->where(['or' => $orWhere])
            ->first();
          $exists = $exists ? json_decode(json_encode($exists), true) : null;
          if ($exists['id']) {
            $o['id'] = $exists['id'];
          }
        }
        // if (!isset($o['partner_account_id']))
        //   $o['partner_account_id'] = $lead['account_id'];
        $o['partner_id'] = $lead['partner_id'];
        $people[] = $o;
      }
      return $people;
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }

  private function _mapFinances($finance)
  {
    try {
      $unset_fields = ['lead_owner_finance_id', 'owner_id', 'partner_account_people_id'];
      foreach ($unset_fields as $f) {
        unset($finance[$f]);
      }
      return $finance;
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }

  private function _mapIncomes($incomes, $ownerId)
  {
    try {
      $unset_fields = ['id', 'lead_id', 'owner_id', 'created', 'updated', 'shared_amount', 'shared'];
      $fields_calculated = ['amount', 'gross_amount', 'net_amount', 'user_input_amount', 'weekly_amount', 'fortnightly_amount', 'annually_amount'];
      $data = [];
      foreach ($incomes as $k => $e) {
        $hasOwnerShip = false;
        // Recalculate the amounts if ownership applied
        if (!empty($e['shared']))
        {
          foreach ($e['shared'] as $share){
            if ($share['owner_id'] === $ownerId && $share['percent'] > 0) {
              foreach ($fields_calculated as $fc){
                if($e[$fc]){
                  $e[$fc] = $e[$fc] * ($share['percent'] / 100);
                }
              }
              $hasOwnerShip = true;
            }
          }
        } elseif ($e['owner_id'] === $ownerId || !$e['owner_id']) {
          $hasOwnerShip = true;
        }

        if (!$hasOwnerShip) continue;

        $e['source_id'] = $e['id'];
        // Remove Unwanted fields
        foreach ($unset_fields as $f) {
          unset($e[$f]);
        }
        $data[] = $e;
      }
      return $data;
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }
  private function _mapExpenses($expenses)
  {
    try {
      $unset_fields = ['id', 'lead_id', 'owner_id', 'shared', 'shared_percentage', 'created', 'updated', 'lead_owner_expenses_breakdown'];
      $unset_fields_breakdown = ['id', 'lead_owner_expense_id', 'created', 'updated'];

      foreach ($expenses as $k => $e) {
        $expenses[$k]['source_id'] = $expenses[$k]['id'];
        $expenses[$k]['amount'] = ($expenses[$k]['shared'] === true) ? ($expenses[$k]['shared_amount']) : $expenses[$k]['amount'];

        foreach ($expenses[$k]['lead_owner_expenses_breakdown'] as $b => $breakdown) {
          foreach ($unset_fields_breakdown as $f) {
            unset($expenses[$k]['lead_owner_expenses_breakdown'][$b][$f]);
          }
        }
        $expenses[$k]['breakdown'] = $expenses[$k]['lead_owner_expenses_breakdown'];
        foreach ($unset_fields as $f) {
          unset($expenses[$k][$f]);
        }
      }
      return $expenses;
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }

  /**
   * Create income rows for partner_account_people from finance
   * @param mixed $finance
   * @return array<array>|bool
   */
  private function _mapIncomesFinances($finance)
  {
    try {
      $incomes = [];
      $incomeMap = [
        1 => 'income_monthly_net', //Salary/Wages
        2 => 'income_monthly_investment_property', //Rental income
        3 => 'income_monthly_investment', //Other Investments
        4 => 'income_monthly_government', //Centrelink and Family Benefits
        5 => 'income_monthly_superannuation', //Superannuation
        6 => 'income_monthly_other' //Other Income
      ];
      foreach ($incomeMap as $confIncomeId => $col) {
        $incomes[] = [
          'source_finance_id' => $finance['lead_owner_finance_id'],
          'config_income_id' => $confIncomeId,
          'gross_amount' => $finance[$col],
        ];
      }
      return $incomes;
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }

  /**
   * Create expense rows for partner_account_people from finance
   * @param mixed $finance
   * @return array<array>|bool
   */
  private function _mapExpensesFinances($finance)
  {
    try {
      $expenses = [];
      $expenseMap = [
        1 => 'expense_monthly_rent_board', //Rent/Board
        2 => 'expense_transport', //Vehicles/Transport
        3 => 'expense_groceries_pet_care', //Food/Groceries
        4 => 'expense_clothing_personal_care', //Clothing/Personal care
        5 => 'expense_private_schooling_and_tuition', //Private School Fees
        6 => 'expense_out_of_pocket_healthcare_costs', //Health Insurance and Medical
        7 => 'expense_other_insurance', //Other Insurances
        8 => 'expense_telephone_internet_tv', //Phone, Internet and Cable
        9 => 'expense_utilities', //Utilities, Rates and Body Corp
        10 => 'expense_recreational_entertainment', //Recreational and Entertainment
        11 => 'expense_child_support', //Child and Spouse Support
        12 => 'expense_childcare_and_adult_education', //Childcare and Public School Fees
        13 => 'expense_other', //Other Monthly Expenses
      ];
      foreach ($expenseMap as $confExpenseId => $col) {
        $expenses[] = [
          'source_finance_id' => $finance['lead_owner_finance_id'],
          'config_expense_id' => $confExpenseId,
          'amount' => $finance[$col],
        ];
      }
      return $expenses;
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }

  private function _mapEmployments($employments)
  {
    try {
      $unset_fields = ['lead_owner_employment_id', 'lead_owner_id', 'partner_account_people_id', 'created', 'updated'];
      foreach ($employments as $k => $e) {
        foreach ($unset_fields as $f) {
          unset($employments[$k][$f]);
        }
      }
      return $employments;
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }

  private function _mapPeopleAddresses($addresses)
  {
    try {
      $unset_fields = ['lead_owner_address_id', 'lead_owner_id', 'partner_account_people_id', 'created', 'updated'];
      foreach ($addresses as $k => $e) {
        foreach ($unset_fields as $f) {
          unset($addresses[$k][$f]);
        }
      }
      return $addresses;
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }

  private function _mapAssets($assets, $addSource = false, $ownerId = null)
  {
    try {
      $unset_fields = ['lead_asset_id', 'lead_id', 'lead_owner_id', 'partner_account_id', 'partner_account_people_id', 'asset_type', 'created', 'updated'];
      $fields_calculated = ['value', 'debtors_money_owed'];
      foreach ($assets as $k => $e) {
        if ($addSource === true)
          $assets[$k]['source_id'] = $assets[$k]['lead_asset_id'];
        foreach ($unset_fields as $f) {
          unset($assets[$k][$f]);
        }
        if($ownerId){//partner_account_people assets
          if($e['shared'] && is_array($e['shared']) && count($e['shared']) > 0)
          {
            $shareFound = false;
            foreach ($e['shared'] as $share){
              if($share['owner_id'] === $ownerId){
                $shareFound = true;
                foreach ($fields_calculated as $fc){
                  if($e[$fc]){
                    $assets[$k][$fc] = $e[$fc]*($share['percent'] / 100);
                  }
                }
              }
            }
            if($shareFound === false)
              unset($assets[$k]);
          }
          else//lead/account liabilties
            unset($assets[$k]);
        }
        else{
          if($e['shared'] && is_array($e['shared']) && count($e['shared']) > 0)//owner/partner_account_people assets
            unset($assets[$k]);
        }
      }
      return $assets;
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }

  private function _mapLiabilities($liabilities, $addSource = false, $ownerId = null)
  {
    try {
      $unset_fields = ['lead_liability_id', 'lead_asset_id', 'lead_id', 'lead_owner_id', 'partner_account_id', 'partner_account_people_id', 'liability', 'created', 'updated'];
      $fields_calculated = ['limit','loan_balance', 'repayment_pm', 'asset_fi_amount'];
      foreach ($liabilities as $k => $e) {
        if ($addSource === true)
          $liabilities[$k]['source_id'] = $liabilities[$k]['lead_liability_id'];
        foreach ($unset_fields as $f) {
          unset($liabilities[$k][$f]);
        }
        if($ownerId){//partner_account_people liabilities
          if($e['shared'] && is_array($e['shared']) && count($e['shared']) > 0)
          {
            $shareFound = false;
            foreach ($e['shared'] as $share){
              if($share['owner_id'] === $ownerId){
                $shareFound = true;
                foreach ($fields_calculated as $fc){
                  if($e[$fc]){
                    $liabilities[$k][$fc] = $e[$fc]*($share['percent'] / 100);
                  }
                }
              }
            }
            if($shareFound === false)
              unset($liabilities[$k]);
          }
          else//lead/account liabilities
            unset($liabilities[$k]);
        }
        else{
          if($e['shared'] && is_array($e['shared']) && count($e['shared']) > 0)//owner/partner_account_people liabilities
            unset($liabilities[$k]);
        }
      }
      return $liabilities;
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }

  private function _mapReferences($references)
  {
    try {
      $unset_fields = ['lead_reference_id', 'lead_id', 'partner_account_id', 'created', 'updated'];
      foreach ($references as $k => $e) {
        foreach ($unset_fields as $f) {
          unset($references[$k][$f]);
        }
      }
      return $references;
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }

  private function _mapAddresses($addresses, $lead)
  {
    try {
      $unset_fields = ['lead_address_id', 'lead_id', 'partner_account_id', 'created', 'updated'];
      $has_b_address = false;
      $has_r_address = false;
      foreach ($addresses as $k => $a) {
        if ($a['address_type'] == "trading") {
          $has_b_address = true;
        }
        if ($a['address_type'] == "business") {
          $has_r_address = true;
        }
        foreach ($unset_fields as $f) {
          unset($addresses[$k][$f]);
        }
      }
      if ($lead['b_address'] && $has_b_address == false) {
        $addresses[] = [
          'address_type' => 'trading',
          'address' => $lead['b_address'],
          'suburb' => $lead['b_suburb'],
          'state' => $lead['b_state'],
          'postcode' => $lead['b_postcode'],
          'country' => $lead['b_country'],
        ];
      }
      if ($lead['r_address'] && $has_r_address == false) {
        $addresses[] = [
          'address_type' => 'business',
          'address' => $lead['r_address'],
          'suburb' => $lead['r_suburb'],
          'state' => $lead['r_state'],
          'postcode' => $lead['r_postcode'],
          'country' => $lead['r_country'],
        ];
      }
      return $addresses;
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }

  private function _mapAbn($abn)
  {
    try {
      $unset_fields = ['abn_id', 'lead_id', 'partner_account_id', 'created'];
      foreach ($unset_fields as $f) {
        unset($abn[$f]);
      }
      return $abn;
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }
  private function _mapNzbn($nzbn)
  {
    try {
      $unset_fields = ['id', 'created', 'updated'];
      foreach ($unset_fields as $f) {
        unset($nzbn[$f]);
      }
      return $nzbn;
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }

  private function _mapEntityTrust($entity_trust)
  {
    try {
      $unset_fields = ['entity_trust_id', 'lead_id', 'partner_account_id'];
      foreach ($unset_fields as $f) {
        unset($entity_trust[$f]);
      }
      return $entity_trust;
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return false;
    }
  }

  public function updateLeadEntity($lead_id, $params)
  {
    try {
      $associated = [
        'Owners',
        'Owners.OwnerAllEmployments',
        'Owners.OwnerAllAddresses',
        'Owners.MailingAddress',
        'Owners.SettlementPostAddress',
        'Owners.LeadAssetsEntity',
        'Owners.LeadLiabilitiesEntity',
        'Owners.LendSignatureRequestEntity',
        'Owners.ConCreditPropSendEntity',
        'Owners.ConPrelimOwnerEntity',
        'Owners.LeadOwnerIncomeEntity',
        'Owners.LeadOwnerExpenseEntity',
        'Owners.HomeLoanPayOffOption',
        'LeadAssetFinanceEntity',
        'LeadAssetsEntity',
        'LeadLiabilitiesEntity',
        'LeadReferenceEntity',
        'AllAddresses',
        'LeadAbnLookupEntity',
        'EntityTrustEntity',
        'ConPageStatusEntity',
        'ConPrelimEntity',
        'ConReqAndObjEntity',
        'ConCreditPropFeeEntity',
        'ConRequirementEntity',
        'LeadPricingEntity',
        'LeadLenderMatchEntity',
        'LeadCreditScoreEntity',
        'LeadAssociatedDataEntity',
        'NzbnLookupEntity',
        'LeadHomeLoanDetail',
        'LeadHomeLoanDetail.HomeLoanFeature',
        'LeadHomeLoanDetail.HomeLoanRefinanceReason',
        'LeadHomeLoanProperty',
      ];
      $lead = $this->get($lead_id, [
        'contain' => $associated
      ]);
      if (isset($params['man_status_id']) && $params['man_status_id'] == '0') {
        unset($params['man_status_id']);
      }
      $this->patchEntity($lead, $params, ['associated' => $associated]);
      $dirty = $this->_getDirtyAll($lead);
      if (!$this->save($lead)) {
        log::error($lead->getErrors());
        throw new \RuntimeException('Cannot update a DB row.');
      }

      return $dirty;
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      throw new \Exception("Cannot update a DB row.");
    }
  }

  public function createLead($lead_data, $clone = false)
  {
    try {
      $lead_table = TableRegistry::getTableLocator()->get('LeadEntity');
      $associated = [
        'Owners',
        'Owners.OwnerAllEmployments',
        'Owners.OwnerAllEmployments.LeadAbnLookupEntity',
        'Owners.OwnerAllAddresses',
        'Owners.MailingAddress',
        'Owners.SettlementPostAddress',
        'Owners.LeadOwnerIncomeEntity',
        'Owners.LendSignatureRequestEntity',
        'Owners.ConCreditPropSendEntity',
        'Owners.ConPrelimOwnerEntity',
        'Owners.PartnerLeadUploadsEntity',
        'Owners.PartnerLeadUploadsEntity.PartnerLeadUploadsMetaEntity',
        'Owners.HomeLoanPayOffOption',
        'LeadHomeLoanDetail',
        'LeadHomeLoanDetail.HomeLoanFeature',
        'LeadHomeLoanDetail.HomeLoanRefinanceReason',
        'LeadHomeLoanProperty',
        'LeadOwnerIncomeEntity',
        'LeadOwnerIncomeEntity.ConIncomeShareEntity',
        'LeadOwnerExpenseEntity',
        'LeadOwnerExpenseEntity.LeadOwnerExpensesBreakdownEntity',
        'LeadAssetFinanceEntity',
        'LeadAssetsEntity',
        'LeadAssetsEntity.ConAssetShareEntity',
        'LeadLiabilitiesEntity',
        'LeadLiabilitiesEntity.LeadAssetsEntity',
        'LeadLiabilitiesEntity.ConLiabilityShareEntity',
        'LeadReferenceEntity',
        'AllAddresses',
        'LeadAbnLookupEntity',
        'EntityTrustEntity',
        'ConPageStatusEntity',
        'ConPrelimEntity',
        'ConReqAndObjEntity',
        'ConCreditPropFeeEntity',
        'ConRequirementEntity',
        'LeadPricingEntity',
        'LeadLenderMatchEntity',
        'SelectedLenderMatch',
        'PartnerLeadUploadsEntity',
        'PartnerLeadUploadsEntity.PartnerLeadUploadsMetaEntity',
        'LeadQuoteRefEntity',
        'IntermediaryLenderMappingEntity',
        'LeadAssociatedDataEntity',
        'NzbnLookupEntity',
        // 'PartnerUserLeadsEntity',//created after lead creation as we need lead_ref created when this occurs
        // 'LeadNotesEntity', // same for this
      ];
      // Only for Clone
      if ($clone) {
        // create records via hasMany relation, not belongsToMany
        $mapping = [
          'Owners.HomeLoanPayOffOption' => 'Owners.LeadOwnerHomeLoanPayOffOption',
          'LeadHomeLoanDetail.HomeLoanFeature' => 'LeadHomeLoanDetail.LeadHomeLoanFeature',
          'LeadHomeLoanDetail.HomeLoanRefinanceReason' => 'LeadHomeLoanDetail.LeadHomeLoanRefinanceReason',
        ];
        foreach ($associated as &$value) {
          if (isset($mapping[$value])) {
            $value = $mapping[$value];
          }
        }
      }

      $post_lead_data = $lead_data;
      unset($lead_data['partner_user_lead']);
      unset($lead_data['lead_notes']);

      $lead = $lead_table->newEntity($lead_data, [
        'associated' => $associated
      ]);
      $lead_table->save($lead);

      $this->_postAddLeadOptions($post_lead_data, $lead->lead_id);

      return $lead;
    } catch (\Exception $e) {
      Log::error($e->getMessage());
      Log::error($e->getTraceAsString());
      return $e;
    }
  }

  protected function __checkAccountLevel($entity)
  {
    if (isset($entity->call_queue_status) && $entity->call_queue_status !== -1) {
      $accountActive = "inactive";
    }else{
      $accountActive = "active";
    }
    $dirty = $entity->getDirty();
    $account_table = TableRegistry::getTableLocator()->get('PartnerAccountEntity');
    if (!empty($entity->lead_type) && $entity->lead_type === 'consumer' && empty($entity->account_id)) {
      // find POC owner
      if (!empty($entity->owners_all)) {
        $poc_owner = null;
        foreach ($entity->owners_all as $owner) {
          if (!empty($owner->point_of_contact)) {
            $poc_owner = $owner;
            break;
          }
        }
        // find same detail as POC owner from account people
        if (!empty($poc_owner)) {
          $people_table = TableRegistry::getTableLocator()->get('PartnerAccountPeopleEntity');
          if (!empty($poc_owner->partner_account_people_id)) {
            $matched_person = $people_table->get($poc_owner->partner_account_people_id, [
              'contain' => ['PartnerAccountLinkPeopleEntity']
            ]);
          } else {
            $orWhere = [];
            if (!empty($poc_owner->dob)) {
              $orWhere[] = ['PartnerAccountPeopleEntity.dob' => date('Y-m-d', strtotime($poc_owner->dob))];
            }
            if (!empty($poc_owner->mobile)) {
              $orWhere[] = ['PartnerAccountPeopleEntity.mobile' => $poc_owner->mobile];
            }
            if (!empty($poc_owner->phone)) {
              $orWhere[] = ['PartnerAccountPeopleEntity.phone' => $poc_owner->phone];
            }
            if (!empty($poc_owner->email)) {
              $orWhere[] = ['PartnerAccountPeopleEntity.email' => $poc_owner->email];
            }
            $matched_person = $people_table->find('all')->contain(['PartnerAccountLinkPeopleEntity'])
              ->where([
                'PartnerAccountPeopleEntity.partner_id' => $entity->partner_id,
                'PartnerAccountPeopleEntity.first_name' => $poc_owner->first_name,
                'PartnerAccountPeopleEntity.last_name' => $poc_owner->last_name,
                'PartnerAccountPeopleEntity.status' => 'active',
                'OR' => $orWhere,
              ])
              ->order(['PartnerAccountPeopleEntity.id' => 'desc'])
              ->first();
          }

          if (!empty($matched_person->partner_account_link_people)) {
            $entity->account_id = $matched_person->partner_account_link_people[0]->account_id;
          } else {
            $account = $account_table->newEntity([
              'partner_id' => $entity->partner_id,
              'account_type' => 'consumer',
              'status' => $accountActive,
            ]);
            $account_table->save($account);
            $entity->account_id = $account->partner_account_id;
          }

          if (!empty($entity->account_id)) {
            $entity = $this->__checkAccountPeopleLevel($entity);
          }
        }
      }
    } elseif ($entity->lead_type === 'commercial') { //commercial
      $run_abn_lookup_in_bg = false;

      if ((in_array('abn', $dirty) || empty($entity->account_id)) && !empty($entity->abn)) {
        if (!empty($entity->abn_lookup)) {
          $abn_lookup = json_decode(json_encode($entity->abn_lookup), true);
          unset($abn_lookup['lead_id']);
        } else {
          // run abn lookup service
          $response = (new \Cake\Http\Client)->get(
            getenv('ABN_SERVICE_URL')."/search/{$entity->abn}",
            [],
            ['headers' => ['Content-Type' => 'application/json']]
          );

          if ($response->getStatusCode() !== 200) {
            $run_abn_lookup_in_bg = true;
          } else {
            $abn_lookup = $response->getJson();
            $abn_lookup['active'] = true;
            $abn_lookup['business_name'] = $abn_lookup['organisation_name'];
            if (empty($entity->business_name)) {
              $entity->business_name = $abn_lookup['business_name'];
            }

            // patch it to lead entity:
            $this->patchEntity($entity, [
              'abn_lookup' => $abn_lookup,
              'acn' => $abn_lookup['acn'],
            ], [
              'associated' => [
                'LeadAbnLookupEntity',
              ]
            ]);
          }
        }

        $account = $account_table->find('all')->where(['abn' => $entity->abn, 'partner_id' => $entity->partner_id])->first();
        if (empty($account)) {
          if (!empty($entity->account_id)) {
            $account = $account_table->get($entity->account_id, [
              'contain' => [
                'PartnerAccountMetaEntity',
              ]
            ]);
            if (empty($account->abn) ) {
              $account_table->patchEntity($account, [
                'abn' => $entity->abn,
                'nzbn_id' => $entity->nzbn_id,
              ]);
            } else {
              $account = $account_table->newEntity([
                'partner_id' => $entity->partner_id,
                'abn' => $entity->abn,
                'account_type' => 'company',
                'nzbn_id' => $entity->nzbn_id,
                'country' => strtoupper(getenv('REGION', true)),
                'status' => $accountActive,
              ]);
            }
          } else {
            $account = $account_table->newEntity([
              'partner_id' => $entity->partner_id,
              'abn' => $entity->abn,
              'account_type' => 'company',
              'nzbn_id' => $entity->nzbn_id,
              'country' => strtoupper(getenv('REGION', true)),
              'status' => $accountActive,
            ]);
          }

          // generate account meta data:
          $partner_account_meta = $this->_mapAccountMeta(json_decode(json_encode($entity), true));
          $account_table->patchEntity($account, [
            'partner_account_meta' => $partner_account_meta,
          ], [
            'associated' => [
              'PartnerAccountMetaEntity',
            ]
          ]);

          // patch abn_lookup to account:
          if (!empty($abn_lookup)) {
            $account_table->patchEntity($account, [
              'abn_lookup' => $abn_lookup,
            ], [
              'associated' => [
                'LeadAbnLookupEntity',
              ]
            ]);
          }

          if (array_key_exists('prev_business_name', $account->partner_account_meta->toArray()) && $account->partner_account_meta['prev_business_name'] === null) {
            $account->partner_account_meta['prev_business_name'] = 0;
          }

          $account_table->save($account);
          $entity->account_id = $account->partner_account_id;
        } else {
          $entity->account_id = $account->partner_account_id;
        }

        // add it to background job if failed
        if ($run_abn_lookup_in_bg) {
          $bg_jobs_table = TableRegistry::getTableLocator()->get('BackgroundJobEntity');
          $bg_job = $bg_jobs_table->newEntity([
            'job_type' => 'run_abn_lookup',
            'ref_id`' => json_encode(['partner_account_id' => $entity->account_id, 'abn' => $entity->abn]),
            'job_status' => 0,
            'class_name' => 'PartnerAccounts',
            'function_name' => 'runAbnLookup',
          ]);
          $bg_jobs_table->save($bg_job);
        }
      } elseif (empty($entity->account_id)) {
        // initial lead created dummy account //
        $account = $account_table->newEntity([
          'partner_id' => $entity->partner_id,
          'account_type' => 'company',
          'country' => strtoupper(getenv('REGION', true)),
          'status' => $accountActive,
        ]);
        $account_table->save($account);
        $entity->account_id = $account->partner_account_id;
      }
      $entity = $this->__checkAccountPeopleLevel($entity);
    }

    if(in_array('call_queue_status', $dirty) && $entity->call_queue_status === -1 && !empty($entity->account_id)){
      $account = $account_table->get($entity->account_id);
      $account_table->patchEntity($account, [
        'status' => 'active',
      ]);
      $account_table->save($account);
    }

    return $entity;
  }

  protected function __checkAccountPeopleLevel($entity)
  {
    if (isset($entity->call_queue_status) && $entity->call_queue_status !== -1) {
      $accountPeopleActive = "inactive";
    }else{
      $accountPeopleActive = "active";
    }
    $dirty = $entity->getDirty();
    if(!empty($entity->owners_all)){
      $people_table = TableRegistry::getTableLocator()->get('PartnerAccountPeopleEntity');
      $partner_account_link_people_table = TableRegistry::getTableLocator()->get('PartnerAccountLinkPeopleEntity');
      foreach ($entity->owners_all as $key => $owner) {
        if (!empty($owner->partner_account_people_id)) {
          $matched_person = $people_table->get($owner->partner_account_people_id);
          if(in_array('call_queue_status', $dirty) && $entity->call_queue_status === -1 && !empty($matched_person) && $matched_person->status == 'inactive'){
            $people = $people_table->get($owner->partner_account_people_id);
            $people_table->patchEntity($matched_person, [
              'status' => 'active',
            ]);
            $people_table->save($matched_person);
          }
        } else {
          $orWhere = [];
          if (!empty($owner->dob)) {
            $orWhere[] = ['PartnerAccountPeopleEntity.dob' => date('Y-m-d', strtotime($owner->dob))];
          }
          if (!empty($owner->mobile)) {
            $orWhere[] = ['PartnerAccountPeopleEntity.mobile' => $owner->mobile];
          }
          if (!empty($owner->phone)) {
            $orWhere[] = ['PartnerAccountPeopleEntity.phone' => $owner->phone];
          }
          if (!empty($owner->email)) {
            $orWhere[] = ['PartnerAccountPeopleEntity.email' => $owner->email];
          }

          $matched_person = $people_table->find('all')
            ->where([
              'PartnerAccountPeopleEntity.partner_id' => $entity->partner_id,
              'PartnerAccountPeopleEntity.first_name' => $owner->first_name,
              'PartnerAccountPeopleEntity.last_name' => $owner->last_name,
              'PartnerAccountPeopleEntity.status' => 'active',
              'OR' => $orWhere,
            ])
            ->order(['PartnerAccountPeopleEntity.id' => 'desc'])
            ->first();
        }

        if (!empty($matched_person)) {
          $entity->owners_all[$key]->partner_account_people_id = $matched_person->id;
        } else {
          $people = json_decode(json_encode($owner), true);
          $people['partner_id'] = $entity->partner_id;
          $people['status'] = $accountPeopleActive;

          $people = $people_table->newEntity($people);
          $people_table->save($people);

          $entity->owners_all[$key]->partner_account_people_id = $people->id;
        }

        // generate link between account and people
        if (!empty($entity->account_id)) {
           // check if already exists
           $partner_account_link_people = $partner_account_link_people_table->find('all')->where([
             'account_id' => $entity->account_id,
             'people_id' => $entity->owners_all[$key]->partner_account_people_id,
           ]);

           if (!$partner_account_link_people->count()) {
            $partner_account_link_people = $partner_account_link_people_table->newEntity([
              'account_id' => $entity->account_id,
              'people_id' => $entity->owners_all[$key]->partner_account_people_id,
              'is_main_point_of_contact' => $owner->point_of_contact,
            ]);
            $partner_account_link_people_table->save($partner_account_link_people);
          }
        }
      }
    }

    return $entity;
  }

  /**
   * NOTE - This functionality is handled at DB level by triggers now
   * Updates Lead asscoiated data
   * @param mixed $entity
   * @return void
   */
  private function _updateLeadAssociatedData($entity)
  {
    $dirty = $entity->getDirty();
    $lead_associated_data_table = TableRegistry::getTableLocator()->get('LeadAssociatedDataEntity');
    $lead_associated_data = [
      'lead_id' => $entity->lead_id,
    ];

    // lead_associated_data:
    if (in_array('equipment_id', $dirty) || in_array('sub_equipment_id', $dirty)) {
      if (!empty($entity->sub_equipment_id)) {
        $lead_associated_data['equipment_id'] = $entity->sub_equipment_id;
      } elseif (!empty($entity->equipment_id)) {
        $lead_associated_data['equipment_id'] = $entity->equipment_id;
      }
    }
    Log::debug('_updateLeadAssociatedData > ' . $entity->lead_id);
    $lead_associated_data_entities = $lead_associated_data_table->find('all')->where(['lead_id' => $entity->lead_id]);
    Log::debug('lead_associated_data_entities => ' . json_encode($lead_associated_data_entities));
    if ($lead_associated_data_entities->count()) {
      $lead_associated_data_entity = $lead_associated_data_entities->first();
      if (!empty($lead_associated_data_entity)) {
        $lead_associated_data_table->patchEntity($lead_associated_data_entity, $lead_associated_data);
      } else {
        $lead_associated_data_entity = $lead_associated_data_table->newEntity($lead_associated_data);
      }
    } else {
      $lead_associated_data_entity = $lead_associated_data_table->newEntity($lead_associated_data);
    }
    if (!empty($lead_associated_data_entity)) {
      $lead_associated_data_table->save($lead_associated_data_entity);
    }
  }

  private function _updateStatusHistory($entity)
  {
    $dirty = $entity->getDirty();
    $data = [];

    if ($entity->isNew()) {
      $data = [
        'lead_id' => $entity->lead_id,
        'status_id' => 1,
      ];
    } elseif (in_array('partner_status_id', $dirty)) {
      $data = [
        'lead_id' => $entity->lead_id,
        'status_id' => $entity->partner_status_id,
      ];
    }

    if (in_array('man_status_id', $dirty) && !empty($entity->man_status_id)) {
      //do we really need the current partner user, if so how do we access it here
      $dataMan = [
        'lead_id' => $entity->lead_id,
        'man_status_id' => $entity->man_status_id,
      ];
      if (!empty($_COOKIE['auth_token'])) {
        $result = LendAuthenticate::decode(['allowedAlgs' => ['HS256']], $_COOKIE['auth_token']);
        if ($result['success']) {
          $user = TableRegistry::getTableLocator()->get('PartnerUserEntity')->find('all')
            ->where(['email' => $result['payload']->sub])
            ->first();
          if (!empty($user)) {
            $dataMan['partner_user_id'] = $user->partner_user_id;
          }
        }
      }
      if($entity->partner_user_id){
        $dataMan['partner_user_id'] = $entity->partner_user_id;
      }
      $man_status_history_table = TableRegistry::getTableLocator()->get('ManStatusHistoryEntity');
      $man_status_history = $man_status_history_table->newEntity($dataMan);
      $man_status_history_table->save($man_status_history);
    }

    if (empty($data)) {
      return false;
    }

    $lead_status_history_table = TableRegistry::getTableLocator()->get('LeadStatusHistoryEntity');
    $lead_status_history = $lead_status_history_table->newEntity($data);
    $lead_status_history_table->save($lead_status_history);
  }


  private function _recordLeadStatusChange($entity, $user, $status) {
    $partnerLeadHistoryTable = TableRegistry::getTableLocator()->get('PartnerLeadHistoryEntity');
    $partnerLeadHistoryEntity = $partnerLeadHistoryTable->newEntity([
        'partner_id' => $entity->partner_id,
        'partner_user_id' => $user->partner_user_id,
        'lead_id' => $entity->lead_id,
        'created' => date('Y-m-d H:i:s'),
        'history_detail' => 'Lead ' . $status,
    ]);
    return $partnerLeadHistoryTable->save($partnerLeadHistoryEntity);
  }

  private function _updateConPageStatus($entity)
  {
    $con_page_status_table = TableRegistry::getTableLocator()->get('ConPageStatusEntity');
    $con_page_status = $con_page_status_table->find('all')->where(['lead_id' => $entity->lead_id])->first();
    if (empty($con_page_status)) {
      return $entity;
    }

    $con_req_obj_status = [
      'purpose_id',
      'equipment_id',
      'amount_requested',
      'loan_term_requested_months',
    ];
    $con_lender_pricing_status = [
      'purpose_id',
      'how_soon_id',
      'loan_term_requested_months',

    ];

    $dirty = $entity->getDirty();
    if (!empty(array_intersect($con_req_obj_status, $dirty)) && $con_page_status->con_req_obj_status === 'complete') {
      $con_page_status_table->patchEntity($con_page_status, [
        'con_req_obj_status' => 'review required'
      ]);
    } elseif ($con_page_status->con_req_obj_status === 'incomplete') {
      if (
        !empty($entity->purpose_id)
        && !empty($entity->amount_requested)
        && !empty($entity->loan_term_requested_months)
      ) {
        $lead = $this->get($entity->lead_id, [
          'contain' => ['ConReqAndObjEntity']
        ]);
        if (!empty($lead->con_req_and_obj[0]->description)) {
          $con_page_status_table->patchEntity($con_page_status, [
            'con_req_obj_status' => 'ready to confirm'
          ]);
        }
      }
    }

    // Check Lender Pricing Status:
    if (!empty(array_intersect($con_lender_pricing_status, $dirty)) && $con_page_status->con_lender_pricing_status === 'product selected') {
      $con_page_status_table->patchEntity($con_page_status, [
        'con_lender_pricing_status' => 'review required'
      ]);
    }

    $con_loan_details_status = false;
    if (
      !empty($entity->purpose_id)
      && !empty($entity->amount_requested)
      && !empty($entity->loan_term_requested_months)
      && !empty($entity->how_soon_id)
    ) {
      $con_loan_details_status = true;
      // when purpose is `Other Vehicle`, `equipment_id` is required:
      if ($entity->purpose_id === 23 && empty($entity->equipment_id)) {
        $con_loan_details_status = false;
      }
    }
    $con_page_status_table->patchEntity($con_page_status, [
      'con_loan_details_status' => $con_loan_details_status
    ]);

    $con_page_status_table->save($con_page_status);
  }

  private function _postAddLeadOptions($data, $lead_id)
  {
    if (!empty($data['partner_user_lead']) && !empty($data['partner_user_lead']['partner_user_id'])) {
      $partnerUserLeadsTable = TableRegistry::getTableLocator()->get('PartnerUserLeadsEntity');
      $data['partner_user_lead']['lead_id'] = $lead_id;
      $row = $partnerUserLeadsTable->newEntity($data['partner_user_lead']);
      $partnerUserLeadsTable->save($row);
    }

    if (!empty($data['lead_notes']) && !empty($data['lead_notes']['notes'])) {
      $leadNotesTable = TableRegistry::getTableLocator()->get('LeadNotesEntity');
      $data['lead_notes']['lead_id'] = $lead_id;
      $row = $leadNotesTable->newEntity($data['lead_notes']);
      $leadNotesTable->save($row);
    }
  }

  private function _addBackgroundJob($entity)
  {

    // $entity->isNew() always false in afterSave method
    // i cant move to beforeSave, because lead_id is empty
    if ($entity->get('is_just_created')) {
      // get partner detail:
      if (!empty($entity->partner_id)) {
        $partner_table = TableRegistry::getTableLocator()->get('PartnerEntity');
        $partner = $partner_table->get($entity->partner_id, [
          'contain' => [
            'PartnerFeatureFlagEntity',
            'ManStatusEntity',
          ]
        ]);
      }

      // call queue status check:
      if (
        !empty($partner->feature_flag->enable_call_queue) &&
        (
          empty($partner->not_allowed_source_in_queue) ||
          empty($entity->source) ||
          !in_array($entity->source, array_map(function ($item) {
            return trim($item);
          }, explode(',', $partner->not_allowed_source_in_queue)))
        ) &&
        (
          empty($partner->block_intermediary_original_partner_ids) ||
          empty($entity->intermediary_original_partner_id) ||
          !in_array($entity->intermediary_original_partner_id, array_map(function ($item) {
            return trim($item);
          }, explode(',', $partner->block_intermediary_original_partner_ids)))
        )
      ) {
        $background_jobs_table = TableRegistry::getTableLocator()->get('BackgroundJobEntity');
        $background_job = $background_jobs_table->newEntity([
          'lead_id' => $entity->lead_id,
          'job_type' => 'set_lead_call_state',
          'job_status' => 0,
          'class_name' => 'CallQueue',
          'function_name' => 'set_lead_call_state',
        ]);
        $background_jobs_table->save($background_job);
      }
    }
  }

  protected function __checkPurposeEquipment($entity)
  {
    if ($entity->lead_type === 'commercial') {
      return $entity;
    }

    $dirty = $entity->getDirty();
    if (in_array('purpose_id', $dirty) && !empty($entity->purpose_id)) {
      $new_purpose_id = $entity->purpose_id;
    }
    if (in_array('equipment_id', $dirty) && !empty($entity->equipment_id)) {
      $new_equipment_id = $entity->equipment_id;
    }

    // if purpose_id and equipment_id are not empty, then return the entity
    if (!empty($new_purpose_id) && !empty($new_equipment_id)) {
      $this->patchEntity($entity, [
        'asset_finance' => [
          'equipment_id' => $new_equipment_id,
        ],
      ], [
        'associated' => ['LeadAssetFinanceEntity'],
      ]);
      return $entity;
    }

    // if purpose_id is empty and equipment_id is not empty or it's new consumer, then set default
    if (empty($entity->purpose_id) && !empty($entity->equipment_id)) {
      $this->patchEntity($entity, [
        'purpose_id' => 20,
        'equipment_id' => 11,
        'asset_finance' => [
          'equipment_id' => 11,
        ],
      ], [
        'associated' => ['LeadAssetFinanceEntity'],
      ]);
    }

    // if purpose_id is not empty and equipment_id is empty, then set equipment_id
    if (!empty($new_purpose_id) && empty($new_equipment_id)) {
      $purpose_table = TableRegistry::getTableLocator()->get('FrmPurposeEntity');
      $purpose = $purpose_table->get($new_purpose_id);
      $this->patchEntity($entity, [
        'equipment_id' => $purpose->equipment_id,
        'asset_finance' => [
          'equipment_id' => $purpose->equipment_id,
        ],
      ], [
        'associated' => ['LeadAssetFinanceEntity'],
      ]);
    }

    return $entity;
  }

  public function reinstateLead($lead_id, $action_from){

    $update_lead_info = $this->get($lead_id, ['contain' => ['PartnerAccountEntity']]);
    $account_id       = $update_lead_info->partner_account->partner_account_id;
    $reinstate_lead   = false;

    if(!isset($account_id)){
      return $reinstate_lead;
    }

    $isLeadArchivedFromInActive = $update_lead_info->is_archived === 2;
    $isPartnerAccountInactive   = $update_lead_info->partner_account->status === 'inactive';

    if (($isLeadArchivedFromInActive && $action_from === 'archived') || ($isPartnerAccountInactive && $action_from === 'business_detail')) {
      $partnerAccountTable = TableRegistry::getTableLocator()->get('PartnerAccountEntity');

      $partnerAccount = $partnerAccountTable->get($account_id, [
        'contain' => [
            'PartnerAccountLinkPeopleEntity.PartnerAccountPeopleEntity' => function($q) {
                return $q->where(['PartnerAccountPeopleEntity.status' => 'inactive']);
            },
            'LeadEntity' => function($q) {
                return $q->where(['LeadEntity.is_archived' => 2]);
            },
            'LeadEntity.InactiveOwner'
        ]
      ]);
      // Reactivate account if inactive
      if ($partnerAccount->status === 'inactive') {
        $partnerAccount->status = 'active';
        $partnerAccountTable->save($partnerAccount);
      }
      // Reactivate all inactive people linked to this account
      if (!empty($partnerAccount->partner_account_link_people)) {
        $people_ids = collection($partnerAccount->partner_account_link_people)
          ->map(function ($link) {
              return $link->people_id;
          })
          ->toArray();

      if (!empty($people_ids)) {
          $partnerAccountPeopleTable = TableRegistry::getTableLocator()->get('PartnerAccountPeopleEntity');
          $partnerAccountPeopleTable->updateAll(
              ['status' => 'active'],
              ['id IN' => $people_ids]
          );
        }
      }
    // Reactivate inactive owners and unarchive leads
    if (!empty($partnerAccount->leads)) {
      $leadOwnersTable = TableRegistry::getTableLocator()->get('LeadOwnersEntity');
      foreach ($partnerAccount->leads as $archivedLead) {
        // Unarchive lead
        $this->updateAll(
            ['is_archived' => 0],
            ['lead_id' => $archivedLead->lead_id]
        );
        // Handle inactive owners
        if (!empty($archivedLead->inactive_owner)) {
            $owner_ids = [];
            // Handle both single entity and array of entities
            if (is_array($archivedLead->inactive_owner)) {
                foreach ($archivedLead->inactive_owner as $owner) {
                    if (!empty($owner->owner_id)) {
                        $owner_ids[] = $owner->owner_id;
                    }
                }
            } else {
                // Single entity
                if (!empty($archivedLead->inactive_owner->owner_id)) {
                    $owner_ids[] = $archivedLead->inactive_owner->owner_id;
                }
            }
            // Reactivate owners if we found any
            if (!empty($owner_ids)) {
                $leadOwnersTable->updateAll(
                    ['status' => 'active'],
                    ['owner_id IN' => $owner_ids]
                );
            }
          }
        }
      }
      $reinstate_lead = true;
    }
    return $reinstate_lead;
  }

  public function triggerGenerateAccount($lead_id){
    $entity = $this->get($lead_id, ['contain' => ['Owners']]);
    $entity = $this->__checkAccountLevel($entity);
    $this->save($entity);
  }

  private function _checkRules($data, $rules)
  {
    $success = true;
    if (!empty($rules['rules'])) {
      $condition = strtolower($rules['condition']);
      foreach ($rules['rules'] as $rule) {
        if (!empty($rule['rules'])) {
          $success = $this->_checkRules($data, $rule);
        } else {
          $success = $this->_checkOperator($data, $rule);
        }

        if (!$success && $condition === 'and') {
          break;
        } elseif ($success && $condition === 'or') {
          break;
        }
      }
    } else {
      $success = $this->_checkOperator($data, $rules);
    }

    return $success;
  }

  private function _checkOperator($data, $rule)
  {
    $operator = $rule['operator'];
    $array_field = explode('.', $rule['field']);
    $datasource = $array_field[0];
    $field = $array_field[1];
    $value = $rule['value'];
    $fieldValue = $data[$datasource][$field];
    $success = false;


    if (!in_array($operator, [
        'is_empty',
        'is_null',
        'not_contains'
      ]) &&
      empty($fieldValue) &&
      !is_numeric($fieldValue)
    ) {
      return false;
    }

    switch ($operator) {
      case 'equal':
        $success = $fieldValue == $value;
        break;
      case 'not_equal':
        $success = $fieldValue != $value;
        break;
      case 'in':
        $success = in_array($fieldValue, $value);
        break;
      case 'not_in':
        $success = !in_array($fieldValue, $value);
        break;
      case 'less':
        $success = $fieldValue < $value;
        break;
      case 'less_or_equal':
        $success = $fieldValue <= $value;
        break;
      case 'greater':
        $success = $fieldValue > $value;
        break;
      case 'greater_or_equal':
        $success = $fieldValue >= $value;
        break;
      case 'between':
        $success = $fieldValue >= $value[0] && $fieldValue <= $value[1];
        break;
      case 'not_between':
        $success = $fieldValue < $value[0] || $fieldValue > $value[1];
        break;
      case 'begins_with':
        $success = strpos($fieldValue, $value) === 0;
        break;
      case 'not_begins_with':
        $success = strpos($fieldValue, $value) !== 0;
        break;
      case 'contains':
        $success = strpos($fieldValue, $value) !== false;
        break;
      case 'not_contains':
        $success = strpos($fieldValue, $value) === false;
        break;
      case 'ends_with':
        $success = substr($fieldValue, -strlen($value)) === $value;
        break;
      case 'not_ends_with':
        $success = substr($fieldValue, -strlen($value)) !== $value;
        break;
      case 'is_empty':
        $success = empty($fieldValue);
        break;
      case 'is_not_empty':
        $success = !empty($fieldValue);
        break;
      case 'is_null':
        $success = $fieldValue === null;
        break;
      case 'is_not_null':
        $success = $fieldValue !== null;
        break;
    }
    return $success;
  }

  public function formatPayload($lead)
  {
    try {
      $lead = json_decode(json_encode($lead), true);
      $payload = [];
      $mappings = [
          'partner' => 'partners',
          'lead_credit_scores' => 'lead_credit_scores',
          'pricing' => 'lead_pricing',
          'abn_lookup' => 'abn_lookup',
          'asset_finance' => 'lead_asset_finance',
          'owners_all' => 'lead_owners',
          'preferences' => 'preferences',
          'lead_associated_data' => 'lead_associated_data',
          'assets' => 'lead_assets',
          'liabilities' => 'lead_liabilities',
          'latest_commission' => 'latest_commission',
      ];
      $owners_mappings = [
          'all_addresses' => 'addresses',
          'all_employments' => 'employments',
          'owner_credit_scores' => 'credit_scores',
      ];

      foreach ($mappings as $lend_field => $payload_field) {
          if (!empty($lead[$lend_field])) {
              $payload[$payload_field] = $lead[$lend_field];
          }
          unset($lead[$lend_field]);
      }
      foreach ($owners_mappings as $lend_field => $payload_field) {
          foreach ($payload['lead_owners'] as &$owner) {
              $owner[$payload_field] = $owner[$lend_field];
              unset($owner[$lend_field]);
          }
      }
      // remove leads address not trading:
      if (!empty($lead['all_addresses'])) {
          $payload['lead_addresses'] = array_filter($lead['all_addresses'], function ($addr) {
              return $addr['address_type'] === 'trading';
          });
      }

      $payload['leads'] = $lead;

      return $payload;
    } catch (\Exception $e) {
        Log::error($e->getMessage());
        Log::error($e->getTraceAsString());
        return false;
    }
  }
}
