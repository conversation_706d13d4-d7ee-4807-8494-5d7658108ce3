<?php

namespace App\Model\Table;

use App\Lend\MakeDecision;
use App\Model\Table\AppTable;
use App\Traits\CsvExportExtraFields;
use App\View\Helper\LendHelper;
use Cake\Log\Log;
use Cake\ORM\TableRegistry;
use Cake\Utility\Security;
use Cake\Utility\Text;

use App\Lend\Config;
use App\Lend\LendInternalAuth;
use App\Lend\LendStatus;
use Cake\Core\Configure;
use Cake\Datasource\ConnectionManager;


use Cake\Cache\Cache;
use Symfony\Component\Debug\Debug;

use App\Lend\CurlHelper;
use App\Lend\EncryptionHelper;

class LeadsTable extends AppTable
{

    use CsvExportExtraFields;

    public $lead_owner;
    public $activity;

    private $leadFields = [];
    private $fieldAliases = [];
    private $otherFields = [];
    private $joinTables = [];
    private $isLeadOnly = true;


    //map between old table aliases and new entity references
    private $entityMap = [
      'le'=>['keys'=> ['LeadEntity'=>['lead_id']], 'entity'=> 'LeadEntity', 'join'=> 'none'],
      'lo'=>['entity'=> 'LeadOwnersEntity', 'join'=> 'manual'],
      'lad'=>['entity'=> 'LeadAssociatedDataEntity', 'join'=> 'manual'],
      'pcc'=>['entity'=> 'PartnerCommissionEntity', 'join'=> 'manual'],
      'setr'=>['entity'=> 'SettlementReviewsEntity', 'join'=> 'manual'],
      'pc'=>['entity'=> 'PartnerCommissionEntity', 'join'=> 'manual'],
      'ppt'=>['entity'=> 'PartnerProductTypeEntity', 'join'=> 'manual'],
      'st'=>['entity'=> 'LendStatusEntity', 'join'=> 'manual'],
      'mt'=>['entity'=> 'ManStatusEntity', 'join'=> 'manual'],
      'len'=>['entity'=> 'LenderEntity', 'join'=> 'manual'],
      'llu'=>['entity'=> 'CurrentLenderStatus', 'join'=> 'manual'],
      'ln'=>['entity'=> 'LeadNotesEntity', 'join'=> 'manual'],
      'ln2'=>['entity'=> 'LeadNotesEntity', 'join'=> 'manual'],
      'duet'=>['entity'=> 'PartnerCallbackEntity', 'join'=> 'manual'],
      'sc'=>['entity'=> 'LendScoreEntity', 'join'=> 'manual'],
      'il'=>['entity'=> 'IntermediaryLendersEntity', 'join'=> 'manual'],
      'pu'=>['entity'=> 'PartnerUserEntity', 'join'=> 'manual'],
      'msg'=>['entity'=> 'ManStatusGroupEntity', 'join'=> 'manual'],
      'cps'=>['entity'=> 'ConPageStatusEntity', 'join'=> 'manual'],
      'refp'=>['entity'=> 'ReferrerPeople', 'join'=> 'manual'],
      'laf'=>['entity'=> 'LeadAssetFinanceEntity', 'join'=> 'manual'],
      'sal'=>['entity'=> 'SaleEntity', 'join'=> 'manual'],
      'sd'=>['entity'=> 'SaleDetailEntity', 'join'=> 'manual'],
      'llu2'=>['entity'=> '', 'join'=> 'manual'],
      'pu2'=>['entity'=> '', 'join'=> 'manual'],
      'pu3'=>['entity'=> '', 'join'=> 'manual'],
      'ollad'=>['entity'=> '', 'join'=> 'manual'],
      'ol_llu'=>['entity'=> 'OriginalLenderLeadUpdateEntity', 'join'=> 'manual'],
      'st2'=>['entity'=> '', 'join'=> 'manual'],
      'pul'=>['entity'=> 'PartnerUserName', 'join'=> 'manual'],
      'pu_lead'=>['entity'=> 'PartnerUserLeadsEntity', 'join'=> 'manual'],
      'ln3'=>['entity'=> '', 'join'=> 'manual'],
    ];


    function __construct($arg) {
			parent::__construct($arg);
      $this->lead_owner = TableRegistry::get('LeadOwners');
      $this->abnlookup = TableRegistry::get('LeadAbnLookup');
      $this->activity = TableRegistry::get('LeadActivity');
      $this->sales = TableRegistry::get('Sales');
      $this->applicants = TableRegistry::get('Applicants');
      $this->send_prevention = TableRegistry::get('LeadSendPrevention');
		}

    public function initialize(array $config){
        parent::initialize($config);
    }

    // When leads status changed, we will add data into [lead_activity] table with $activity_code=5.
    private function addActivityFive($new_status, $lead_id, $prev_status=null){
      // If we don't know previous status, get status from [leads] table first.
      if(empty($prev_status)){
        $lead = $this->search(array('lead_id'=>$lead_id));
        $prev_status = $lead['leads'][0]['status_id'];
      }

      if($prev_status !== $new_status){
        $activity_type = 'Status change:['.$prev_status.'] to ['.$new_status.']';
        $this->activity->addActivity($lead_id, $activity_type, false, 5);
      }
    }

    public function search($data) {
			/*@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
			 @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
			 	THESE SEARCH FUNCTIONS NEED RE-DOING / SOME "TLC"
				Since we have moved all details to owners, they are not as
				good as they used to be. A bit "hacky".
			 @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
			@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@*/
			$data = array_filter($data); // Remove empty search terms
			if ( count($data) === 0 ) return array(); // Return no results if no criteria was set

			$to_search_lead_table = array();
			if (isset($data['external_id'])) $to_search_lead_table['external_id'] = $data['external_id'];
			if (isset($data['organisation_name'])) $to_search_lead_table['organisation_name'] = $data['organisation_name'];
			if (isset($data['abn'])) $to_search_lead_table['abn'] = $data['abn'];
			if (isset($data['b_address'])) $to_search_lead_table['b_address'] = $data['b_address'];
			if (isset($data['b_suburb'])) $to_search_lead_table['b_suburb'] = $data['b_suburb'];
			if (isset($data['b_postcode'])) $to_search_lead_table['b_postcode'] = $data['b_postcode'];
			if (isset($data['lead_id'])) $to_search_lead_table['lead_id'] = $data['lead_id'];
			if (isset($data['status_id'])) $to_search_lead_table['status_id'] = $data['status_id'];
			if (isset($data['email'])) $to_search_lead_table['email_business'] = $data['email'];
			if (isset($data['website'])) $to_search_lead_table['website'] = $data['website'];
			if (isset($data['phone'])) $to_search_lead_table['business_phone'] = $data['phone'];
			if (isset($data['phone'])) $to_search_lead_table['business_phone_other'] = $data['phone'];
			if (isset($data['user_id'])) $to_search_lead_table['user_id'] = $data['user_id'];
			if (isset($data['in_lead_id'])) $to_search_lead_table['in_lead_id'] = $data['in_lead_id'];

			if (!empty($to_search_lead_table)) {
				$leads_sql = $this->fullSearchOfLeadTableX('leads', $to_search_lead_table);
				$leads = $this->DB->execute($leads_sql['sql'], $leads_sql['params'])->fetchAll('assoc');
				for ($i=0; $i < count($leads); $i++) {
					$leads[$i]['external_id'] = (new LendInternalAuth)->hashLeadId($leads[$i]['lead_id']);
					$poc_details = $this->DB->execute('SELECT first_name, last_name, phone, mobile FROM lead_owners WHERE lead_id=? AND point_of_contact=1', [$leads[$i]['lead_id']])->fetch('assoc');
					$leads[$i]['first_name'] = $poc_details['first_name'];
					$leads[$i]['last_name'] = $poc_details['last_name'];
					$leads[$i]['phone'] = $poc_details['phone'];
					$leads[$i]['mobile'] = $poc_details['mobile'];
				}
			} else {
				$leads = array();
			}

			$to_search_lead_owners_table = array();
			if (isset($data['last_name'])) $to_search_lead_owners_table['last_name'] = $data['last_name'];
			if (isset($data['email'])) $to_search_lead_owners_table['email'] = $data['email'];
			if (isset($data['b_address'])) $to_search_lead_owners_table['address'] = $data['b_address'];
			if (isset($data['b_suburb'])) $to_search_lead_owners_table['suburb'] = $data['b_suburb'];
			if (isset($data['b_postcode'])) $to_search_lead_owners_table['postcode'] = $data['b_postcode'];
			if (isset($data['phone'])) $to_search_lead_owners_table['mobile'] = $data['phone'];
			if (isset($data['phone'])) $to_search_lead_owners_table['phone'] = $data['phone'];
			if (isset($data['address'])) $to_search_lead_owners_table['address'] = $data['address'];

			if (!empty($to_search_lead_owners_table)) {
				$people_sql = $this->fullSearchOfLeadTableX('lead_owners', $to_search_lead_owners_table);
				$people = $this->DB->execute($people_sql['sql'], $people_sql['params'])->fetchAll('assoc');
			} else {
				$people = array();
			}

			return array(
				'leads' => $leads,
				'people' => $people
			);
		}


		private function fullSearchOfLeadTableX($table, $data) {
			$sql = 'SELECT * FROM '.$table.' WHERE '; // Prepare the beginning of the sql
				// Loop through search terms data
				$arr_where = array();
				$arr_param = array();
				foreach ($data as $key => $value) {

						// Check if we should be searching this key over multiple columns
						switch ($key) {
							case 'external_id':
								$value = (new LendInternalAuth)->unhashLeadId($value);
								$key = 'lead_id'; // This is actually a lead ID search
								$keytwo = 'external_id'; // The paramter can't match 'lead_id' as that exists below.
								$arr_where[] = '('.$key.' = :'.$keytwo.') '; // Exact Match
								$arr_param[$keytwo] = $value;
								break;
							case 'organisation_name':
								$arr_where[] = '('.$key.' LIKE :'.$key.' OR business_name LIKE :'.$key.') ';
								$arr_param[$key] = '%'.$value.'%';
								break;
							case 'abn':
								$arr_where[] = '('.$key.' = :'.$key.' OR acn = :'.$key.') ';
								$arr_param[$key] = $value;
								break;
							case 'b_address':
								$arr_where[] = '('.$key.' LIKE :'.$key.' OR r_address LIKE :'.$key.') ';
								$arr_param[$key] = '%'.$value.'%';
								break;
							case 'b_suburb':
								$arr_where[] = '('.$key.' LIKE :'.$key.' OR r_suburb LIKE :'.$key.' ) ';
								$arr_param[$key] = '%'.$value.'%';
								break;
							case 'b_postcode':
								$arr_where[] = '('.$key.' LIKE :'.$key.' OR r_postcode LIKE :'.$key.' ) ';
								$arr_param[$key] = '%'.$value.'%';
								break;
							case 'lead_id': case 'status_id':
								$arr_where[] = '('.$key.' = :'.$key.') '; // Exact Match
								$arr_param[$key] = $value;
								break;
							case 'in_lead_id':
								$arr_where[] = 'lead_id IN ('.implode(',', $value).')';
								break;
							default:
								$arr_where[] = '('.$key.' LIKE :'.$key.') '; // Like
								$arr_param[$key] = '%'.$value.'%';
								break;
						}
				}
				$where = implode(' AND ', $arr_where);
				$sql .= $where;
				return array('sql'=>$sql, 'params'=>$arr_param);
		}

    /*
    * Marks a lead as fake
    */
    public function markAsFake($leadid, $prev_status=null) {
      $this->addActivityFive(5, $leadid, $prev_status);
      $this->DB->execute('UPDATE leads SET status_id=5 WHERE lead_id = "'.$leadid.'"');
      return true;
    }


    /*
    */
    public function isValidStatusName($openedTab = '') {
      $validLendStatusNames = LendStatus::getPartnerStatuses(null, null);
      return (!empty($openedTab) && in_array($openedTab, $validLendStatusNames))
          || (!empty($openedTab) && $openedTab == 'Leads');
    }

    /*
      @params: $datesArr - array contain 'start_date' , 'end_date'
      return : boolean(true)  - if both start_date and end_date are passed in and they are also valid dates range
              string ('Error text') - if both start_date and end_date are passed in and not valid
              string ('') - if any of start_date, end_date is not passed in
    */
    public function isValidDatesRange($datesArr = array()) {
      try {
        $ret = '';

        if (isset($datesArr['start_date'], $datesArr['end_date'])) {
          //check format first
          if (!preg_match('/^[0-9]{4}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])$/', $datesArr['start_date']))
            throw new \Exception('Invalid Start Date');
          if (!preg_match('/^[0-9]{4}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])$/', $datesArr['end_date']) )
            throw new \Exception('Invalid End Date');

          //check if both start_date and end_date are valid in format 'YYYY-MM-DD'
          $date1 = new \DateTime($datesArr['start_date']);

          //make sure the date is valid
          $errors = \DateTime::getLastErrors();
          if ($errors['warning_count'] || $errors['error_count'])
            throw new \Exception('Start Date is invalid');

          $date2 = new \DateTime($datesArr['end_date']);

          $errors = \DateTime::getLastErrors();
          if ($errors['warning_count'] || $errors['error_count'])
            throw new \Exception('End Date is invalid');

          $today = new \DateTime(date('Y-m-d'));

          //start Date and End Date must be valid range
          $days1 = $date2->diff($date1)->days;

          //End Date and Today's date must be valid range
          $days2 = $today->diff($date2)->days;

          if ($days2 < 0 || $days1 < 0)
            throw new \Exception('Start Date, End Date must be valid');
          else
            $ret = true;
        }
      } catch (\Exception $e) {
        $ret = $e->getMessage();
      }
      return $ret;
    }

    public function mapOldEquipmentSource($equipmentSource){
      if ($equipmentSource == "Private") return 'Private Sale';
      if ($equipmentSource == "Other") return 'Other Sale';
      return $equipmentSource;
    }

    public function formatLeadData($data, $config=array(), $fromAPI = false) {
        // The API also uses this function, but in Api to make it more legible for external users we call it "owners", so...
        if (isset($data['owner'])) {
          $data['lead_owner'] = $data['owner'];
          unset($data['owner']);
        }

        if (!empty($data['lead']['equipment_source'])) {
          $data['lead']['equipment_source'] = $this->mapOldEquipmentSource($data['lead']['equipment_source']);
        }

        if (!empty($config['check_all_owners'])) {
          foreach ($data['lead_owner'] as $key => $owner) {
            if(!empty($owner['home_owner']) OR $owner['home_owner'] == 'on'){
              $data['lead_owner'][$key]['home_owner'] = 1;
            } else {
              $data['lead_owner'][$key]['home_owner'] = 0;
            }
            $data['lead_owner'][$key]['country'] = strtoupper(getenv('REGION', true));
          }
        } else {
          if(!empty($data['lead_owner']['home_owner']) OR $data['lead_owner']['home_owner'] == 'on'){
            $data['lead_owner']['home_owner'] = 1;
          } else {
            $data['lead_owner']['home_owner'] = 0;
          }
          $data['lead_owner']['country'] = strtoupper(getenv('REGION', true));
        }

				// For the following fields... Remove any spaces
				$space_removal_fields = array('lead'=>array('abn', 'acn'));
				foreach ($space_removal_fields as $table=>$fields) foreach($fields as $key=>$field) if (!empty($data[$table][$field]))
					$data[$table][$field] = str_replace(' ','',$data[$table][$field]);

				// For the following fields... Remove any commas or dollar signs
        $comma_removal_fields = array(
          'lead' => array('sales_last_month', 'amount_requested', 'sales_annual', 'monthly_expenses', 'sales_monthly', 'merchant_monthly_sales', 'existing_loan_borrowed', 'existing_loan_owing', 'existing_loan_repayment', 'business_cc_limit', 'business_cc_limit_left', 'statements_rev_last_month', 'statements_rev_avg_month',
  				'other_payable', 'business_cc_limit', 'business_cc_payable', 'overdraft_loc_limit', 'overdraft_loc_payable', 'existing_loan_repayment', 'existing_loan_owing', 'tax_overdue', 'super_payable', 'payg_payable', 'gst_liability', 'accounts_payable', 'rent_mth'),
        );
				foreach ($comma_removal_fields as $table=>$fields) foreach($fields as $key=>$field) if (!empty($data[$table][$field]))
					$data[$table][$field] = preg_replace('/[^0-9\.]/', '', $data[$table][$field]);


				// For the following fields... Strip anything not numeric (Formats phonenumbers FROM our consistent way (XXXX-XXX-XXX), to 'XXXXXXXXXX' for DB)
				$strip_nonnumeric_removal_fields = array('lead'=>array('business_phone', 'business_phone_other'),
                                                  'lead_owner'=>array('mobile', 'phone'));
				foreach ($strip_nonnumeric_removal_fields as $table=>$fields){
          if ($table === 'lead_owner' AND !empty($config['check_all_owners'])) {
            foreach ($data[$table] as $key => $owner) {
              foreach($fields as $field){
                if (!empty($data[$table][$key][$field])) {
        					// Remove +610, +61, 610, 61 if they are at the beginning of the string
        					if (substr($data[$table][$key][$field], 0, 4)==='+610')      { $data[$table][$key][$field] = substr($data[$table][$key][$field], 3);
        					} elseif (substr($data[$table][$key][$field], 0, 3)==='+61') { $data[$table][$key][$field] = '0'.substr($data[$table][$key][$field], 3); /* Add 0 */
        					} elseif (substr($data[$table][$key][$field], 0, 3)==='610') { $data[$table][$key][$field] = substr($data[$table][$key][$field], 2);
        					} elseif (substr($data[$table][$key][$field], 0, 2)==='61')  { $data[$table][$key][$field] = '0'.substr($data[$table][$key][$field], 2); /* Add 0 */ }
        					$data[$table][$key][$field] = preg_replace('/[^0-9]/', '', $data[$table][$key][$field]);
                }
      				}
            }
          } else {
            foreach($fields as $key=>$field){
              if (!empty($data[$table][$field])) {
      					// Remove +610, +61, 610, 61 if they are at the beginning of the string
      					if (substr($data[$table][$field], 0, 4)==='+610')      { $data[$table][$field] = substr($data[$table][$field], 3);
      					} elseif (substr($data[$table][$field], 0, 3)==='+61') { $data[$table][$field] = '0'.substr($data[$table][$field], 3); /* Add 0 */
      					} elseif (substr($data[$table][$field], 0, 3)==='610') { $data[$table][$field] = substr($data[$table][$field], 2);
      					} elseif (substr($data[$table][$field], 0, 2)==='61')  { $data[$table][$field] = '0'.substr($data[$table][$field], 2); /* Add 0 */ }
      					$data[$table][$field] = preg_replace('/[^0-9]/', '', $data[$table][$field]);
              }
    				}
          }
        }

        if (empty($config['dont_toggle_phonenumbers'])):
          // Decide phone or mobile
          if (!empty($config['check_all_owners'])) {
            foreach ($data['lead_owner'] as $key => $owner) {
              if (getenv('REGION', true) === 'au' && !empty($owner['mobile']) AND substr($owner['mobile'], 0, 2)!=='04') {
                $data['lead_owner'][$key]['phone'] = $owner['mobile'];
                $data['lead_owner'][$key]['mobile'] = '';
              } elseif(getenv('REGION', true) === 'nz' && !empty($owner['mobile']) AND substr($owner['mobile'], 0, 2)!=='02') {
                $data['lead_owner'][$key]['phone'] = $owner['mobile'];
                $data['lead_owner'][$key]['mobile'] = '';
              }
            }
          } else {
            if (getenv('REGION', true) === 'au' && !empty($data['lead_owner']['mobile']) AND substr($data['lead_owner']['mobile'], 0, 2)!=='04') {
              $data['lead_owner']['phone'] = $data['lead_owner']['mobile'];
              $data['lead_owner']['mobile'] = '';
            } elseif (getenv('REGION', true) === 'nz' && !empty($data['lead_owner']['mobile']) AND substr($data['lead_owner']['mobile'], 0, 2)!=='02') {
              $data['lead_owner']['phone'] = $data['lead_owner']['mobile'];
              $data['lead_owner']['mobile'] = '';
            }
          }
        endif;

				// Sometimes, DOB may arrive in a different format, so change from [dd/mm/yy] format to [yy-mm-dd].
        // only overwrite DOB and driving_licence_expiry data when not come from Partner API

        if (!empty($config['check_all_owners'])) {
          foreach ($data['lead_owner'] as $key => $owner) {
            if (!empty($owner['dob']) AND !$fromAPI) {
    					$data['lead_owner'][$key]['dob'] = $this->setValidDateFormat($owner['dob']);
            }
            if (!empty($owner['driving_licence_expiry']) AND !$fromAPI) {
              $data['lead_owner'][$key]['driving_licence_expiry'] = $this->setValidDateFormat($owner['driving_licence_expiry']);
            }
          }
        } else {
          if (!empty($data['lead_owner']['dob']) and !$fromAPI) {
            $data['lead_owner']['dob'] = $this->setValidDateFormat($data['lead_owner']['dob']);
          }
          if (!empty($data['lead_owner']['driving_licence_expiry']) AND !$fromAPI) {
            $data['lead_owner']['driving_licence_expiry'] = $this->setValidDateFormat($data['lead_owner']['driving_licence_expiry']);
          }
        }

				// If trading since is split up
				if (!empty($data['lead']['company_registration_date']) AND is_array($data['lead']['company_registration_date'])){
          if(!empty($data['lead']['company_registration_date']['yyyy'])){
  					$data['lead']['company_registration_date'] = (!empty($data['lead']['company_registration_date']['yyyy'])?$data['lead']['company_registration_date']['yyyy']:'0000').'-'.
                                              (!empty($data['lead']['company_registration_date']['mm'])?$data['lead']['company_registration_date']['mm']:'00').'-'.
                                              (!empty($data['lead']['company_registration_date']['dd'])?$data['lead']['company_registration_date']['dd']:'01');
          }else{
            unset($data['lead']['company_registration_date']);
          }
        }
        // Add company_registration_date into trading_since until we remove it from DB.
        $data['lead']['trading_since'] = !empty($data['lead']['company_registration_date']) ? $data['lead']['company_registration_date'] : '';

        if(empty($data['lead']['years_in_business']) && !empty($data['lead']['company_registration_date']) && strtotime($data['lead']['company_registration_date']) > 0){
          $data['lead']['years_in_business'] = date('Y', $data['lead']['created'] ? strtotime($data['lead']['created']): time()) - date('Y', strtotime($data['lead']['company_registration_date']));  
        }
        if (!empty($data['abnlookup']['state']) ) {
          if (empty($data['lead']['b_state'])) {
            $data['lead']['b_state'] = $data['abnlookup']['state'];
          }
          if (!empty($config['check_all_owners'])) {
            foreach ($data['lead_owner'] as $key => $owner) {
              if (empty($owner['state'])) {
                $data['lead_owner'][$key]['state'] = $data['abnlookup']['state'];
              }
            }
          } else {
            if (empty($data['lead_owner']['state'])) {
              $data['lead_owner']['state'] = $data['abnlookup']['state'];
            }
          }
        }

        if($data['lead']['b_address'])
          $data['trading_address']['address'] = $data['lead']['b_address'];
        if($data['lead']['b_suburb'])
          $data['trading_address']['suburb'] = $data['lead']['b_suburb'];
        if($data['lead']['b_state'])
          $data['trading_address']['state'] = $data['lead']['b_state'];
        if($data['lead']['b_postcode'])
          $data['trading_address']['postcode'] = $data['lead']['b_postcode'];
        if($data['lead']['b_country'])
          $data['trading_address']['country'] = $data['lead']['b_country'];

    /*$data['lead']['call_me_first'] = !empty($data['lead']['call_me_first']) ? '1' : '0';
          $data['lead_owner']['home_owner'] = !empty($data['lead_owner']['home_owner']) ? '1' : '0';
          $data['lead']['equipment_found'] = !empty($data['lead']['equipment_found']) ? '1' : '0';*/

    foreach ($data as $table => $fields) {
      if (is_array($fields)) {
        foreach ($fields as $f => $v) {
          if (is_array($v) || is_object($v) || is_bool($v) || is_numeric($v)) {
            $data[$table][$f] = $v;
          } else {
            $data[$table][$f] = trim($v);
          }
        }
      }
    }
    $data['lead']['b_country'] = $data['lead']['r_country'] = strtoupper(getenv('REGION', true));

    if ($data['lead_asset_finance']) {
        $data['lead_asset_finance'] = TableRegistry::getTableLocator()->get('LeadAssetFinanceEntity')->calculateFinancedAmountAndLtv($data['lead_asset_finance'], $data['lead']['amount_requested']);
    }
      if (isset($data['lead_owner']['first_name'])) {
          $data['lead_owner']['first_name'] = ucfirst($data['lead_owner']['first_name']);
      }
      if (isset($data['lead_owner']['middle_name'])) {
          $data['lead_owner']['middle_name'] = ucfirst($data['lead_owner']['middle_name']);
      }
      if (isset($data['lead_owner']['last_name'])) {
          $data['lead_owner']['last_name'] = ucfirst($data['lead_owner']['last_name']);
    }

      return $data;
    }

    public function addTradingAddress($leadId, $addressData){
      $count = $this->DB->execute("SELECT count(lead_address_id) addresses FROM lead_addresses WHERE lead_id = ".$leadId)->fetch('assoc');
      //only add if not present
      if($count['addresses'] == 0){
        $extraData = ['lead_id' => $leadId, 'address_type' => 'trading', 'created' => date('Y-m-d H:i:s')];
        $addressData = array_merge($addressData, $extraData);
        $q = $this->getInsertQuery('lead_addresses', $addressData);
        $this->DB->execute($q['query'], $q['values']);
      }
    }

    public function addLead($params){
      if (isset($params['lead_id'])) unset($params['lead_id']); // Test to see if it fixes GA's bug

      if (!isset($params['created'])) $params['created'] = date('Y-m-d H:i:s');
      // $params['industry_id'] = !empty($params['industry_id']) ? $params['industry_id'] : 189; Remove default, see DT-331
      // $params['purpose_id'] = !empty($params['purpose_id']) ? $params['purpose_id'] : 8; Remove default, see DT-376
      $params['force_send'] = !empty($params['force_send']) ? '1' : '0';

      $lead = TableRegistry::getTableLocator()->get('LeadEntity')->createLead($params);
      return $lead->lead_id;
    }

    public function updateLead($params, $unsetLeadRef = true){
      $params['last_changed_date'] = date('Y-m-d H:i:s');
      if (!empty($params['lead_ref']) && empty($params['lead_id'])) {
        $conditions = array('lead_ref' => $params['lead_ref']);
      } else {
        $conditions = array('lead_id'=>$params['lead_id']);

        unset($params['lead_id']);
      }
      if ($unsetLeadRef !== false && array_key_exists('lead_ref', $params)) {
        unset($params['lead_ref']);
      }
      $q = $this->getUpdateQuery('leads', $params, $conditions);
      return $this->DB->execute($q['query'], $q['values']);
    }

    public function updatePartnerStatusId($lead_id, $status){
      $this->DB->execute("INSERT INTO lead_status_history (lead_id, status_id, created) VALUES (?, ?, ?);", [$lead_id, $status, date('Y-m-d H:i:s')]);
      return $this->DB->execute("UPDATE leads SET partner_status_id=?, last_changed_date=? WHERE lead_id=?", [$status, date('Y-m-d H:i:s'), $lead_id]);
    }

    public function getLead($params){
      $q = $this->getSelectQuery('leads', $params, false);
      $lead = $this->DB->execute($q['query'], $q['values'])->fetch('assoc');
      if ($lead) $lead['hashed_lead_id'] = (new LendInternalAuth)->hashLeadId($lead['lead_id']);
      return $lead;
    }

    public function leadIdFromRef($leadRef){
      //convert lead ref back to lead id
      $leadId = (new LendInternalAuth)->unhashLeadId($leadRef);
      return $leadId;
    }

    public function getLeadDetails($lead_id, $settings=array()){
      $lead = $this->getLead(array('lead_id'=>$lead_id));
      if (!$lead) return false;
      $lead['client_login_code'] = $this->createAutoLoginCode($lead_id);
      $product_type = TableRegistry::get('PartnerProductTypes')->getProductSubType($lead['product_type_id']);

      if(!empty($settings['get_all_owners'])){
        $owner = $this->lead_owner->getLeadOwners(array('lead_id'=>$lead_id, 'status' => 'active'));
      }else{
        $owner = $this->lead_owner->getLeadOwner(array('lead_id'=>$lead_id, 'point_of_contact'=>1, 'status' => 'active'));
      }

      $lead_asset_finance = TableRegistry::get('LeadAssetFinance')->getLeadAssetFinanceByLeadID($lead_id);
      $trust = TableRegistry::get('EntityTrust')->getEntityTrust(array('lead_id'=>$lead_id));
      $abn = $this->abnlookup->getLeadABN($lead_id);
      if (!empty($lead['nzbn_id'])) {
        $nzbn = TableRegistry::getTableLocator()->get('NzbnLookupEntity')->get($lead['nzbn_id'])->toArray();
      }
      $bs_accounts = [];
      $bank_statements_analysis = [];
      if(!isset($settings['include_bs']) || $settings['include_bs'] === true){
        $bs_accounts = TableRegistry::get('BankStatementsAccounts')->getBSAccounts(array('lead_id'=>$lead_id, 'selected'=>1));
        $bank_statements_analysis = TableRegistry::get('BankStatementsAnalysis')->getBSAnalysis(array('lead_id'=>$lead_id));
        if(!$bank_statements_analysis){
          $bs = TableRegistry::get('BankStatements')->getBankStatements(array('lead_id'=>$lead_id));
          if($bs AND count($bs)===1){
            $bank_statements_analysis = TableRegistry::get('BankStatementsAnalysis')->getBSAnalysis(array('bank_statement_id'=>$bs[0]['bank_statement_id']));
          }
        }

        if(!$bank_statements_analysis && $lead['account_id']){
          $bank_statements_analysis = TableRegistry::get('BankStatementsAnalysis')->getBSAnalysis(array('partner_account_id'=>$lead['account_id'], 'lead_id'=>$lead_id));
        }

        if($bank_statements_analysis){
          $bank_statements_analysis = $bank_statements_analysis[0];
        }
      }
      
      if(!isset($settings['include_percentage']) || $settings['include_percentage'] === true){
        $tmp = $this->checkPercentage(array('lead'=>$lead, 'lead_owner' => (!empty($settings['get_all_owners']) ? $owner[array_search('1', array_column($owner, 'point_of_contact'))] : $owner)));
        $lead['percent_complete'] = $tmp['current_percentage'];
        unset($tmp);
      }else{
        $lead['percent_complete'] = 0;
      }

      //get all but the most recent sale at the beginning of that lead
      $tmp = $this->DB->execute("SELECT s.product_id, s.status as sale_status, s.additional_info, s.created as sent, lp.lender_product_id, lp.lender_id, l.*
      FROM sales s
      LEFT JOIN lender_product lp
      ON (lp.lender_product_id = s.product_id)
      LEFT JOIN lenders l
      ON (l.lender_id = lp.lender_id)
      WHERE s.lead_id = ?
      ORDER BY s.sale_id DESC
      ", [$lead_id])->fetchAll('assoc');

      // override sales data
      foreach($tmp as $k=>$t){
        $tmp[$k]['status'] = $t['sale_status'];
        $tmp[$k]['created'] = $t['sent'];
        unset($tmp[$k]['sale_status']);
        unset($tmp[$k]['sent']);
      }

      //get lender info
      $lender = !empty($tmp)? $tmp : array();

      $toreturn = array(
                    'lead'=>$lead,
                    'lead_owner'=>$owner,
                    'lead_asset_finance' => $lead_asset_finance,
                    'trust' => $trust,
                    'product_type'=>(!empty($product_type) ? $product_type : []),
                    'abnlookup'=>(!empty($abn) ? $abn : (object)[]),
                    'nzbnlookup' => @$nzbn,
                    'bs_accounts'=>$bs_accounts,
                    'bank_statements_analysis'=>$bank_statements_analysis,
                    'lender' => $lender, // it contains an array, most recent sale has key 0
                  );

      if (!empty($settings['include_partner']) AND $settings['include_partner'])
        $toreturn['partner'] = TableRegistry::get('Partners')->getPartner(array('partner_id' => $lead['partner_id']));

      return $toreturn;
    }

    /* ==============================================================================
    * NOTE:: When you add new required fields for percentage checking function below,
    *        Please concern that every page we call this function.
    *        Some pages also need to be added the new fields in their query.
    ============================================================================== */
    public function checkPercentage($lead, $includingBS = false){
      $missingFields = array();
      if (!empty($lead['lead']['bs_doc_id']) && empty($lead['lead']['bs_doc_retrieved']))
      {
        $lead['lead']['statements_uploaded'] = null;
      }
      $required = array(
                    'lead'        => array(
                      /*'abn', */'organisation_name', 'amount_requested', 'purpose_id', 'statements_uploaded', /*'loan_term_requested',*/ /*'industry_id',*/ 'sales_monthly', 'b_address',
                    ),
                    'lead_owner'  => array(
                      'first_name', 'last_name', 'email',/* 'dob', 'driving_licence_num', 'home_owner', 'owner_type', 'address',*/
                    ));

      // if ($includingBS === true)
      //   $required['lead'][] = 'statements_uploaded';

      //Organise
      if(empty($lead['lead_owner'])){
      $extra_owner_fields = array(/*'owner_type', 'equity',*/ 'mobile', 'phone', /*'home_owner_detail'*/);
        if(!empty($lead['lead'])){
          foreach($lead['lead'] as $field=>$value){
            if(in_array($field, $required['lead_owner']) OR in_array($field, $extra_owner_fields)){
              $lead['lead_owner'][$field]=$value;
              unset($lead['lead'][$field]);
            }
          }
        }
      }

      /* =======================
      *  Special case
      * ======================= */
      if (empty($lead['lead']['is_abn_unknown'])) {
        $required['lead'][] = 'abn';
      }
      if(empty($lead['lead']['company_registration_date']['yyyy'])) {
        $required['lead'][] = 'company_registration_date';
      }
      // If industry is 'Other', [industry_detail] is required.
      // if(!empty($lead['lead']['industry_id'])){
      //   $current_industry = (new Config)->getConfig('config_industries', array('industry_id'=>$lead['lead']['industry_id']));
      //   if(strtolower($current_industry[0]['child'])==='other'){
      //     $required['lead'][] = 'industry_detail';
      //   }
      // }
      // If company_registration_date is more than or equal to 1 year, [sales_annual] is required.
      // if(!empty($lead['lead']['company_registration_date'])){
      //   $diff = $this->dateDiff($lead['lead']['company_registration_date'], date('Y-m-d'));
      //   if($diff->y >= 1) $required['lead'][] = 'sales_annual';
      // }elseif((!empty($lead['lead']['company_registration_date']['yyyy']) OR !empty($lead['lead']['company_registration_date'])) AND $lead['lead']['company_registration_date']!=='0000-00-00'){
      //   $diff = $this->dateDiff($lead['lead']['company_registration_date'], date('Y-m-d'));
      //   if($diff->y >= 1) $required['lead'][] = 'sales_annual';
      // }

      // If home_owner is 'Other', [home_owner_detail] is required.
      // if((!empty($lead['lead_owner']['home_owner']) AND (string)$lead['lead_owner']['home_owner']==='5')
      //   || (!empty($lead['lead']['home_owner']) AND (string)$lead['lead']['home_owner']==='5')){
      //   $required['lead_owner'][] = 'home_owner_detail';
      // }
      // If owner_type is not 'director only' or 'applicant only', [lead_owners.equity] and [leads.total_owner] are required.
      // if(!empty($lead['lead_owner']['owner_type']) AND ((string)$lead['lead_owner']['owner_type']!=='2') AND ((string)$lead['lead_owner']['owner_type']!=='6')){
      //   $required['lead_owner'][] = 'equity';
      //   $required['lead'][] = 'total_owners';
      // }
      // At least one Mobile or Phone number is required.
      if(empty($lead['lead_owner']['mobile']) AND empty($lead['lead_owner']['phone'])){
        $required['lead_owner'][] = 'mobile';
      } elseif (!empty($lead['lead_owner']['mobile'])) {
        $required['lead_owner'][] = 'mobile';
      } else {
        $required['lead_owner'][] = 'phone';
      }

      //if Equipment finance
      /**
       * Most of this function should really be in the controller. Only fetching data should be here
       */
      if (!empty($lead['lead']['product_type_id']) AND $lead['lead']['product_type_id'] == 10) {
        //we need to validate ABN
        $required['lead'][] = 'abn';
        $required['lead'][] = 'equipment_id';
        $required['lead'][] = 'equipment_details';
        //we need the eqipment fields to be filled
        if(!empty($lead['lead']['equipment_id'])){
          switch ($lead['lead']['equipment_id']) {
            case 1: //Car van Ute
            case 3: //Truck
            default:
              $required['lead']['equipment_condition'] = 'equipment_condition';
          }
        }
      }
      $required['lead'][] = 'industry_id';
      foreach($required as $table=>$fields){
        foreach($fields as $f){
          if(empty($lead[$table][$f]) OR $lead[$table][$f]==='0000-00-00'){
            $missingFields[] = $f;
          }
        }
      }
      //every other field gets (100 - total below) evenly
      $points = array(
        'statements_uploaded' => 22,
        'abn' => 15,
      );

      $currentPercentage = $this->_calculatePercentage($lead, $required, $missingFields, $points);
      return array('current_percentage' => $currentPercentage, 'missing_fields'=>$missingFields );
    }

    protected function _calculatePercentage($lead = array(), $required = array(), $missingFields = array(), $points = array())
    {
        $reservedPoints = array();
      $numberOfRequiredFields = 0;
      foreach ($required as $table => $fields) {
        $numberOfRequiredFields += count($fields);

        //check if any of $points must be required, removed the ones from $points if it is not in $required
        foreach ($points as $field => $score) {
          if (in_array($field, $fields))
            $reservedPoints[$field] = $score;
        }
      }

      $percentage = 100;
      //keep decimal during process
      $eachMarkForOtherField = round((100 - array_sum($reservedPoints) ) / ($numberOfRequiredFields - count($reservedPoints)), 2);

      foreach ($missingFields as $field) {
        if (array_key_exists($field, $reservedPoints)) {
          $percentage -= $reservedPoints[$field];
          // $processed ++;
        } else {
          $percentage -= $eachMarkForOtherField;
          // $processed ++;
        }
      }
      //round off
      $percentage = $percentage < 0 ? 0 : round($percentage, 0);
      // Update leads.percentage
      if(!empty($lead['lead']['lead_id'])){
        $this->DB->execute("SET @disable_trigger=true;");
        $this->DB->execute("UPDATE leads SET percentage=? WHERE lead_id=?", [$percentage, $lead['lead']['lead_id']]);
        $this->DB->execute("SET @disable_trigger=false;");
      }
      return $percentage;
    }

    /* get sent leads against given partner ID and months */
    public function sentLeadsPastXMonths($partnerID, $months, $partnerUserIdPassedIn = null, $includingArchived = false)
    {
      $date = date('Y-m-01', strtotime('now - ' . $months . ' months'));
      $today = date('Y-m-d');
      $subWhere = "";
      if (!empty($partnerUserIdPassedIn)) {
        $subWhere = " and lead_id in (select lead_id from partner_user_leads where partner_user_id = '$partnerUserIdPassedIn' and status='ACCESS') ";
      }
      $sql = "SELECT *
              FROM leads
              WHERE partner_id = ?
              AND status_id = 17
              $subWhere
              AND created >= ?
              AND created <= ?";
      if (empty($includingArchived)) {
        $sql .= " AND is_archived = '0'";
      }
      $leads = $this->DB->execute($sql, [$partnerID, $date, $today])
                        ->fetchAll('assoc');
      //init variable to return
      $byMonth = array();
      $m = 0;
      // Prepare the array
      do {
        // $mon = date('M', strtotime('now - ' . $m . ' months'));
        $mon = date('M', strtotime(date('M') . ' 1 - ' . $m . ' months')); // Using 1st of month, because 29th-31st March this produces a bug with February
        $byMonth[$mon] = array();
        $m++;
      } while ($m < (int)$months);

      // Populate $byMonth
      foreach ($leads as $lead) {
        $key = date('M', strtotime($lead['created']));
        if (!array_key_exists($key, $byMonth)) continue;
        $byMonth[ $key][] = $lead;
      }
      $byMonth = array_reverse($byMonth);

      return array($byMonth, empty($leads));
    }

    public function createAutoLoginCode($lead_id, $additionalCode = null) {
      $internalAuth = new LendInternalAuth;
			// Add expiry date
			$expiry_date = strtotime('+ 14 days');
			$login = $internalAuth->hashLeadId($lead_id).'l3nd'.$expiry_date;

			$build_sig = Security::getSalt().'#'.$login; // Build the signature we are expecting on the receiving end
      $build_sig = hash('sha1', $build_sig); // Build the signature we are expecting on the receiving end
			$code = $login.'#'.$build_sig; // Join it via a hash
      if (!empty($additionalCode)) {
        parse_str($additionalCode, $parseCode);
        $applicant = @$parseCode['applicant'];
        $first_name = @$parseCode['first_name'];
        $last_name = @$parseCode['last_name'];
        if (!empty($applicant) && (empty($first_name) || empty($last_name))) {
          $owner_id = $internalAuth->unhashLeadId($applicant);
          $owner = TableRegistry::getTableLocator()->get('LeadOwnersEntity')->get($owner_id)->toArray();
          $parseCode['first_name'] = $owner['first_name'];
          $parseCode['last_name'] = $owner['last_name'];
          $additionalCode = http_build_query($parseCode);
        }
        $code .= '#'.$additionalCode;
      }
			$code = base64_encode( $code ); // Base64 Encode it
			$code = "?aid=".$code; // Send it back
			return $code;
		}

    // TODO: More work to be done on this to handle other filters
    public function sentLeadsUsingFilters($partnerID, $filters, $partnerUserIdPassedIn = null, $showArchived = false) {
      $search = (!empty($filters['search']) AND is_array($filters['search'])) ? true : false;

      if($showArchived !== false) {
        if($showArchived === 'countArchived') {
          $archiveWhere = ' AND l.is_archived = 1 '; //we want the strict count of archived items
        }else{
          $archiveWhere = ' AND l.is_archived <=  ' . (int)$showArchived . ' ';
        }
      }else{
        $archiveWhere = '';
      }


      $sql   = 'SELECT l.lead_id, l.lead_ref, l.partner_id, l.created, l.statements_uploaded, l.last_changed_date, l.product_type_id,
                        l.partner_status_id, l.abn, l.organisation_name, l.business_name, l.amount_requested, l.purpose_id, l.loan_term_requested, l.trading_since,
                        l.industry_id, l.industry_detail, l.sales_monthly, l.sales_annual, l.b_address, l.bs_doc_id, l.bs_doc_retrieved, l.is_abn_unknown, l.total_owners, l.company_registration_date, l.b_state, l.is_archived,
                        lo.first_name, lo.last_name, lo.mobile, lo.phone, lo.email, lo.dob, lo.driving_licence_num, lo.home_owner, lo.home_owner_detail, lo.owner_type, lo.address, lo.equity, ifnull(lu.application_type, "New") as application_type,
                        le.lender_id, le.lender_name, le.shorthand, le.lender_logo, lsp.prevention_id, lsp.reason,
                        abn.entity_type_desc, abn.state as abn_state,
                        CASE WHEN lu.lender_modified_time IS NOT null AND lu.lender_modified_time != "" THEN lu.lender_modified_time ELSE lu.created END as last_lender_status_created,
                        lu.combined_status_string as last_lender_status,
                        ln.created as last_note_created,
                        pa.partner_alias_id, pa.company_name as alias_company_name,
                        sc.lend_score,
                        sc.serviceability,
                        ppt.product_type_name as product_type_name,
                        sub_ppt.product_type_name as sub_product_type_name ';
      if($search){
        $conditions = array();
        $sql .= ', (';
        foreach($filters['search'] as $field=>$value){
          $value = str_replace("'", "\'", $value);
          if ($field === 'l.lead_id')
          {
            $conditions[] = "IF (".$field." REGEXP '^".str_replace(' ', '|', trim($value))."$', 1, 0)";
          } elseif($field === 'l.organisation_name'){
            $keywords = explode(' ', $value);
            foreach($keywords as $keyword){
              $conditions[] = "IF (".$field." REGEXP '".$keyword."', 1, 0)";
            }
          }else{
            $conditions[] = "IF (".$field." REGEXP '".str_replace(' ', '|', trim($value))."', 1, 0)";
          }
        }
        $sql .= implode(' + ', $conditions);
        $sql .= ") as match_point ";
      }
      $nowDatetime = (new \DateTime)->format('Y-m-d H:i:s');  //current date time

      $sql .= ' FROM leads l LEFT JOIN lead_owners lo ON ( lo.owner_id = (SELECT owner_id FROM lead_owners WHERE lead_id = l.lead_id AND point_of_contact = 1 limit 1))
                LEFT JOIN (
                          SELECT max(lender_update_id) as lender_update_id, lead_id
                          FROM lender_lead_updates
                          GROUP BY lead_id
                      ) llu
                      ON llu.lead_id = l.lead_id
                LEFT JOIN lender_lead_updates lu
                ON (lu.lender_update_id = llu.lender_update_id and lu.lead_id = llu.lead_id )

                LEFT JOIN (
                          SELECT max(note_id) as note_id, lead_id
                          FROM lead_notes
                          GROUP BY lead_id
                      ) lln
                      ON lln.lead_id = l.lead_id
                LEFT JOIN lead_notes ln
                ON (ln.note_id = lln.note_id and ln.lead_id = lln.lead_id )

                LEFT JOIN (
                      SELECT max(concat(scheduled_time, callback_id)) as scheduled_time_id, lead_id
                          FROM partner_callbacks
                          WHERE scheduled_time <= "'.$nowDatetime.'"
                          AND status = "0"
                          GROUP BY lead_id
                      ) pac
                      ON pac.lead_id = l.lead_id
                LEFT JOIN partner_callbacks pc1
                ON (pc1.scheduled_time = substring(pac.scheduled_time_id, 1, 19) and pc1.callback_id = substring(pac.scheduled_time_id, 20) and pc1.lead_id = pac.lead_id )
                LEFT JOIN (
                      SELECT min(concat(scheduled_time, callback_id)) as scheduled_time_id, lead_id
                          FROM partner_callbacks
                          WHERE scheduled_time > "'.$nowDatetime.'"
                          AND status = "0"
                          GROUP BY lead_id
                      ) pacc
                      ON pacc.lead_id = l.lead_id
                LEFT JOIN partner_callbacks pc2
                ON (pc2.scheduled_time = substring(pacc.scheduled_time_id, 1, 19) and pc2.callback_id = substring(pacc.scheduled_time_id, 20) and pc2.lead_id = pacc.lead_id )

                LEFT JOIN (
                      SELECT max(sale_id) as recent_sale_id, lead_id
                      FROM sales
                      GROUP BY lead_id
                    ) sa
                    ON sa.lead_id = l.lead_id
                LEFT JOIN sales sal
                ON (sal.sale_id = sa.recent_sale_id)
                LEFT JOIN lender_product lp
                ON (lp.lender_product_id = sal.product_id )
                LEFT JOIN lenders le
                ON (le.lender_id = lp.lender_id)
                LEFT JOIN lead_send_prevention as lsp
                ON l.lead_id = lsp.lead_id
                LEFT JOIN partner_aliases as pa
                ON l.partner_alias_id = pa.partner_alias_id
                LEFT JOIN partner_product_types as ppt
                ON ppt.product_type_id = l.product_type_id
                LEFT JOIN partner_product_types as sub_ppt
                ON sub_ppt.product_type_id = ppt.sub_product
                LEFT JOIN (
                  SELECT * FROM lead_abn_lookup WHERE abn_id IN (
                    SELECT MAX(abn_id) FROM lead_abn_lookup GROUP BY lead_id
                  )
                ) as abn ON l.lead_id = abn.lead_id
                LEFT JOIN (
                  SELECT bs_analysis_id, avg_mto, lead_id FROM bank_statements_analysis
                  WHERE lead_id IS NOT null
                  AND bs_analysis_id IN (
                  	SELECT Max(bs_analysis_id)
                  	FROM bank_statements_analysis as bsbs
                  	WHERE bsbs.lead_id is not null
                  	GROUP BY lead_id
                  )
                  UNION
                  SELECT sbsa.bs_analysis_id, sbsa.avg_mto, sbs.lead_id FROM bank_statements_analysis as sbsa
                  JOIN bank_statements as sbs ON sbsa.bank_statement_id = sbs.bank_statement_id
                  WHERE sbsa.bs_analysis_id IN (
                  	SELECT MAX(bs_analysis_id)
                  	FROM bank_statements_analysis as bsa
                  	JOIN bank_statements as bs ON bsa.bank_statement_id = bs.bank_statement_id
                  	WHERE bs.lead_id NOT IN (
                  		SELECT lead_id FROM bank_statements_analysis WHERE lead_id IS NOT null
                  	)
                  	GROUP BY bs.lead_id
                  )
                  ORDER BY lead_id
                ) as bsa ON l.lead_id = bsa.lead_id
                LEFT JOIN lend_score as sc ON l.lead_id = sc.lead_id
                WHERE 1 = 1 ' . $archiveWhere;

      if (!empty($partnerUserIdPassedIn)) {
        $sql .= "  AND l.lead_id in (SELECT lead_id FROM partner_user_leads WHERE partner_user_id = '$partnerUserIdPassedIn' AND status='ACCESS') ";
      }

      if (!empty($partnerID)) {
        $sql .= 'AND l.partner_id = ? ';
        $val[] = $partnerID;
        $cachedKey = 'sentLeadsUsingFilters-partner-' . $partnerID.'-';
      } else {
        $sql .= 'AND l.partner_id is not null ';
        $cachedKey = 'sentLeadsUsingFilters-partner-all-';
      }

      if (isset($filters['start_date'])) {
        $sql  .= 'AND l.created >= ? ';
        $val[] = $filters['start_date'].' 00:00:00';
        $cachedKey .=  'start-' . $filters['start_date'];
      }

      if (isset($filters['end_date'])) {
        $sql  .= 'AND l.created <= ? ';
        $val[] = $filters['end_date'].' 23:59:59';
        $cachedKey .=  'end-' . $filters['start_date'];
      }

      //grab all partner_status_id in group names other than 'Settled'
      if (isset($filters['status']) && strtolower($filters['status']) === 'leads')
      {
        $filters['partner_status_id'] = LendStatus::getPartnerStatuses(null, 'leadsGroup');
      }

      if (isset($filters['partner_status_id'])) {
        if (!is_array($filters['partner_status_id'])) {
          $filters['partner_status_id'] = array($filters['partner_status_id']);
        }
        $sql .= "AND l.partner_status_id in (".implode(',', $filters['partner_status_id']).") ";
        $cachedKey .=  'partner-status-ids-' . implode('-', $filters['partner_status_id']);
      }

      // filter for search string
      if($search){
        $conditions = array();
        foreach($filters['search'] as $field=>$value){
          if ($field === 'l.lead_id')
            $conditions[] = $field." REGEXP '^".str_replace(' ', '|', trim(str_replace("'", "\'", $value)))."$' ";
          else
            $conditions[] = $field." REGEXP '".str_replace(' ', '|', trim(str_replace("'", "\'", $value)))."' ";
        }
        $sql .= " AND ( ".implode(' OR ', $conditions)." )";
      }
      $sortedBySpecialColumnName = null;
      if($search){
        $sql = "SELECT tmp.* FROM ({$sql}) as tmp ORDER BY tmp.match_point DESC";
      } elseif (empty($filters['sort']) || $filters['sort'] != 'product_type') {
        //we will handle sorted by product_type later, not here
        //find out more in post-sort section
        //sortable column
        // array of field name => table alias
        $validColumns = array( 'first_name' => 'lo', 'lead_ref' => '', 'amount_requested' => 'l',
                              'status' => '', 'shorthand'=> 'le',
                              'created' => 'l', 'last_changed_date' => 'l',
                              'last_lender_status_created' => '',
                              'last_note_created' => '',
                              'callback_overdue_next' => '',
                              'alias_company_name' => '',
                              'lend_score' => '',
                            );
        //set default sort column & direction && do validation here
        if (empty($filters['sort']) || !in_array($filters['sort'], array_keys($validColumns))) {
          $sortedBySpecialColumnName = @$filters['sort'];
          $filters['sort'] = 'created';
          $prefix = 'l';
          $suffix = '';
        } else {
          //find the table alias as the prefix
          $prefix = $validColumns[$filters['sort']];
          $suffix = ', l.created desc';
        }
        if (empty($filters['direction']) || !in_array($filters['direction'], array('asc', 'desc'))) {
          $filters['direction'] = 'desc';
        } else if ($filters['sort'] == 'lead_ref' || $filters['sort'] == 'shorthand' || $filters['sort'] == 'amount_requested' || $filters['sort'] == 'first_name') {
          $filters['direction'] = $filters['direction'] == 'asc' ? 'desc' : 'asc';
        }

        //add sort column & direction to query
        if (!empty($filters['sort']) && $filters['sort'] != 'lead_ref' && $filters['sort'] != 'status') { // last three columns with prefix = ''
          $sql .= 'ORDER BY '.(!empty($prefix) ? $prefix.'.' : '').$filters['sort'].' '.$filters['direction']. $suffix;
          $cachedKey .= '-sort-'.$filters['sort'].'-'.$filters['direction'];
        }
      }
      if (!empty($filters['returnTotal'])) {
        $returnTotal = !empty($val) ? $this->DB->execute($sql, $val)->fetchAll('assoc')
                        : $this->DB->execute($sql)->fetchAll('assoc');
      }
      if (isset($filters['limit'])) {
        $sql .= ' LIMIT '.$filters['limit'];
      }

      if (isset($filters['offset'])) {
        $sql .= ' OFFSET '.$filters['offset'];
      }

      if (false === ($leads = Cache::read($cachedKey, 'short'))) {
        $leads        = !empty($val) ? $this->DB->execute($sql, $val)->fetchAll('assoc')
                        : $this->DB->execute($sql)->fetchAll('assoc');
        // Cache::write($cachedKey, $leads, 'short');
      }

      //do something special so that the leads excluded from partner_commissions table during same period
      // $excludedLeads = $this->settledLeadsByPartner($partnerID, $filters, 'list');
      // $excludedLeadIds = array();
      // foreach ($excludedLeads as $r) {
      //   $excludedLeadIds[] = $r['lead_id'];
      // }
      // if ($excludedLeadIds) {
      //   foreach ($leads as $key => $lead) {
      //     if (in_array($lead['lead_id'], $excludedLeadIds))
      //       unset($leads[$key]);
      //   }
      // }

      $internalAuth = new LendInternalAuth;

      //read 18 statuses from lend config
      $partnerStatusIds = LendStatus::getPartnerStatuses(null, 'original');
      $leadsStatusIds = LendStatus::getPartnerStatuses('Leads', 'groupName');

      foreach ($leads as $key => $lead) {
//          Log::write('debug', 'mikesdebug ==> '.json_encode($lead), ['queriesLog']);
//          die();
//          print_r($lead);
//          die();
        $required = $this->checkPercentage(array('lead'=>$lead));
        $leads[$key]['percent_complete'] = $required['current_percentage'];
        $leads[$key]['hashed_lead_id'] = $internalAuth->hashLeadId($lead['lead_id']);

        //set product_type field
        $leads[$key]['product_type'] = !empty($lead['sub_product_type_name']) ? $lead['sub_product_type_name'] .' ('.$lead['product_type_name'].')'
                                      : (
                                        !empty($lead['product_type_name']) ? $lead['product_type_name']
                                        : '-'
                                      );

        //set the partner status name
        if (isset($lead['partner_status_id']) ) {
          $leads[$key]['partner_status_name'] = array_key_exists($lead['partner_status_id'], $partnerStatusIds)
                  ? $partnerStatusIds[$lead['partner_status_id']]['name']
                  : 'Undefined';
        }

        //something consistent with the logic in view /Element/dashboard_filter_others.ctp
        $leads[$key]['status'] = in_array($lead['partner_status_id'],$leadsStatusIds)
                  ? $required['current_percentage']
                  : $leads[$key]['partner_status_name'];

        //get who added the lead
        $history = TableRegistry::get('PartnerLeadHistory')->getPartnerLeadHistoryWithDetails(array('lead_id'=>$lead['lead_id']), array('history_id'=>'asc'));
        foreach ($history as $h) {
          if($h['history_detail'] == 'Lead Added'){
            $added_by = $h['partner_user_name'];
            break;
          }
        }
        $leads[$key]['added_by'] = !empty($added_by) ? $added_by : null;
      }
      // sorting
      unset($partnerStatusIds, $leadsStatusIds, $key, $lead);
      if ($sortedBySpecialColumnName !== null) {
        $filters['sort'] = $sortedBySpecialColumnName;
      }
      if (!empty($filters['sort'])) {
        if ($filters['sort'] == 'status') {
          if ($filters['direction'] == 'desc') {
            usort($leads, function ($a, $b) {
              if ($a['status'] == 'Pending' && $b['status'] == 'Pending') return $a['percent_complete'] > $b['percent_complete'];
              else return strcasecmp($a['status'], $b['status']);
            });
          } else {
            usort($leads, function ($a, $b) {
              if ($a['status'] == 'Pending' && $b['status'] == 'Pending') return $a['percent_complete'] < $b['percent_complete'];
              else return (-1) * strcasecmp($a['status'], $b['status']);
            });
          }
        } else if ($filters['sort'] == 'lead_ref') {
          if ($filters['direction'] == 'asc') {
            usort($leads, function ($a, $b) { return strcasecmp($a['hashed_lead_id'], $b['hashed_lead_id']); });
          } else {
            usort($leads, function ($a, $b) { return (-1) * strcasecmp($a['hashed_lead_id'], $b['hashed_lead_id']); });
          }
        } else if ($filters['sort'] == 'added_by') {
          if ($filters['direction'] == 'asc') {
            usort($leads, function ($a, $b) { return strcasecmp($a['added_by'], $b['added_by']); });
          } else {
            usort($leads, function ($a, $b) { return (-1) * strcasecmp($a['added_by'], $b['added_by']); });
          }
        }
      }

      //post-sort section -  field: product_type
      if (!empty($filters['sort']) && $filters['sort'] == 'product_type') {
        if ($filters['direction'] == 'asc') {
          usort($leads, function($a, $b) {return strcmp($a['product_type'], $b['product_type']);});
        } else {
          usort($leads, function($a, $b) {return (-1) * strcmp($a['product_type'], $b['product_type']);});
        }
      }

      // highlight search
      if (!empty($filters['search']) && !empty($filters['highlight'])) {
        foreach ($filters['search'] as $field => $v) {
          $filters['search'][$field] = $filters['highlight'];
          //highlight hashed one as well if lead_id is filtered by.
          if (strpos($field, 'lead_id') !== false)
            $filters['search']['hashed_lead_id'] = $filters['highlight'];
        }

        $leads = $this->highlight($filters['search'], $leads);
      }
      if (!empty($filters['returnTotal']) && isset($returnTotal)) {
        return array($leads, count($returnTotal));
      } else
        return $leads;
    }

    protected function addExtraFieldsToSelect(&$select_fields, $extraQueryOptions)
    {
        if (empty($extraQueryOptions)) {
            return;
        }
        foreach ($extraQueryOptions as $option) {
            switch ($option) {
                case 'csv_export':
                    $select_fields['referrer_nickname'] = 'ref.nickname';
                    $select_fields['cqcq_completed_date'] = 'lsr.signed_time';
            }
        }
    }

    protected function addExtraJoins(&$joinString, $extraQueryOptions)
    {
        if (empty($extraQueryOptions)) {
            return;
        }
        foreach ($extraQueryOptions as $option) {
            switch ($option) {
                case 'csv_export':
                    $joinString .= 'LEFT JOIN referrers ref ON ref.id = refp.referrer_id';
                    $joinString .= '
                    LEFT JOIN
    (
        SELECT
            lsr1.id,
            lsr1.lead_id,
            lsr1.signed_time
        FROM
            lend_signature_requests lsr1
        INNER JOIN
            (
                SELECT
                    lead_id,
                    MAX(signed_time) AS latest_signed_time
                FROM
                    lend_signature_requests
                GROUP BY
                    lead_id
            ) lsr2
            ON lsr1.lead_id = lsr2.lead_id
            AND lsr1.signed_time = lsr2.latest_signed_time
    ) lsr
    ON le.lead_id = lsr.lead_id;
                    ';
            }
        }
    }


    protected function applyExtraQueryOptions($extraQueryOptions)
    {
        if (empty($extraQueryOptions)) {
            return;
        }
        foreach ($extraQueryOptions as $option) {
            switch ($option) {
                case 'csv_export':
                    $this->applyCsvExportExtraQueryOption();
            }
        }
    }

    public function readRecordWithPag(
      $params = false, 
      $order = null, 
      $limit = false, 
      $assoc = true, 
      $filterJason = false, 
      $queryStringManage = null, 
      $onlyCount = null, 
      $isIntermediary = false, 
      $lastOriginalLeadUpdateDays = ['status' => 0, 'note' => 0, 'selected'=>[]],
      $partnerId = null,
      $extraQueryOptions = null
      )
    {


      $intermediary_stuck_lead_page = isset($lastOriginalLeadUpdateDays['selected']) && $isIntermediary;
 
        $includeArchived=false;
        $contentType='all';
        $listPartnerStatus=null;
        $listLender=null;
        $postFilters = [];
        $postFilterMode = false;
        if($filterJason!=false){
          if(isset($filterJason['filters']) && $filterJason['filters']){//post filters have been sent
          $postFilterMode = true;
            $fieldMap['product_type_id'] = 'le.product_type_id';
            $fieldMap['product_type_name'] = 'ppt.product_type_name';//tested
            $fieldMap['man_status_id'] = 'le.man_status_id';//tested
            $fieldMap['man_status_name'] = 'mt.status_name'; //tested
            $fieldMap['status_id'] = 'le.lender_status_id';
            $fieldMap['lead_type'] = 'le.lead_type';//tested
            $fieldMap['lender_name'] = 'lad.lender_name';//tested
            $fieldMap['referrer'] = 'refp.id'; //tested
            $fieldMap['assignee'] = 'pul.partner_user_name';//tested
            $fieldMap['assignee_id'] = 'pul.partner_user_id';//tested
            $fieldMap['assignee_date'] = 'pul.partner_user_granted';
            $fieldMap['partner_status'] = 'le.partner_status_id';//tested
            $fieldMap['tag_id'] = 'le.tag_id';//tested
            foreach ($filterJason['filters'] as $filter => $data) {
              if (isset($fieldMap[$filter]))
                $postFilters[$fieldMap[$filter]] = $data;
              if(($filter === 'filter_by_date') && !in_array($data, ['created', 'updated']))
                unset ($filterJason['filters'][$filter]);   
            }
            if($filterJason['filters']['is_closed'] != -1){
              $postFilters['le.is_closed'] = (($filterJason['filters']['is_closed']) && (intval($filterJason['filters']['is_closed']) === 1));
            }
          } else {
            $includeArchived=$filterJason['show_archive'];
            $contentType=$filterJason['content_type'];
            $listPartnerStatus=((!empty($filterJason['list_status']) && strlen($filterJason['list_status'])>0)?explode(',',$filterJason['list_status']):null);
            $listLender=((!empty($filterJason['list_lender']) && strlen($filterJason['list_lender'])>0)?explode(',',$filterJason['list_lender']):null);
          }
        }
        $statusList=null;
        if(is_array($params) && isset($params['status_id_list'])){
            $statusList=implode(',',$params['status_id_list']);
            unset($params['status_id_list']);

        }
        $startDate=null;
        $endDate=null;
        if(is_array($params) && isset($params['start_date']) && isset($params['end_date'])){
            $startDate=$params['start_date'];
            $endDate=$params['end_date'];
            unset($params['start_date']);
            unset($params['end_date']);
        }

        $fields=array(); $values=array();
        $searchValue=$params['search_value'];
        unset($params['search_value']);


            foreach($params as $field=>$value){
                $arr_field = explode(' ', trim($field));
                if(count($arr_field)>1) $fields[] = $field.' ?';
                else $fields[] = $field.'=?';

                $values[] = $value;
            }
            $select_fields = [
              'details' => "CONCAT(lo.first_name,' ',lo.last_name)",
              'created' => 'le.created',
              'lead_ref' => 'le.lead_ref',
              'amount_requested' => 'le.amount_requested',
              'funded_amount' => 'pcc.funded_amount',
              'client_declared_sales_monthly' => 'le.client_declared_sales_monthly',
              'sales_monthly' => 'le.sales_monthly',
              'campaign' => 'le.campaign',
              'b_state' => 'le.b_state',
              'statements_uploaded' => 'le.statements_uploaded',
              'product_type_name' => 'ppt.product_type_name',
              'lead_type' => 'le.lead_type',
              'partner_user_name' => "COALESCE(partner_user_name,'-')",
              'partner_user_granted' => "COALESCE(partner_user_granted,'-')",
              'lender_logo' => 'len.lender_logo',
              'status' => 'st.status_name',
              'lender_status' => 'llu.combined_status_string',
              'lender_modified_time' => 'llu.lender_modified_time',
              'last_note_created' => 'ln.created',
              'last_note' => 'ln.notes',
              'last_intermediary_note_created' => 'ln2.created',
              'last_intermediary_note' => 'ln2.notes',
              'next_task' => 'duet.scheduled_time',
              'lend_score' => 'sc.lend_score',
              'lead_owner' => "CONCAT(lo.first_name,' ',lo.last_name)",
              'lead_owner_dob' => 'lo.dob',
              'lead_owner_address' => "CONCAT(loa.address,' ',loa.suburb,' ',loa.state,' ',loa.postcode)",
              'lead_status' => 'st.status_name',
              'lead_man_status' => 'mt.status_name',
              'organisation_name' => 'le.organisation_name',
              'purpose' => 'pr.purpose',
              'purpose_other' => 'le.purpose_other',
              'is_archived' => 'le.is_archived',
              'status_id' => 'le.status_id',
              'man_status_id' => 'le.man_status_id',
              'lender_name' => 'lad.lender_name',
              'lender_id' => 'len.lender_id',
              'shorthand' => 'len.shorthand',
              'intermediary_lender_name' => 'il.name',
              'intermediary_lender_logo' => 'il.logo',
              'completion_percent' => 'le.percentage',
              'funded_type' => 'pcc.funded_type',
              'funded_date' => 'pcc.funded_date',
              'lead_id' => 'le.lead_id',
              'partner_status_id' => 'le.partner_status_id',
              'added_by' => 'pu.name',
              'group_name' => 'st.group_name',
              'man_group_name' => 'msg.group_name',
              'consumer_guide_status' => 'cps.con_cgqp_status',
              'is_tick_and_flick' => 'le.is_tick_and_flick',
              'last_changed_date' => 'le.last_changed_date',
              'is_closed' => 'le.is_closed',
              'owner_consent' => "(SELECT CASE WHEN COUNT(*) = COUNT(lo.consent) THEN 1 ELSE 0 END
                  FROM lead_owners lo
                    WHERE lo.lead_id = le.lead_id AND lo.status = 'Active')",
              'ga_client_id' => 'lgd.ga_client_id',
              'gclid' => 'lgd.gclid',
              'utm_source' => 'lgd.utm_source',
              'utm_medium' => 'lgd.utm_medium',
              'utm_campaign' => 'lgd.utm_campaign',
              'utm_term' => 'lgd.utm_term',
              'utm_content' => 'lgd.utm_content',
            ];
            $select_fields_string = [];

            $this->addExtraFieldsToSelect($select_fields, $extraQueryOptions);

            foreach ($select_fields as $alias => $field) {
              $select_fields_string[] = "{$field} as {$alias}";
            }

            $selectQuery = "SELECT ".implode(', ', $select_fields_string);

            if ($isIntermediary) {
                $selectQuery = "SELECT DISTINCT ".implode(', ', $select_fields_string);
                $selectQuery .= ",CASE WHEN pu2.partner_user_id IS NOT null THEN pu2.name ELSE pu3.name END as referrer_name, 
                                  CASE WHEN pu2.partner_user_id IS NOT null THEN pu2.email ELSE pu3.email END as referrer_email,
                                  CASE WHEN pu2.partner_user_id IS NOT null THEN pu2.mobile ELSE pu3.mobile END as referrer_mobile,
                                  llu2.created as original_lead_status_update_date,
                                  st2.status_name as original_lead_status_name,
                                  st2.lend_status_id as original_lead_status_id,
                                  st2.group_name as original_lead_status_group_name,
                                  ln3.notes as original_lead_last_note,
                                  ln3.created as original_lead_note_update_date,
                                  ol_llu.combined_status_string as referrer_status_name";
                $select_fields = array_merge($select_fields, [
                  'referrer_status_name' => 'ol_llu.combined_status_string',
                  'original_lead_status_update_date' => 'llu2.created',
                  'original_lead_note_update_date' => 'ln3.created',
                  'referrer_name' => 'referrer_name',
                ]);
            }
            // Add sale_details:
            $selectQuery .= ",CASE WHEN le.referer IS NOT null THEN le.referer ELSE CONCAT(refp.first_name, refp.last_name) END as referrer,
                              le.business_name,
                              le.client_declared_sales_monthly,
                              refp.contact_number as referrer_contact_number,
                              laf.supplier,
                              laf.supplier_contact_name,
                              laf.contract_type,
                              laf.make as asset_make,
                              laf.model as asset_model,
                              laf.year as asset_year,
                              laf.asset_age_months as asset_age_months,
                              laf.sale_type as asset_sale_type,
                              laf.valuation as asset_valuation,
                              laf.manual_valuation as asset_manual_valuation,
                              laf.condition as asset_condition,
                              laf.asset_purchase_price as asset_purchase_price,
                              laf.asset_deposit as asset_deposit,
                              laf.ltv as asset_ltv,
                              CASE WHEN laf.contract_type IS null THEN 'basic' ELSE 'e2e' END as is_asset_e2e,
                              sal.created as submission_date,
                              sd.lender_name as selected_lender_name,
                              sd.tier_name as selected_tier_name,
                              sd.product_name as selected_product_name,
                              sd.establishment_fees as selected_establishment_fees,
                              sd.monthly_fees as selected_monthly_fees,
                              sd.include_fees_requested as selected_include_fees_requested,
                              sd.financed_amount as selected_financed_amount,
                              sd.commission as selected_commission,
                              sd.commission_type as selected_commission_type,
                              sd.origination_fee as selected_origination_fee,
                              sd.term_months as selected_term_months,
                              sd.apr as selected_apr,
                              sd.base_rate as selected_base_rate,
                              sd.customer_rate as selected_customer_rate,
                              sd.repayment_amt as selected_repayment_amt,
                              sd.repayment_freq as selected_repayment_freq,
                              setr.settlement_date as settled_settlement_date,
                              setr.lender_name as settled_lender_name,
                              setr.settled_product_name as settled_product_name,
                              setr.lender_ref as settled_lender_ref,
                              setr.loan_amount as settled_loan_amount,
                              setr.brokerage_fee as settled_brokerage_fee,
                              setr.brokerage_fee_value_type as settled_brokerage_fee_value_type,
                              setr.base_rate as settled_base_rate,
                              setr.customer_rate as settled_customer_rate,
                              setr.total_estab_fees as settled_total_estab_fees,
                              setr.application_fee_included as settled_application_fee_included,
                              setr.origination_fee as settled_origination_fee,
                              setr.referrer_commission as settled_referrer_commission,
                              setr.mirrors_submission as settled_mirrors_submission,
                              setr.tier_name as settled_tier_name,
                              setr.apr as settled_apr,
                              setr.monthly_fees as settled_monthly_fees,
                              setr.repayment_amt as settled_repayment_amt,
                              setr.repayment_freq as settled_repayment_freq,
                              lo.mobile, 
                              lo.email
                              ";

        if($onlyCount){
            $selectQuery=' SELECT count(distinct le.lead_id) as count ';
        }

        $fromQuery = " FROM leads le ";

        $whereQuery =" WHERE ".implode(' AND ', $fields);
        //-- LEFT JOIN partner_aliases as pa ON le.partner_alias_id = pa.partner_alias_id

//        if($queryStringManage!=null and strtolower($queryStringManage)=='archived'){
//            $whereQuery.=' AND le.is_archived=1   ' ;
//        }else{
        $whereQuery.=($includeArchived?' ':' AND le.is_archived=0 ') ;
//        }


        $whereQuery.=(strtolower($contentType)!='all'?' AND pc.commission_id = le.latest_commission_id AND pc.funded_type=\''.$contentType.'\' ':'') ;

        if ($postFilterMode === true) {
          if (($startDate != null && $endDate != null) && $filterJason['filters']['filter_by_date']) {
            $map = ['created'=>'created', 'updated'=>'last_changed_date'];
            $fromDate = $startDate.' 00:00:00';
            $toDate = $endDate.' 23:59:59';
            unset($startDate);
            unset($endDate);
            $status = '';
            if(isset($postFilters['le.partner_status_id'])){
              $stat = $postFilters['le.partner_status_id'];
              $status = ' AND (le.partner_status_id'.((is_array($stat) && (count($stat) > 0))?' IN (\''.implode('\',\'',$stat).'\')':' = \''.$stat.'\'').')';
              unset($postFilters['le.partner_status_id']);
            }
            if (isset($filterJason['filters']['settled_date']) && ($filterJason['filters']['settled_date'] == 0)) {//'based on selection'/settled = false
              $whereQuery .= ' AND le.' . $map[$filterJason['filters']['filter_by_date']] . ' >= \'' . $fromDate . '\' AND le.' . $map[$filterJason['filters']['filter_by_date']] . ' <= \'' . $toDate . '\'';//created/updated in date range
            }
            else{//settled date/default
              $whereQuery .= ' AND ((pcc.lead_id IS NOT null AND pcc.funded_date BETWEEN \'' . $fromDate . '\' AND \'' . $toDate . '\')';//settled in date range
              $whereQuery .= ' OR (pcc.lead_id IS null AND le.'.$map[$filterJason['filters']['filter_by_date']]. ' BETWEEN \'' . $fromDate . '\' AND \'' . $toDate . '\''.$status.'))';//not settled - created/updated in date range
            }
          }
        } else {
          if(strtolower($queryStringManage)=='settled'){
              //for settled
              $whereQuery.=' AND pcc.lead_id IS NOT null ';
              $whereQuery.=($startDate!=null && $endDate!=null?' AND pcc.funded_date >= \''.$startDate.'\' AND pcc.funded_date <= \''.$endDate.'\' ':'');

          }else{//non settled page leads,pending,attempting,inprogress,rejected
              $whereQuery.=($statusList!=null?' AND le.partner_status_id in '.("({$statusList})").' ':'') ;
              $whereQuery.=($startDate!=null && $endDate!=null?' AND le.created >= \''.$startDate.'\' AND le.created <= \''.$endDate.'\' ':'');
          }
        }
        
// if there is a searchValue in ui passed to server

       if(strlen($searchValue) >0){

           $whereQuery.="  AND (lower(le.lead_ref) like ? or lower(le.organisation_name) like ?
           or lower(le.purpose_other) like ? or lower(le.amount_requested) like ?
           or lower(ppt.product_type_name) like ? or lower(len.lender_name) like ?
           or lower(sc.lend_score) like ? or lower(le.percentage) like ?
           or lower(pu.name) like ? or lower(llu.combined_status_string) like ?
           or lower(pcc.funded_amount) like ? or lower(pcc.funded_type) like ?
           or lower(lo.lead_owner) like ?
                  ) ";
           for($cc=1;$cc<=13;$cc++){//12 items to check with searchval
               $values[]='%'.strtolower($searchValue).'%';
           }
       }

       if($listLender!=null){
           $tmp = [];
           foreach($listLender as $kl=>$vl){
               if(strlen(trim($vl))>1) {
                   $tmp[] = "lower(lad.lender_name) like ?";
                   $values[] = '%' . strtolower($vl) . '%';
               }
           }
           $whereQuery .= " AND (".implode(' OR ', $tmp).")";
       }


       // stuck lead
      if ( $intermediary_stuck_lead_page ) {
        $statusWhereQuery = "";
        if ($lastOriginalLeadUpdateDays['status'] > 0 && in_array('status', $lastOriginalLeadUpdateDays['selected'])) {
          $statusWhereQuery = " (st2.lend_status_id NOT IN (4, 25) AND llu2.created < DATE_SUB(NOW(), INTERVAL " . $lastOriginalLeadUpdateDays['status'] . " day))";
        }
        $noteWhereQuery = "";
        if ($lastOriginalLeadUpdateDays['note'] > 0 && in_array('note', $lastOriginalLeadUpdateDays['selected'])) {
          $noteWhereQuery = " (ln3.created < DATE_SUB(NOW(), INTERVAL " . $lastOriginalLeadUpdateDays['note'] . " day) OR ollad.max_intermediary_note_id IS NULL)";
        }
        $whereQuery .= " AND st.group_name NOT IN ('Settled', 'Rejected')";
        $whereQuery .= " AND st2.group_name NOT IN ('Settled', 'Rejected')";
        $whereQuery .= " AND le.is_closed = 0";
        if(count($lastOriginalLeadUpdateDays['selected']) == 1){
          if ($statusWhereQuery != "") {
            $whereQuery .= " AND " . $statusWhereQuery;
          } elseif ($noteWhereQuery != "") {
            $whereQuery .= " AND " . $noteWhereQuery;
          }
        } elseif (count($lastOriginalLeadUpdateDays['selected']) == 2){
          if($lastOriginalLeadUpdateDays['combinator'] == "AND") {
            $whereQuery .= " AND (" . $statusWhereQuery . " AND " . $noteWhereQuery . ")";
          }elseif ($lastOriginalLeadUpdateDays['combinator'] == "OR") {
            $whereQuery .= " AND (" . $statusWhereQuery . " OR " . $noteWhereQuery . ")";
          }
        } 
      }
       if($listPartnerStatus!=null){
          $tmp = [];
          foreach($listPartnerStatus as $vps){
              if(empty($vps)) continue;
              $tmp[] = $vps;
          }
           $whereQuery .= " AND (le.partner_status_id in  (".implode(',', $tmp)."))";
       }

       //add post filters
      $postfIlterString = "";
      foreach($postFilters as $postFilter=>$val){
        if($postFilter === 'le.is_closed')
          $postfIlterString .= ($val === true)?" AND le.is_closed = 1":" AND (le.is_closed IS NULL OR le.is_closed = 0)";
        else if (in_array($postFilter, ['pul.partner_user_id', 'le.product_type_id'])) {
          if (is_array($val)) {
            $conditions = [];
            $key = array_search(0, $val);//0 indicates null - leads which are not assigned
            if ($key !== false) {
              $conditions[] = $postFilter." IS NULL";
              unset($val[$key]);
            }
            if (count($val) > 0) {
                $conditions[] = $postFilter . " IN ('".implode("','",$val)."')";
            }
            if(count($conditions)> 0)
              $postfIlterString .= " AND (" . implode(" OR ", $conditions).")";
          }
          else if(($val === 0) || ($val=== null))//0 indicates null - leads which are not assigned
            $postfIlterString .= " AND " . $postFilter . " IS NULL";
          else
            $postfIlterString .= " AND " . $postFilter . " = '" . $val . "'";
        }
        else{
          $postfIlterString .= " AND " . $postFilter;
          $postfIlterString .= (is_array($val) && (count($val) > 0))?" IN ('".implode("','",$val)."')":" = '".$val."'";
        }
      }
      $whereQuery .= $postfIlterString;
    // echo $postfIlterString;
    // die();

      //add call queue check
      $whereQuery .= " AND le.call_queue_status in (".implode(',', Configure::read('Lend.NOT_IN_CALL_QUEUE_STATUS')).") ";

       //if where query only search on le, then search le first and join other tables
       $whereQueryArray = explode("AND", str_replace("WHERE", "", $whereQuery));
       $isOnlyQueryLe = true;
       foreach($whereQueryArray as $wq){
        if(strtolower(substr(trim($wq), 0, 3)) != "le." && strtolower(substr(trim($wq), 0, 4)) != "(le."){
          $isOnlyQueryLe = false;
          break;
        }
       }

       $leFields = array("created", "last_changed_date", "lead_ref", "amount_requested", "sales_monthly", "client_declared_sales_monthly", "campaign", "b_state", "statements_uploaded", "lead_type", "organisation_name", "purpose_other ", "is_archived ", "status_id", "partner_status_id");
       $orderQuery = "";
       if($order){
            $order_by=array();
            // ## WARNING:: SQL injection issue if $field contains something like: `; insert into...`.
            foreach($order as $field=>$asc_desc){
              if (!in_array($field, array_keys($select_fields))) {
                continue;
              }
              if(strtolower(substr(trim($field), 0, 3)) != "le." && !in_array($field, $leFields)){
                $isOnlyQueryLe = false;
              }
              $order_by[] = $field.' '.(in_array(strtolower($asc_desc), ['asc', 'desc']) ? $asc_desc : 'desc');
            }

            if (empty($order_by)) {
              $order_by[] = 'created desc';
            }
            $orderQuery = ' ORDER BY '.(implode(', ', $order_by));
        }

        $limitQuery = "";
        if($limit){
            if(is_array($limit)){
              if (!is_numeric($limit['limit']) || !is_numeric($limit['offset'])) {
                $limitQuery = ' LIMIT 10';
              } else {
                $limitQuery = ' LIMIT '.$limit['limit'].' OFFSET '.$limit['offset'];
              }
            } elseif (is_numeric($limit)) {
              $limitQuery = ' LIMIT '.$limit;
            } else {
              $limitQuery = ' LIMIT 10';
            }
        }

        $inLeadsQuery = "";

        //$isOnlyQueryLe = false;
        if($isOnlyQueryLe == true){
          $onlyCountJoinQuery = $onlyCount?' INNER JOIN lead_associated_data lad on lad.lead_id = le.lead_id':'';
          $onlyLeQuery = "SELECT le.lead_id FROM leads le ".$onlyCountJoinQuery.$whereQuery.$orderQuery;
          $selectedLeads = $this->ReaderDB->execute($onlyLeQuery, $values)->fetchAll('assoc');
          if($onlyCount){
            return [['count' => count($selectedLeads)]];
          }
          if(!$selectedLeads){
            return [];
          }
          $inLeadsQuery = " lead_id in (".implode(",",array_column($selectedLeads, 'lead_id')).")";
          $fromQuery = " FROM (SELECT * FROM leads WHERE ".$inLeadsQuery.") le";
        }

        $joinQuery = " LEFT JOIN partner_product_types ppt on le.product_type_id=ppt.product_type_id";
          // -- ,pa.company_name as alias_company_name
        $joinQuery.=(strtolower($contentType)!='all'?" LEFT JOIN partner_commissions pc ON le.lead_id=pc.lead_id":"");
        $joinQuery .="
        INNER JOIN lead_associated_data lad on lad.lead_id = le.lead_id
        LEFT JOIN lead_ga_data lgd on lgd.lead_id = le.lead_id
        LEFT JOIN lender_lead_updates llu ON llu.lender_update_id = le.lender_status_id
        LEFT JOIN sales sal ON sal.sale_id = lad.max_sale_id
        LEFT JOIN sale_details as sd ON sal.sale_id = sd.sale_id
        LEFT JOIN lender_product lp ON (lp.lender_product_id = sal.product_id )
        LEFT JOIN lenders len ON (len.lender_id = lp.lender_id)
        LEFT JOIN intermediary_lenders il ON (il.id = len.intermediary_lender_id)
        LEFT JOIN lead_notes ln ON ln.note_id = lad.max_note_id
        LEFT JOIN lead_notes ln2 ON ln2.note_id = lad.max_intermediary_note_id
        LEFT JOIN lend_score as sc ON le.lead_id = sc.lead_id
        LEFT JOIN partner_callbacks as duet ON duet.callback_id = lad.min_callback_id
        LEFT JOIN partner_lead_history as lh ON lh.lead_id = le.lead_id AND lh.partner_id = le.partner_id AND lh.history_detail = 'Lead Added' AND lh.partner_user_id IS NOT null
        LEFT JOIN partner_users as pu ON lh.partner_user_id = pu.partner_user_id
        LEFT JOIN lend_statuses st on le.partner_status_id=st.lend_status_id
        LEFT JOIN man_statuses mt on le.man_status_id=mt.id
        LEFT JOIN man_status_groups msg on mt.man_status_group_id=msg.id
        LEFT JOIN con_page_status cps on le.lead_id=cps.lead_id
        LEFT JOIN lead_owners as lo ON le.lead_id = lo.lead_id AND lo.point_of_contact = 1
        LEFT JOIN lead_owner_addresses as loa ON loa.lead_owner_id = lo.owner_id
        LEFT JOIN referrer_people as refp on le.referrer_person_id = refp.id
        LEFT JOIN lead_asset_finance as laf on le.lead_id = laf.lead_id
        LEFT JOIN frm_purpose as pr on le.purpose_id = pr.purpose_id";
        $fundedDateWhereQuery = (isset($startDate) && $startDate!=null && isset($endDate) && $endDate!=null?' funded_date >= \''.$startDate.'\' AND funded_date <= \''.$endDate.'\' ':'');
        $fundedWhereQueriesArray = [];
        $fundedWhereQuery = "";
        if($fundedDateWhereQuery){
          $fundedWhereQueriesArray[] = $fundedDateWhereQuery;
        }
        if($inLeadsQuery){
          $fundedWhereQueriesArray[] = $inLeadsQuery;
        }
        if(!empty($fundedWhereQueriesArray)){
          $fundedWhereQuery = " WHERE ".implode(" AND ", $fundedWhereQueriesArray);
        }
        $joinQuery .=" LEFT JOIN (SELECT commission_id, lead_id,funded_amount,funded_type,funded_date FROM partner_commissions where commission_id in (SELECT max(commission_id) FROM partner_commissions ".$fundedWhereQuery." GROUP BY lead_id) ) pcc on pcc.lead_id=le.lead_id";
        $joinQuery .= " LEFT JOIN settlement_reviews as setr on pcc.commission_id = setr.commission_id ";
        $joinQuery .=" LEFT JOIN 
        (SELECT pu.partner_user_id, pu.name as partner_user_name, pul.lead_id, pul.granted as partner_user_granted FROM partner_user_leads pul
            LEFT JOIN partner_users pu ON pu.partner_user_id = pul.partner_user_id WHERE pul.status = 'ACCESS') pul ON pul.lead_id = le.lead_id
        ";
        if($isIntermediary) {
          $joinQuery .= " LEFT JOIN intermediary_lender_mapping ilm ON ilm.new_lead_id = le.lead_id";
          $joinQuery .= " LEFT JOIN leads as ol ON ol.lead_id = ilm.original_lead_id ";
          $joinQuery .= " LEFT JOIN lender_lead_updates as ol_llu ON ol_llu.lender_update_id = ol.lender_status_id 
                              AND ol_llu.product_id IN (SELECT lp.lender_product_id FROM lenders as ld JOIN lender_product as lp ON ld.lender_id = lp.lender_id WHERE ld.partner_id = $partnerId)";
          $joinQuery .= " LEFT JOIN lead_associated_data ollad on ollad.lead_id = ol.lead_id ";
          $joinQuery .= " LEFT JOIN partner_lead_history plh2 ON plh2.history_id = ollad.max_history_id ";
          $joinQuery .= " LEFT JOIN partner_users as pu2 ON plh2.partner_user_id = pu2.partner_user_id";
          $joinQuery .= " LEFT JOIN partner_users as pu3 ON ol.partner_id = pu3.partner_id AND pu3.account_admin = 1 ";
          $joinQuery .= " LEFT JOIN lender_lead_updates llu2 ON llu2.lender_update_id = ol.lender_status_id ";
          $joinQuery .= " LEFT JOIN lend_statuses st2 on ol.partner_status_id = st2.lend_status_id ";
          $joinQuery .= " LEFT JOIN lead_notes ln3 on ln3.note_id = ollad.max_intermediary_note_id";
        }

        $this->addExtraJoins($joinQuery, $extraQueryOptions);

        // Added to fix duplicates.
        $groupByQuery = " GROUP BY le.lead_id";

        if($isOnlyQueryLe == true){
          $query = $selectQuery.$fromQuery.$joinQuery.$groupByQuery.$orderQuery.$limitQuery;
          $values = [];
        }else if($onlyCount){
          $query = $selectQuery.$fromQuery.$joinQuery.$whereQuery;
          $countLeads = $this->ReaderDB->execute($query, $values)->fetch('assoc');
          return [$countLeads];
        }else{
          $query = $selectQuery.$fromQuery.$joinQuery.$whereQuery.($onlyCount?"":$groupByQuery).$orderQuery.$limitQuery;
        }
        $q=array('query'=>$query, 'values'=>$values);
        
        $result=$this->ReaderDB->execute($q['query'], $q['values'])->fetchAll('assoc');

        foreach ($result as $index => $record) {
            //sales monthly fallback
            if ((!$record['sales_monthly'] || $record['sales_monthly'] == 0) && $record['client_declared_sales_monthly'] > 0) {
              $result[$index]['sales_monthly'] = $record['client_declared_sales_monthly'];
            }
            $fieldsToFill = [
                'referrer_name',
                'referrer_email',
                'referrer_mobile',
                'referrer_status_name',
            ];

            foreach ($fieldsToFill as $field) {
                if (!isset($record[$field])) {
                    $result[$index][$field] = null;
                }
            }
        }

        if($assoc) {
            return $result;
        }else{//num
            foreach($result as $k=>$v){
                $result[$k]=array_values($v);
            }
            return $result;
        }
    }

    /**
     * New ORM based function to get leads data
     * To replace readRecordWithPag function
     */
    public function getLeads(
      $params = [], 
      $order = null, 
      $limit = false, 
      $assoc = true, //is always true in usage
      $filterJson = false, 
      $queryStringManage = null, 
      $onlyCount = null, 
      $isIntermediary = false, 
      $lastOriginalLeadUpdateDays = ['status' => 0, 'note' => 0, 'selected'=>[]],
      $partnerId = null,
      $extraQueryOptions = []
    )
    {
      $includeArchived = false;
      $isIntermediary = true;
      $this->leadFields = [];
      $this->fieldAliases = [];//used to map  field aliases (which is used for output, ordering and possibly filters) to actual table.field notation
      $this->otherFields = [];
      $this->joinTables = [];
      $orderBy = [];
      $this->isLeadOnly = true;
      $postFilters = [];
      $limitValue = null;
      $offsetValue = null;
      $startDate=null;
      $endDate=null;
      if(is_array($params) && isset($params['start_date']) && isset($params['end_date'])){
          $startDate=$params['start_date'];
          $endDate=$params['end_date'];
          unset($params['start_date']);
          unset($params['end_date']);
      }

      $statusList=null;
      if(is_array($params) && isset($params['status_id_list'])){
          // $statusList=implode(',',$params['status_id_list']);
          $statusList=$params['status_id_list'];
          unset($params['status_id_list']);
      }

      $searchValue=$params['search_value'];
      unset($params['search_value']);

      $whereConditions = [];//store where conditions that need to be applied at query level - add at end
      $whereValues = [];//value bindings -- need to loop and bind at the end]]]

      foreach($params as $field=>$value){
        $whereConditions[] = [$this->replaceAlias($field) => $value];
      }

      if($filterJson!=false){
       
        if(isset($filterJson['filters'])){
          $fieldMap = [
            'product_type_id' => 'le.product_type_id',
            'product_type_name' => 'ppt.product_type_name',
            'man_status_id' => 'le.man_status_id',
            'man_status_name' => 'mt.status_name',
            'status_id' => 'le.lender_status_id',
            'lead_type' => 'le.lead_type',
            'lender_name' => 'lad.lender_name',
            'referrer' => 'refp.id',
            'assignee' => 'pul.name',
            'assignee_id' => 'pul.partner_user_id',
            'partner_status' => 'le.partner_status_id',
            'tag_id' => 'le.tag_id',
            'owner_first_name' => 'lo.first_name',
          ];
          foreach ($filterJson['filters'] as $filter => $data) {
            if (isset($fieldMap[$filter]))
              $postFilters[$fieldMap[$filter]] = $data;
            if(($filter === 'filter_by_date') && !in_array($data, ['created', 'updated']))
              unset ($filterJson['filters'][$filter]);   
          }
          if($filterJson['filters']['is_closed'] != -1){
            $postFilters['le.is_closed'] = (($filterJson['filters']['is_closed']) && (intval($filterJson['filters']['is_closed']) === 1));
          }

          if (($startDate != null && $endDate != null) && $filterJson['filters']['filter_by_date']) {
            $map = ['created'=>'created', 'updated'=>'last_changed_date'];
            $fromDate = $startDate.' 00:00:00';
            $toDate = $endDate.' 23:59:59';
            unset($startDate);
            unset($endDate);
            $status = [];
            if(isset($postFilters['le.partner_status_id'])){
              if(!is_array($postFilters['le.partner_status_id']) && is_numeric($postFilters['le.partner_status_id'])){
                $status = [$this->replaceAlias('le.partner_status_id') => (int) $postFilters['le.partner_status_id']];
              }
              else if (count($postFilters['le.partner_status_id']) > 0){
                $status = [$this->replaceAlias('le.partner_status_id').' IN '=> $postFilters['le.partner_status_id']];
              }
              unset($postFilters['le.partner_status_id']);
            }
            if (isset($filterJson['filters']['settled_date']) && ($filterJson['filters']['settled_date'] == 0)) {//'based on selection'/settled = false
              $dateField = $this->replaceAlias('le.' . $map[$filterJson['filters']['filter_by_date']]);             
              $whereConditions[] = [$dateField. ' >= ' => $fromDate];
              $whereConditions[] = [$dateField. ' <= ' => $toDate];              
            }
            else{//settled date/default
              $whereConditions[] = [
                'OR' => [
                  [//settled in date range
                    'AND'=>[
                      $this->replaceAlias('pcc.lead_id').'  IS NOT null',
                      [$this->replaceAlias('pcc.funded_date'). ' >= ' => $fromDate],
                      [$this->replaceAlias('pcc.funded_date'). ' <= ' => $toDate],
                    ]
                  ],
                  'AND'=>[//not settled - created/updated in date range
                    $this->replaceAlias('pcc.lead_id').'  IS null',
                    // $this->replaceAlias('le.'.$map[$filterJason['filters']['filter_by_date']]). ' BETWEEN :fromDate AND :toDate',
                    [$this->replaceAlias('le.'.$map[$filterJson['filters']['filter_by_date']]). ' >= ' => $fromDate],
                    [$this->replaceAlias('le.'.$map[$filterJson['filters']['filter_by_date']]). ' <= ' => $toDate],
                    $status
                  ]
                ]
              ];
            }
          }
        }
        else {
          if($filterJson['show_archive'] === true)
            $includeArchived = true;
          if (strtolower($filterJson['content_type']) !== 'all'){
            $this->joinTables[] = [
              'pc'=> [
                'table' => 'partner_commissions',
                'alias' => 'pc',
                'type' => 'LEFT',
                'conditions' => 'pc.lead_id = le.lead_id'
              ]
            ];
            $whereConditions[] = [$this->replaceAlias('pc.commission_id') .' = '.$this->replaceAlias('le.latest_commission_id')];
            $whereConditions[] = [$this->replaceAlias('pc.funded_type') => $filterJson['content_type']];
          }

          $listPartnerStatus=((!empty($filterJson['list_status']) && strlen($filterJson['list_status'])>0)?explode(',',$filterJson['list_status']):null);
          if($listPartnerStatus!=null){
            $tmp = [];
            foreach($listPartnerStatus as $vps){
                if(empty($vps)) continue;
                $tmp[] = $vps;
            }
            $whereConditions[] = [$this->replaceAlias('le.partner_status_id').' IN' => $tmp];
         }
          $listLender=((!empty($filterJson['list_lender']) && strlen($filterJson['list_lender'])>0)?explode(',',$filterJson['list_lender']):null);
          if($listLender!=null){
            $tmp = [];
            foreach($listLender as $kl=>$vl){
              if(strlen(trim($vl))>1) {
                $tmp[] = 'lower('.$this->replaceAlias('lad.lender_name').') LIKE \'%' . strtolower($vl) . '%\'';
              }
            }
            if(count($tmp)> 0)
              $whereConditions[] = ['OR'=> $tmp];
          }
          if(strtolower($queryStringManage)=='settled'){
            //for settled
            $whereConditions[] = [$this->replaceAlias('pcc.lead_id').' IS NOT NULL'];
            if($startDate!=null && $endDate!=null){
              $dateField = $this->replaceAlias('pcc.funded_date');
              $whereConditions[] = [$dateField. ' >= '=> $startDate];
              $whereConditions[] = [$dateField. ' <= '=> $endDate]; 
            }          
          }else{//non settled page leads,pending,attempting,inprogress,rejected
            if($statusList)
              $whereConditions[] = [$this->replaceAlias('le.partner_status_id').' IN'=> $statusList];
            if($startDate!=null && $endDate!=null){
              $dateField = $this->replaceAlias('le.created');
              $whereConditions[] = [$dateField. ' >= '=> $startDate];
              $whereConditions[] = [$dateField. ' <= '=> $endDate]; 
            }
          }
        }
      }

      if (!$includeArchived) {
        $whereConditions[] = [$this->replaceAlias('le.is_archived') => 0];
      }

      if(strlen($searchValue) >0){
        $searchValue = '%' . strtolower($params['search_value']) . '%';
        $searchFields = ['le.lead_ref', 'le.organisation_name', 'le.purpose_other', 'le.amount_requested', 'ppt.product_type_name','len.lender_name', 
          'sc.lend_score', 'le.percentage', 'pu.name', 'llu.combined_status_string', 'pcc.funded_amount', 'pcc.funded_type', 'lo.lead_owner'];
        $searchFieldsNew = [];
        foreach ($searchFields as $searchField){
          $searchFieldsNew['LOWER('.$this->replaceAlias($searchField).') LIKE'] = $searchValue;
        }
        $whereConditions[] = ['OR' => $searchFieldsNew];
      }



      //stuck leads
      if(isset($lastOriginalLeadUpdateDays['selected']) && $isIntermediary){
        $statusWhereQuery = [];
        if ($lastOriginalLeadUpdateDays['status'] > 0 && in_array('status', $lastOriginalLeadUpdateDays['selected'])) {
          $statusWhereQuery = ['AND'=>[$this->replaceAlias('st2.lend_status_id').' NOT IN (4, 25)', $this->replaceAlias('llu2.created').'  < DATE_SUB(NOW(), INTERVAL '. $lastOriginalLeadUpdateDays['status'] . 'day)']];
        }
        $noteWhereQuery = [];
        if ($lastOriginalLeadUpdateDays['note'] > 0 && in_array('note', $lastOriginalLeadUpdateDays['selected'])) {
          $noteWhereQuery = ['OR'=>[$this->replaceAlias('ln3.created').' < DATE_SUB(NOW(), INTERVAL '. $lastOriginalLeadUpdateDays['note'] . ' day)', $this->replaceAlias('ollad.max_intermediary_note_id').' IS NULL']];
        }
        $grpNames = ['Settled', 'Rejected'];
        $whereConditions[] = [$this->replaceAlias('st.group_name').' NOT IN' => $grpNames];
        $whereConditions[] = [$this->replaceAlias('st2.group_name').' NOT IN' => $grpNames];
        $whereConditions[] = [$this->replaceAlias('le.is_closed') => 9];
        if(count($lastOriginalLeadUpdateDays['selected']) == 1){
          if(count($statusWhereQuery) > 0){
            $whereConditions[] = $statusWhereQuery;
          }
          elseif(count($noteWhereQuery) > 0){
            $whereConditions[] = $noteWhereQuery;
          }
        } elseif ((count($lastOriginalLeadUpdateDays['selected']) == 2) &&
            in_array($lastOriginalLeadUpdateDays['combinator'], ["AND", "OR"])){
          $whereConditions[] = [$lastOriginalLeadUpdateDays['combinator'], [$statusWhereQuery, $noteWhereQuery]];
        }
      }

      //add post filters
      foreach($postFilters as $postFilter=>$val){
        if($postFilter === 'le.is_closed'){
          if($val === true)
            $whereConditions[] = [$this->replaceAlias('le.is_closed') => 1];
          else
            $whereConditions[] = ['OR'=> [$this->replaceAlias('le.is_closed') . ' IS' => null, $this->replaceAlias('le.is_closed') => 0]];
        }
        else if (in_array($postFilter, ['pul.partner_user_id', 'le.product_type_id'])) {
          if (is_array($val)) {
            $conditions = [];
            $key = array_search(0, $val);//0 indicates null - leads which are not assigned
            if ($key !== false) {
              $conditions[] = $this->replaceAlias($postFilter)." IS NULL";
              unset($val[$key]);
            }
            if (count($val) > 0)
              $conditions[] = [$this->replaceAlias($postFilter).' IN' => $val];
            if(count($conditions)> 0)
              $whereConditions[] = ['OR' => $conditions];
          }
          else if(($val === 0) || ($val=== null))//0 indicates null - leads which are not assigned
            $whereConditions[] = $this->replaceAlias($postFilter)." IS NULL";
          else
            $whereConditions[] = [$this->replaceAlias($postFilter) => $val];
        }
        else{
          if(is_array($val) && (count($val) > 0))
            $whereConditions[] = [$this->replaceAlias($postFilter).' IN' => $val];
          else
            $whereConditions[] = [$this->replaceAlias($postFilter) => $val];
        }
      }

      //add call queue check
      $whereConditions[] = [$this->replaceAlias('le.call_queue_status').' IN' => Configure::read('Lend.NOT_IN_CALL_QUEUE_STATUS')];

      //apply ordering
      $leFields = ["created", "last_changed_date", "lead_ref", "amount_requested", "sales_monthly", "client_declared_sales_monthly", "campaign", "b_state", "statements_uploaded", "lead_type", "organisation_name", "purpose_other ", "is_archived ", "status_id", "partner_status_id"];
      //TO-DO: Need to update the leFields array above to reflect all lead fields - may not need to as this is what is in old function
      $validDirections = ['asc', 'desc'];
      if($order){
        foreach($order as $field=>$direction){
          if(strtolower(substr(trim($field), 0, 3)) != "le." && !in_array($field, $leFields)){
            $this->isLeadOnly = false;
          }
        }
      }

      if($limit){
        if(is_array($limit)){
          if (!is_numeric($limit['limit']) || !is_numeric($limit['offset'])) {
            $limitValue = 10;
          } else {
            $limitValue = $limit['limit'];
            $offsetValue = $limit['offset'];
          }
        } elseif (is_numeric($limit)) {
          $limitValue = $limit;
        } else {
          $limitValue = 10;
        }
      }

      $leadIds = [];
      if($this->isLeadOnly == true){
        $leads = TableRegistry::getTableLocator()->get('LeadEntity', ['connection' => ConnectionManager::get('reader_db')])
          ->find('all', ['contain'=> ['LeadAssociatedDataEntity'=> ['fields' => []]]])
          ->select('lead_id')
          ->where($whereConditions)
          ;
        $selectedLeads = $leads->toList();
        if($onlyCount){
          return [['count' => count($selectedLeads)]];
        }
        if(!$selectedLeads){
          return [];
        }

        $leadIds = array_column($selectedLeads, 'lead_id');
        $whereConditions[] = ['LeadEntity.lead_id IN' => $leadIds];
      }

      $leadsTable = TableRegistry::getTableLocator()->get('LeadEntity', ['connection' => ConnectionManager::get('reader_db')]);

      $whereConditionsCommissions = [];
      if(isset($startDate) && isset($endDate)){
        $whereConditionsCommissions['funded_date >='] = $startDate;
        $whereConditionsCommissions['funded_date <='] = $endDate;
      }
      if($leadIds)
        $whereConditionsCommissions['lead_id IN'] = $leadIds;

      $partnerCommissionTable = TableRegistry::getTableLocator()->get('PartnerCommissionEntity', ['connection' => ConnectionManager::get('reader_db')]);

      $subquery = $partnerCommissionTable
        ->find()
        ->select([
            'lead_id'=> 'lead_id',
            'max_commission_id' => $partnerCommissionTable->find()->func()->max('commission_id')
        ])
        ->where($whereConditionsCommissions)->group(['lead_id']);

      $this->joinTables[] = [
        'PartnerCommissionEntityMax'=> [
          'table' => $subquery,
          'alias' => 'PartnerCommissionEntityMax',
          'type' => 'LEFT',
          'conditions' => 'PartnerCommissionEntityMax.lead_id = LeadEntity.lead_id'
        ]
      ];
      $this->joinTables[] = [
        'pcc'=> [
          'table' => 'partner_commissions',
          'alias' => 'pcc',
          'type' => 'LEFT',
          'conditions' => 'pcc.commission_id = PartnerCommissionEntityMax.max_commission_id'
        ]
      ];//pcc
      $this->addLeadField(null, [
        'funded_amount'=> 'pcc.funded_amount',
        'funded_type'=> 'pcc.funded_type',
        'funded_date'=> 'pcc.funded_date',
        'settled_lender_ref' => 'pcc.contract_ref',
      ]);

      $this->joinTables[] = [
        'setr'=> [
          'table' => 'settlement_reviews',
          'alias' => 'setr',
          'type' => 'LEFT',
          'conditions' => 'setr.commission_id = pcc.commission_id'
        ]
      ];//setr
      $this->addLeadField(null, [
        'settled_settlement_date'=> 'setr.settlement_date',
        'settled_lender_name'=> 'setr.lender_name',
        'settled_product_name'=> 'setr.settled_product_name',
//        'settled_lender_ref'=> 'setr.lender_ref',
        'settled_loan_amount'=> 'setr.loan_amount',
        'settled_brokerage_fee'=> 'setr.brokerage_fee',
        'settled_brokerage_fee_value_type'=> 'setr.brokerage_fee_value_type',
        'settled_base_rate'=> 'setr.base_rate',
        'settled_customer_rate'=> 'setr.customer_rate',
        'settled_total_estab_fees'=> 'setr.total_estab_fees',
//        'settled_application_fee_included'=> 'setr.application_fee_included',
        'settled_origination_fee'=> 'setr.origination_fee',
        'settled_referrer_commission'=> 'setr.referrer_commission',
        'settled_mirrors_submission'=> 'setr.mirrors_submission',
        'settled_tier_name'=> 'setr.tier_name',
        'settled_apr'=> 'setr.apr',
        'settled_monthly_fees'=> 'setr.acc_keep_fee',
        'settled_repayment_amt'=> 'setr.repayment_amt',
        'settled_repayment_freq'=> 'setr.repayment_freq',
      ]);
      $this->leadFields['settled_application_fee_included'] = $leadsTable->query()->newExpr()->addCase(
        $leadsTable->query()->newExpr()->add(['setr.application_fee_included = 1']),
        ["YES", "NO"],
        ['string', 'string']
      );
      $this->fieldAliases['settled_application_fee_included'] = 'settled_application_fee_included';



      $this->addLeadField(null, ['created', 'lead_ref', 'amount_requested',
        'client_declared_sales_monthly', 'sales_monthly', 'campaign', 
        'b_state', 'statements_uploaded', 'lead_type', 'organisation_name', 'purpose_other',
        'is_archived', 'status_id', 'man_status_id', 'LeadEntity__completion_percent'=> 'LeadEntity.percentage',
        'lead_id','partner_status_id','is_tick_and_flick','last_changed_date','is_closed', 'referer',
        'business_name',
        ], ['referer']);

      $this->joinTables[] = [
        'refp'=> [
          'table' => 'referrer_people',
          'alias' => 'refp',
          'type' => 'LEFT',
          'conditions' => 'refp.id = LeadEntity.referrer_person_id'
        ]
      ];

      $this->addLeadField(null, ['referrer_contact_number'=>'refp.contact_number']);//refp

      $this->joinTables[] = [
        'NewPartnerUserLeadsEntity' => [
          'table' => 'partner_user_leads',
          'alias' => 'NewPartnerUserLeadsEntity',
          'type' => 'LEFT',
          'conditions' => ['NewPartnerUserLeadsEntity.status' => 'ACCESS', 'NewPartnerUserLeadsEntity.lead_id = LeadEntity.lead_id']
        ],
        'pul'=> [
          'table' => 'partner_users',
          'alias' => 'pul',
          'type' => 'LEFT',
          'conditions' => 'pul.partner_user_id = NewPartnerUserLeadsEntity.partner_user_id'//pul
        ]
      ];
      $this->addLeadField(null, [
        'pul_name' => 'pul.name',
        'pul_granted' => 'NewPartnerUserLeadsEntity.granted'
      ]);//pul

      $this->fieldAliases['pul_granted'] = 'pul_granted';

      // purpose
      $this->joinTables[] = [
        'refp'=> [
          'table' => 'frm_purpose',
          'alias' => 'pr',
          'type' => 'LEFT',
          'conditions' => 'pr.purpose_id = LeadEntity.purpose_id'
        ]
      ];

      $this->addLeadField(null, ['purpose'=>'pr.purpose']); // pr

      $this->leadFields['referrer']  = $leadsTable->query()->newExpr()->addCase(
        $leadsTable->query()->newExpr()->add(['LeadEntity.referer IS NOT NULL']),
        [
          $leadsTable->query()->identifier('LeadEntity.referer'),
          $leadsTable->query()->newExpr()->add('CONCAT(refp.first_name, \' \', refp.last_name)')
        ]
      );


      $this->leadFields['details'] = $leadsTable->query()->func()->concat([$leadsTable->query()->identifier('lo.first_name'),' ',$leadsTable->query()->identifier('lo.last_name')]);
      $this->leadFields['lead_owner'] = $leadsTable->query()->func()->concat([$leadsTable->query()->identifier('lo.first_name'),' ',$leadsTable->query()->identifier('lo.last_name')]);
      $this->addLeadField(null, ['mobile' => 'lo.mobile' , 'email' => 'lo.email', 'lead_owner_dob' => 'lo.dob']);

      $this->leadFields['partner_user_name'] = $leadsTable->query()->func()->coalesce([
        $leadsTable->query()->identifier('pul.name'),
        '-'
      ]);

      $this->leadFields['is_asset_e2e'] = $leadsTable->query()->newExpr()->addCase(
        $leadsTable->query()->newExpr()->add(['laf.contract_type IS NULL']),
        ["basic", "e2e"],
        ['string', 'string']
      );

      $this->fieldAliases['referrer'] = 'referrer';
      $this->fieldAliases['details'] = 'details';
      $this->fieldAliases['lead_owner'] = 'lead_owner';
      $this->fieldAliases['partner_user_name'] = 'partner_user_name';
      $this->fieldAliases['is_asset_e2e'] = 'is_asset_e2e';

      $this->joinTables[] = [
        'lo'=> [
          'table' => 'lead_owners',
          'alias' => 'lo',
          'type' => 'INNER',
          'conditions' => ['lo.lead_id = LeadEntity.lead_id', 'lo.point_of_contact' => true, 'lo.status' => 'active']
        ]
      ];
      $this->addLeadField(null, ['lender_name'=> 'lad.lender_name', 'owner_consent'=>'lad.owner_consent']);
      $this->joinTables[] = [
        'lad'=> [
          'table' => 'lead_associated_data',
          'alias' => 'lad',
          'type' => 'INNER',
          'conditions' => ['lad.lead_id = LeadEntity.lead_id']
        ]
      ];

      $this->joinTables[] = [
        'sal'=> [
          'table' => 'sales',
          'alias' => 'sal',
          'type' => 'LEFT',
          'conditions' => 'sal.sale_id = lad.max_sale_id'
        ]
      ];

      $this->joinTables[] = [
        'sd'=> [
          'table' => 'sale_details',
          'alias' => 'sd',
          'type' => 'LEFT',
          'conditions' => 'sd.sale_id = sal.sale_id'
        ]
      ];

      $this->joinTables[] = [
        'sal_lp'=> [
          'table' => 'lender_product',
          'alias' => 'sal_lp',
          'type' => 'LEFT',
          'conditions' => 'sal_lp.lender_product_id = sal.product_id'
        ]
      ];
      $this->joinTables[] = [
        'len'=> [
          'table' => 'lenders',
          'alias' => 'len',
          'type' => 'LEFT',
          'conditions' => 'len.lender_id = sal_lp.lender_id'
        ]
      ];
      $this->joinTables[] = [
        'il'=> [
          'table' => 'intermediary_lenders',
          'alias' => 'il',
          'type' => 'LEFT',
          'conditions' => 'il.id = len.intermediary_lender_id'
        ]
      ];

      $this->joinTables[] = [
        'ppt'=> [
          'table' => 'partner_product_types',
          'alias' => 'ppt',
          'type' => 'LEFT',
          'conditions' => 'ppt.product_type_id = LeadEntity.product_type_id'
        ]
      ];
      $this->addLeadField(null, ['product_type_name'=> 'ppt.product_type_name']);//ppt
      $this->joinTables[] = [
        'st'=> [
          'table' => 'lend_statuses',
          'alias' => 'st',
          'type' => 'LEFT',
          'conditions' => 'st.lend_status_id = LeadEntity.partner_status_id'
        ]
      ];
      $this->addLeadField(null, ['status'=>'st.status_name', 'lead_status'=>'st.status_name', 'group_name'=> 'st.group_name']);//st
      $this->joinTables[] = [
        'mt'=> [
          'table' => 'man_statuses',
          'alias' => 'mt',
          'type' => 'LEFT',
          'conditions' => 'mt.id = LeadEntity.man_status_id'
        ]
      ];
      $this->joinTables[] = [
        'msg'=> [
          'table' => 'man_status_groups',
          'alias' => 'msg',
          'type' => 'LEFT',
          'conditions' => 'msg.id = mt.man_status_group_id'
        ]
      ];
      $this->addLeadField(null, ['lead_man_status'=>'mt.status_name']);//mt

      $this->addLeadField(null, ['lender_logo'=>'len.lender_logo','lender_id'=>'len.lender_id','shorthand'=>'len.shorthand']);//len
      $this->joinTables[] = [
        'llu'=> [
          'table' => 'lender_lead_updates',
          'alias' => 'llu',
          'type' => 'LEFT',
          'conditions' => 'llu.lender_update_id = LeadEntity.lender_status_id'
        ]
      ];
      
      $this->addLeadField(null, ['lender_status'=> 'llu.combined_status_string', 'lender_modified_time'=>'llu.lender_modified_time']);//llu
      $this->joinTables[] = [
        'ln'=> [
          'table' => 'lead_notes',
          'alias' => 'ln',
          'type' => 'LEFT',
          'conditions' => 'ln.note_id = lad.max_note_id'
        ]
      ];
      $this->addLeadField(null, ['last_note_created' => 'ln.created', 'last_note' =>'ln.notes']);//ln
      $this->joinTables[] = [
        'ln2'=> [
          'table' => 'lead_notes',
          'alias' => 'ln2',
          'type' => 'LEFT',
          'conditions' => 'ln2.note_id = lad.max_intermediary_note_id'
        ]
      ];

      $this->addLeadField(null, ['last_intermediary_note_created' => 'ln2.created', 'last_intermediary_note' =>'ln2.notes']);//ln2
      $this->joinTables[] = [
        'duet'=> [
          'table' => 'partner_callbacks',
          'alias' => 'duet',
          'type' => 'LEFT',
          'conditions' => 'duet.callback_id = lad.min_callback_id'
        ]
      ];
      
      $this->addLeadField(null, ['next_task' => 'duet.scheduled_time']);//duet
      $this->joinTables[] = [
        'sc'=> [
          'table' => 'lend_score',
          'alias' => 'sc',
          'type' => 'LEFT',
          'conditions' => 'sc.lead_id = LeadEntity.lead_id'
        ]
      ];

      $this->addLeadField(null, ['lend_score'=>'sc.lend_score']);//sc
      $this->addLeadField(null, ['intermediary_lender_name'=> 'il.name', 'intermediary_lender_logo'=> 'il.logo']);//il
      $this->joinTables[] = [
        'history_added'=> [
          'table' => 'partner_lead_history',
          'alias' => 'history_added',
          'type' => 'LEFT',
          'conditions' => [
            'history_added.lead_id = LeadEntity.lead_id',
            'history_added.partner_id = LeadEntity.partner_id',
            'history_added.partner_user_id IS NOT NULL',
            'history_added.history_detail' => 'Lead Added'
          ]
        ]
      ];
      $this->joinTables[] = [
        'pu'=> [
          'table' => 'partner_users',
          'alias' => 'pu',
          'type' => 'LEFT',
          'conditions' => 'pu.partner_user_id = history_added.partner_user_id'
        ]
      ];
      
      $this->addLeadField(null, ['added_by' => 'pu.name']);//pu

      $this->addLeadField(null, ['man_group_name'=>'msg.group_name']);//msg

      $this->joinTables[] = [
        'cps'=> [
          'table' => 'con_page_status',
          'alias' => 'cps',
          'type' => 'LEFT',
          'conditions' => 'cps.lead_id = LeadEntity.lead_id'
        ]
      ];

      $this->addLeadField(null, ['consumer_guide_status' =>'cps.con_cgqp_status']);//cps
      $this->joinTables[] = [
        'laf'=> [
          'table' => 'lead_asset_finance',
          'alias' => 'laf',
          'type' => 'LEFT',
          'conditions' => 'laf.lead_id = LeadEntity.lead_id'
        ]
      ];

      $this->addLeadField(null, [
        'supplier'=>'laf.supplier', 
        'supplier_contact_name'=>'laf.supplier_contact_name', 
        'contract_type'=>'laf.contract_type',
        'asset_make' => 'laf.make',
        'asset_model' => 'laf.model',
        'asset_year' => 'laf.year',
        'asset_age_months'=>'laf.asset_age_months',
        'asset_sale_type' => 'laf.sale_type',
        'asset_valuation' => 'laf.valuation',
        'asset_manual_valuation' => 'laf.manual_valuation',
        'asset_condition' => 'laf.condition',
        'asset_purchase_price' => 'laf.asset_purchase_price',
        'asset_deposit'=>'laf.asset_deposit',
        'asset_ltv' => 'laf.ltv',
      ]);//laf
      $this->addLeadField(null, ['submission_date'=>'sal.created']);//sal

      $this->addLeadField(null, [
        'selected_lender_name'=>'sd.lender_name',
        'selected_tier_name'=>'sd.tier_name',
        'selected_product_name'=>'sd.product_name',
        'selected_establishment_fees'=>'sd.establishment_fees',
        'selected_monthly_fees'=>'sd.monthly_fees',
//        'selected_include_fees_requested'=>'sd.include_fees_requested',
        'selected_financed_amount'=>'sd.financed_amount',
        'selected_commission'=>'sd.commission',
        'selected_commission_type'=>'sd.commission_type',
        'selected_origination_fee'=>'sd.origination_fee',
        'selected_term_months'=>'sd.term_months',
        'selected_apr'=>'sd.apr',
        'selected_base_rate'=>'sd.base_rate',
        'selected_customer_rate'=>'sd.customer_rate',
        'selected_repayment_amt'=>'sd.repayment_amt',
        'selected_repayment_freq'=>'sd.repayment_freq'
      ]);//sd
      $this->leadFields['selected_include_fees_requested'] = $leadsTable->query()->newExpr()->addCase(
        $leadsTable->query()->newExpr()->add(['sd.include_fees_requested = 1']),
        ["YES", "NO"],
        ['string', 'string']
      );

      $this->fieldAliases['selected_include_fees_requested'] = 'selected_include_fees_requested';


      if ($isIntermediary) {
        $lenderProductIds = [];
        $products = TableRegistry::getTableLocator()->get('LenderProductEntity', ['connection' => ConnectionManager::get('reader_db')])
          ->find('all', ['contain'=> ['LenderEntity'=> ['fields' => ['lender_id']]]])
          ->select('lender_product_id')
          ->where(['LenderEntity.partner_id'=> $partnerId])->enableHydration(false)->toArray();
        $lenderProductIds = array_column($products, 'lender_product_id');

        $this->addLeadField(null, ['original_lead_status_update_date'=> 'llu2.created']);//llu2
        
        //pu2 and pu3 manual joins
        $this->joinTables[] = [
          'CustomIntermediaryLenderMappingEntity' => [
            'table' => 'intermediary_lender_mapping',
            'alias' => 'CustomIntermediaryLenderMappingEntity',
            'type' => 'LEFT',
            'conditions' => ['CustomIntermediaryLenderMappingEntity.original_lead_id = LeadEntity.lead_id']
          ],
          'OriginalLeadEntity'=> [
            'table' => 'leads',
            'alias' => 'OriginalLeadEntity',
            'type' => 'LEFT',
            'conditions' => 'CustomIntermediaryLenderMappingEntity.original_lead_id = OriginalLeadEntity.lead_id'
          ],

          'llu2'=> [
            'table' => 'lender_lead_updates',
            'alias' => 'llu2',
            'type' => 'LEFT',
            'conditions' => 'llu2.lender_update_id = OriginalLeadEntity.lender_status_id'
          ],//llu2

          'st2'=> [
            'table' => 'lend_statuses',
            'alias' => 'st2',
            'type' => 'LEFT',
            'conditions' => 'st2.lend_status_id = OriginalLeadEntity.partner_status_id'
          ],//llu2

          'pu3'=> [
            'table' => 'partner_users',
            'alias' => 'pu3',
            'type' => 'LEFT',
            'conditions' => ['pu3.partner_id = OriginalLeadEntity.partner_id', 'pu3.account_admin = 1']
          ],//pu3
          
          'ollad'=> [
            'table' => 'lead_associated_data',
            'alias' => 'ollad',
            'type' => 'LEFT',
            'conditions' => 'CustomIntermediaryLenderMappingEntity.original_lead_id = ollad.lead_id'
          ],

          'ln3'=> [
            'table' => 'lead_notes',
            'alias' => 'ln3',
            'type' => 'LEFT',
            'conditions' => 'ln3.note_id = ollad.max_intermediary_note_id'
          ],//ln3

          'PartnerLeadHistoryEntity'=> [
            'table' => 'partner_lead_history',
            'alias' => 'PartnerLeadHistoryEntity',
            'type' => 'LEFT',
            'conditions' => 'PartnerLeadHistoryEntity.history_id = ollad.max_history_id'
          ]
          ,
          'pu2'=> [
            'table' => 'partner_users',
            'alias' => 'pu2',
            'type' => 'LEFT',
            'conditions' => 'pu2.partner_user_id = PartnerLeadHistoryEntity.partner_user_id'
          ]//pu2
        ];
        
        $this->addLeadField(null, ['pu2.partner_user_id', 'pu2.name', 'pu2.email', 'pu2.mobile'], ['pu2.partner_user_id', 'pu2.name', 'pu2.email', 'pu2.mobile']);//pu2
        $this->addLeadField(null, ['pu3.partner_user_id', 'pu3.name', 'pu3.email', 'pu3.mobile'],['pu3.partner_user_id', 'pu3.name', 'pu3.email', 'pu3.mobile']);//pu3
        $this->addLeadField(null, ['CustomIntermediaryLenderMappingEntity.new_lead_id','CustomIntermediaryLenderMappingEntity.original_lead_id'],['CustomIntermediaryLenderMappingEntity.new_lead_id','CustomIntermediaryLenderMappingEntity.original_lead_id']);
        if(count ($lenderProductIds) > 0)
        {
          //ol_llu
          $this->joinTables[] = [
            'ol_llu'=> [
              'table' => 'lender_lead_updates',
              'alias' => 'ol_llu',
              'type' => 'LEFT',
              'conditions' => [
                  'ol_llu.product_id IN' => $lenderProductIds,
                  'ol_llu.lead_id = CustomIntermediaryLenderMappingEntity.original_lead_id'
              ]
            ]
          ];//ol_llu
          $this->addLeadField(null, ['referrer_status_name'=> 'ol_llu.combined_status_string']);//ol_llu
        }
        else{
          $this->leadFields['referrer_status_name'] = $leadsTable->query()->newExpr('NULL');
          $this->fieldAliases['referrer_status_name'] = 'referrer_status_name';

        }
        
        $this->addLeadField(null, [
          'original_lead_status_name' => 'st2.status_name', 
          'original_lead_status_id' => 'st2.lend_status_id',
          'original_lead_status_group_name' => 'st2.group_name'
        ]);//st2

        $this->addLeadField(null, [
          'original_lead_last_note' => 'ln3.notes',
          'original_lead_note_update_date' => 'ln3.created',
        ]);//ln3

        $this->otherFields['IntermediaryLenderMappingEntity.OriginalLead.LeadAssociatedDataEntity'] = ['fields'=>['LeadAssociatedDataEntity__lad_id'=>'LeadAssociatedDataEntity.id']];
        $this->leadFields['referrer_name'] = $leadsTable->query()->newExpr()->addCase(
          $leadsTable->query()->newExpr()->add(['pu2.partner_user_id IS NOT NULL']),
          [
            $leadsTable->query()->identifier('pu2.name'),
            $leadsTable->query()->identifier('pu3.name')
          ],
          ['string', 'string']
        );
        $this->leadFields['referrer_email'] = $leadsTable->query()->newExpr()->addCase(
          $leadsTable->query()->newExpr()->add(['pu2.partner_user_id IS NOT NULL']),
          [
            $leadsTable->query()->identifier('pu2.email'),
            $leadsTable->query()->identifier('pu3.email')
          ],
          ['string', 'string']
        );
        $this->leadFields['referrer_mobile'] = $leadsTable->query()->newExpr()->addCase(
          $leadsTable->query()->newExpr()->add(['pu2.partner_user_id IS NOT NULL']),
          [
            $leadsTable->query()->identifier('pu2.mobile'),
            $leadsTable->query()->identifier('pu3.mobile')
          ],
          ['string', 'string']
        );
      }
      else
      {
        $this->leadFields['referrer_name'] = $leadsTable->query()->newExpr('NULL');
        $this->leadFields['referrer_email'] = $leadsTable->query()->newExpr('NULL');
        $this->leadFields['referrer_mobile'] = $leadsTable->query()->newExpr('NULL');
        $this->leadFields['referrer_status_name'] = $leadsTable->query()->newExpr('NULL');
        $this->fieldAliases['referrer_status_name'] = 'referrer_status_name';
      }

      $this->fieldAliases['referrer_name'] = 'referrer_name';
      $this->fieldAliases['referrer_email'] = 'referrer_email';
      $this->fieldAliases['referrer_mobile'] = 'referrer_mobile';

      if($onlyCount){
        $this->leadFields = ['count' => 'COUNT(LeadEntity.lead_id)'];
        foreach ($this->otherFields as $entity => $props){
          $this->otherFields[$entity]['fields'] = [];
        }
      }

      $this->applyExtraQueryOptions($extraQueryOptions);

      //add mandatory fields for joins (if they don't already exist)
      foreach($this->entityMap as $map){
        if(!isset($map['keys']) || ($map['join'] === 'manual'))
          continue;
        $fullEntity = '';
        foreach ($map['keys'] as $entity => $fields) {
          if($entity === 'LeadEntity')
          {
            foreach($fields as $field){
              if(!in_array($field, $this->leadFields))
                $this->leadFields[] = $field;
            }
          }
          else{
            $fullEntity.= (($fullEntity === "")?"":".").$entity;
            if (isset($this->otherFields[$fullEntity])) {//only add specific fields if fields have already been specified, else ORM does will make this work
              foreach($fields as $field){
                if(!in_array($field, $this->otherFields[$fullEntity]['fields']))
                  $this->otherFields[$fullEntity]['fields'][] = $field;
              }
            }
          }
        }
      }

      // assemble the components and get results
      $options =['contain' => $this->otherFields];

      $qry = $leadsTable->find('all', $options)->select($this->leadFields);
      
      //joins
      foreach ($this->joinTables as $join) {
        foreach ($join as $alias => $joinConfig) {
          $table = $joinConfig['table'];
          $alias = $joinConfig['alias'];
          $type = $joinConfig['type'];
          $conditions = $joinConfig['conditions'];
  
          switch ($type) {
            case 'LEFT':
              $qry->leftJoin([$alias => $table], $conditions);
              break;
            case 'INNER':
              $qry->innerJoin([$alias => $table], $conditions);
              break;
          }
        }
      }
      if($this->isLeadOnly == true){
        $whereConditions = ['LeadEntity.lead_id IN' => $leadIds];
        // $qry->groupBy('LeadEntity.lead_id');//grouping is causing slowness
      }

      $qry->where($whereConditions);

      // dump($this->fieldAliases);
      if($order){
        foreach($order as $field=>$direction){
          if(isset($this->fieldAliases[$field]))
            $field = $this->fieldAliases[$field];
          $orderBy[$field] = (in_array(strtolower($direction), $validDirections) ? $direction : 'desc');
        }
        if (empty($orderBy)) {
          $orderBy['LeadEntity.created'] = 'desc';
        }
      }
      $qry->order($orderBy);
      $qry->limit($limitValue);
      $qry->offset($offsetValue);

      $qry->distinct();
      $leadList = $qry->enableHydration(false)->toArray();
      //
      $this->processExtraFields($leadList);
      $this->flattenLeadData($leadList);
      return $leadList;
    }

    /**
     * Function to flatten and post process ORM result data
     */
    private function flattenLeadData(&$leadData){
      $aliases = array_keys($this->fieldAliases);
      foreach($leadData as $li=> $lead){
        //flatten arrays
        foreach ($leadData[$li] as $fi => $f){
          if(is_array($f))
            $this->mergeRecursiveNonNumericArrays($leadData[$li], $f);
          //this is due to conflict - column alias needed and table name in relation is same
          if(($fi === 'lender_status') && is_array($leadData[$li][$fi]) && isset($leadData[$li][$fi]['lender_status_remove'])){
            $leadData[$li]['lender_status'] = $leadData[$li][$fi]['lender_status_remove'];
            if(is_array($leadData[$li][$fi]))
              unset($leadData[$li][$fi]['lender_status_remove']);
          }  
        }
        //remove unwanted fields and fix data types
        foreach ($lead as $fi => $f){
          if(!in_array($fi, $aliases))
            unset($leadData[$li][$fi]);
          if($f instanceof \Cake\I18n\Time){
            $leadData[$li][$fi] = $f->format('Y-m-d H:i:s');
          }
          else if (is_float($f) || is_double($f)) {
            $leadData[$li][$fi] = number_format($f, 2, '.', '');
          }
        }
      }      
    }


    /**
     * Iteratively add array data to outer array
     */
    private function mergeRecursiveNonNumericArrays(&$leadDataRow, $currentArray) {
      foreach ($currentArray as $key => $value) {
        if (is_array($value))
          $this->mergeRecursiveNonNumericArrays($leadDataRow,$value);
        // else if(!is_numeric($key) && !isset($leadDataRow[$key]))
        else if(!is_numeric($key))
          $leadDataRow[$key] = $value;
      }
    }


    //
    /**
     * Tracks lead only query and add alias prefix if required
     */
    private function replaceAlias($field){
      $parts = explode('.', $field);
      if (count($parts) == 1){//this indicates no alias provided, assumed to be lead
        $parts = ['le', $field];
      }

      if($parts[0] !== 'le')
        $this->isLeadOnly = false;

      
      $alias = $parts[0];
      if(isset($this->entityMap[$alias])){
        $ent = $this->entityMap[$alias];
        $f = $parts[1];
        switch ($ent['join']) {
          case 'none':
            $field = $ent['entity'].".".$parts[1];
            break;
          case 'manual':
            //we may not need to add addlientfield for manual tables as fields shuld be available - TO-DO check this
            return $field;//no change rquired for manual field 
            $fieldAlias = $alias."__".$alias."_".$parts[1];
            $field = $alias."_".$parts[1];
            $this->addLeadField($alias, [$fieldAlias => $alias.".".$parts[1]], [$fieldAlias => $alias.".".$parts[1]]);
            break;
          case 'orm':
            //creating unique aliased field for the where condition
            $lastEntity = array_key_last($ent['keys']);
            $fieldAlias = $lastEntity."__".$alias."_".$parts[1];
            $field = $alias."_".$parts[1];
            $field = $fieldAlias;
            $this->addLeadField($alias, [$fieldAlias => $lastEntity.".".$parts[1]], [$fieldAlias => $lastEntity.".".$parts[1]]);
            break;
        }
      }
      else
        Log::error("Unknown entity alias ".$parts[0]);
      return $field;   
      
      
    }
    /**
     * Replaces old lead query alias with relevant Entity
     */
    private function replaceAliasOld($field, $trackIsLeadOnly = true){
      $parts = explode('.', $field);
      if (count($parts) === 1) {//this indicates no alias provided, assumed to be lead
        return "LeadEntity.".$field;  
      }
      $alias = $parts[0];
      $fieldName = $parts[1];
      if (array_key_exists($alias, $this->entityMap)) {
        if(($trackIsLeadOnly === true) && ($alias !== 'le'))
          $this->isLeadOnly = false;
        return $this->entityMap[$alias]['entity'] . '.' . $fieldName;
      } else {
        return $field;
      }
    }

    /**
     * Add field to the query
     */
    private function addLeadField($alias = null, $fields = [], $trackingExcludedFields = []) {

      //replace aliases with ORM entity names in alised fields and add Entity names to values to avoid ambiguity 
      $ent = null;
      if($alias && isset($this->entityMap[$alias]) && ($this->entityMap[$alias]['join'] === 'orm')){
        $ent = array_key_last($this->entityMap[$alias]['keys']);
        foreach($fields as $i=>$field){
          $v = $field;
          if(strpos($field, ".") === false)
            $v = $ent.".".$field;
          if(!is_int($i)){
            $j = str_replace($alias."__", $ent."__", $i);
            unset($fields[$i]);
            $fields[$j] = $v;
          }
        }
      }
      if($alias)//indicates ORM fields
      {
        $entity = $this->entityMap[$alias]['entity'];
        // Initialize 'fields' array if it does not exist
        if (!isset($this->otherFields[$entity]) || !isset($this->otherFields[$entity]['fields'])) {
          $this->otherFields[$entity]['fields'] = $fields;
        }
        else{
          $this->otherFields[$entity]['fields'] = array_merge($this->otherFields[$entity]['fields'], $fields);
        }
      }
      else{
        $this->leadFields = array_merge($this->leadFields, $fields);
      }

      $trackingExcluded = (count($trackingExcludedFields) > 0);
      $excludedIndexes = array_keys($trackingExcludedFields);
      foreach($fields as $i=>$field){
        if($trackingExcluded === true){
          if(!is_int($i) && (!in_array($i, $excludedIndexes))){
            $fieldParts = explode('__', $i);
            //fieldParts should always have two elements
            $this->fieldAliases[isset($fieldParts[1]) ? $fieldParts[1] : $fieldParts[0]] = $i;
          }
          else if(is_int($i) && !in_array($field, $trackingExcludedFields)){
            $val = null;
            if($ent)//orm field
              $val = $ent."__".$field;
            else{//Lead field
              if(strpos($field, ".") === false)
                $val = "LeadEntity__".$field;
              else{//manual join field
                $fieldParts = explode('.', $field);
                $val = $fieldParts[0]."__".$field;
              }
            }
            $this->fieldAliases[$field] = $val;
          }
        }
        else{
          if(!is_int($i)){
            $fieldParts = explode('__', $i);
            //fieldParts should always have two elements
            $this->fieldAliases[isset($fieldParts[1]) ? $fieldParts[1] : $fieldParts[0]] = $i;
          }
          else{
            $val = null;
            if($ent)//orm field
              $val = $ent."__".$field;
            else{//Lead field
              if(strpos($field, ".") === false)
                $val = "LeadEntity__".$field;
              else{//manual join field
                $fieldParts = explode('.', $field);
                $val = $fieldParts[0]."__".$field;
              }
            }
            $this->fieldAliases[$field] = $val;
          }
        }
      }
    }
    
    public function getInterMediaryLead($params = false, 
      $order = null, 
      $limit = false, 
      $assoc = true, 
      $filterJason = false, 
      $queryStringManage = null, 
      $onlyCount = null, 
      $isIntermediary = false, 
      $lastOriginalLeadUpdateDays = ['status' => 0, 'note' => 0, 'selected'=>[]],
      $returnTotalCount = false,
      $partnerId,
      $extraQueryOptions = null
      )
      {


      $whereQuery ='';
      $statusWhereQuery = "";
      if ($lastOriginalLeadUpdateDays['status'] > 0 && in_array('status', $lastOriginalLeadUpdateDays['selected'])) {
        $statusWhereQuery = " (COALESCE(d.updated, d.created) < DATE_SUB(CONVERT_TZ( now(), 'UTC', 'Australia/Sydney'), INTERVAL " . $lastOriginalLeadUpdateDays['status'] . " day))";
      }
      $noteWhereQuery = "";
      if ($lastOriginalLeadUpdateDays['note'] > 0 && in_array('note', $lastOriginalLeadUpdateDays['selected'])) {
        $noteWhereQuery = " (ln3.created < DATE_SUB(CONVERT_TZ( now(), 'UTC', 'Australia/Sydney'), INTERVAL " . $lastOriginalLeadUpdateDays['note'] . " day) OR ollad.max_intermediary_note_id IS NULL)";
      }
      if(count($lastOriginalLeadUpdateDays['selected']) == 1){
        if ($statusWhereQuery != "") {
          $whereQuery .= " AND " . $statusWhereQuery;
        } elseif ($noteWhereQuery != "") {
          $whereQuery .= " AND " . $noteWhereQuery;
        }
      } elseif (count($lastOriginalLeadUpdateDays['selected']) == 2){
        if($lastOriginalLeadUpdateDays['combinator'] == "AND") {
          $whereQuery .= " AND (" . $statusWhereQuery . " AND " . $noteWhereQuery . ")";
        }elseif ($lastOriginalLeadUpdateDays['combinator'] == "OR") {
          $whereQuery .= " AND (" . $statusWhereQuery . " OR " . $noteWhereQuery . ")";
        }
      }

      $sql = "select
                DISTINCT
                CONCAT(lo.first_name,' ',lo.last_name) as details,
                s.created as created,
                l.lead_ref,
                l.amount_requested as amount_requested,
                l.sales_monthly as sales_monthly,
                ppt.product_type_name as product_type_name,
                l.lead_type as lead_type,
                len.lender_logo as lender_logo,
                ln.notes   as last_note,
                ln2.created as last_intermediary_note_created,
                ln2.notes   as last_intermediary_note,
                CONCAT(lo.first_name, ' ', lo.last_name) as lead_owner,
                st.status_name as lead_status,
                l.organisation_name as organisation_name,
                l.purpose_other as purpose_other,
                l.is_archived as is_archived,
                l.status_id as status_id,
                l.man_status_id as man_status_id,
                len.lender_id as lender_id,
                len.shorthand as shorthand,
                il.name as intermediary_lender_name,
                il.logo as intermediary_lender_logo,
                l.percentage as completion_percent,
                l.lead_id as lead_id,
                l.partner_status_id as partner_status_id,
                pu.name as added_by,
                st.group_name as group_name,
                COALESCE(d.updated, d.created) AS original_lead_status_update_date,
                  lst.status_name as original_lead_status_name,
                  lst.lend_status_id as original_lead_status_id,
                  lst.group_name                           as original_lead_status_group_name,
                  ln3.notes                                as original_lead_last_note,
                  ln3.created                              as original_lead_note_update_date,
                  lst.status_name referrer_status_name,
                  CASE WHEN pu2.partner_user_id IS NOT null THEN pu2.name ELSE pu3.name END as referrer_name
                  from leads as l
                  join intermediary_lender_mapping as mp on l.lead_id = mp.new_lead_id
                  join leads as ol on mp.original_lead_id = ol.lead_id
                  join sales as s on ol.lead_id = s.lead_id and s.product_id in (
                    select lp.lender_product_id
                    from lenders as ld
                    join lender_product as lp on ld.lender_id = lp.lender_id
                    where ld.partner_id = ?
                  )
                  join lead_associated_data lad on lad.lead_id = l.lead_id
                  join deals as d on s.sale_id = d.sale_id
                  join ( select max(deal_id) as deal_id from deals group by sale_id ) as d2 on d.deal_id = d2.deal_id
                  join lend_statuses as lst on d.status_id = lst.lend_status_id
                  join lead_owners lo on lo.lead_id = l.lead_id and lo.point_of_contact =1
                  left join lead_associated_data ollad on ollad.lead_id = ol.lead_id
                  left join lender_product lp on lp.lender_product_id = s.product_id
                  left join lenders len ON (len.lender_id = lp.lender_id)
                  left join intermediary_lenders il ON (il.id = len.intermediary_lender_id)
                  left join partner_product_types ppt on l.product_type_id=ppt.product_type_id
                  left join lend_statuses st on l.partner_status_id=st.lend_status_id
                  left join lead_notes ln ON ln.note_id = lad.max_note_id
                  left join lead_notes ln2 ON ln2.note_id = lad.max_intermediary_note_id
                  left join partner_lead_history as lh
                              ON lh.lead_id = l.lead_id AND lh.partner_id = l.partner_id AND lh.history_detail = 'Lead Added' AND
                                  lh.partner_user_id IS NOT null
                  left join partner_users as pu ON lh.partner_user_id = pu.partner_user_id
                  left JOIN partner_lead_history plh2 ON plh2.history_id = ollad.max_history_id
                  left JOIN partner_users as pu2 ON plh2.partner_user_id = pu2.partner_user_id
                  left JOIN partner_users as pu3 ON ol.partner_id = pu3.partner_id AND pu3.account_admin = 1
                  left join lead_notes ln3 on ln3.note_id = ollad.max_intermediary_note_id
                  where l.partner_id = ?
                    and lst.group_name not in ('Rejected', 'Settled')
                    and st.group_name not in  ('Rejected', 'Settled')
                    and l.is_closed = 0
                    $whereQuery";

    if ($order) {
      $orderBy = [];
      foreach ($order as $column => $direction) {
          $orderBy[] = "$column " . (strtolower($direction) === 'asc' ? 'ASC' : 'DESC');
      }
      $sql .= " ORDER BY " . implode(', ', $orderBy);
  } else {
      $sql .= " ORDER BY l.created DESC"; 
  }

  if ($limit && !$onlyCount) {
      $sql .= " LIMIT " . intval($limit['limit']) . " OFFSET " . intval($limit['offset']);
  }
  $data = $this->ReaderDB->execute($sql, [$partnerId, $partnerId])->fetchAll('assoc');

  if ($returnTotalCount) {
      $countSql = "SELECT COUNT(*) as count FROM ($sql) as t";
      $total = $this->ReaderDB->execute($countSql, [$partnerId, $partnerId])->fetchAll('assoc');
      return ['data' => $data, 'total' => $total];
  }
  return $data;
  }





    /*
    @param: $partnerUserId - partner user
    @$fromDate: dates range starts
    $endDate: dates range ends
    $partnerUserIdPassedIn: `partner_user_leads`.`partner_user_id`
    return: total or rows
     */
    public function getLeadsTotalByPartner($partnerId, $fromDate, $endDate, $type = 'total', $partnerUserIdPassedIn = null, $showArchived = false) {
      //initialize return variable
      $cachedKey = 'getLeadsTotalByPartner-'.$type.'-start-'.$fromDate.'-end-'.$endDate;
      if (false !== ($ret = Cache::read($cachedKey, 'short'))) return $ret;

      if (strlen($fromDate) === 10)
        $fromDate .= ' 00:00:00';
      if (strlen($endDate) === 10)
        $endDate .= ' 23:59:59';

      $rowsSubWhere = "";
      if (!empty($partnerUserIdPassedIn)) {
        $rowsSubWhere = " and lead_id in (select lead_id from partner_user_leads where partner_user_id = '$partnerUserIdPassedIn' and status='ACCESS') ";
      }

      $searchParams = [$partnerId, $fromDate, $endDate];
      if($showArchived !== false) {
        $archiveWhere = ' and is_archived <= ? ';
        $searchParams[] = !empty($showArchived) ? '1':'0';
      } else {
        $archiveWhere = ' AND is_archived = "0" ';
      }

      if ($type === 'total') { //nomally fetching data for dashboard
        //get leads, except 'no lenders found' (partner_status_id 4)
        $rows = $this->DB->execute("SELECT count(*) as cnt, partner_status_id
                  FROM leads
                  WHERE partner_id = ?
                  and created >= ?
                  and created <= ?
                  and partner_status_id != 4
                  $archiveWhere
                  $rowsSubWhere
                  group by partner_status_id",
                  $searchParams)->fetchAll('assoc');
        // id => groupname
        $groupNameList = LendStatus::getPartnerStatuses(null, 'groupNameList');

        //construct array for return
        $ret = array('total' => 0);
        foreach ($groupNameList as $k => $v) {
          if (!array_key_exists($v, $ret))
            $ret[$v] = 0;
        }

        //calculation
        if(!empty($rows)){
          foreach ($rows as $row) {
            if ($row['cnt']) {
              $ret['total'] += intval($row['cnt']);
              $ret[$groupNameList[$row['partner_status_id']]] += intval($row['cnt']);
            }
          }
        }

        //do Settled specially HERE
        if ($ret['Settled'] > 0) {
          $ret['total'] -= $ret['Settled'];
          $ret['Settled'] = 0;
        }
        //get settled count from partner_commissions table now
        $sql = 'SELECT count(*) as cnt FROM partner_commissions WHERE partner_id = ? AND funded_date >= ? AND funded_date <= ?';
        if (!empty($partnerUserIdPassedIn)) {
          $sql .= " AND lead_id in (SELECT lead_id FROM partner_user_leads WHERE partner_user_id = '$partnerUserIdPassedIn' AND status='ACCESS') ";
        }
        $rows = $this->DB->execute($sql, [$partnerId, $fromDate, $endDate]) -> fetchAll('assoc');

        if ($rows[0]['cnt']) {
          $ret['Settled'] = intval($rows[0]['cnt']);
          //no longer count Settled into Leads
          // $ret['total'] += $ret['Settled'];
        }

        $ret['Ready to Send'] = $this->getReadytosendCount( $partnerId, array('start_date'=>$fromDate, 'end_date'=>$endDate), $partnerUserIdPassedIn, !empty($showArchived) );

      } elseif ($type === 'metrics') {
        //construct the array for return
        $ret = array(
          'leads_per_day' => 0,
          'commission_per_day' => 0.00,
          'total_commission' => 0.00,
          'conversion_to_app' => 0,
          'conversion_to_settle' => 0,
          'fund_per_day' => 0.00
        );

        //change dates to db favor
        if (strlen($fromDate) === 10) $fromDate = $fromDate . ' 00:00:00';
        if (strlen($endDate) === 10) $endDate = $endDate . ' 23:59:59';

        //how many days between from date and end date
        $date1 = new \DateTime(Date('Y-m-d', strtotime($fromDate)));
        $date2 = new \DateTime(Date('Y-m-d', strtotime($endDate)));
        $days = $date2->diff($date1)->days;
        unset($date1, $date2);
        if ($days < 1) $days = 1; //for today or single day selected
        else $days += 1;  //we changed the options in view lend helper, so both start_date and end_date must be included

        if (empty($showArchived)) {
          //get leads
          $rows = $this->DB->execute("SELECT partner_status_id, count(*) as cnt
                    FROM leads
                    WHERE partner_id = ?
                    and created >= ?
                    and created <= ?
                    $rowsSubWhere
                    AND is_archived = '0'
                    group by partner_status_id",
                    [$partnerId, $fromDate, $endDate])->fetchAll('assoc');
        } else
          //get leads
          $rows = $this->DB->execute("SELECT partner_status_id, count(*) as cnt
                    FROM leads
                    WHERE partner_id = ?
                    and created >= ?
                    and created <= ?
                    $rowsSubWhere
                    group by partner_status_id",
                    [$partnerId, $fromDate, $endDate])->fetchAll('assoc');

        $partnerStatuses = LendStatus::getPartnerStatuses(null, 'pairedStatus');
        $applicationList = LendStatus::getPartnerStatuses(null, 'applicationList');
        $settlementList = LendStatus::getPartnerStatuses(null, 'settlementList');
        $groupNameList = LendStatus::getPartnerStatuses(null, 'groupNameList');

        //temp vars
        $apps = $settles = $total = 0;

        //calculation
        foreach ($rows as $row) {
          $total += intval($row['cnt']);
          if (empty($row['partner_status_id'])) continue;

          if (array_key_exists($row['partner_status_id'], $applicationList))
            $apps += intval($row['cnt']);

          if (array_key_exists($row['partner_status_id'], $settlementList))
            $settles += intval($row['cnt']);
        }
        //clear old-fashion Settles count
        if ($settles > 0) {
          $total -= $settles;
          $settles = 0;
        }

        //fetch funded leads Now
        // $fundedRow = $this->DB->execute("SELECT sum(sl.funded_amount) as funded_amount
        //           FROM leads l
        //           LEFT JOIN sales s
        //           ON s.lead_id = l.lead_id
        //           LEFT JOIN sale_outcomes sl
        //           ON (sl.sale_id = s.sale_id)
        //           WHERE l.partner_id = ?
        //           and l.partner_status_id in (" . implode(',', $settledStatusIds). ")
        //           and sl.funded_amount > 0
        //           and l.created >= ?
        //           and l.created <= ? ",
        //           [$partnerId, $fromDate, $endDate])->fetchAll('assoc');
        //use partner_commissions table instead of leads
        $fundedRow = $this->DB->execute("SELECT sum(funded_amount) as funded_amount, count(commission_id) as cnt, sum(commission) as commission
                   FROM partner_commissions
                   WHERE partner_id = ?
                   and funded_amount > 0
                   and funded_date >= ?
                   and funded_date <= ?
                   $rowsSubWhere
                   ",
                   [$partnerId, $fromDate, $endDate])->fetchAll('assoc');

        if (isset($fundedRow[0]['cnt'])) $settles = intval($fundedRow[0]['cnt']);
        $total += $settles;

        $ret['leads_per_day'] = round($total / $days);
        $ret['conversion_to_app'] = $total > 0 ? round(($apps + $settles) * 10000 / $total) /100 : 0;
        $ret['conversion_to_settle'] = $total > 0 ? round($settles * 10000 / $total) /100: 0;
        $ret['fund_per_day'] = isset($fundedRow[0]['funded_amount']) ? (float)$fundedRow[0]['funded_amount'] / $days : 0;
        $ret['commission_per_day'] = round($fundedRow[0]['commission'] * 100 / $days) /100;
        $ret['total_commission'] = $fundedRow[0]['commission'];
      }
      // Cache::write($cachedKey, $ret, 'short');
      return $ret;
    }

    /*
    @param: $partnerId - partner_id
    @param: $filters - array
    @param: $type - either string 'total' or 'list'
    return: array
    */
    public function settledLeadsByPartner($partnerId, $filters, $type = 'total', $partnerUserIdPassedIn = null, $showArchived = false) {
      $val = array($partnerId);
      $cachedKey = 'settledLeadsByPartner-'.$type.'-partner-'.$partnerId;

      $subWhere = "";
      if (!empty($partnerUserIdPassedIn)) {
        $subWhere = " AND pc.lead_id in (SELECT lead_id FROM partner_user_leads WHERE partner_user_id = '$partnerUserIdPassedIn' AND status='ACCESS') ";
      }

      if($showArchived !== false) {
        if($showArchived === 'countArchived') {
          $archiveWhere = ' AND l.is_archived = ? '; //we want the strict count of archived items
          $val[] = 1;
        }else{
          $archiveWhere = ' AND l.is_archived <= ? ';
          $val[] = $showArchived;
        }
      }else{
        $archiveWhere = '';
      }

      if ($type == 'total')
        $sql = 'SELECT count(pc.commission_id) as cnt
                  FROM partner_commissions pc LEFT JOIN leads l ON l.lead_id = pc.lead_id
                  WHERE pc.partner_id = ? ' . $archiveWhere;

      else  //$type == 'list'
        $sql = 'SELECT pc.*, l.lead_id, l.partner_status_id, l.created, l.last_changed_date, l.product_type_id,
                        l.abn, l.organisation_name, l.amount_requested, l.purpose_id, l.loan_term_requested, l.trading_since, l.industry_id, l.industry_detail, l.sales_monthly, l.sales_annual, l.b_address, l.company_registration_date,
                        lo.first_name, lo.last_name, lo.mobile, lo.email, lo.dob, lo.driving_licence_num, lo.home_owner, lo.home_owner_detail, lo.owner_type, lo.address,
                        so.sale_outcome_id, lp.product_name, lp.lender_product_id, len.shorthand, len.lender_name, len.lender_id, len.lender_logo,
                        pa.partner_alias_id, pa.company_name as alias_company_name,
                        sc.lend_score,
                        sc.serviceability,
                        ppt.product_type_name as product_type_name,
                        sub_ppt.product_type_name as sub_product_type_name

                  FROM partner_commissions pc LEFT JOIN leads l ON l.lead_id = pc.lead_id
                  LEFT JOIN sale_outcomes so ON pc.sale_outcome_id = so.sale_outcome_id
                  LEFT JOIN sales s ON s.sale_id = so.sale_id
                  LEFT JOIN lender_product lp ON lp.lender_product_id = s.product_id
                  LEFT JOIN lenders len ON len.lender_id = lp.lender_id
                  LEFT JOIN lead_owners lo ON ( lo.owner_id = (SELECT max(owner_id) FROM lead_owners WHERE lead_id = l.lead_id AND point_of_contact = 1))
                  LEFT JOIN lend_score as sc ON l.lead_id = sc.lead_id
                  LEFT JOIN partner_aliases as pa ON l.partner_alias_id = pa.partner_alias_id
                  LEFT JOIN partner_product_types as ppt ON ppt.product_type_id = l.product_type_id
                  LEFT JOIN partner_product_types as sub_ppt ON sub_ppt.product_type_id = ppt.sub_product
                  WHERE pc.partner_id = ? '. $archiveWhere;


      $sql .= $subWhere;
      if (isset($filters['start_date'])) {
        $sql .= 'AND pc.funded_date >= ? ';
        $val[] = strlen($filters['start_date']) == 10 ? $filters['start_date'].' 00:00:00' : $filters['start_date'];
        $cachedKey .= '-start-'.$filters['start_date'];
      }

      if (isset($filters['end_date'])) {
        $sql .= 'AND pc.funded_date <= ? ';
        $val[] = strlen($filters['end_date']) == 10 ? $filters['end_date'].' 23:59:59' : $filters['end_date'];
        $cachedKey .= '-end-'.$filters['end_date'];
      }

      //sortable column for list
      if ($type === 'list') {
        // array of field name => table alias
        $validColumns = array( 'organisation_name' => 'l', 'amount_requested' => 'l', 'first_name' => 'lo',
                          'funded_date' => 'pc', 'shorthand' => 'len', 'funded_amount' => 'pc',
                          'funded_type' => 'pc', 'commission' => 'pc', 'alias_company_name' => '',
                          'lend_score' => '',
                        );
        //set default sort column & direction && do validation here
        if (empty($filters['sort']) || !in_array($filters['sort'], array_keys($validColumns))) {
            $prefix = 'pc';
            $filters['sort'] = 'funded_date';
            $suffix = '';
        } elseif($filters['sort'] != 'product_type') { //we handle product_type as post-sort later
            //find the table alias as the prefix
            $prefix = $validColumns[$filters['sort']];
            $suffix = ', pc.funded_date desc';
        }
        if (empty($filters['direction']) || !in_array($filters['direction'], array('asc', 'desc'))) $filters['direction'] = 'desc';

        //add sort column & direction to query
        if (!empty($prefix) || !empty($suffix))
          $sql .= 'ORDER BY '.(!empty($prefix) ? $prefix."." : "").$filters['sort'].' '.$filters['direction'] . $suffix;

        $cachedKey .= '-sort-'.$filters['sort'].'-'.$filters['direction'];
      }

      if (false === ($rows = Cache::read($cachedKey, 'short'))) {
        $rows = $this->DB->execute($sql, $val)->fetchAll('assoc');
        // Cache::write($cachedKey, $rows, 'short');
      }

      if ($type == 'list') {
        $internalAuth = new LendInternalAuth;
        foreach ($rows as $key => $lead) {
          $rows[$key]['hashed_lead_id'] = $internalAuth->hashLeadId($lead['lead_id']);
          $rows[$key]['product_type'] = !empty($lead['sub_product_type_name'])
                  ? $lead['sub_product_type_name'] . ' (' .$lead['product_type_name']. ')'
                  : (
                    !empty($lead['product_type_name']) ? $lead['product_type_name']
                      : '-'
                  );
        }
        //post-sort section - field: product_type
        if (!empty($filters['sort']) && $filters['sort'] == 'product_type') {
          if (!empty($filters['direction']) && $filters['direction'] == 'asc') {
            usort($rows, function($a, $b) {return strcmp($a['product_type'], $b['product_type']);});
          } else {
            usort($rows, function($a, $b) {return (-1) * strcmp($a['product_type'], $b['product_type']);});
          }
        }
      }

      return $rows;
    }

    /*
    @params: $leads - array of leads
    @params: $params - array
    return: array of combined $leads and $viewFilters
    */
    public function getAvailableFiltersByLeads($leads, $params)
    {
      if (! count($leads)) return array($leads, $params);

      if (!empty($params['status']) && $params['status'] === 'Leads') {
        $groupStatuses = LendStatus::getPartnerStatuses($params['status'], 'pairedLeadsStatus');
      } else {
        $groupStatuses = LendStatus::getPartnerStatuses($params['status'], 'pairedStatus');
      }
      if (!array_key_exists('filterStatuses', $params))
        $params['filterStatuses'] = array();
      if (!array_key_exists('filterType', $params))
        $params['filterType'] = '';
      if (!array_key_exists('filterLenders', $params))
        $params['filterLenders'] = array();

      if (array_key_exists('filterStatuses', $params)) {
        $params['filterStatusesChecked'] = !empty($params['filterStatuses']) ? explode('-', $params['filterStatuses']) : array();
        $params['filterStatuses'] = array();
      } else {
        $params['filterStatuses'] = array();
      }
      if (array_key_exists('filterLenders', $params)) {
        $params['filterLendersChecked'] = !empty($params['filterLenders']) ? explode('-', $params['filterLenders']) : array();
        $params['filterLenders'] = array();
      } else {
        $params['filterLenders'] = array();
      }
      if (array_key_exists('filterAliases', $params)) {
        $params['filterAliasesChecked'] = (isset($params['filterAliases']) && is_numeric($params['filterAliases'])) ? explode('-', $params['filterAliases']) : array();
        $params['filterAliases'] = array();
      } else {
        $params['filterAliases'] = array();
      }

      //construct new array for
      $params['leadsCounter'] = array('Type'=> array(), 'Status' => array(), 'Lender'=> array(), 'Alias'=> array());
      //filter options
      foreach ($leads as $key => $lead) {
        //remember lender where possible
        if (!empty($lead['lender_id']) &&
            (empty($params['filterLenders']) || !in_array($lead['shorthand'], $params['filterLenders'])) )
          $params['filterLenders'][$lead['lender_id']] = $lead['shorthand'];

        //remember status where possible
        if ((empty($params['filterStatuses']) || !array_key_exists($lead['partner_status_id'], $params['filterStatuses'])) && !empty($groupStatuses[$lead['partner_status_id']]))
          $params['filterStatuses'][$lead['partner_status_id']] = $groupStatuses[$lead['partner_status_id']];

        //remember alias where possible
        $partner_alias_id = !empty($lead['partner_alias_id']) ? $lead['partner_alias_id'] : 0;
        if (empty($params['filterAliases']) || !array_key_exists($partner_alias_id, $params['filterAliases']))
          $params['filterAliases'][$partner_alias_id] = $lead['alias_company_name'];

        //counter begins just right now
        if (!empty($lead['application_type'])) {
          if (!array_key_exists($lead['application_type'], $params['leadsCounter']['Type']))
            $params['leadsCounter']['Type'][$lead['application_type']] = 1;
          else
            $params['leadsCounter']['Type'][$lead['application_type']] += 1;
        }elseif (!empty($lead['funded_type'])) {
          if (!array_key_exists($lead['funded_type'], $params['leadsCounter']['Type']))
            $params['leadsCounter']['Type'][$lead['funded_type']] = 1;
          else
            $params['leadsCounter']['Type'][$lead['funded_type']] += 1;
        }

        if (!empty($lead['partner_status_id'])):
          if (!array_key_exists($lead['partner_status_id'], $params['leadsCounter']['Status']))
            $params['leadsCounter']['Status'][$lead['partner_status_id']] = 1;
          else
            $params['leadsCounter']['Status'][$lead['partner_status_id']] += 1;
        endif;

        if (!empty($lead['lender_id'])):
          if (!array_key_exists($lead['lender_id'], $params['leadsCounter']['Lender']))
            $params['leadsCounter']['Lender'][$lead['lender_id']] = 1;
          else
            $params['leadsCounter']['Lender'][$lead['lender_id']] += 1;
        endif;

        if (!array_key_exists($partner_alias_id, $params['leadsCounter']['Alias']))
          $params['leadsCounter']['Alias'][$partner_alias_id] = 1;
        else
          $params['leadsCounter']['Alias'][$partner_alias_id] += 1;
        //end of counter

        // search text
        if (isset($params['search']) AND strlen($params['search']) > 0 ) { // This condition is valid for 0 (digit)
          //search fields
          $searchFields = array('first_name', 'last_name', 'organisation_name', 'hashed_lead_id', 'phone', 'mobile', 'email');

          $searches = [];
          if (strpos($params['search'], ' ') !== false) {
            $searches[] = str_replace(' ', '', $params['search']);
          }
          // Search combined search word first if exists, then search each word.
          $searches = array_merge($searches, explode(' ', $params['search']));

          // Do matching
          $matched = false;
          foreach ($searchFields as $field) {
            foreach ($searches as $search) {
              $matched = stripos($lead[$field], $search) !== false;
              if ($matched) {

                // Found that Text::highlight method doesn't highlight 0 if it is only 0 in $search.
                $leads[$key]['matched_value'] = Text::highlight($lead[$field], $search, ['format' => '<span class="bg-yellow">\1</span>', 'html' => true]);
                break;
              }
            }
            if ($matched) break;
          }

          //something not matched
          if (!$matched) {
            unset($leads[$key]);
            continue;
          }
        }

        //checkbox for statuses
        if (!empty($params['filterStatusesChecked']) && !in_array($lead['partner_status_id'], $params['filterStatusesChecked'])) {
          unset($leads[$key]);
          continue;
        }

        //checkbox for lenders
        if (!empty($params['filterLendersChecked']) && !in_array($lead['lender_id'], $params['filterLendersChecked'])) {
          unset($leads[$key]);
          continue;
        }

        //checkbox for aliases
        if (!empty($params['filterAliasesChecked']) && !in_array($partner_alias_id, $params['filterAliasesChecked'])) {
          unset($leads[$key]);
          continue;
        }

        //radio for type
        if (!empty($params['filterType']) && in_array($params['filterType'], array('New', 'Refinance'))) {
          //'Settled' or other status
          if(!empty($lead['funded_type']) && $lead['funded_type'] != $params['filterType']) {
            unset($leads[$key]);
            continue;
          } elseif(!empty($lead['application_type']) && $lead['application_type'] != $params['filterType']) {
            unset($leads[$key]);
            continue;
          }
        }

      }

      if(count($params['leadsCounter']['Alias'])===1 && isset($params['leadsCounter']['Alias'][0])){
        unset($params['leadsCounter']['Alias'], $params['filterAliases'], $params['filterAliasesChecked']);
      }

      // if (empty($params['filterStatusesChecked'])) {
      //   $params['filterStatusesChecked'] = array_keys($params['filterStatuses']);
      // }
      // if (empty($params['filterLendersChecked'])) {
      //   $params['filterLendersChecked'] = array_keys($params['filterLenders']);
      // }

      return array($leads, $params);
    }

    /*
    check and do something if similiar existing lead found
    */
    public function checkExistsLead($data, $newLeadId = false)
    {
      // We no longer need the logic below. Refer to PLAT-4651 for details.
      return true;
      if (empty($data['lead']['lead_id']) && !empty($newLeadId))
        $data['lead']['lead_id'] = $newLeadId;
      try {
        $isNewLendLead = (empty($data['lead']['partner_id'])
                || in_array($data['lead']['partner_id'], array('1', '3', '4', '8', '54', '64')))
                ? true : false ;

        $attemptingAndInProgressStatuses = LendStatus::getPartnerStatuses('keys', 'AttemptingAndInProgress');
        $rejectedStatuses = LendStatus::getPartnerStatuses('Rejected', 'groupName');
        $settledStatuses = LendStatus::getPartnerStatuses('Settled', 'groupName');

        if ($isNewLendLead) {
          //case 13: Skip (Lend's own - NULL, 1,3,4) -
          //         Do not make a new Lend lead Partner Honoured
          //         if original lead is also from Lend (Null, 1,3,4)
          // $conditions = " ( ( l.partner_id is null ) or (l.partner_id = '1'
          //                 or l.partner_id = '3' or l.partner_id = '4') ) ";
          // $existingLead = $this->__findExistingLead($data, $conditions, true, false)
          //                 || $this->__findExistingLead($data, $conditions, false, false);
          // if (!empty($existingLead))
            throw new \Exception("Lend");
            //skip
        }

        //case 1: Originated from lend and status of
        //        originating lead is Attempting, In Progress and < x days ago
        $conditions = " ( ( l.partner_id is null ) or ((l.partner_id = '1'
                        or l.partner_id = '3' or l.partner_id = '4'
                        or l.partner_id = '8' or l.partner_id = '54'
                        or l.partner_id = '64' )
                        and l.partner_status_id in ("
                        . implode(',', $attemptingAndInProgressStatuses)
                        . ") ) )
                        AND l.status_id = 17 ";
        $createdLessThan = true;
        $existingLead = $this->__findExistingLead($data, $conditions, $createdLessThan, false);
        if (!empty($existingLead)) {
          //  1. Mark as Partner Honoured
          $this->__partnerHonouredOption1($existingLead, $data);
          throw new \Exception('case 1');  // no need to go further
        }

        //case 2: Originated from lend and status of
        //       originating lead is Attempting, In Progress and > x days ago
        $conditions = " ( ( l.partner_id is null ) or ((l.partner_id = '1'
                        or l.partner_id = '3' or l.partner_id = '4'
                        or l.partner_id = '8' or l.partner_id = '54'
                        or l.partner_id = '64' )
                        and l.partner_status_id in ("
                        . implode(',', $attemptingAndInProgressStatuses)
                        . ") ) ) ";
        $createdLessThan = false;
        $existingLead = $this->__findExistingLead($data, $conditions, $createdLessThan, false);
        if (!empty($existingLead)) {
          //	2. Mark as Dupe and try to send to next
          $this->__partnerHonouredOption2($existingLead, $data, 'case 2');
          throw new \Exception('case 2');  // no need to go further
        }

        //case 3: Originated from lend and status of
        //        originating lead is Rejected and < x days ago
        $conditions = " ( ( l.partner_id is null ) or ((l.partner_id = '1'
                        or l.partner_id = '3' or l.partner_id = '4'
                        or l.partner_id = '8' or l.partner_id = '54'
                        or l.partner_id = '64' )
                        and l.partner_status_id in ("
                        . implode(',', $rejectedStatuses)
                        . ") ) ) ";
        $createdLessThan = true;
        $existingLead = $this->__findExistingLead($data, $conditions, $createdLessThan, false);
        if (!empty($existingLead)) {
          //	3. Try to send to same lender if lender is NOT Capify
          $this->__partnerHonouredOption3($existingLead, $data, 'case 3');
          throw new \Exception('case 3');  // no need to go further
        }

        //case 4:  Originated from lend and status of
        //         originating lead is Rejected and > x days ago
        $conditions = " ( ( l.partner_id is null ) or ((l.partner_id = '1'
                        or l.partner_id = '3' or l.partner_id = '4'
                        or l.partner_id = '8' or l.partner_id = '54'
                        or l.partner_id = '64' )
                        and l.partner_status_id in ("
                        . implode(',', $rejectedStatuses)
                        . ") ) )";
        $createdLessThan = false;
        $existingLead = $this->__findExistingLead($data, $conditions, $createdLessThan, false);
        if (!empty($existingLead)) {
          //	3. Try to send to same lender if lender is NOT Capify
          $this->__partnerHonouredOption3($existingLead, $data, 'case 4');
          throw new \Exception('case 4');  // no need to go further
        }

        //case 5: Originated from lend and status of
        //        originating lead is Settled and < x days ago
        $conditions = " ( ( l.partner_id is null ) or ((l.partner_id = '1'
                        or l.partner_id = '3' or l.partner_id = '4'
                        or l.partner_id = '8' or l.partner_id = '54'
                        or l.partner_id = '64' )
                        and l.partner_status_id in ("
                        . implode(',', $settledStatuses)
                        . ") ) ) ";
        $createdLessThan = true;
        $existingLead = $this->__findExistingLead($data, $conditions, $createdLessThan, false);
        if (!empty($existingLead)) {
          //	3. Try to send to same lender if lender is NOT Capify
          $this->__partnerHonouredOption3($existingLead, $data, 'case 5');
          throw new \Exception('case 5');  // no need to go further
        }

        //case 6: Originated from lend and status of
        //        originating lead is Settled and > x days ago
        $conditions = " ( ( l.partner_id is null ) or ((l.partner_id = '1'
                        or l.partner_id = '3' or l.partner_id = '4'
                        or l.partner_id = '8' or l.partner_id = '54'
                        or l.partner_id = '64' )
                        and l.partner_status_id in ("
                        . implode(',', $settledStatuses)
                        . ") ) ) ";
        $createdLessThan = false;
        $existingLead = $this->__findExistingLead($data, $conditions, $createdLessThan, false);
        if (!empty($existingLead)) {
          //	3. Try to send to same lender if lender is NOT Capify
          $this->__partnerHonouredOption3($existingLead, $data, 'case 6');
          throw new \Exception('case 6');  // no need to go further
        }

        //case 7: Originated from other partner and status of
        //        originating lead is Attempting, In Progress and < x days ago
        $conditions = " ( l.partner_id is not null and l.partner_id <> '1'
                        and l.partner_id <> '3' and l.partner_id <> '4'
                        and l.partner_id <> '8' and l.partner_id <> '54'
                        and l.partner_id <> '64'
                        and l.partner_id <> '" . $data['lead']['partner_id'] . "'
                        and l.partner_status_id in ("
                        . implode(',', $attemptingAndInProgressStatuses)
                        . ")  ) ";
        $createdLessThan = true;
        $existingLead = $this->__findExistingLead($data, $conditions, $createdLessThan, false);
        if (!empty($existingLead)) {
          //	2. Mark as Dupe and try to send to next
          $this->__partnerHonouredOption2($existingLead, $data, 'case 7');
          throw new \Exception('case 7');  // no need to go further
        }

        //case 8: Originated from other partner and status of
        //        originating lead is Attempting, In Progress and > x days ago
        $conditions = " ( l.partner_id is not null and l.partner_id <> '1'
                        and l.partner_id <> '3' and l.partner_id <> '4'
                        and l.partner_id <> '8' and l.partner_id <> '54'
                        and l.partner_id <> '64'
                        and l.partner_id <> '" . $data['lead']['partner_id'] . "'
                        and l.partner_status_id in ("
                        . implode(',', $attemptingAndInProgressStatuses)
                        . ")  ) ";
        $createdLessThan = false;
        $existingLead = $this->__findExistingLead($data, $conditions, $createdLessThan, false);
        if (!empty($existingLead)) {
          //	2. Mark as Dupe and try to send to next
          $this->__partnerHonouredOption2($existingLead, $data, 'case 8');
          throw new \Exception('case 8');  // no need to go further
        }

        //case 9: Originated from other partner and status of
        //        originating lead is Rejected and < x days ago
        $conditions = " ( l.partner_id is not null and l.partner_id <> '1'
                        and l.partner_id <> '3' and l.partner_id <> '4'
                        and l.partner_id <> '8' and l.partner_id <> '54'
                        and l.partner_id <> '64'
                        and l.partner_id <> '" . $data['lead']['partner_id'] . "'
                        and l.partner_status_id in ("
                        . implode(',', $rejectedStatuses)
                        . ")  ) ";
        $createdLessThan = true;
        $existingLead = $this->__findExistingLead($data, $conditions, $createdLessThan, false);
        if (!empty($existingLead)) {
          //	2. Mark as Dupe and try to send to next
          $this->__partnerHonouredOption2($existingLead, $data, 'case 9');
          throw new \Exception('case 9');  // no need to go further
        }

        //case 10: Originated from other partner and status of
        //        originating lead is Rejected and > x days ago
        $conditions = " ( l.partner_id is not null and l.partner_id <> '1'
                        and l.partner_id <> '3' and l.partner_id <> '4'
                        and l.partner_id <> '8' and l.partner_id <> '54'
                        and l.partner_id <> '64'
                        and l.partner_id <> '" . $data['lead']['partner_id'] . "'
                        and l.partner_status_id in ("
                        . implode(',', $rejectedStatuses)
                        . ")  ) ";
        $createdLessThan = false;
        $existingLead = $this->__findExistingLead($data, $conditions, $createdLessThan, false);
        if (!empty($existingLead)) {
          //	2. Mark as Dupe and try to send to next
          $this->__partnerHonouredOption3($existingLead, $data, 'case 10');
          throw new \Exception('case 10');  // no need to go further
        }

        //case 11: Originated from other partner and status of
        //        originating lead is Settled and < x days ago
        $conditions = " ( l.partner_id is not null and l.partner_id <> '1'
                        and l.partner_id <> '3' and l.partner_id <> '4'
                        and l.partner_id <> '8'  and l.partner_id <> '54'
                        and l.partner_id <> '64'
                        and l.partner_id <> '" . $data['lead']['partner_id'] . "'
                        and l.partner_status_id in ("
                        . implode(',', $settledStatuses)
                        . ")  ) ";
        $createdLessThan = true;
        $existingLead = $this->__findExistingLead($data, $conditions, $createdLessThan, false);
        if (!empty($existingLead)) {
          //	2. Mark as Dupe and try to send to next
          $this->__partnerHonouredOption2($existingLead, $data, 'case 11');
          throw new \Exception('case 11');  // no need to go further
        }

        //case 12: Originated from other partner and status of
        //        originating lead is Settled and > x days ago
        $conditions = " ( l.partner_id is not null and l.partner_id <> '1'
                        and l.partner_id <> '3' and l.partner_id <> '4'
                        and l.partner_id <> '8'  and l.partner_id <> '54'
                        and l.partner_id <> '64'
                        and l.partner_id <> '" . $data['lead']['partner_id'] . "'
                        and l.partner_status_id in ("
                        . implode(',', $settledStatuses)
                        . ")  ) ";
        $createdLessThan = false;
        $existingLead = $this->__findExistingLead($data, $conditions, $createdLessThan, false);
        if (!empty($existingLead)) {
          //	2. Mark as Dupe and try to send to next
          $this->__partnerHonouredOption3($existingLead, $data, 'case 12');
          throw new \Exception('case 12');  // no need to go further
        }

        //case 13: doing something on application project, not here

        //case 14: Prevent broker adding the same lead, get them to contact us
        $conditions = " ( l.partner_id = '" . $data['lead']['partner_id'] . "' ) ";
        $createdLessThan = false;
        $existingLead = $this->__findExistingLead($data, $conditions, $createdLessThan, false);
        if (!empty($existingLead)) {
          //same partner same lead, contact us
          throw new \Exception('case 14');  // no need to go further
        }
        return true;
      } catch (\Exception $e) {
        $error = $e->getMessage();
        return stripos($error, 'case ') !== false ? true : $error;
      }
    }

    //partner honoured related option 1
    //Mark as Partner Honoured
    private function __partnerHonouredOption1($existingLead, $newLead)
    {
      $partnerHonouredTable = TableRegistry::get('PartnerHonoured');
      $conditions = array(
                        'lend_lead_id' => $existingLead['lead_id'],
                        'partner_lead_id' => $newLead['lead']['lead_id']
                        );
      if (! $partnerHonouredTable->existingRecord($conditions)) {
        $partnerHonouredTable->addPartnerHonoured(
                            array('lend_lead_id' => $existingLead['lead_id'],
                                'partner_lead_id' => $newLead['lead']['lead_id'])
                            );
      }
      $this->updateLead(array('lead_id' => $newLead['lead']['lead_id'],
                              'partner_status_id' => 2) );
    }

    //partner honoured related option 2
    //Mark as Dupe and try to send to next
    private function __partnerHonouredOption2($existingLead, $newLead, $note = '')
    {
      $partnerDuplicateLeadsTable = TableRegistry::get('PartnerDuplicateLeads');
      $conditions = array(
                        'existing_lead_id' => $existingLead['lead_id'],
                        'partner_lead_id' => $newLead['lead']['lead_id']
                      );
      if (!$partnerDuplicateLeadsTable->existingRecord($conditions)) {
        $partnerDuplicateLeadsTable->addNew(array(
          'existing_lead_id' => $existingLead['lead_id'],
          'partner_lead_id' => $newLead['lead']['lead_id'],
          'action' => 'option2',
          'note' => $note,
        ));
      }
    }

    //partner honoured related option 3
    //Try to send to same lender if lender is NOT Capify
    private function __partnerHonouredOption3($existingLead, $newLead, $note = '')
    {
      //did something in SendToLender.php => hasThisBeenSentToThisLenderBefore()
      $partnerDuplicateLeadsTable = TableRegistry::get('PartnerDuplicateLeads');
      $conditions = array(
                        'existing_lead_id' => $existingLead['lead_id'],
                        'partner_lead_id' => $newLead['lead']['lead_id']
                      );
      if (!$partnerDuplicateLeadsTable->existingRecord($conditions)) {
        $partnerDuplicateLeadsTable->addNew(array(
          'existing_lead_id' => $existingLead['lead_id'],
          'partner_lead_id' => $newLead['lead']['lead_id'],
          'action' => 'option3',
          'note' => $note,
        ));
      }
    }

    //partner honoured related option 4
    //Mark as Dupe and try to send to next lender if lender IS Capify
    private function __partnerHonouredOption4($existingLead, $newLead, $note = '')
    {
      //did something in SendToLender.php => hasThisBeenSentToThisLenderBefore()
      $partnerDuplicateLeadsTable = TableRegistry::get('PartnerDuplicateLeads');
      $conditions = array(
                        'existing_lead_id' => $existingLead['lead_id'],
                        'partner_lead_id' => $newLead['lead']['lead_id']
                      );
      if (!$partnerDuplicateLeadsTable->existingRecord($conditions)) {
        $partnerDuplicateLeadsTable->addNew(array(
          'existing_lead_id' => $existingLead['lead_id'],
          'partner_lead_id' => $newLead['lead']['lead_id'],
          'action' => 'option4',
          'note' => $note,
        ));
      }
    }

    // @param: @data - array of lead and lead_owner
    // @param: $conditions - '' will not be actually null, '1 = 1' means null actually
    // @param: $funded - boolean, true by default
    // @return: the lead with highest socre
    private function __findExistingLead($data, $conditions = '', $createdLessThan = true, $funded = true, $passMark = 10, $excludes = [])
    {
      $partnerHonouredLookup = Configure::read('Lend.partner_honoured_lookup');
      if (!empty($excludes)) {
        foreach ($excludes as $table => $fields) {
          if (!empty($partnerHonouredLookup['scores'][$table])) {
            foreach ($fields as $f) {
              if (!empty($partnerHonouredLookup['scores'][$table][$f])) {
                unset($partnerHonouredLookup['scores'][$table][$f]);
              }
            }
          }
        }
      }
      $aliasTable = array('lead_owner' => 'lo', 'lead' => 'l'); //consistent with $sql later
      $scoreFieldArray = array();
      foreach ($partnerHonouredLookup['scores'] as $tableName => $tableFieldList) {
        foreach ($tableFieldList as $field => $score) {
          //only look at lead & lead_owner currently
          if ($field === 'owner_name') {
            if (empty($data[$tableName][$field])
              && isset($data[$tableName]['first_name'], $data[$tableName]['last_name'])) {
                $data[$tableName][$field] = $data[$tableName]['first_name'] . ' '
                                            .$data[$tableName]['last_name'];
                $scoreFieldArray[] = 'IF(concat(`' . $aliasTable[$tableName] . '`.`first_name`, " ",
                                                `' . $aliasTable[$tableName] . '`.`last_name`) REGEXP "^'
                                    . trim(preg_replace('/[^\w\s]/','',$data[$tableName][$field])) .'$", ' . $score . ', 0)';
                continue; //go for next
              }
          }
          //fields other than 'owner_name'
          if (empty($data[$tableName][$field]) || empty($score)) continue;

          $scoreFieldArray[] = 'IF(`' . $aliasTable[$tableName] . '`.`' . $field . '` REGEXP "^'
                              . trim(preg_replace('/[^\w\s]/','',$data[$tableName][$field])) .'$", ' . $score . ', 0)';
        }
      }
      unset($field, $score, $aliasField, $value);

      if (empty($scoreFieldArray)) return false; //no needs to go further

      $createdDate = date('Y-m-d 00:00:00', strtotime('-' . $partnerHonouredLookup['days'] . ' days'));
      if (empty($conditions))   // put something in conditions if '' is passed in
        $conditions = " l.partner_id IS NULL AND l.status_id = 17 ";
      if (!empty($data['lead']['lead_id']))
        $conditions .= " AND l.lead_id <> " . $data['lead']['lead_id'] . " ";
      if (!empty($data['lead']['product_type_id']))
        $conditions .= " AND l.product_type_id = " . $data['lead']['product_type_id'] . " ";

      $sql = "SELECT lo.first_name, lo.last_name, lo.phone, lo.mobile, lo.email,
                l.lead_id, l.abn, l.acn, l.organisation_name, l.business_name,
                l.partner_id, po.partner_type, SUM(so.funded_amount) as funded_amount, "
              . implode(' + ', $scoreFieldArray) . ' as score '
              . " FROM leads AS l
              JOIN lead_owners AS lo ON l.lead_id = lo.lead_id
              LEFT OUTER JOIN sales AS s ON l.lead_id = s.lead_id
              LEFT OUTER JOIN sale_outcomes AS so ON s.sale_id = so.sale_id
              LEFT JOIN partners AS po ON po.partner_id = l.partner_id
              WHERE  l.created " . (!$createdLessThan ? "< " : ">= ") . " ?
              AND " . $conditions . "
              GROUP BY l.lead_id ";
      if ($funded) $sql .= " HAVING funded_amount IS NULL";
      $sql .= " ORDER BY score DESC ";
      $lead = $this->DB->execute($sql, [$createdDate])
                      ->fetch('assoc');

      return !empty($lead['score']) && $lead['score'] >= $passMark ? $lead : false;
    }

    /*
    check if a partner is trying to create or update same lead in last x days
    */
    public function partnerSameOwnerLead($data, $partnerId, $excludes = [])
    {
      if (!array_key_exists('lead_owner', $data))
        throw new \Exception("Missing LeadOwner");
      if (!array_key_exists('lead', $data))
        throw new \Exception("Missing Lead");

      //if it's Lend partner_id, then return false all times
      if (in_array($partnerId, Configure::read('Lend.LEND_PARTNER_IDS')))
        return false;

      if($data['lead']['is_archived'] == 1 || $data['lead']['is_closed'] == 1){
        return false;
      }

      $conditions = [];
      $conditions[] = "l.partner_id = '$partnerId'";

      // All asset finance product types are allow to dupe leads
      $asset_finances = TableRegistry::get('PartnerProductTypes')->getAllAssetFinance();
      if(!empty($asset_finances)){
        $conditions[] = "l.product_type_id NOT IN (".(implode(',', $asset_finances)).")";
      }

      $conditions = " (".implode(" AND ", $conditions).") ";

      $createdLessThan = true;
      $funded = false;
      $mark = 10;
      return $this->__findExistingLead($data, $conditions, $createdLessThan, $funded, $mark, $excludes);

    }

    public function highlightSearchString($partnerLeads, $search){
      $keywords = explode(' ', strtolower($search));
      $patterns = array();
      $replace = array();
      foreach($keywords as $keyword){
        $patterns[] = "/$keyword/i";
        $replace[] = "@#$-%^&$keyword&^%-$#@";
      }
      $fields = array('first_name', 'last_name', 'organisation_name', 'email', 'phone', 'mobile');
      foreach($partnerLeads as $key=>$lead){
        foreach($fields as $f){
          $lead[$f] = preg_replace($patterns, $replace, $lead[$f]);
          $partnerLeads[$key][$f] = preg_replace(array('/\@\#\$\-\%\^\&/', '/\&\^\%\-\$\#\@/'), array("<i class=\"bg-green\">", "</i>"), $lead[$f]);
        }
      }
      return $partnerLeads;
    }

    public function getReadytosendCount($partnerId, $params = [], $partnerUserIdPassedIn = null, $includeArchived = true) {
      $query = ['partner_id' => $partnerId, 'partner_status_id' => 55];
      if (!empty($params['start_date']))
        $query['created >='] = substr($params['start_date'], 0, 10) .' 00:00:00';
      if (!empty($params['end_date']))
        $query['created <='] = substr($params['end_date'], 0, 10) .' 00:00:00';
      $q = $this->getSelectQuery('leads', $query, false);
      if (!empty($partnerUserIdPassedIn)) {
        if ($includeArchived)
          $q['query'] .= " AND lead_id in (SELECT lead_id FROM partner_user_leads WHERE partner_user_id = '$partnerUserIdPassedIn' AND status='ACCESS') ";
        else
          $q['query'] .= " AND lead_id in (SELECT lead_id FROM partner_user_leads WHERE partner_user_id = '$partnerUserIdPassedIn' AND status='ACCESS' and is_archived = 0) ";
      } elseif (!$includeArchived) {
          $q['query'] .= " AND is_archived = 0";
      }
      $leads = $this->DB->execute($q['query'], $q['values'])->fetchAll('assoc');

      //something needs to check in lead_send_prevention table
      if (count($leads)) {
        $leadIds = array_column($leads, 'lead_id');
        $preventionLeads = $this->DB->execute('SELECT lead_id
                        FROM lead_send_prevention
                        WHERE lead_id in ('. implode(',', $leadIds).') ',[] )->fetchAll('assoc');
        foreach ($preventionLeads as $row) {
          if (($key = array_search($row['lead_id'], $leadIds)) !== false) {
            unset($leadIds[$key]);
          }
        }
      } else {
        $leadIds = [];
      }
      return count($leadIds);
    }

    public function getPartnerUsers($leadId) {
      if (!$leadId) return false;

      $sql = 'SELECT pu.* FROM leads l, partner_users pu WHERE l.partner_id = pu.partner_id AND l.lead_id = ?';
      $ret = $this->DB->execute($sql, [$leadId])->fetchAll('assoc');

      return !empty($ret) ? $ret : false;
    }

    public function isLeadPartnerHonoured($leadId)
		{
      
			$sql = 'SELECT l.lead_id, l.partner_id, ph.lend_lead_id
							FROM partner_honoured ph
							LEFT JOIN leads l
							ON l.lead_id = ph.partner_lead_id
							WHERE ph.partner_lead_id = ? ';
			$ret = $this->DB->execute($sql, [$leadId])->fetchAll('assoc');

			return !empty($ret) ? $ret : false;
		}

    /* remove partner_lead_id from partner_honoured table where possible */
		public function removeFromPartnerHonouredWherePossbile($leadId)
		{
			$phs = $this->isLeadPartnerHonoured($leadId);
			if (!empty($phs)) {
				return $this->DB->execute("DELETE FROM partner_honoured WHERE partner_lead_id = ? ", [$leadId]);
			}
			return true;
		}

    /*
    - get lead's brtoker info against the given Lend's Lender
    @param: $lead - lead info array
    @param: $lenderPartnerId - Lend's Lender partner ID
    return: array of broker company name, broker name, broker mobile/phone
    */
    public function getLeadBrokerDetails($lead, $lenderPartnerId)
    {
      try {
        $ret = [];
        if (isset($lead['lead']))
          $lead = $lead['lead'];

        //check lead
        if (empty($lead['lead_id']))
          throw new \Exception('Not found Lead ID');

        //partner lead
        if (empty($lead['partner_id']))
          throw new \Exception('Not found Partner ID');

        $leadId = $lead['lead_id'];
        $partnerId = $lead['partner_id'];

        //get latest sale's id with given Lender Partner ID
        $sale = $this->DB->execute('SELECT s.sale_id, s.created, s.product_id
                    FROM sales as s
                    JOIN lender_product as lp
                    ON lp.lender_product_id = s.product_id
                    JOIN lenders as l
                    ON l.lender_id = lp.lender_id
                    WHERE s.lead_id = ?
                    AND l.partner_id = ?
                    ORDER BY s.sale_id DESC', [$leadId, $lenderPartnerId])
                ->fetch('assoc');
        if (empty($sale['sale_id']))
          throw new \Exception('Not found Sale');  // a bit weird

        //get Sent row in table partner_lead_history - who sent the lead to this lender
        $history = $this->DB->execute('SELECT *
                    FROM partner_lead_history
                    WHERE lead_id = ?
                    AND history_detail = "Sent"
                    AND ref_type = "sale_id"
                    AND ref_id = ?
                    ORDER BY created DESC', [$leadId, $sale['sale_id']])
                ->fetch('assoc');

        if (!empty($history['partner_user_id'])) {
          //get broker details from sent partner user id
          $partnerUser = $this->DB->execute('SELECT pu.name, pu.mobile, pu.phone, p.company_name, p.organisation_name
                        FROM partner_users as pu
                        JOIN partners as p
                        ON p.partner_id = pu.partner_id
                        WHERE pu.partner_user_id = ?
                        AND pu.active = "1"', [$history['partner_user_id']])
                    ->fetch('assoc');

        }
        if (empty($partnerUser)) {
          //get broker details from partner poc
          $partnerUser = $this->DB->execute('SELECT pu.name, pu.mobile, pu.phone, p.company_name, p.organisation_name
                        FROM partners as p
                        JOIN partner_users as pu
                        ON p.partner_id = pu.partner_id
                        WHERE pu.active = "1"
                        AND p.partner_id = ?
                        AND pu.point_of_contact = "1"', [$partnerId])
                    ->fetch('assoc');
        }
        if (empty($partnerUser))
          throw new \Exception("Not found Parnter Info");

        $ret[] = $partnerUser['company_name'];
        $ret[] = $partnerUser['name'];
        $ret[] = !empty($partnerUser['mobile']) ? $partnerUser['mobile'] : (!empty($partnerUser['phone']) ? $partnerUser['phone'] : '');
        return $ret;
      } catch (\Exception $e) {
        return false;
      }
    }

    /*
    - get partner alias's name
    @param: $partnerAliasId - Partner Alias ID
    return: company name of Partner Alias
    */
    public function getLeadAliasName($partnerAliasId)
    {
      $row = $this->DB->execute("SELECT * FROM partner_aliases WHERE partner_alias_id = ? ", [$partnerAliasId])
              ->fetch('assoc');

      return !empty($row['company_name']) ? $row['company_name'] : '';
    }

    /*
    - the lead must be sent to Lend's Lender, in other word, it is only for lender portal
    @param: $lead - array of lead details
    @param: $lenderPartnerId - Lend Lender Partner ID
    return: array of information
    */
    public function getLenderAboutLeadCallMeFirst($lead, $lenderPartnerId)
    {
      $salesTable     = TableRegistry::get('SaleEntity');
      $lender_id      = TableRegistry::get('LenderEntity')->find()
        ->select(['lender_id'])
        ->where(['partner_id' => $lenderPartnerId])
        ->first()
        ->lender_id; 

      $query = $salesTable->find()
        ->select($salesTable)
        ->select($salesTable->SaleReferences) 
        ->contain('SaleReferences')
        ->where(['SaleEntity.lead_id' => $lead['lead']['lead_id']])
        ->where(['SaleReferences.lender_id' => $lender_id]);

      $results = $query->first();

      $call_me_first =  $results->call_me_first;
      
      $ret = [];
      if ($call_me_first){
        $ret['Who to Contact'] = 'Broker';
        // $ret['Broker Details'] = $this->getLeadBrokerDetails($lead, $lenderPartnerId);
        $ret['Broker Details'] = !empty($this->getCurrentUser($lead['lead']['lead_id'])) ? $this->getCurrentUser($lead['lead']['lead_id']) : $this->getLeadBrokerDetails($lead, $lenderPartnerId);
      } else {
        $ret['Who to Contact'] = 'Client';
        //Note: Call on behalf of will only be needed if "Who to Contact" is "Client"
        if (!empty($lead['lead']['partner_alias_id'])) {
          $ret['Call on behalf of'] = $this->getLeadAliasName($lead['lead']['partner_alias_id']);
        }
      }

      return $ret;
    }

    private function getCurrentUser($leadId){

			$sql = 'SELECT pul.*, pu.name, pu.email, pu.mobile, pu.phone
							FROM partner_user_leads as pul,
							partner_users as pu
							WHERE pul.partner_user_id = pu.partner_user_id
							AND pul.status="ACCESS"
							AND pu.active = "1"
              AND pul.lead_id = ?';

			$rows = $this->DB->execute($sql, [$leadId])->fetchAll('assoc');

			$ret = [];
				foreach ($rows as $row) {
					$ret[$row['partner_user_id']] = [
							'name'=>$row['name'],
							'email'=>$row['email'],
							'phone'=>$row['phone'],
							'mobile'=>$row['mobile'],
					];
			}

      $current_user = [];
      $currentUser = $ret;
      if(!empty($currentUser)){
        foreach($currentUser as $user){
          $current_user = ['name'=>$user['name'], 'email'=>$user['email'], 'mobile'=>$user['mobile']];
          break;
        }
      }
      return $current_user;
    }

    public function getAvailableEquipmentSource($lead_type="commercial") {
			$sql = "SELECT sale_type, find_in_set ('".$lead_type."', uses) from config_asset_sale_types where active = 1 and find_in_set ('".$lead_type."', uses) > 0 order by `order` asc";
			$rows = $this->DB->execute($sql)->fetchAll('assoc');
      $available = [];
			foreach($rows as $row) {
				$available[] =trim($row['sale_type'], "'");
			}
			return $available;
		}

    public function getCustomerType($isForClients = false) {

      $sql = "SHOW COLUMNS FROM `leads` LIKE 'customer_type'";

      $rows = $this->DB->execute($sql)->fetch('assoc');
      preg_match('/enum\((.*)\)$/', $rows['Type'], $enums);
      $customer_types = array();
      $statuses = explode(',', $enums[1]);
      foreach($statuses as $type) {
        $customer_types[] =trim($type, "'");
      }

      if($isForClients)   $diff = 'Invoice Other Businesses';
      else                $diff = 'Businesses you invoice';
      $customer_types = array_diff($customer_types, [$diff]);
      $customer_type = array_values($customer_types);

      return $customer_type;

    }

    public function getLeaseOrFinance() {

      $sql = "SHOW COLUMNS FROM `leads` LIKE 'lease_or_finance'";

      $rows = $this->DB->execute($sql)->fetch('assoc');
      preg_match('/enum\((.*)\)$/', $rows['Type'], $enums);
      $available = array();
      $statuses = explode(',', $enums[1]);
      foreach($statuses as $status) {
        $available[] =trim($status, "'");
      }

      return $available;

    }

    public function searchLeads($partnerID, $params) {
      $sql = "SELECT l.lead_ref, l.partner_status_id, l.created, l.last_changed_date, l.abn, l.organisation_name,
                     l.business_name, l.amount_requested, l.loan_term_requested, l.trading_since, l.industry_detail,
                     l.sales_monthly, l.sales_annual, l.b_address, l.company_registration_date,
                     lo.first_name, lo.last_name, lo.mobile, lo.phone
              FROM leads l
              LEFT JOIN lead_owners lo
                ON lo.lead_id = l.lead_id AND lo.point_of_contact = 1
              WHERE l.partner_id = ?
                AND (
                  l.organisation_name LIKE ?
                  OR l.business_name LIKE ?
                  OR lo.first_name LIKE ?
                  OR lo.last_name LIKE ?
                  OR l.lead_ref = ?
                  OR lo.email = ?
                  OR lo.mobile = ?
                )
              ORDER BY l.last_changed_date DESC";

      $var     = '%'.$params['search'].'%';
      $results = $this->ReaderDB->execute($sql, [$partnerID,$var,$var,$var,$var,$params['search'],$params['search'],$params['search']])->fetchAll('assoc');

      foreach ($results as $key => $result)
        foreach (array('organisation_name','business_name','first_name','last_name') as $fields)
          $results[$key][$fields] = ucwords(strtolower($results[$key][$fields]));

      return $results;
    }


    public function updateStatus($leadid, $statusid) {
      $this->addActivityFive($statusid, $leadid);
      $this->DB->execute('UPDATE leads SET status_id="'.$statusid.'" WHERE lead_id = "'.$leadid.'"');
      return true;
    }

    public function getPartnerStatus($leadid){
      $sql = "SELECT ls.status_name FROM lend_statuses AS ls LEFT JOIN leads ON leads.partner_status_id = ls.lend_status_id WHERE leads.lead_id = ?";
      $ret = $this->DB->execute($sql, [$leadid])->fetch('assoc');
      return $ret['status_name'];
    }

    public function isPartnerStatus($leadId, $statusName, $mustBePartnerLead = true)
    {
      $sql = "SELECT l.lead_id, l.partner_id, l.partner_status_id, ls.lend_status_id, ls.status_name
        FROM leads AS l
        LEFT JOIN lend_statuses AS ls ON ls.lend_status_id = l.partner_status_id
        WHERE l.lead_id = ? ";
      $ret = $this->DB->execute($sql, [$leadId])->fetch('assoc');
      if ($mustBePartnerLead)
        return !empty($ret['lead_id']) && $ret['status_name'] == $statusName
            && !empty($ret['partner_id'])
            && !in_array($ret['partner_id'], ['1', '3', '4', '8', '54', '64']) ? true : false;
      else
        return !empty($ret['lead_id']) && $ret['status_name'] == $statusName ? true : false;
    }

    public function hasProductTypeId($leadId, $excludingProductTypeId = null)
    {
      $sql = "SELECT lead_id,product_type_id FROM leads WHERE lead_id = ?";
      $ret = $this->DB->execute($sql, [$leadId])->fetch('assoc');
      $return = true;
      if (empty($ret['lead_id']))
        $return = false;
      elseif (empty($excludingProductTypeId))
        $return = !empty($ret['product_type_id']) ? true : false;
      else
        $return = $ret['product_type_id'] != $excludingProductTypeId ? true: false;
      return $return;
    }

    public function findLeadIdByLeadRef($leadRef) {
      $sql = "SELECT lead_id FROM leads WHERE lead_ref = ?";
      $result = $this->DB->execute($sql, [$leadRef])->fetch('assoc');
      return $result != false ? $result['lead_id'] : false;
    }

    public function findLeadsByLeadRefs($leadRefs) {
      if(empty($leadRefs)) return false;
      $target = implode(',', preg_replace('/[0-9]/', '?', array_keys($leadRefs)));
      $sql = "SELECT * FROM leads WHERE lead_ref in ({$target})";
      return $this->DB->execute($sql, $leadRefs)->fetchAll('assoc');
    }

    private function findLeadIdsByKeyword($partnerId, $passInPartnerUserId, $keyword){
      $join = !empty($passInPartnerUserId) ? " JOIN partner_user_leads as pul ON l.lead_id = pul.lead_id AND pul.partner_user_id = '{$passInPartnerUserId}' AND pul.status='ACCESS' " : "";
      $sql = "SELECT l.lead_id
              FROM leads l
              LEFT JOIN lead_owners lo
                ON lo.lead_id = l.lead_id AND lo.point_of_contact = 1
              {$join}
              WHERE l.partner_id = ?
                AND (
                  l.organisation_name LIKE ?
                  OR l.business_name LIKE ?
                  OR lo.first_name LIKE ?
                  OR lo.last_name LIKE ?
                  OR CONCAT(lo.first_name,' ',lo.last_name) like ?
                  OR l.lead_ref = ?
                  OR lo.email = ?
                  OR lo.mobile = ?
                )
              ORDER BY l.lead_id ASC";

      $var     = '%'.$keyword.'%';
      if($results = $this->ReaderDB->execute($sql, [$partnerId,$var,$var,$var,$var,$var,$keyword,$keyword,$keyword])->fetchAll('assoc'))
        return array_column($results, 'lead_id');
      else
        return false;
    }

    private function getLenderStatusJoin($join, $additional_fields){
      $statusJoin[] = " LEFT JOIN lender_lead_updates ON (leads.lead_id = lender_lead_updates.lead_id)
                        LEFT JOIN (
                          SELECT lead_id,
                            MAX(lender_update_id) AS lender_update_id
                          FROM lender_lead_updates
                          GROUP BY lead_id
                        ) as llu ON leads.lead_id = llu.lead_id AND lender_lead_updates.lender_update_id = llu.lender_update_id";
      $statusAdditionalFields[] = "lender_lead_updates.combined_status_string as lender_status";

      $join = array_merge($join, $statusJoin);
      $additional_fields = array_merge($additional_fields, $statusAdditionalFields);
      return [$join, $additional_fields];
    }


    public function fullSearch($partnerId, $passInPartnerUserId, $data, $pagesize, $page, $countOnly = false) {
      $join = []; $where = []; $values =[]; $additional_fields = [];
      $manualStatus = (TableRegistry::getTableLocator()->get('PartnerEntity')->find()->select('status_system')->where(['partner_id' => $partnerId])->first()->status_system === "manual");
      $statusJoin = $manualStatus ? 'man_statuses lend_statuses ON leads.man_status_id = lend_statuses.id' : 'lend_statuses ON leads.partner_status_id = lend_statuses.lend_status_id';
      $where[] = "leads.partner_id=?";
      $values[] = $partnerId;
      // For partner user who is not admin, they only can see their own leads.
      if(!empty($passInPartnerUserId)) $join[] = "JOIN partner_user_leads as pul ON leads.lead_id = pul.lead_id AND pul.partner_user_id = '{$passInPartnerUserId}' AND pul.status='ACCESS'";
      // Firstly, grab all lead_ids by keyword.
      if(!empty($data['keyword'])){
        $lead_ids = $this->findLeadIdsByKeyword($partnerId, $passInPartnerUserId, $data['keyword']);
        // If there is a keyword, but no result, then we can simply return false from here.
        if(empty($lead_ids)) return false;
      }
      if(!empty($data['filters'])){
        // Generate additional Join query:
        $filter_data_sources = array_unique(array_column($data['filters'], 'data_source'));
        list($join, $additional_fields) = $this->generateAdditionalJoinQuery($filter_data_sources);


        // Generate WHERE conditions:
        foreach($data['filters'] as $filter){
          if($manualStatus && ($filter['data_source']==="lend_statuses")){
            if($filter['field']==="status_name"){
              $filter['field'] = "id";
            }
            else if($filter['field']==="group_name"){
              $filter['field'] = "man_status_group_id";
            }
          }
          $query = $this->generateWhereConditions($filter);
          $where[] = $query['query'];
          if(!empty($query['values'])) $values = array_merge($values, $query['values']);
        }
      }

      if(!$manualStatus)
        list($join, $additional_fields) = $this->getLenderStatusJoin($join, $additional_fields);

      // Convert array to string
      $join = !empty($join) ? implode(' ', $join) : "";
      $additional_fields = !empty($additional_fields) ? ",".implode(',', $additional_fields) : "";
      $where = " WHERE ".(!empty($lead_ids) ? "leads.lead_id IN (".implode(',', $lead_ids).")" : "1")." AND (".(!empty($where) ? implode(' AND ', $where) : " 1 ").")";

      $sql = "SELECT leads.lead_ref, leads.created, leads.statements_uploaded, leads.last_changed_date, leads.product_type_id, leads.source,
                         leads.abn, leads.organisation_name, leads.business_name, leads.amount_requested, leads.purpose_id, leads.equipment_id, leads.loan_term_requested,
                        leads.industry_id, leads.industry_detail, leads.sales_monthly, leads.sales_annual, leads.b_address, leads.b_state, leads.bs_doc_id, leads.bs_doc_retrieved, leads.is_abn_unknown, leads.total_owners, leads.company_registration_date, leads.is_archived,
                        leads.business_type_abn, leads.register_gst, leads.call_me_first, leads.send_type, leads.call_queue_status, leads.lead_type, leads.is_tick_and_flick,
                        lead_owners.first_name, lead_owners.last_name, lead_owners.mobile, lead_owners.phone, lead_owners.email, lead_owners.dob, lead_owners.home_owner, lead_owners.home_owner_detail, lead_owners.owner_type, lead_owners.address, lead_owners.state, lead_owners.equity, lead_owners.lvr, lead_owners.credit_history,
                        lend_score.lend_score, lend_statuses.status_name
                        {$additional_fields}
              FROM leads
              JOIN lead_owners ON leads.lead_id = lead_owners.lead_id AND lead_owners.point_of_contact=1
              LEFT JOIN referrer_people on leads.referrer_person_id = referrer_people.id
              LEFT JOIN referrers on referrers.id = referrer_people.referrer_id
              LEFT JOIN lead_abn_lookup ON lead_abn_lookup.abn_id =referrers.abn_id
              JOIN {$statusJoin}
              LEFT JOIN lend_score ON leads.lead_id = lend_score.lead_id
              {$join}
              {$where}
              GROUP BY leads.lead_id
              ORDER BY leads.lead_id DESC" . (($countOnly || $pagesize === false) ? "" :  " LIMIT {$pagesize} OFFSET ".(($page-1)*$pagesize));

      if($countOnly) {
        return $this->ReaderDB->execute($sql, $values)->count();
      }else{
        $leads = $this->ReaderDB->execute($sql, $values)->fetchAll('assoc');
        foreach($leads as &$lead) {
          $this->getLeadAddedBy($lead);
        }

        return $leads;
      }

    }

    private function getLeadAddedBy(&$lead){
        $sql = "SELECT plh.partner_id, plh.partner_user_id FROM partner_lead_history plh LEFT JOIN leads l ON l.lead_id = plh.lead_id
                    WHERE l.lead_ref = ? AND plh.history_detail = 'Lead Added'";
        $history = $this->DB->execute($sql, [$lead['lead_ref']])->fetch('assoc');
        if(!empty($history['partner_user_id'])) {
          $sql = "SELECT `name` FROM partner_users WHERE partner_user_id = ?";
          $lead['added_by'] = $this->DB->execute($sql, [$history['partner_user_id']])->fetch('assoc')['name'];
        }elseif(!empty($history['partner_id'])){
          $sql = "SELECT `name` FROM partner_users WHERE partner_id = ?";
          $lead['added_by'] = $this->DB->execute($sql, [$history['partner_id']])->fetch('assoc')['name'];
        }else{
          $lead['added_by'] = null;
        }
    }
    // NOTE:: This ADDITIONAL JOIN queries are generated depends on the filters, because this will make query speed slow.
    private function generateAdditionalJoinQuery($filter_data_sources){
      $join = [];
      $additional_fields = [];
      // If there are `first_rejected` or `last_rejected` in the filters, add join `lender_lead_updates`.
      if(in_array('first_rejected', $filter_data_sources)){
        $join[] = "LEFT JOIN (
                    SELECT * FROM lender_lead_updates WHERE lender_update_id IN (
                      SELECT MIN(lender_update_id) as lender_update_id
                      FROM lender_lead_updates as llu
                      JOIN lender_statuses as lst ON llu.lender_status_id = lst.lender_status_id
                      WHERE lst.type IN (2,3,5)
                      GROUP BY llu.lead_id
                    )
                  ) as first_rejected ON leads.lead_id = first_rejected.lead_id";
        $additional_fields[] = "first_rejected.created as first_rejected_date";
      }else{
        $additional_fields[] = "null as first_rejected_date";
      }

      if(in_array('last_rejected', $filter_data_sources)){
        $join[] = "LEFT JOIN (
                    SELECT * FROM lender_lead_updates WHERE lender_update_id IN (
                      SELECT MAX(lender_update_id) as lender_update_id
                      FROM lender_lead_updates as llu
                      JOIN lender_statuses as lst ON llu.lender_status_id = lst.lender_status_id
                      WHERE lst.type IN (2,3,5)
                      GROUP BY llu.lead_id
                    )
                  ) as last_rejected ON leads.lead_id = last_rejected.lead_id";
        $additional_fields[] = "last_rejected.created as last_rejected_date";
      }else{
        $additional_fields[] = "null as last_rejected_date";
      }

      // If there is `current_lender` in the filter, add join `sales` table.
      if(in_array('current_lender', $filter_data_sources)){
        $join[] = "LEFT JOIN (
                    SELECT s.lead_id, lp.lender_id
                    FROM sales as s
                    JOIN lender_product as lp ON s.product_id = lp.lender_product_id
                    WHERE sale_id IN (SELECT MAX(sale_id) FROM sales WHERE status!=0 GROUP BY lead_id)
                  ) as current_lender ON leads.lead_id = current_lender.lead_id";
        $additional_fields[] = "current_lender.lender_id as current_lender_id";
      }else{
        $additional_fields[] = "null as current_lender_id";
      }

      // If there is `first_funded` in the filter, add join `partner_commissions` table.
      if(in_array('first_funded', $filter_data_sources)){
        $join[] = "LEFT JOIN (SELECT * FROM partner_commissions WHERE commission_id IN (SELECT MIN(commission_id) as commission_id FROM partner_commissions GROUP BY lead_id)) as first_funded ON first_funded.lead_id = leads.lead_id";
        $additional_fields[] = "first_funded.funded_date as first_funded_date";
        $additional_fields[] = "first_funded.funded_amount as first_funded_amount";
        $additional_fields[] = "first_funded.funded_type as first_funded_type";
      }else{
        $additional_fields[] = "null as first_funded_date";
        $additional_fields[] = "null as first_funded_amount";
        $additional_fields[] = "null as first_funded_type";
      }

      // If there is `last_funded` in the filter, add join `partner_commissions` table.
      if(in_array('last_funded', $filter_data_sources)){
        $join[] = "LEFT JOIN (SELECT * FROM partner_commissions WHERE commission_id IN (SELECT MAX(commission_id) as commission_id FROM partner_commissions GROUP BY lead_id)) as last_funded ON last_funded.lead_id = leads.lead_id";
        $additional_fields[] = "last_funded.funded_date as last_funded_date";
        $additional_fields[] = "last_funded.funded_amount as last_funded_amount";
        $additional_fields[] = "last_funded.funded_type as last_funded_type";
      }else{
        $additional_fields[] = "null as last_funded_date";
        $additional_fields[] = "null as last_funded_amount";
        $additional_fields[] = "null as last_funded_type";
      }

      // If there is `last_activity` in the filter, add join `partner_lead_history` table.
      if(in_array('last_activity', $filter_data_sources)){
        $join[] = "LEFT JOIN (SELECT * FROM partner_lead_history WHERE history_id IN (SELECT MAX(history_id) FROM partner_lead_history GROUP BY lead_id)) as last_activity ON leads.lead_id = last_activity.lead_id";
        $additional_fields[] = "last_activity.created as last_activity_date";
      }else{
        $additional_fields[] = "null as last_activity_date";
      }

      // If there is `last_activity` in the filter, add join `partner_lead_history` table.
      if(in_array('partner_lead_history', $filter_data_sources)){
        $join[] = "LEFT JOIN partner_lead_history ON leads.lead_id = partner_lead_history.lead_id AND partner_lead_history.history_detail='Lead Added'";
        $additional_fields[] = "partner_lead_history.partner_user_id as added_by";
      }else{
        $additional_fields[] = "null as added_by";
      }

      return [$join, $additional_fields];
    }

    private function generateWhereConditions($filter){
      $query = '';
      $values = [];
      switch(strtolower($filter['operator'])){
        case "equal to":
        case "is":
          $query = $filter['data_source'].".".$filter['field']."=?";
        break;

        case "not equal to":
          $query = $filter['data_source'].".".$filter['field']."!=?";
        break;

        case "contains":
          $query = $filter['data_source'].".".$filter['field']." LIKE ?";
        break;

        case "does not contain":
          $query = $filter['data_source'].".".$filter['field']." NOT LIKE ?";
        break;

        case "is any of":
          $query = $filter['data_source'].".".$filter['field']." IN (".implode(',', array_map(function($arr){
                                                                            return '?';
                                                                          }, explode(',', $filter['value']))).")";
        break;
        //
        case "is none of":
          $query = $filter['data_source'].".".$filter['field']." NOT IN (".implode(',', array_map(function($arr){
                                                                            return '?';
                                                                          }, explode(',', $filter['value']))).")";
        break;

        case "greater than":
        case "is after":
          $query = $filter['data_source'].".".$filter['field'].">?";
        break;

        case "greater than or equal to":
          $query = $filter['data_source'].".".$filter['field'].">=?";
        break;

        case "less than":
        case "is before":
          $query = $filter['data_source'].".".$filter['field']."<?";
        break;

        case "less than or equal to":
          $query = $filter['data_source'].".".$filter['field']."<=?";
        break;

        case "between":
          $query = $filter['data_source'].".".$filter['field']." BETWEEN ? AND ?";
        break;

        case "known":
          $query = $filter['data_source'].".".$filter['field']." IS NOT null";
        break;

        case "unknown":
          $query = $filter['data_source'].".".$filter['field']." IS null";
        break;
      }

      switch(strtolower($filter['operator'])){
        case "is any of":
        case "is none of":
          $values = explode(',', $filter['value']);
          foreach($values as $k=>$v){
            $values[$k] = trim($v);
          }
        break;

        case "between":
          $values[] = $filter['value'];
          $values[] = $filter['value2'];
        break;

        case "contains":
        case "does not contain":
          $values[] = "%".$filter['value']."%";
        break;

        case "known":
        case "unknown":
        break;

        default:
          $values[] = $filter['value'];
        break;
      }

      // ===============================================
      // Special converting value for specific fields:
      // -----------------------------------------------
      // leads.company_registration_date
      // -----------------------------------------------
      if($filter['data_source']==='leads' AND $filter['field']==='company_registration_date'){
        foreach($values as $k=>$v){
          $values[$k] = date('Y-m-d 00:00:00', strtotime('-'.$v.' months'));
        }
      }
      // -----------------------------------------------
      // partner_commissions
      // -----------------------------------------------
      if($filter['data_source']==='partner_commissions' AND $filter['field']==='commission'){
        $query = "leads.lead_id IN (SELECT lead_id FROM partner_commissions WHERE {$query} GROUP BY lead_id)";
      }
      // -----------------------------------------------
      // lead_owners.home_owner
      // -----------------------------------------------
      if($filter['data_source']==='lead_owners' AND $filter['field']==='home_owner'){
        if($filter['value']=='1')
          $query = $filter['data_source'].".".$filter['field']."=1";
        else
          $query = $filter['data_source'].".".$filter['field']."!=1";
        $values = []; // reset values
      }

      return ['query'=>$query, 'values'=>$values];
    }

    public function getPrivateLendingPurpose(){
        $q = "SELECT * FROM frm_private_lending_purpose WHERE status = 1";
        return $this->DB->execute($q, [])->fetchAll('assoc');
    }

    public function getBooksPackage(){
        $q = "SELECT * FROM frm_books_package WHERE STATUS = 1";
        return $this->DB->execute($q, [])->fetchAll('assoc');
    }

    public function getEquipment(){
        $q = "SELECT * FROM frm_equipment WHERE STATUS = 1";
        return $this->DB->execute($q, [])->fetchAll('assoc');
    }

    public function getAssetTypes(){
        $q = "SELECT * FROM config_asset_types";
        return $this->DB->execute($q, [])->fetchAll('assoc');
    }

    public function getLiabilities(){
        $q = "SELECT * FROM config_liabilities";
        return $this->DB->execute($q, [])->fetchAll('assoc');
    }

    public function getLiabilityID($liability_type=false, $liability_name=false){
        if(empty($liability_type) OR empty($liability_name)) return false;
        $q = "SELECT liability_id FROM config_liabilities WHERE liability_type = ? AND liability_name = ?";
        return $this->DB->execute($q, [$liability_type, $liability_name])->fetch('assoc');
    }

    public function getAssetTypeID($asset_type=false, $asset_type_name=false){
        if(empty($asset_type) OR empty($asset_type_name)) return false;
        $q = "SELECT asset_type_id FROM config_asset_types WHERE asset_type = ? AND asset_type_name = ?";
        return $this->DB->execute($q, [$asset_type, $asset_type_name])->fetch('assoc');
    }

    public function getequipmentID($equipment=false){
        if(empty($equipment)) return false;
        $q = "SELECT equipment_id FROM frm_equipment WHERE equipment = ? AND status = 1";
        return $this->DB->execute($q, [$equipment])->fetch('assoc');
    }

    public function getEnums($table, $column){
        $q = "SHOW COLUMNS FROM ".$table." WHERE FIELD = ?";
        $results = $this->DB->execute($q, [$column])->fetch('assoc')['Type'];
        preg_match("/^enum\(\'(.*)\'\)$/", $results, $matches);
        $enum = explode("','", $matches[1]);
        return $enum;
    }

    public function saveAssetFinance($params=false, $lead_ref=false){
        if(empty($params) OR empty($lead_ref)) return false;
        $lead_id = (new LendInternalAuth)->unhashLeadId($lead_ref);
        // $params = json_decode($params);
        if(isset($params['business_credit_history'])){
           $q = "UPDATE leads SET business_credit_history = ? WHERE lead_id = ?";
           $this->DB->execute($q, [$params['business_credit_history'], $lead_id]);
        }

        if(isset($params['people'])){
            foreach ($params['people'] as $key => $value) {
                if(isset($value['employment'])){
                    $employment_data = unserialize(serialize($value['employment']));
                    unset($value['employment']);
                }
                if(isset($value['addresses'])){
                    $address_data = unserialize(serialize($value['addresses']));
                    unset($value['addresses']);
                }
                $targets = array();
                $fields = array();
                $values = array();
                $enc_fields = ['driving_licence_num', 'driving_licence_card_number', 'medicare_number', 'passport_number'];
                foreach(json_decode(json_encode($value), true) as $field=>$v){
                    $targets[] = '?';
                    if(in_array($field, $enc_fields)){
                      $fields[] = $field."_e";
                      $values[] = EncryptionHelper::safeEncrypt($v);
                    }
                    else{
                      $fields[] = $field;
                      if($field == 'directorship_start_date')
                        $values[] = date('Y-m-d', intval($v));
                      elseif ($field == 'dependant_details')
                        $values[] = json_encode($v);
                      else
                        $values[] = $v;
                    }
                }
                $targets[] = '?';
                $fields[] = 'lead_id';
                $values[] = $lead_id;
                $query = $this->DB->execute("INSERT INTO lead_owners (".implode(',', $fields).") VALUES (".implode(',', $targets).")", $values);
                $lead_owner_id = $query->lastInsertId();
                if(isset($employment_data)){
                    foreach ($employment_data as $key1 => $value1) {
                        $targets = array();
                        $fields = array();
                        $values = array();
                        foreach(json_decode(json_encode($value1), true) as $field=>$v){
                            $targets[] = '?';
                            $fields[] = $field;
                            if($field == 'date_from' OR $field == 'date_to')
                               $values[] = date('Y-m-d', intval($v));
                            else
                                $values[] = $v;
                        }
                        $targets[] = '?';
                        $fields[] = 'lead_owner_id';
                        $values[] = $lead_owner_id;
                        $targets[] = '?';
                        $fields[] = 'created';
                        $values[] = date('Y-m-d H:i:s');
                        $this->DB->execute("INSERT INTO lead_owner_employment (".implode(',', $fields).") VALUES (".implode(',', $targets).")", $values);
                    }
                }
                if(isset($address_data)){
                    foreach ($address_data as $key1 => $value1) {
                        $targets = array();
                        $fields = array();
                        $values = array();
                        foreach(json_decode(json_encode($value1), true) as $field=>$v){
                            $targets[] = '?';
                            $fields[] = $field;
                            if($field == 'date_from' OR $field == 'date_to')
                               $values[] = date('Y-m-d', intval($v));
                            else
                                $values[] = $v;
                        }
                        $targets[] = '?';
                        $fields[] = 'lead_owner_id';
                        $values[] = $lead_owner_id;
                        $targets[] = '?';
                        $fields[] = 'created';
                        $values[] = date('Y-m-d H:i:s');
                        $this->DB->execute("INSERT INTO lead_owner_addresses (".implode(',', $fields).") VALUES (".implode(',', $targets).")", $values);
                    }
                }
            }
        }
        if(isset($params['references'])){
            foreach ($params['references'] as $key => $value) {
                $targets = array();
                $fields = array();
                $values = array();
                foreach(json_decode(json_encode($value), true) as $field=>$v){
                    $targets[] = '?';
                    $fields[] = $field;
                    $values[] = $v;
                }
                $targets[] = '?';
                $fields[] = 'lead_id';
                $values[] = $lead_id;
                $query = $this->DB->execute("INSERT INTO lead_references (".implode(',', $fields).") VALUES (".implode(',', $targets).")", $values);
            }
        }
        if(isset($params['assets'])){
            foreach ($params['assets'] as $key => $value) {
                $targets = array();
                $fields = array();
                $values = array();

                $arr = json_decode(json_encode($value));
                $asset_type_id = $this->getAssetTypeID($arr->asset_type, $arr->asset_type_name);
                foreach(json_decode(json_encode($value), true) as $field=>$v){
                    if($field != 'asset_type_name' AND $field != 'asset_type'){
                        $targets[] = '?';
                        $fields[] = $field;
                        $values[] = $v;
                    }
                }
                $targets[] = '?';
                $fields[] = 'asset_type_id';
                $values[] = $asset_type_id['asset_type_id'];
                $targets[] = '?';
                $fields[] = 'lead_id';
                $values[] = $lead_id;
                $targets[] = '?';
                $fields[] = 'lead_owner_id';
                $values[] = $lead_owner_id;
                $query = $this->DB->execute("INSERT INTO lead_assets (".implode(',', $fields).") VALUES (".implode(',', $targets).")", $values);
            }
        }
        if(isset($params['asset_finance'])){
            $targets = array();
            $fields = array();
            $values = array();
            foreach(json_decode(json_encode($params['asset_finance']), true) as $field=>$v){
                $targets[] = '?';
                if($field == 'condition'){
                    $fields[] = '`condition`';
                    $values[] = $v;
                }
                elseif($field == 'equipment'){
                    $fields[] = 'equipment_id';
                    $values[] = $this->getequipmentID($v)['equipment_id'];
                }
                else{
                    $fields[] = $field;
                    $values[] = $v;
                }

            }
            $targets[] = '?';
            $fields[] = 'lead_id';
            $values[] = $lead_id;
            $query = $this->DB->execute("INSERT INTO lead_asset_finance (".implode(',', $fields).") VALUES (".implode(',', $targets).")", $values);
        }
        if(isset($params['liabilities'])){
            foreach ($params['liabilities'] as $key => $value) {
                $targets = array();
                $fields = array();
                $values = array();

                $arr = json_decode(json_encode($value));
                $liability_id = $this->getLiabilityID($arr->liability_type, $arr->liability_name);
                foreach(json_decode(json_encode($value), true) as $field=>$v){
                    if($field != 'liability_type' AND $field != 'liability_name'){
                        $targets[] = '?';
                        if($field == 'limit')
                            $fields[] = '`limit`';
                        else
                            $fields[] = $field;
                        if($field == 'asset_fi_commenced')
                            $values[] = date('Y-m-d', intval($v));
                        else
                            $values[] = $v;
                    }
                }
                $targets[] = '?';
                $fields[] = 'liability_id';
                $values[] = $liability_id['liability_id'];
                $targets[] = '?';
                $fields[] = 'lead_id';
                $values[] = $lead_id;
                $targets[] = '?';
                $fields[] = 'lead_owner_id';
                $values[] = $lead_owner_id;
                $query = $this->DB->execute("INSERT INTO lead_liabilities (".implode(',', $fields).") VALUES (".implode(',', $targets).")", $values);
            }
        }
    }

    public function isValidTimeStamp($timestamp)
    {
        return ((string) (int) $timestamp === $timestamp)
            && ($timestamp <= PHP_INT_MAX)
            && ($timestamp >= ~PHP_INT_MAX);
    }

    /**
     * Fetch lead table fields
     * @return array
     */
    public function getLeadTableFields(){
        $q = "SELECT COLUMN_NAME, DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'leads';";
        return $this->DB->execute($q)->fetchAll('assoc');
    }

    public function clone($lead_id, $inter_lender_id = false){
      if (!$lead_id) return false;

      $lead = $this->getLead(array('lead_id'=>$lead_id));
      if (!$lead) return false;

      unset($lead['lead_id']);
      unset($lead['hashed_lead_id']);
      unset($lead['lead_ref']);
      unset($lead['last_changed_date']);
      unset($lead['lender_status_id']);
      unset($lead['campaign']);

      $lead['status_id'] = 1;
      $lead['partner_status_id'] = 1;
      $lead['send_type'] = 'Manual';
      $lead['force_send'] = false;
      $lead['created'] = date('Y-m-d H:i:s');

      if (!empty($inter_lender_id)) {
        $lead['intermediary_original_partner_id'] = $lead['partner_id'];
        $lead['partner_id'] = $inter_lender_id;
        $partner = TableRegistry::getTableLocator()->get('PartnerEntity')->get($inter_lender_id);
        $lead['call_me_first'] = $partner->call_me_first;
        unset($lead['account_id']);
        unset($lead['call_queue_status']);
      }

      $new_lead_id = $this->addLead($lead);

      return $new_lead_id;
    }

    public function archiveBulkLeads($lead_refs){
      $sql ='UPDATE leads SET is_archived = 1 WHERE lead_ref IN (' .rtrim(str_repeat("?,", count($lead_refs)), ',').');';
      $this->DB->execute($sql,$lead_refs);
    }

    public function unArchiveBulkLeads($lead_refs){
      $sql ='UPDATE leads SET is_archived = 0 WHERE lead_ref IN (' .rtrim(str_repeat("?,", count($lead_refs)), ',').');';
      $this->DB->execute($sql,$lead_refs);
    }

    public function getDistinctSourceByPartnerId($partner_id){
      $data = $this->DB->execute("SELECT source FROM leads WHERE partner_id=? AND source IS NOT null GROUP BY source", [$partner_id])->fetchAll('assoc');
      return array_values(array_column($data, 'source'));
    }

    public function getDistinctCampaignByPartnerId($partner_id){
      $data = $this->DB->execute("SELECT campaign FROM leads WHERE partner_id=? AND campaign IS NOT null GROUP BY campaign", [$partner_id])->fetchAll('assoc');
      return array_values(array_column($data, 'campaign'));
    }

    public function intermediaryLeadSettingsUpdate($data){

      $send_type = 'Manual';
      $lead_id   = $data['new_lead_id'];
      $inter_partner_id = $data['inter_lender_id'];

      $this->DB->execute("UPDATE leads SET `send_type`='$send_type',`partner_id`='$inter_partner_id' WHERE lead_id=?",[$lead_id]);

      $this->intermediaryLeadMapping($data);

    }

    public function intermediaryLeadMapping($data){

      $new_lead_id = $data['new_lead_id'];
      $original_lead_id = $data['original_lead_id'];


      $sql = "INSERT INTO
                intermediary_lender_mapping(`original_lead_id`,`new_lead_id`)
                  VALUES (?,?) on duplicate key update `new_lead_id`=?";


      $this->DB->execute($sql,[$original_lead_id,$new_lead_id,$new_lead_id]);
    }

    public function setLeadsTermMonths($leadsID,$month){

      if($leadsID !=""){
        $sql = "UPDATE leads SET loan_term_requested_months = ? WHERE  leads.lead_id = ? AND (leads.loan_term_requested_months IS NOT NULL AND leads.loan_term_requested_months !=0)";
        $this->DB->execute($sql, [$month,$leadsID]);
        return TRUE;
      }
      return FALSE;
    }

    /**
     * saveLeadBrokerFlowId
     * referrerCode: A_xxxxxxx means account level, L_xxxxxxx means lead level
     *
     * @return void
     */
    public function saveLeadBrokerFlowId($lead_id, $brokerFlowId){
      try {
        $lead_ref = (new LendInternalAuth)->hashLeadId($lead_id);
        $sendData = ['referrerCode' => "L_".$lead_ref, "documentId" => $brokerFlowId];
        $curl = new CurlHelper(getenv('DOMAIN_BANKFEEDS')."/broker-flow");
        $response = $curl->post($sendData, true, ["x-api-key" => getenv('BANK_STATEMENT_API_KEY')]);
        return $this->setJsonResponse($response);
      } catch(\Exception $e) {
        Log::error($e->getMessage());
        Log::error($e->getTraceAsString());
        return false;
      }
    }

    public function checkFailedLeadExist($account_id){

      $sql = "SELECT *,
                  ( SELECT lead_ref FROM leads WHERE leads.lead_id = partner_accounts_failure_leads_log.lead_id LIMIT 1 ) AS lead_ref 
                FROM
                  partner_accounts_failure_leads_log 
                WHERE
                  partner_accounts_failure_leads_log.account_id = '$account_id' 
                  AND partner_accounts_failure_leads_log.sent_status = 0 
                ORDER BY
                  partner_accounts_failure_leads_log.created DESC 
                LIMIT 1"; 

      return $data = $this->DB->execute($sql)->fetch('assoc');


    }


    public function isPassPrecheckLeadCreation($account_id, $product_type_id, $lender_product_id){

      $sql = "SELECT
                  count(*) as last_30_days_same_product_for_account
                FROM
                  leads 
                WHERE
                  account_id =?
                  AND product_type_id =?
                  AND created >= DATE_SUB( NOW(), INTERVAL 30 DAY )";
      
      $data = $this->DB->execute($sql, [$account_id, $product_type_id])->fetch('assoc');

      if($data['last_30_days_same_product_for_account'] > 0){
        return "ERROR_01";
      }

      $sql = "SELECT
                count(*) as last_30_days_differet_product_for_account
              FROM
                leads
                INNER JOIN sales ON leads.lead_id = sales.lead_id
                INNER JOIN lender_product ON sales.product_id = lender_product.lender_product_id 
              WHERE
                account_id = ?
                AND lender_product.lender_id = ( SELECT lender_id FROM lender_product WHERE lender_product.lender_product_id = ? LIMIT 1 ) 
                AND leads.created >= DATE_SUB(
                  NOW(),
                  INTERVAL 30 DAY)";

      $data = $this->DB->execute($sql, [$account_id, $lender_product_id])->fetch('assoc');

      if($data['last_30_days_differet_product_for_account'] > 0){
        return "ERROR_02";
      }

      return true;

    }

    public function tagLeadAsFail($fail_data) {

      $lead_ref   = $fail_data['lead_id'];
      $account_id = $fail_data['account_id'];

      $sql        = "INSERT INTO partner_accounts_failure_leads_log ( `account_id`, `lead_id`, `sent_status` )
                        VALUES('$account_id','$lead_ref',0)";

      $this->DB->execute($sql);
    }

    public function deleteLeadTagAsFailed($pas_data){
      
      $lead_id    = $pas_data['lead_id'];
      $account_id = $pas_data['account_id'];

      $sql = "DELETE FROM partner_accounts_failure_leads_log WHERE account_id = '$account_id' AND lead_id = '$lead_id'";

      $this->DB->execute($sql);

    }
    
    // tmp mapping with question //
    public function updateTempLeadWithQuestion($lead_id,$params){


      
      $PartnerAccountMetaTable = TableRegistry::get('PartnerAccountMeta');
      $partner_account_meta = $PartnerAccountMetaTable->find()->where(['partner_account_id' =>$params['account_id']])->first()->toArray();

      if(empty($partner_account_meta)){
        Log::write('debug', 'partner_account_meta is empty');
        return false;
      }

      $indutry_id = $partner_account_meta['industry_id'];
   
      $company_registration_date	= $partner_account_meta['registration_date'];
      $sales_monthly = $partner_account_meta['sales_monthly'];

      if(empty($sales_monthly)){
        $error[] = 'sales_monthly is empty'; 
      }

      


      $organisation_name = $partner_account_meta['organisation_name'];
      
      $answers = json_decode($params['matching_query'],true);


      if($answers['selectedProductTypeId'])
        $product_type_id = $answers['selectedProductTypeId'];
      if($answers['main']['loanAmount'])
       $loan_amount = $answers['main']['loanAmount'];
      if($answers['main']['desiredTermInMonths'])
        $loan_term_requested_months = $answers['main']['desiredTermInMonths'];
      if($answers['main']['loanPurpose'])
        $loan_purpose_id = $answers['main']['loanPurpose'];

      // stagin test
      $company_registration_date	= '2018-01-01';
      $is_quote = 1;
      $is_archived =1;

      $sql = "UPDATE leads SET is_quote = ?,is_archived = ?, amount_requested = ?,organisation_name=?,purpose_id = ?, product_type_id = ?,loan_term_requested_months=? , industry_id = ?,company_registration_date=?, sales_monthly=? WHERE lead_id = ?";
      
      if($lead_id){
        $this->DB->execute($sql, [ $is_quote, $is_archived , $loan_amount,$organisation_name,$loan_purpose_id,$product_type_id,
                           $loan_term_requested_months,$indutry_id,$company_registration_date,$sales_monthly,$lead_id]);
      }

      $q=array('query'=>$query, 'values'=>$values);
      return $this->DB->execute($q['query'], $q['values'])->fetchAll('assoc');
    }

    public function leadStatusCounter($dbParams, $grouped=null){
      $values = [$dbParams['le.partner_id'], $dbParams['start_date'], $dbParams['end_date']];
      $query = "SELECT count(le.lead_id) as count, ls.group_name FROM leads le JOIN lend_statuses ls ON ls.lend_status_id = le.partner_status_id ";
      if(!empty($dbParams['pul.partner_user_id'])){
        $query .= " JOIN partner_user_leads pul ON pul.lead_id = le.lead_id";
      }
      $query .= " WHERE le.partner_id = ? AND le.is_archived = 0 AND le.created BETWEEN ? AND ? ";
      if(!empty($dbParams['pul.partner_user_id'])){
        $query .= " AND pul.partner_user_id = ? AND pul.status = 'ACCESS'";
        $values[] = $dbParams['pul.partner_user_id'];
      }
      if(!empty($dbParams['le.partner_status_id'])){
        $query .= " AND le.partner_status_id = ? ";
        $values[] = $dbParams['le.partner_status_id'];
      }
      if(!empty($grouped)){
        $query .= " GROUP BY ".$grouped;
      }

      $q=array('query'=>$query, 'values'=>$values);
      return $this->ReaderDB->execute($q['query'], $q['values'])->fetchAll('assoc');
    }

    public function getLeadAccount($account_id) {
      $query = 'SELECT * FROM partner_accounts pa JOIN partner_account_meta pam ON pa.partner_account_id = pam.partner_account_id WHERE pa.partner_account_id = ?;';
      $result = $this->DB->execute($query,[$account_id])->fetch('assoc');
      return $result;
    }

}