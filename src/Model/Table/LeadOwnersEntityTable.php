<?php
namespace App\Model\Table;

use App\Enums\LendSignTemplateUse;
use App\Lend\EncryptionHelper;
use Cake\Database\Schema\TableSchema;
use App\Model\Entity\LeadOwners;
use Cake\ORM\Table;
use Cake\Datasource\ConnectionManager;
use Cake\Datasource\EntityInterface;
use Cake\ORM\TableRegistry;
use Cake\Log\Log;
use Cake\Utility\Security;
use Hashids\Hashids;
use App\Lend\AvatarService;
use App\Lend\SocketHelper;



class LeadOwnersEntityTable extends Table
{
  protected $_table = 'lead_owners';

  protected $_primaryKey = 'owner_id';

  protected $_entityClass = LeadOwners::class;

  public function initialize(array $config)
  {
    $this->addBehavior('OwnerRefToOwnerId');
    $this->addBehavior('NameSanitize');

    $this->belongsTo('LeadEntity')
      ->setForeignKey('lead_id')
      ->setProperty('lead');

    $this->belongsTo('PartnerAccountPeopleEntity')
      ->setForeignKey('partner_account_people_id')
      ->setProperty('partner_account_people');

    $this->hasOne('LeadOwnerFinancesEntity')
      ->setForeignKey('owner_id')
      ->setProperty('finances');

    $this->hasMany('OwnerAllEmployments', ['className' => 'LeadOwnerEmploymentEntity'])
      ->setConditions(['OwnerAllEmployments.status' => 'active'])
      ->setForeignKey('lead_owner_id')
      ->setProperty('all_employments');

    $this->hasMany('CurrentEmployment', ['className' => 'LeadOwnerEmploymentEntity'])
      ->setConditions(['CurrentEmployment.date_to IS NULL', 'CurrentEmployment.status' => 'active'])
      ->setForeignKey('lead_owner_id')
      ->setProperty('current_employment');

    $this->hasMany('LeadOwnerPreviousEmploymentEntity', ['className' => 'LeadOwnerEmploymentEntity'])
      ->setConditions(['LeadOwnerPreviousEmploymentEntity.date_to IS NOT NULL', 'LeadOwnerPreviousEmploymentEntity.status' => 'active'])
      ->setForeignKey('lead_owner_id')
      ->setProperty('employment_previous');

    $this->hasMany('OwnerAllAddresses', ['className' => 'LeadOwnerAddressesEntity'])
      ->setConditions(['OwnerAllAddresses.status' => 'active', 'OwnerAllAddresses.address_type' => 'residential'])
      ->setForeignKey('lead_owner_id')
      ->setProperty('all_addresses');

    $this->hasOne('MailingAddress', [
      'className' => 'LeadOwnerAddressesEntity',
      'sort' => ['MailingAddress.lead_owner_address_id' => 'asc'],
      'strategy' => 'select',
      'limit' => 1
    ])
      ->setConditions(['MailingAddress.status' => 'active', 'MailingAddress.address_type' => 'mailing'])
      ->setForeignKey('lead_owner_id')
      ->setProperty('mailing_address');

    $this->hasOne('SettlementPostAddress', [
      'className' => 'LeadOwnerAddressesEntity',
      'sort' => ['SettlementPostAddress.lead_owner_address_id' => 'asc'],
      'strategy' => 'select',
      'limit' => 1
    ])
      ->setConditions(['SettlementPostAddress.status' => 'active', 'SettlementPostAddress.address_type' => 'settlement_post'])
      ->setForeignKey('lead_owner_id')
      ->setProperty('settlement_post_address');

    $this->hasOne('CurrentAddress', [
      'className' => 'LeadOwnerAddressesEntity',
      'sort' => ['CurrentAddress.lead_owner_address_id' => 'asc'],
      'strategy' => 'select',
      'limit' => 1
    ])
      ->setConditions(['CurrentAddress.date_to IS NULL', 'CurrentAddress.status' => 'active', 'CurrentAddress.address_type' => 'residential'])
      ->setForeignKey('lead_owner_id')
      ->setProperty('current_address');

    $this->hasMany('FormerAddresses', ['className' => 'LeadOwnerAddressesEntity'])
      ->setConditions(['FormerAddresses.date_to IS NOT NULL', 'FormerAddresses.status' => 'active'])
      ->setForeignKey('lead_owner_id')
      ->setProperty('former_addresses');

    $this->hasMany('LeadAssetsEntity')
      ->setConditions(['LeadAssetsEntity.status' => 'Active'])
      ->setForeignKey(['lead_owner_id', 'lead_id'])
      ->setBindingKey(['owner_id', 'lead_id'])
      ->setProperty('lead_assets');

    $this->hasMany('LeadLiabilitiesEntity')
      ->setConditions(['LeadLiabilitiesEntity.status' => 'Active'])
      ->setForeignKey(['lead_owner_id', 'lead_id'])
      ->setBindingKey(['owner_id', 'lead_id'])
      ->setProperty('liabilities');

    $this->hasMany('ConCreditPropSendEntity')
      ->setForeignKey('owner_id')
      ->setSort(['ConCreditPropSendEntity.created' => 'desc'])
      ->setProperty('con_credit_prop_sends');

    $this->hasMany('ConPrelimOwnerEntity')
      ->setForeignKey('owner_id')
      ->setSort(['ConPrelimOwnerEntity.created' => 'desc'])
      ->setProperty('con_preliminary_owner');

    $this->hasMany('LeadOwnerIncomeEntity')
      ->setForeignKey('owner_id')
      ->setProperty('incomes');

    $this->hasMany('LeadOwnerExpenseEntity')
      ->setForeignKey('owner_id')
      ->setProperty('expenses');

    $this->hasMany('PartnerLeadUploadsEntity')
      ->setForeignKey('owner_id')
      ->setConditions(['status' => 'Active'])
      ->setProperty('owner_uploads');
    
    $this->belongsToMany('HomeLoanPayOffOption', [
        'foreignKey' => 'owner_id',
        'targetForeignKey' => 'option_id',
        'through' => 'lead_owner_home_loan_pay_off_options', // The name of the join table
        'conditions' => ['type' => 'retirement'],
        'joinData' => false
    ])->setProperty('retired_pay_off_options');

    // Only use it when clone a lead
    $this->hasMany('LeadOwnerHomeLoanPayOffOption')
    ->setForeignKey('owner_id')
    ->setProperty('retired_pay_off_option_ids');

    $this->hasOne('CheckMobile', [
      'className' => 'TotalCheckCallEntity',
      'sort' => ['CheckMobile.created' => 'DESC'],
      'strategy' => 'select',
      'conditions' => ['CheckMobile.type' => 'mobile'],
      'foreignKey' => 'value',
      'bindingKey' => 'mobile',
      'propertyName' => 'check_mobile',
    ]);

    $this->hasOne('CheckEmail', [
      'className' => 'TotalCheckCallEntity',
      'sort' => ['CheckEmail.created' => 'DESC'],
      'strategy' => 'select',
      'conditions' => ['CheckEmail.type' => 'email'],
      'foreignKey' => 'value',
      'bindingKey' => 'email',
      'propertyName' => 'check_email',
    ]);
    
    $this->hasMany('ConIncomeShareEntity')
      ->setForeignKey('owner_id')
      ->setProperty('income_shared');

    $this->hasMany('LeadCreditScoreEntity')
      ->setForeignKey('owner_id')
      ->setProperty('owner_credit_scores');

    $this->hasMany('PartnerRequestedPrivacyForm', [
      'className' => 'LendSignatureRequestEntity',
      'sort' => ['PartnerRequestedPrivacyForm.created' => 'desc'],
      'strategy' => 'select',
    ])
      ->setConditions(['PartnerRequestedPrivacyForm.template_use_shortname' => LendSignTemplateUse::CommercialPrivacyForms])
      ->setForeignKey('owner_id')
      ->setProperty('partner_requested_privacy_forms');


    $this->hasOne('LatestPrivacy', [
      'className' => 'LendSignatureRequestEntity',
      'sort' => ['LatestPrivacy.created' => 'DESC'],
      'conditions' => ['LatestPrivacy.template_use_shortname' => LendSignTemplateUse::CommercialPrivacyForms],
      'strategy' => 'select',
      'foreignKey' => 'owner_id',
      'bindingKey' => 'owner_id',
      'propertyName' => 'latest_privacy',
    ]);

    $this->hasMany('LendSignatureRequestEntity')
      ->setForeignKey('owner_id')
      ->setSort(['LendSignatureRequestEntity.created' => 'desc'])
      ->setProperty('lend_signature_requests');

    $this->hasMany('ConCreditGuideQuote', [
      'className' => 'LendSignatureRequestEntity',
      'sort' => ['ConCreditGuideQuote.created' => 'desc'],
      'strategy' => 'select',
    ])
      ->setConditions(['ConCreditGuideQuote.template_use_shortname in ' => [LendSignTemplateUse::CreditGuideAndQuote, LendSignTemplateUse::NonSignatureCreditGuide]])
      ->setForeignKey('owner_id')
      ->setProperty('con_credit_guide_quotes');

    $this->hasOne('LatestIdCheck', [
        'className' => 'PartnerCrbIdcheckEntity',
        'sort' => ['LatestIdCheck.partner_crb_idcheck_id' => 'desc'],
        'strategy' => 'select',
        'limit' => 1
      ])
      ->setForeignKey('lead_owner_id')
      ->setProperty('latest_id_matrix');
      
  }


  public function _initializeSchema(TableSchema $schema): TableSchema
  {
    $schema->setColumnType('owner_type', 'integer');
    $schema->setColumnType('first_home_buyer', 'boolean');
    return parent::_initializeSchema($schema);
  }

  public function afterSave($event, $entity, $options) {
    if($entity->isDirty('home_owner') && $entity->point_of_contact){
      $leadPricingTable = TableRegistry::getTableLocator()->get('LeadPricingEntity');
      $leadPricing = $leadPricingTable->find()->where(['lead_id' => $entity->lead_id])->first();
      if($leadPricing){
        $leadPricing->backed_with_property = $entity->home_owner;
        $leadPricingTable->save($leadPricing);
      }else{
        $leadPricing = $leadPricingTable->newEntity([
          'lead_id' => $entity->lead_id,
          'backed_with_property' => $entity->home_owner
        ]);
        $leadPricingTable->save($leadPricing);
      }
    }
    
    if ($entity->status === 'active') {
      $leadAssociatedData = TableRegistry::getTableLocator()->get('LeadAssociatedDataEntity');
      $leadId = $entity->lead_id;
      $lad = $leadAssociatedData->find()->where(['lead_id' => $leadId])->first();
      if($lad){//should be always true
        if ($entity->consent == null) {
          $leadAssociatedData->patchEntity($lad, ['owner_consent' => 0]);
        }
        else{
          $allHaveConsent = $this->find()
              ->where([
                  'lead_id' => $leadId,
                  'status' => 'active',
                  'consent IS' => null
              ])
              ->count() === 0;
          $leadAssociatedData->patchEntity($lad, ['owner_consent' => $allHaveConsent ? 1 : 0]);
        }
        $leadAssociatedData->save($lad);
      }
      $lead = TableRegistry::getTableLocator()->get('LeadEntity')->get($entity->lead_id, [
        'fields' => [
          'lead_id',
          'partner_id',
        ],
        'contain' => [
          'PartnerEntity' => ['fields' => ['partner_id', 'status_system']],
          'PartnerEntity.PartnerFeatureFlagEntity' => ['fields' => ['partner_id', 'access_to_kanban_v2']],
        ]
      ]);

      if(($lead->partner->status_system === "manual") && !empty($lead->partner->feature_flag->access_to_kanban_v2)){
        //check to see if socket server event needs to be triggered due to data changes
        $fields = [
          'first_name',
          'last_name',
          'mobile',
          'phone',
          'avatar_image_url',
        ];
        $dirty = $entity->getDirty();
        $dirtyFields = array_intersect($fields, $dirty);
        if(count($dirtyFields) > 0){
            SocketHelper::kanbanUpdateLead($entity->lead_id);
        }
      }
    }

    if ($entity->isDirty('point_of_contact') || $entity->isDirty('is_guarantor') || $entity->isDirty('retire_age') || $entity->isDirty('retire_pay_off_plan') || $entity->isDirty('first_name') || $entity->isDirty('last_name') || $entity->isDirty('retired_pay_off_options')) {
      $leadHomeLoanCompliancesTable = TableRegistry::getTableLocator()->get('LeadHomeLoanCompliances');
      $leadHomeLoanCompliancesTable->resetPageStatus($entity->lead_id);
    }
  }
  
  public function beforeMarshal($event, $entity, $options)
  {
    $enc_fields = ['driving_licence_num', 'driving_licence_card_number', 'medicare_number', 'passport_number'];
    foreach($enc_fields as $enc_field){
      if(isset($entity[$enc_field])){
        $entity[$enc_field.'_e'] = EncryptionHelper::safeEncrypt($entity[$enc_field]);
        unset($entity[$enc_field]);
      }
    }
    if(isset($entity['security_answer_decrypted'])){
      $entity['security_answer'] = EncryptionHelper::safeEncrypt($entity['security_answer_decrypted']);
      unset($entity['security_answer_decrypted']);
    }
  }


  public function beforeSave($event, EntityInterface $entity, $options)
  {
    if ($entity->owner_ref && !$entity->owner_id) {
      $hashids = new Hashids('lead_owners', 7);
      $owner_id = $hashids->decode($entity->owner_ref)[0];
      $entity->owner_id = $owner_id;
      unset($entity->owner_ref);
    }
    $entity->country = strtoupper(getenv('REGION'));
    if ($entity->isNew()) {
      $entity = $this->__checkAccountLevel($entity);
    }
    $con_page_status_table = TableRegistry::getTableLocator()->get('ConPageStatusEntity');
    $con_page_status = $con_page_status_table->find('all')->where(['lead_id' => $entity->lead_id])->first();
    if (empty($con_page_status)) {
      return $entity;
    }
    $con_prelim_status = [
      'first_name',
      'last_name',
      'email',
      'dob',
      'mobile',
      'phone',
      'residency_status',
      'marital_status',
    ];

    $con_cgqp_status = [
      'first_name',
      'last_name',
    ];

    $con_lender_pricing_status = [
      'dob',
      'gender',
      'residency_status',
      'credit_history',
      'number_of_dependants',
      'dependant_details',
    ];

    $dirty = $entity->getDirty();
    $con_credit_proposal_status = [
      'first_name',
      'last_name',
      'phone',
      'mobile',
    ];

    $keep_nccp_status = false;
    if(isset($entity->keep_nccp_status) && $entity->keep_nccp_status === true){
      $keep_nccp_status = true;
      unset($entity->keep_nccp_status);
    }

    if (
      !empty(array_intersect($con_credit_proposal_status, $dirty))
      && in_array($con_page_status->con_credit_proposal_status, ['sent', 'overridden'])
      && $keep_nccp_status !== true) {
      $con_page_status_table->patchEntity($con_page_status, [
        'con_credit_proposal_status' => 'review required'
      ]);
    }
    if ($entity->isNew()) {
      $con_page_status_table->patchEntity($con_page_status, [
        'con_prelim_status' => 'incomplete'
      ]);
    } else {
      if (!empty(array_intersect($con_prelim_status, $dirty)) 
        && $con_page_status->con_prelim_status === 'complete' 
        && $keep_nccp_status !== true) {
        $con_page_status_table->patchEntity($con_page_status, [
          'con_prelim_status' => 'review required'
        ]);
      } elseif ($con_page_status->con_req_obj_status === 'incomplete') {
        $con_page_status_table->checkCompletePrelim($entity->lead_id);
      }
    }
    if (!empty(array_intersect($con_cgqp_status, $dirty)) && !$keep_nccp_status) {
      if (!$entity->isNew()) {
        $con_page_status_table->patchEntity($con_page_status, [
          'con_cgqp_status' => 'review required'
        ]);
      }
      // Inactive exists items in `con_credit_guide_quotes` table
      $lend_signature_requests_table = TableRegistry::getTableLocator()->get('LendSignatureRequestEntity');
      $lend_signature_request = $lend_signature_requests_table->find('all')
                              ->contain(['LeadOwnersEntity'])
                              ->where(['LeadOwnersEntity.lead_id' => $entity->lead_id]);
      if (!empty($lend_signature_request)) {
        $patches = [];
        foreach ($lend_signature_request as $item) {
          $patches[] = [
            'id' => $item['id'],
            'active' => false
          ];
        }
        $lend_signature_requests_table->patchEntities($lend_signature_request, $patches);
        foreach ($lend_signature_request as $item) {
          $lend_signature_requests_table->save($item);
        }
      }

      $lend_signature_request_applicant = TableRegistry::getTableLocator()->get('LendSignatureRequestEntity')->find()
      ->where([
        'owner_id' => $entity->owner_id,
        'template_use_shortname' => LendSignTemplateUse::CreditGuideAndQuote,
      ])
      ->order(['id' => 'DESC'])
      ->first();
      if ($lend_signature_request_applicant) {
        $lend_signature_request_applicant->status = 'review required';
        $table = TableRegistry::getTableLocator()->get('LendSignatureRequestEntity');
        if (!$table->save($lend_signature_request_applicant)) {
            Log::error('Unable to update LendSignatureRequest status');
        }
      }
    }
    // Check Lender Pricing Status:
    if (!empty(array_intersect($con_lender_pricing_status, $dirty)) && $con_page_status->con_lender_pricing_status === 'product selected') {
      $con_page_status_table->patchEntity($con_page_status, [
        'con_lender_pricing_status' => 'review required'
      ]);
    }

    $con_page_status_table->save($con_page_status);
    $AvatarService = new AvatarService;

    if (in_array('email', $dirty)) {
      $entity->avatar_image_url =  $AvatarService->getImageURL($entity->email);
    }
    
    $fieldsToTrim = ['first_name', 'last_name', 'middle_name'];
    foreach ($fieldsToTrim as $field) {
      if (isset($entity->{$field}) && $entity->{$field} != trim($entity->{$field})) {
        $entity->{$field} = trim($entity->{$field});
      }
    }
  }

  protected function __checkAccountLevel($entity)
  {
    if (!empty($entity->partner_account_people_id) || empty($entity->lead_id)) {
      return $entity;
    }

    // check leads.account_id exists
    // if NOT exists => stop process.
    // if exists, find same details account_people under account_id
    //    - if found one, assign people_id to this owner.
    //    - else, find account_people under partner_id
    //       -if found one, create new people under account_id with group_id = found_people_id.
    //       -else, create new people under account_id.

    $lead_table = TableRegistry::getTableLocator()->get('LeadEntity');
    $lead = $lead_table->get($entity->lead_id);
    if (empty($lead->account_id)) {
      return $entity;
    }

    $people_table = TableRegistry::getTableLocator()->get('PartnerAccountPeopleEntity');
    $matched_person = $people_table->find('all')
      ->where([
        'PartnerAccountPeopleEntity.partner_id' => $lead->partner_id,
        'PartnerAccountPeopleEntity.first_name' => $entity->first_name,
        'PartnerAccountPeopleEntity.last_name' => $entity->last_name,
        'PartnerAccountPeopleEntity.status' => 'active',
        'OR' => [
          'PartnerAccountPeopleEntity.email' => $entity->email,
          'PartnerAccountPeopleEntity.mobile' => $entity->mobile,
          'PartnerAccountPeopleEntity.phone' => $entity->phone,
        ]
      ])
      ->order(['PartnerAccountPeopleEntity.id' => 'asc'])
      ->first();

    if (!empty($matched_person)) {
      $entity->partner_account_people_id = $matched_person->id;
    } else {
      $people = json_decode(json_encode($entity), true);
      $people['partner_id'] = $lead->partner_id;
      $people['partner_account_link_people'] = [
        [
          'account_id' => $lead->account_id,
          'is_main_point_of_contact' => !empty($entity->point_of_contact),
        ]
      ];

      $people = $people_table->newEntity($people, [
        'associate' => [
          'PartnerAccountLinkPeopleEntity',
        ]
      ]);
      $people_table->save($people);
      $entity->partner_account_people_id = $people->id;
    }

    return $entity;
  }

}
