<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * PartnerChecklistItems Model
 */
class PartnerChecklistItemEntityTable extends Table
{
    /**
     * Initialize method
     *
     * @param array $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('partner_checklist_items');
        $this->setDisplayField('item_name');
        $this->setPrimaryKey('partner_checklist_item_id');

        $this->addBehavior('Timestamp');

        $this->belongsTo('PartnerChecklistEntity')
            ->setForeignKey('partner_checklist_id')
            ->setProperty('partner_checklist');

        $this->hasMany('LeadChecklistItemEntity')
            ->setForeign<PERSON>ey('partner_checklist_item_id')
            ->setProperty('lead_checklist_items');
    }
}
