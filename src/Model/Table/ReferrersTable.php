<?php
namespace App\Model\Table;

use Cake\ORM\Query;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;
use Hashids\Hashids;

/**
 * Referrers Model
 *
 * @property \App\Model\Table\PartnersTable|\Cake\ORM\Association\BelongsTo $Partners
 * @property \App\Model\Table\ReferrerAgreementsTable|\Cake\ORM\Association\HasMany $ReferrerAgreements
 * @property \App\Model\Table\ReferrerPeopleTable|\Cake\ORM\Association\HasMany $ReferrerPeople
 * @property \App\Model\Table\ReferrerRctiTable|\Cake\ORM\Association\HasMany $ReferrerRcti
 *
 * @method \App\Model\Entity\Referrer get($primaryKey, $options = [])
 * @method \App\Model\Entity\Referrer newEntity($data = null, array $options = [])
 * @method \App\Model\Entity\Referrer[] newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\Referrer|bool save(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\Referrer|bool saveOrFail(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\Referrer patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method \App\Model\Entity\Referrer[] patchEntities($entities, array $data, array $options = [])
 * @method \App\Model\Entity\Referrer findOrCreate($search, callable $callback = null, $options = [])
 *
 * @mixin \Cake\ORM\Behavior\TimestampBehavior
 */
class ReferrersTable extends Table
{

    /**
     * Initialize method
     *
     * @param array $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config)
    {
        parent::initialize($config);

        $this->setTable('referrers');
        $this->setDisplayField('id');
        $this->setPrimaryKey('id');

        $this->addBehavior('CustomTimestamp');
        $this->addBehavior('GenerateReferrerRef');

        $this->belongsTo('PartnerEntity', [
            'foreignKey' => 'partner_id',
            'joinType' => 'INNER'
        ])->setProperty('partner');
        $this->hasMany('ReferrerAgreements', [
            'conditions' => ['ReferrerAgreements.status' => 1],
            'foreignKey' => 'referrer_id'
        ]);
        $this->hasMany('ReferrerPeople', [
            'conditions' => ['ReferrerPeople.status' => 1],
            'foreignKey' => 'referrer_id'
        ]);
        $this->hasOne('PocPerson', ['className' => 'ReferrerPeople'])
            ->setConditions(['PocPerson.is_point_of_contact' => true, 'PocPerson.status' => true])
            ->setForeignKey('referrer_id')
            ->setProperty('referrer_person_poc');

        $this->hasMany('ReferrerRcti', [
            'conditions' => ['ReferrerRcti.status' => 1],
            'foreignKey' => 'referrer_id'
        ]);

        $this->hasMany('PartnerReferrerCategoryMapEntity', [
            'foreignKey' => 'referrer_id'
        ])
            ->setProperty('partner_referrer_category_map');

        $this->belongsToMany('PartnerReferrerCategoryEntity', [
            'through' => 'PartnerReferrerCategoryMapEntity',
            'foreignKey' => 'referrer_id',
            'targetForeignKey' => 'partner_referrer_category_id',
            'conditions' => ['PartnerReferrerCategoryEntity.active' => 1]
        ])
            ->setProperty('partner_referrer_categories');

        $this->hasMany('ReferrerPartnerUserAccessEntity', [
            'foreignKey' => 'referrer_id'
        ])
            ->setProperty('referrer_partner_user_access');

        $this->belongsToMany('PartnerUserEntity', [
            'through' => 'ReferrerPartnerUserAccessEntity',
            'foreignKey' => 'referrer_id',
            'targetForeignKey' => 'partner_user_id',
            'conditions' => [
                'PartnerUserEntity.active' => 1
            ]
        ])
            ->setProperty('partner_users');

        $this->belongsTo('LeadAbnLookupEntity', [
            'foreignKey' => 'abn_id',
            'joinType' => 'INNER'
            ])
            ->setProperty('abn_lookup');
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator)
    {
        $validator
            ->nonNegativeInteger('id')
            ->allowEmptyString('id', 'create');

        $validator
            ->scalar('region')
            ->allowEmptyString('region');

        $validator
            ->scalar('address')
            ->maxLength('address', 255)
            ->allowEmptyString('address');

        $validator
            ->scalar('suburb')
            ->maxLength('suburb', 255)
            ->allowEmptyString('suburb');

        $validator
            ->scalar('state')
            ->maxLength('state', 255)
            ->allowEmptyString('state');

        $validator
            ->scalar('postcode')
            ->maxLength('postcode', 12)
            ->allowEmptyString('postcode');

        $validator
            ->scalar('country')
            ->maxLength('country', 12)
            ->allowEmptyString('country');

        $validator
            ->scalar('unit_number')
            ->maxLength('unit_number', 20)
            ->allowEmptyString('unit_number');

        $validator
            ->scalar('street_number')
            ->maxLength('street_number', 20)
            ->allowEmptyString('street_number');

        $validator
            ->scalar('street_name')
            ->maxLength('street_name', 200)
            ->allowEmptyString('street_name');

        $validator
            ->scalar('street_type')
            ->maxLength('street_type', 20)
            ->allowEmptyString('street_type');

        $validator
            ->boolean('gst_status')
            ->requirePresence('gst_status', 'create')
            ->allowEmptyString('gst_status', false);

        $validator
            ->scalar('bank_account_name')
            ->maxLength('bank_account_name', 255)
            ->allowEmptyString('bank_account_name');

        $validator
            ->scalar('bank_bsb')
            ->maxLength('bank_bsb', 255)
            ->allowEmptyString('bank_bsb');

        $validator
            ->scalar('bank_account_number')
            ->maxLength('bank_account_number', 255)
            ->allowEmptyString('bank_account_number');

        $validator
            ->decimal('commercial_commission')
            ->allowEmptyString('commercial_commission');

        $validator
            ->scalar('commercial_commission_type')
            ->allowEmptyString('commercial_commission_type');

        $validator
            ->decimal('consumer_commission')
            ->allowEmptyString('consumer_commission');

        $validator
            ->scalar('consumer_commission_type')
            ->allowEmptyString('consumer_commission_type');

        $validator
            ->boolean('status')
            ->requirePresence('status', 'create')
            ->allowEmptyString('status', false);

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules)
    {
        $rules->add($rules->existsIn(['partner_id'], 'PartnerEntity'));

        return $rules;
    }

    public function ref2id($ref)
    {
        $hashids = new Hashids('referrer', 7);
        return $hashids->decode($ref)[0];
    }

    public function checkReferrerExists($referrer_lend_partner_id, $partner_id, $abn){
        $referrer = $this->find('all')
            ->contain(['LeadAbnLookupEntity'])
            ->where([
                'lend_partner_id' => $referrer_lend_partner_id,
                'partner_id' => $partner_id,
                'Referrers.status' => 1,
            ])
            ->first();
        if($referrer){
            return $referrer;
        }
        $referrer = $this->find('all')
            ->contain(['LeadAbnLookupEntity'])
            ->where([
                'partner_id' => $partner_id,
                'LeadAbnLookupEntity.abn' => $abn,
                'Referrers.status' => 1,
                'LeadAbnLookupEntity.active' => 1
            ])
            ->first();
        return $referrer;
    }
}
