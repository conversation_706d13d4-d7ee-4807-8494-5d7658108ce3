<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * LeadChecklists Model
 */
class LeadChecklistEntityTable extends Table
{
    /**
     * Initialize method
     *
     * @param array $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('lead_checklists');
        $this->setDisplayField('lead_checklist_id');
        $this->setPrimaryKey('lead_checklist_id');

        $this->belongsTo('LeadEntity')
            ->setForeignKey('lead_id')
            ->setProperty('lead');

        $this->belongsTo('PartnerChecklistEntity')
            ->setForeignKey('partner_checklist_id')
            ->setProperty('partner_checklist');

        $this->hasMany('LeadChecklistItemEntity')
            ->setFore<PERSON><PERSON>ey('lead_checklist_id')
            ->setProperty('checklist_items');
    }

}
