<?php

namespace App\Model\Table;

use App\Model\Entity\LeadAssociatedData;
use App\Model\Entity\LeadCallAttempt;
use Cake\ORM\Query;
use Cake\ORM\Table;
use Cake\ORM\TableRegistry;
use App\Lend\SocketHelper;
use App\Lend\KanbanHelper;

class LeadAssociatedDataEntityTable extends Table
{
    protected $_table = 'lead_associated_data';

    protected $_primaryKey = 'id';

    protected $_entityClass = LeadAssociatedData::class;

    public function initialize(array $config)
    {
        parent::initialize($config);
        
        $this->belongsTo('LeadEntity')
            ->setForeignKey('lead_id')
            ->setProperty('lead');

        $this->hasOne('LenderEntity')
            ->setBindingKey('max_lender_id')
            ->setForeignKey('lender_id')
            ->setProperty('lender');

        $this->hasOne('LeadNotesEntity')
            ->setBindingKey('max_note_id')
            ->setForeignKey('note_id')
            ->setProperty('note');

        $this->hasOne('PartnerLeadHistoryEntity')
            ->setBindingKey('max_history_id')
            ->setForeignKey('history_id')
            ->setProperty('history');

        $this->hasOne('IntermediaryLatestNote',['className' => 'LeadNotesEntity'])
            ->setBindingKey('max_intermediary_note_id')
            ->setForeignKey('note_id')
            ->setProperty('intermediary_note');
        
        $this->hasOne('SaleEntity')
            ->setBindingKey('max_sale_id')
            ->setForeignKey('sale_id')
            ->setProperty('sale');

        $this->hasOne('PartnerCallbackEntity')
            ->setBindingKey('min_callback_id')
            ->setForeignKey('callback_id')
            ->setProperty('partner_callback');

    }

    public function afterSave($event, $entity, $options) {

        $lead = TableRegistry::getTableLocator()->get('LeadEntity')->get($entity->lead_id, [
            'fields' => [
                'lead_id',
                'partner_id',
            ]
        ]);

        if(KanbanHelper::partnerHasKanbanV2Access($lead->partner_id)){
            if($entity->isDirty('lender_name')){
                SocketHelper::kanbanUpdateLead($entity->lead_id, [
                    'lender' => [
                        'old' => $entity->extractOriginal(['lender_name'])['lender_name'],
                        'new' => $entity->lender_name
                    ]
                ]);
            }
            else{
                $dirty = $entity->getDirty();
                $fields = [
                    'max_lender_id',
                    'max_note_id',
                ];
                $dirtyFields = array_intersect($fields, $dirty);
                if(count($dirtyFields) > 0){
                    SocketHelper::kanbanUpdateLead($entity->lead_id);
                }
            }
        }
    }
}
