<?php

namespace App\Model\Table;

use App\Model\Table\AppTable;
use Cake\ORM\TableRegistry;
use Cake\Mailer\Email;
use Cake\Core\Configure;
use Cake\Utility\Security;
use Cake\Log\Log;

use App\Lend\LendInternalAuth;
use Cake\Mailer\TransportFactory;
use Cake\Mailer\Transport\SmtpTransport;
use App\Auth\LendAuthenticate;



class PartnerNotificationsTable extends AppTable
{

    public $settings;
    public $groups;
    public $items;
    public $sms_history;

    function __construct($arg)
    {
        parent::__construct($arg);
        $this->settings     = TableRegistry::get('PartnerNotificationSettings');
        $this->groups       = TableRegistry::get('PartnerNotificationGroups');
        $this->items        = TableRegistry::get('PartnerNotificationItems');
        $this->sms_history  = TableRegistry::get('PartnerSmsHistory');
    }


    public function initialize(array $config)
    {
        parent::initialize($config);
    }

    public function getNotifications($params)
    {
        $q = TableRegistry::get('App')->getSelectQuery('partner_notifications', $params);
        return $this->DB->execute($q['query'], $q['values'])->fetchAll('assoc');
    }

    public function updateNotification($notifID, $params)
    {
        $q = $this->getUpdateQuery('partner_notifications', $params, array('notif_id' => $notifID));
      $resp = $this->DB->execute($q['query'], $q['values']);
      if($params['`read`'] !== null){
        // Get partner_user_id efficiently without expensive JOIN and handleOverrides
        $notification = $this->find()
            ->select(['partner_user_id'])
            ->where(['notif_id' => $notifID])
            ->first();
        
        if ($notification) {
            $partnerUserId = $notification['partner_user_id'];
            $notificationData = ['count' => $this->getUnreadCount($partnerUserId)];
            $message = ['type'=> 'notification_count', 'data'=> $notificationData];
            $this->sendSocketMessage([$partnerUserId], 'notifications', $message);
        }
      }
      return $resp;
    }

    public function markAllAsRead($partnerUserID)
    {
        $q = 'UPDATE partner_notifications SET `read` = ? WHERE `partner_user_id` = ? AND `read` IS NULL';
      $resp = $this->DB->execute($q, [date('Y-m-d H:i:s'), $partnerUserID]);
      $message = ['type'=> 'notification_count', 'data'=> ['count'=> 0]];
      $this->sendSocketMessage([$partnerUserID], 'notifications', $message);
      return $resp;
    }

    public function getNotification($notifID)
    {
        $sql   = 'SELECT pn.*, pni.*
                FROM partner_notifications pn
                LEFT JOIN partner_notification_items pni
                  ON pni.notif_item_id = pn.notif_item_id
                WHERE pn.notif_id = ?';
        $notif =  $this->DB->execute($sql, [$notifID])->fetch('assoc');
        if (!$notif) return $notif;

        $notif = $this->handleOverrides($notif);

        return $notif;
    }

    public function getFullNotifications($partnerUserIds, $limit, $page=1, $unreadOnly = false){
        if (!is_numeric($page)) return array();

        if ($page - 1 >= 0) $page--;
        
        // Handle both single ID and array of IDs
        if (!is_array($partnerUserIds)) {
            $partnerUserIds = [$partnerUserIds];
        }
        $placeholders = implode(',', array_fill(0, count($partnerUserIds), '?'));
        
        $sql =
            'SELECT pn.*, pni.*
              FROM partner_notifications pn
              LEFT JOIN partner_notification_items pni
              	ON pni.notif_item_id = pn.notif_item_id
              WHERE pn.partner_user_id IN (' . $placeholders . ')
              AND pni.delivery_type = "System"';

      if ($unreadOnly === "true" || $unreadOnly === true) {
        $sql .= ' AND pn.read IS NULL';
      }

      $sql .= ' ORDER BY pn.created DESC LIMIT ' . $limit . ' OFFSET ' . ($page * $limit);

        $notifs =  $this->DB->execute($sql, $partnerUserIds)->fetchAll('assoc');

        foreach ($notifs as $key => $notif) {
            $notifs[$key] = $this->handleOverrides($notif);
        }

        return $notifs;
    }

    public function getUnreadCount($partnerUserId)
    {
        $unread = $this->ReaderDB->execute('SELECT COUNT(*) as unread FROM partner_notifications pn WHERE pn.partner_user_id = ? AND pn.read IS NULL', [$partnerUserId])->fetch('assoc');
        return (int)$unread['unread'];
    }

    private function handleOverrides($notif)
    {
        if (!empty($notif['item_body_override']))
        $notif['body'] = $notif['item_body_override'];

        if (!empty($notif['item_subject_override']))
        $notif['subject'] = $notif['item_subject_override'];

        if (empty($notif['item_body_override']) and !empty($notif['for_lead_id'])) {
            $getLeadDetailsSettings = array('include_partner' => true, 'include_bs' => false, 'include_percentage' => true);
            if (strpos($notif['subject'], '[percent_complete]') === false && strpos($notif['body'], '[percent_complete]') === false) {
                $getLeadDetailsSettings['include_percentage'] = false;
            }
            $lead  = TableRegistry::get('Leads')->getLeadDetails($notif['for_lead_id'], $getLeadDetailsSettings);
            $partnerAccount = false;
            if (!empty($notif['for_partner_account_id'])) {
                $partnerAccount  = TableRegistry::getTableLocator()->get('PartnerAccountEntity')->get($notif['for_partner_account_id'], [
                    'contain' => [
                        'PartnerAccountLinkPeopleEntity.PartnerAccountPeopleEntity',
                        'PartnerAccountMetaEntity',
                    ]
                ]);
                $partnerAccount = json_decode(json_encode($partnerAccount), true);
                // convert people
                if (!empty($partnerAccount['partner_account_link_people'])) {
                    $partnerAccount['partner_account_people'] = array_map(function ($a) {
                        $person = $a['partner_account_people'];
                        $person['is_main_point_of_contact'] = $a['is_main_point_of_contact'];
                        return $person;
                    }, $partnerAccount['partner_account_link_people']);
                    unset($partnerAccount['partner_account_link_people']);
                }
            }
            $notif = $this->items->convertItem($notif, @$lead, false, false, false, $partnerAccount);
        }

        unset($notif['item_body_override'], $notif['item_subject_override']);

        return $notif;
    }

    /**
     * @param: $partnerUserId - only send to that user if it is passed in
     **/
    public function sendPartnerNotifications($partnerID, $notifLookupCode, $leadid = false, $adhocData = array(), $partnerUserId = null, $extraData = false, $parterAccountId = null)
    {
        $toSend       = array();
        $lead         = ($leadid) ? TableRegistry::get('Leads')->getLeadDetails($leadid, array('include_partner' => true)) : array();
        // If it's archived lead, do nothing.
        if (!empty($lead['lead']['is_archived'])) {
            Log::write('debug', $notifLookupCode . ' - Skipped! Sending notification for Archived lead (' . $leadid . ')');
            return $toSend;
        }

        $partner      = TableRegistry::get('Partners')->getPartner(array('partner_id' => $partnerID));
        if (!empty($leadid)) {
            //check partner_users.access_all_leads && records in partner_user_leads table
            $partnerUsers = TableRegistry::get('PartnerUsers')->getPartnerUsersWithAccessToGivenLead($partnerID, $leadid);
            if (empty($partnerUserId)) {
                //if specific partner user id is passed in, then ignore this step
                $leadReassignedToPartnerUserIds = TableRegistry::get('PartnerUsers')->getPartnerUserIdsAssignedToLead($partnerID, $leadid);
            }
        } else {
            $partnerUsers = TableRegistry::get('PartnerUsers')->getPartnerUser(array('partner_id' => $partnerID), true);
        }

        $partnerAccount = false;
        if ($parterAccountId) {
            $partnerAccount  = TableRegistry::getTableLocator()->get('PartnerAccountEntity')->get($parterAccountId, [
                'contain' => [
                    'PartnerAccountLinkPeopleEntity.PartnerAccountPeopleEntity',
                    'PartnerAccountMetaEntity',
                ]
            ]);
            $partnerAccount = json_decode(json_encode($partnerAccount), true);
            // convert people
            if (!empty($partnerAccount['partner_account_link_people'])) {
                $partnerAccount['partner_account_people'] = array_map(function ($a) {
                    $person = $a['partner_account_people'];
                    $person['is_main_point_of_contact'] = $a['is_main_point_of_contact'];
                    return $person;
                }, $partnerAccount['partner_account_link_people']);
                unset($partnerAccount['partner_account_link_people']);
            }
        }

        $lookupConditions = array('notif_lookup_code' => $notifLookupCode);
        //push partner_id key into lookup conditions where possible
        if (!empty($partnerID)) {
            //we need to look up `partner_notif_custom_items` table
            $lookupConditions['partner_id'] = $partnerID;
        }
        if (!empty($adhocData['delivery_type'])) $lookupConditions['delivery_type'] = $adhocData['delivery_type'];

        $notifications = $this->items->getItems($lookupConditions);

        if (!$notifications) {
            Log::write('debug', $notifLookupCode . ' - Not found related notifications', json_encode($lookupConditions));
            return [];
        }

        if ($notifLookupCode == 'AssignLead' && $partnerUserId && $leadid) {
            $partnerUsers = TableRegistry::get('PartnerUsers')->getPartnerUser(array('partner_user_id' => $partnerUserId), true);
        }
        if ($notifLookupCode == 'LeadAssignedNewUser' && $partnerUserId) {
            $partnerUsers = [TableRegistry::get('PartnerUsers')->get($partnerUserId)];
        }

        if (!$partnerUsers) {
            Log::write('debug', $notifLookupCode . ' - Partner Users is empty.', json_encode(['partner_id' => $partnerID, 'partner_user_id' => $partnerUserId, 'lead_id' => $leadid]));
        }

        $swap_body = false;
        $footer = $this->getEmailFooter($partnerUserId);
        if ($footer['success'] && $footer['content'] !== null) {
            $swap_body = true;
        }


        foreach ($notifications as $notification):
            $custom_footer = false;
            switch ($notification['recipient']):
                case 'Partner':
                    // If partner is not active, skip to send a notification:
                    if (empty($partner['active']) and !in_array($notifLookupCode, array('WelGetStar', 'WelGetStarNz', 'BrokerVerifyEmail', 'RandPwd'))) {
                        Log::write('debug', $notifLookupCode . ' - Skipped Due to inactive partner account', json_encode(['partner' => $partner, 'partner_user_id' => $partnerUserId]));
                        continue 2;
                    }

                    $original_notification = $notification;
                    foreach ($partnerUsers as $partnerUser) {
                        Log::write('debug', $notifLookupCode . ' partnerUsers notifications start ======> :' . json_encode($partnerUser['email']));
                        if (empty($partnerUserId) && !empty($leadReassignedToPartnerUserIds)) {
                            //no specific user is passed in and has some users reassigned to this lead
                            if (!in_array($partnerUser['partner_user_id'], $leadReassignedToPartnerUserIds)) {
                                Log::write('debug', $notifLookupCode . 'Notification skipped due to lead reassigned to another user ==== continue');
                                continue;
                            }
                        } elseif (!empty($partnerUserId) && $partnerUser['partner_user_id'] != $partnerUserId) {
                            Log::write('debug', $notifLookupCode . 'Notification skipped due to not the user expected ==== continue');
                            continue;
                        }

                        // If Lead is not assigned to any Partner User, send notification only to admin.
                        if (
                            empty($partnerUserId)
                            && empty($leadReassignedToPartnerUserIds)
                            && $partnerUser['account_admin'] == "0"
                        ) {
                            Log::write('debug', $notifLookupCode . 'Notification skipped due to no partner user assigned to lead ==== continue');
                            continue;
                        }

                        $ignore_codes = [
                            'WelGetStar',
                            'WelGetStarNz',
                            'BrokerVerifyEmail',
                            'ResetPassw',
                            'BrokerVerifyEmail2',
                            'RandPwd',
                            'InviteUser',
                            'DocusignaCompNotify',
                            'UpdateSMTP2FA',
                            'DataImportFinished',
                            'SmsFailed',
                            'UpdateSMTP2FA',
                            'TaskOverdue',
                        ];
                        if (!in_array($notifLookupCode, $ignore_codes)) {
                            $failedConditions = [];
                            if ($partnerUser['notification_recipient'] != 1) $failedConditions[] = 'notification_recipient';
                            if ($partnerUser['active'] != 1) $failedConditions[] = 'active';
                            if ($partnerUser['email_verified'] != 1) $failedConditions[] = 'email_verified';

                            if (!empty($failedConditions)) {
                                Log::write('debug', $notifLookupCode . ' Notification skipped due to failed conditions: ==== continue' . implode(', ', $failedConditions));
                                continue;
                            }
                        }

                        if (!empty($adhocData['credit_reports_section']))
                        $adhocData['credit_reports_section'] = $this->generateCreditReportsLink($extraData['lead_ref'], $partnerUser['email']);

                        // Convert the codes (e.g. [org_name]) to actual data:
                        $partnerUser['Partner'] = $partner;
                        $notification = $this->items->convertItem($original_notification, $lead, $partnerUser, $adhocData, $extraData, $partnerAccount);


                        // important  - live test prod if issues Log::write('error', 'notifications new :'.json_encode($notification));

                        $remove_applicant_check_for = ['TaskOverdueIn30', 'TaskDueNow' ,'TaskOverdue'];

                        if(empty($extraData['lead_owners_poc_full_name']) && in_array($notifLookupCode, $remove_applicant_check_for)){
                         $notification['body'] = preg_replace('/\nApplicant: .*?\n/', "\n", $notification['body']);
                        }

                        $notification['for_lead_id'] = $leadid;
                        $userSettings  = $this->settings->getSettings(array('ref_id' => $partnerUser['partner_user_id'], 'ref_type' => 'partner_user_id'));

                        // Check subscription settings...

                        // so, which field in the partner_notification_settings table are we looking at?
                        $field = ($notification['delivery_type'] === 'System') ? false : strtolower($notification['delivery_type']) . '_on';
                        // $skip_email_codes = ['WelGetStar','BrokerVerifyEmail', 'PartnerNewLead', 'ResetPassw'];
                        // Find this user's setting (from partner_notification_settings)
                        $settingKey = (!empty($userSettings) and !in_array($notifLookupCode, $ignore_codes)) ? array_search($notification['notif_group_id'], array_column($userSettings, 'notif_group_id')) : false;

                        // They don't have a setting for it, skip
                        if ($settingKey === false and !in_array($notifLookupCode, $ignore_codes)) {
                            Log::write('debug', $notifLookupCode . ' Notification skipped due to no setting key ==== continue');
                            continue;
                        }

                        // If it is a inviting new user request then manually change email address to the new invited user's email address
                        // But if new invited user's email not exist, then skip
                        if ($notifLookupCode == 'InviteUser') {
                            if (isset($adhocData['invited_email']))
                                $partnerUser['email'] = $adhocData['invited_email'];
                            else {
                                Log::write('debug', $notifLookupCode . ' InviteUser: No invited email found ==== continue');
                                continue;
                            }
                        }

                        // NOTE: All ones for the app are sent regardless of subscription ("System" delivery type), hence "!$field"
                        if (!$field or in_array($notifLookupCode, $ignore_codes) or ($field and $userSettings[$settingKey][$field])) {
                            $toSend[] = array('recipients' => $partnerUser, 'notifications' => $notification);
                            Log::write('debug', $notifLookupCode . ' $toSend[] success ====> notifications new :' . json_encode([
                                'notification' => $notification,
                                'field' => @$field,
                                'settingKey' => @$settingKey,
                                'userSettings' => @$userSettings[$settingKey],
                            ]));
                            
                            // If this is a Partner notification and delivery type is SMS, send to additional recipients
                            // this is inside recipient Partner loop //
                            if ($notification['delivery_type'] === 'SMS') {
                                if (!empty($leadid)) {
                                    // Get the partner users associated with this lead
                                    $partnerUserTable = TableRegistry::getTableLocator()->get('PartnerUserEntity');
                                    
                                    // Query to get partner users for this lead
                                    $notificationUsers = $partnerUserTable->find()
                                        ->select(['email', 'name', 'mobile', 'partner_id', 'partner_user_id'])
                                        ->innerJoin(
                                            ['LeadNotificationPartnerUsersEntity' => 'lead_notification_partner_users'],
                                            ['LeadNotificationPartnerUsersEntity.partner_user_id = PartnerUserEntity.partner_user_id']
                                        )
                                        ->where([
                                            'LeadNotificationPartnerUsersEntity.lead_id' => $leadid,
                                            'LeadNotificationPartnerUsersEntity.status' => 'ACCESS'
                                        ])
                                        ->all();
                    
                                    // Add each user's email as CC
                                    foreach ($notificationUsers as $partner_user) {
                                        $toSend[] = array('recipients' => $partner_user, 'notifications' => $notification);
                                        Log::write('debug', $notifLookupCode . ' Additional SMS recipient added: ' . $partner_user->mobile);
                                    }
                                }    
                            }
                        } else {
                            Log::write('debug', $notifLookupCode . ' $toSend[] failed ====> notifications new :' . json_encode($notification));
                        }

                        Log::write('debug', $notifLookupCode . ' partnerUsers notifications end[if success] ======> :' . json_encode($partnerUser['email']));
                    }
                    break;

                case 'Client':
                    if (!empty($adhocData['upload_login_code'])) {
                        $adhocData['client_login_code'] .= '&code=' . $adhocData['upload_login_code'];
                    } elseif (!empty($adhocData['additionalCode'])) {
                        $adhocData['client_login_code'] = TableRegistry::get('Leads')->createAutoLoginCode($lead['lead']['lead_id'], $adhocData['additionalCode']);
                        $adhocData['client_login_code'] .= '&code=' . $notifLookupCode;
                    }
                    $partnerUser = [];
                    if (!empty($partnerUserId)) {
                        foreach ($partnerUsers as $partnerUser) {
                            if ($partnerUser['partner_user_id'] == $partnerUserId)
                                break;
                        }
                    } else {
                        foreach ($partnerUsers as $partnerUser) {
                            if ($partnerUser['account_admin'] == 1) {
                                break;
                            }
                        }
                    }

                    $partnerUser['Partner'] = $partner;
                    if ($swap_body && $notification['body_without_footer'] !== null && $notification['delivery_type'] === 'Email') {
                        $notification['body'] = $notification['body_without_footer'];
                        $custom_footer = true;
                    }
                    $notification = $this->items->convertItem($notification, $lead, $partnerUser, $adhocData, $extraData, $partnerAccount);
                    $notification['for_lead_id'] = $leadid;
                    $notification['partner_id'] = !empty($lead['lead']['partner_id']) ? $lead['lead']['partner_id'] : null;

                    // Web Affiliate partners can turn Client SMS on/off: (Default: off)
                    if (strtolower($notification['delivery_type']) === 'sms') {
                        //Ask applicant SMSes only need BurstSMS credentials, there is no setting to restirct them
                        if (in_array($notifLookupCode, ['AskApplicantLeadForm', 'CommercialAskApplicantLeadForm'])) {
                            $toSend[] = array('recipients' => $lead['lead_owner'], 'notifications' => $notification);
                        } else {
                            $userSettings = $this->settings->getSettings(array('ref_id' => $partnerID, 'ref_type' => 'partner_id'));
                            $settingKey = !empty($userSettings) ? array_search($notification['notif_group_id'], array_column($userSettings, 'notif_group_id')) : false;
                            if ($settingKey === false) break;

                            if ($userSettings[$settingKey]['sms_on'])
                                $toSend[] = array('recipients' => $lead['lead_owner'], 'notifications' => $notification);
                        }
                    } else {
                        $reply_to = null;
                        if (!empty($partnerUser['email'])) {
                            $reply_to = $partnerUser['email'];
                            if (empty($adhocData['partner_email'])) {
                                $adhocData['partner_email'] = $partnerUser['email'];
                            }
                        } else {
                            foreach ($partnerUsers as $pu) {
                                if ($pu['account_admin'] == 1) {
                                    $reply_to = $pu['email'];
                                    break;
                                }
                            }
                        }

                        if (!empty($extraData)) {
                            $toSend[] = [
                                'recipients' => [
                                    'email' => $extraData['client_email'],
                                    'from' => $extraData['from'],
                                ],
                                'notifications' => $notification,
                            ];
                        } else {
                            $toSend[] = [
                                'recipients' => [
                                    'email' => ($adhocData['to'] ? $adhocData['to'] : $lead['lead_owner']['email']),
                                    'reply_to' => $reply_to,
                                    'from' => !empty($adhocData['partner_email']) ?
                                                [
                                        'email' => $adhocData['partner_email'],
                                        'name' => (!empty($partner['company_name']) ? $partner['company_name'] : $partner['organisation_name'])
                                                ] :
                                                null
                                ],
                                'notifications' => $notification
                            ];
                        }
                    }

                    break;
                case 'LeadApplicant':
                    if ($swap_body && $notification['body_without_footer'] !== null && $notification['delivery_type'] === 'Email') {
                        $notification['body'] = $notification['body_without_footer'];
                        $custom_footer = true;
                    }
                    $notification = $this->items->convertItem($notification, $lead, false, false, $extraData, $partnerAccount);
                    $notification['for_lead_id'] = $leadid;
                    $notification['partner_id'] = !empty($lead['lead']['partner_id']) ? $lead['lead']['partner_id'] : null;
                    $to = !empty($adhocData['to']) ? $adhocData['to'] : $lead['lead_owner']['email'];
                    if (!empty($extraData['applicant_email'])) {
                        $to = $extraData['applicant_email'];
                    }
                    if (!empty($adhocData['partner_email'])) {
                        $from = $adhocData['partner_email'];
                    }
                    if (!empty($extraData['from'])) {
                        $from = $extraData['from'];
                    }
                    $toSend[] = [
                        'recipients' => [
                            'email' => $to,
                            'from' => $from,
                        ],
                        'notifications' => $notification
                    ];
                    break;
                case 'ThirdParty':
                    $notification = $this->items->convertItem($notification, $lead, false, false, $extraData);
                    $to = $extraData['to'];
                    $from = $extraData['from'];
                    $toSend[] = [
                        'recipients' => [
                            'email' => $to,
                            'from' => $from,
                        ],
                        'notifications' => $notification
                    ];
                    break;
                case 'Referrer':

                    if ($swap_body && $notification['body_without_footer'] !== null && $notification['delivery_type'] === 'Email') {
                        $notification['body'] = $notification['body_without_footer'];
                        $custom_footer = true;
                    }
                    $notification = $this->items->convertItem($notification, [], ['Partner' => $partner], false, $extraData);

                    $to = $extraData['to'];
                    $from = $extraData['from'];
                    $toSend[] = [
                        'recipients' => [
                            'email' => $to,
                            'from' => $from,
                        ],
                        'notifications' => $notification
                    ];
                    break;

                case 'ReferrerPerson':
                    if($partnerUserId){
                        //missing in stgainh //
                        if ($swap_body && $notification['body_without_footer'] !== null && $notification['delivery_type'] === 'Email') {
                            $notification['body'] = $notification['body_without_footer'];
                            $custom_footer = true;
                        }

                        $partner_user_table = TableRegistry::getTableLocator()->get('PartnerUsers');
                        $partnerUser = $partner_user_table->get($partnerUserId);
                        if($partnerUser){
                            $notification = $this->items->convertItem($notification, [], $partnerUser, false, $extraData);
                            $to = $extraData['to'];
                            $from = $extraData['from'];
                            $toSend[] = [
                                'recipients' => [
                                    'email' => $to,
                                    'from' => $from,
                                ],
                                'notifications' => $notification
                            ];
                        }
                    }
                    
                    break;

                case 'Lend':
                    // Not possible to set 'Email', 'SMS' and 'System' for Lend. Only available for 'Slack'
                    // NOTE:: set Phil's email to receive email notification when Set 'Email' for 'Lend':
                    if (in_array($notifLookupCode, ['WelGetStar', 'WelGetStarNz'])) {
                        $partnerUsers[0]['Partner'] = $partner;
                        $notification = $this->items->convertItem($notification, $lead, $partnerUsers[0], $adhocData, $extraData, $partnerAccount);
                    } else {
                        $notification = $this->items->convertItem($notification, $lead, array('Partner' => $partner), $adhocData, $extraData, $partnerAccount);
                    }
                    $notification['for_lead_id'] = $leadid;
                    if ($notifLookupCode === 'LenderProductDraft') {
                        // $toSend[] = array('recipients' => array('email' => '<EMAIL>'), 'notifications' => $notification);
                        // send to Phil, Jonathan and Donelle
                        array_push($toSend, array('recipients' => array('email' => '<EMAIL>'), 'notifications' => $notification));
                    } else
                        $toSend[] = array('recipients' => array('email' => '<EMAIL>'), 'notifications' => $notification);
                    break;

                case 'Lender':
                    // To avoid breaking anything, handle both "[0][partner_id]" and "[partner_id]"...
                    $lenderDetails = !empty($lead['lender'][0]['partner_id'])
                        ? $lead['lender'][0]
                        : (!empty($lead['lender']['partner_id']) ? $lead['lender'] : NULL);

                    if ($notifLookupCode == 'LenderTmpPass' && isset($adhocData['lender_contact_name'], $adhocData['lender_email'], $adhocData['lender_password'])) {
                        $notification = $this->items->convertItem($notification, $lead, false, $adhocData, $extraData, $partnerAccount);
                        $toSend[] = array('recipients' => array('partner_user_id' => null, 'email' => $adhocData['lender_email']), 'notifications' => $notification);
                    } elseif ($notifLookupCode == 'LenderLeadRevoked') {
                        $notification = $this->items->convertItem($notification, $lead, false, $adhocData, $extraData, $partnerAccount);
                        $toSend[] = array('recipients' => array('partner_user_id' => null, 'email' => $adhocData['lender_email']), 'notifications' => $notification);
                    } elseif ($notifLookupCode == 'EmailSubmit') {
                        $notification = $this->items->convertItem($notification, $lead, false, $adhocData, $extraData, $partnerAccount);
                        $toSend[] = array('recipients' => array('partner_user_id' => null, 'email' => $adhocData['lender_email']), 'notifications' => $notification);
                    } elseif ($lenderDetails) {
                        $lenders = TableRegistry::getTableLocator()->get('PartnerUsers')->getPartnerUser(array('partner_id' => $lenderDetails['partner_id'], 'account_admin' => 1), true);
                        if (!empty($lenders) && $notifLookupCode !== 'SenToLndr') {
                            foreach ($lenders as $lender) {
                                $notification = $this->items->convertItem($notification, $lead, $lender, $adhocData, $extraData, $partnerAccount);
                                $toSend[] = array('recipients' => $lender, 'notifications' => $notification);
                            }
                        }
                    }
                    break;
            endswitch;
        endforeach;


        if ($swap_body && $custom_footer) {
            $toSend[0]['notifications']['body'] .= $footer['content'];
        }
        // If there's no notifications to send, it's pointless continuing
        if (empty($toSend)) {
            Log::write('debug', $notifLookupCode . '- $toSend[] is empty' . json_encode($notification));
            return [];
        }

        foreach ($toSend as $key => $send):
            $send['notifications']['extra'] = $extraData;
            if (stripos($send['notifications']['delivery_type'], 'slack') !== false):
                $toSend[$key]['successfully_sent'] = $this->sendSlackNotification($send['notifications']);
            else:
                switch ($send['notifications']['delivery_type']) {
                    case 'Email':
                        $originalTransport = $this->sendEmailNotification($send['recipients'], $send['notifications'], $lead, !empty($send['notifications']['custom']), $partnerUserId);
                        $toSend[$key]['successfully_sent'] = $originalTransport['success'];
                        $toSend[$key]['lastResponse'] = $originalTransport['lastResponse'];
                        break;
                    case 'SMS':
                        $toSend[$key]['successfully_sent'] = $this->sendSMSNotification($send['recipients'], $send['notifications']);
                        break;
                    case 'System':
                        $toSend[$key]['successfully_sent'] = $this->sendSystemNotification($send['recipients'], $send['notifications']);
                        break;
                }
            endif;
        endforeach;

        return $toSend;
    }


    // 1 - Get the notification(s) using the $notifLookupCode.
    // 2 - Loop the notifications
    // 2.1 - During loop, remove any the user is not subscribed to (using "notif_group_id")
    // 2.2 - During loop, change any tags to real data (e.g. [org_name])
    // 3 - Using the "delivery_type", send the notification (Email, SMS, System).
    // 3.1 (Email) - Send email to user
    // 3.2 (SMS) - Send SMS to user
    // 3.3 (System) - Set the user an unread notification
    // 4 - Write all to log
    // public function sendPartnerUse

    // add new forth parameter - $custom - opitonal - true or false
    private function sendEmailNotification($recipient, $item, $lead, $custom = false, $partner_user_id = null)
    {
        Log::info('---- sendEmailNotification START', ['scope' => 'notifications']);
        Log::info(json_encode([
            'recipient' => $recipient,
            'item' => $item,
            'lead_id' => $lead['lead']['lead_id'],
            'custom' => $custom,
            'partner_user_id' => $partner_user_id,
        ]), ['scope' => 'notifications']);
        $BCC_needed = [];
        $smtp_custom = false;

        //overwrite recipient's email address in dev environment where possible
        $mailSettings = 'notifications';
        if (getenv('LEND_ENV') === '0') {
            if (!empty(getenv('MAILTRAP_USERNAME')) && !empty(getenv('MAILTRAP_PASSWORD'))) {
                $mailSettings = 'mailtrap';
            } else {
                $recipient['email'] = getenv('DEV_EMAIL_RECIPIENT');
                $recipient['reply_to'] = getenv('DEV_EMAIL_RECIPIENT');
            }
        }

        $mail = new Email($mailSettings);

        // partner user if logic to get the SMTP details ==> PHil's requirment  //
        if ($item['notif_lookup_code'] == 'UpdateSMTP2FA') {
            $partner_user_id = $recipient['partner_user_id'];
            $partner_id =      $recipient['partner_id'];
        } else {

            $partner_id = $lead['partner']['partner_id'];

            if (empty($partner_user_id)) {
                // this is priority to get the partner-user-id
                $partnerUserLeadsTable = TableRegistry::getTableLocator()->get('PartnerUserLeadsEntity');
                $partnerUsersAccessToLead = $partnerUserLeadsTable->find()
                    ->where([
                        'lead_id' => $lead['lead']['lead_id'],
                        'status' => 'ACCESS'
                    ])
                    ->first();
                $partner_user_id = $partnerUsersAccessToLead->partner_user_id; // this is priority  01


                if (empty($partner_user_id)) {
                    if (!empty($_COOKIE['auth_token'])) {
                        $result = LendAuthenticate::decode(['allowedAlgs' => ['HS256']], $_COOKIE['auth_token']);

                        if ($result['success']) {
                            $userEmail = $result['payload']->sub;

                            $user = TableRegistry::getTableLocator()->get('PartnerUserEntity')->find('all')
                                ->where(['email' => $userEmail])
                                ->first();

                            if (!empty($user)) {
                                $partner_user_id = $user->partner_user_id; // this is priority  02
                            }
                        }
                    }
                }


                if (empty($partner_user_id)) {
                    $partnerUsersTable = TableRegistry::getTableLocator()->get('PartnerUserEntity');
                    $partnerUser = $partnerUsersTable->find()
                        ->where(['partner_id' => $partner_id, 'account_admin' => true, 'active' => true])
                        ->order(['partner_user_id' => 'ASC'])
                        ->first();
                    $partner_user_id = $partnerUser->partner_user_id; // this is priority  03
                }
            }
        }
        // partner user if logic //

        if (($item['recipient'] == 'Client' || $item['recipient'] == 'thirdparty' || $item['recipient'] == 'referrer')
            ||
            ($item['notif_lookup_code'] == 'UpdateSMTP2FA')
        ) {

            $smtpCredentialsEntity = TableRegistry::getTableLocator()->get('PartnerExtraCredentialsEntity')
                ->getCredentialsDecrypted($partner_id, 'smtp', $partner_user_id);
            $smtpCredentials = $smtpCredentialsEntity ? $smtpCredentialsEntity->toArray() : null;

            $smtpVeified = TableRegistry::getTableLocator()->get('PartnerTwofaCodes')->find('all')
                ->where([
                    'partner_user_id' => $partner_user_id,
                    'verification_type' => 'smtp',
                ])
                ->order(['id' => 'DESC'])
                ->first();





            if (!empty($smtpCredentials['credentials']) && ($smtpVeified->verified == 1 || $item['notif_lookup_code'] == 'UpdateSMTP2FA')) {
                $encryption = $smtpCredentials['credentials']->smtp_encryption;

                $transportConfig = [
                    'host' => $smtpCredentials['credentials']->host,
                    'port' => $smtpCredentials['credentials']->port,
                    'username' => $smtpCredentials['credentials']->username,
                    'password' => $smtpCredentials['credentials']->password,
                    'className' => 'Smtp',
                    'log' => true,
                ];

                if ($encryption === 'tls') {
                    $transportConfig['tls'] = true;
                } elseif ($encryption === 'ssl') {
                    $transportConfig['host'] = 'ssl://' . $transportConfig['host'];
                }

                $transport = new SmtpTransport($transportConfig);
                $mail->setTransport($transport);
                $originalTransport = $mail->getTransport();

                $smtp_custom = true;

                $mail->setFrom([$smtpCredentials['credentials']->from => $smtpCredentials['credentials']->from_name]);
            }
        }

        $mail->addHeaders(['X-SMTPAPI' => json_encode(array('category' => array('notification')))]);

        $layout = 'default';
        if (getenv('REGION', true) === 'nz') {
            $layout = 'defaultNz';
        }

        if ($custom) {
            if ($item['recipient'] !== 'Client') {
                if (getenv('REGION', true) === 'au') {
                    $layout = 'customNotification';
                } elseif (getenv('REGION', true) === 'nz') {
                    $layout = 'customNotificationNz';
                }
            } else {
                $layout = 'customNotificationClient';
            }
        } elseif ($item['recipient'] === 'Client') {
            $layout = 'client';
        } elseif ($item['recipient'] === 'Referrer') {
            $layout = 'client';
        } elseif (!empty($item['notif_lookup_code']) && in_array($item['notif_lookup_code'], array('PenBusApp1', 'PenBusApp2'))) {
            $layout = 'noHeaderFooter';
        } elseif ($item['recipient'] === 'ThirdParty') {
            $layout = 'client';
        }

        $mail->template('notifications', $layout);
        $mail->emailFormat('html');
        $mail->to($recipient['email']);

        if (!empty($recipient['reply_to']))
        $mail->replyTo($recipient['reply_to']);

        if (!empty($recipient['from'])) {
            if (is_array($recipient['from'])) {
                $mail->setFrom($recipient['from']['email'], $recipient['from']['name']);
            } else {
                $mail->setFrom($recipient['from']);
            }
            if (is_array($recipient['from'])) {
                $mail->setSender($recipient['from']['email'], $recipient['from']['name']);
            } else {
                $mail->setSender($recipient['from']);
            }
        }

        // DO NOT BCC to ourselves if the lookup code is 'AppBSRequest'
        if ($recipient['email'] != '<EMAIL>' && getenv('LEND_ENV') != '0' && $item['notif_lookup_code'] != 'AppBSRequest') {
            // $mail->addBcc("<EMAIL>");
            array_push($BCC_needed, "<EMAIL>");
        }

        // If lookup code is 'SendToAppl' or 'AppBSRequest', partner email will be added into BCC
        $code_adding_partner_email = [
            'SendToAppl',
            'AppBSRequest',
            'AskAttach',
            'AppConsentRequest',
            'SendToApplMultiTasks',
            'EmailSubmit',
            'AskApplicantLeadForm',
            'AskApplicantLeadFinl',
            'AskApplicantDocsFinl',
            'CommercialAskApplicantLeadForm',
            'CommercialAskApplicantLeadFinl',
            'CommercialAskApplicantDocsFinl',
            'InvoiceRequestEmail',
        ];
        //if lead is assigned to a partner user, change sender to that user
        $assignee_sender = [
            'AppBSRequest',
            'AskAttach',
            'QuoteLink',
            'AppConsentRequest',
            'SendToApplMultiTasks',
            'AppliAskAttach',
            'BSFailAcc',
            'CreditProposalEmail',
            'AskApplicantLeadForm',
            'CommercialAskApplicantLeadForm',
            'CommercialAskApplicantDocs'
        ];
        if (
            in_array($item['notif_lookup_code'], $code_adding_partner_email)
            || in_array($item['notif_lookup_code'], $assignee_sender)
        ) {
            $partner_users = TableRegistry::get('PartnerUsers')->getPartnerUser(array('partner_id' => $lead['lead']['partner_id'], 'active' => '1'), true);

            //check if any user assigned to this lead
            if (!empty($lead['lead']['lead_id'])) {
                $leadReassignedToPartnerUserIds = TableRegistry::get('PartnerUsers')->getPartnerUserIdsAssignedToLead($lead['lead']['partner_id'], $lead['lead']['lead_id']);
            }
            if ($partner_users) {
                if (in_array($item['notif_lookup_code'], $code_adding_partner_email)) {
                    if (!empty($leadReassignedToPartnerUserIds)) {
                        foreach ($partner_users as $partnerUser) {
                            //if assigned to any partner users, then bcc them
                            if (!empty($leadReassignedToPartnerUserIds) && !in_array($partnerUser['partner_user_id'], $leadReassignedToPartnerUserIds)) continue;
                            $mail->addCc($partnerUser['email']);
                        }
                    } else {
                        foreach ($partner_users as $partnerUser) {
                            if ($partnerUser['point_of_contact'] == 1) {
                                $mail->addCc($partnerUser['email']);
                            }
                        }
                    }
                }
                if (in_array($item['notif_lookup_code'], $assignee_sender)) {
                    foreach ($partner_users as $partnerUser) {
                        if (!empty($leadReassignedToPartnerUserIds) && in_array($partnerUser['partner_user_id'], $leadReassignedToPartnerUserIds)) { //should only be 1 as per business logic
                            $mail->setFrom($partnerUser['email'], $partnerUser['name']);
                            $mail->replyTo($partnerUser['email'], $partnerUser['name']);
                        }
                    }
                }
            }
        }



     

        // Add CC recipients for Partner notifications
        if ($item['recipient'] === 'Partner') {
            $lead_id = $item['for_lead_id'];
            
            if (!empty($lead_id)) {
                // Get the partner users associated with this lead
                $partnerUserTable = TableRegistry::getTableLocator()->get('PartnerUserEntity');
                
                // Query to get partner users for this lead
                $notificationUsers = $partnerUserTable->find()
                    ->select(['email', 'name'])
                    ->innerJoin(
                        ['LeadNotificationPartnerUsersEntity' => 'lead_notification_partner_users'],
                        ['LeadNotificationPartnerUsersEntity.partner_user_id = PartnerUserEntity.partner_user_id']
                    )
                    ->where([
                        'LeadNotificationPartnerUsersEntity.lead_id' => $lead_id,
                        'LeadNotificationPartnerUsersEntity.status' => 'ACCESS'
                    ])
                    ->all();

                // Add each user's email as CC
                foreach ($notificationUsers as $user) {
                    if (!empty($user->email)) {
                        $mail->addCc($user->email);
                        Log::write('debug', 'Added CC from partner_users: ' . $user->email);
                    }
                }

            }
        }

        if ($smtp_custom && $smtpCredentials) {
            $mail->setFrom([$smtpCredentials['credentials']->from => $smtpCredentials['credentials']->from_name]);
            $mail->replyTo($smtpCredentials['credentials']->reply_to);
        }

        Log::info($mail, ['scope' => 'notifications']);

        $mail->subject($item['subject']);
        $mail->viewVars(array(
            'data'        => $item,
            'lead'        => $lead,
            'lendconfig'  => Configure::read('Lend'),
        ));

        if (!empty($item['extra']['attachments'])) {
            $mail->addAttachments($item['extra']['attachments']);
        }

        try {
            $success = $mail->send();
            Log::info('---- Success sendEmailNotification FINISH', ['scope' => 'notifications']);
        } catch (\Exception $e) {
            Log::write('error', 'Failed to send email: ' . $e->getMessage());
            $success = false;
            Log::info('---- Error sendEmailNotification FINISH', ['scope' => 'notifications']);
        }

        // After sending (or attempting to send) the email
        $transport = $mail->getTransport();
        $lastResponse = $transport->getLastResponse();
        $this->BCCReplacement($lead, $layout, $item, $BCC_needed);

        return [
            'success' => $success,
            'lastResponse' => $lastResponse
        ];
    }

    private function BCCReplacement($lead, $layout, $item, $to)
    {
        if (count($to) > 0) {
            $mail = new Email('notifications');
            $mail->addHeaders(['X-SMTPAPI' => json_encode(array('category' => array('notification')))]);
            $mail->template('notifications', $layout);
            $mail->emailFormat('html');
            $mail->to($to[0]);
            if (count($to) > 1) {
                for ($i = 1; $i < count($to); $i++) {
                    $mail->addCc($to[$i]);
                }
            }
            $mail->subject($item['subject']);
            $mail->viewVars(array(
                'data'        => $item,
                'lead'        => $lead,
                'lendconfig'  => Configure::read('Lend'),
            ));
            try {
                $success = $mail->send();
            } catch (\Exception $e) {
                \Cake\Log\Log::error($e->getMessage());
                $success = false;
            }
        }
    }

    public static function hideShortLinks($text, $replacement)
    {
        $substring = getenv('SHORT_LINK_HOST');
        if (empty($substring)) {
            return $text;
        }
        $textWords = explode(' ', $text);
        $wordsToReplace = [];
        foreach ($textWords as $index => $word) {
            if (strpos($word, $substring) !== false) {
                $wordsToReplace[] = $index;
            }
        }
        foreach ($wordsToReplace as $index) {
            $textWords[$index] = $replacement;
        }

        return implode(' ', $textWords);
    }

    public function sendSMSNotification($recipient, $item)
    {
        $smsconfig = Configure::read('Lend')['SMS'];
        if (!$smsconfig['enabled']) return array('success' => false, 'message' => 'SMS Sending is currently disabled');
        if (!$recipient['mobile']) return array('success' => false, 'message' => 'Mobile number not provided');

        $data = [
            'recipient' => $item['recipient'],
            'partner_id' => !empty($recipient['partner_id']) ? $recipient['partner_id'] : $item['partner_id'],
            'lead_id' => !empty($item['for_lead_id']) ? $item['for_lead_id'] : $recipient['lead_id'],
            'sms_to' => preg_replace('/\s+/', '', $recipient['mobile']),
            'sms_message' => $item['body'],
            'partner_account_people_id' => $item['partner_account_people_id'],
        ];
        if (!empty($recipient['partner_user_id'])) {
            $data['partner_user_id'] = $recipient['partner_user_id'];
        } elseif (!empty($item['partner_user_id'])) {
            $data['partner_user_id'] = $item['partner_user_id'];
        }

        if (isset($item['notify_for_reply'])) {
            $data['notify_for_reply'] = $item['notify_for_reply'];
        }

        // Add Sms history
        $data['sms_message'] = self::hideShortLinks($data['sms_message'], '<PRIVATE LINK>');
        $sms_id = $this->sms_history->addPartnerSmsHistory($data);
        $hashedSmsId = (new LendInternalAuth)->hashLeadId($sms_id, 'sms');
        $mobile = $recipient['mobile'];
        if (substr($mobile, 0, 1) === '0') {
            $mobile = substr($mobile, 1);
        }

        $to = (getenv('REGION', true) === 'nz' ? '64' : '61') . str_replace(' ', '', $mobile);

        $partnerSmsCredentials = TableRegistry::getTableLocator()->get('PartnerExtraCredentialsEntity')
        ->getCredentialsDecrypted($data['partner_id'], 'burstSms');

        if (empty($partnerSmsCredentials)) {
            $smsCredentials = [
                'sender_id' => $smsconfig['BurstSMS']['sender_id'],
                'api_key' => getenv('BURSTSMS_APIKEY'),
                'secret' => getenv('BURSTSMS_SECRET'),
            ];
        } else {
            $smsCredentials = $partnerSmsCredentials->allCredentialsArray();
        }

        $post_data = [
            "message" => $item['body'],
            "to" => $to,
            'reply_callback' => getenv('DOMAIN_CRM') . '/api/hooks/sms/' . $hashedSmsId,
        ];
        if (!empty($smsCredentials['sender_id'])) {
            $post_data['from'] = $smsCredentials['sender_id'];
        }

        // Check possible time range to send SMS
        if ($send_at = $this->isItRestrictionTime($data)) {
            $post_data['send_at'] = $send_at;
        }

        Log::info('Send SMS post_data: ' . json_encode($post_data), ['scope' => 'notifications']);

        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $smsconfig['BurstSMS']['api_base_url'] . "/send-sms.json",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => $post_data,
            CURLOPT_USERPWD => $smsCredentials['api_key'] . ":" . $smsCredentials['secret'],
            CURLOPT_HTTPHEADER => array(
                "content-type: multipart/form-data;"
            ),
        ));

        $response = curl_exec($curl);

        if (curl_errno($curl)) {
            curl_close($curl);
            $this->sms_history->deleteBySmsId($sms_id); // Delete sms record if failure
            Log::error('Send SMS error: ' . curl_error($curl), ['scope' => 'notifications']);
            return array('success' => false, 'message' => 'We are unable to send the SMS right now, something went wrong.', 'debug_error' => 'Error:' . curl_error($curl));
        }

        $response = json_decode($response, true);
        curl_close($curl);

        if ($response['error']['code'] === 'SUCCESS') {
            return array('success' => true, 'result' => $response);
        } else {
            $this->sms_history->deleteBySmsId($sms_id); // Delete sms record if failure
            Log::error('Send SMS error: ' . json_encode($response['error']), ['scope' => 'notifications']);
            return array('success' => false, 'message' => 'We are unable to send the SMS right now, something went wrong.', 'debug_error' => $response['error']);
        }
    }


    /*
    * System notifications will always be for a Partner User
    */
    public function sendSystemNotification($recipient, $item){
      $created = date('Y-m-d H:i:s');
        $q = $this->getInsertQuery('partner_notifications', array(
            'notif_item_id'         => $item['notif_item_id'],
            'partner_user_id'       => $recipient['partner_user_id'],
        'created'               => $created,
            'for_lead_id'           => (empty($item['for_lead_id']) ? NULL : $item['for_lead_id']),
            'item_subject_override' => $item['subject'],
            'item_body_override'    => $item['body']
        ));
      $resp = $this->DB->execute($q['query'], $q['values']);
      $id = $resp->lastInsertId('partner_notifications');

      //call socket server and send notification
      $notificationData = ['body' => $item['body']];
      $notificationData['count'] = $this->getUnreadCount( $recipient['partner_user_id']);
      $notificationData['id'] = $id;
      $notificationData['subject'] = ucwords(strtolower(str_replace('Lend: ', '', $item['subject'])));
      $notificationData['created'] = $created;//needs to be formatted client side as we can't predict when it will be consumed
      $message = ['type'=> 'new_notification', 'data'=> $notificationData];
      $this->sendSocketMessage([$recipient['partner_user_id']], 'notifications', $message);
      return $resp;
    }

    private function sendSlackNotification($item)
    {
        $slack_ch = explode(':', $item['delivery_type'])[1];
        return $this->postToSlack($item['body'], $slack_ch, (!empty($item['subject']) ? json_decode($item['subject'], true) : ''));
    }


    private function isItRestrictionTime($data)
    {
        if (strtolower($data['recipient']) === 'client') {
            $leads_model = TableRegistry::get('Leads');
            $lead = $leads_model->getLead(['lead_id' => $data['lead_id']]);
            $recipient_time = $this->getRecipientTime($lead['original_time_zone']);
            $available_time_start = '08:00:00';
            $available_time_end = '22:00:00';
            if (getenv('REGION', true) === 'au') {
                $timezone = 'Australia/Sydney';
            } elseif (getenv('REGION', true) === 'nz') {
                $timezone = 'Pacific/Auckland';
            }
        } elseif (!empty($data['partner_user_id'])) {
            $partner_users_model = TableRegistry::get('PartnerUsers');
            $partner_user = $partner_users_model->getPartnerUser(['partner_user_id' => $data['partner_user_id']]);
            if (!empty($partner_user['timezone'])) {
                $recipient_time = $this->getRecipientTime($partner_user['timezone']);
                $available_time_start = $partner_user['sms_available_time_start'];
                $available_time_end = $partner_user['sms_available_time_end'];
                $timezone = $partner_user['timezone'];
            } else {
                $recipient_time = strtotime(date('Y-m-d H:i:00'));
                $available_time_start = '08:00:00';
                $available_time_end = '22:00:00';
                if (getenv('REGION', true) === 'au') {
                    $timezone = 'Australia/Sydney';
                } elseif (getenv('REGION', true) === 'nz') {
                    $timezone = 'Pacific/Auckland';
                }
            }
        }

        if ($recipient_time < strtotime(date('Y-m-d') . ' ' . $available_time_start))
            return $this->convertTimeToUTCTime(date('Y-m-d') . ' ' . $available_time_start, $timezone);
        elseif ($recipient_time >= strtotime(date('Y-m-d') . ' ' . $available_time_end))
            return $this->convertTimeToUTCTime(date('Y-m-d', strtotime('+1 day')) . ' ' . $available_time_start, $timezone);
        else
            return false;
    }

    private function getRecipientTime($timezone = 'Australia/Sydney')
    {
        if (empty($timezone)) {
            if (getenv('REGION', true) === 'au') {
                $timezone = 'Australia/Sydney';
            } elseif (getenv('REGION', true) === 'nz') {
                $timezone = 'Pacific/Auckland';
            }
        }
        date_default_timezone_set($timezone);
        $applicant_time = new \DateTime(date('Y-m-d H:i:s'));
        $applicant_time = $applicant_time->format('Y-m-d H:i:00');
        date_default_timezone_set('Australia/Sydney'); // Back to Sydney, Lend Office Location
        return strtotime($applicant_time);
    }

    private function convertTimeToUTCTime($date, $timezone)
    {
        $datetime = new \DateTime($date, new \DateTimeZone($timezone));
        $datetime->setTimezone(new \DateTimeZone('UTC'));
        return $datetime->format('Y-m-d H:i:s');
    }

    private function generateCreditReportsLink($lead_ref, $email)
    {

        // Add expiry date
        $expiry_date = strtotime('+ 24 Hour');
        $login = "{\"email\":\"$email\", \"expiry_date\":\"$expiry_date\", \"lead_ref\":\"$lead_ref\"}";

        $build_sig = Security::salt() . '#' . $login;
        $build_sig = hash('sha1', $build_sig);
        $code = $login . '#' . $build_sig;
        $code = base64_encode($code);
        $code = getenv(
            'DOMAIN_BRO',
            true
        ) . "?crblink=" . $code;

        return $code;
    }


    private function getEmailFooter($partnerUserId)
    {
        $result = [
            'success' => false,
            'content' => null
        ];

        if (empty($partnerUserId)) {
            return $result;
        }
        $emailCustomElementsTable = TableRegistry::getTableLocator()->get('EmailCustomElementsEntity');
        $footer = $emailCustomElementsTable->find()
            ->where([
                'partner_user_id' => $partnerUserId,
                'type' => 'footer',
                'active' => true
            ])
            ->first();

        if ($footer) {
            $result['success'] = true;
            $result['content'] = html_entity_decode($footer['html']);
        }
        return $result;
    }
}
