<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * PartnerChecklistProductTypes Model
 */
class PartnerChecklistProductTypeEntityTable extends Table
{
    /**
     * Initialize method
     *
     * @param array $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('partner_checklist_product_types');
        $this->setDisplayField('partner_checklist_product_type_id');
        $this->setPrimaryKey('partner_checklist_product_type_id');

        $this->belongsTo('PartnerChecklistEntity')
            ->setForeignKey('partner_checklist_id')
            ->setProperty('partner_checklist');

        $this->belongsTo('PartnerProductTypeEntity')
            ->setForeignKey('product_type_id')
            ->setProperty('product_type');
    }

}
