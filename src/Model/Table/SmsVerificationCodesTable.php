<?php
namespace App\Model\Table;

use Cake\ORM\Table;
use Cake\Validation\Validator;
use Cake\I18n\FrozenTime;

class SmsVerificationCodesTable extends Table
{
    public function initialize(array $config)
    {
        parent::initialize($config);

        $this->setTable('sms_verification_codes');
        $this->setDisplayField('verification_code');
        $this->setPrimaryKey('id');

        $this->addBehavior('Timestamp');
    }

    public function validationDefault(Validator $validator)
    {
        $validator
            ->integer('id')
            ->allowEmptyString('id', null, 'create');

        $validator
            ->scalar('verification_code')
            ->maxLength('verification_code', 10)
            ->requirePresence('verification_code', 'create')
            ->notEmptyString('verification_code');

        $validator
            ->scalar('phone_number')
            ->maxLength('phone_number', 20)
            ->requirePresence('phone_number', 'create')
            ->notEmptyString('phone_number');

        $validator
            ->scalar('status')
            ->requirePresence('status', 'create')
            ->notEmptyString('status');

        $validator
            ->integer('attempts')
            ->notEmptyString('attempts');

        $validator
            ->integer('max_attempts')
            ->notEmptyString('max_attempts');

        $validator
            ->dateTime('expires_at')
            ->requirePresence('expires_at', 'create')
            ->notEmptyDateTime('expires_at');

        $validator
            ->dateTime('used_at')
            ->allowEmptyDateTime('used_at');

        return $validator;
    }

    public function createVerificationCode($phoneNumber)
    {
        // Generate 6-digit verification code
        $code = str_pad(rand(100000, 999999), 6, '0', STR_PAD_LEFT);
        
        return $this->createVerificationCodeWithCode($phoneNumber, $code);
    }
    public function createVerificationCodeWithCode($phoneNumber, $code)
    {
        // Set expiration to 10 minutes from now
        $expiresAt = new FrozenTime('+10 minutes');

        $data = [
            'verification_code' => $code,
            'phone_number' => $phoneNumber,
            'status' => 'pending',
            'expires_at' => $expiresAt,
            'attempts' => 0,
            'max_attempts' => 3,
        ];

        $entity = $this->newEntity($data);
        return $this->save($entity);
    }
    public function verifyCode($phoneNumber, $code)
    {
        // Find the latest pending verification code for this phone number
        $verification = $this->find()
            ->where([
                'phone_number' => $phoneNumber,
                'verification_code' => $code,
                'status' => 'pending'
            ])
            ->orderDesc('created')
            ->first();

        if (!$verification) {
            return ['success' => false, 'message' => 'Invalid verification code'];
        }

        $verification->attempts += 1;
        $now = new FrozenTime();
        if ($now > $verification->expires_at) {
            $verification->status = 'expired';
            $this->save($verification);
            return ['success' => false, 'message' => 'Verification code has expired'];
        }
        if ($verification->attempts >= $verification->max_attempts) {
            $verification->status = 'expired';
            $this->save($verification);
            return ['success' => false, 'message' => 'Too many verification attempts'];
        }

        // If this attempt is successful (code matches and not expired)
        $verification->status = 'used';
        $verification->used_at = new FrozenTime();
        $this->save($verification);

        return ['success' => true, 'message' => 'Code verified successfully'];
    }
    public function getLatestCodeForPhone($phoneNumber, $status = 'pending')
    {
        return $this->find()
            ->where([
                'phone_number' => $phoneNumber,
                'status' => $status
            ])
            ->orderDesc('created')
            ->first();
    }
    public function cleanupExpiredCodes()
    {
        return $this->deleteAll([
            'expires_at <' => new FrozenTime(),
            'status' => 'pending'
        ]);
    }
    public function hasRecentCode($phoneNumber)
    {
        $recentTime = new FrozenTime('-60 seconds');
        
        $count = $this->find()
            ->where([
                'phone_number' => $phoneNumber,
                'created >=' => $recentTime
            ])
            ->count();

        return $count > 0;
    }
} 