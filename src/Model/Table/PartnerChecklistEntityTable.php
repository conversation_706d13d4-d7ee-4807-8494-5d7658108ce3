<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * PartnerChecklists Model
 */
class PartnerChecklistEntityTable extends Table
{
    /**
     * Initialize method
     *
     * @param array $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('partner_checklists');
        $this->setDisplayField('checklist_name');
        $this->setPrimaryKey('partner_checklist_id');

        $this->addBehavior('CustomTimestamp');

        $this->belongsTo('PartnerEntity')
            ->setForeignKey('partner_id')
            ->setProperty('partner');

        $this->hasMany('PartnerChecklistProductTypeEntity')
            ->setForeignKey('partner_checklist_id')
            ->setProperty('partner_checklist_product_types');

        $this->hasMany('PartnerChecklistItemEntity')
            ->setDependent(true)//this will delete all checklist items
            ->setForeignKey('partner_checklist_id')
            ->setProperty('checklist_items');

        $this->hasMany('LeadChecklistEntity')
            ->setForeignKey('partner_checklist_id')
            ->setProperty('lead_checklists');
    }
}
