<?php
namespace App\Model\Table;

use App\Model\Table\AppTable;
use Cake\Log\Log;
use Cake\ORM\TableRegistry;
use App\Lend\LendInternalAuth;
use Cake\Core\Configure;
use App\Lend\CurlHelper;
use App\Lend\LendSignatureServiceBackendClient;

class PartnerNotificationItemsTable extends AppTable{

    public $settings;

    function __construct($arg) {
			parent::__construct($arg);
		}


    public function initialize(array $config){
        parent::initialize($config);
    }

    /*
    NOTE:
    `partner_notification_items` table does not have `partner_id` field
    but $params may contain `partner_id` key, if so, look up `partner_notif_custom_items` table with the rest of $params,
    And items returned from `partner_notif_custom_items` has high priority, will overwrite items returned from `partner_notification_items` table where possible
    */
    public function getItems($params)
    {
      if (array_key_exists('partner_id', $params)) {
        $partnerId = $params['partner_id'];
        unset($params['partner_id']);
      }
      //firstly get items from `partner_notification_items` table

      $q = TableRegistry::get('App')->getSelectQuery('partner_notification_items', $params);
      $items = $this->DB->execute($q['query'], $q['values'])->fetchAll('assoc');


      //then look up `partner_notif_custom_items` table if key partner_id exists in $params
      if (isset($partnerId)) {
        //put partner_id back into $params, get ready to look up `partner_notif_custom_items` table
        $params['partner_id'] = $partnerId;

        //check if any item exists in partner_notif_custom_items table
        $q = TableRegistry::get('App')->getSelectQuery('partner_notif_custom_items', $params);
        $customItems = $this->DB->execute($q['query'], $q['values'])->fetchAll('assoc');
        //overwrite $item if $customItems contains any item
        if (!empty($customItems)) {
          foreach($customItems as $customItem) {
            //mark as customised item
            $customItem['custom'] = true;
            $overwrite = false;
            foreach ($items as &$item) {
              if ($item['notif_lookup_code'] === $customItem['notif_lookup_code']
                  && $item['delivery_type'] === $customItem['delivery_type']
                  && $item['notif_group_id'] === $customItem['notif_group_id']
                  && $item['recipient'] === $customItem['recipient']) {
                    $overwrite = true;
                    //overwrite this item
                    $item = $customItem;
                  }
            }
            unset($item);
            if ($overwrite === false) {
              //push this customised item into $item
              $items[] = $customItem;
            }
          }
        }
      }

      //return an array
      return $items;
    }

    public function getAllItems(){
      return $this->DB->execute('SELECT * FROM partner_notification_items')->fetchAll('assoc');
    }

    private function getSentDate($partnerId, $leadId) {
      $sent = $this->DB->execute('SELECT created 
                                FROM partner_lead_history 
                                WHERE history_detail = "sent" 
                                  AND partner_id = ? 
                                  AND lead_id = ? 
                                ORDER BY created DESC 
                                LIMIT 1', [$partnerId, $leadId])->fetch('assoc');
      if (!empty($sent)) {
        $date = $this->formatDate($sent['created']);
      }
      return @$date;
    }

    private function formatDate($date) {
      $time = strtotime($date);
      $formatted =  date('dS M Y',$time);
      return $formatted;
    }

    public function convertItem($item, array $lead, $partnerUser=false, $adhocData = false, $extraData = false, $partnerAccount = false) {

      if(!empty($lead['lead']['partner_id']) AND !empty($lead['lead']['lead_id']))
        $sentDate = $this->getSentDate($lead['lead']['partner_id'], $lead['lead']['lead_id']);

      /* $lead is an array containing the following: lead, lead_owner, abnlookup, bs_accounts, bank_statements_analysis, ... */
      /* array(
        code  => field
        ...
      )*/
      //get something from config/lend.php
      $lendconfig = Configure::read('Lend');

      if(isset($adhocData['percent_complete'])) {
        $percentComplete = $adhocData['percent_complete'];
      }elseif(isset($lead['lead']['percent_complete'])){
        $percentComplete = $lead['lead']['percent_complete'];
      }else{
        $percentComplete = "";
      }

      $lender_name = !empty($adhocData['lender_name']) ? $adhocData['lender_name'] : (!empty($lead['lender']['lender_name']) ? $lead['lender']['lender_name'] : (!empty($lead['lender'][0]['lender_name']) ? $lead['lender'][0]['lender_name'] : ''));

      if(isset($partnerUser['account_type']) && $partnerUser['account_type'] == 'Intermediary'){
        $inter_group_name = tableRegistry::get('IntermediaryLenderMapping')->getintermediaryLendergroupName($partnerUser['partner_user_id']);  
        $lender_name = $inter_group_name['inter_lender_name'];
      }

      if(!empty($partnerAccount['partner_account_people'])){
        foreach($partnerAccount['partner_account_people'] as $people){
          if($people['is_main_point_of_contact'] === true){
            $partnerAccount['poc'] = $people;
          }
        }
      }

      if (!empty($partnerUser['Partner']['country'])) {
        $DOMAIN_BRO = getenv('DOMAIN_BRO');
        if (strtolower($partnerUser['Partner']['country']) === 'au') {
          putenv('REGION=au');
          putenv('DOMAIN_BRO=' . $DOMAIN_BRO);
        } elseif (strtolower($partnerUser['Partner']['country']) === 'nz') {
          putenv('REGION=nz');
          if (stripos($DOMAIN_BRO, 'lend.com.au') !== false) {
            putenv('DOMAIN_BRO=' . str_replace('lend.com.au', 'lend.co.nz', $DOMAIN_BRO));
          }
          if (stripos($DOMAIN_BRO, 'lend.local') !== false) {
            putenv('DOMAIN_BRO=' . str_replace('lend.local', 'lend.local.nz', $DOMAIN_BRO));
          }
        }
      }

      $customEmailSignature = '';
      if (!empty($partnerUser)) {
        $customElement = TableRegistry::getTableLocator()->get('EmailCustomElements')
        ->find('all')
        ->where([
          'partner_user_id' => $partnerUser['partner_user_id'],
          'type' => 'footer',
          'active' => 1
        ])->first();

        $customEmailSignature = $customElement['html'] ?? '';
      }

      $smsDelivery = $item['delivery_type'] && (strtolower($item['delivery_type']) == 'sms');
      
      $mapping = array(
        '[responder]' => !empty($adhocData['responder']) ? $adhocData['responder'] : '',
        '[lead_ref]' => !empty($extraData['lead_ref']) ? $extraData['lead_ref'] : '',
        '[lead_reference]' => !empty($adhocData['lead_reference']) ? $adhocData['lead_reference'] : '',
        '[requested_files]' => @$adhocData['requested_files'],
        '[sent_date]' => !empty($sentDate) ? $sentDate : '',
        '[created_date]' => !empty($lead['lead']['created']) ? $this->formatDate($lead['lead']['created']) : '',
        '[Firstname]' => !empty($extraData['applicant_name']) ? $extraData['applicant_name'] : '',
        '[adhoc_subject]' => !empty($adhocData['subject']) ? $adhocData['subject'] : '(no subject)',
        '[adhoc_body]' => !empty($adhocData['body']) ? $adhocData['body'] : '(no content)',
        '[org_name]' => !empty($lead['lead']['organisation_name']) ? $lead['lead']['organisation_name'] : (!empty($adhocData['client_first_name']) ? $adhocData['client_first_name'] : (!empty($lead['lead_owner']['first_name']) ? ucwords($lead['lead_owner']['first_name']) : '')),
        '[client_first_name]' => !empty($adhocData['client_first_name']) ? $adhocData['client_first_name'] : (!empty($lead['lead_owner']['first_name']) ? ucwords($lead['lead_owner']['first_name']) : ''),
        '[client_last_name]' =>  !empty($adhocData['client_last_name']) ? $adhocData['client_last_name'] : (!empty($lead['lead_owner']['last_name']) ? ucwords($lead['lead_owner']['last_name']) : ''),
        '[client_email]' => !empty($lead['lead_owner']['email']) ? $lead['lead_owner']['email'] : '',
        '[partner_name]' => !empty($partnerUser['name']) ? ucwords($partnerUser['name']) : '',
        '[prev_partner_name]' => !empty($adhocData['prev_partner_name']) ? ucwords($adhocData['prev_partner_name']) : '',
        '[prev_partner_name_with_prefix]' => !empty($adhocData['prev_partner_name']) ? ' that was previously assigned to '.ucwords($adhocData['prev_partner_name']) : '',
        '[partner_company_name]' => (!empty($partnerUser['Partner']) && empty($lead)) ? $partnerUser['Partner']['company_name'] : (!empty($lead['partner']['company_name']) ? $lead['partner']['company_name'] : ''),
        '[partner_user_name]' => !empty($adhocData['partner_user_name']) ? $adhocData['partner_user_name'] : (!empty($partnerUser['name']) ? ucwords($partnerUser['name']) : ''),
        '[partner_user_email]' => !empty($adhocData['partner_user_email']) ? $adhocData['partner_user_email'] : (!empty($partnerUser['email']) ? ucwords($partnerUser['email']) : ''),
        '[lead_id]' => !empty($lead['lead']['lead_id']) ? $lead['lead']['lead_id'] : '',
        '[hashed_lead_id]' => !empty($adhocData['hashed_lead_id'])
                              ? $adhocData['hashed_lead_id']
                              : (!empty($lead['lead']['lead_id']) ? (new LendInternalAuth)->hashLeadId($lead['lead']['lead_id']) : ''),
        '[percent_complete]' => !empty($percentComplete) ? $percentComplete . '%' : '',
        '[lender_name]' => $lender_name,
        '[client_mobile]' => !empty($lead['lead_owner']['mobile']) ? $lead['lead_owner']['mobile'] : '',
        '[client_phone]' => !empty($lead['lead_owner']['phone']) ? $lead['lead_owner']['phone'] : '',
        '[partner_password]' => !empty($adhocData['partner_password']) ? $adhocData['partner_password'] : '',
        '[partner_portal]'  => '<a href="'.getenv('DOMAIN_BRO', true).'">Lend Platform</a>',

        // For extra data
        '[task_scheduled_time]' => !empty($extraData['task_scheduled_time']) ? $extraData['task_scheduled_time'] : '',
        '[task_type]' => !empty($extraData['task_type']) ? $extraData['task_type'] : '',
        '[task_reason]' => !empty($extraData['task_reason']) ? $extraData['task_reason'] : '',
        '[reset_password]' => !empty($partnerUser['token']) ? '<a href="'.getenv('DOMAIN_BRO', true).'/reset-password/' . $partnerUser['token'] . '" class="login-btn" target="_blank">Reset Password</a>' : '',
        '[reset_password_url]' => !empty($partnerUser['token']) ? getenv('DOMAIN_BRO', true).'/reset-password/' . $partnerUser['token'] : '',
        '[sms_content]' => !empty($extraData['sms_content']) ? $extraData['sms_content'] : '',

        '[login]' => '<a href="'.getenv('DOMAIN_BRO', true).'" class="login-btn" target="_blank">Login</a>',
        '[view_lead_link]' => !empty($lead['lead']['hashed_lead_id']) ? '<a href="'.getenv('DOMAIN_BRO', true).'/lead/'.$lead['lead']['hashed_lead_id'].'/summary" class="login-btn" target="_blank">Click here</a>' : '',
        '[partner_verify_email_btn]' => !empty($partnerUser['email']) ? '<a href="'.getenv('DOMAIN_BRO', true).'/partner-users/verify-email/'.base64_encode($partnerUser['email']).'" class="login-btn">Verify Email</a>' : '',
        '[lead_management_page]' => !empty($lead['lead']['hashed_lead_id']) ? '<a href="'.getenv('DOMAIN_BRO', true).'/lead/'.$lead['lead']['hashed_lead_id'].'/summary">lead management page</a>' : '',
        '[partner_dashboard_link]' => '<a href="'.getenv('DOMAIN_BRO', true).'/partners/dashboard">dashboard</a>',
        '[partner_commissions_link]' => '<a href="'.getenv('DOMAIN_BRO', true).'/commissions">commissions page</a>',
        '[applicant_note]' => !empty($adhocData['applicant_note']) ? "\r\n". $adhocData['applicant_note'] : '',
        '[applicant_note_text]' => !empty($adhocData['applicant_note_text']) ? $adhocData['applicant_note_text'] : '',
        '[applicant_login_code]' => !empty($adhocData['client_login_code'])
                                    ? $adhocData['client_login_code']
                                    : (!empty($lead['lead']['client_login_code']) ? $lead['lead']['client_login_code'] : ''),
        '[upload_login_code]' => !empty($adhocData['upload_login_code']) ? '<a href="'.getenv('DOMAIN_APPLICANT').'/documents'.$adhocData['upload_login_code'].'" class="login-btn">Complete</a>' : '',

        '[applicant_login_link]' => !empty($adhocData['client_login_code'])
                                    ? '<a href="'.getenv('DOMAIN_BRO', true).'/applicants/'.$adhocData['client_login_code'].'" class="login-btn">Complete</a>'
                                    : (
                                      !empty($lead['lead']['client_login_code'])
                                      ? '<a href="'.getenv('DOMAIN_BRO', true).'/applicants/'.$lead['lead']['client_login_code'].'" class="login-btn">Complete</a>'
                                      : (!empty($adhocData['bs_spa_link'])?'<a href="'.$adhocData['bs_spa_link'].'" class="login-btn">Complete</a>':'')
                                      ),
        '[ask_applicant_login_link]' => !empty($adhocData['client_login_code'])
        ? ($smsDelivery ? LendSignatureServiceBackendClient::shortenUrl(getenv('DOMAIN_ASK_APPLICANT', true).'/'.$adhocData['client_login_code']):'<a href="'.getenv('DOMAIN_ASK_APPLICANT', true).'/'.$adhocData['client_login_code'].'" class="login-btn">Complete</a>')
        : '',
        '[ask_applicant_login_url_short]' => !empty($adhocData['client_login_code']) ? LendSignatureServiceBackendClient::shortenUrl(getenv('DOMAIN_ASK_APPLICANT', true).'/'.$adhocData['client_login_code']) : '',

        '[referreral_invitation_link]' => !empty($extraData['invitation_code']) 
        ? '<a href="'.getenv('DOMAIN_REFERRERS').'/'.$extraData['invitation_code']. '" class="login-btn">Accept Invitation</a>':'',

        '[referreral_reset_link]' => !empty($extraData['referrer_forgot_password_code']) 
        ? '<a href="'.getenv('DOMAIN_REFERRERS').'/'.$extraData['referrer_forgot_password_code']. '" class="login-btn">Reset Password</a>':'',

        '[quote_view_link]' => !empty($adhocData['client_login_code'])
              ? '<a href="'.getenv('DOMAIN_BRO', true).'/applicants/view-quote'.$adhocData['client_login_code'].'" class="login-btn">View</a>'
              : (
              !empty($lead['lead']['client_login_code'])
                  ? '<a href="'.getenv('DOMAIN_BRO', true).'/applicants/view-quote'.$lead['lead']['client_login_code'].'" class="login-btn">View</a>'
                  : ''
              ),

        '[lender_view_lead_link]' => !empty($adhocData['hashed_lead_id'])
            ? '<a href="'.getenv('DOMAIN_BRO', true).'/leads/view/'.$adhocData['hashed_lead_id'].'">here</a>'
            : (!empty($lead['lead']['hashed_lead_id']) ? '<a href="'.getenv('DOMAIN_BRO', true).'/lender/leads/edit/'.$lead['lead']['hashed_lead_id'].'">here</a>' : ''),
        '[task_link]' => '<a href="'.getenv('DOMAIN_BRO', true).'/calendar'.'">here</a>',
        '[partner_sms_sidebar_link]' => !empty($lead['lead']['lead_id'])?'<a href="'.getenv('DOMAIN_BRO', true).'/lead/'.(new LendInternalAuth)->hashLeadId($lead['lead']['lead_id']).'/communication/sms?active-tab=sms_history">here</a>':"",
        '[invite_link_btn]' => !empty($adhocData['invite_link']) ? '<p><a style="display: inline-block; border-radius: 4px; font-size: 16px; padding: 12px 32px; color: #ffffff; background: #00cf88; text-decoration: none;" href="'.$adhocData['invite_link'].'">Accept Invite</a></p>' : '',
        '[invite_link]' =>!empty($adhocData['invite_link']) ? $adhocData['invite_link'] : '',

        // Slack buttons:
        '[lend_verify_broker]' => json_encode(array('Verify New Broker :rocket:' => array('url'=>getenv('DOMAIN_TEAM') . '/partners/verify'))),
        '[lend_bs_merge]' => !empty($lead['lead']['lead_id']) ? json_encode(array('BS merge :rocket:' => array('url'=>getenv('DOMAIN_CRM').'/bank-statements-analysis/manual-merge-bs-account-level/'.$lead['lead']['lead_id']))) : '',
        '[lend_bs_overdraft]' => !empty($lead['lead']['lead_id']) ? json_encode(array('Confirm Overdraft :rocket:' => array('url'=>getenv('DOMAIN_CRM').'/bank-statements-analysis/manual-merge-bs-account-level/'.$lead['lead']['lead_id']))) : '',
        '[lend_bs_exclusion]' => !empty($lead['lead']['lead_id']) ? json_encode(array('Reveiw Exclusions :rocket:' => array('url'=>getenv('DOMAIN_CRM').'/bank-statements-analysis/manual-merge-bs-account-level/'.$lead['lead']['lead_id']))) : '',
        '[partner_id]'        => !empty($lead['lead']['partner_id']) ? "`partner ".$lead['lead']['partner_id']."`" : '',

        //source from lenders plugin: lookupcode = 'LenderTmpPass'
        '[lender_login]' => '<a href="'.getenv('DOMAIN_BRO', true).'/lenders/login" class="login-btn" target="_blank">Login</a>',
        '[lender_contact_name]' => !empty($adhocData['lender_contact_name']) ? $adhocData['lender_contact_name'] : '',
        '[lender_email]' => !empty($adhocData['lender_email']) ? $adhocData['lender_email'] : '',
        '[lender_password]' => !empty($adhocData['lender_password']) ? $adhocData['lender_password'] : '',
        '[partner_logo]' => !empty($partnerUser['Partner']['logo'])
              ? '<img src="' . $lendconfig['AWS']['url'] . $partnerUser['Partner']['logo'] . '" width="180" style="width: 180px;" alt="' . @$partnerUser['Partner']['company_name'] . '"  />'
              : '<h2 style="margin: 0;">' . @$partnerUser['Partner']['company_name'] . '</h2>',
        '[domain_wp]' => getenv('DOMAIN_WP'),
        '[sub_applicant_name]' => !empty($adhocData['sub_applicant_name']) ? $adhocData['sub_applicant_name'] : '',
        '[product_type]' => !empty($adhocData['product_type']) ? $adhocData['product_type'] : '',
        '[ask_applicant_tasks]' => !empty($adhocData['ask_applicant_tasks']) ? $this->_convertTasks($adhocData['ask_applicant_tasks']) : '',
        '[ask_applicant_sections]' => (!empty($adhocData['ask_applicant_sections']) && is_array($adhocData['ask_applicant_sections']) && (count($adhocData['ask_applicant_sections']) > 0)) ? '<ol><li>'. implode('</li><li>', $adhocData['ask_applicant_sections']). '</li></ol>' : '',
        '[ask_applicant_sections_text]' => !empty($adhocData['ask_applicant_sections_text']) ? $adhocData['ask_applicant_sections_text'] : '',
        '[ask_applicant_documents]' => (!empty($adhocData['ask_applicant_documents']) && is_array($adhocData['ask_applicant_documents']) && (count($adhocData['ask_applicant_documents']) > 0)) ? '<ol><li>'. implode('</li><li>', $adhocData['ask_applicant_documents']). '</li></ol>' : '',
        '[ask_applicant_documents_text]' => !empty($adhocData['ask_applicant_documents_text']) ? $adhocData['ask_applicant_documents_text'] : '',
        '[ask_applicant_electronic_signature_documents]' => (!empty($adhocData['ask_applicant_electronic_signature_documents']) && is_array($adhocData['ask_applicant_electronic_signature_documents']) && (count($adhocData['ask_applicant_electronic_signature_documents']) > 0)) ? '<ol><li>'. implode('</li><li>', $adhocData['ask_applicant_electronic_signature_documents']). '</li></ol>' : '',
        '[ask_applicant_electronic_signature_documents_text]' => !empty($adhocData['ask_applicant_electronic_signature_documents_text']) ? $adhocData['ask_applicant_electronic_signature_documents_text'] : '',

        '[credit_reports_section]' => !empty($adhocData['credit_reports_section']) ? '<a href="'.$adhocData['credit_reports_section'].'">credit reports section</a>' : '',
        '[lead_pdf_frm]' => !empty($adhocData['lead_pdf_frm']) ? $this->_convertEmailSubmissionLinks($adhocData['lead_pdf_frm']) : '',

        // Account level short code:
        '[account_org_name]' => !empty($partnerAccount['partner_account_meta']['organisation_name']) ? $partnerAccount['partner_account_meta']['organisation_name'] : '',
        '[account_management_page]' => !empty($partnerAccount['account_ref']) ? '<a href="'.getenv('DOMAIN_BRO', true).'/accounts/'.$partnerAccount['account_ref'].'/details">lead management page</a>' : '',
        '[applicant_first_name]' => isset($partnerAccount['poc']['first_name']) ? $partnerAccount['poc']['first_name']: ($extraData['applicant_first_name'] ?? ''),
        '[applicant_last_name]' => isset($partnerAccount['poc']['last_name']) ? $partnerAccount['poc']['last_name']: '',
        '[lend_account_bs_exclusion]' => !empty($partnerAccount['partner_account_id']) ? json_encode(array('Reveiw Exclusions :rocket:' => array('url'=>getenv('DOMAIN_CRM').'/bank-statements-analysis/partner-account-manual-merge-bs/'.$partnerAccount['partner_account_id']))) : '',
        '[lend_account_bs_merge]' => !empty($partnerAccount['partner_account_id']) ? json_encode(array('BS merge :rocket:' => array('url'=>getenv('DOMAIN_CRM').'/bank-statements-analysis/partner-account-manual-merge-bs/'.$partnerAccount['partner_account_id']))) : '',
        '[partner_account_ref]' => !empty($partnerAccount['account_ref']) ? $partnerAccount['account_ref'] : "",
        '[partner_account_id]' => !empty($partnerAccount['partner_account_id']) ? $partnerAccount['partner_account_id'] : "",
        '[bs_upload_link]' => !empty($partnerAccount['account_ref']) ? '<a href="'.getenv('DOMAIN_BS_SPA') . "?a=" . $partnerAccount['account_ref'] . "&signature=" . CurlHelper::generateSignature(['a' => $partnerAccount['account_ref']], getenv('BANKFEEDS_SECRET')).'" class="login-btn">Complete</a>' : "",
        '[applicant_name]' => $extraData['applicant_name'] ?? '',
        '[broker_full_name]' => $extraData['broker_full_name'] ? $extraData['broker_full_name'] : $adhocData['broker_full_name'] ?? '',
        '[broker_company_name]' => $extraData['broker_company_name'] ?? '',
        '[supplier_name]' => $extraData['supplier_name'] ?? '',
        '[referrer_name]' => $extraData['referrer_name'] ?? '',
        '[referrer_first_name]' => $extraData['referrer_first_name'] ?? '',
        '[referrer_last_name]' => $extraData['referrer_last_name'] ?? '',
        '[rcti_link]' => !empty($extraData['rcti_link']) ? '<a href="'.$extraData['rcti_link'].'" class="login-btn">View RCTI</a>' : '',
        '[smtp_verification_code]' => $adhocData['smtp_verification_code'] ?? '',
        '[automations_list]' => (!empty($adhocData['automations_list']) && is_array($adhocData['automations_list']) && (count($adhocData['automations_list']) > 0)) ? '<ol><li>'. implode('</li><li>', $adhocData['automations_list']). '</li></ol>' : '',

        // new broker information //
        '[broker_mobile]' => $partnerUser['mobile'] ?? '',
        '[broker_phone]' => $partnerUser['phone'] ?? '',
        '[broker_email]' => $partnerUser['email'] ?? '',
        '[broker_position]' => $partnerUser['position'] ?? '',
        '[due_date]' => !empty($extraData['due_date']) ? $extraData['due_date'] : '',
        '[end_date]' => !empty($extraData['end_date']) ? $extraData['end_date'] : '',
        '[lead_owners_poc_full_name]' => !empty($extraData['lead_owners_poc_full_name']) ? $extraData['lead_owners_poc_full_name'] : '',

        // Task Reminder
        '[reminder_view_lead_link]' => !empty($lead['lead']['lead_ref']) ? '<a href="' . getenv('DOMAIN_BRO', true) . '/lead/' . $lead['lead']['lead_ref'] . '/summary" class="login-btn" target="_blank">View Lead</a>' : '',

        // Partner Notices 
        '[platform_notice]' => !empty($adhocData['platform_notice']) ? $adhocData['platform_notice'] : '',
        '[platform_title]' => !empty($adhocData['platform_title']) ? $adhocData['platform_title'] : '',
        '[tag_category]' => !empty($adhocData['tag_category']) ? $adhocData['tag_category'] : '',
        '[10_days_from_now]' => date('dS M Y', strtotime('+10 days')),
        '[custom_broker_signature]' => $customEmailSignature,

      );

      $codes = array_keys($mapping);
      $replacements = array_values($mapping);
      if (!empty($adhocData) && is_array($adhocData)) {
        foreach ($adhocData as $key => $value) {
          if (empty($mapping['['.$key.']']) && isset($value)) {
            $codes[] = '[' . $key . ']';
            $replacements[] = $value;
          }
        }
      }

      $item['body']    = str_replace($codes, $replacements, $item['body']);
      $item['body']    = preg_replace('/\n\s*\n/', "\n\n", $item['body']);
      $item['body_without_footer']    = str_replace($codes, $replacements, $item['body_without_footer']);
      $item['body_without_footer']    = preg_replace('/\n\s*\n/', "\n\n", $item['body_without_footer']);
      $item['subject'] = str_replace($codes, $replacements, $item['subject']);
      return $item;
    }


  private function _convertTasks($tasks){
    $items = [];
    foreach($tasks as $task){
      switch($task){
        case 'application': $items[] = '<li>Enter some additional information in our application form</li>'; break;
        case 'bs':          $items[] = '<li>Safely upload your business bank statement data to improve your chance of a successful application</li>'; break;
        case 'consent':     $items[] = '<li>Give Consent (for the purposes of identity verification and credit checks. These will not impact your credit score)</li>'; break;
      }
    }
    $ol = '<ol>'. implode('', $items). '</ol>';
    return $ol;
  }

  private function _convertEmailSubmissionLinks($links) {
    $items = [];
    if (!empty($links)) {
      $explodeLinks = explode("&&", $links);
      $leadPdf = $explodeLinks[0];
      $downloadZip = $explodeLinks[1];
    }

    $leadPdfElement = '<li> <a href="'. $leadPdf .'" class="login-btn" target="_blank">Lead Details</a> </li>';
    $items[] = $leadPdfElement;

    if ($downloadZip !== 'NoFiles') {
      $items[] = '<li style="display:inline-flex;">
                    <form action="'. $downloadZip . '" method="get" target="_blank" style="display: inline-flex;">
                      <button style="background:none;cursor: pointer;color:inherit;border:none;padding:0;font:inherit;outline:inherit" type="submit">
                          <a href="" class="login-btn">Documents</a>
                        </button>
                    </form>
                  </li>';
    }

    $ol = '<ul style="display: inline-flex; list-style: none;"">'. implode('', $items). '</ul>';

    return $ol;
  }
}