<?php

namespace App\Model\Table;

use Cake\ORM\Table;
use Cake\ORM\TableRegistry;
use App\Model\Table\AppTable;
use App\Lend\LendInternalAuth;
use App\Traits\LendDateTime;

class PartnerCallbacksTable extends AppTable
{
    use LendDateTime;
    function __construct($arg)
    {
        parent::__construct($arg);
    }

    public function initialize(array $config)
    {
        parent::initialize($config);
    }

    public function addPartnerCallback($params)
    {
        if ($params) {
            $params['created'] = date('Y-m-d H:i:s');

            $q = $this->getInsertQuery('partner_callbacks', $params);
        }
        $query = $this->DB->execute($q['query'], $q['values']);
        return $query->lastInsertId();
    }

    public function getPartnerCallback($params)
    {
        $q = $this->getSelectQuery('partner_callbacks', $params);
        return $this->DB->execute($q['query'], $q['values'])->fetch('assoc');
    }

    public function updatePartnerCallbacks($params)
    {
        $conditions = array('callback_id' => $params['callback_id']);
        unset($params['callback_id']);
        $q = $this->getUpdateQuery('partner_callbacks', $params, $conditions);
        return $this->DB->execute($q['query'], $q['values']);
    }

    public function getPartnerCallbacksWithDetails($params = [], $sort = [], $page = 1, $limit = 0)
    {
        $query = "SELECT pc.*, pu.name as consultant,
                        lo.first_name, lo.last_name, lo.avatar_image_url, ls.status_name, ms.status_name as man_status_name, ppt.product_icon, pt.color, l.lead_ref, l.is_archived, l.is_closed,
                        l.is_closed as is_closed
                FROM partner_callbacks as pc
                JOIN partner_users as pu ON pc.partner_user_id = pu.partner_user_id
                LEFT JOIN lead_owners as lo ON pc.lead_id = lo.lead_id AND lo.point_of_contact=1
                LEFT JOIN leads as l ON l.lead_id = pc.lead_id
                LEFT JOIN lend_statuses as ls ON l.partner_status_id = ls.lend_status_id
                LEFT JOIN man_statuses as ms ON l.man_status_id = ms.id
                LEFT JOIN partner_product_types as ppt ON l.product_type_id = ppt.product_type_id
                LEFT JOIN partner_tags as pt ON pt.id = l.tag_id";

        // Conditions:
        $fields = array();
        $values = array();
        if ($params) {
            foreach ($params as $field => $value) {
                $arr_field = explode(' ', trim($field));
                if (count($arr_field) > 1) {
                    if (is_array($value)) {
                        $fields[] = $field . ' (' . implode(',', array_map(function () {
                            return '?';
                        }, $value)) . ' )';
                    } else {
                        $fields[] = $field . ' ?';
                    }
                } else $fields[] = $field . '=?';
                if (is_array($value)) {
                    $values = array_merge($values, $value);
                } else {
                    $values[] = $value;
                }
            }
            $query .= " WHERE " . implode(' AND ', $fields);
        }

        $query .= " GROUP BY pc.callback_id ";
        // Sort:
        if ($sort) {
            $order_by = array();
            foreach ($sort as $sort_field => $asc_desc) {
                $order_by[] = $sort_field . ' ' . $asc_desc;
            }
            $query .= " ORDER BY " . implode(', ', $order_by);
        } else {
            $query .= " ORDER BY pc.scheduled_time ASC ";
        }
        if ($page && $limit)
            $query .= " LIMIT {$limit} OFFSET " . ($limit * $page);
        $callbacks = $this->DB->execute($query, $values)->fetchAll('assoc');

        // $result = array();
        if ($callbacks) {
            foreach ($callbacks as $key => $c) {
                // If status is not completed, check whether it's overdue or not:
                if (!$c['status']) {
                    if (strtotime($c['scheduled_time']) < strtotime('now')) {
                        $callbacks[$key]['status'] = 'overdue';
                    } else {
                        $callbacks[$key]['status'] = 'scheduled';
                    }
                } else {
                    $callbacks[$key]['status'] = 'completed';
                }
                // Convert scheduled_time to ISO format
                $callbacks[$key]['scheduled_time'] = $this->convertToISO($c['timezone'], $c['scheduled_time']);
                $callbacks[$key]['end_time'] = $this->convertToISO($c['timezone'], $c['end_time']);
                // Set lead full name:
                $callbacks[$key]['full_name'] = $c['first_name'] . ' ' . $c['last_name'];
                // Hash lead_id:
                $callbacks[$key]['hashed_lead_id'] = (new LendInternalAuth)->hashLeadId($c['lead_id']);
            }
        }
        return $callbacks;
    }

    public function getCountOfOverdueCallBack($partnerUserId)
    {
        $now = new \DateTime();
        $nowDateTime = $now->format('Y-m-d H:i:s');

        $query = $this->ReaderDB->prepare('
        SELECT count(*) as cnt 
        FROM partner_callbacks pc
        LEFT JOIN leads l on l.lead_id = pc.lead_id
        WHERE (status = 0 OR status is NULL) 
        AND pc.partner_user_id = :partnerUserId 
        AND pc.scheduled_time < :nowDateTime
        AND (l.is_archived IS NULL OR l.is_archived = 0)
        AND (l.is_closed IS NULL OR l.is_closed = 0)
        ');
        $query->bindValue(':partnerUserId', $partnerUserId);
        $query->bindValue(':nowDateTime', $nowDateTime);
        $query->execute();
        $result = $query->fetch('assoc');
        return (int)$result['cnt'];
    }

    public function getAssignableUsers($leadId)
    {
        $query = $this->ReaderDB->prepare('
        SELECT pu.name, pu.email, pu.partner_user_id, pu.account_admin, pu.access_all_leads
        FROM partner_users as pu
        JOIN leads as l ON l.partner_id = pu.partner_id
        WHERE l.lead_id = :leadId
        ');
        $query->bindValue(':leadId', $leadId);
        $query->execute();
        $users = $query->fetchAll('assoc');
        $sql = 'SELECT pu.name, pu.email, pu.partner_user_id, pu.account_admin, pu.access_all_leads
							FROM partner_user_leads as pul,
							partner_users as pu
							WHERE pul.partner_user_id = pu.partner_user_id
							AND pul.status="ACCESS"
							AND pu.active = "1"
              AND pul.lead_id = ?';
        $leadCurrentUser = $this->DB->execute($sql, [$leadId])->fetchAll('assoc');
        $allUsers = [];
        if (!empty($users)) {
            foreach ($users as $key => $value) {
                if ($value['account_admin'] == '1' || $value['access_all_leads'] == '1') {
                    $allUsers[$value['partner_user_id']] = $value;
                }
            }
        }
        if ($leadCurrentUser[0]) {
            $allUsers[$leadCurrentUser[0]['partner_user_id']] = $leadCurrentUser[0];
        }
        return $allUsers;
    }
}
