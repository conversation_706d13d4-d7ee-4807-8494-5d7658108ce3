<?php
namespace App\Model\Table;

use App\Model\Entity\PartnerAccountPeople;
use Cake\ORM\Table;
use Cake\Database\Schema\TableSchema;
use App\Lend\EncryptionHelper;
use Cake\ORM\TableRegistry;
use App\Auth\LendAuthenticate;

class PartnerAccountPeopleEntityTable extends Table
{
  protected $_table = 'partner_account_people';

  protected $_primaryKey = 'id';

  protected $_entityClass = PartnerAccountPeople::class;

  public function initialize(array $config)
  {
    $this->addBehavior('CustomTimestamp');
    $this->addBehavior('NameSanitize');

    $this->belongsTo('PartnerEntity')
      ->setForeignKey('partner_id')
      ->setProperty('partner');

    $this->hasMany('PartnerAccountLinkPeopleEntity')
      ->setForeignKey('people_id')
      ->setProperty('partner_account_link_people');
    
    $this->hasMany('LeadOwnersEntity')
      ->setForeignKey('partner_account_people_id')
      ->setProperty('lead_owners');

    $this->belongsTo('PartnerAccountMetaEntity')
      ->setBindingKey('partner_account_id')
      ->setForeignKey('partner_account_id')
      ->setProperty('partner_account_meta');

    $this->hasMany('PartnerApplicantGroupUploadsEntity')
      ->setForeignKey('people_id')
      ->setConditions(['PartnerApplicantGroupUploadsEntity.status' => 'Active'])
      ->setProperty('partner_applicant_group_uploads');

    $this->hasMany('PartnerApplicantGroupUploadsRequestedEntity')
      ->setForeignKey('people_id')
      ->setConditions(['PartnerApplicantGroupUploadsRequestedEntity.status' => true])
      ->setProperty('partner_applicant_group_uploads_requested');

    $this->hasOne('LeadOwnerFinancesEntity')
      ->setForeignKey('partner_account_people_id')
      ->setProperty('finance');

    $this->hasMany('LeadAssetsEntity')
      ->setConditions(['LeadAssetsEntity.status' => 'Active'])
      ->setForeignKey('partner_account_people_id')
      ->setProperty('assets');

    $this->hasMany('LeadLiabilitiesEntity')
      ->setConditions(['LeadLiabilitiesEntity.status' => 'Active'])
      ->setForeignKey('partner_account_people_id')
      ->setProperty('liabilities');

    $this->hasMany('LeadOwnerAddressesEntity')
      ->setConditions(['LeadOwnerAddressesEntity.status' => 'active'])
      ->setForeignKey('partner_account_people_id')
      ->setProperty('addresses');

    // copied from leadsOwnerEntity logic   
    $this->hasOne('PartnerAccountPeopleCurrentAddress', [
      'className' => 'LeadOwnerAddressesEntity',
      'sort' => ['PartnerAccountPeopleCurrentAddress.lead_owner_address_id' => 'asc'],
      'strategy' => 'select',
      'limit' => 1
    ])
      ->setConditions(['PartnerAccountPeopleCurrentAddress.date_to IS NULL', 'PartnerAccountPeopleCurrentAddress.status' => 'active'])
      ->setForeignKey('partner_account_people_id')
      ->setProperty('partner_account_people_current_address');
      
    $this->hasMany('LeadOwnerEmploymentEntity')
      ->setConditions(['LeadOwnerEmploymentEntity.status' => 'active'])
      ->setForeignKey('partner_account_people_id')
      ->setProperty('employments');

    $this->hasMany('PartnerAccountPeopleAssetsEntity')
      ->setConditions(['PartnerAccountPeopleAssetsEntity.status' => 'Active'])
      ->setForeignKey('partner_account_people_id')
      ->setProperty('people_assets');
    
    $this->hasMany('PartnerAccountPeopleExpensesEntity')
      ->setForeignKey('partner_account_people_id')
      ->setProperty('people_expenses');

    $this->hasMany('PartnerAccountPeopleIncomesEntity')
      ->setForeignKey('partner_account_people_id')
      ->setProperty('people_incomes');

      $this->hasMany('PartnerAccountPeopleLiabilitiesEntity')
      ->setConditions(['PartnerAccountPeopleLiabilitiesEntity.status' => 'Active'])
      ->setForeignKey('partner_account_people_id')
      ->setProperty('people_liabilities');

      $this->hasOne('CheckMobile', [
        'className' => 'TotalCheckCallEntity',
        'sort' => ['CheckMobile.created' => 'DESC'],
        'strategy' => 'select',
        'conditions' => ['CheckMobile.type' => 'mobile'],
        'foreignKey' => 'value',
        'bindingKey' => 'mobile',
        'propertyName' => 'check_mobile',
      ]);
  
      $this->hasOne('CheckEmail', [
        'className' => 'TotalCheckCallEntity',
        'sort' => ['CheckEmail.created' => 'DESC'],
        'strategy' => 'select',
        'conditions' => ['CheckEmail.type' => 'email'],
        'foreignKey' => 'value',
        'bindingKey' => 'email',
        'propertyName' => 'check_email',
      ]);

      $this->hasMany('PartnerAccountPeopleNotesEntity')
        ->setForeignKey('partner_account_people_id')
        ->setProperty('applicant_notes');

      $this->hasMany('LendSignatureRequestEntity')
        ->setForeignKey('partner_account_people_id')
        ->setProperty('lend_signature_requests');

  }

  public function _initializeSchema(TableSchema $schema): TableSchema
  {
    $schema->setColumnType('owner_type', 'integer');
    return parent::_initializeSchema($schema);
  }

  public function beforeMarshal($event, $entity, $options)
  {
    $enc_fields = ['driving_licence_num', 'driving_licence_card_number', 'medicare_number', 'passport_number'];
    foreach($enc_fields as $enc_field){
      if(isset($entity[$enc_field])){
        $entity[$enc_field.'_e'] = EncryptionHelper::safeEncrypt($entity[$enc_field]);
        unset($entity[$enc_field]);
      }
    }
  }
  

  public static function getDuplicate($entityData)
  {
    $conditions = [];
    if (!empty($entityData['id'])) {
      $conditions['id <>'] = intval($entityData['id']);
    }
    if(isset($entityData['partner_id']) && !empty($entityData['partner_id'])) {
      $conditions['partner_id'] = $entityData['partner_id'];
    }

    if(isset($entityData['partner_account_id']) && !empty($entityData['partner_account_id'])) {
      $conditions['partner_account_id'] = $entityData['partner_account_id'];
    }
    if(isset($entityData['email']) && !empty($entityData['email'])) {
      $conditions['email'] = $entityData['email'];
    }
    if(isset($entityData['mobile']) && !empty($entityData['mobile'])) {
      $conditions['mobile'] = $entityData['mobile'];
    }
    if(isset($entityData['first_name']) && !empty($entityData['first_name'])) {
      $conditions['first_name'] = $entityData['first_name'];
    }
    if(isset($entityData['last_name']) && !empty($entityData['last_name'])) {
      $conditions['last_name'] = $entityData['last_name'];
    }

    if(isset($entityData['partner_account_people_id']) && !empty($entityData['partner_account_people_id'])) {
      $conditions['partner_account_people_id !='] = $entityData['partner_account_people_id'];
    }

    $accountPeopleTable = TableRegistry::getTableLocator()->get('PartnerAccountPeopleEntity');
    return $accountPeopleTable
      ->find('all', ['conditions' => $conditions])
      ->order(['created' => 'desc'])
      ->first();

  }

  public function beforeSave($event, $entity, $options)
  {
    // Set partner_user_id from auth token if available
    if (!empty($_COOKIE['auth_token'])) {
      $result = LendAuthenticate::decode(['allowedAlgs' => ['HS256']], $_COOKIE['auth_token']);
      if ($result['success']) {
        $user = TableRegistry::getTableLocator()->get('PartnerUserEntity')->find('all')
          ->where(['email' => $result['payload']->sub])
          ->first();
        
        if ($user && !$entity->has('partner_user_id')) {
          $entity->partner_user_id = $user->partner_user_id;
        }
      }
    }
    
    return true;
  }

  public function afterSave($event, $entity, $options)
  {
    if (empty($entity->group_id)) {
      $duplicate = self::getDuplicate($entity->toArray());
      if (empty($duplicate)) {
        $entity->group_id = $entity->id;
      }
      else {
        if (!empty($duplicate->group_id)) {
          $entity->group_id = $duplicate->group_id;
        }
        else {
          $entity->group_id = $duplicate->id;
        }
      }
      $this->save($entity);
    }
  }

  public static function isValidFirstName($field)
  {
    if (
      isset($field)
      && !empty($field)
      && strlen($field) >= 2
    ) {
      return true;
    }
    return false;
  }

  public static function isValidLastName($field)
  {
    if (
      isset($field)
      && !empty($field)
      && strlen($field) >= 2
    ) {
      return true;
    }
    return false;
  }

  public static function isValidMobile($field)
  {
    $envCountry  = strtolower(getenv('REGION', true));
    if ($envCountry == 'nz') {
      $mobileLength = 11;
    }
    else {
      $mobileLength = 10;
    }
    if (
      isset($field)
      && !empty($field)
      && strlen($field) <= $mobileLength
      && strlen($field) >= 6
    ) {
      return true;
    }
    return false;
  }

  public static function isValidPhone($field)
  {
      $envCountry  = strtolower(getenv('REGION', true));
      if ($envCountry == 'nz') {
          $phoneLength = 11;
      }
      else {
          $phoneLength = 10;
      }
    if (
      isset($field)
      && !empty($field)
      && strlen($field) <= $phoneLength
      && strlen($field) >= 6
    ) {
      return true;
    }
    return false;
  }

  public static function createPartnerAccountLinkPeople($entity, $saveContext)
  {
    $partnerAccount = @$saveContext['entities']['PartnerAccountEntity'];
    if (!empty($partnerAccount)) {
      $partnerAccountLinkPeopleTable = TableRegistry::getTableLocator()->get('PartnerAccountLinkPeopleEntity');
      $accountPocs = $partnerAccountLinkPeopleTable
        ->find()
        ->where([
          'account_id' => $partnerAccount->partner_account_id,
          'is_main_point_of_contact' => true,
        ]);

      $isPoc = true;
      if (!empty($accountPocs)) {
        $isPoc = false;
      }
      $partnerAccountLinkPeopleTable = TableRegistry::getTableLocator()->get('PartnerAccountLinkPeopleEntity');
      return $partnerAccountLinkPeopleTable->createIfNotExists([
        'account_id' => $partnerAccount->partner_account_id,
        'people_id' => $entity->id,
        'is_main_point_of_contact' => $isPoc,
      ]);
    }
  }
  
  public static function formatCallNumber($entityData, $fieldName)
  {
    $mobile = $entityData[$fieldName];
    $mobile = str_replace([' ', '-', '(', ')'], '', $mobile);
    if (substr($mobile, 0, 1) == '0') {
      return $mobile;
    }
    if (substr($mobile, 0, 1) == '+') {
      return $mobile;
    }
    $length = strlen($mobile) + 1;
    return str_pad($mobile, $length, "0", STR_PAD_LEFT);
  }

  public static function formatMobile($entityData)
  {
    return self::formatCallNumber($entityData, 'mobile');
  }

  public static function formatPhone($entityData)
  {
    return self::formatCallNumber($entityData, 'phone');
  }


}
