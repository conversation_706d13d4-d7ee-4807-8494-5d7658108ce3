<?php
namespace App\Model\Table;

use App\Model\Table\AppTable;
use Cake\ORM\TableRegistry;
use App\Lend\LendInternalAuth;

use Cake\Cache\Cache;

class PartnerCommissionsTable extends AppTable
{

    function __construct($arg)
    {
        parent::__construct($arg);
    }

    public function initialize(array $config)
    {
        parent::initialize($config);
    }

    public function getCommission($params)
    {
        $q = TableRegistry::get('App')->getSelectQuery('partner_commissions', $params);
        return $this->DB->execute($q['query'], $q['values'])->fetch('assoc');
    }

    public function getCommissions($params)
    {
        $q = TableRegistry::get('App')->getSelectQuery('partner_commissions', $params);
        return $this->DB->execute($q['query'], $q['values'])->fetchAll('assoc');
    }

    public function getCommissionsForDateRange($partnerId, array $params, $partnerUserIdPassedIn = null)
    {
        if (!isset($params['start_date']) || !isset($params['end_date'])) {
            throw new \Exception("Missing dates");
        }

        $cachedKey = 'getCommissionsForDateRange-partner-'.$partnerId;
        $cachedKey .= '-start-'.$params['start_date'] . '-end-'.$params['end_date'];
        $subWhere = "";
        if (!empty($partnerUserIdPassedIn)) {
            $subWhere = " AND pc.lead_id in (SELECT lead_id FROM partner_user_leads WHERE partner_user_id ='$partnerUserIdPassedIn' AND status='ACCESS')";
        }
      //DO NOT fetch LOC pre-commission row (commission = 0)
        $sql = "SELECT pc.*, l.organisation_name, lo.first_name, lo.last_name, s.product_id, lp.lender_id, le.shorthand, le.lender_name, lm.lender_name as lender_name_match, lm.id, sr.origination_fee
              FROM partner_commissions pc
              LEFT JOIN sale_outcomes so
              	ON so.sale_outcome_id = pc.sale_outcome_id
              LEFT JOIN leads l
              	ON l.lead_id = pc.lead_id
              LEFT JOIN lead_owners lo
              	ON (lo.owner_id = (SELECT max(owner_id) FROM lead_owners WHERE lead_id = l.lead_id AND point_of_contact = 1))
              LEFT JOIN sales s
              	ON s.sale_id = so.sale_id
              LEFT JOIN lender_product lp
              	ON lp.lender_product_id = s.product_id
              LEFT JOIN lenders le
              	ON le.lender_id = lp.lender_id
              LEFT JOIN lead_lender_matches lm
                ON lm.lead_id = l.lead_id
              LEFT JOIN settlement_reviews sr
                ON sr.commission_id = pc.commission_id
              WHERE pc.funded_date >= ?
              	AND pc.funded_date <= ?
                AND (pc.commission <> 0 OR sr.origination_fee <> 0)
                AND pc.is_active = 1
                $subWhere
              	AND pc.partner_id = ?";
        if (strlen($params['start_date']) === 10) {
            $params['start_date'] .= ' 00:00:00';
        }
        if (strlen($params['end_date']) === 10) {
            $params['end_date'] .= ' 23:59:59';
        }
      //set up the order now
      // array of field name => table alias
        $validColumns = array( 'organisation_name' => 'l',  'first_name' => 'lo',
                        'funded_date' => 'pc', 'shorthand' => 'len', 'funded_amount' => 'pc', 'scheduled_date' => 'pc',
                        'funded_type' => 'pc', 'commission' => 'pc'
                      );
      //set default sort column & direction && do validation here
        if (empty($params['sort']) || !in_array($params['sort'], array_keys($validColumns))) {
            $params['sort'] = 'funded_date';
        }
        if (empty($params['direction']) || !in_array($params['direction'], array('asc', 'desc'))) {
            $params['direction'] = 'desc';
        }

      //find the table alias as the prefix
        $prefix = $validColumns[$params['sort']];
      //add sort column & direction to query
        $sql .= ' ORDER BY '.$prefix. '.' .$params['sort'].' '.$params['direction'];
      //special order field
        if ($params['sort'] === 'first_name') {
            $sql .= ','.$prefix. '.last_name '.$params['direction'];
        } elseif ($params['sort'] === 'scheduled_date') {
            $sql .= ','.$prefix. '.status '.$params['direction'];
        }

        $cachedKey .= '-sort-'.$params['sort'].'-'.$params['direction'];

        if (false === ($commissions = Cache::read($cachedKey, 'short'))) {
            $commissions = $this->DB->execute($sql, [$params['start_date'], $params['end_date'], $partnerId])->fetchAll('assoc');
          // Cache::write($cachedKey, $commissions, 'short');
        }

        $hashing = new LendInternalAuth;
        foreach ($commissions as $key => $lead) {
            $commissions[$key]['hashed_lead_id'] = $hashing->hashLeadId($lead['lead_id']);
            $commissions[$key]['hashed_commission_id'] = $hashing->hashLeadId($lead['commission_id']);
            if (!empty($commissions[$key]['lender_name_match'])) {
                $commissions[$key]['lender_name'] = $commissions[$key]['lender_name_match'];
            }
        }

        return $commissions;
    }

    /*
    @param: $commissions - array of partner-commissions
    @param: $params - array of params from request url
    return: two of arrays - filtered commissions and filters
    */
    public function getAvailableFiltersByCommissions($commissions, $params)
    {

        if (! count($commissions)) {
            return array($commissions, $params);
        }

        if (!array_key_exists('filterType', $params) || in_array($params['filterType'], array('', 'undefined'))) {
            $params['filterType'] = '';
        }
        if (!array_key_exists('filterLenders', $params) || in_array($params['filterLenders'], array('', 'undefined'))) {
            $params['filterLenders'] = array();
        }
        if (!array_key_exists('filterPartnerUser', $params) || in_array($params['filterPartnerUser'], array('', 'undefined'))) {
            $params['filterPartnerUser'] = '';
        }

        if (array_key_exists('filterLenders', $params)) {
            $params['filterLendersChecked'] = !empty($params['filterLenders']) ? explode('-', $params['filterLenders']) : array();
            $params['filterLenders'] = array();
        } else {
            $params['filterLenders'] = array();
        }

      //construct new array for
        $params['leadsCounter'] = array('Type'=> array(), 'Lender'=> array());
      //filter options
        foreach ($commissions as $key => $lead) {
          //remember lender where possible
            if (!empty($lead['lender_id']) &&
            (empty($params['filterLenders']) || !in_array($lead['shorthand'], $params['filterLenders']))) {
                $params['filterLenders'][$lead['lender_id']] = $lead['shorthand'];
            }

          //counter begins just right now
            if (!empty($lead['application_type'])) {
                if (!array_key_exists($lead['application_type'], $params['leadsCounter']['Type'])) {
                    $params['leadsCounter']['Type'][$lead['application_type']] = 1;
                } else {
                    $params['leadsCounter']['Type'][$lead['application_type']] += 1;
                }
            } elseif (!empty($lead['funded_type'])) {
                if (!array_key_exists($lead['funded_type'], $params['leadsCounter']['Type'])) {
                    $params['leadsCounter']['Type'][$lead['funded_type']] = 1;
                } else {
                    $params['leadsCounter']['Type'][$lead['funded_type']] += 1;
                }
            }


            if (!empty($lead['lender_id'])) :
                if (!array_key_exists($lead['lender_id'], $params['leadsCounter']['Lender'])) {
                    $params['leadsCounter']['Lender'][$lead['lender_id']] = 1;
                } else {
                    $params['leadsCounter']['Lender'][$lead['lender_id']] += 1;
                }
            endif;
          //end of counter

          //checkbox for lenders
            if (!empty($params['filterLendersChecked']) && !in_array($lead['lender_id'], $params['filterLendersChecked'])) {
                unset($commissions[$key]);
                continue;
            }

          // radio for type
            if (!empty($params['filterType']) && in_array($params['filterType'], array('New', 'Refinance'))) {
              //'Settled' or other status
                if (!empty($lead['funded_type']) && $lead['funded_type'] != $params['filterType']) {
                    unset($commissions[$key]);
                    continue;
                } elseif (!empty($lead['application_type']) && $lead['application_type'] != $params['filterType']) {
                    unset($commissions[$key]);
                    continue;
                }
            }
        }

        return array($commissions, $params);
    }




    public function getPartnersCommisionsByLeadId($lead_id)
    {


        $sql = "select * from partner_commissions where partner_commissions.lead_id = ? order by commission_id desc limit 1"; // todo: add limit removed from UI funded button //
      
        $p_commissions = $this->DB->execute($sql, [$lead_id])->fetch('assoc');

        return array("commission"=> $p_commissions['commission'],
                    "funded_date"=> $p_commissions['funded_date'],
                    "funded_amount"=> $p_commissions['funded_amount'],
                    "commission_expected"=> $p_commissions['commission_expected'],
                    "intermediary_lender_commision"=> $p_commissions['intermediary_lender_commision'],
                    "drawdown_amount"=> $p_commissions['drawdown_amount'],
                    "drawdown_date"=> $p_commissions['drawdown_date'],
                    "commission_id"=> $p_commissions['commission_id']

                  );


      //return $commissions;
    }

    public function getCommissionsDataForSettlement($leadId)
    {
        $query = <<<SQL
select c.commission_id, c.funded_amount, c.intermediary_lender_commision, c.commission, c.commission_expected,
       so.sale_outcome_id, s.sale_id, lp.product_name, ppt.product_type_name, c.funded_date, l.lender_name,
       s.off_panel_lender, IF(s.off_panel_lender IS NULL,0,1) as is_off_panel,
       lad.max_lender_id as lad_max_lender_id,
       lad.lender_name as lad_lender_name
from partner_commissions as c
left join sale_outcomes so on so.sale_outcome_id = c.sale_outcome_id
left join sales s on so.sale_id = s.sale_id
left join lender_product lp on s.product_id = lp.lender_product_id
left join lenders l on l.lender_id = lp.lender_id
left join partner_product_types ppt on s.product_type_id = ppt.product_type_id
left join lead_associated_data lad ON lad.lead_id = c.lead_id
where c.lead_id = ?
SQL;

        return $this->DB->execute($query, [$leadId])->fetchAll('assoc');
    }

    public function getLeadBrokerDetails($hashedLeadId, $partnerId, $commissionId = null)
    {
        $leadId = (new LendInternalAuth)->unhashLeadId($hashedLeadId);
        $sql = 'SELECT pc.*, p.company_name, p.abn, p.address, p.suburb, p.state, p.postcode,
              p.bank_acc_name, p.bank_bsb, p.bank_acc_num, p.logo, pu.email, l.organisation_name,
              p.is_gst_registered
              FROM partner_commissions pc
              LEFT JOIN leads l
              ON pc.lead_id = l.lead_id
              LEFT JOIN partners p
              ON p.partner_id = pc.partner_id
              LEFT JOIN partner_users pu
              ON pu.partner_id = p.partner_id and pu.point_of_contact = "1"
              WHERE pc.partner_id = ?
              AND pc.lead_id = ?
                ';
        $val = array($partnerId, $leadId);
      //if commission_id is passed in, then put into conditions as well
        if (!empty($commissionId)) {
            $sql .= ' AND pc.commission_id = ? ';
            $val[] = $commissionId;
        }
        $row = $this->DB->execute($sql, $val)->fetchAll('assoc');
        if (empty($row)) {
            throw new \Exception("Lead Not Found");
        }

      //construct an array that contains below info :
        $ret =  array(
          'brokerName' => '',
          'brokerRegisterGST' => '',
          'brokerAddress' => array(
            'street' => '',
            'suburb' => '',
            'state' => '',
            'country' => ''
          ),
          'brokerBank' => array(
            'accountName' => '',
            'bsb' => '',
            'accountNumber' => '',
          ),
          'brokerEmail' => '',
          'brokerABN' => '',
          'row' => array( 'funded_date' => null,
                          'client_name' => null,
                          'hashed_lead_id' => null,
                          'funded_amount' => null,
                          'commission' => null,
                          'scheduled_date' => null,
                    )
        );
        if (isset($row[0])) {
            $row = $row[0];
        } else {
            throw new \Exception('Lead Not Found');
        }

        if (!empty($row['company_name'])) {
            $ret['brokerName'] = $row['company_name'];
        }
        if (!empty($row['address'])) {
            $ret['brokerAddress']['street'] = $row['address'];
        }
        if (!empty($row['suburb'])) {
            $ret['brokerAddress']['suburb'] = $row['suburb'];
        }
        if (!empty($row['state'])) {
            $ret['brokerAddress']['state'] = !empty($row['postcode']) ? $row['state'].' '.$row['postcode'] : $row['state'];
        }
        if (!empty($row['country'])) {
            $ret['brokerAddress']['country'] = $row['country'];
        }
        if (!empty($row['abn'])) {
            $ret['brokerABN'] = $row['abn'];
        }
        if (!empty($row['email'])) {
            $ret['brokerEmail'] = $row['email'];
        }
        if (!empty($row['funded_date'])) {
            $ret['row']['funded_date'] = $row['funded_date'];
        }
        if (!empty($row['funded_amount'])) {
            $ret['row']['funded_amount'] = $row['funded_amount'];
        }
        if (!empty($row['commission'])) {
            $ret['row']['commission'] = $row['commission'];
        }
        if (!empty($row['scheduled_date'])) {
            $ret['row']['scheduled_date'] = $row['scheduled_date'];
        }
        if (!empty($row['organisation_name'])) {
            $ret['row']['client_name'] = $row['organisation_name'];
        }
        if (!empty($row['bank_acc_name'])) {
            $ret['brokerBank']['accountName'] = $row['bank_acc_name'];
        }
        if (!empty($row['bank_bsb'])) {
            $ret['brokerBank']['bsb'] = $row['bank_bsb'];
        }
        if (!empty($row['bank_acc_num'])) {
            $ret['brokerBank']['accountNumber'] = $row['bank_acc_num'];
        }
        if (!empty($row['bank_acc_num'])) {
            $ret['brokerBank']['accountNumber'] = $row['bank_acc_num'];
        }

        $ret['brokerRegisterGST'] =  ($row['is_gst_registered'] !="0") ? 1 : 0;

        $ret['row']['hashed_lead_id'] = $hashedLeadId;

        return $ret;
    }


    public function updateDrawdownAmount($data)
    {


        $drawdown_amount = $data['PartnerCommissions']['drawdown_amount'];
        $drawdown_date   = $data['PartnerCommissions']['drawdown_date'];

        $drawdown_date = strpos($drawdown_date, '/') !== false ? implode('-', array_reverse(explode('/', $drawdown_date))):  $drawdown_date ;
        if (strlen($drawdown_date) === 10) {
            $drawdown_date = $drawdown_date.' 00:00:00';
        }

        $commission_id   = $data['p_commission_id_hidden'];
  
        $sql = "update partner_commissions set drawdown_amount = ?, drawdown_date = ? where commission_id = ?";
        $this->DB->execute($sql, [ $drawdown_amount,   $drawdown_date ,   $commission_id ]);

        return true;
    }

  //$partner_id is lender id //
    public function getLastLenderCommisionsRelatedRecord($lead_id)
    {
 

        $get_last_product_type = $this->getLastFundedProductType($lead_id);




        if ($get_last_product_type == 'Line of Credit' || $get_last_product_type == 'Business Overdraft') {
            $sql = "SELECT * 
              FROM
                partner_lender_funded
                JOIN partner_commissions ON partner_lender_funded.lead_id = partner_commissions.lead_id 
              WHERE
                partner_commissions.lead_id = ? and partner_lender_funded.product_type in ('line of credit','business overdraft')
              ORDER BY
                partner_lender_funded.partner_lender_funded_id DESC,
                partner_commissions.commission_id DESC 
              LIMIT 1";

            $row = $this->DB->execute($sql, [$lead_id])->fetch('assoc');

            if (empty($row)) {
                return false;
            }
            return $row;
        } else {
            return false;
        }
    }

    private function getLastFundedProductType($lead_id)
    {

        $sql = 'select product_type from partner_lender_funded where lead_id = ? order by partner_lender_funded.partner_lender_funded_id DESC LIMIT 1';
        return $row = $this->DB->execute($sql, [$lead_id])->fetch('assoc')['product_type'];
    }
}
