<?php
namespace App\Model\Table;

use App\Model\Entity\LeadNotes;
use Cake\ORM\Table;
use App\Lend\SocketHelper;
use App\Lend\KanbanHelper;
use Cake\ORM\TableRegistry;

class LeadNotesEntityTable extends Table
{
  protected $_table = 'lead_notes';

  protected $_primaryKey = 'note_id';

  protected $_entityClass = LeadNotes::class;

  public function initialize(array $config)
  {
    $this->addBehavior('CustomTimestamp');
    
    $this->belongsTo('LeadEntity')
      ->setForeignKey('lead_id')
      ->setProperty('lead');

    $this->belongsTo('PartnerUserEntity')
    ->setForeignKey('partner_user_id')
    ->setProperty('partner_user');

    $this->belongsTo('PartnerAccountEntity')
    ->setForeignKey('partner_account_id')
    ->setProperty('partner_account');
    
  }

  public function afterSave($event, $entity, $options) {
    if (empty($entity->lead_id)) {
      return;
    }
    $lead = TableRegistry::getTableLocator()->get('LeadEntity')->get($entity->lead_id, [
      'fields' => [
          'lead_id',
          'partner_id',
      ]
    ]);

    if(KanbanHelper::partnerHasKanbanV2Access($lead->partner_id)){
      SocketHelper::kanbanUpdateLead($entity->lead_id);
    }
  }
}
