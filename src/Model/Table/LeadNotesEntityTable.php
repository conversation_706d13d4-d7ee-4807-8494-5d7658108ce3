<?php
namespace App\Model\Table;

use App\Model\Entity\LeadNotes;
use Cake\ORM\Table;
use App\Lend\SocketHelper;
use Cake\ORM\TableRegistry;

class LeadNotesEntityTable extends Table
{
  protected $_table = 'lead_notes';

  protected $_primaryKey = 'note_id';

  protected $_entityClass = LeadNotes::class;

  public function initialize(array $config)
  {
    $this->addBehavior('CustomTimestamp');
    
    $this->belongsTo('LeadEntity')
      ->setForeignKey('lead_id')
      ->setProperty('lead');

    $this->belongsTo('PartnerUserEntity')
    ->setForeignKey('partner_user_id')
    ->setProperty('partner_user');

    $this->belongsTo('PartnerAccountEntity')
    ->setForeignKey('partner_account_id')
    ->setProperty('partner_account');
    
  }

  public function afterSave($event, $entity, $options) {
    if (empty($entity->lead_id)) {
      return;
    }
    $lead = TableRegistry::getTableLocator()->get('LeadEntity')->get($entity->lead_id, [
      'fields' => [
          'lead_id',
          'partner_id',
      ],
      'contain' => [
          'PartnerEntity' => ['fields' => ['partner_id', 'status_system']],
          'PartnerEntity.PartnerFeatureFlagEntity' => ['fields' => ['partner_id', 'access_to_kanban_v2']],
      ]
    ]);

    if(($lead->partner->status_system === "manual") && !empty($lead->partner->feature_flag->access_to_kanban_v2)){
      SocketHelper::kanbanUpdateLead($entity->lead_id);
    }
  }
}
