<?php
namespace App\Model\Table;

use Cake\ORM\Query;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;
use Cake\Datasource\ConnectionManager;
use Cake\Event\Event;
use Cake\Auth\DefaultPasswordHasher;
use Cake\ORM\TableRegistry;
use Hashids\Hashids;
use Cake\Core\Configure;
use \Aws\S3\S3Client;

use App\Model\Table\AppTable;
use phpseclib\Crypt\RSA;
use Cake\Log\Log;
use App\Traits\S3Trait;

class PartnerUsersTable extends Table
{
    use S3Trait;
    public $partner_notifications;
    protected $DB;

    public function __construct($arg)
    {
      parent::__construct($arg);
      $this->partner_notifications = TableRegistry::get('PartnerNotifications');
    }
    public function initialize(array $config){
        parent::initialize($config);
        $this->DB = ConnectionManager::get('default');
    }

    protected function _setPassword($password) {
        if (strlen($password) > 0) {
            return (new DefaultPasswordHasher)->hash($password);
        }
    }

    public function checkPassword($passwordToCheck, $realPassword) {
      return (new DefaultPasswordHasher)->check($passwordToCheck, $realPassword);
    }

    public function addPartnerUser($params){
      if($params){
        $params['created'] = date('Y-m-d H:i:s');
        $params['password'] = $this->_setPassword($params['password']);
        $params['level'] = isset($params['level']) ? $params['level'] : 1; // it should default to 1

        $poc = $this->getPartnerUser(array('partner_id' => $params['partner_id'], 'point_of_contact' => 1));
        $params['account_admin']    = ($poc != null ? 0 : 1);
        $params['point_of_contact'] = isset($params['point_of_contact']) ? $params['point_of_contact'] : ($poc != null ? 0 : 1);

        $params['active'] = isset($params['active']) ? $params['active'] : 0;
        $params['notification_recipient'] = isset($params['notification_recipient']) ? $params['notification_recipient'] : 1;

        if(isset($params['mobile'])){
          $params['mobile'] = preg_replace("/[^0-9]/", "", $params['mobile'] );//only numbers allowed
        }
        if(isset($params['phone'])){
            $params['phone'] = preg_replace("/[^0-9]/", "", $params['phone'] );//only numbers allowed
        }

        $q = TableRegistry::get('App')->getInsertQuery('partner_users', $params);
      }
      $query = $this->DB->execute($q['query'], $q['values']);
      $partner_user_id = $query->lastInsertId();

      // Add partner_user_ref
      $hashids = new Hashids('partner_users', 7);
      $partner_user_ref = $hashids->encode($partner_user_id);
      $this->updatePartnerUser($partner_user_id, ['partner_user_ref'=>$partner_user_ref]);

      return $partner_user_id;
    }

    //these rules must be consistent with ones in JS
    public function isValidPass($password)
    {
      return (strlen($password) < 10) //10+ chars
        || strtoupper($password) == $password  //contain lower case
        || strtolower($password) == $password //contain upper case
        || !preg_match('/[0-9]/', $password) //contain digits
        || !strpbrk($password, '[ !~@#$%^&*()_+\-=[]{};:\'"|,.<>/?]')  //contain special chars
        ? false : true;
    }

    public function getPartnerUserIdsAssignedToLead($partnerId, $leadId) {
      //who has access to this lead
      $partnerUsersAccessToLead = $this->DB->execute("SELECT * FROM partner_user_leads WHERE lead_id = ? and status = ?", [$leadId, 'ACCESS'])->fetchAll('assoc');
      $result = [];
      if (!empty($partnerUsersAccessToLead)) {
        //get all user ids who has been assigned to this lead
        $accessListUserIds = [];
        foreach ($partnerUsersAccessToLead as $row)
        {
          $accessListUserIds[] = $row['partner_user_id'];
        }
        //all active users belong to this partner
        $rows = $this->getPartnerUser(['partner_id' => $partnerId], true);

        foreach ($rows as $row)
        {
          //check if the user is active, then push to result
          if (in_array($row['partner_user_id'], $accessListUserIds)) {
            $result[] = $row['partner_user_id'];
          }
        }
      }
      return $result;
    }

    public function getPartnerUsers($partnerID) {
      $pusrs = $this->DB->execute('SELECT * FROM partner_users WHERE partner_id = ?', [$partnerID])->fetchAll('assoc');
      foreach ($pusrs as $key => $value) unset($pusrs[$key]['password']);
      return $pusrs;
    }

    public function getAdminPartnerUser($partnerID) {
      $q = TableRegistry::get('App')->getSelectQuery('partner_users', ['partner_id'=>$partnerID, 'account_admin'=>1], false);
      return $this->DB->execute($q['query'], $q['values'])->fetch('assoc');  
    }

    public function getPartnerUsersWithAccessToGivenLead($partnerId, $leadId){
      $rows = $this->getPartnerUser(['partner_id' => $partnerId], true);

      //check related records in `partner_user_leads`
      $puLeads = $this->DB->execute("SELECT * FROM partner_user_leads WHERE lead_id = ? ", [$leadId])->fetchAll('assoc');
      $accessListUserIds = [];
      foreach ($puLeads as $row) {
        if (strtolower($row['status']) == 'access' && !empty($row['partner_user_id']))
          $accessListUserIds[] = $row['partner_user_id'];
      }

      foreach($rows as $k => $row) {
        //flag = 0, check further to see if the user has access permission to this lead, remove it if not
        if (empty($row['access_all_leads']) && !in_array($row['partner_user_id'], $accessListUserIds))
          unset($rows[$k]);
      }
      return $rows;
    }

    public function getPartnerUser($params, $all=false){
      $q = TableRegistry::get('App')->getSelectQuery('partner_users', $params, false);
      if ($all) return $this->DB->execute($q['query'], $q['values'])->fetchAll('assoc');
      return $this->DB->execute($q['query'], $q['values'])->fetch('assoc');
    }

    public function updatePartnerUser($partnerUserID, $params){
      if (!empty($params['password'])){
         $params['password'] = $this->_setPassword($params['password']);
      }
      if(isset($params['mobile'])){
          $params['mobile'] = preg_replace("/[^0-9]/", "", $params['mobile'] );//only numbers allowed
      }
      if(isset($params['phone'])){
          $params['phone'] = preg_replace("/[^0-9]/", "", $params['phone'] );//only numbers allowed
      }
      $q = TableRegistry::get('App')->getUpdateQuery('partner_users', $params, array('partner_user_id'=>$partnerUserID));

        try {
            $res = $this->DB->execute($q['query'], $q['values']);
            $ret = array('success' => true,
                'data' => $res );
        }catch (\Exception $e){
            $ret = array('success' => false,
                'error' => $e->getMessage() );
        }

      return $ret;
    }

    public function verifyPartnerUserEmail($email)
    {
      try {
        if (strpos($email, '@') === false || substr_count($email, '@') > 1)
          throw new \Exception("Email Address not found");

        $sql = " SELECT partner_user_id, partner_id, email, email_verified, notification_recipient, active "
               . " FROM partner_users "
               . " WHERE email = ? "
               . " ORDER BY password desc, partner_user_id desc";

        $user = $this->DB->execute($sql, [$email])->fetch('assoc');

        if (empty($user['partner_user_id']))
          throw new \Exception("Account not found");

        if (!empty($user['email_verified']))
          throw new \Exception("Email Address has been verified before");

        $q = TableRegistry::get('App')->getUpdateQuery('partner_users',
                  array('email_verified' => '1', 'notification_recipient' => '1'),
                  array('partner_user_id'=>$user['partner_user_id']));

        if (!$this->DB->execute($q['query'], $q['values']))
          throw new \Exception("Error occurs, please try later");

        $ret = array('success' => true,
                    'error' => false);
      } catch (\Exception $e) {
        $ret = array('success' => false,
                    'error' => $e->getMessage() );
      }
      return $ret;
    }

    public function generateForgotPassCode($email)
    {
      $sql = " SELECT partner_user_id, partner_id, email, email_verified, notification_recipient, active, token, token_expired "
             . " FROM partner_users "
             . " WHERE email = ? "
             . " ORDER BY password desc, partner_user_id desc";
      $partnerUser = $this->DB->execute($sql, [$email])->fetch('assoc');
      if (empty($partnerUser['partner_user_id']))
        return false;

      //generate token here
      $params = array(
        'token' => md5(uniqid(rand(), true)),  // generate unique string
        'token_expired' => (new \DateTime())->add(new \DateInterval('PT30M'))->format('Y-m-d H:i:s'),  // in 30 mins
        'token_generated' => date('Y-m-d H:i:s')
      );

      $q = TableRegistry::get('App')->getUpdateQuery('partner_users', $params, array('partner_user_id'=>$partnerUser['partner_user_id']));
      return $this->DB->execute($q['query'], $q['values']) ? $params['token'] : null;
    }

    public function getUsersByPartnerIds($partnerIds)
    {
      $sql = 'SELECT name, partner_id
              FROM partner_users
              WHERE partner_id in (' . implode(',', $partnerIds) . ')
              ORDER BY partner_id ASC';
      $rows = $this->DB->execute($sql,[])->fetchAll('assoc');
      $ret = [];
      foreach ($rows as $row)
        $ret[$row['partner_id']][] = ucwords(strtolower($row['name']));
      return $ret;
    }

    public function getActiveUsers($partnerId, $return_id_name=true)
    {
      $sql = 'SELECT partner_user_id, name
              FROM partner_users
              WHERE partner_id = ?
              AND active = ? ';
      $rows = $this->DB->execute($sql,[$partnerId, '1'])->fetchAll('assoc');
      if($return_id_name){
        $ret = [];
        foreach ($rows as $row) {
          $ret[$row['partner_user_id']] = $row['name'];
        }

        return $ret;
      }else{
        return $rows;
      }
    }

    public function getPartnerUserRefOfPOC($partnerId)
    {
      $sql = 'SELECT name, partner_user_ref, partner_id, partner_user_id
              FROM partner_users
              WHERE partner_id = ?
              AND point_of_contact = ?
              ORDER by active DESC';
      $row = $this->DB->execute($sql,[$partnerId, '1'])->fetch('assoc');
      return !empty($row['partner_user_ref']) ? $row['partner_user_ref'] : null;
    }

    public function uploadAccreditation($tmpfile, $template_id = null)
    {
        $bucket = 'signature-service-templates'.(getenv('LEND_ENV')=='2' ? '' : '-staging');
        $s3filename = $tmpfile['name'];
        $partnerId = $tmpfile['partner_id'];

        $contentType = '';
        $mimeTypes = array(
          'png' => 'image/png',
          'jpeg' => 'image/jpeg',
          'jpg' => 'image/jpeg',
          'gif' => 'image/gif',
          'bmp' => 'image/bmp',
          'tiff' => 'image/tiff',
          'tif' => 'image/tiff',
          'pdf' => 'application/pdf',
          'doc' => 'application/msword',
          'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        );
        $ext = pathinfo($s3filename, PATHINFO_EXTENSION);
        if(array_key_exists($ext, $mimeTypes)){
          $contentType = $mimeTypes[$ext];
        }

        $this->prepareS3(false); // Initiate connection if not already

        // Build the path where to save the file in our usual format
        $s3_path = (in_array((string) getenv('LEND_ENV'), array('0', '1'))) ? 'dev' . DS : 'production'.DS; // So development is seperated from live
        $s3_path .= date('Ymd') . DS;
        if (!empty($template_id)) {
          $s3_path .= $template_id . DS;
        }
        $s3_file = date('dmYGis') . '-' . str_replace(' ', '_', $tmpfile['name']); // Filename now
        $s3_full = $bucket.'/'.$s3_path . '' . date('dmYGis') . '-' . str_replace(' ', '_', $tmpfile['name']); // Filename now
        $s3_full = str_replace('\\', '/', $s3_full);

        // Upload to S3
        try {
            $this->s3Client->putObject([ // Put the backup in the bucket
                'ACL'        => 'public-read',
                'Bucket'     => $bucket,
                'Key'        => $s3_full,
                'SourceFile' => $tmpfile['files'][0]['tmp_name'],
                'ContentType' => $contentType,
            ]);
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
            return false;
        }

        return array(
            's3path' => $s3_path,
            's3filename' => $s3_file,
            's3full' => $s3_full,
        );
        if (!$this->s3Client) {
            $this->prepareS3(false);
        } // Initiate connection if not already
    }
    
    public function updateWisrPartnerCompanyId($wisrPartnerCompanyId, $user)
    {
        $wisr_lender_id = TableRegistry::get('LenderEntity')->find('all')->where(['shorthand' => 'wisr'])->first()->lender_id;

        $this->updateCredentials($wisrPartnerCompanyId, $user, $wisr_lender_id, 'lend', 'keys.lend.public');
        $this->updateCredentials($wisrPartnerCompanyId, $user, $wisr_lender_id, 'external', 'keys.external.public');
    }

    private function updateCredentials($wisrPartnerCompanyId, $user, $wisr_lender_id, $keyPair, $publicKeyConfig)
    {
        $publicKey = Configure::read($publicKeyConfig); 
        $keyForEncryption = new RSA();
        $keyForEncryption->loadKey($publicKey);
        $keyForEncryption->setEncryptionMode(RSA::ENCRYPTION_PKCS1);
        $keyForEncryption->setHash('sha512');
        $encryptedPartnerCompanyId = $keyForEncryption->encrypt($wisrPartnerCompanyId);
        $base64EncodedEncryptedPartnerCompanyId = base64_encode($encryptedPartnerCompanyId);

        $data_to_update = [
            'partner_company_id' => $base64EncodedEncryptedPartnerCompanyId,
            'partner_user_id' => $user['partner_user_id'],
            'partner_id' => $user['partner_id'],
            'lender_id' => $wisr_lender_id,
            'key_pair' => $keyPair
        ];

        $credentialsTable = TableRegistry::get('PartnerLenderCredentials');

        $credentials = $credentialsTable
            ->find()
            ->where([
                'partner_user_id' => $user['partner_user_id'],
                'lender_id' => $wisr_lender_id,
                'key_pair' => $keyPair
            ])
            ->first();

        if ($credentials) {
            $credentials = $credentialsTable->patchEntity($credentials, $data_to_update);
        } else {
            $credentials = $credentialsTable->newEntity($data_to_update);
        }

        if ($credentialsTable->save($credentials, [], false)) {
            Log::write('debug', 'Entity saved successfully.');
        } else {
            Log::write('debug', 'Failed to save record.');
        }
    }



  public function getUserInfoForSignature($user)
  {
    $partner = TableRegistry::getTableLocator()->get('PartnerEntity')
      ->get($user['partner_id'])->toArray();
    return [
      'name' => $user['name'],
      'partner_user_id' => $user['partner_user_id'],
      'partner_user_ref' => $user['partner_user_ref'],
      'email' => $user['email'],
      'mobile' => $user['mobile'] ?? '',
      'first_name' => $user['first_name'],
      'last_name' => $user['last_name'],
      'position' => $user['position'] ?? '',
      'title' => $user['title'] ?? '',
      'account_admin' => $user['account_admin'],
      'created' => $user['created'],
      'partner_ref' => $partner['partner_ref'],
      'partner_name' => $partner['company_name'],
      'partner_logo' => $partner['logo'] ? 'https://files.lend.com.au/' . $partner['logo'] : "",
      'partner_trading_address' => $partner['trading_address'] ?? '',
    ];
  }


}
