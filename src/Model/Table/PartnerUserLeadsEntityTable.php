<?php

namespace App\Model\Table;

use App\Lend\KanbanHelper;
use App\Lend\SocketHelper;
use App\Model\Entity\PartnerUserLead;
use Cake\ORM\Table;
use Cake\ORM\TableRegistry;
use Cake\Datasource\EntityInterface;
use App\Auth\LendAuthenticate;
use Cake\Core\Configure;
use Cake\Datasource\ConnectionManager;

class PartnerUserLeadsEntityTable extends Table
{
  protected $_table = 'partner_user_leads';
  protected $_primaryKey = 'partner_user_lead_id';

  protected $_entityClass = PartnerUserLead::class;

  public function initialize(array $config)
  {
    $this->addBehavior('CustomTimestamp');

    $this->belongsTo('LeadEntity')
          ->setForeignKey('lead_id')
          ->setProperty('LeadEntity');

    $this->belongsTo('PartnerUserEntity')
          ->setForeignKey('partner_user_id')
          ->setProperty('user');

    parent::initialize($config);
  }

  public function beforeSave($event, EntityInterface $entity, $options)
  {
    if ($entity->isNew()) {
      $entity->granted = date('Y-m-d H:i:s');
    }
    return $entity;
  }

  public function afterSave($event, EntityInterface $entity, $options)
  {
    if (!empty($_COOKIE['auth_token'])) {
      $result = LendAuthenticate::decode(['allowedAlgs' => ['HS256']], $_COOKIE['auth_token']);
      if ($result['success']) {
        $user = TableRegistry::getTableLocator()->get('PartnerUserEntity')->find('all')
          ->where(['email' => $result['payload']->sub])
          ->first();
      }
    }

    $lead_table = TableRegistry::getTableLocator()->get('LeadEntity');
    $lead = $lead_table
          ->find()->select(['lead_id', 'lead_ref', 'partner_id' , 'call_queue_status', 'source', 'man_status_id', 'product_type_id'])
          ->where(['LeadEntity.lead_id' => $entity->lead_id])
          ->first();
    //check if partner is on manual status
    $partner = TableRegistry::getTableLocator()->get('PartnerEntity')->get($lead->partner_id, [
      'fields' => [
        'partner_id', 'status_system'
      ],
      'contain' => [
        'PartnerFeatureFlagEntity' => ['fields' => ['partner_id', 'access_to_kanban_v2']],
      ]
    ]);

    $partnerIsManual = (($partner->status_system === "manual") && !empty($partner->feature_flag->access_to_kanban_v2));

    if ($entity->isNew() && $entity->status === 'ACCESS') {
      $previousAssignment = $this->find()
          ->where([
              'lead_id' => $entity->lead_id,
              'status' => 'REVOKED',
              'partner_user_id !=' => $entity->partner_user_id
          ])
          ->order(['partner_user_lead_id' => 'DESC'])
          ->first();

      $partnerUser = TableRegistry::getTableLocator()->get('PartnerUserEntity')
        ->find()->select(['PartnerUserEntity.name'])
        ->where(['PartnerUserEntity.partner_user_id' => $previousAssignment->partner_user_id ?? null])
        ->first();

      $adhocData = [
          'prev_partner_name' => $partnerUser ? $partnerUser->name : '',
      ];

      if($lead->call_queue_status != -2 && (empty($user) || $user->partner_user_id !== $entity->partner_user_id)){
        TableRegistry::getTableLocator()->get('PartnerNotifications')->sendPartnerNotifications(
          $lead->partner_id,
          'AssignLead',
          $entity->lead_id,
          $adhocData,
          $entity->partner_user_id,
          null
        );
      }

      if($previousAssignment && ($lead->call_queue_status != -2)){//lead was previously assigned to someone and not being assigned by takeLead
        $currentPartnerUser = TableRegistry::getTableLocator()->get('PartnerUserEntity')
          ->find()->select(['PartnerUserEntity.name'])
          ->where(['PartnerUserEntity.partner_user_id' => $entity->partner_user_id])
          ->first();

        $adhocData['partner_user_name'] = $currentPartnerUser->name;
        TableRegistry::getTableLocator()->get('PartnerNotifications')->sendPartnerNotifications(
          $lead->partner_id,
          'LeadAssignedNewUser',
          $entity->lead_id,
          $adhocData,
          $previousAssignment->partner_user_id,
          null
        );
      }
    }
    if($entity->status === 'ACCESS'){
      //Revoke any other 'ACCESS' rows for this lead
      $otherAccessRows = $this->find()
        ->where([
          'lead_id' => $entity->lead_id,
          'status' => 'ACCESS',
          'partner_user_lead_id !=' => $entity->partner_user_lead_id // Exclude the current row
        ])
        ->all();

        if ($otherAccessRows->count() > 0) {
          $entitiesData = [];
          $now = date('Y-m-d H:i:s');
          foreach ($otherAccessRows as $accessRow) {
            $entitiesData[] = [
                'partner_user_lead_id' => $accessRow->partner_user_lead_id,
                'status' => 'REVOKED',
                'updated'=> $now
            ];
          }
          $entitiesToUpdate = $this->patchEntities($otherAccessRows->toArray(), $entitiesData);
          $this->saveMany($entitiesToUpdate);
        }
      if($partnerIsManual){
        // Create a record in PartnerLeadHistoryEntity for ACCESS
        if ($entity->isNew()) {
          $partnerLeadHistoryTable = TableRegistry::getTableLocator()->get('PartnerLeadHistoryEntity');
          $partnerLeadHistoryEntity = $partnerLeadHistoryTable->newEntity([
            'partner_id' => $lead->partner_id,
            'partner_user_id' => !empty($user) ? $user->partner_user_id : $entity->partner_user_id,
            'lead_id' => $entity->lead_id,
            'created' => date('Y-m-d H:i:s'),
            'history_detail' => 'Lead Assigned',
            'ref_type' => 'partner_user_id',
            'ref_id' => $entity->partner_user_id,
          ]);
          $partnerLeadHistoryTable->save($partnerLeadHistoryEntity);
        }

        KanbanHelper::removeLead($entity->partner_user_id, $lead->lead_ref, true);//should not be needed but here to prevent duplicates
        KanbanHelper::addLead($entity->partner_user_id, $lead->lead_ref);
        SocketHelper::kanbanAddLead($lead->lead_id, [$entity->partner_user_id]);//add to the assigned users board
      }  
    }
    else if(($partnerIsManual && $entity->status === 'REVOKED') && ($lead->partner_id !== Configure::read('Lend.LEND_TRASHED_LEAD_PARTNER_ID'))){
        // Create a record in PartnerLeadHistoryEntity for REVOKED
        $partnerLeadHistoryTable = TableRegistry::getTableLocator()->get('PartnerLeadHistoryEntity');
        $partnerLeadHistoryEntity = $partnerLeadHistoryTable->newEntity([
            'partner_id' => $lead->partner_id,
            'partner_user_id' => !empty($user) ? $user->partner_user_id : $entity->partner_user_id,
            'lead_id' => $entity->lead_id,
            'created' => date('Y-m-d H:i:s'),
            'history_detail' => 'Lead Revoked',
            'ref_type' => 'partner_user_id',
            'ref_id' => $entity->partner_user_id,
        ]);
        $partnerLeadHistoryTable->save($partnerLeadHistoryEntity);

      KanbanHelper::removeLead($entity->partner_user_id, $lead->lead_ref, true);
      SocketHelper::kanbanRemoveLead($lead->lead_id, $lead->lead_ref, $lead->man_status_id, $lead->product_type_id, [$entity->partner_user_id]);
    }
    if($partnerIsManual){
      SocketHelper::kanbanUpdateLead($entity->lead_id);//update lead data on other users boards
    }
  }
}
