<?php

namespace App\Model\Table;

use App\Auth\LendAuthenticate;
use App\Lend\KanbanHelper;
use App\Lend\SocketHelper;
use App\Lend\LendInternalAuth;
use App\Model\Entity\PartnerUser;
use Cake\Database\Schema\TableSchema;
use Cake\ORM\Table;
use Cake\Datasource\EntityInterface;
use Cake\ORM\TableRegistry;


class PartnerUserEntityTable extends Table
{
    protected $_table = 'partner_users';

    protected $_primaryKey = 'partner_user_id';

    protected $_entityClass = PartnerUser::class;

    public function initialize(array $config)
    {
        $this->belongsTo('PartnerEntity')
            ->setForeignKey('partner_id')
            ->setProperty('partner');

        $this->hasMany('ConPartnerUserFeeDefaultEntity')
            ->setConditions(['ConPartnerUserFeeDefaultEntity.active' => true])
            ->setForeignKey('partner_user_id')
            ->setProperty('con_partner_user_fee_default');

        $this->hasOne('ConPartnerUserSettingsEntity')
            ->setForeignKey('partner_user_id')
            ->setProperty('user_settings');

        $this->hasMany('LeadNotesEntity')
            ->setForeignKey('partner_user_id')
            ->setProperty('lead_notes');

        $this->hasMany('PartnerCallbackEntity')
            ->setForeignKey('partner_user_id')
            ->setProperty('tasks');

        $this->hasMany('PartnerUserLeadsEntity')
            ->setForeignKey('partner_user_id')
            ->setProperty('partner_user_leads');

        $this->hasMany('ConReqAndObjEntity')
            ->setForeignKey('partner_user_id')
            ->setProperty('con_req_and_obj');

        $this->hasMany('ConPrelimEntity')
            ->setForeignKey('partner_user_id')
            ->setProperty('con_preliminary');

        $this->hasMany('ConCreditPropSendEntity')
            ->setForeignKey('partner_user_id')
            ->setProperty('con_credit_prop_send');
            
        $this->hasMany('KanbanLeadPositionsEntity')
            ->setForeignKey('partner_user_id')
            ->setProperty('kanban_lead_positions');

        $this->hasMany('PartnerNotificationSettingEntity')
            ->setForeignKey('ref_id')
            ->setConditions(['PartnerNotificationSettingEntity.ref_type' => 'partner_user_id'])
            ->setProperty('partner_notification_settings');

        $this->belongsToMany('Referrers', [
            'through' => 'ReferrerPartnerUserAccessEntity',
            'foreignKey' => 'partner_user_id',
            'targetForeignKey' => 'referrer_id',
            'conditions' => ['Referrers.active_status' => 1]
        ])
        ->setProperty('referrers');

        $this->hasMany('PartnerNoticeDismissedEntity', [
            'foreignKey' => 'partner_user_id',
        ]);
    }

    public function _initializeSchema(TableSchema $schema)
    {
        $schema->setColumnType('accreditation_list', 'json');
        $schema->setColumnType('kanban_leads', 'json');
        $schema->setColumnType('kanban_filter', 'json');
        $schema->setColumnType('closed_leads_filter', 'json');

        return $schema;
    }

    public function afterSave($event, EntityInterface $entity, $options)
    {
        $partnerId = $entity->partner_id ??$this->get($entity->partner_user_id, ['fields' => ['partner_id']])->partner_id;

        //check if partner is on manual status
        $partner = TableRegistry::getTableLocator()->get('PartnerEntity')->get($partnerId, [
            'fields' => [
                'partner_id', 'status_system'
            ],
            'contain' => [
                'PartnerFeatureFlagEntity' => ['fields' => ['partner_id', 'access_to_kanban_v2']],
            ]
        ]);

        if(($partner->status_system === "manual") && !empty($partner->feature_flag->access_to_kanban_v2)){
            if($entity->isNew()){
                KanbanHelper::createPositionData($entity->partner_user_id);
            }
            else{
                $dirtyFields = $entity->getDirty();
                if(count(array_intersect(['account_admin', 'access_all_leads'], $dirtyFields)) > 0){
                    KanbanHelper::createPositionData($entity->partner_user_id);
                    SocketHelper::invalidateBoards($partnerId, [$entity->partner_user_id]);
                }
            }
        } 
    }
}
