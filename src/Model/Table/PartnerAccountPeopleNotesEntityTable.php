<?php
namespace App\Model\Table;

use App\Model\Entity\PartnerAccountPeopleNotes;
use Cake\ORM\Table;

class PartnerAccountPeopleNotesEntityTable extends Table
{
  protected $_table = 'partner_account_people_notes';

  protected $_primaryKey = 'note_id';

  protected $_entityClass = PartnerAccountPeopleNotes::class;

  public function initialize(array $config)
  {
    $this->addBehavior('CustomTimestamp');

    $this->belongsTo('PartnerAccountPeopleEntity')
    ->setForeignKey('partner_account_people_id')
    ->setProperty('partner_account_people');

    $this->belongsTo('PartnerUserEntity')
      ->setForeignKey('partner_user_id')
      ->setProperty('partner_user');
  }

}
