<?php
namespace App\Model\Table;

use Cake\Log\Log;
use Cake\ORM\Table;
use App\Model\Table\AppTable;
use App\Lend\LendInternalAuth;

class AdminJobsTable extends AppTable {

		function __construct($arg) {
			parent::__construct($arg);
		}


		public function addAdminJob($params){
			if(empty($params)) return false;

			$q = $this->getInsertQuery('admin_jobs', $params);
      $query = $this->TeamLendDB->execute($q['query'], $q['values']);
      return $query->lastInsertId();
		}

		public function getAdminJob($params){
			if(empty($params)) return false;

			$q = $this->getSelectQuery('admin_jobs', $params);
			return $this->TeamLendDB->execute($q['query'], $q['values'])->fetch('assoc');
		}

		public function getAdminJobs($params){
			if(empty($params)) return false;

			$lend_db = $this->DB->config()['database'];

			$query = "SELECT j.*, l.partner_id, l.organisation_name, l.business_name, lo.first_name, lo.last_name, p.company_name as partner_company_name
								FROM admin_jobs as j
								JOIN `".$lend_db."`.leads as l ON j.lead_id = l.lead_id
								JOIN `".$lend_db."`.lead_owners as lo ON l.lead_id = lo.lead_id AND lo.point_of_contact=1
								LEFT JOIN `".$lend_db."`.partners as p ON l.partner_id = p.partner_id
								WHERE ".implode('=? AND ', array_keys($params))."=?";

			return $this->TeamLendDB->execute($query, array_values($params))->fetchAll('assoc');
		}

		public function groupByJobType($jobs){
			if(empty($jobs)) return false;

			$lend_internal_auth = new LendInternalAuth;

			$grouped = array();
			foreach($jobs as $job){
				if(empty($grouped[$job['job_type']]))
					$grouped[$job['job_type']] = array();

				$job['hashed_lead_id'] = $lend_internal_auth->hashLeadId($job['lead_id']);
				$grouped[$job['job_type']][] = $job;
			}
			return $grouped;
		}

		public function updateAdminJob($params=false, $job_id=false){
			if(empty($params) OR empty($job_id)) return false;

			$q = "UPDATE admin_jobs SET ".implode('=?, ', array_keys($params))."=? WHERE job_id=?";
			$values = array_values($params);
			$values[] = $job_id;

			return $this->TeamLendDB->execute($q, $values);
		}

		public function markAsComplete($lead_id, $job_type, $partner_user_id){
			// #1. Check there is matched data in AdminJobs table:
			$job = $this->getAdminJob(['lead_id'=>$lead_id, 'job_type'=>$job_type, 'job_status'=>'pending']);
			// #2. Update status as complete if there is a matched data:
			if($job){
				$data = array('partner_user_id'=>$partner_user_id, 'job_status'=>'complete', 'updated'=>date('Y-m-d H:i:s'));
				$this->updateAdminJob($data, $job['job_id']);
			}
		}

    public function getDocuSignAdminJobs($params){
        if(empty($params)) return false;

        $q = $this->getSelectQuery('admin_jobs', $params);
        return $this->TeamLendDB->execute($q['query'], $q['values'])->fetchAll('assoc');
    }


}
