<?php

namespace App\Model\Table;

use Cake\ORM\Table;

class CommPrivacyLenderListEntityTable extends Table
{
  protected $_table = 'comm_privacy_lender_list';

  public function getCustomBusinessListFieldForDocusign() {
    $lenderListEntities = $this->find('all')->toArray();

    $lenderNames = [];
    $lenderAbnAcn = [];
    $lenderWebsites = [];

    foreach ($lenderListEntities as $index => $entity) {
      $lenderNames[] = $entity['credit_provider'];
      $lenderAbnAcn[] = $entity['abn_or_acn'];
      $lenderWebsites[] = $entity['website'];
    }

    return [
      'names' => $lenderNames,
      'abn_or_acn' => $lenderAbnAcn,
      'websites' => $lenderWebsites,
    ];
  }

  public function getCustomBusinessListFieldForDocusignFormatted() {
    $lenderData = $this->getCustomBusinessListFieldForDocusign();

    $pageSize = 60;

    $headerAbnAcn = "ABN or ACN \n\n\n";
    $headerNames = "CREDIT PROVIDER \n\n\n";
    $headerWebsites = "WEBSITE \n\n\n";

    $namesKey = 'Lender Names';
    $abnAcnKey = 'Lender ABN ACN';
    $websitesKey = 'Lender Website';

    $numberOfPages = ceil(count($lenderData['names']) / $pageSize);

    $formattedData = [];
    for ($i = 0; $i < $numberOfPages; $i++) {
      $lenderNames = $headerNames;
      $lenderAbnAcn = $headerAbnAcn;
      $lenderWebsites = $headerWebsites;

      $start = $i * $pageSize;
      $end = $start + $pageSize;

      foreach (array_slice($lenderData['names'], $start, $end) as $name) {
        $lenderNames .= $name . "\n";
      }

      foreach (array_slice($lenderData['abn_or_acn'], $start, $end) as $abn_or_acn) {
        $lenderAbnAcn .= $abn_or_acn . "\n";
      }

      foreach (array_slice($lenderData['websites'], $start, $end) as $website) {
        $lenderWebsites .= $website . "\n";
      }

      $usedNamesKey = $namesKey;
      $usedAbnAcnKey = $abnAcnKey;
      $usedWebsitesKey = $websitesKey;
      if ($i > 0) {
        $usedNamesKey = $namesKey . ' ' . ($i + 1);
        $usedAbnAcnKey = $abnAcnKey . ' ' . ($i + 1);
        $usedWebsitesKey = $websitesKey . ' ' . ($i + 1);
      }
      $formattedData[$usedNamesKey] = $lenderNames;
      $formattedData[$usedAbnAcnKey] = $lenderAbnAcn;
      $formattedData[$usedWebsitesKey] = $lenderWebsites;
    }
    return $formattedData;

  }
}
