<?php
namespace App\Model\Table;

use Cake\ORM\Table;
use Cake\Datasource\ConnectionManager;
use Symfony\Component\Process\Process;
use Cake\Core\Configure;
use Cake\ORM\TableRegistry;
use App\Lend\LendInternalAuth;
use Cake\Utility\Text;
use \Aws\S3\S3Client;
use Cake\Log\Log;
use App\Traits\S3Trait;
use Exception;

class AppTable extends Table {

	use S3Trait;

		protected $DB;
		protected $TeamLendDB;
		protected $ReaderDB;

    function __construct($arg) {
			$this->DB = ConnectionManager::get(Configure::read('datasource'));
			if(getenv('MAIN_READER_DB_HOST') !== getenv('MAIN_DB_HOST')){
				$this->ReaderDB = ConnectionManager::get('reader_db');
			}else{
				$this->ReaderDB = $this->DB;
			}
			$this->TeamLendDB = ConnectionManager::get('team_lend_db');
		}

		public function setValidDateFormat($date, $return_date_format='Y-m-d'){
			$check_date_format = array('d/m/Y', 'm/d/Y', 'Y/m/d', 'Y-m-d', 'd-m-Y', 'm-d-Y');

			// Check $date format
			$separator = '';
			if (strpos($date,'-')) $separator = '-';
			else if (strpos($date,'/')) $separator = '/';
			else return false;

			// Fix $date format
			$date_temp = explode($separator,$date);
			foreach ($date_temp as $key => $value) $date_temp[$key] = str_pad($value,2,0,STR_PAD_LEFT);
			$date = implode($separator,$date_temp);

			foreach($check_date_format as $format){
			  if($dateTimeObj = \DateTime::createFromFormat($format, $date) AND $dateTimeObj->format($format)===$date){
			    return $dateTimeObj->format($return_date_format);
			  }
			}

			// If none of the format matched
			if(empty($dateTimeObj)){
			  return false;
			}
		}

		/* =========================================================================
		* Generate INSERT Query
		* How to use :
		* $this->getInsertQuery(TABLE_NAME, PARAMETERS);
		* TABLE_NAME: (string) database table name
		* PARAMETERS: (array) fields and values
		* 						e.g - array('field1'=>'value1', 'field2'=>'value2', ...)
		========================================================================= */
		public function getInsertQuery($table, $params){
			$fields=array(); $values=array(); $targets=array();
			foreach($params as $field=>$value){
			  // Treat $value zero as not empty
				if (!is_numeric($value) && empty($value)) continue; // No point adding it to the insert query I guess, as it will use DB's default
				$targets[] = '?';
				$fields[] = "`$field`";
				$values[] = $value;
			}
			$query = "INSERT INTO ".$table." (".(implode(',', $fields)).") VALUES (".(implode(',', $targets)).")";
			return array('query'=>$query, 'values'=>$values);
		}

		/* =========================================================================
		* Generate SELECT Query
		* How to use :
		* $this->getSelectQuery(TABLE_NAME, CONDITIONS, SORT_TYPE);
		* TABLE_NAME: (string) database table name
		* CONDITIONS: (array) fields and values to use in WHERE
		* 						e.g - array('field1'=>'value1', 'field2 >'=>'value2', 'field3 IN'=>[1,2,3] ...)
		*									-> WHERE field1='value1' AND field2 > 'value2' AND field3 IN (1,2,3)
		* SORT_TYPE: (array) fields and values to use in ORDER BY
		*							e.g - array('field1'=>'DESC', 'field2'=>'ASC')
		*									-> ORDER BY field1 DESC, field2 ASC
		* LIMIT:		 (array | string) when it's array, ['limit'=>10, 'offset'=>15]
		*															when it's string, '10' or '15, 10'
		========================================================================= */
		public function getSelectQuery($table, $params=false, $order=false, $limit=false,$columnNames=false){
			$fields=array(); $values=array();
			if($params){
				foreach($params as $field=>$value){
					$arr_field = explode(' ', trim($field));
	        if(count($arr_field)>1){
						if(is_array($value)){
							$fields[] = $field.' ('.implode(',', array_map(function(){
								return '?';
							}, $value)).' )';
						}else{
							$fields[] = $field.' ?';
						}
					}
	        else $fields[] = $field.'=?';

					if(is_array($value)){
						$values = array_merge($values, $value);
					}else{
						$values[] = $value;
					}
				}
				$query = "SELECT ".(!$columnNames?'*':$columnNames)." FROM ".$table." WHERE ".implode(' AND ', $fields);
			}else{
				$query = "SELECT ".(!$columnNames?'*':$columnNames)." FROM ".$table;
			}

			if($order){
				$order_by=array();
				foreach($order as $field=>$asc_desc){
					$order_by[] = $field.' '.$asc_desc;
				}
				$query .= ' ORDER BY '.(implode(', ', $order_by));
			}

			if($limit){
				if(is_array($limit)){
					$query .= ' LIMIT '.$limit['limit'].' OFFSET '.$limit['offset'];
				}else{
					$query .= ' LIMIT '.$limit;
				}
			}

			return array('query'=>$query, 'values'=>$values);
		}

		/* =========================================================================
		* Generate UPDATE Query
		* How to use :
		* $this->getUpdateQuery(TABLE_NAME, PARAMETERS, CONDITIONS);
		* TABLE_NAME: (string) database table name
		* PARAMETERS: (array) fields and values to update
		* 						e.g - array('field1'=>'value1', 'field2'=>'value2', ...)
		*									-> UPDATE [table] SET field1='value1', field2='value1', ...
		* CONDITIONS: (array) fields and values to use in WHERE
		*							e.g - array('field1'=>'value1', 'field2'=>'value2', ...)
		*									-> WHERE field1='value1' AND field2='value2' AND ...
		========================================================================= */
		public function getUpdateQuery($table, $params, $conditions){
			$fields=array(); $values=array();
			foreach($params as $field=>$value){
				$fields[] = $field.'=?';
				$values[] = ($value || $value == '0') ? $value : NULL;
			}
			$where=array();
			foreach($conditions as $field=>$value){
				$where[]  = "`$field`=?";
				$values[] = $value;
			}
			$query = "UPDATE ".$table." SET ".(implode(',', $fields))." WHERE ".(implode(' AND ', $where));
			return array('query'=>$query, 'values'=>$values);
		}

		/* =========================================================================
		* Generate DELETE Query
		* How to use :
		* $this->getDeleteQuery(TABLE_NAME, PARAMETERS);
		* TABLE_NAME: (string) database table name
		* PARAMETERS: (array) fields and values
		* 						e.g - array('field1'=>'value1', 'field2'=>'value2', ...)
		*									-> WHERE field1='value1' AND field2='value2' AND ...
		========================================================================= */
		public function getDeleteQuery($table, $params){
			$fields=array(); $values=array();
			foreach($params as $field=>$value){
				$fields[] = $field.'=?';
				$values[] = $value;
			}

			$query = "DELETE FROM ".$table." WHERE ".(implode(' AND ', $fields));
			return array('query'=>$query, 'values'=>$values);
		}

		// get date diff
		public function dateDiff($start_date, $point_date=false, $format='Y-m-d'){
	    $start_date = new \DateTime(Date($format, strtotime($start_date)));
	    $point_date = new \DateTime(Date($format, strtotime($point_date ? $point_date : date('Y-m-d'))));
	    return $point_date->diff($start_date);
	  }

		/*
		* Provide an operator, fieldname, and value(s) and returns the result
		*/
		public function operatorHandler($data){
			$pass=true; // Assume pass is true until proven otherwise
			/* ======================================================================
			* NOTE:: NEW option for [LAST LOAN DETECTED] ::
			* bank_statements_analysis > last_loan_detected
			* If there is no last loan detected found, it will pass.
			* Mostly setup for this will be like "more than 90 days" which means the lead has loan but it was more than 90 days ago so it will be pass.
			* However, many of the leads haven't had any loans before. In this case, value of this field will be "-1", so we just make it pass. Otherwise, it won't pass because -1 is less than 90.
			====================================================================== */
			if($data['fieldname']==='last_loan_detected' AND $data['fieldvalue']==-1) return true;

			// Check which operator we are dealing with so we can use the correct if statement:
			switch ($data['operator']) {
				case 'xmto':
					if ($data['value_1'] == 1) { //we need to check MTO
					  /*
						**Check:**
						* If [Loan Amount] (after any tolerance) is greater than x_mto of [BSA: Avg. MTO (last 90d)],
						**or if no BS** [Monthly Rev] it would fail, else it would pass.
						*/
					  $amountRequested = $data['fieldvalue'];
					  $xMTOAndTolerance = explode(',', $data['value_2']); // max lend and tolerance
					  $xMTO = $xMTOAndTolerance[0];
					  $tolerance = $xMTOAndTolerance[1];
					  $turnover = ((empty($data['avg_mto_90']) OR $data['avg_mto_90']=='0.00') ? $data['sales_monthly'] : $data['avg_mto_90']);
					  $loanable = ($turnover * ($xMTO/100)) * ((100+$tolerance )/100);

					if (($amountRequested > $loanable)) $pass = false;



					}

					  break;
				case '>':
					// Using <=, instead of >, because we're looking for the false case;
					if ( floatval($data['fieldvalue']) <= floatval($data['value_1']) ) $pass = false;
					break;

				case '>=':
					// Using less than, instead of >=, because we're looking for the false case;
					if ( floatval($data['fieldvalue']) < floatval($data['value_1']) ) $pass = false;
					break;

				case '<':
					// Using >=, instead of <, because we're looking for the false case;
					if ( floatval($data['fieldvalue']) >= floatval($data['value_1']) ) $pass = false;
					break;

				case '<=':
					// Using >, instead of <=, because we're looking for the false case;
					if ( floatval($data['fieldvalue']) > floatval($data['value_1']) ) $pass = false;
					break;

				case '===':
					// Using !==, instead of ===, because we're looking for the false case;
					if ( is_numeric($data['fieldvalue']) && floatval($data['fieldvalue']) !== floatval($data['value_1']) ) $pass = false;
					elseif ( !is_numeric($data['fieldvalue']) && (string)$data['fieldvalue'] !== (string)$data['value_1'] ) $pass = false;
					break;

				case '!==':
					// Using ===, instead of !==, because we're looking for the false case;
					if ( is_numeric($data['fieldvalue']) && floatval($data['fieldvalue']) === floatval($data['value_1']) ) $pass = false;
					elseif ( !is_numeric($data['fieldvalue']) && (string)$data['fieldvalue'] === (string)$data['value_1'] ) $pass = false;
					break;

				case 'between':
					// Using < >, instead of > <, to look for the false case:
					if ( floatval($data['fieldvalue']) < floatval($data['value_1']) OR floatval($data['fieldvalue']) > floatval($data['value_2']) ) {
						$pass = false;
					}
					break;

				case 'in_array':
					$array = explode(',', strtolower($data['value_1']));
					// Using !in_array instead of in_array, because we're looking for the false case;
					if ( !in_array(strtolower($data['fieldvalue']), $array) ) $pass = false;
					break;

				case '!in_array':
					$array = explode(',', strtolower($data['value_1']));
					// Using in_array instead of !in_array, because we're looking for the false case;
					if ( in_array(strtolower($data['fieldvalue']), $array) ) $pass = false;
					break;

				case 'exists':
					if(!isset($data['fieldvalue']) OR empty($data['fieldvalue'])) $pass = false;
					break;

				case '!exists':
					if(isset($data['fieldvalue']) AND !empty($data['fieldvalue'])) $pass = false;
					break;

				}


				// If criteria check doesn't pass but exception rule is setup, check with exception rule.
				if(!$pass AND !empty($data['exception']['operator'])){
					$pass = $this->operatorHandler($data['exception']);
				}
				return $pass;
		}

		//generate random string containing digits and letters only
		public function randomString( $length = 8)
		{
			$chars = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
			$charsLength = strlen($chars);
			$randomString = ''; //to return
			for ($i = 0; $i < $length; $i++) {
					$randomString .= $chars[rand(0, $charsLength - 1)];
			}
			return $randomString;
	}

	/*
	* Generic CURL request to post to our slack
	*/
	public function postToSlack($msg, $channel=false, $buttonData=null){
		// What webhook to post to?
		switch ($channel) {
			case 'lend':              	$url = '*******************************************************************************'; break;
			case 'lend-leads':        	$url = '*******************************************************************************'; break;
			case 'lend_errors':       	$url = '*******************************************************************************'; break;
			case 'equifax_errors':    	$url = '*****************************************************************************'; break;
			case 'lend_deals_funded': 	$url = '*******************************************************************************'; break;
			case 'lend_lead_updates': 	$url = '*******************************************************************************'; break;
			case 'broker-leads-lend':   $url = '*****************************************************************************'; break;
			case 'broker-leads':        $url = '*****************************************************************************'; break;
			case 'broker-deals-funded': $url = '*****************************************************************************'; break;
			case 'broker-support':      $url = '*****************************************************************************'; break;
			case 'partner_verification':$url = '*****************************************************************************'; break;
			case 'jonathan':          	$url = '*****************************************************************************'; break;
			case 'jangho':            	$url = '*****************************************************************************'; break;
			case 'customer-service':		$url = '*******************************************************************************'; break;
			case 'bs_held_for_review':  $url = '*******************************************************************************'; break;
			case 'partner_verification_nz': $url = '*******************************************************************************'; break;
			case 'send_errors':          $url = '*******************************************************************************'; break;
			case 'broker-funded-lend':   $url = '*****************************************************************************'; break;
			case 'broker-funded':        $url = '*****************************************************************************'; break;
			case 'off_panel_lenders': $url = '*******************************************************************************'; break;
			default: return false;
		}

		// Override Post Channel to 'lend_dev_dumps', if it is a local test.
		$env = (string)getenv('LEND_ENV');
		if(in_array($env, ['0', '1'])){
			$url='*******************************************************************************';
			$route = ($env=='0' ? 'DEV' : 'STAGE');
			$msg = "[{$route}][{$channel}] {$msg}";
		}

		try {

			$payload = array('text'=> $msg,);

			if (!empty($buttonData)) {
				$payload['attachments'][0] = array('fallback'=>'Visit Star for details: https://star.lend.com.au', 'actions'=>array(), 'color'=>'#3AA3E3',);
				foreach ($buttonData as $buttonText => $button)
					$payload['attachments'][0]['actions'][] = array(
							"type"   => "button",
							"text"   => $buttonText,
							"url"    => $button['url'],
							"style"  => (empty($button['style'])) ? '' : $button['style']
						);
			}

			$ch = curl_init();
			curl_setopt($ch, CURLOPT_URL, $url);
			curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
			curl_setopt($ch, CURLOPT_USERAGENT, 'PHP-MCAPI/2.0');
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($ch, CURLOPT_TIMEOUT, 5);
			curl_setopt($ch, CURLOPT_POST, true);
			curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
			curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
			curl_exec($ch);
			curl_close($ch);
		} catch (\Exception $e) {}
		return true;
	}

	/*
	@param: $filters - array of field name as key, search term as value
	@param: $data - simple array of array(field name => field value, ...)
	return: same structure of $data with highlight words(html tag)
	*/
	public function highlight($filters, $data)
	{
		if (empty($filters)) return $data;
		if (empty($data)) return array();

		$format = '<i class="bg-green">\1</i>';
		foreach ($data as $k => $row) {
			foreach ($filters as $field => $value) {
				if (strpos($field, '.') !== false) {
					//remove table alias name
					$field = array_pop(explode('.', $field)); // last part as field name
				}

				if (!isset($row[$field])) continue;
				$keywords = explode(' ', $value);

				foreach ($keywords as $keyword) {
					$data[$k][$field] = Text::highlight(
						$data[$k][$field],
						$keyword,
						['format' => $format,
						 'html' => true ]
						);
				}
			}
		}
		return $data;
	}

	/*
	@param: $tableName - name of table whos id field we want a hash from
	return: hash of the next insert id
	*/
	public function getNextIdHash($tableName) {
		//accurate way of getting autoincrement - even if you trucate the table
		$this->DB->execute("SET information_schema_stats_expiry = 0;");
		$sql = "SELECT AUTO_INCREMENT as id FROM
				information_schema.tables
				WHERE table_name = '{$tableName}';";

		$id = $this->DB->execute($sql)->fetch('assoc')['id'];

		$hash = (new \App\Lend\LendInternalAuth)->hashLeadId($id);
		return $hash;
	  }

	public function createRecord($record){
        $q = TableRegistry::get('App')->getInsertQuery($this->getTable(), $record);
        $query = $this->DB->execute($q['query'], $q['values']);
        $id = $query->lastInsertId();
        return $id;
    }
    public function updateRecordById($id,$params){
        $q=TableRegistry::get('App')->getUpdateQuery($this->getTable(), $params, array($this->getPrimaryKey()=>$id));
        return $this->DB->execute($q['query'], $q['values']);
    }

    public function readRecord( $params=false, $order=false, $limit=false,$assoc=true,$columnNames=false){
        $q = $this->getSelectQuery($this->getTable(), $params, $order,$limit,$columnNames);
        if($assoc) {
            return $this->DB->execute($q['query'], $q['values'])->fetchAll('assoc');
        }else{
            return $this->DB->execute($q['query'], $q['values'])->fetchAll('num');
        }
    }

    public function deleteRecord($params){
        $q=$this->getDeleteQuery($this->getTable(),$params);
        return $this->DB->execute($q['query'], $q['values']);
    }

		public function convertOneDigitIntoWord($digit)
		{
			switch ($digit)
	    {
	        case "0":
	            return "zero";
	        case "1":
	            return "one";
	        case "2":
	            return "two";
	        case "3":
	            return "three";
	        case "4":
	            return "four";
	        case "5":
	            return "five";
	        case "6":
	            return "six";
	        case "7":
	            return "seven";
	        case "8":
	            return "eight";
	        case "9":
	            return "nine";
	    }
		}

		public function leaveSpecificFields($arr, $fields){
			$result = [];
			foreach($arr as $item){
				$add = [];
				foreach($item as $field=>$value){
					if(in_array($field, $fields)){
						$add[$field] = $value;
					}
				}
				$result[] = $add;
			}
			return $result;
		}

		public function getORMQueryRawSql($query){
			$sql = $query->sql();
			$params = $query->getValueBinder()->bindings();
			$reversedParams  = array_reverse($params);
			foreach($reversedParams as $key=>$value){
				$sql = str_replace($key, "'".$value['value']."'", $sql);
			}
			return $sql;
		}

	/**
	 * Send a message to socket server (to broadcast to sockets)
	 */
	public function sendSocketMessage($userIds, $topic, $message){
		try{
			if(empty(getenv('SEND_SOCKET_REQUESTS')) || (!in_array(getenv('SEND_SOCKET_REQUESTS'), ['1', 'true']))){
                return;
            }
			$payload = [
				'userIds' => $userIds,
				'topic' => $topic,
				'message' => $message
			];
			$ch = curl_init();
			curl_setopt_array($ch, [
				CURLOPT_URL => getenv('DOMAIN_SOCKET') . '/broadcast',
				CURLOPT_RETURNTRANSFER => true,
				CURLOPT_ENCODING => "",
				CURLOPT_MAXREDIRS => 10,
				CURLOPT_TIMEOUT => 60,
				CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
				CURLOPT_CUSTOMREQUEST => "POST",
				CURLOPT_POSTFIELDS => json_encode($payload),
				CURLOPT_HTTPHEADER => [
					"Content-Type: application/json",
					"x-api-key:".getenv('SOCKET_API_KEY'),
				],
			]);
			//execute post
			$result = curl_exec($ch);
			$curlReturnValue = curl_errno($ch);
			$httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
			$err = curl_error($ch);
			curl_close($ch);

			if ('0' !== "$curlReturnValue") {
				throw new \Exception("Failed to communicate with server: " . $err);
			} else {
				if($httpcode != '200'){
					if($result){
						$decodedResult = json_decode($result, true);
						if($decodedResult['error']){
							throw new \Exception($decodedResult['error']);
						}
						else{
							throw new \Exception('Http Error Code: ' . $httpcode);
						}
					}
				}
			}
		}
		catch (Exception $ex){
			$this->postToSlack(":warning: :no_bell: Unable to send message to socket server :no_bell: :warning: \n"
				. "Exception: `".$ex->getMessage()."` \n"
                . "Payload: ```".json_encode($payload)."``` \n",
                'lend_errors'
			);
		}
	}
}
