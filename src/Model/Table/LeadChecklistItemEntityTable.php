<?php

declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

class LeadChecklistItemEntityTable extends Table
{
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('lead_checklist_items');
        $this->setDisplayField('lead_checklist_item_id');
        $this->setPrimaryKey('lead_checklist_item_id');

        $this->addBehavior('Timestamp', [
            'events' => [
                'Model.beforeSave' => [
                    'updated' => true,
                ],
            ],
        ]);

        $this->belongsTo('LeadChecklistEntity')
            ->setForeignKey('lead_checklist_id')
            ->setProperty('lead_checklist');

        $this->belongsTo('PartnerChecklistItemEntity')
            ->setForeignKey('partner_checklist_item_id')
            ->setProperty('partner_checklist_item');
    }
}
