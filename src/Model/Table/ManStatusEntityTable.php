<?php
namespace App\Model\Table;

use App\Lend\KanbanHelper;
use App\Lend\SocketHelper;
use Cake\ORM\Query;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;
use \App\Model\Entity\ManStatus;
use Cake\Datasource\EntityInterface;
use Cake\ORM\TableRegistry;



/**
 * ManStatusEntity Model
 *
 * @property \App\Model\Table\PartnerEntityTable|\Cake\ORM\Association\BelongsTo $PartnerEntity
 * @property \App\Model\Table\ManStatusGroupEntityTable|\Cake\ORM\Association\BelongsTo $ManStatusGroupEntity
 * @property \App\Model\Table\LeadEntityTable|\Cake\ORM\Association\HasMany $LeadEntity
 * @property \App\Model\Table\ManStatusHistoryEntityTable|\Cake\ORM\Association\HasMany $ManStatusHistoryEntity
 *
 * @method \App\Model\Entity\ManStatus get($primaryKey, $options = [])
 * @method \App\Model\Entity\ManStatus newEntity($data = null, array $options = [])
 * @method \App\Model\Entity\ManStatus[] newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\ManStatus|bool save(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\ManStatus|bool saveOrFail(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\ManStatus patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method \App\Model\Entity\ManStatus[] patchEntities($entities, array $data, array $options = [])
 * @method \App\Model\Entity\ManStatus findOrCreate($search, callable $callback = null, $options = [])
 *
 * @mixin \Cake\ORM\Behavior\TimestampBehavior
 */
class ManStatusEntityTable extends Table
{


    protected $_table = 'man_statuses';

    protected $_primaryKey = 'id';

    protected $_entityClass = ManStatus::class;

    /**
     * Initialize method
     *
     * @param array $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config)
    {
        parent::initialize($config);

        $this->setTable('man_statuses');
        $this->setDisplayField('id');
        $this->setPrimaryKey('id');

        $this->addBehavior('CustomTimestamp');

        $this->belongsTo('PartnerEntity', [
            'foreignKey' => 'partner_id',
            'joinType' => 'INNER'
        ])->setProperty('partner');

        $this->belongsTo('ManStatusGroupEntity', [
            'foreignKey' => 'man_status_group_id',
            'joinType' => 'LEFT'
        ])->setProperty('man_status_group');

        $this->hasMany('LeadEntity', [
            'foreignKey' => 'man_status_id'
        ])->setProperty('lead');
        
        $this->hasMany('ManStatusHistoryEntity', [
            'foreignKey' => 'man_status_id'
        ])->setProperty('man_status_history');
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator)
    {
        $validator
            ->nonNegativeInteger('id')
            ->allowEmptyString('id', 'create');

        $validator
            ->scalar('status_name')
            ->maxLength('status_name', 255)
            ->allowEmptyString('status_name');

        $validator
            ->nonNegativeInteger('order')
            ->requirePresence('order', 'create')
            ->allowEmptyString('order', false);

        $validator
            ->boolean('active')
            ->requirePresence('active', 'create')
            ->allowEmptyString('active', false);

        $validator
            ->boolean('is_new_lead')
            ->allowEmptyString('is_new_lead');

        $validator
            ->boolean('is_settled')
            ->allowEmptyString('is_settled');

        $validator
            ->boolean('is_fake_lead')
            ->allowEmptyString('is_fake_lead');

        $validator
            ->boolean('is_call_q_not_proceeding')
            ->allowEmptyString('is_call_q_not_proceeding');

        $validator
            ->boolean('is_submitted')
            ->allowEmptyString('is_submitted');

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules)
    {
        $rules->add($rules->existsIn(['partner_id'], 'PartnerEntity'));
        $rules->add($rules->existsIn(['man_status_group_id'], 'ManStatusGroupEntity'));

        return $rules;
    }

    public function afterSave($event, EntityInterface $entity, $options)
    {
        $partnerId = $entity->partner_id ??$this->get($entity->id, ['fields' => ['partner_id']])->partner_id;

        if($partnerId && KanbanHelper::partnerHasKanbanV2Access($partnerId)){
            if (!$entity->isNew()){
                if($entity->isDirty('man_status_group_id')){
                    KanbanHelper::manualStatusChangeGroup($entity->id);
                }
                $fields = [
                    'status_name',
                    'man_status_group_id',
                    'active',
                ];//invalidate_kanban
                $dirty = $entity->getDirty();
                $dirtyFields = array_intersect($fields, $dirty);
                if(count($dirtyFields) > 0){
                    SocketHelper::invalidateBoards($partnerId);
                }
            }
        }
    }
}
