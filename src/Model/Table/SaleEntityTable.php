<?php

namespace App\Model\Table;

use App\Model\Entity\Sale;
use Cake\ORM\Query;
use Cake\ORM\Table;
use App\Lend\SocketHelper;
use App\Lend\KanbanHelper;
use Cake\ORM\TableRegistry;

class SaleEntityTable extends Table
{
    protected $_table = 'sales';

    protected $_primaryKey = 'sale_id';

    protected $_entityClass = Sale::class;

    public function initialize(array $config)
    {
        parent::initialize($config);
        $this->addBehavior('CustomTimestamp');
        $this->addBehavior('GenerateSaleRef');

        
        $this->belongsTo('LeadEntity')
            ->setForeignKey('lead_id')
            ->setProperty('lead');

        $this->belongsTo('LenderProductEntity')
            ->setForeignKey('product_id')
            ->setProperty('product');

        $this->belongsTo('PartnerProductTypeEntity')
            ->setForeignKey('product_type_id')
            ->setProperty('product_type');

        $this->hasOne('SaleReferences', [
            'className' => 'SaleRefs',
            'foreignKey' => 'sale_id',
        ]);

        $this->hasMany('SaleOutcomesEntity')
            ->setForeignKey('sale_id')
            ->setProperty('sale_outcomes');

        $this->hasMany('DealsEntity')
            ->setForeignKey('sale_id')
            ->setProperty('deals');

        $this->hasOne('SaleDetailEntity')
            ->setForeignKey('sale_id')
            ->setProperty('sale_detail');
    }

    public function afterSave($event, $entity, $options) {
        $lenderId = null;
        $lenderName = null;
        $oldLenderId = null;
        $oldLenderName = null;

        $lead = TableRegistry::getTableLocator()->get('LeadEntity')->get($entity->lead_id, [
            'fields' => [
                'lead_id',
                'partner_id',
            ]
        ]);

        if(KanbanHelper::partnerHasKanbanV2Access($lead->partner_id)){
            if ($entity->product) {
                $lenderId = $entity->product->lender_id;
                $lenderName = $entity->off_panel_lender ? json_decode($entity->off_panel_lender, true)['lender_name'] ?? null : null;
                if(!$lenderName){
                    $lenderName = $entity->product->lender->lender_name;
                }
            }

            //get sale entity this for the most recent sale for this lead(older than the current one)
            $previousSale = $this->find('all', [
                'contain' => [
                        'LenderProductEntity' => ['fields' => ['lender_product_id', 'lender_id']],
                        'LenderProductEntity.LenderEntity' => ['fields' => ['lender_id', 'lender_name']],
                    ]
                ])
                ->where([
                    'lead_id' => $entity->lead_id,
                    'sale_id <' => $entity->sale_id // Ensure it's an older sale
                ])
                ->order(['sale_id' => 'DESC']) // Get the most recent older sale
                ->first();
            if ($previousSale && $previousSale->product) {
                $oldLenderId = $previousSale->product->lender_id;
                $oldLenderName = $previousSale->off_panel_lender ? json_decode($previousSale->off_panel_lender, true)['lender_name'] ?? null : null;
                if(!$oldLenderName){
                    $oldLenderName = $previousSale->product->lender->lender_name;
                }
            }

            if(($lenderName !== $oldLenderName)){//Socket filters only use lender name text
                SocketHelper::kanbanUpdateLead($entity->lead_id, [
                    'lender' => [
                        'old' => $oldLenderName,
                        'new' => $lenderName
                    ]
                ]);
            }
            else if($lenderId !== $oldLenderId){//Socket cards also use other lender fields
                SocketHelper::kanbanUpdateLead($entity->lead_id);
            }
        }
    }
}
