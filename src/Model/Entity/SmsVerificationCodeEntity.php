<?php
namespace App\Model\Entity;

use Cake\ORM\Entity;

class SmsVerificationCodeEntity extends Entity
{
    protected $_accessible = [
        'verification_code' => true,
        'phone_number' => true,
        'status' => true,
        'attempts' => true,
        'max_attempts' => true,
        'expires_at' => true,
        'used_at' => true,
        'created' => true,
        'modified' => true,
    ];
    protected $_hidden = [
        'verification_code', // Hide from JSON output for security
    ];

    public function isExpired()
    {
        return new \DateTime() > new \DateTime($this->expires_at->format('Y-m-d H:i:s'));
    }
    public function isUsable()
    {
        return $this->status === 'pending' && 
               !$this->isExpired() && 
               $this->attempts < $this->max_attempts;
    }
    public function hasExceededAttempts()
    {
        return $this->attempts >= $this->max_attempts;
    }
} 