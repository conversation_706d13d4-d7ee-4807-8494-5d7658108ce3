## Setting up

Any NEW brokers that sign up, must sign this PDF form before they are verified and accredited.

* Using DocuSign to forward the Accreditation Form to brokers.

## Docusign Set up

* Create a new template in DocuSign for Accreditation Form and attach the respective form.
* Create custom fields for the relevant fields in the document like name, title, membership numbers etc
* Copy the template ref and run docusign migrations, use the previously copied ref in the 'docusign_ref' column in the 'docusign_templates' table for the id = 3

### Note
* Local file upload use serverless-s3-local which points to http://localhost:4569.
* Please make sure the S3 bucket 'accreditation-forms' exist.
* Signed Accreditation form S3 path is saved in partner_users table signed_accreditation_S3 field