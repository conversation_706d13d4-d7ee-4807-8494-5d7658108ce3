$(document).ready( function(){

  $('.input-daterange input').each(function() {
    $(this).datepicker( {
      format: 'dd/mm/yyyy',
      startDate: '01/01/2016',
      endDate: new Date,
    } ).on('changeDate', function(){
      $(this).datepicker('hide');
    }); ;
});

  /* chart function */
  function _generateOptionsConfig(y1, y2) {
    if (typeof y1 === undefined) y1 = false;
    if (typeof y2 === undefined) y2 = false;
    var yAxesArray = [];
    if (y1 && y2) {
      yAxesArray.push({
                      id: 'SettlementsAxis',
                      type: 'linear',
                      position: 'left',
                      gridLines: {
                        display: false,
                      },
                      ticks: {
                        min: 0,
                        beginAtZero: true,
                        callback: function(value, index, values) {
                          if(parseInt(value) >= 1000){
                            return '$' + value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
                          } else if (Math.floor(value) === value) {
                            return '$' + value;
                          }
                        },
                      },
                      stacked: true,
                    });
      yAxesArray.push({
                      id: 'CommissionsAxis',
                      type: 'linear',
                      position: 'right',
                      gridLines: {
                        display: false,
                      },
                      ticks: {
                        beginAtZero: true,
                        callback: function(value, index, values) {
                          if(parseInt(value) >= 1000){
                            return '$' + value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
                          } else if (Math.floor(value) === value) {
                            return '$' + value;
                          }
                        },
                      },
                      stacked: true,
                    });
    } else if (y1) {
      yAxesArray.push({
                      id: 'SettlementsAxis',
                      type: 'linear',
                      position: 'left',
                      gridLines: {
                        display: false,
                      },
                      ticks: {
                        min: 0,
                        beginAtZero: true,
                        callback: function(value, index, values) {
                          // if (Math.floor(value) === value) {
                          //     return value;
                          // }
                          if(parseInt(value) >= 1000){
                            return '$' + value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
                          } else if (Math.floor(value) === value) {
                            return '$' + value;
                          }
                        },
                      },
                      stacked: true,
                    });
    } else if (y2) {
      yAxesArray.push({
                      id: 'CommissionsAxis',
                      type: 'linear',
                      position: 'left',
                      gridLines: {
                        display: false,
                      },
                      ticks: {
                        beginAtZero: true,
                        callback: function(value, index, values) {
                          if(parseInt(value) >= 1000){
                            return '$' + value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
                          } else if (Math.floor(value) === value) {
                            return '$' + value;
                          }
                        },
                      },
                      stacked: true,
                    });
    }
    return  {
      responsive: true,
      maintainAspectRatio: false,
      tooltips: {
        callbacks: {
          label: function(tooltipItem, data) {
            var label = data.datasets[tooltipItem.datasetIndex].label || '';
            if (label)
            {
              if(label == 'Refinance Commission'
                || label == 'New Commission'
                || label == 'All Commission'
                || label == 'Refinance Capital'
                || label == 'New Capital'
                || label == 'All Capital') {
                var value = data.datasets[tooltipItem.datasetIndex].data[tooltipItem.index];

                label += ': $' + value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
              }else if (label) {
                label += ': ' +data.datasets[tooltipItem.datasetIndex].data[tooltipItem.index];
              }
            }
            return label;
          }
        }
      },
      // title : {
      //   display: true,
      //   text: 'Settled Leads',
      // },
      legend: {
        labels: {
          usePointStyle: true,
        }
      },
      scales: {
        xAxes: [{
            gridLines: {
                display: false,
            },
            stacked: true,
        }],
        yAxes: yAxesArray,
      }
    };
  }

  function _generateStackedOptionsConfig() {
    return  {
      responsive: true,
      maintainAspectRatio: false,
      tooltips: {
        callbacks: {
          label: function(tooltipItem, data) {
            var label = data.datasets[tooltipItem.datasetIndex].label || '';
            if (label)
            {
              if(label == 'Refinance Commission'
                || label == 'New Commission'
                || label == 'All Commission'
                || label == 'Refinance Capital'
                || label == 'New Capital'
                || label == 'All Capital') {
                var value = data.datasets[tooltipItem.datasetIndex].data[tooltipItem.index];

                label += ': $' + value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
              }else if (label) {
                label += ': ' +data.datasets[tooltipItem.datasetIndex].data[tooltipItem.index];
              }
            }
            return label;
          }
        }
      },
      // title : {
      //   display: true,
      //   text: 'Settled Leads',
      // },
      legend: {
        labels: {
          usePointStyle: true,
        }
      },
      scales: {
        xAxes: [{
            gridLines: {
                display: false,
            },
            stacked: true,
        }],
        yAxes: [{
          gridLines: {
            display: false,
          },
          ticks: {
            min: 0,
            // beginAtZero: true,
            callback: function(value, index, values) {
              if(parseInt(value) >= 1000){
                return '$' + value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
              } else if (Math.floor(value) === value) {
                return '$' + value;
              }

            },
          },
          stacked: true,
        }]
      }
    };
  }

  function _generateCommissionChartConfig(xLabels, data1Label, data1, data2Label, data2, stacked) {
    if (typeof stacked === 'undefined') stacked = false;
    if (typeof data1Label === 'undefined') data1Label = false;
    if (typeof data1 == 'undefined') data1 = false;
    if (typeof data2Label === 'undefined') data2Label = false;
    if (typeof data2 == 'undefined') data2 = false;
    if (stacked === false) {
      //generate options config, for non-stacked only
      var optionsConfig;
      var dataSetsArray = [];
      if (data1 && data2){
        optionsConfig = _generateOptionsConfig(true, true);
        dataSetsArray.push({
                            label: data1Label,
                            pointStyle: 'circle',
                            yAxisID: 'SettlementsAxis',
                            data: data1,
                            backgroundColor: '#01CF88',
                            borderColor: '#00BA78',
                            borderWidth: 1,
                            type: 'line',
                            fill: false,
                          });
        dataSetsArray.push({
                            label: data2Label,
                            pointStyle: 'rect',
                            yAxisID: 'CommissionsAxis',
                            data: data2,
                            backgroundColor: '#395EFE',
                            borderColor: '#2B4BDA',
                            borderWidth: 1,
                          });
      } else if (data1) {
        optionsConfig = _generateOptionsConfig(true, false);
        dataSetsArray.push({
          label: data1Label,
          pointStyle: 'rect',
          // yAxisID: 'SettlementsAxis',
          data: data1,
          backgroundColor: '#01CF88',
          borderColor: '#00BA78',
          borderWidth: 1,
          type: 'bar',
          fill: false,
        });
      } else if (data2) {
        optionsConfig = _generateOptionsConfig(false, true);
        dataSetsArray.push({
                            label: data2Label,
                            pointStyle: 'rect',
                            // yAxisID: 'CommissionsAxis',
                            data: data2,
                            backgroundColor: '#395EFE',
                            borderColor: '#2B4BDA',
                            borderWidth: 1,
                          });
      }

      return {
        type: 'bar',
        data: {
          labels: xLabels,
          datasets: dataSetsArray,
        },
        options: optionsConfig,
      };

    } else {
      //stacked
      var optionsConfig = _generateStackedOptionsConfig();
      return {
        type: 'bar',
        data: {
          labels: xLabels,
          datasets: [
            {
              label: data1Label,
              pointStyle: 'rect',
              data: data1,
              backgroundColor: '#01CF88',
              borderColor: '#00BA78',
              borderWidth: 1,
              stack: 'stack 0',
              fill: false,
            },
            {
              label: data2Label,
              pointStyle: 'rect',
              data: data2,
              backgroundColor: '#395EFE',
              borderColor: '#2B4BDA',
              borderWidth: 1,
              stack: 'stack 0'
            }
          ],
        },
        options: optionsConfig,
      };
    }
  }

  function prepareSettlementsCommissionsTwelveMonthGraph(data, selectedValue, checkedValue, includeOriginationFeeValue) {
    if (typeof selectedValue === 'undefined')
    selectedValue = 'all';
    var monthNames       = [];

    var monthCapitalNew = [], monthCapitalRef = [], monthCapitalAll = [];
    var monthCommissionNew = [], monthCommissionRef = [], monthCommissionAll = [];

    // Populate
    for(var month in data) {
      //X
      monthNames.push(month);

      //Ys
      monthCommissionNew.push(data[month].commission.New);
      monthCommissionRef.push(data[month].commission.Refinance);
      monthCommissionAll.push(data[month].commission.All);
      monthCapitalNew.push(data[month].amount.New);
      monthCapitalRef.push(data[month].amount.Refinance);
      monthCapitalAll.push(data[month].amount.All)
    }

    // Prepare the configuration
    if (checkedValue) {
      if (selectedValue === 'all') {
        var config = _generateCommissionChartConfig(monthNames, 'New Commission', monthCommissionNew, 'Refinance Commission', monthCommissionRef, true);
      } else if (selectedValue === 'new') {
        var config = _generateCommissionChartConfig(monthNames, 'New Commission', monthCommissionNew);
      } else if (selectedValue === 'ref'){
        var config = _generateCommissionChartConfig(monthNames, false, false, 'Refinance Commission', monthCommissionRef);
      }

    } else {
      if (selectedValue === 'all') {
        var config = _generateCommissionChartConfig(monthNames, 'All Capital', monthCapitalAll, 'All Commission', monthCommissionAll);
      } else if (selectedValue === 'new') {
        var config = _generateCommissionChartConfig(monthNames, 'New Capital', monthCapitalNew, 'New Commission', monthCommissionNew);
      } else if (selectedValue === 'ref'){
        var config = _generateCommissionChartConfig(monthNames, 'Refinance Capital', monthCapitalRef, 'Refinance Commission', monthCommissionRef);
      }
    }

    populateCanvas('settlementsCommissionsLastTwelveMonths', config)
  }

 // generate radios html
  function generateCommissionsRadiosHTML(selectedValue, checkedValue, includeOriginationFeeValue) {

    var htmlString = '<div class="filterContent">';
    if (selectedValue === 'all') {
      htmlString += '<label class="inline middle graphToggler">'
              + '<input type="radio" value="all" name="commissionTwelveRadio" checked>'
              + '<span id="comm_All" class="label-body">All</span>'
              + '</label>';
    } else {
      htmlString += '<label class="inline middle graphToggler">'
              + '<input type="radio" value="all" name="commissionTwelveRadio">'
              + '<span id="comm_All" class="label-body">All</span>'
              + '</label>';
    }
    if (selectedValue === 'new') {
      htmlString += '<label class="inline middle graphToggler">'
                + '<input type="radio" value="new" name="commissionTwelveRadio" checked>'
                + '<span id="comm_New" class="label-body">New</span>'
                + '</label>';

    } else {
      htmlString += '<label class="inline middle graphToggler">'
              + '<input type="radio" value="new" name="commissionTwelveRadio">'
              + '<span id="comm_New" class="label-body">New</span>'
              + '</label>';

    }
    if (selectedValue === 'ref') {
      htmlString += '<label class="inline middle graphToggler">'
              + '<input type="radio" value="ref" name="commissionTwelveRadio" checked>'
              + '<span id="comm_Refinance" class="label-body">Refinance</span>'
              + '</label>';
    } else {
      htmlString += '<label class="inline middle graphToggler">'
              + '<input type="radio" value="ref" name="commissionTwelveRadio">'
              + '<span id="comm_Refinance" class="label-body">Refinance</span>'
              + '</label>';
    }

    //check box
    if (checkedValue === 'on') {
      htmlString += '<label class="p-l-3 p-l-0-xs inline middle graphToggler">'
              + '<input type="checkbox" value="on" name="commissionTwelveCheck" checked>'
              + '<span id="comm_CommissionOnlyCheckbox" class="label-body">Commission Only</span>'
              + '</label>';
    } else {
      htmlString += '<label class="p-l-3 p-l-0-xs inline middle graphToggler">'
              + '<input type="checkbox" value="on" name="commissionTwelveCheck">'
              + '<span id="comm_CommissionOnlyCheckbox" class="label-body">Commission Only</span>'
              + '</label>';
    }

    if (includeOriginationFeeValue === 'on') {
      htmlString += '<label class="p-l-3 p-l-0-xs inline middle graphToggler">'
        + '<input type="checkbox" value="on" name="includeOriginationFeeValue" checked>'
        + '<span id="comm_IncludeOriginationFeeValueCheckbox" class="label-body">Include Origination Fee</span>'
        + '</label>';
    } else {
      htmlString += '<label class="p-l-3 p-l-0-xs inline middle graphToggler">'
        + '<input type="checkbox" value="on" name="includeOriginationFeeValue">'
        + '<span id="comm_IncludeOriginationFeeValueCheckbox" class="label-body">Include Origination Fee</span>'
        + '</label>';
    }
    htmlString += '</div>';
    return htmlString;
  }

  function drawSettlementsCommissionsTwelve(selectedValue, checkedValue, includeOriginationFeeValue) {

    $('.settlementsCommissionsLastTwelveMonths').append('<div class="overlayChartRefresh"><img src="/img/SpinBrokerSpin.gif" class="SpinBrokerSpin" style="margin:80px auto;" /></div>');

    $.ajax({
      type: 'GET', url: `/partner-commissions/get-settlements-and-commissions/12${includeOriginationFeeValue ? '/true' : ''}`, // 12 Months
      success: function(r){
        //clear previous spinner, iframe & canvas (if applicable)
        $('.settlementsCommissionsLastTwelveMonths').html('');

        // if (!r.success) return showGraphFailureMessage('settlementsCommissionsLastTwelveMonths', r.message); // We'll display a notice where the graph SHOULD have been populated

        addEmptyCanvas('settlementsCommissionsLastTwelveMonths', 880, 220);
        if (selectedValue === 'undefined') selectedValue = 'all';
        prepareSettlementsCommissionsTwelveMonthGraph(r.data, selectedValue, checkedValue, includeOriginationFeeValue);
        var htmlString = generateCommissionsRadiosHTML(selectedValue, checkedValue, includeOriginationFeeValue);
        $('.settlementsCommissionsLastTwelveMonths').append(htmlString);
      }
    });
  }
  if ($('.settlementsCommissionsLastTwelveMonths').length) {
    drawSettlementsCommissionsTwelve('all', false, false)
  }

  function redrawSettlementsCommissionsTwelveGraph(e) {
    var selectedValue = $('input[name="commissionTwelveRadio"]:checked').val();
    var checkedValue = $('input[name="commissionTwelveCheck"]:checked').val();
    var includeOriginationFeeValue = $('input[name="includeOriginationFeeValue"]:checked').val();

    if (selectedValue === undefined) selectedValue = 'all';
    if (checkedValue === undefined) checkedValue = false;

    drawSettlementsCommissionsTwelve(selectedValue, checkedValue, includeOriginationFeeValue);
  }

  $('body').delegate( 'input[name="commissionTwelveRadio"]', 'change', redrawSettlementsCommissionsTwelveGraph);
  $('body').delegate( 'input[name="commissionTwelveCheck"]', 'change', redrawSettlementsCommissionsTwelveGraph);
  $('body').delegate( 'input[name="includeOriginationFeeValue"]', 'change', redrawSettlementsCommissionsTwelveGraph);

  window.commissionsDateRangeTriggered = function(e) {
    //check the dates if valid
    try{
      var timeframe = getDateRangeValues();

      //get the current filters where possible
      filterType = $('input[name="search[filters][type]"]:checked').val();
      var filterLender = [];

      $('#filterLenderDiv input:checked').each (function () {
        filterLender.push($(this).val());
      });
      
      var filterPartnerUser = $('select[name="search[filters][partnerUser]"]').val();

      resetDiv('commissionsTableContainer');

      var newurl = window.location.origin+window.location.pathname+'?' + timeframe
                    + '&filterType=' + filterType
                    +  '&filterLenders=' + filterLender.join('-')
                    + '&filterPartnerUser=' + filterPartnerUser;
      history.pushState(null, '', newurl);

      //overwrite url to send a request immediately
      newUrl = '/partner-commissions/by-date-range?' + timeframe
                    + '&filterType=' + filterType
                    +  '&filterLenders=' + filterLender.join('-')
                    + '&filterPartnerUser=' + filterPartnerUser;

      window.ajaxManager.addReq({
        type: 'GET', url: newUrl,
        success: function(r){
          $('#commissionsTableContainer').empty().append(r);
        }
      });

    } catch (err) {
      alert(err);
    }
  }

  function resetDiv(divId, height) {
    height = height || 80;
      $('#' + divId).html('<div><img src="/img/SpinBrokerSpin.gif" class="SpinBrokerSpin" style="display:block;margin:' + height + 'px auto;overfloat:hidden;" /></div>');
  }


  function clearFilterButtonClicked (e) {
    //check the dates if valid
    try{
      var timeframe = getDateRangeValues();
      resetDiv('commissionsTableContainer');

      var newurl = window.location.origin+window.location.pathname+'?' + timeframe
                    + '&filterType=All'
                    +  '&filterLenders='
                    + '&filterPartnerUser=';
      history.pushState(null, '', newurl);

      //overwrite url to send a request immediately
      newUrl = '/partner-commissions/by-date-range?' + timeframe
                    + '&filterType=All'
                    +  '&filterLenders='
                    + '&filterPartnerUser=';

      window.ajaxManager.addReq({
        type: 'GET', url: newUrl,
        success: function(r){
          $('#commissionsTableContainer').empty().append(r)
                  .find('.input-daterange input').each(function() {
                          $(this).datepicker( {
                            format: 'dd/mm/yyyy',
                            startDate: '01/01/2016',
                            endDate: new Date,
                          } ).on('changeDate', function(){
                            $(this).datepicker('hide');
                          }); ;
                      });
        }
      });

    } catch (err) {
      alert(err);
    }
  }

  function sortedByColumn(e) {
    try {
    //get the column
    var ele = $(this);
    var direction = '', sort = '';
    if (ele.hasClass('asc')) {
      ele.removeClass('asc').addClass('desc');
      //sort them in order of desc
      direction = 'desc';
    } else if (ele.hasClass('desc')) {
      ele.removeClass('desc');
      //sort them in default order
      direction = 'asc';
    }
    else {
      ele.addClass('asc');
      //sort them in order of asc
      direction = 'asc';
    }

    if (direction !== '') {
      sort = ele.attr('col-name');
    }

    var timeframe = getDateRangeValues();

    //get the current filters where possible
    filterType = $('input[name="search[filters][type]"]:checked').val();
    var filterLender = [];

    $('#filterLenderDiv input:checked').each (function () {
      filterLender.push($(this).val());
    });

    resetDiv('commissionsTableContainer');

    var newurl = window.location.origin+window.location.pathname+'?' + timeframe
                  + '&filterType=' + filterType
                  +  '&filterLenders=' + filterLender.join('-')
                  + '&sort=' + sort + '&direction=' + direction;
    history.pushState(null, '', newurl);

    //overwrite url to send a request immediately
    newUrl = '/partner-commissions/by-date-range?' + timeframe
                  + '&filterType=' + filterType
                  +  '&filterLenders=' + filterLender.join('-')
                  + '&sort=' + sort + '&direction=' + direction;

    window.ajaxManager.addReq({
        type: 'GET', url: newUrl,
        success: function(r){
          $('#commissionsTableContainer').empty().append(r)
                  .find('.input-daterange input').each(function() {
                          $(this).datepicker( {
                            format: 'dd/mm/yyyy',
                            startDate: '01/01/2016',
                            endDate: new Date,
                          } ).on('changeDate', function(){
                            $(this).datepicker('hide');
                          }); ;
                      });
        }
      });
    } catch (err) {
      alert(err.message);
    }
  }

  $('body').delegate('#filterButton', 'click',  commissionsDateRangeTriggered);
  $('body').delegate('.clearFilterButton', 'click',  clearFilterButtonClicked);
  $('body').delegate('th.sortable-col', 'click', sortedByColumn);
});
